#!/bin/bash

echo "==================================================="
echo "MYVideo Libraries Packaging Script"
echo "==================================================="

# 设置输出目录
OUTPUT_DIR="aar-output"
ROOT_DIR=$(pwd)

# 创建输出目录
mkdir -p $OUTPUT_DIR

echo "Creating output directory: $OUTPUT_DIR"
echo ""

# 执行Gradle任务清理项目
echo "Cleaning project..."
./gradlew clean

# 定义所有模块
MODULES=(
    "libBase" "libEngine" "libLogic" "libNet" "libPlayer" "libPlugin"
    "libSpeaker" "libStatistic" "libAssetsView" "annotation"
    "libAnnotateProcessor" "cutsameModel" "draft" "captureModule" "editModule"
)

# 打包并复制所有模块
echo ""
echo "Building and copying modules..."

for MODULE in "${MODULES[@]}"; do
    # 检查模块类型
    if grep -q "id 'java-library'" "$MODULE/build.gradle" || grep -q "apply plugin: 'java-library'" "$MODULE/build.gradle"; then
        # Java Library模块
        echo ""
        echo "Building $MODULE JAR (Java Library)..."
        ./gradlew :$MODULE:jar

        # 查找并复制JAR文件
        JAR_PATH=$(find "$MODULE/build/libs" -name "*.jar" | grep -v "javadoc" | grep -v "sources" | head -n 1)
        if [ -n "$JAR_PATH" ]; then
            cp "$JAR_PATH" "$OUTPUT_DIR/$MODULE.jar"
            echo "Copied: $MODULE.jar"
        else
            echo "Warning: JAR file for $MODULE not found!"
        fi
    else
        # Android Library模块
        echo ""
        echo "Building $MODULE AAR (Android Library)..."
        ./gradlew :$MODULE:assembleDebug

        # 复制AAR文件
        if [ -f "$MODULE/build/outputs/aar/$MODULE-debug.aar" ]; then
            cp "$MODULE/build/outputs/aar/$MODULE-debug.aar" "$OUTPUT_DIR/$MODULE.aar"
            echo "Copied: $MODULE.aar"
        else
            echo "Warning: $MODULE-debug.aar not found!"
        fi
    fi
done

echo ""
echo "==================================================="
echo "Library files have been built and copied to $ROOT_DIR/$OUTPUT_DIR"
echo "==================================================="

# 设置执行权限
chmod +x "$ROOT_DIR/$OUTPUT_DIR"/*.*