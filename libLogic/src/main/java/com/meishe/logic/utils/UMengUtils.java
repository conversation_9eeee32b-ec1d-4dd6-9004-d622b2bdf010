package com.meishe.logic.utils;

import android.content.Context;

import com.umeng.analytics.MobclickAgent;
import com.umeng.umcrash.UMCrash;

import java.util.Map;

public class UMengUtils {
    public static final String APPID = "5f4484feb4b08b653e99029d";
    public static final String EVENT_ID = "event_compile";


    /**
     * 友盟埋点统计
     * On event.
     *
     * @param context the context
     */
    public static void onEvent(Context context) {
        MobclickAgent.onEvent(context, EVENT_ID);
    }

    /**
     * 友盟埋点统计
     * On event.
     *
     * @param context the context
     * @param map     the map
     */
    public static void onEvent(Context context, Map map) {
        MobclickAgent.onEvent(context, EVENT_ID, map);
    }

    /**
     * 友盟埋点统计
     * On event.
     *
     * @param context the context
     * @param string  the string
     */
    public static void onEvent(Context context, String string) {
        MobclickAgent.onEvent(context, EVENT_ID, string);
    }

    /**
     * 友盟自定义异常上报
     * Generate custom log.
     *
     * @param e the e
     */
    public static void generateCustomLog(String e) {
        UMCrash.generateCustomLog(e, "UmengException");
    }

    /**
     *
     * 友盟自定义异常上报
     * Generate custom log.
     *
     * @param e the e
     */
    public static void generateCustomLog(Throwable e) {
        UMCrash.generateCustomLog(e, "UmengException");
    }

}
