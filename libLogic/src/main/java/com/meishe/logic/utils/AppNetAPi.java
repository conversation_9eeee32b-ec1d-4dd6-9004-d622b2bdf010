package com.meishe.logic.utils;

import android.text.TextUtils;

import com.meishe.base.utils.GsonUtils;
import com.meishe.base.utils.Utils;
import com.meishe.logic.bean.SettingParameter;
import com.meishe.logic.manager.PreferencesManager;
import com.meishe.net.custom.RequestCallback;
import com.meishe.net.custom.SimpleDownListener;

import java.util.HashMap;
import java.util.Map;


/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> LiHangZhou
 * @CreateDate :2020/11/25 19:25
 * @Description :app模块网络请求接口API  app net interface api
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class AppNetAPi {

    /**
     * 获取上传阿里云秘钥
     * Get the secret key for uploading ali cloud
     *
     * @param tag            Object 请求标识
     * @param token          the token
     * @param uploadModule   String  bs工程：bs_project；app工程：app_project；视频：resource_video；
     *                       图片：resource_image；音频：resource_audio；模板：material_template；临时目录：temp
     * @param extensionName  String 上传的文件的扩展名
     * @param projectId      long 上传的模块是bs工程时，需要携带工程id
     * @param uuid           String 模板的uuid,上传模板时需要这个参数
     * @param isNeedCallback boolean 分片上传是否需要回调 true need ,false not
     * @param callback       RequestCallback 请求回调
     */
    public static void getAliSecret(Object tag, String token, String uploadModule, String extensionName,
                                    long projectId, String uuid, boolean isNeedCallback, RequestCallback<?> callback) {
        String apiName = "upload/sts/info";
        Map<String, String> params = new HashMap<>(8);
        //类型1: 腾讯云2:亚马逊3:阿里云4:百度云,目前只支持阿里云上传的模块
        // Type 1: Tencent Cloud 2: Amazon 3: Alibaba Cloud 4: Baidu Cloud, currently only supports modules uploaded by Alibaba Cloud
        params.put("type", "3");
        params.put("extension", extensionName);
        params.put("uploadModule", uploadModule);
        //0否当前工程相关的资源不需要回调 1是
        // 0 No, resources related to the current project do not require a callback. 1 Yes
        params.put("isNeedCallback", isNeedCallback ? "1" : "0");
        params.put("platform", "app");
        if (!TextUtils.isEmpty(uuid)) {
            params.put("uuid", uuid);
        }
        //是否为序列本 0否1是
        //Is it a sequence book 0 No 1 Yes
        //params.put("isDir", "0");
        if (projectId > 0) {
            params.put("projectId", String.valueOf(projectId));
        }
        //该接口需要添加请求头。This interface needs to add a request header.
        Map<String, String> header = new HashMap<>(1);
        header.put("token", token);
        NvsServerClient.get().postWithHeader(tag, NvsServerClient.get().getCloudClipUrl(), apiName, header, params, callback);
    }

    /**
     * 获取skd认证文件
     * Obtain the SKD authentication file
     *
     * @param tag      Object 请求标识
     * @param callback RequestCallback 请求回调
     */
    public static void getSdkLicense(Object tag, RequestCallback<?> callback) {
        Map<String, String> params = new HashMap<>(1);
        params.put("appId", Utils.getApp().getPackageName());
        String apiName = "api/authorization/ST/current";
        NvsServerClient.get().requestGet(tag, apiName, params, callback);
    }

    /**
     * 登录云剪辑
     * Login Cloud Clip
     *
     * @param tag        Object 请求标识
     * @param jsonParams Object,json格式的参数。
     * @param callback   RequestCallback 请求回调
     */
    public static void loginCloudClip(Object tag, Object jsonParams, RequestCallback<?> callback) {
        String apiName = "login";
        NvsServerClient.get().requestPost(tag, NvsServerClient.get().getCloudClipUrl(), apiName, jsonParams, callback);
    }

    /**
     * 用户登录
     * User Login
     *
     * @param tag       Object 请求标识
     * @param mapParams Object,map格式的参数。
     * @param callback   RequestCallback 请求回调
     */
    public static void loginUser(Object tag,  Map<String, String> mapParams, RequestCallback<?> callback) {
        String apiName = "materialcenter/myvideo/user/login";
        NvsServerClient.get().requestPost(tag, NvsServerClient.getAssetsHost(), apiName, mapParams, callback);
    }

    /**
     * 上传云剪辑
     * Upload Cloud Clip
     *
     * @param tag        Object 请求标识
     * @param jsonParams Object,json格式的参数。
     * @param token      String token 登录返回的token
     * @param callback   RequestCallback 请求回调
     */
    public static void uploadCloudClip(Object tag, Object jsonParams, String token, RequestCallback<?> callback) {
        String apiName = "project/transform";
        //该接口需要添加请求头。This interface needs to add a request header.
        Map<String, String> header = new HashMap<>(1);
        header.put("token", token);
        NvsServerClient.get().postWithHeader(tag, NvsServerClient.get().getCloudClipUrl(), apiName, header, jsonParams, callback);
    }

    /**
     * Create or update project.
     * 创建或更新草稿工程
     * @param infoUrl     the info url
     * @param templateUrl the template url
     * @param projectId   the project id
     * @param token       the token
     * @param callback    the callback
     */
    public static void createOrUpdateProject(String infoUrl, String templateUrl, String projectId, String token, RequestCallback<?> callback) {
        //该接口需要添加请求头。This interface needs to add a request header.
        Map<String, String> params = new HashMap<>(2);
        params.put("multiProjectUrl", templateUrl);
        params.put("infoUrl", infoUrl);
        params.put("appVersion", Utils.getAppVersionName());
        String resolvingPower = "720";
        SettingParameter parameter = GsonUtils.fromJson(PreferencesManager.get().getSettingParams(), SettingParameter.class);
        if (parameter != null) {
            int compileResolution = parameter.getCompileResolution();
            resolvingPower = compileResolution == 2 ? "720" : "1080";
        }
        params.put("resolvingPower", resolvingPower);
        if (!TextUtils.isEmpty(projectId)) {
            params.put("projectId", projectId);
        }
        Map<String, String> header = new HashMap<>(1);
        header.put("token", token);
        NvsServerClient.get().postWithHeader(projectId, NvsServerClient.get().getCloudClipUrl(), "project/update", header, params, callback);
    }


    /**
     * Add background material.
     * 添加背景素材
     * @param uuid       the uuid 背景素材uuid
     * @param packageUrl the package url 背景链接
     * @param token      the token 用户token
     * @param callback   the callback 回调
     */
    public static void addBackgroundMaterial(String uuid, String packageUrl, String token, RequestCallback<?> callback) {
        //该接口需要添加请求头。This interface needs to add a request header.
        Map<String, String> params = new HashMap<>(2);
        params.put("uuid", uuid);
        params.put("displayName", uuid);
        params.put("displayNameZhCN", uuid);
        params.put("materialType", "22");
        params.put("packageUrl", packageUrl);
        Map<String, String> header = new HashMap<>(1);
        header.put("token", token);
        NvsServerClient.get().postWithHeader(packageUrl, NvsServerClient.get().getCloudClipUrl(), "material/add", header, params, callback);
    }

    /**
     * 上传意见反馈
     * Upload comments and feedback
     *
     * @param tag        Object 请求标识
     * @param jsonParams Object,json格式的参数。
     * @param callback   RequestCallback 请求回调
     */
    public static void uploadFeedback(Object tag, Object jsonParams, RequestCallback<?> callback) {
        String apiName = "feedback/index.php";
        NvsServerClient.get().requestPost(tag, apiName, jsonParams, callback);
    }

    /**
     * 获取特效素材资源列表
     * Gets a list of effects material resources
     *
     * @param tag         Object 请求标识
     * @param type        int  资源类型
     * @param aspectRatio int 资源比例
     * @param categoryId  int 资源分类id
     * @param page        int 资源页数
     * @param pageSize    int 资源页数大小
     * @param callback    RequestCallback 请求回调
     */
    public static void getMaterialList(Object tag, int type, int aspectRatio, int categoryId,
                                       int page, int pageSize, RequestCallback<?> callback) {
        Map<String, String> params = new HashMap<>(2);
        params.put("command", "listMaterial");
        params.put("acceptAspectRatio", String.valueOf(aspectRatio));
        params.put("category", String.valueOf(categoryId));
        params.put("page", String.valueOf(page));
        params.put("pageSize", String.valueOf(pageSize));
        params.put("type", String.valueOf(type));
        params.put("lang", Utils.isZh() ? "zh_CN" : "en");
        String apiName = "materialinfo/index.php";
        NvsServerClient.get().requestGet(tag, apiName, params, callback);
    }

    /**
     * 下载实现方法
     * Download implementation method
     *
     * @param tag      下载标识
     * @param url      下载地址
     * @param filePath 文件路径
     * @param fileName 文件名
     * @param listener 下载监听器
     */
    public static void download(String tag, String url, String filePath, String fileName, SimpleDownListener listener) {
        NvsServerClient.get().download(tag, url, filePath, fileName, listener);
    }

    /**
     * 取消所有请求
     * Cancel all requests
     */
    public static void cancelAll() {
        NvsServerClient.get().cancelAll();
    }

    /**
     * 取消某个请求
     * Cancel a request
     *
     * @param tag 唯一标识
     */
    public static void cancelRequest(Object tag) {
        NvsServerClient.get().cancelRequest(tag);
    }
}
