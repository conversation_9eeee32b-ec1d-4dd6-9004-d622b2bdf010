{"java.configuration.updateBuildConfiguration": "automatic", "java.compile.nullAnalysis.mode": "automatic", "java.project.sourcePaths": ["app/src/main/java", "editModule/src/main/java", "libBase/src/main/java"], "java.project.referencedLibraries": ["libs/**/*.jar", "${env:ANDROID_HOME}/platforms/android-33/android.jar", "${env:ANDROID_HOME}/extras/android/m2repository/com/android/support/support-v4/28.0.0/support-v4-28.0.0.jar", "${env:ANDROID_HOME}/extras/android/m2repository/com/android/support/appcompat-v7/28.0.0/appcompat-v7-28.0.0.jar"], "java.configuration.runtimes": [{"name": "JavaSE-11", "path": "/Library/Java/JavaVirtualMachines/zulu-11.jdk/Contents/Home"}], "java.jdt.ls.vmargs": "-XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable"}