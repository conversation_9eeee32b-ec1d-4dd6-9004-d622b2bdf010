plugins {
    id 'com.android.library'
}

android {
    compileSdkVersion 31

    defaultConfig {
        minSdkVersion 21
        targetSdkVersion 31

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled true
            matchingFallbacks = ['release']
            consumerProguardFiles 'proguard-rules.pro'
        }

        debug {
            minifyEnabled false
            signingConfig signingConfigs.debug
            matchingFallbacks = ['debug']
            debuggable true
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

dependencies {
    implementation project(path: ':libLogic')
    implementation project(path: ':libEngine')
    implementation project(path: ':libPlugin')
    implementation project(path: ':draft')
    implementation 'com.facebook.fresco:fresco:1.12.0'
    // 支持 GIF 动图，需要添加
    implementation 'com.facebook.fresco:animated-gif:1.12.0'
    // 支持 WebP （静态图+动图），需要添加
    implementation 'com.facebook.fresco:animated-webp:1.12.0'
    // 仅支持 WebP 静态图，需要添加
    implementation 'com.facebook.fresco:webpsupport:1.12.0'

    implementation 'androidx.appcompat:appcompat:1.3.0'
    implementation 'com.google.android.material:material:1.4.0'
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.3'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.4.0'
}