<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tl="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/color_ff181818">

    <LinearLayout
        android:id="@+id/ll_top_menu"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <EditText
            android:id="@+id/et_caption_input"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_px_105"
            android:layout_marginLeft="@dimen/dp_px_39"
            android:layout_marginTop="@dimen/dp_px_45"
            android:layout_marginRight="@dimen/dp_px_39"
            android:background="@drawable/editor_caption_input_corner"
            android:hint="@null"
            android:importantForAutofill="no"
            android:paddingLeft="@dimen/dp_px_15"
            android:paddingRight="@dimen/dp_px_15"
            android:textColor="@color/white_8"
            android:textCursorDrawable="@drawable/editor_caption_input_cursor"
            android:textSize="@dimen/sp_px_48" />

        <RelativeLayout
            android:id="@+id/rl_menu_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <View
                android:id="@+id/menu_divide"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_px_2"
                android:layout_marginTop="@dimen/dp_px_111"
                android:background="@color/menu_divide_color" />

            <ImageView
                android:id="@+id/iv_delete"
                android:layout_width="@dimen/dp_px_51"
                android:layout_height="@dimen/dp_px_51"
                android:layout_centerVertical="true"
                android:layout_marginStart="@dimen/dp_px_69"
                android:layout_marginLeft="@dimen/dp_px_69"
                android:contentDescription="@null"
                android:src="@mipmap/iv_tab_delete"
                android:visibility="gone" />

            <com.meishe.third.tablayout.SlidingTabLayout
                android:id="@+id/tab_layout"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_px_114"
                android:layout_marginLeft="@dimen/dp_px_15"
                android:layout_toStartOf="@+id/iv_confirm"
                android:layout_toLeftOf="@+id/iv_confirm"
                android:layout_toEndOf="@+id/iv_delete"
                android:layout_toRightOf="@+id/iv_delete"
                tl:tl_indicator_color="@color/color_ffffffff"
                tl:tl_indicator_height="@dimen/dp_px_6"
                tl:tl_indicator_width="@dimen/dp_px_30"
                tl:tl_tab_space_equal="true"
                tl:tl_tab_width="@dimen/dp_px_160"
                tl:tl_textSelectColor="@color/white"
                tl:tl_textSize="@dimen/sp_px_39"
                tl:tl_textUnselectedColor="@color/white_5" />

            <ImageView
                android:id="@+id/iv_confirm"
                android:layout_width="@dimen/dp_px_125"
                android:layout_height="@dimen/dp_px_75"
                android:layout_alignParentEnd="true"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="@dimen/dp_px_20"
                android:layout_marginRight="@dimen/dp_px_20"
                android:contentDescription="@null"
                android:paddingStart="@dimen/dp_px_25"
                android:paddingLeft="@dimen/dp_px_25"
                android:paddingEnd="@dimen/dp_px_25"
                android:paddingRight="@dimen/dp_px_25"
                android:src="@mipmap/ic_confirm" />
        </RelativeLayout>


        <com.meishe.base.view.MViewPager
            android:id="@+id/viewPager"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_px_780" />
    </LinearLayout>

    <FrameLayout
        android:id="@+id/fl_bottom_confirm"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_225"
        android:visibility="gone">

        <View
            android:id="@+id/v_bottom_line"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_px_1"
            android:background="@color/menu_divide_color" />

        <TextView
            android:id="@+id/tv_apply_to_all"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="left"
            android:layout_marginStart="@dimen/dp_px_42"
            android:layout_marginLeft="@dimen/dp_px_42"
            android:layout_marginTop="@dimen/dp_px_57"
            android:text="@string/apply_all"
            android:textColor="@color/white_8"
            android:textSize="@dimen/sp_px_30" />

        <TextView
            android:id="@+id/tv_content"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/dp_px_50"
            android:textColor="@color/white_8"
            android:textSize="@dimen/sp_px_39" />

        <ImageView
            android:id="@+id/iv_bottom_confirm"
            android:layout_width="@dimen/dp_px_70"
            android:layout_height="@dimen/dp_px_70"
            android:layout_gravity="end"
            android:layout_marginTop="@dimen/dp_px_48"
            android:layout_marginEnd="@dimen/dp_px_45"
            android:layout_marginRight="@dimen/dp_px_45"
            android:background="@mipmap/ic_confirm"
            android:contentDescription="@null" />
    </FrameLayout>

</merge>