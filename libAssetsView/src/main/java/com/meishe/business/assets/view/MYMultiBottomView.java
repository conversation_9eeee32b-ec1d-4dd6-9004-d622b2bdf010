package com.meishe.business.assets.view;

import android.annotation.SuppressLint;
import android.content.Context;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.AttributeSet;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;
import androidx.viewpager.widget.ViewPager;

import com.meishe.base.adapter.CommonFragmentAdapter;
import com.meishe.base.utils.KeyboardUtils;
import com.meishe.base.utils.LogUtils;
import com.meishe.base.utils.SizeUtils;
import com.meishe.base.view.MViewPager;
import com.meishe.business.R;
import com.meishe.third.tablayout.SlidingTabLayout;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 底部弹出菜单 ：水印菜单、贴纸菜单、字幕菜单、美颜、转场
 */
public class MYMultiBottomView extends LinearLayout {

    /**
     * 水印
     * Type menu of water mark
     */
    public static final int TYPE_MENU_WATER_MARK = 1;


    /**
     * 贴纸
     * Type menu of sticker
     */
    public static final int TYPE_MENU_STICKER = 2;
    /**
     * 字幕
     * Type menu of caption
     */
    public static final int TYPE_MENU_CAPTION = 3;

    /**
     * 美颜
     * Type menu of beauty
     */
    public static final int TYPE_MENU_BEAUTY = 4;

    /**
     * 转场
     * Type menu of transition
     */
    public static final int TYPE_MENU_TRANSITION = 5;

    /**
     * 特效
     * Type menu of effect
     */
    public static final int TYPE_MENU_EFFECT = 7;

    /**
     * 道具
     * Type menu of prop
     */
    public static final int TYPE_MENU_PROP = 8;

    /**
     * 音量和音频淡入淡出
     * Type menu of volume fade
     */
    public static final int TYPE_MENU_VOLUME_FADE = 9;

    private ImageView mIvConfirm;
    private SlidingTabLayout mTabLayout;
    private ArrayList<Fragment> mFragmentList;
    private MViewPager mViewPager;
    private FragmentManager mFragmentManager;
    private EditText mEtCaptionInput;
    private int mType = 0;
    private FrameLayout mFlBottomConfirm;
    private LinearLayout mLlTopMenu;
    private ImageView mIvBottomConfirm, mIvDelete;
    private TextView mTvContent;
    private OnViewStateListener mOnViewStateListener;
    private MultiBottomEventListener mEventListener;
    private TextView mApplyToAll;
    /**
     * 切换字幕框的时候置为false，防止多次添加相同字幕
     * Set to false when switching subtitle boxes to prevent adding the same subtitle multiple times.
     */
    private boolean addCaption = true;

    public MYMultiBottomView(Context context) {
        this(context, null);
    }

    public MYMultiBottomView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public MYMultiBottomView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView();
        initListener();
    }

    private void initView() {
        setOrientation(VERTICAL);
        View view = LayoutInflater.from(getContext()).inflate(R.layout.layout_multi_bottom_view, this);
        mIvConfirm = view.findViewById(R.id.iv_confirm);
        mTabLayout = view.findViewById(R.id.tab_layout);
        mViewPager = view.findViewById(R.id.viewPager);
        mIvDelete = view.findViewById(R.id.iv_delete);
        mFlBottomConfirm = view.findViewById(R.id.fl_bottom_confirm);
        mIvBottomConfirm = view.findViewById(R.id.iv_bottom_confirm);
        mEtCaptionInput = view.findViewById(R.id.et_caption_input);
        mApplyToAll = view.findViewById(R.id.tv_apply_to_all);
        mLlTopMenu = view.findViewById(R.id.ll_top_menu);
        mTvContent = view.findViewById(R.id.tv_content);
        mViewPager.setOffscreenPageLimit(5);
        mViewPager.setScroll(true);
        mFragmentList = new ArrayList<>();
    }

    private void initListener() {
        setOnTouchListener(new OnTouchListener() {
            @SuppressLint("ClickableViewAccessibility")
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                return v.getId() == getId();
            }
        });

        mViewPager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

            }

            @Override
            public void onPageSelected(int position) {
                post(new Runnable() {
                    @Override
                    public void run() {
                        if (mEventListener != null) {
                            mEventListener.onFragmentSelected(getSelectedFragment(), mType);
                        }
                    }
                });
                if (position > 0) {
                    KeyboardUtils.hideSoftInput(mEtCaptionInput);
                }
            }

            @Override
            public void onPageScrollStateChanged(int state) {

            }
        });
        mIvConfirm.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mEventListener != null) {
                    mEventListener.onConfirm(mType);
                }
                hide();
            }
        });

        mIvBottomConfirm.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mEventListener != null) {
                    mEventListener.onConfirm(mType);
                }
                hide();
            }
        });

        mIvDelete.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mEventListener != null) {
                    mEventListener.onErasure(mType);
                }
            }
        });
        mApplyToAll.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mEventListener != null && mViewPager != null) {
                    mEventListener.onApplyToAll(getFragment(mViewPager.getCurrentItem()));
                }
            }
        });
        mEtCaptionInput.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
            }

            @Override
            public void afterTextChanged(Editable s) {
                if (isShow() && addCaption) {
                    if (mEventListener != null && s != null) {
                        mEventListener.onEditTextChange(s.toString());
                    }
                }
                addCaption = true;
            }
        });
    }

    private void initTabLayout(String[] tabs) {
        if (tabs == null || tabs.length == 0) {
            return;
        }
        mViewPager.setAdapter(new CommonFragmentAdapter(mFragmentManager, mFragmentList, Arrays.asList(tabs)));
        mTabLayout.setViewPager(mViewPager);
    }

    private void setSelectItem(int position) {
        mViewPager.setCurrentItem(position);
    }

    public ArrayList<Fragment> getFragmentList() {
        return mFragmentList;
    }

    public void setViewPagerOffscreenPageLimit(int limit) {
        if (mViewPager != null) {
            mViewPager.setOffscreenPageLimit(limit);
        }
    }

    /**
     * 动态更改View的高度
     * Change view height
     */
    private void changeViewHeight(int topMenuHeight, int viewPagerHeight) {
        LayoutParams layoutParams = (LayoutParams) mLlTopMenu.getLayoutParams();
        layoutParams.height = topMenuHeight;
        mLlTopMenu.setLayoutParams(layoutParams);
        LayoutParams pagerLayoutParams = (LayoutParams) mViewPager.getLayoutParams();
        pagerLayoutParams.height = viewPagerHeight;
        mViewPager.setLayoutParams(pagerLayoutParams);
    }

    /**
     * 展示确定按钮布局
     * Display the confirm view
     */
    private void displayConfirmView(boolean isNeedInput, boolean showBottomConfirm) {
        if (!isNeedInput) {
            if (showBottomConfirm) {
                mFlBottomConfirm.setVisibility(VISIBLE);
                mIvConfirm.setVisibility(GONE);
                changeViewHeight(SizeUtils.dp2px(200), SizeUtils.dp2px(162));
            } else {
                mFlBottomConfirm.setVisibility(GONE);
                mIvConfirm.setVisibility(VISIBLE);
                changeViewHeight(SizeUtils.dp2px(261), SizeUtils.dp2px(217));
            }
        } else {
            mFlBottomConfirm.setVisibility(GONE);
            mIvConfirm.setVisibility(VISIBLE);
        }
    }

    /**
     * @param tabs      the tab list
     * @param fragments the fragment list
     * @param position  the selected tab
     */
    private void show(String[] tabs, List<Fragment> fragments, int position) {
        if (mFragmentList.size() != 0) {
            mFragmentList.clear();
        }
        LogUtils.d("show,size=" + fragments.size());
        mFragmentList.addAll(fragments);
        initTabLayout(tabs);
        setSelectItem(position);
        Animation animation = AnimationUtils.loadAnimation(getContext(), R.anim.view_enter);
        this.setAnimation(animation);
        this.setVisibility(VISIBLE);
        if (mOnViewStateListener != null) {
            mOnViewStateListener.onShow();
        }
    }

    /**
     * Set fragment manager
     *
     * @param fragmentManager the fragmentManager
     */
    public void setFragmentManager(FragmentManager fragmentManager) {
        mFragmentManager = fragmentManager;
    }

    /**
     * 展示视图
     * Show view
     *
     * @param tabs              the tab list
     * @param fragments         the fragment list
     * @param position          the selected tab
     * @param type              the group type
     * @param isNeedInput       true need input ,false not
     * @param showBottomConfirm true show bottom confirm view
     */
    public void show(String[] tabs, List<Fragment> fragments, int position, int type, boolean isNeedInput, boolean showBottomConfirm, boolean needErasure) {
        displayConfirmView(isNeedInput, showBottomConfirm);
        mEtCaptionInput.setVisibility(isNeedInput ? VISIBLE : GONE);
        if (isNeedInput) {
            KeyboardUtils.showSoftInput(mEtCaptionInput, InputMethodManager.SHOW_IMPLICIT);
        }
        if (needErasure) {
            mIvDelete.setVisibility(VISIBLE);
        } else {
            mIvDelete.setVisibility(GONE);
        }
        mType = type;
        //webp 加载多个page时，会卡顿，这里需要处理一下
        //When webp loads multiple pages, it may get stuck, so we need to handle it here.
        if (type == TYPE_MENU_EFFECT || type == TYPE_MENU_PROP) {
            setViewPagerOffscreenPageLimit(1);
        } else {
            setViewPagerOffscreenPageLimit(5);
        }
        show(tabs, fragments, position);
    }

    /**
     * 展示视图,可以设置尺寸
     * Show view width size
     *
     * @param tabs              the tab list
     * @param fragments         the fragment list
     * @param position          the selected tab
     * @param type              the group type
     * @param isNeedInput       true need input ,false not
     * @param showBottomConfirm true show bottom confirm view
     * @param size              the size of layout
     */
    public void show(String[] tabs, List<Fragment> fragments, int position, int type, boolean isNeedInput, boolean showBottomConfirm, boolean needErasure, int[] size) {
        show(tabs, fragments, position, type, isNeedInput, showBottomConfirm, needErasure);
        if (size != null && size.length == 2 && size[0] > 0 && size[1] > 0) {
            changeViewHeight(SizeUtils.dp2px(size[1]), SizeUtils.dp2px(size[0]));
        }
    }

    /**
     * 展示视图
     * Show view
     *
     * @param tabs           the tab list
     * @param fragments      the fragment list
     * @param position       the selected tab
     * @param type           the group type
     * @param editText       the edit text
     * @param keyboardHeight the keyboard height
     */
    public void show(String[] tabs, List<Fragment> fragments, int position, int type, String editText, int keyboardHeight) {
        if (keyboardHeight > 0) {
            setKeyboardHeight(keyboardHeight);
        }
        if (!TextUtils.isEmpty(editText)) {
            mEtCaptionInput.setText(editText);
            mEtCaptionInput.setSelection(mEtCaptionInput.getText().length());
        }
        show(tabs, fragments, position, type, true, false, false);
    }

    /**
     * 展示默认样式视图
     * Show  default view
     *
     * @param tabs      the tab list
     * @param fragments the fragment list
     * @param position  the selected tab
     * @param type      the group type
     */
    public void showDefault(String[] tabs, List<Fragment> fragments, int position, int type) {
        show(tabs, fragments, position, type, false, false, false);
    }

    /**
     * 展示默认样式视图
     * Show view width size
     *
     * @param tabs      the tab list
     * @param fragments the fragment list
     * @param position  the selected tab
     * @param type      the group type
     * @param size      the size of layout
     */
    public void showWidthSize(String[] tabs, List<Fragment> fragments, int position, int type, int[] size) {
        show(tabs, fragments, position, type, false, false, false, size);
    }

    /**
     * 展示视图
     * Show view
     *
     * @param tabs      the tab list
     * @param fragments the fragment list
     * @param position  the selected tab
     * @param type      the group type
     */
    public void show(String[] tabs, List<Fragment> fragments, int position, int type, boolean needErasure) {
        show(tabs, fragments, position, type, false, false, needErasure);
    }

    /**
     * 展示视图
     * Show view
     *
     * @param tabs      the tab list
     * @param fragments the fragment list
     * @param position  the selected tab
     * @param type      the group type
     */
    public void showNoConfirm(String[] tabs, List<Fragment> fragments, int position, int type, boolean needErasure) {
        show(tabs, fragments, position, type, false, false, needErasure);
        mIvConfirm.setVisibility(GONE);
    }

    /**
     * 展示没有确定布局的视图
     * Show no confirm view
     *
     * @param tabs      the tab list
     * @param fragments the fragment list
     * @param position  the selected tab
     * @param type      the group type
     */
    public void showNoConfirm(String[] tabs, List<Fragment> fragments, int position, int type) {
        show(tabs, fragments, position, type, false);
        mIvConfirm.setVisibility(GONE);
    }

    public void setKeyboardHeight(int keyboardHeight) {
        int captionViewpagerHeight = SizeUtils.dp2px(250);
        if (keyboardHeight < captionViewpagerHeight) {
            keyboardHeight = captionViewpagerHeight;
        }
        changeViewHeight(SizeUtils.dp2px(94) + keyboardHeight, keyboardHeight);
    }

    /**
     * 隐藏
     * Hide
     */
    public void hide() {
        if (!isShow()) {
            return;
        }

        Animation animation = AnimationUtils.loadAnimation(getContext(), R.anim.view_exit);
        this.setAnimation(animation);
        this.setVisibility(GONE);
        if (mEtCaptionInput != null) {
            KeyboardUtils.hideSoftInput(mEtCaptionInput);
            mEtCaptionInput.setText("");
        }
        //LogUtils.d("hide,remove,size=" + mFragmentList.size());
        for (int i = 0; i < mFragmentList.size(); i++) {
            removeFragmentInternal(mFragmentList.get(i));
        }
        mFragmentList.clear();
        mViewPager.setAdapter(null);
        if (mOnViewStateListener != null) {
            mOnViewStateListener.onHide();
        }
    }

    /**
     * 是否显示
     * Is show
     */
    public boolean isShow() {
        return getVisibility() == VISIBLE;
    }


    public int getType() {
        return mType;
    }

    /**
     * 从Transaction移除Fragment
     *
     * @param fragment 目标Fragment
     */
    private void removeFragmentInternal(Fragment fragment) {
        FragmentTransaction transaction = mFragmentManager.beginTransaction();
        transaction.remove(fragment);
        transaction.commitNowAllowingStateLoss();
    }

    /**
     * Sets edit text.
     * 设置编辑框文字
     *
     * @param text the m edit text
     */

    public void setEditText(String text) {
        addCaption = false;
        mEtCaptionInput.setText(text);
        mEtCaptionInput.setSelection(mEtCaptionInput.getText().length());
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        if (mFragmentList != null && mFragmentList.size() > 0) {
            //LogUtils.d("onDetachedFromWindow,remove,size=" + mFragmentList.size());
            for (int i = 0; i < mFragmentList.size(); i++) {
                removeFragmentInternal(mFragmentList.get(i));
            }
            mFragmentList.clear();
            mViewPager.setAdapter(null);
        }
    }

    /**
     * 获取选中的fragment
     * Get the selected fragment
     */
    public Fragment getSelectedFragment() {
        return getFragment(mViewPager.getCurrentItem());
    }

    /**
     * 获取所需位置的fragment
     * Get the target fragment
     *
     * @param position the position
     */
    public Fragment getFragment(int position) {
        if (mFragmentList == null || position < 0 || position >= mFragmentList.size()) {
            return null;
        }
        return mFragmentList.get(position);
    }

//    /**
//     * 获取选中的tab text
//     * Get the selected tab text
//     */
//    public String getSelectedTabText() {
//        return getTabText(mTabLayout.getSelectedTabPosition());
//    }

//    /**
//     * 获取所需位置的tab text
//     * Get the target tab text
//     *
//     * @param position the position
//     */
//    public String getTabText(int position) {
//        TabLayout.Tab selectedTab = mTabLayout.getTabAt(position);
//        if (selectedTab != null && selectedTab.getText() != null) {
//            return selectedTab.getText().toString();
//        }
//        return "";
//    }


    /**
     * 设置事件监听
     * Set listener
     *
     * @param listener the listener
     */
    public void setMultiBottomEventListener(MultiBottomEventListener listener) {
        mEventListener = listener;
    }

    public void setOnViewStateListener(OnViewStateListener listener) {
        this.mOnViewStateListener = listener;
    }

    public interface MultiBottomEventListener {
        void onEditTextChange(String text);

        void onConfirm(int type);

        void onApplyToAll(Fragment fragment);

        void onErasure(int type);

        void onFragmentSelected(Fragment fragment, int type);
    }

    public interface OnViewStateListener {
        void onShow();

        void onHide();
    }

}
