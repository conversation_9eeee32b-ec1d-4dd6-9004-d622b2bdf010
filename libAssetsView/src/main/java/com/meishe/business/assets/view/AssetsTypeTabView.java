package com.meishe.business.assets.view;

import android.content.Context;
import android.graphics.Color;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.meishe.base.manager.LinearLayoutManagerWrapper;
import com.meishe.base.utils.Utils;
import com.meishe.base.view.decoration.ItemDecoration;
import com.meishe.business.R;
import com.meishe.business.assets.ConfigUtil;
import com.meishe.third.adpater.BaseQuickAdapter;
import com.meishe.third.adpater.BaseSelectAdapter;
import com.meishe.third.adpater.BaseViewHolder;

import java.util.Arrays;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2021/3/19 15:50
 * @Description :素材类别选择器 The assets type tab view
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class AssetsTypeTabView extends FrameLayout {

    private ItemClickedListener mItemClickedListener;
    private ListAdapter mListAdapter;

    public void setItemClickedListener(ItemClickedListener listener) {
        this.mItemClickedListener = listener;
    }

    public void setSelected(int position) {
        if (mListAdapter != null) {
            mListAdapter.setSelectPosition(position);
        }
    }

    public AssetsTypeTabView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        if (ConfigUtil.isToC()) {
            setVisibility(GONE);
        }
        initView(context);
    }

    private void initView(Context context) {
        View rootView = LayoutInflater.from(context).inflate(R.layout.view_assets_type_tab, this);
        RecyclerView recyclerView = rootView.findViewById(R.id.rv_recyclerView);
        LinearLayoutManager layoutManager = new LinearLayoutManagerWrapper(getContext(), LinearLayoutManager.HORIZONTAL, false);
        recyclerView.setLayoutManager(layoutManager);
        mListAdapter = new ListAdapter();
        recyclerView.addItemDecoration(new ItemDecoration(13, 13));
        recyclerView.setAdapter(mListAdapter);
        String[] mData = Utils.getApp().getResources().getStringArray(R.array.menu_tab_assets_type);
        mListAdapter.setNewData(Arrays.asList(mData));

        mListAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                mListAdapter.setSelectPosition(position);
                if (mItemClickedListener != null) {
                    mItemClickedListener.onItemClicked(position);
                }
            }
        });
        post(new Runnable() {
            @Override
            public void run() {
                mListAdapter.setSelectPosition(0);
            }
        });

    }

    class ListAdapter extends BaseSelectAdapter<String> {

        public ListAdapter(){
            super(R.layout.item_assets_type);
        }

        @Override
        protected void convert(@NonNull BaseViewHolder helper, String item) {
            TextView tabText = helper.getView(R.id.tv_tab);
            tabText.setText(item);
            if (getSelectPosition() == helper.getAdapterPosition()) {
                tabText.setBackground(getResources().getDrawable(R.drawable.shape_item_assets_tab_bg));
                tabText.setTextColor(Color.WHITE);
            } else {
                tabText.setBackground(null);
                tabText.setTextColor(getResources().getColor(R.color.color_ff808080));
            }
        }
    }

    public interface ItemClickedListener{
        void onItemClicked(int position);
    }
}
