package com.meishe.business.assets.presenter;

import android.text.TextUtils;

import com.meicam.sdk.NvsVideoResolution;
import com.meishe.base.model.Presenter;
import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.FileUtils;
import com.meishe.base.utils.LogUtils;
import com.meishe.base.utils.ThreadUtils;
import com.meishe.business.assets.iview.AssetsView;
import com.meishe.draft.db.DraftDbManager;
import com.meishe.draft.db.FileInfoDao;
import com.meishe.draft.db.FileInfoEntity;
import com.meishe.engine.EditorEngine;
import com.meishe.engine.EngineNetApi;
import com.meishe.engine.asset.AssetsManager;
import com.meishe.engine.asset.IAssetsManager;
import com.meishe.engine.asset.bean.AssetDownloadInfo;
import com.meishe.engine.asset.bean.AssetInfo;
import com.meishe.engine.asset.bean.AssetList;
import com.meishe.engine.asset.bean.RequestParam;
import com.meishe.engine.bean.BaseInfo;
import com.meishe.engine.interf.IBaseInfo;
import com.meishe.libplugin.PluginManager;
import com.meishe.libplugin.user.IUserPlugin;
import com.meishe.net.custom.BaseResponse;
import com.meishe.net.custom.RequestCallback;
import com.meishe.net.custom.SimpleDownListener;
import com.meishe.net.model.Progress;

import java.io.File;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2021/8/4 17:38
 * @Description :资源文件管理逻辑处理类 Resource file management logic processing class.
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class AssetsPresenter<VIEW extends AssetsView> extends Presenter<VIEW> {
    private int mPageNumber;
    private boolean mHasNext;
    private int mPageSize = 10;
    private FileInfoDao mFileInfoDao;
    private final Set<String> mDownloadSet = new HashSet<>();

    /**
     * Load data
     * 加载数据
     *
     * @param type       the type
     * @param subType    the sub type
     * @param categoryId the category id
     * @param kind       the kind
     * @param needUpdate Is need update or not? true；yes; false：no
     */
    public void loadData(int type, int subType, int categoryId, int kind, final boolean needUpdate) {
        loadData(new RequestParam(type, subType, categoryId, kind), 0, needUpdate);
    }

    /**
     * Load  more data
     * 加载更多数据
     *
     * @param type       the type
     * @param subType    the sub type
     * @param categoryId the category id
     * @param kind       the kind
     * @param needUpdate Is need update or not? true；yes; false：no
     */
    public boolean loadMoreData(int type, int subType, int categoryId, int kind, final boolean needUpdate) {
        if (!mHasNext) {
            return false;
        }
        loadData(new RequestParam(type, subType, categoryId, kind), mPageNumber + 1, needUpdate);
        return true;
    }

    /**
     * Click asset item.
     * 点击资源项
     *
     * @param assetInfo the asset info 资源项
     */
    public void clickAssetItem(IBaseInfo assetInfo) {
        if (assetInfo == null) {
            return;
        }
        String packageId = assetInfo.getPackageId();
        if (TextUtils.isEmpty(packageId)) {
            return;
        }
        EngineNetApi.downloadOrClick(packageId, "2", new RequestCallback<AssetDownloadInfo>() {
            @Override
            public void onSuccess(BaseResponse<AssetDownloadInfo> response) {
                LogUtils.d("clickAssetItem success");
            }

            @Override
            public void onError(BaseResponse<AssetDownloadInfo> response) {
                LogUtils.d("clickAssetItem error");
            }
        });
    }

    /**
     * 下载特效资源包
     * Download the effects resource pack
     *
     * @param assetInfo asset info
     * @param position  the index of asset info int the list
     */
    public void downloadAsset(final AssetInfo assetInfo, final int position) {
        if (assetInfo == null) {
            return;
        }
        String packageId = assetInfo.getPackageId();
        /*
         * 如果请求过，则返回
         * If the request has been made , return
         */
        if (mDownloadSet.contains(packageId)) {
            LogUtils.d("You can not request now!");
            return;
        }
        mDownloadSet.add(packageId);
        AssetsManager.get().downloadAsset(assetInfo, true, new SimpleDownListener(assetInfo.getDownloadUrl()) {
            @Override
            public void onProgress(Progress progress) {
                if (!isViewActive()) {
                    return;
                }
                getView().onDownloadProgress(position);
            }

            @Override
            public void onFinish(File file, Progress progress) {
                /*
                 * 删除请求记录
                 * Delete request record
                 */
                mDownloadSet.remove(packageId);
                if (mFileInfoDao == null) {
                    mFileInfoDao = DraftDbManager.get().getFileInfoDao();
                }
                ThreadUtils.getSinglePool().execute(new Runnable() {
                    @Override
                    public void run() {
                        String assetPath = assetInfo.getAssetPath();
                        String fileMD5 = FileUtils.getStringMd5(assetPath);
                        IUserPlugin userPlugin = PluginManager.get().getUserPlugin();
                        String userId = "";
                        if (userPlugin != null) {
                            userId = userPlugin.getUserId();
                        }
                        FileInfoEntity fileInfo = mFileInfoDao.getFile(fileMD5, userId);
                        if (fileInfo != null) {
                            fileInfo.setLocalPath(assetPath);
                            fileInfo.setUrl(assetInfo.getDownloadUrl());
                            mFileInfoDao.updateDraft(fileInfo);
                        } else {
                            fileInfo = new FileInfoEntity();
                            fileInfo.setMd5(fileMD5);
                            fileInfo.setId(fileMD5+userId);
                            fileInfo.setLocalPath(assetPath);
                            fileInfo.setUserId(userId);
                            fileInfo.setUrl(assetInfo.getDownloadUrl());
                            mFileInfoDao.insertDraft(fileInfo);
                        }
                    }
                });
                if (!isViewActive()) {
                    return;
                }
                getView().onDownloadFinish(position, assetInfo);
            }

            @Override
            public void onError(Progress progress) {
                /*
                 * 删除请求记录
                 * Delete request record
                 */
                mDownloadSet.remove(packageId);
                if (!isViewActive()) {
                    return;
                }
                getView().onDownloadError(position);
            }
        });
    }

    private boolean isViewActive() {
        try {
            VIEW view = getView();
            if (view == null) {
                return false;
            }
            return view.isActive();
        } catch (Exception e) {
            LogUtils.e(e);
        }
        return false;
    }

    private void loadData(final RequestParam requestParam, final int pageNumber, final boolean needUpdate) {
        mPageNumber = pageNumber;
        final int subType = requestParam.subType;

        if (interceptBefore(requestParam, pageNumber, needUpdate)) {
            return;
        }
        NvsVideoResolution videoResolution = EditorEngine.getInstance().getVideoResolution();
        int[] aspectRatio = new int[]{BaseInfo.AspectRatio_NoFitRatio, 1};
        if (videoResolution != null) {
            aspectRatio = AssetsManager.get().getAspectRatio(videoResolution.imageWidth * 1F / videoResolution.imageHeight);
        }
        AssetsManager.get().getAssetsList(requestParam,
                aspectRatio[0], aspectRatio[1], pageNumber, mPageSize, needUpdate, new IAssetsManager.AssetsRequestCallback<AssetList>() {
                    @Override
                    public void onSuccess(BaseResponse<AssetList> response) {
                        AssetsPresenter.this.onSuccess(response, pageNumber, subType, needUpdate);
                    }

                    @Override
                    public void onError(BaseResponse<AssetList> response) {
                        AssetsPresenter.this.onError(subType, requestParam, pageNumber, needUpdate);
                    }
                });
    }

    /**
     * Insert method before request
     * 发起请求前的插入方法
     *
     * @param requestParam 请求参数 The request parameter
     * @param pageNumber   请求页数 The request page number
     * @param needUpdate   是否需要更新数据  Is need update data or not.
     * @return 是否需要打断操作 true:yes false：no
     */
    protected boolean interceptBefore(final RequestParam requestParam, final int pageNumber, final boolean needUpdate) {
        return false;
    }

    /**
     * Handle the request result from first page
     * 处理首页获取到的数据
     *
     * @param list 首次请求的返回结果 the request result from first page
     * @return 处理后的数据 the processed data
     */
    protected List<AssetInfo> handleDataInFirstPage(List<AssetInfo> list) {
        return list;
    }

    private void onError(final int subType, RequestParam requestParam, final int pageNumber, final boolean needUpdate) {
        if (!isViewActive()) {
            return;
        }
        if (subType == 0) {
            AssetsManager.get().getLocalAssetList(requestParam, new IAssetsManager.AssetCallback() {
                @Override
                public void onSuccess(List<AssetInfo> assetInfoList) {
                    if (!isViewActive()) {
                        return;
                    }
                    if (assetInfoList != null) {
                        getView().onNewDataBack(handleDataInFirstPage(new ArrayList<>(assetInfoList)), subType, needUpdate);
                    }
                }

                @Override
                public void onFailure() {
                    if (isViewActive()) {
                        if (pageNumber == 0) {
                            getView().onDataError(subType, needUpdate);
                        }
                    }
                }
            });
        } else {
            if (isViewActive()) {
                if (pageNumber == 0) {
                    getView().onDataError(subType, needUpdate);
                } else {
                    getView().onMoreDataBack(null, subType, needUpdate);
                }
            }
        }
    }

    private void onSuccess(BaseResponse<AssetList> response, int pageNumber, int subType, boolean needUpdate) {
        if (!isViewActive()) {
            return;
        }
        AssetList data = response.getData();
        if (data == null || CommonUtils.isEmpty(data.realAssetList)) {
            if (pageNumber == 0) {
                getView().onDataError(subType, needUpdate);
            } else {
                getView().onMoreDataBack(null, subType, needUpdate);
            }
        } else {
            if (pageNumber == 0) {
                getView().onNewDataBack(handleDataInFirstPage(new ArrayList<>(data.realAssetList)), subType, needUpdate);
            } else {
                getView().onMoreDataBack(data.realAssetList, subType, needUpdate);
            }
        }
        if (AssetsManager.IS_NEW_ASSETS) {
            mHasNext = data != null && data.total > getView().getItemCount();
            if (data != null) {
                LogUtils.d("ItemCount() =  " + getView().getItemCount() + ", total = " + data.total + ", mHasNext = " + mHasNext);
            }
        } else {
            mHasNext = data != null && data.hasNext;
        }
    }

    public void setPageSize(int pageSize) {
        mPageSize = pageSize;
    }
}
