package com.meishe.business.assets.fragment.adapter;

import android.graphics.PointF;
import android.text.TextUtils;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.facebook.drawee.backends.pipeline.Fresco;
import com.facebook.drawee.drawable.ScalingUtils;
import com.facebook.drawee.generic.GenericDraweeHierarchy;
import com.facebook.drawee.generic.GenericDraweeHierarchyBuilder;
import com.facebook.drawee.generic.RoundingParams;
import com.facebook.drawee.interfaces.DraweeController;
import com.facebook.drawee.view.SimpleDraweeView;
import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.ImageLoader;
import com.meishe.base.utils.StringUtils;
import com.meishe.business.R;
import com.meishe.business.assets.AssetUtils;
import com.meishe.engine.asset.bean.AssetInfo;
import com.meishe.engine.bean.BaseInfo;
import com.meishe.third.adpater.BaseQuickAdapter;
import com.meishe.third.adpater.BaseViewHolder;

import java.util.List;

/**
 * All rights Reserved, Designed By www.meishesdk.com
 *
 * @Author: ChuChenGuang
 * @CreateDate: 2021/3/10 17:41
 * @Description: 通用 adapter  the common adapter
 * @Copyright: www.meishesdk.com Inc. All rights reserved.
 */
public class CommonAdapter extends BaseQuickAdapter<AssetInfo, BaseViewHolder> {
    protected int mSelectedPosition = -1;
    private String noneAsset = "";
    private boolean needSelected = true;
    private boolean needDisplayName = false;
    protected int mAssetSubType = 0;

    public CommonAdapter(int layoutResId) {
        super(layoutResId);
        noneAsset = StringUtils.getString(R.string.no);
    }

    public CommonAdapter(int layoutResId, boolean needSelected, boolean needDisplayName) {
        this(layoutResId);
        this.needSelected = needSelected;
        this.needDisplayName = needDisplayName;
    }

    public void setAssetSubType(int mAssetSubType) {
        this.mAssetSubType = mAssetSubType;
    }

    /**
     * 选中某一项
     * Selected item .
     *
     * @param position The index of list
     */
    public void selected(int position) {
        if (mSelectedPosition >= 0) {
            notifyItemChanged(mSelectedPosition);
        }
        mSelectedPosition = position;
        if (position >= 0 && position < getData().size()) {
            notifyItemChanged(position);
        }
    }

    /**
     * 选中某一项
     * Selected item .
     *
     * @param uuid The uuid of item
     */
    public void selected(String uuid) {
        List<AssetInfo> data = getData();
        if (CommonUtils.isEmpty(data)) {
            selected(-1);
            return;
        }
        if (TextUtils.isEmpty(uuid)) {
            if (data.size() != 0) {
                AssetInfo assetInfoFirst = data.get(0);
                if (assetInfoFirst == null || noneAsset.equals(assetInfoFirst.getName())) {
                    selected(0);
                } else {
                    selected(-1);
                }
            }
        } else {
            int index = -1;
            for (int i = 0; i < data.size(); i++) {
                AssetInfo assetInfo = data.get(i);
                if ((assetInfo.getEffectMode() == BaseInfo.EFFECT_MODE_PACKAGE && uuid.equals(assetInfo.getPackageId())) || (assetInfo.getEffectMode() == BaseInfo.EFFECT_MODE_BUILTIN && uuid.equals(assetInfo.getEffectId()))) {
                    index = i;
                    break;
                }
            }
            if (mSelectedPosition != index) {
                selected(index);
            }
        }
    }

    public int getSelectedPosition() {
        return mSelectedPosition;
    }

    public String getSelectedId() {
        AssetInfo item = getItem(mSelectedPosition);
        if (item == null || noneAsset.equals(item.getName())) {
            return "";
        }
        if (item.getEffectMode() == BaseInfo.EFFECT_MODE_PACKAGE) {
            return item.getPackageId();
        }
        if (item.getEffectMode() == BaseInfo.EFFECT_MODE_BUILTIN) {
            return item.getEffectId();
        }
        return item.getId();
    }

    @Override
    protected void convert(@NonNull BaseViewHolder helper, AssetInfo item) {
        ImageView ivCover = helper.getView(getCoverViewId());
        FrameLayout rootCover = helper.getView(R.id.root_cover);
        TextView tvName = helper.getView(R.id.tv_name);
        TextView tvAssetPurchased = helper.getView(R.id.tv_asset_purchased);
        if (AssetUtils.assetPurchasedVisible(tvAssetPurchased, mAssetSubType, item.isAuthorized())) {
            tvAssetPurchased.setVisibility(View.VISIBLE);
        } else {
            if (tvAssetPurchased != null) {
                tvAssetPurchased.setVisibility(View.GONE);
            }
        }

        int paddingNo = (int) mContext.getResources().getDimension(R.dimen.dp_px_36);
        //更改封面大小 Change cover view size
        if (helper.getAdapterPosition() == 0) {
            ivCover.setPadding(paddingNo, paddingNo, paddingNo, paddingNo);
        } else if (ivCover.getPaddingLeft() == paddingNo) {
            ivCover.setPadding(0, 0, 0, 0);
        }

        String name = item.getName();
        if (needDisplayName && tvName != null && (!TextUtils.isEmpty(name))) {
            tvName.setVisibility(View.VISIBLE);
            tvName.setText(name);
        }
        if (item.getEffectMode() == BaseInfo.EFFECT_MODE_BUILTIN) {
            ivCover.setImageResource(item.getCoverId());
            ivCover.setScaleType(ImageView.ScaleType.CENTER);
        } else {
            String coverPath = item.getCoverPath();
            loadImage(ivCover, coverPath);
        }
        if (helper.getAdapterPosition() == mSelectedPosition && needSelected) {
            rootCover.setBackground(CommonUtils.getRadiusDrawable(3, mContext.getResources().getColor(R.color.color_fffc2b55), 6, -1));
        } else {
            rootCover.setBackground(CommonUtils.getRadiusDrawable(6, mContext.getResources().getColor(R.color.color_ff242424)));
        }

        ImageView ivDownload = helper.getView(R.id.iv_downloading);
        if (!item.isHadDownloaded() || item.needUpdate()) {
            ImageLoader.loadUrl(mContext, R.mipmap.downloading, ivDownload);
            int progress = item.getDownloadProgress();
            if (progress >= 0 && progress < 100) {
                ivDownload.setVisibility(View.VISIBLE);
                ivCover.setVisibility(View.GONE);
            } else {
                ivDownload.setVisibility(View.GONE);
                ivCover.setVisibility(View.VISIBLE);
            }
        } else {
            ivDownload.setVisibility(View.GONE);
            ivCover.setVisibility(View.VISIBLE);
        }
    }

    protected int getCoverViewId() {
        return R.id.iv_cover;
    }

    protected void loadImage(ImageView ivCover, String coverPath) {
        if (ivCover instanceof SimpleDraweeView
                && !TextUtils.isEmpty(coverPath) && coverPath.startsWith("http")) {
            RoundingParams roundingParams = new RoundingParams();
            roundingParams.setCornersRadius(6);
            PointF pf = new PointF(0.5f, 0.5f);
            GenericDraweeHierarchyBuilder builder =
                    new GenericDraweeHierarchyBuilder(mContext.getResources());
            GenericDraweeHierarchy hierarchy = builder.setActualImageScaleType(ScalingUtils.ScaleType.CENTER_CROP)
                    .setActualImageFocusPoint(pf)
                    .setRoundingParams(roundingParams).build();
            ((SimpleDraweeView) ivCover).setHierarchy(hierarchy);
            DraweeController controller = Fresco.newDraweeControllerBuilder()
                    .setUri(coverPath)
                    .setAutoPlayAnimations(true)
                    .setOldController(((SimpleDraweeView) ivCover).getController())
                    .build();
            ((SimpleDraweeView) ivCover).setController(controller);
        } else {
            ImageLoader.loadUrl(mContext, coverPath, ivCover);
        }
    }
}
