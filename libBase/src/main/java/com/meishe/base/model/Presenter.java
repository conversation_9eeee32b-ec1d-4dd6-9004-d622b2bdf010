package com.meishe.base.model;

import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleOwner;

import java.lang.ref.WeakReference;
import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;
import java.lang.reflect.Proxy;

/**
 * author : lhz
 * date   : 2019/5/11
 * desc   :基类Mvp的Presenter，不包含明确的Model
 * The base class Mvp The Presenter Without the explicit reference to Model
 *
 * @param <V> the type parameter
 */
@SuppressWarnings("unchecked")

public abstract class Presenter<V extends IBaseView> implements IPresenter<V> {
    @Override
    public void onAny(LifecycleOwner owner, Lifecycle.Event event) {
    }

    @Override
    public void onCreate() {
    }

    @Override
    public void onDestroy() {
    }

    @Override
    public void onStart() {
    }

    @Override
    public void onStop() {
    }

    @Override
    public void onResume() {
    }

    @Override
    public void onPause() {
    }

    /**
     * View对象 - 弱引用
     * View object - Weak reference
     */
    private WeakReference<V> mWeakView;
    /**
     * 动态代理生成的View对象
     * View object generated by dynamic proxy
     */
    private V mProxyView;


    /**
     * 关联V层和P层
     * Correlate layer V and layer P
     */
    @Override
    public void attachView(V v) {
        mWeakView = new WeakReference<>(v);
        MvpViewHandler viewHandler = new MvpViewHandler(mWeakView.get());
        Class<?>[] interfaces = null;
        if (findBaseView(v.getClass())) {
            interfaces = v.getClass().getInterfaces();
        } else {
            Class<?> superclass = v.getClass().getSuperclass();
            while (superclass != null) {
                if (findBaseView(superclass)) {
                    interfaces = superclass.getInterfaces();
                    break;
                }
                superclass = superclass.getSuperclass();
            }
            if (interfaces == null || interfaces.length == 0) {
                throw new RuntimeException("Not find IBaseView!,Please implement IBaseView.");
            }
        }
        mProxyView = (V) Proxy.newProxyInstance(v.getClass().getClassLoader(),
                interfaces, viewHandler);
    }

    private boolean findBaseView(Class<?> clazz) {
        if (clazz == null) {
            return false;
        }
        Class<?>[] interfaces = clazz.getInterfaces();
        if (interfaces.length == 0) {
            return false;
        }

        for (Class<?> item : interfaces) {
            if (item.isInterface()) {
                if (item.isAssignableFrom(IBaseView.class)) {
                    return true;
                }
                if (findBaseView(item)) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * P层和V层是否关联.
     * Are Layers P and V related
     */
    private boolean isViewAttached() {
        return mWeakView != null && mWeakView.get() != null;
    }

    /**
     * 断开V层和P层
     * Disconnect the V and P layers
     */
    @Override
    public void detachView() {
        if (isViewAttached()) {
            mWeakView.clear();
            mWeakView = null;
        }
    }

    /**
     * 获取View对象
     * To obtain View object
     */
    @Override
    public V getView() {
        return mProxyView;
    }

    /**
     * View管理对象
     * View Management object
     */
    private class MvpViewHandler implements InvocationHandler {
        private IBaseView mView;

        /*
         * 如果V层没被销毁, 执行V层的方法.
         * If Layer V is not destroyed, perform the Layer V method.
         * */
        MvpViewHandler(IBaseView view) {
            mView = view;
        }

        @Override
        public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
            try {
                if (isViewAttached()) {
                    return method.invoke(mView, args);
                }
            } catch (Exception e) {
            }
            /*
             * P层不需要关注V层的返回值
             * The P layer does not need to care about the return value of the V layer
             * */
            return null;
        }
    }

}
