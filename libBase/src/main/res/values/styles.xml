<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- 对话框样式 -->
    <style name="Theme.Light.Dialog" parent="android:style/Theme.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:scrollHorizontally">true</item>
    </style>
    <!-- 弹出框动画 由下至上 -->
    <style name="dialogStyle_translate_bottom" parent="@android:style/Animation.Dialog">
        <item name="android:windowEnterAnimation">@anim/dialog_enter_tra_bottom</item>
        <!-- 进入时的动画 -->
        <item name="android:windowExitAnimation">@anim/dialog_exit_tra_bottom</item>
        <!-- 退出时的动画 -->
    </style>
    <!-- 弹出框动画 由上至下 -->
    <style name="dialogStyle_translate_top" parent="@android:style/Animation.Dialog">
        <item name="android:windowEnterAnimation">@anim/dialog_enter_tra_top</item>
        <!-- 进入时的动画 -->
        <item name="android:windowExitAnimation">@anim/dialog_exit_tra_top</item>
        <!-- 退出时的动画 -->
    </style>
    <!-- 弹出框动画 渐隐渐现 -->
    <style name="dialogStyle_alpha" parent="@android:style/Animation.Dialog">
        <item name="android:windowEnterAnimation">@anim/dialog_enter_alpha</item>
        <!-- 进入时的动画 -->
        <item name="android:windowExitAnimation">@anim/dialog_exit_alpha</item>
        <!-- 退出时的动画 -->
    </style>

    <style name="ActivityTranslucent" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:background">@android:color/transparent</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:backgroundDimEnabled">false</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowAnimationStyle">@null</item>
        <item name="android:windowDisablePreview">true</item>
    </style>

    <declare-styleable name="RoundBoundView">
        <attr name="boundColor" format="color" />
        <attr name="boundInnerRadius" format="dimension" />
    </declare-styleable>
</resources>