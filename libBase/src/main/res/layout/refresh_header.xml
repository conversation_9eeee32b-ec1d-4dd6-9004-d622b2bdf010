<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_px_135"
    android:gravity="center"
    android:orientation="horizontal">

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_px_135"
        android:layout_marginRight="@dimen/dp_px_6">

        <ImageView
            android:id="@+id/arrow"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_px_66"
            android:layout_centerInParent="true"
            android:scaleType="centerCrop"
            android:src="@mipmap/arrow_down" />

        <ProgressBar
            android:id="@+id/progress_bar"
            android:layout_width="@dimen/dp_px_66"
            android:layout_height="@dimen/dp_px_66"
            android:layout_centerInParent="true"
            android:visibility="gone" />
    </RelativeLayout>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_px_135"
        android:gravity="center_vertical"
        android:padding="@dimen/dp_px_15"
        android:layout_gravity="center"
        android:orientation="vertical">

        <TextView
            android:id="@+id/description"
            android:layout_width="fill_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:textSize="@dimen/sp_px_30"
            android:textColor="@color/color_ff808080"
            android:gravity="center" />

        <TextView
            android:id="@+id/updated_at"
            android:layout_width="fill_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:layout_marginTop="@dimen/dp_px_15"
            android:textSize="@dimen/sp_px_30"
            android:textColor="@color/color_ff808080"
            android:gravity="center" />
    </LinearLayout>
</LinearLayout>

