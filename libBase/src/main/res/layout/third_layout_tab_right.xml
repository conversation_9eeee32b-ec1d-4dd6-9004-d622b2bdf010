<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clipChildren="false"
    android:clipToPadding="false">

    <LinearLayout
        android:id="@+id/ll_tap"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:gravity="center"
        android:orientation="horizontal"
        tools:ignore="UseCompoundDrawables">

        <TextView
            android:id="@+id/tv_tab_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:singleLine="true"/>

        <ImageView
            android:id="@+id/iv_tab_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:contentDescription="@null" />

    </LinearLayout>

    <com.meishe.third.tablayout.widget.MsgView
        android:id="@+id/rtv_msg_tip"
        xmlns:mv="http://schemas.android.com/apk/res-auto"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_toRightOf="@+id/ll_tap"
        android:gravity="center"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_px_35"
        android:visibility="gone"
        mv:mv_backgroundColor="@color/red_fd48"
        mv:mv_isRadiusHalfHeight="true"
        mv:mv_strokeColor="@color/white"
        mv:mv_strokeWidth="@dimen/dp_px_3"
        android:layout_toEndOf="@+id/ll_tap" />

</RelativeLayout>