<?xml version="1.0" encoding="utf-8"?>
<view xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    class="com.meishe.base.utils.ToastUtils$UtilsMaxWidthRelativeLayout"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/utils_toast_bg"
    android:paddingLeft="16dp"
    android:paddingTop="12dp"
    android:paddingRight="16dp"
    android:paddingBottom="12dp">

    <View
        android:id="@+id/utvLeftIconView"
        android:layout_width="22dp"
        android:layout_height="22dp"
        android:layout_alignParentLeft="true"
        android:layout_centerVertical="true"
        android:layout_marginRight="8dp"
        android:visibility="gone"
        tools:background="#00ff00"
        tools:visibility="gone" />

    <View
        android:id="@+id/utvTopIconView"
        android:layout_width="22dp"
        android:layout_height="22dp"
        android:layout_alignParentTop="true"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="4dp"
        android:visibility="gone"
        tools:background="#00ff00"
        tools:visibility="gone" />

    <View
        android:id="@+id/utvRightIconView"
        android:layout_width="22dp"
        android:layout_height="22dp"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:layout_marginLeft="8dp"
        android:visibility="gone"
        tools:background="#00ff00"
        tools:visibility="gone" />

    <View
        android:id="@+id/utvBottomIconView"
        android:layout_width="22dp"
        android:layout_height="22dp"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="4dp"
        android:visibility="gone"
        tools:background="#00ff00"
        tools:visibility="gone" />

    <TextView
        android:id="@android:id/message"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_above="@id/utvBottomIconView"
        android:layout_below="@id/utvTopIconView"
        android:layout_centerInParent="true"
        android:layout_toLeftOf="@id/utvRightIconView"
        android:layout_toRightOf="@id/utvLeftIconView"
        android:fontFamily="sans-serif"
        android:gravity="center"
        android:lineSpacingExtra="2dp"
        android:textColor="#DE000000"
        android:textSize="14sp"
        tools:text="This is long text." />
</view>