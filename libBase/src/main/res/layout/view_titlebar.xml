<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/main_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#ff000000">

    <RelativeLayout
        android:id="@+id/backLayout"
        android:layout_width="64dp"
        android:layout_height="match_parent" />

    <ImageView
        android:id="@+id/back_layout_imageView"
        android:layout_width="@dimen/dp_px_50"
        android:layout_height="@dimen/dp_px_50"
        android:layout_centerVertical="true"
        android:layout_marginLeft="@dimen/dp13"
        android:src="@mipmap/ic_titlebar_back" />

    <TextView
        android:id="@+id/text_center"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_centerVertical="true"
        android:text="@string/videoEdit"
        android:textColor="@color/title_center_text_color"
        android:textSize="@dimen/title_textSize" />

    <RelativeLayout
        android:id="@+id/forwardLayout"
        android:layout_width="90dp"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true">

        <TextView
            android:id="@+id/text_right"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_alignParentRight="true"
            android:layout_alignParentBottom="true"
            android:layout_marginEnd="@dimen/dp13"
            android:layout_marginRight="@dimen/dp13"
            android:textColor="#ff4a90e2"
            android:textSize="@dimen/title_textSize"
            android:visibility="gone" />
    </RelativeLayout>
</RelativeLayout>