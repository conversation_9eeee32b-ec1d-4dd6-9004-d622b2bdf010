package com.meishe.user.manager.observer;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/3/11 18:40
 * @Description :上传监听对象， The observer for uploading.
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class UploadObserver {

    public void onStateChanged(String tag, int count){

    }

    public boolean isAlive(){
        return false;
    }
}
