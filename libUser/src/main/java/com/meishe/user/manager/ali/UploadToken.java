package com.meishe.user.manager.ali;

import java.io.Serializable;

/**
 * The type Upload token.
 * 上传标记类
 */
public class UploadToken implements Serializable {
    private String accessKeyId;
    private String secretAccessKey;
    private String securityToken;
    private String endpoint;
    private String bucket;
    /**
     * 上传到云存储文件的key
     * The key uploaded to cloud storage file
     */
    private String relativePath;
    private String expiration;
    private String region;
    private String objectId;
    private String projectId;

    private String host;
    /**
     * 回调时携带的信息
     * The call back info
     */
    private UploadCallBackInfo callbackInfo;

    private M3u8FileNameInfo m3u8FileNameInfo;

    /**
     * Gets access key id.
     * 获取访问密钥id
     *
     * @return the access key id 访问密钥id
     */
    public String getAccessKeyId() {
        return accessKeyId;
    }

    /**
     * Sets access key id.
     * 设置访问密钥id
     *
     * @param accessKeyId the access key id 访问密钥id
     */
    public void setAccessKeyId(String accessKeyId) {
        this.accessKeyId = accessKeyId;
    }

    /**
     * Gets secret access key.
     * 获取秘密访问密钥
     *
     * @return the secret access key 秘密访问密钥
     */
    public String getSecretAccessKey() {
        return secretAccessKey;
    }

    /**
     * Sets secret access key.
     * 设置秘密访问密钥
     *
     * @param secretAccessKey the secret access key 秘密访问密钥
     */
    public void setSecretAccessKey(String secretAccessKey) {
        this.secretAccessKey = secretAccessKey;
    }

    /**
     * Gets security token.
     * 获得安全token
     *
     * @return the security token 安全token
     */
    public String getSecurityToken() {
        return securityToken;
    }

    /**
     * Sets security token.
     * 设置安全token
     *
     * @param securityToken the security token  安全token
     */
    public void setSecurityToken(String securityToken) {
        this.securityToken = securityToken;
    }

    /**
     * Gets endpoint.
     * 获得端点
     *
     * @return the endpoint 端点
     */
    public String getEndpoint() {
        return endpoint;
    }

    /**
     * Sets endpoint.
     * 设置端点
     *
     * @param endpoint the endpoint 端点
     */
    public void setEndpoint(String endpoint) {
        this.endpoint = endpoint;
    }

    public String getBucket() {
        return bucket;
    }

    public void setBucket(String bucket) {
        this.bucket = bucket;
    }

    public String getRelativePath() {
        return relativePath;
    }

    public void setRelativePath(String relativePath) {
        this.relativePath = relativePath;
    }

    public String getExpiration() {
        return expiration;
    }

    public void setExpiration(String expiration) {
        this.expiration = expiration;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public String getObjectId() {
        return objectId;
    }

    public void setObjectId(String objectId) {
        this.objectId = objectId;
    }

    public UploadCallBackInfo getCallbackInfo() {
        return callbackInfo;
    }

    public void setCallbackInfo(UploadCallBackInfo callbackInfo) {
        this.callbackInfo = callbackInfo;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public M3u8FileNameInfo getM3u8FileNameInfo() {
        return m3u8FileNameInfo;
    }

    public void setM3u8FileNameInfo(M3u8FileNameInfo m3u8FileNameInfo) {
        this.m3u8FileNameInfo = m3u8FileNameInfo;
    }

    public static class UploadCallBackInfo implements Serializable {
        /**
         * 回调时的content-type
         * Content type during callback
         */
        private String callbackBodyType;
        /**
         *  回调的url
         *  The callback url
         */
        private String callbackUrl;
        private String callbackBody;
        /**
         * 回调携带的自定的参数
         * The custom info in callback
         */
        private CustomInfo customInfo;

        public String getCallbackBodyType() {
            return callbackBodyType;
        }

        public void setCallbackBodyType(String callbackBodyType) {
            this.callbackBodyType = callbackBodyType;
        }

        public String getCallbackUrl() {
            return callbackUrl;
        }

        public void setCallbackUrl(String callbackUrl) {
            this.callbackUrl = callbackUrl;
        }

        public String getCallbackBody() {
            return callbackBody;
        }

        public void setCallbackBody(String callbackBody) {
            this.callbackBody = callbackBody;
        }

        public CustomInfo getCustomInfo() {
            return customInfo;
        }

        public void setCustomInfo(CustomInfo customInfo) {
            this.customInfo = customInfo;
        }
    }

    public static class CustomInfo implements Serializable {
        private long objectId;
        private int resourceType;

        public long getObjectId() {
            return objectId;
        }

        public void setObjectId(long objectId) {
            this.objectId = objectId;
        }

        public int getResourceType() {
            return resourceType;
        }

        public void setResourceType(int resourceType) {
            this.resourceType = resourceType;
        }
    }

    public static class M3u8FileNameInfo implements Serializable{
        private String common;
        private String alpha;
        private String reverse;
        private String reverseAlpha;

        public String getCommon() {
            return common;
        }

        public void setCommon(String common) {
            this.common = common;
        }

        public String getAlpha() {
            return alpha;
        }

        public void setAlpha(String alpha) {
            this.alpha = alpha;
        }

        public String getReverse() {
            return reverse;
        }

        public void setReverse(String reverse) {
            this.reverse = reverse;
        }

        public String getReverseAlpha() {
            return reverseAlpha;
        }

        public void setReverseAlpha(String reverseAlpha) {
            this.reverseAlpha = reverseAlpha;
        }

        @Override
        public String toString() {
            return "M3u8FileNameInfo{" +
                    "common='" + common + '\'' +
                    ", alpha='" + alpha + '\'' +
                    ", reverse='" + reverse + '\'' +
                    ", reverseAlpha='" + reverseAlpha + '\'' +
                    '}';
        }
    }
}
