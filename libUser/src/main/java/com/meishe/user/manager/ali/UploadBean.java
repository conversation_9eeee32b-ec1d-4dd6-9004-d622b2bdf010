package com.meishe.user.manager.ali;

import java.io.Serializable;

/**
 * The type Upload bean.
 * 上传类型的类
 */
public class UploadBean implements Serializable {

    public static final int KEY_VIDEO = 1;
    public static final int KEY_AUDIO = 2;
    public static final int KEY_PACKAGE = 3;
    public static final int KEY_PROJECT_JSON = 4;
    public static final int KEY_PROJECT_TEMPLATE = 5;
    public static final int KEY_COVER = 6;
    public static final int KEY_BACKGROUND = 7;
    public static final int KEY_PROJECT_XML = 8;

    public UploadBean(int index, int pathKey, String pathValue) {
        this.index = index;
        this.pathKey = pathKey;
        this.pathValue = pathValue;
    }

    public UploadBean(int index, int pathKey, String pathValue, String uuid) {
        this.index = index;
        this.pathKey = pathKey;
        this.pathValue = pathValue;
        this.uuid = uuid;
    }

    /**
     * The Index.
     * 索引
     */
    public int index;
    /**
     * The Path key.
     * 路径key
     */
    public int pathKey;
    /**
     * The Path value.
     * 路径值
     */
    public String pathValue;

    /**
     * 原始路径，如果有路径转换的话，originalPathValue 才会存在
     */
    public String originalPathValue;

    public String uuid;

    private String uploadModule;

    public RemoteInfo remoteInfo = new RemoteInfo();

    public void setUploadModule(String uploadModule) {
        this.uploadModule = uploadModule;
    }

    public String getUploadModule() {
        return uploadModule;
    }

    @Override
    public String toString() {
        return "UploadBean{" +
                "index=" + index +
                ", pathKey=" + pathKey +
                ", pathValue='" + pathValue + '\'' +
                ", uuid='" + uuid + '\'' +
                ", uploadModule='" + uploadModule + '\'' +
                '}';
    }

    public class RemoteInfo{
        public String url;
        public String m3u8CommonUrl;
        public String resourceId;
        public String m3u8AlphaUrl;
        public String m3u8ReverseUrl;
        public String m3u8ReverseAlphaUrl;
    }
}
