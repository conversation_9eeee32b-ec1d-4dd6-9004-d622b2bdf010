package com.meishe.user.manager;

import com.meishe.base.utils.ThreadUtils;
import com.meishe.draft.data.DraftData;
import com.meishe.engine.asset.bean.TemplateUploadParam;
import com.meishe.user.manager.ali.ALiHelper;
import com.meishe.user.manager.observer.UploadObservable;
import com.meishe.user.manager.observer.UploadObserver;

import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/3/11 14:36
 * @Description :上传管理类
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class CloudUploadManager {

    private final UploadObservable mObservable;
    private int mUploadingCount;
    private Map<String, ALiHelper> mIdToALiHelperMap = new HashMap<>();

    /**
     * Instantiates a new Cloud upload manager.
     */
    public CloudUploadManager() {
        mObservable = new UploadObservable();
    }

    /**
     * Register upload observer.
     * 注册监听
     * @param observer the observer
     */
    public void registerUploadObserver(UploadObserver observer) {
        mObservable.registerObserver(observer);
    }

    /**
     * Unregister upload observer.
     * 解注册监听
     * @param observer the observer
     */
    public void unRegisterUploadObserver(UploadObserver observer) {
        mObservable.unregisterObserver(observer);
    }

    /**
     * Upload draft.
     * 上传草稿
     * @param draftData      the draft data 草稿数据
     * @param token          the token 用户token
     * @param uploadListener the upload listener 上传回调
     */
    public void uploadDraft(DraftData draftData, String token, ALiHelper.UploadListener uploadListener){
        if (draftData == null) {
            mObservable.notifyStateChanged("", 0);
            return;
        }
        mObservable.notifyStateChanged(draftData.getProjectId(), ++mUploadingCount);
        ALiHelper aLiHelper = ALiHelper.create();
        aLiHelper.setUuid(draftData.getProjectId());
        String taskId = aLiHelper.getTaskId();
        mIdToALiHelperMap.put(taskId, aLiHelper);
        String finalTaskId = taskId;
        aLiHelper.setOnUploadListener(new ALiHelper.UploadListener() {
            @Override
            public void onSuccess(DraftData data, String userName) {
                ThreadUtils.runOnUiThread(() -> {
                    if (uploadListener != null) {
                        uploadListener.onSuccess(data, userName);
                    }
                    mIdToALiHelperMap.remove(finalTaskId);
                    mObservable.notifyStateChanged(draftData.getProjectId(), --mUploadingCount);
                });
            }

            @Override
            public void onFailed(String message, int code) {
                ThreadUtils.runOnUiThread(() -> {
                    if (uploadListener != null) {
                        uploadListener.onFailed(message, code);
                    }
                    mIdToALiHelperMap.remove(finalTaskId);
                    mObservable.notifyStateChanged(draftData.getProjectId(), --mUploadingCount);
                });
            }

            @Override
            public void onProgress(long progress, long total) {
                ThreadUtils.runOnUiThread(() -> {
                    if (uploadListener != null) {
                        uploadListener.onProgress(progress, total);
                    }
                });
            }

            @Override
            public void onStart(DraftData data) {
                ThreadUtils.runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        if (uploadListener != null) {
                            uploadListener.onStart(data);
                        }
                    }
                });
            }
        });
        if (draftData.isOnlyCloud()) {
            uploadNow(aLiHelper, draftData, token);
        } else {
            aLiHelper.tryToUploadDraft(draftData, token);
        }

    }

    private void uploadNow(ALiHelper aLiHelper, DraftData draftData, String token) {
        TemplateUploadParam templateUploadParam = new TemplateUploadParam();
        DraftData.CloudInfo cloudInfo = draftData.getCloudInfo();
        templateUploadParam.materialFile = new File(cloudInfo.templatePath);
        templateUploadParam.coverFile = new File(draftData.getCoverPath());
        templateUploadParam.templateDescFilePath = cloudInfo.infoPath;
        aLiHelper.tryToUploadResource(templateUploadParam, draftData, token, true);
    }

    /**
     * Cancel task
     * 取消任务
     * @param uuidList 任务id数组 the list of task id
     */
    public void cancelTask(List<String> uuidList) {
        for (String uuid : uuidList) {
            ALiHelper aLiHelper = mIdToALiHelperMap.get(uuid);
            if (aLiHelper != null) {
                aLiHelper.cancelAllNetTask();
            }
        }
    }
}
