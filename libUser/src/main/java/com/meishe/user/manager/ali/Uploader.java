package com.meishe.user.manager.ali;

import com.alibaba.sdk.android.oss.ClientConfiguration;
import com.alibaba.sdk.android.oss.ClientException;
import com.alibaba.sdk.android.oss.OSSClient;
import com.alibaba.sdk.android.oss.ServiceException;
import com.alibaba.sdk.android.oss.callback.OSSCompletedCallback;
import com.alibaba.sdk.android.oss.common.auth.OSSCredentialProvider;
import com.alibaba.sdk.android.oss.common.auth.OSSStsTokenCredentialProvider;
import com.alibaba.sdk.android.oss.model.PutObjectRequest;
import com.alibaba.sdk.android.oss.model.PutObjectResult;
import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.FileUtils;
import com.meishe.base.utils.LogUtils;
import com.meishe.base.utils.StringUtils;
import com.meishe.base.utils.Utils;
import com.meishe.logic.utils.AppNetAPi;
import com.meishe.net.custom.BaseResponse;
import com.meishe.net.custom.RequestCallback;
import com.meishe.user.R;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/7/12 10:12
 * @Description :上传类，封装上传逻辑
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class Uploader {
   private OSSClient mOssClient;

   private UploadListener mUploadListener;
   private AtomicInteger mUploadFailedCount;
   private AtomicInteger mUploadCount;
   private int mTotalCount;
   private Map<String, Integer> mProgressMap = new HashMap<>();
   private ArrayList<Object> mNetTagList = new ArrayList<>();
   private List<UploadBean> mUploadBeanList;

   public void setUploadListener(UploadListener uploadListener) {
      this.mUploadListener = uploadListener;
   }

   public boolean startUpload(String token, List<UploadBean> uploadBeanList){
      if (CommonUtils.isEmpty(uploadBeanList)) {
         return false;
      }
      mTotalCount = uploadBeanList.size();
      mUploadBeanList = uploadBeanList;
      mUploadCount = new AtomicInteger(mTotalCount);
      mProgressMap.clear();
      mUploadFailedCount = new AtomicInteger(0);
      for (UploadBean uploadBean : uploadBeanList) {
         tryToUploadFile(token, uploadBean, mUploadListener);
      }
      return true;
   }

   /**
    * 试图上传草稿中的文件
    * Try to upload the file of draft
    */
   public void tryToUploadFile(String token, final UploadBean uploadBean, UploadListener uploadListener) {
      String filePath = uploadBean.pathValue;
      String uploadModule = uploadBean.getUploadModule();
      String extensionName = FileUtils.getFileExtension(filePath);
      mProgressMap.put(uploadBean.pathValue, 0);
      long projectId = 0;
      String taskTag = uploadBean.uuid + "|" + filePath;
      mNetTagList.add(taskTag);
      AppNetAPi.getAliSecret(taskTag, token, uploadModule, extensionName, projectId, uploadBean.uuid, false,
              new RequestCallback<UploadToken>() {

                 @Override
                 public void onSuccess(BaseResponse<UploadToken> response) {
                    if (response.getCode() == 0) {
                       UploadToken token = response.getData();
                       if (token == null) {
                          return;
                       }
                       createClient(token);
                       String resultPath = token.getHost() + "/" + token.getRelativePath();
                       UploadToken.M3u8FileNameInfo m3u8FileNameInfo = token.getM3u8FileNameInfo();
                       uploadBean.remoteInfo.url = resultPath;
                       uploadBean.remoteInfo.resourceId = token.getObjectId();
                       if (m3u8FileNameInfo != null) {
                          uploadBean.remoteInfo.m3u8CommonUrl = m3u8FileNameInfo.getCommon();
                          uploadBean.remoteInfo.m3u8AlphaUrl = m3u8FileNameInfo.getAlpha();
                          uploadBean.remoteInfo.m3u8ReverseUrl = m3u8FileNameInfo.getReverse();
                          uploadBean.remoteInfo.m3u8ReverseAlphaUrl = m3u8FileNameInfo.getReverseAlpha();
                       }
                       uploadFile(uploadBean, response.getData());
                    } else {
                       //失败后不再重复请求 Do not repeat requests after failure
                       cancelAllNetTask();
                       if (null != uploadListener) {
                          uploadListener.onFailed(response.getMessage(), response.getCode());
                       }
                    }
                 }

                 @Override
                 public void onError(BaseResponse<UploadToken> response) {
                    LogUtils.e("onFailed message = " + response.getMessage());
                    //失败后不再重复请求 Do not repeat requests after failure
                    cancelAllNetTask();
                    if (null != uploadListener) {
                       uploadListener.onFailed(response.getMessage(), response.getCode());
                    }
                 }
              });
   }

   /**
    * 上传文件
    * Upload file
    *
    * @param uploadBean  UploadBean upload object
    * @param uploadToken the upload token
    */
   private void uploadFile(final UploadBean uploadBean, final UploadToken uploadToken) {
      // 构造上传请求 Construct an upload request.
      PutObjectRequest put = new PutObjectRequest(uploadToken.getBucket(), uploadToken.getRelativePath(), uploadBean.pathValue);
      // 上传进度回调 Upload progress callback
      put.setProgressCallback((request, currentSize, totalSize) -> {
         LogUtils.d("onProgress = " + currentSize * 100 / totalSize);
         if (null != mUploadListener && mUploadFailedCount.get() == 0) {
            //如果其中有一个资源上传失败，就不再对外提供进度
            //If one of the resources fails to upload, the progress will no longer be provided externally.
            float progress = 100F / mUploadCount.get() * (currentSize * 1.0F / totalSize);
            mProgressMap.put(request.getUploadFilePath(), (int) progress);
            Set<Map.Entry<String, Integer>> entries = mProgressMap.entrySet();
            int allProgress = 0;
            for (Map.Entry<String, Integer> entry : entries) {
               allProgress += entry.getValue();
            }
            LogUtils.d("onProgress: allProgress = " + allProgress);
            mUploadListener.onProgress(allProgress, mTotalCount);
         }
      });
      mOssClient.asyncPutObject(put, new OSSCompletedCallback<PutObjectRequest, PutObjectResult>() {
         @Override
         public void onSuccess(PutObjectRequest request, PutObjectResult result) {
            mUploadCount.decrementAndGet();
            if (mUploadFailedCount.get() != 0) {
               //资源存在上传失败，不再上传工程文件
               LogUtils.e("Resource upload failed, no longer uploading project files");
               if (mUploadListener != null) {
                  mUploadListener.onFailed(StringUtils.getString(R.string.upload_ali_failed), -1);
               }
            } else {
               if (mUploadCount.get() == 0) {
                  if (mUploadListener != null) {
                     mUploadListener.onSuccess(mUploadBeanList, uploadToken.getProjectId());
                  }
               }
            }
         }

         @Override
         public void onFailure(PutObjectRequest request, ClientException clientException, ServiceException serviceException) {
            String failedMessage = StringUtils.getString(R.string.upload_ali_failed);
            // 请求异常。Request exception
            if (clientException != null) {
               // 本地异常，如网络异常等。 Local exceptions, such as network anomalies, etc
               failedMessage += clientException.getMessage();
            }
            if (serviceException != null) {
               // 服务异常。The service exception
               failedMessage += "accesskeyId error" + serviceException.getMessage();
            }
            LogUtils.e("ServiceException = " + serviceException + ", ClientException = " + clientException + " ,UploadFailedCount = " + mUploadFailedCount);
            mUploadFailedCount.incrementAndGet();
            if (null != mUploadListener) {
               mUploadListener.onFailed(failedMessage, -1);
            }
         }
      });
   }

   public void cancelAllNetTask() {
      if (!CommonUtils.isEmpty(mNetTagList)) {
         for (Object tag : mNetTagList) {
            AppNetAPi.cancelRequest(tag);
         }
      }
   }

   /**
    * 创建上传Client
    * Create Client
    *
    * @param token the UploadToken
    */
   private void createClient(UploadToken token) {
      OSSCredentialProvider credentialProvider = new OSSStsTokenCredentialProvider(token.getAccessKeyId(), token.getSecretAccessKey(), token.getSecurityToken());
      //配置 Config
      final ClientConfiguration conf = new ClientConfiguration();
      // 连接超时时间，默认15秒 Connection timeout, default to 15 seconds
      conf.setConnectionTimeout(15 * 1000);
      // Socket超时时间，默认15秒 Socket timeout, default to 15 seconds
      conf.setSocketTimeout(15 * 1000);
      // 最大并发请求数，默认5个 Maximum concurrent requests, default to 5
      conf.setMaxConcurrentRequest(9);
      //失败后最大重试次数，默认2次 Maximum number of retries after failure, default to 2
      conf.setMaxErrorRetry(3);
      mOssClient = new OSSClient(Utils.getApp(), token.getEndpoint(), credentialProvider, conf);
   }

   public interface UploadListener {

      /**
       * On success.
       */
      void onSuccess(List<UploadBean>  uploadBean, String projectId);

      /**
       * On failed.
       *
       * @param message the message
       * @param code    the code
       */
      void onFailed(String message, int code);

      /**
       * On progress.
       *
       * @param progress the progress
       * @param total    the total
       */
      void onProgress(long progress, long total);
   }
}
