package com.meishe.user.view.fragment;

import android.view.View;
import android.widget.LinearLayout;

import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentPagerAdapter;
import androidx.fragment.app.FragmentTransaction;
import androidx.viewpager.widget.ViewPager;

import com.google.android.material.tabs.TabLayout;
import com.meishe.base.adapter.CommonFragmentAdapter;
import com.meishe.base.model.BaseFragment;
import com.meishe.base.utils.CommonUtils;
import com.meishe.draft.data.DraftData;
import com.meishe.libplugin.PluginManager;
import com.meishe.libplugin.user.IUserPlugin;
import com.meishe.user.CloudDraftManager;
import com.meishe.user.R;
import com.meishe.user.UserPlugin;
import com.meishe.user.bean.CloudDraftData;
import com.meishe.user.manager.observer.DataObserver;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/3/8 18:08
 * @Description :
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class CloudDraftFragment extends BaseFragment {

    private TabLayout mTabLayout;
    private ViewPager mViewPager;
    private List<BaseCloudUpDownloadFragment> mFragmentList = new ArrayList<>();
    private DataObserver mDataObserver;
    private CloudBottomDeleteFragment mBottomDeleteView;
    private LinearLayout mBtnLogin;


    public CloudDraftFragment() {
    }

    public static CloudDraftFragment create() {
        return new CloudDraftFragment();
    }

    @Override
    protected int bindLayout() {
        return R.layout.fragment_draft_cloud_layout;
    }

    @Override
    protected void onLazyLoad() {

    }

    @Override
    protected void initView(View rootView) {
        EventBus.getDefault().register(this);
        mViewPager = rootView.findViewById(R.id.vp_pager);
        mTabLayout = rootView.findViewById(R.id.tab_layout);
        mBtnLogin = rootView.findViewById(R.id.btn_login);
        changeViewStatus();
    }


    @Override
    protected void initData() {
        mBtnLogin.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                EventBus.getDefault().post(UserPlugin.MESSAGE_LOGIN);
            }
        });
        initViewPager();
        CloudDraftManager.getInstance().registerDataObserver(mDataObserver = new DataObserver() {
            @Override
            public void onDownloadDataChanged(List<CloudDraftData> data) {
                TabLayout.Tab tabAt = mTabLayout.getTabAt(2);
                if (tabAt != null) {
                    String text = "下载中";
                    if (!CommonUtils.isEmpty(data)) {
                        text += data.size();
                    }
                    tabAt.setText(text);
                }
            }

            @Override
            public void onUploadDataChanged(List<DraftData> data) {
                TabLayout.Tab tabAt = mTabLayout.getTabAt(1);
                if (tabAt != null) {
                    String text = "上传中";
                    if (!CommonUtils.isEmpty(data)) {
                        text += data.size();
                    }
                    tabAt.setText(text);
                }
            }
        });
    }


    @Override
    public void onDestroyView() {
        super.onDestroyView();
        EventBus.getDefault().unregister(this);
        CloudDraftManager.getInstance().unRegisterDataObserver(mDataObserver);
    }

    private void initViewPager() {
        String[] tabs = getResources().getStringArray(R.array.tab_draft_up_download);
        mFragmentList.add(CloudUploadFragment.create(new CloudUploadFragment.OnDataUpdateListener() {
            @Override
            public void onDataUpDate(int count) {
                mTabLayout.getTabAt(0).setText("已上传" + count);
            }

            @Override
            public void onDataSelected() {
                switchDownloadState();
            }
        }));
        mFragmentList.add(CloudUploadingFragment.create());
        mFragmentList.add(CloudDownloadFragment.create());
        FragmentPagerAdapter adapter = new CommonFragmentAdapter(getChildFragmentManager(), mFragmentList);
        mViewPager.setAdapter(adapter);
        mViewPager.setOffscreenPageLimit(3);
        mViewPager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int i, float v, int i1) {

            }

            @Override
            public void onPageSelected(int i) {
                switchDownloadState();
            }

            @Override
            public void onPageScrollStateChanged(int i) {

            }
        });
        mTabLayout.setupWithViewPager(mViewPager);
        mTabLayout.removeAllTabs();
        for (String tab : tabs) {
            mTabLayout.addTab(mTabLayout.newTab().setText(tab));
        }
    }

    public void refreshData(boolean isByUser) {
        if (mViewPager == null) {
            return;
        }
        int currentItem = mViewPager.getCurrentItem();
        if (CommonUtils.isIndexAvailable(currentItem, mFragmentList)) {
            mFragmentList.get(currentItem).refreshData(isByUser);
        }
        changeViewStatus();
    }

    public void goManagerState(int layoutId) {
        showDeleteView(layoutId, false, new CloudBottomDeleteFragment.OnEventListener() {
            @Override
            public void onDelete() {
                int currentItem = mViewPager.getCurrentItem();
                if (CommonUtils.isIndexAvailable(currentItem, mFragmentList)) {
                    mFragmentList.get(currentItem).onDelete();
                }
            }

            @Override
            public void onDownload() {
                int currentItem = mViewPager.getCurrentItem();
                if (CommonUtils.isIndexAvailable(currentItem, mFragmentList)) {
                    mFragmentList.get(currentItem).onDownload();
                }
            }
        });
        for (BaseCloudUpDownloadFragment fragment : mFragmentList) {
            fragment.goManagerState();
        }
    }

    public void exitManagerState(int layoutId) {
        EventBus.getDefault().post(0);
        closeDeleteView(layoutId);
        for (BaseCloudUpDownloadFragment fragment : mFragmentList) {
            fragment.exitManagerState();
        }
    }

    public void deleteDraft() {
        for (BaseCloudUpDownloadFragment fragment : mFragmentList) {
            fragment.deleteDraft();
        }
    }

    /**
     * 展示删除视图
     * Show the delete view
     */
    public void showDeleteView(int bottomLayoutId, boolean canDownload, CloudBottomDeleteFragment.OnEventListener listener) {
        if (getContext() == null) {
            return;
        }
        if (mBottomDeleteView == null) {
            mBottomDeleteView = CloudBottomDeleteFragment.create(canDownload, new CloudBottomDeleteFragment.OnEventListener() {
                @Override
                public void onDelete() {
                    if (listener != null) {
                        listener.onDelete();
                    }
                    exitManagerState(bottomLayoutId);
                }

                @Override
                public void onDownload() {
                    if (listener != null) {
                        listener.onDownload();
                    }
                    exitManagerState(bottomLayoutId);
                }
            });
        }
        FragmentActivity activity = getActivity();
        if (activity == null) {
            return;
        }
        View view = activity.findViewById(bottomLayoutId);
        if (view.getVisibility() != View.VISIBLE) {
            view.setVisibility(View.VISIBLE);
        }

        FragmentTransaction transaction = activity.getSupportFragmentManager().beginTransaction();
        transaction.replace(view.getId(), mBottomDeleteView);
        transaction.commitAllowingStateLoss();
    }

    protected void switchDownloadState() {
        if (mBottomDeleteView != null && mBottomDeleteView.isAdded()) {
            int currentItem = mViewPager.getCurrentItem();
            if (CommonUtils.isIndexAvailable(currentItem, mFragmentList)) {
                BaseCloudUpDownloadFragment fragment = mFragmentList.get(currentItem);
                mBottomDeleteView.switchDownload(fragment.hasSelectData());
            }
        }
    }

    /**
     * 不显示删除视图
     * Not display the delete view
     */
    private void closeDeleteView(int bottomLayoutId) {
        if (mBottomDeleteView != null) {
            FragmentActivity activity = getActivity();
            if (activity == null) {
                return;
            }
            View view = activity.findViewById(bottomLayoutId);
            if (view.getVisibility() == View.VISIBLE) {
                view.setVisibility(View.GONE);
            }
            FragmentTransaction transaction = activity.getSupportFragmentManager().beginTransaction();
            transaction.remove(mBottomDeleteView);
            transaction.commitAllowingStateLoss();
        }
    }

    private void changeViewStatus() {
        IUserPlugin userPlugin = PluginManager.get().getUserPlugin();
        if (userPlugin != null && userPlugin.isLogin()) {
            mViewPager.setVisibility(View.VISIBLE);
            mTabLayout.setVisibility(View.VISIBLE);
            mBtnLogin.setVisibility(View.INVISIBLE);
        } else {
            mViewPager.setVisibility(View.INVISIBLE);
            mTabLayout.setVisibility(View.INVISIBLE);
            mBtnLogin.setVisibility(View.VISIBLE);
        }
    }

    /**
     * On message event.
     * 消息事件
     *
     * @param event the event
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onMessageEvent(Integer event) {

    }
}
