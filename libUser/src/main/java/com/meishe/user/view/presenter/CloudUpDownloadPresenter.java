package com.meishe.user.view.presenter;

import android.text.TextUtils;

import com.meicam.sdk.NvsAssetPackageManager;
import com.meicam.sdk.NvsStreamingContext;
import com.meishe.base.model.Presenter;
import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.FileIOUtils;
import com.meishe.base.utils.FileUtils;
import com.meishe.base.utils.GsonUtils;
import com.meishe.base.utils.LogUtils;
import com.meishe.base.utils.ThreadUtils;
import com.meishe.draft.DraftManager;
import com.meishe.draft.data.DraftData;
import com.meishe.draft.db.DraftDbManager;
import com.meishe.draft.db.FileInfoDao;
import com.meishe.draft.db.FileInfoEntity;
import com.meishe.draft.observer.DraftObserver;
import com.meishe.engine.DownloadManager;
import com.meishe.engine.asset.AssetsManager;
import com.meishe.engine.asset.bean.CloudPathMapBean;
import com.meishe.engine.bean.bridges.FileInfoBridge;
import com.meishe.engine.bean.template.ExportTemplateDescInfo;
import com.meishe.engine.db.AssetEntity;
import com.meishe.engine.observer.DownLoadObserver;
import com.meishe.engine.util.PathUtils;
import com.meishe.libplugin.PluginManager;
import com.meishe.libplugin.user.IUserPlugin;
import com.meishe.user.CloudDraftManager;
import com.meishe.user.bean.CloudDraftData;
import com.meishe.user.bean.CloudToLocalMap;
import com.meishe.user.bean.ResourceInfo;
import com.meishe.user.view.iview.CloudUpDownloadView;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.meishe.user.view.fragment.BaseCloudUpDownloadFragment.TYPE_UPLOADED;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/3/9 13:29
 * @Description :云草稿上传和下载表现层 The presenter for uploading and downloading cloud draft.
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class CloudUpDownloadPresenter extends Presenter<CloudUpDownloadView> {
    private static final int PAGE_NUMBER = 10;
    private final DownLoadObserver<CloudDraftData> mDownloadObserver;
    private boolean mHasNext;
    private int totalCount;
    private int mPageIndex;
    private final FileInfoDao mFileInfoDao;

    public CloudUpDownloadPresenter() {
        CloudDraftManager.getInstance().registerDownloadObserver(mDownloadObserver = new DownLoadObserver<CloudDraftData>() {
            @Override
            public void onStateChanged(int count) {
            }

            @Override
            public void onProgress(String tag, int progress) {
                getView().onDownloadProgress(tag, progress);
            }

            @Override
            public void onFailed(String tag) {
                onDownloadFailed(tag);
            }
        });
        mFileInfoDao = DraftDbManager.get().getFileInfoDao();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        CloudDraftManager.getInstance().unRegisterDownloadObserver(mDownloadObserver);
    }

    public void getDraftList(int type) {
        if (type == TYPE_UPLOADED) {
            getUploadedNewData();
        }
    }

    public void getUnloadDraftData(List<DraftData> data) {
        if (CommonUtils.isEmpty(data)) {
            data = CloudDraftManager.getInstance().getUploadData();
        }
        List<CloudDraftData> cloudDraftDataList = new ArrayList<>();
        for (DraftData datum : data) {
            cloudDraftDataList.add(CloudDraftData.create(datum));
        }
        getView().onNewDataBack(cloudDraftDataList);
    }


    private void getUploadedNewData() {
        IUserPlugin userPlugin = PluginManager.get().getUserPlugin();
        if (userPlugin != null) {
            CloudDraftManager.RequestCallBack requestCallBack = new CloudDraftManager.RequestCallBack() {
                @Override
                public void onSuccess(List<CloudDraftData> draftData, int projectCount) {
                    getView().onNewDataBack(draftData);
                    totalCount = draftData.size();
                    mHasNext = projectCount > totalCount;
                }

                @Override
                public void onError() {
                    getView().onDataError();
                }
            };
            String token = userPlugin.getToken();
            if (TextUtils.isEmpty(token)) {
                getView().onNewDataBack(new ArrayList<>());
            } else {
                requestNewData(token, requestCallBack);
            }
        }
    }

    private void requestNewData(String token, CloudDraftManager.RequestCallBack requestCallBack) {
        mPageIndex = 0;
        CloudDraftManager.getInstance().getCloudDraft(token, mPageIndex, PAGE_NUMBER, requestCallBack);
    }

    public boolean loadMoreData() {
        if (!mHasNext) {
            return false;
        }
        return getMoreUploadedData();
    }

    private boolean getMoreUploadedData() {
        if (!mHasNext) {
            return false;
        }
        IUserPlugin userPlugin = PluginManager.get().getUserPlugin();
        if (userPlugin != null) {
            CloudDraftManager.getInstance().getCloudDraft(userPlugin.getToken(), ++mPageIndex, PAGE_NUMBER, new CloudDraftManager.RequestCallBack() {
                @Override
                public void onSuccess(List<CloudDraftData> draftData, int projectCount) {
                    getView().onMoreDataBack(draftData);
                    totalCount += draftData.size();
                    mHasNext = projectCount > totalCount;
                }

                @Override
                public void onError() {
                    getView().onDataError();
                }
            });
        }
        return true;
    }

    public String getDownloadFolder(CloudDraftData draftData) {
        if (draftData == null) {
            return null;
        }
        String uuid = draftData.getNewUuid();
        if (TextUtils.isEmpty(uuid)) {
            uuid = draftData.getUuid();
        }
        return PathUtils.getGenerateCloudDraftFileFolder(uuid);
    }

    public String getFootageDownloadFolder() {
        return PathUtils.getCloudDraftFootageFileFolder();
    }

    /**
     * Download draft boolean.
     * 下载草稿
     *
     * @param draftData the draft data 草稿数据
     * @return the boolean 是否成功开始下载
     */
    public boolean downloadDraft(CloudDraftData draftData) {
        if (draftData == null) {
            return false;
        }
        if (CloudDraftManager.getInstance().isDownloading(draftData)) {
            return false;
        }
        String fileFolder = draftData.getDownloadFolder();
        if (TextUtils.isEmpty(fileFolder)) {
            return false;
        }
        ThreadUtils.getIoPool().execute(() -> {
            File file = new File(fileFolder);
            File[] files = file.listFiles();
            if (files != null && files.length > 0) {
                for (File value : files) {
                    FileUtils.delete(value);
                }
            }
            ThreadUtils.runOnUiThread(() -> downloadNow(draftData));
        });
        return true;
    }

    private void downloadNow(CloudDraftData draftData) {
        String fileFolder = draftData.getDownloadFolder();
        String footageFolder = draftData.getFootageFolder();
        if (TextUtils.isEmpty(fileFolder) || TextUtils.isEmpty(footageFolder)) {
            return;
        }

        DownloadManager.DownloadParam<CloudDraftData> downloadParam = new DownloadManager.DownloadParam<>(draftData.getUuid());
        downloadParam.setAttachment(draftData);
        downloadParam.appendParam(draftData.getInfoUrl(), fileFolder);
        CloudDraftManager.getInstance().downloadFile(draftData, downloadParam, 25, new DownLoadObserver<CloudDraftData>() {
            @Override
            public void onSuccess(String tag, DownloadManager.DownloadParam<CloudDraftData> param) {
                DownloadManager.Param infoParam = param.getParam(draftData.getInfoUrl());
                if (infoParam == null) {
                    return;
                }
                String jsonData = FileIOUtils.readFile2String(infoParam.dstFile,
                        "utf-8");
                ExportTemplateDescInfo descInfo = GsonUtils.fromJson(jsonData, ExportTemplateDescInfo.class);
                if (descInfo != null) {
                    Set<String> assetsId = new HashSet<>();
                    List<ExportTemplateDescInfo.InnerAssetWrapper> innerAssets = descInfo.getInnerAssets();
                    if (!CommonUtils.isEmpty(innerAssets)) {
                        List<ExportTemplateDescInfo.InnerAsset> assets = innerAssets.get(0).getAssets();
                        if (!CommonUtils.isEmpty(assets)) {
                            for (ExportTemplateDescInfo.InnerAsset asset : assets) {
                                assetsId.add(asset.uuid);
                            }
                        }
                    }

                    String templateUrl = draftData.getTemplateUrl();
                    downloadParam.appendParam(templateUrl, draftData.getUuid() + "." + FileUtils.getFileSuffix(templateUrl), fileFolder);

                    if (!assetsId.isEmpty()) {
                        AssetsManager.getAssetsUrl(new ArrayList<>(assetsId), new AssetsManager.CloudMapRequestCallBack() {
                            @Override
                            public void onSuccess(CloudPathMapBean mapBeans) {
                                if (mapBeans != null) {
                                    List<CloudPathMapBean.PathMap> elements = mapBeans.getElements();
                                    if (!CommonUtils.isEmpty(elements)) {
                                        for (CloudPathMapBean.PathMap element : elements) {
                                            String packageUrl = element.getPackageUrl();
                                            String assetsFolder = PathUtils.getCloudDraftAssetsFolder(FileUtils.getFileSuffix(packageUrl));
                                            AssetEntity assets = CloudDraftManager.getInstance().getAssets(element.getId());
                                            if (assets == null) {
                                                assets = CloudPathMapBean.create(element);
                                                CloudDraftManager.getInstance().insertAssets(assets);
                                            }
                                            FileInfoBridge.putFileInFo(draftData.getUuid(), new FileInfoBridge.FileInfo(element.getId())
                                                    .setFileName(element.getDisplayName())
                                                    .setFileNameZh(element.getDisplayNameZhCn())
                                                    .setFilePath(element.getCoverUrl())
                                                    .setCategory(element.getCategory())
                                                    .setType(element.getType())
                                                    .setKind(element.getKind())
                                                    .setCustomDisPlayName(element.getCustomDisPlayName()).setAssets(true));
                                            if (!TextUtils.isEmpty(packageUrl)) {
                                                DownloadManager.Param assetsParam = new DownloadManager.Param(packageUrl, assetsFolder, "");
                                                assetsParam.addExtendData("key", element.getId());
                                                downloadParam.appendParam(assetsParam);
                                            }
                                        }
                                    }
                                }
                                collectFootageListAndDownload(descInfo);
                            }

                            @Override
                            public void onError() {
                                collectFootageListAndDownload(descInfo);
                            }
                        });
                    } else {
                        collectFootageListAndDownload(descInfo);
                    }
                }
            }

            @Override
            public void onProgress(String tag, int progress) {
                getView().onDownloadProgress(tag, progress);
            }

            private void collectFootageListAndDownload(ExportTemplateDescInfo descInfo) {
                List<ExportTemplateDescInfo.FootageInfoWrapper> footageInfos = descInfo.getFootageInfos();
                if (CommonUtils.isEmpty(footageInfos)) {
                    onDownloadFailed(draftData.getUuid());
                    return;
                }
                List<ExportTemplateDescInfo.FootageInfo> infos = footageInfos.get(0).getInfos();
                if (CommonUtils.isEmpty(infos)) {
                    onDownloadFailed(draftData.getUuid());
                    return;
                }
                CloudDraftManager.getInstance().getProjectResourceInfo(draftData.getProjectId(), new CloudDraftManager.ResourceInfoCallback() {
                    @Override
                    public void onSuccess(ResourceInfo resourceInfo) {
                        String newUuid = draftData.getNewUuid();
                        String uuid = TextUtils.isEmpty(newUuid) ? draftData.getUuid() : newUuid;

                        if (resourceInfo == null) {
                            onDownloadFailed(draftData.getUuid());
                            return;
                        }
                        List<ResourceInfo.Resource> resourceList = resourceInfo.getResourceList();
                        if (CommonUtils.isEmpty(resourceList)) {
                            onDownloadFailed(draftData.getUuid());
                            return;
                        }
                        Map<String, String> urlToM3U8 = new HashMap<>();
                        Map<String, String> urlToChannel = new HashMap<>();
                        for (ResourceInfo.Resource resource : resourceList) {
                            String url = resource.getUrl();
                            String m3u8Url = resource.getM3u8Url();
                            FileInfoBridge.FileInfo fileInfo = new FileInfoBridge.FileInfo(url);
                            String leftChannelUrl = resource.getLeftChannelUrl();
                            fileInfo.setRemotePath(url)
                                    .setLeftChannelUrl(leftChannelUrl)
                                    .setRightChannelUrl(resource.getRightChannelUrl())
                                    .setWidth(resource.getWidth())
                                    .setHeight(resource.getHeight())
                                    .setResourceId(resource.getId())
                                    .setFileSize(resource.getFileSize())
                                    .setDuration(resource.getDuration() * 1000);
                            ResourceInfo.ThumbnailInfo thumbnailInfo = resource.getThumbnailInfo();
                            if (thumbnailInfo != null) {
                                fileInfo.setThumbNailInfo(new FileInfoBridge.ThumbNailInfo(thumbnailInfo.urlPrefix, thumbnailInfo.interval, thumbnailInfo.extension));
                            }
                            FileInfoBridge.putFileInFo(uuid, fileInfo);
                            if (!TextUtils.isEmpty(url) && !TextUtils.isEmpty(m3u8Url)) {
                                urlToM3U8.put(url, m3u8Url);
                            }

                            if (!TextUtils.isEmpty(url) && !TextUtils.isEmpty(leftChannelUrl)) {
                                urlToChannel.put(url, leftChannelUrl);
                            }
                            for (ExportTemplateDescInfo.FootageInfo info : infos) {
                                //需要和配置文件分开保存，不然容易误删
                                //It needs to be saved separately from the configuration file,
                                // otherwise it is easy to delete by mistake.
                                if (!TextUtils.isEmpty(info.url)) {
                                    if (ExportTemplateDescInfo.TYPE_FOOTAGE_INTERNAL.equals(info.type)) {
                                        String newUrl = urlToM3U8.get(info.url);
                                        if (!TextUtils.isEmpty(newUrl)) {
                                            DownloadManager.Param param = new DownloadManager.Param(newUrl, footageFolder, info.extraData);
                                            param.addExtendData("key", info.url);
                                            downloadParam.appendParam(param);
                                        } else {
                                            downloadParam.appendParam(info.url, info.extraData, footageFolder);
                                        }
                                    } else {
                                        String newUrl = urlToM3U8.get(info.url);
                                        if (!TextUtils.isEmpty(newUrl)) {
                                            DownloadManager.Param param = new DownloadManager.Param(newUrl, footageFolder, "");
                                            param.addExtendData("key", info.url);
                                            downloadParam.appendParam(param);
                                        } else {
                                            downloadParam.appendParam(info.url, footageFolder);
                                        }
                                    }
                                    String channelUrl = urlToChannel.get(info.url);
                                    if (!TextUtils.isEmpty(channelUrl)) {
                                        DownloadManager.Param param = new DownloadManager.Param(channelUrl, footageFolder);
                                        param.addExtendData("channel", "true");
                                        param.addExtendData("key", info.url);
                                        downloadParam.appendParam(param);
                                    }
                                }
                            }
                        }
                        String cover = draftData.getCover();
                        if (!TextUtils.isEmpty(cover)) {
                            DownloadManager.Param coverParam = new DownloadManager.Param(cover, footageFolder, "");
                            coverParam.addExtendData("Cover", "true");
                            downloadParam.appendParam(coverParam);
                        }
                        CloudDraftManager.getInstance().downloadFile(downloadParam, 75);
                    }

                    @Override
                    public void onError() {
                        onDownloadFailed(draftData.getUuid());
                    }
                });
            }

            @Override
            public void onFailed(String tag) {
                onDownloadFailed(tag);
            }
        });
    }

    public void deleteDraft(List<CloudDraftData> draftList, CloudDraftManager.CloudDeleteCallBack callBack) {
        String[] list = new String[draftList.size()];
        for (int index = 0; index < draftList.size(); index++) {
            list[index] = draftList.get(index).getProjectId();
        }
        CloudDraftManager.getInstance().deleteDraft(list, new CloudDraftManager.CloudDeleteCallBack() {
            @Override
            public void onSuccess() {
                if (callBack != null) {
                    callBack.onSuccess();
                }
                List<String> data = new ArrayList<>();
                for (CloudDraftData cloudDraftData : draftList) {
                    data.add(cloudDraftData.getUuid());
                }
                DraftManager.getInstance().deleteDraftFromNet(data);
            }

            @Override
            public void onError() {
                if (callBack != null) {
                    callBack.onError();
                }
            }
        });
    }


    public void getDownloadList() {
        getView().onNewDataBack(CloudDraftManager.getInstance().getDownloadData());
    }

    /**
     * Handle download data.
     * 处理下载数据
     *
     * @param tag              the tag
     * @param param            the param
     * @param downLoadObserver the down load observer
     */
    public void handleDownloadData(final String tag, DownloadManager.DownloadParam<CloudDraftData> param, DraftObserver downLoadObserver) {
        CloudDraftData datum = param.getAttachment();
        String templatePath = null;
        String infoPath = null;
        String toJson = null;
        long allFileSize = 0;
        Map<String, DownloadManager.Param> params = param.getParams();
        String coverPath = null;
        CloudToLocalMap map = new CloudToLocalMap();
        String projectId = datum.getNewUuid();
        boolean isCloud = TextUtils.isEmpty(projectId);
        if (isCloud) {
            projectId = datum.getUuid();
        }
        if (!CommonUtils.isEmpty(params)) {
            Set<Map.Entry<String, DownloadManager.Param>> entries = params.entrySet();
            for (Map.Entry<String, DownloadManager.Param> entry : entries) {
                DownloadManager.Param value = entry.getValue();
                String url = value.getExtendData("key");
                if (TextUtils.isEmpty(url)) {
                    url = value.url;
                }

                String cover = value.getExtendData("Cover");
                if ("true".equals(cover)) {
                    coverPath = value.dstFile;
                }
                String channelUrl = value.getExtendData("channel");
                if ("true".equals(channelUrl)) {
                    FileInfoBridge.FileInfo fileInfo = FileInfoBridge.getFileInfo(projectId, url);
                    if (fileInfo != null) {
                        fileInfo.setLeftChannelUrl(value.dstFile);
                    }
                    continue;
                }
                FileInfoBridge.FileInfo fileInfo = FileInfoBridge.getFileInfo(projectId, url);
                if (fileInfo != null) {
                    if (!fileInfo.isAssets()) {
                        fileInfo.setKey(value.dstFile);
                        fileInfo.setFilePath(value.dstFile);
                        FileInfoBridge.putFileInFo(projectId, fileInfo);
                    }
                    //是普通文件，入库，下次上传无需再
                    //It is a regular file that needs to be stored. There is no need to upload it again next time
                    String finalUrl = url;
                    if (!finalUrl.toLowerCase().endsWith("m3u8")) {
                        final String localPath = value.dstFile;
                        ThreadUtils.getSinglePool().execute(() -> {
                            String fileMd5 = FileUtils.getFileMD5ToString(localPath);
                            String userId = "";
                            IUserPlugin userPlugin = PluginManager.get().getUserPlugin();
                            if (userPlugin != null) {
                                userId = userPlugin.getUserId();
                            }
                            FileInfoEntity fileInfoEntity = mFileInfoDao.getFile(fileMd5, userId);
                            if (fileInfoEntity != null) {
                                fileInfoEntity.setLocalPath(localPath);
                                fileInfoEntity.setUrl(finalUrl);
                                File file = new File(fileInfo.filePath);
                                if (file.exists()) {
                                    fileInfoEntity.setM3u8CommonUrl(file.getName());
                                }
                                fileInfoEntity.setResourceId(fileInfo.resourceId);
                                mFileInfoDao.updateDraft(fileInfoEntity);
                            } else {
                                fileInfoEntity = new FileInfoEntity();
                                fileInfoEntity.setMd5(fileMd5);
                                fileInfoEntity.setId(fileMd5 + userId);
                                fileInfoEntity.setLocalPath(localPath);
                                fileInfoEntity.setUrl(finalUrl);
                                fileInfoEntity.setUserId(userId);
                                File file = new File(fileInfo.filePath);
                                if (file.exists()) {
                                    fileInfoEntity.setM3u8CommonUrl(file.getName());
                                }
                                fileInfoEntity.setResourceId(fileInfo.resourceId);
                                mFileInfoDao.insertDraft(fileInfoEntity);
                            }
                        });
                    }
                    if (fileInfo.isAssets()) {
                        NvsAssetPackageManager assetPackageManager = NvsStreamingContext.getInstance().getAssetPackageManager();
                        String packageId = assetPackageManager.getAssetPackageIdFromAssetPackageFilePath(value.dstFile);
                        AssetEntity assets = CloudDraftManager.getInstance().getAssets(packageId);
                        if (assets != null) {
                            assets.setAssetPath(value.dstFile);
                            CloudDraftManager.getInstance().updateAssets(assets);
                        }
                    }
                    allFileSize += fileInfo.getFileSize();
                    map.add(url, value.dstFile, fileInfo.isAssets());
                }
            }
            toJson = GsonUtils.toJson(map);
            DownloadManager.Param paramItem = params.get(datum.getTemplateUrl());
            if (paramItem != null) {
                templatePath = paramItem.dstFile;
            }
            paramItem = params.get(datum.getInfoUrl());
            if (paramItem != null) {
                infoPath = paramItem.dstFile;
            }
        }
        String finalProjectId = projectId;
        String finalTemplatePath = templatePath;
        String finalCoverPath = coverPath;
        String finalToJson = toJson;
        String finalInfoPath = infoPath;
        long finalAllFileSize = allFileSize;
        ThreadUtils.getIoPool().execute(() -> {
            List<FileInfoBridge.FileInfo> infoList = FileInfoBridge.getFileInfoList(finalProjectId);
            if (!CommonUtils.isEmpty(infoList)) {
                for (FileInfoBridge.FileInfo fileInfo : infoList) {
                    CloudDraftManager.getInstance().updateResourceData(finalProjectId, fileInfo);
                }
            }
            //FileInfoBridge.clearData(finalProjectId);
            ThreadUtils.runOnUiThread(() -> {
                if (TextUtils.isEmpty(finalTemplatePath)) {
                    onDownloadFailed(tag);
                    LogUtils.e("TemplatePath dir is null!");
                    return;
                }
                File parentFile = new File(finalTemplatePath).getParentFile();
                if (parentFile != null) {
                    DraftManager.getInstance().setCurrentDraftDir(parentFile.getAbsolutePath());
                } else {
                    onDownloadFailed(tag);
                    LogUtils.e("Draft dir is null!");
                    return;
                }
                List<CloudToLocalMap.Item> cloudToLocalList = map.getCloudToLocalList();
                if (CommonUtils.isEmpty(cloudToLocalList)) {
                    onDownloadFailed(tag);
                    LogUtils.e("Local data is null!");
                } else {
                    DraftManager.getInstance().updateCloudDraft(finalProjectId, datum.getProjectId(), datum.getName(), finalCoverPath, finalTemplatePath, finalInfoPath, finalToJson,
                            datum.getDuration() * 1000, finalAllFileSize, String.valueOf(Long.parseLong(datum.getModifiedTime()) * 1000), downLoadObserver);
                    datum.setState(CloudDraftData.STATE_DOWNLOADED);
                    getView().onDownloadSuccess(tag, datum);
                }
            });
        });
    }

    private void onDownloadFailed(String tag) {
        CloudDraftManager.getInstance().removeDownloadData(tag);
        getView().onDownloadFailed(tag);
    }

    public void cancelUpload(List<CloudDraftData> deleteData) {
        List<String> uuidList = new ArrayList<>();
        for (CloudDraftData deleteDatum : deleteData) {
            uuidList.add(deleteDatum.getUuid());
        }
        CloudDraftManager.getInstance().cancelUpload(uuidList);
    }

    public void cancelDownload(List<CloudDraftData> deleteData) {
        List<String> uuidList = new ArrayList<>();
        for (CloudDraftData deleteDatum : deleteData) {
            uuidList.add(deleteDatum.getUuid());
        }
        CloudDraftManager.getInstance().cancelDownload(uuidList);
    }
}
