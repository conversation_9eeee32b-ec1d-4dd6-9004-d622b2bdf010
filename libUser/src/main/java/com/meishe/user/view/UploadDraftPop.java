package com.meishe.user.view;

import android.content.Context;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.meishe.base.utils.ThreadUtils;
import com.meishe.base.utils.ToastUtils;
import com.meishe.draft.data.DraftData;
import com.meishe.third.pop.XPopup;
import com.meishe.third.pop.core.CenterPopupView;
import com.meishe.user.R;
import com.meishe.user.manager.ali.ALiHelper;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> LiHangZhou
 * @CreateDate :2020/12/4 18:01
 * @Description :上传草稿弹窗 upload the draft pop
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class UploadDraftPop extends CenterPopupView {
    private ProgressBar mPbProgress;
    private ALi<PERSON>elper mHelper;

    public static UploadDraftPop create(Context context) {
        return (UploadDraftPop) new XPopup
                .Builder(context)
                .dismissOnTouchOutside(false)
                .dismissOnBackPressed(false)
                .autoDismiss(false)
                .asCustom(new UploadDraftPop(context));
    }

    public UploadDraftPop(@NonNull Context context) {
        super(context);
    }

    @Override
    protected int getImplLayoutId() {
        return R.layout.dialog_upload_progress;
    }

    @Override
    protected void onCreate() {
        super.onCreate();
        mPbProgress = findViewById(R.id.progressbar);
        if (mHelper == null) {
            mHelper = ALiHelper.create();
        }
        mHelper.setOnUploadListener(new ALiHelper.UploadListener() {
            View vSuccess;
            TextView tvSuccess;

            @Override
            public void onSuccess(DraftData data, String userName) {
                if (vSuccess == null) {
                    vSuccess = LayoutInflater.from(getContext()).inflate(R.layout.toast_upload_success, null);
                    tvSuccess = vSuccess.findViewById(R.id.tv_toast_success);
                }
                tvSuccess.setText(String.format(getResources().getString(R.string.upload_input_dialog_success), userName));
                ToastUtils.make().setGravity(Gravity.CENTER, -1, -1).show(vSuccess);
                mPbProgress.setProgress(0);
                dismiss();
            }

            @Override
            public void onFailed(String message, int code) {
                ThreadUtils.runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        mPbProgress.setProgress(0);
                        ToastUtils.make().setGravity(Gravity.CENTER, -1, -1).setDurationIsLong(false).show(R.string.upload_fail);
                        dismiss();
                    }
                });
            }

            @Override
            public void onProgress(long progress, long total) {
                if (mPbProgress != null) {
                    mPbProgress.setProgress((int) (100 * progress / total));
                }
            }

            @Override
            public void onStart(DraftData data) {

            }
        });
    }

    public void show(DraftData draftData, String token) {
        if (mHelper == null) {
            mHelper = ALiHelper.create();
        }
        mHelper.tryToUploadDraft(draftData, token);
        show();
    }


    @Override
    protected void onDismiss() {
        super.onDismiss();
    }
}
