package com.meishe.user.view.fragment;

import android.view.View;

import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.meishe.base.model.BaseMvpFragment;
import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.SizeUtils;
import com.meishe.base.view.PullToRefreshAndPushToLoadView;
import com.meishe.base.view.decoration.ItemDecoration;
import com.meishe.user.R;
import com.meishe.user.bean.CloudDraftData;
import com.meishe.user.view.adapter.CloudUpDownloadAdapter;
import com.meishe.user.view.iview.CloudUpDownloadView;
import com.meishe.user.view.presenter.CloudUpDownloadPresenter;

import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/3/9 13:28
 * @Description :
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class BaseCloudUpDownloadFragment extends BaseMvpFragment<CloudUpDownloadPresenter> implements CloudUpDownloadView {
    protected RecyclerView mRecyclerView;
    protected CloudUpDownloadAdapter mAdapter;
    protected PullToRefreshAndPushToLoadView mRefreshLayout;
    private CloudBottomDeleteFragment mBottomDeleteView;

    @Override
    protected CloudUpDownloadPresenter createPresenter() {
        return new CloudUpDownloadPresenter();
    }

    @Override
    protected int bindLayout() {
        return R.layout.fragment_draft_cloud_up_download_layout;
    }

    @Override
    protected void onLazyLoad() {
    }

    @Override
    protected void initView(View rootView) {
        mRecyclerView = rootView.findViewById(R.id.rv_list);
        mAdapter = new CloudUpDownloadAdapter();
        mRecyclerView.setLayoutManager(new GridLayoutManager(getContext(), 3));
        int decoration = SizeUtils.dp2px(7.5F);
        mRecyclerView.addItemDecoration(new ItemDecoration(decoration, decoration, decoration, decoration));
        mRecyclerView.post(new Runnable() {
            @Override
            public void run() {
                int itemSize = (int) ((mRecyclerView.getWidth() - decoration * 6) / 3F);
                mAdapter.setItemSize(itemSize);
            }
        });
        mRecyclerView.setAdapter(mAdapter);
        mRefreshLayout = rootView.findViewById(R.id.ptl_recyclerView);
        mRefreshLayout.setCanLoadMore(true);
        mRefreshLayout.setCanRefresh(true);
        mRefreshLayout.finishRefreshing();
        initListener();
    }

    @Override
    protected void initData() {

    }

    protected void initListener() {

    }

    @Override
    public void onNewDataBack(List<CloudDraftData> data) {
        if (mAdapter != null) {
            mAdapter.setNewData(data);
            mRecyclerView.smoothScrollToPosition(0);
            finishRefresh();
            finishLoading();
        }
    }

    @Override
    public void onMoreDataBack(List<CloudDraftData> data) {
        if (!CommonUtils.isEmpty(data)) {
            mAdapter.addData(data);
        }
        finishRefresh();
        finishLoading();
    }

    @Override
    public void onDataError() {
        finishRefresh();
        finishLoading();
    }

    @Override
    public void onDownloadFailed(String tag) {
    }

    @Override
    public void onDownloadProgress(String tag, int progress) {

    }

    @Override
    public void onDownloadSuccess(String tag, CloudDraftData param) {

    }

    protected void finishLoading() {
        if (mRefreshLayout.isLoading()) {
            mRefreshLayout.finishLoading();
        }
    }

    protected void finishRefresh() {
        if (mRefreshLayout.isRefreshing()) {
            mRefreshLayout.finishRefreshing();
        }
    }


    @Override
    public void refreshData(boolean isByUser) {
    }

    public void goManagerState() {
        if (mAdapter != null) {
            mAdapter.goManagerState();
        }
    }

    public void exitManagerState() {
        if (mAdapter != null) {
            mAdapter.exitManagerState();
        }
    }

    public void deleteDraft() {

    }

    public boolean hasSelectData() {
        List<CloudDraftData> data = mAdapter.getData();
        for (CloudDraftData datum : data) {
            if (datum.isSelected()) {
                return true;
            }
        }
        return false;
    }

    public void onDelete(){

    }


    public void onDownload(){

    }
}
