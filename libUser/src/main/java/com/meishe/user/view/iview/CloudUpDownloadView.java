package com.meishe.user.view.iview;

import com.meishe.base.model.IBaseView;
import com.meishe.user.bean.CloudDraftData;

import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/3/9 13:30
 * @Description :
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public interface CloudUpDownloadView extends IBaseView {
    /**
     * The constant TYPE_UPLOADING.
     */
    int TYPE_UPLOADING = 1;
    /**
     * The constant TYPE_UPLOADED.
     */
    int TYPE_UPLOADED = 0;
    /**
     * The constant TYPE_DOWNLOADING.
     */
    int TYPE_DOWNLOADING = 2;

    /**
     * On new data back.
     *
     * @param data the data
     */
    void onNewDataBack(List<CloudDraftData> data);

    /**
     * On more data back.
     *
     * @param data the data
     */
    void onMoreDataBack(List<CloudDraftData> data);

    /**
     * On data error.
     */
    void onDataError();

    /**
     * On download failed.
     *
     * @param tag the tag
     */
    void onDownloadFailed(String tag);

    void onDownloadProgress(String tag, int progress);

    void onDownloadSuccess(String tag, CloudDraftData param);

    void refreshData(boolean isByUser);
}
