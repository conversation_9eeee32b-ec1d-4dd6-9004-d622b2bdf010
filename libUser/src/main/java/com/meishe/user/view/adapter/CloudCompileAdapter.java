package com.meishe.user.view.adapter;

import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.meishe.base.utils.ImageLoader;
import com.meishe.base.utils.StringUtils;
import com.meishe.third.adpater.BaseQuickAdapter;
import com.meishe.third.adpater.BaseViewHolder;
import com.meishe.user.R;
import com.meishe.user.bean.VideoCompileBean;
import com.meishe.user.view.CustomProgress;

import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/3/9 14:06
 * @Description :云合成adapter The cloud compile adapter
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class CloudCompileAdapter extends BaseQuickAdapter<VideoCompileBean, BaseViewHolder> {

    public CloudCompileAdapter() {
        super(R.layout.item_cloud_draft_compile_layout);
    }

    @NonNull
    @Override
    public BaseViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        BaseViewHolder holder = super.onCreateViewHolder(parent, viewType);
        holder.addOnClickListener(R.id.iv_delete);
        holder.addOnClickListener(R.id.iv_download);
        return holder;
    }

    @Override
    protected void convert(@NonNull BaseViewHolder helper, VideoCompileBean item) {
        if (item == null) {
            return;
        }
        ImageView coverView = helper.getView(R.id.iv_cover);
        ImageLoader.loadUrl(mContext, item.getCoverUrl(), coverView);
        CustomProgress progressBar = helper.getView(R.id.progress_bar);
        ImageView downloadImage = helper.getView(R.id.iv_download);
        TextView downloadText = helper.getView(R.id.tv_download);
        if (item.isCompiling()) {
            visibleView(progressBar);
            progressBar.setProgress(item.getCompileProgress());
        } else {
            goneView(progressBar);
        }
        if (item.isCompiling() || item.isDownloading()) {
            downloadImage.setImageResource(R.mipmap.ic_cloud_compile_download_unselect);
            goneView(downloadText);
            if (item.isDownloading()) {
                visibleView(downloadText);
                invisibleView(downloadImage);
            }
        } else {
            if (item.isNeedDownload()) {
                invisibleView(downloadText);
                downloadImage.setImageResource(R.mipmap.ic_cloud_compile_download_select);
                visibleView(downloadImage);
            } else {
                invisibleView(downloadImage);
                visibleView(downloadText);
                downloadText.setText(R.string.download_success);
            }
        }

        TextView view = helper.getView(R.id.tv_title);
        view.setText(item.getTitle());
        view = helper.getView(R.id.tv_duration);
        view.setText(item.getDurationString());
        view = helper.getView(R.id.tv_create_at);
        view.setText(StringUtils.getString(R.string.create_at) + item.getCreatedAt());
        view = helper.getView(R.id.tv_size);
        view.setText(item.getFileSize());
    }

    @Override
    protected void convertPayloads(@NonNull BaseViewHolder helper, VideoCompileBean item, @NonNull List<Object> payloads) {
        if (payloads.size() < 1) {
            convert(helper,item);
        } else {
            for (Object payload : payloads) {
                // 刷新倒计时 Refresh countdown
                if (payload instanceof Integer) {
                    if (((int) payload) == 1) {
                        CustomProgress progressBar = helper.getView(R.id.progress_bar);
                        visibleView(progressBar);
                        int progress = progressBar.getProgress();
                        if (progress > item.getCompileProgress()) {
                            progressBar.setProgress(progress);
                        } else {
                            progressBar.setProgress(item.getCompileProgress());
                        }
                    } else if (((int) payload) == 2) {
                        TextView tvDownload = helper.getView(R.id.tv_download);
                        visibleView(tvDownload);
                        goneView(helper.getView(R.id.iv_download));
                        int downloadProgress = item.getDownloadProgress();
                        tvDownload.setText(downloadProgress == 100 ?
                                StringUtils.getString(R.string.download_success) : downloadProgress + "%");
                    }
                }
            }
        }
    }

    private void goneView(View view) {
        if (view.getVisibility() != View.GONE) {
            view.setVisibility(View.GONE);
        }
    }

    private void invisibleView(View view) {
        if (view.getVisibility() != View.INVISIBLE) {
            view.setVisibility(View.INVISIBLE);
        }
    }
    private void visibleView(View view) {
        if (view.getVisibility() != View.VISIBLE) {
            view.setVisibility(View.VISIBLE);
        }
    }
}
