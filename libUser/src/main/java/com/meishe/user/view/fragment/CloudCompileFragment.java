package com.meishe.user.view.fragment;

import android.view.View;
import android.widget.LinearLayout;

import androidx.fragment.app.FragmentPagerAdapter;
import androidx.viewpager.widget.ViewPager;

import com.google.android.material.tabs.TabLayout;
import com.meishe.base.adapter.CommonFragmentAdapter;
import com.meishe.base.model.BaseMvpFragment;
import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.StringUtils;
import com.meishe.base.utils.ThreadUtils;
import com.meishe.libplugin.PluginManager;
import com.meishe.libplugin.user.IUserPlugin;
import com.meishe.user.R;
import com.meishe.user.UserPlugin;
import com.meishe.user.bean.VideoCompileBean;
import com.meishe.user.manager.CloudCompileManager;
import com.meishe.user.view.iview.CloudCompileView;
import com.meishe.user.view.presenter.CloudCompilePresenter;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/3/8 18:08
 * @Description :
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class CloudCompileFragment extends BaseMvpFragment<CloudCompilePresenter> implements CloudCompileView {

    private TabLayout mTabLayout;
    private ViewPager mViewPager;
    private List<BaseCloudCompileFragment> mFragmentList = new ArrayList<>();
    private CloudCompileManager.CompileUpDownloadDataObserver mCompileUnDownloadDataObserver;
    private LinearLayout mBtnLogin;
    public CloudCompileFragment() {
    }

    public static CloudCompileFragment create() {
        return new CloudCompileFragment();
    }

    @Override
    protected int bindLayout() {
        return R.layout.fragment_draft_cloud_layout;
    }

    @Override
    protected void onLazyLoad() {

    }

    @Override
    protected void initView(View rootView) {
        EventBus.getDefault().register(this);
        mViewPager = rootView.findViewById(R.id.vp_pager);
        mTabLayout = rootView.findViewById(R.id.tab_layout);
        mBtnLogin = rootView.findViewById(R.id.btn_login);
        changeViewStatus();
    }

    @Override
    protected void initData() {
        mBtnLogin.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                EventBus.getDefault().post(UserPlugin.MESSAGE_LOGIN);
            }
        });
        initViewPager();
    }

    public void goToCompilingPage(){
        ThreadUtils.runOnUiThreadDelayed(new Runnable() {
            @Override
            public void run() {
                if (mViewPager != null) {
                    mViewPager.setCurrentItem(1);
                }
            }
        }, 200);
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        EventBus.getDefault().unregister(this);
        CloudCompileManager.getInstance().release();
        CloudCompileManager.getInstance().unregisterUpDownloadDataObserver(mCompileUnDownloadDataObserver);
    }

    private void initViewPager() {
        String[] tabs = getResources().getStringArray(R.array.tab_draft_compile);
        CloudCompileManager.getInstance().registerUpDownloadDataObserver(mCompileUnDownloadDataObserver = new CloudCompileManager.CompileUpDownloadDataObserver(){
            @Override
            public void onDownloadDataChanged(List<VideoCompileBean> data) {
                TabLayout.Tab tabAt = mTabLayout.getTabAt(2);
                if (tabAt != null) {
                    String text = StringUtils.getString(R.string.tab_draft_compile_3);
                    if (!CommonUtils.isEmpty(data)) {
                        text += data.size();
                    }
                    tabAt.setText(text);
                }
            }

            @Override
            public void onUploadDataChanged(List<VideoCompileBean> data) {
                TabLayout.Tab tabAt = mTabLayout.getTabAt(1);
                if (tabAt != null) {
                    String text = StringUtils.getString(R.string.tab_draft_compile_2);
                    if (!CommonUtils.isEmpty(data)) {
                        text += data.size();
                    }
                    tabAt.setText(text);
                }
            }
        });
        mFragmentList.add(CloudCompileAllFragment.create());
        mFragmentList.add(CloudCompilingFragment.create());
        mFragmentList.add(CloudCompileDownloadFragment.create());
        FragmentPagerAdapter adapter = new CommonFragmentAdapter(getChildFragmentManager(), mFragmentList);
        mViewPager.setAdapter(adapter);
        mViewPager.setOffscreenPageLimit(3);
        mViewPager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int i, float v, int i1) {

            }

            @Override
            public void onPageSelected(int i) {

            }

            @Override
            public void onPageScrollStateChanged(int i) {

            }
        });
        mTabLayout.setupWithViewPager(mViewPager);
        mTabLayout.removeAllTabs();
        for (String tab : tabs) {
            mTabLayout.addTab(mTabLayout.newTab().setText(tab));
        }
    }

    public void refreshData(boolean isByUser) {
        if (mViewPager == null) {
            return;
        }
        changeViewStatus();
        int currentItem = mViewPager.getCurrentItem();
        if (CommonUtils.isIndexAvailable(currentItem, mFragmentList)) {
            mFragmentList.get(currentItem).refreshData(isByUser);
        }
    }

    private void changeViewStatus() {
        IUserPlugin userPlugin = PluginManager.get().getUserPlugin();
        if (userPlugin != null && userPlugin.isLogin()) {
            mViewPager.setVisibility(View.VISIBLE);
            mTabLayout.setVisibility(View.VISIBLE);
            mBtnLogin.setVisibility(View.INVISIBLE);
        } else {
            mViewPager.setVisibility(View.INVISIBLE);
            mTabLayout.setVisibility(View.INVISIBLE);
            mBtnLogin.setVisibility(View.VISIBLE);
        }
    }

    /**
     * On message event.
     * 消息事件
     *
     * @param event the event
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onMessageEvent(Integer event) {

    }
}
