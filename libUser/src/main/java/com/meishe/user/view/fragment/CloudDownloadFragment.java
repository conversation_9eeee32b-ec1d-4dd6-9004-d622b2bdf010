package com.meishe.user.view.fragment;

import android.view.View;

import com.meishe.base.utils.CommonUtils;
import com.meishe.base.view.PullToRefreshAndPushToLoadView;
import com.meishe.libplugin.user.IUserPlugin;
import com.meishe.user.CloudDraftManager;
import com.meishe.user.R;
import com.meishe.user.bean.CloudDraftData;
import com.meishe.user.manager.observer.DataObserver;
import com.meishe.user.view.DownloadConfirmPop;
import com.meishe.user.view.iview.CloudUpDownloadView;

import java.util.ArrayList;
import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/3/9 13:28
 * @Description :
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class CloudDownloadFragment extends BaseCloudUpDownloadFragment implements CloudUpDownloadView {
    private DataObserver mDataObserver;

    public static CloudDownloadFragment create() {
        return new CloudDownloadFragment();
    }

    @Override
    protected void onLazyLoad() {
        mPresenter.getDownloadList();
    }

    @Override
    protected void initView(View rootView) {
        super.initView(rootView);
        mAdapter.setIsDownloading(true);
        mRefreshLayout.setCanLoadMore(false);
        mRefreshLayout.setCanRefresh(false);
    }

    @Override
    public void initListener() {
        CloudDraftManager.getInstance().registerDataObserver(mDataObserver = new DataObserver() {
            @Override
            public void onDownloadDataChanged(List<CloudDraftData> data) {
                onNewDataBack(new ArrayList<>(data));
            }
        });
        mAdapter.setOnItemChildClickListener((adapter, view, position) -> {
            if (view.getId() == R.id.iv_select_clicked) {
                CloudDraftData item = mAdapter.getItem(position);
                item.setSelected(!item.isSelected());
                mAdapter.notifyItemChanged(position);
            }
        });
        mRefreshLayout.setOnRefreshAndLoadMoreListener(new PullToRefreshAndPushToLoadView.PullToRefreshAndPushToLoadMoreListener() {
            @Override
            public void onRefresh() {
                finishLoading();
                finishRefresh();
            }

            @Override
            public void onLoadMore() {
                finishLoading();
                finishRefresh();
            }
        });
    }

    @Override
    public void onDownloadProgress(String tag, int progress) {
        List<CloudDraftData> data = mAdapter.getData();
        if (CommonUtils.isEmpty(data)) {
            return;
        }
        for (int index = 0; index < data.size(); index++) {
            CloudDraftData datum = data.get(index);
            if (tag.equals(datum.getUuid())) {
                datum.setProgress(progress);
                CloudDraftData item = mAdapter.getItem(index);
                if (item.getState() != CloudDraftData.STATE_DOWNLOADING) {
                }
                mAdapter.notifyItemChanged(index, 1);
            }
        }
    }

    @Override
    public void onDownloadSuccess(String tag, CloudDraftData draftData) {
        List<CloudDraftData> data = mAdapter.getData();
        for (int index = 0; index < data.size(); index++) {
            CloudDraftData datum = data.get(index);
            if (tag.equals(datum.getUuid())) {
                datum.setState(draftData.getState());
                mAdapter.notifyItemChanged(index);
            }
        }
    }


    @Override
    public void onDelete() {
        List<CloudDraftData> data = mAdapter.getData();
        List<CloudDraftData> deleteData = new ArrayList<>();
        for (int index = data.size() - 1; index >= 0; index--) {
            CloudDraftData draftData = data.get(index);
            if (draftData.isSelected()) {
                deleteData.add(draftData);
            }
        }
        if (deleteData.size() > 0) {
            showCancelDownloadDialog(deleteData);
        }
    }

    private void showCancelDownloadDialog(List<CloudDraftData> deleteData) {
        DownloadConfirmPop.Builder builder = new DownloadConfirmPop.Builder();
        builder.setHintText(R.string.hint_cloud_cancel_download_pop_top)
                .setCancelText(R.string.cancel)
                .setConfirmText(R.string.confirm)
                .build(getActivity(), new IUserPlugin.OnEventListener<Object>() {
            @Override
            public void onConfirm(Object obj) {
                //取消下载 Cancel download
                cancelDownload(deleteData);

            }

            @Override
            public void onCancel() {
            }
        }).show();
    }

    private void cancelDownload(List<CloudDraftData> deleteData) {
        mPresenter.cancelDownload(deleteData);
        List<CloudDraftData> data = mAdapter.getData();
        for (int index = data.size() - 1; index >= 0; index--) {
            CloudDraftData draftData = data.get(index);
            if (draftData.isSelected()) {
                mAdapter.remove(index);
            }
        }
    }

    @Override
    public void refreshData(boolean isByUser) {
        if (isByUser) {
            mRefreshLayout.autoRefresh();
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (mDataObserver != null) {
            CloudDraftManager.getInstance().unRegisterDataObserver(mDataObserver);
        }
    }
}
