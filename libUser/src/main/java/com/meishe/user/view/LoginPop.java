package com.meishe.user.view;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.DialogInterface;
import android.telephony.PhoneNumberUtils;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.text.method.HideReturnsTransformationMethod;
import android.text.method.PasswordTransformationMethod;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.CompoundButton;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.Switch;
import android.widget.TextView;

import androidx.appcompat.app.AlertDialog;

import com.meishe.base.utils.KeyboardUtils;
import com.meishe.base.utils.LogUtils;
import com.meishe.base.utils.NetUtils;
import com.meishe.base.utils.ToastUtils;
import com.meishe.base.utils.Utils;
import com.meishe.libplugin.user.IUserPlugin;
import com.meishe.libplugin.user.UserConstant;
import com.meishe.third.pop.util.ViewUtil;
import com.meishe.user.R;
import com.meishe.user.UserUtil;
import com.meishe.user.encrypt.AES;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> Yangtailin
 * @CreateDate :2020/12/4 15:38
 * @Description :登录上传弹窗 Login upload pop
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class LoginPop implements View.OnClickListener {
    private EditText mEtAccount;
    private EditText mEtPassword;
    private TextView mTvUpload;
    private IUserPlugin.ILoginCallBack mLoginCallBack;
    private Switch mSwitch;
    private View mErrorHintView;
    private boolean mIsSubAccount = false;
    private ImageView imageAccountClear, imagePasswordHide;
    /**
     * 密码是否可见
     * Whether the password is visible
     */
    private boolean isPsdVisible = false;
    private AlertDialog mDialogProxy;
    private View mIconClose;
    private Context mContext;

    public LoginPop setLoginCallBack(IUserPlugin.ILoginCallBack callBack) {
        this.mLoginCallBack = callBack;
        return this;
    }

    public LoginPop(Context context, IUserPlugin.ILoginCallBack callBack) {
        mContext = context;
        if (mDialogProxy == null) {
            View view = createView();
            AlertDialog alertDialog = new AlertDialog.Builder(context).setView(view).setOnDismissListener(new DialogInterface.OnDismissListener() {
                @Override
                public void onDismiss(DialogInterface dialog) {
                    KeyboardUtils.hideSoftInput(view);
                }
            }).create();
            Window window = alertDialog.getWindow();
            window.setBackgroundDrawable(context.getResources().getDrawable(R.drawable.bg_empty));
            window.setGravity(Gravity.BOTTOM);
            mDialogProxy = alertDialog;
        }
        setLoginCallBack(callBack);
    }


    public Context getContext() {
        return mContext;
    }

    private View createView() {
        @SuppressLint("InflateParams") View view = LayoutInflater.from(getContext()).inflate(R.layout.dialog_login, null);
        mEtAccount = view.findViewById(R.id.et_account);
        mEtPassword = view.findViewById(R.id.et_password);
        mTvUpload = view.findViewById(R.id.tv_upload);
        mSwitch = view.findViewById(R.id.checkbox);
        mErrorHintView = view.findViewById(R.id.tv_error_hint);
        imageAccountClear = view.findViewById(R.id.image_account_clear);
        imagePasswordHide = view.findViewById(R.id.image_password_hide);
        mIconClose = view.findViewById(R.id.user_login_close);
        mSwitch.setSelected(true);
        final SwitchTrackTextDrawable unSelectDrawable = new SwitchTrackTextDrawable(getContext(),
                "", getContext().getResources().getString(R.string.user_account_sub));
        final SwitchTrackTextDrawable selectDrawable = new SwitchTrackTextDrawable(getContext(),
                getContext().getResources().getString(R.string.user_account_main), "");
        mSwitch.setTrackDrawable(unSelectDrawable);
        mSwitch.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                LogUtils.d("isChecked = " + isChecked);
                mIsSubAccount = isChecked;
                if (isChecked) {
                    mSwitch.setTrackDrawable(selectDrawable);
                } else {
                    mSwitch.setTrackDrawable(unSelectDrawable);
                }
            }
        });
        mTvUpload.setText(getContext().getResources().getString(R.string.user_login));
        initListener();
        View accountTv = view.findViewById(R.id.tv_account);
        View pwdTv = view.findViewById(R.id.tv_password);
        float dimension;
        if (Utils.isZh()) {
            dimension = getContext().getResources().getDimension(R.dimen.dp_px_150);
        } else {
            dimension = getContext().getResources().getDimension(R.dimen.dp_px_180);
        }
        ViewGroup.LayoutParams layoutParams = accountTv.getLayoutParams();
        layoutParams.width = (int) dimension;
        accountTv.setLayoutParams(layoutParams);
        layoutParams = pwdTv.getLayoutParams();
        layoutParams.width = (int) dimension;
        pwdTv.setLayoutParams(layoutParams);
        return view;
    }


    public void dismiss() {
        mDialogProxy.dismiss();
        if (mEtAccount != null) {
            mEtAccount.setText("");
        }
        if (mEtPassword != null) {
            mEtPassword.setText("");
        }
    }

    public void show() {
        mDialogProxy.show();
    }

    private void initListener() {
        mEtAccount.addTextChangedListener(mTextWatcher);
        mEtPassword.addTextChangedListener(mTextWatcher);
        mTvUpload.setOnClickListener(this);
        imageAccountClear.setOnClickListener(this);
        imagePasswordHide.setOnClickListener(this);
        mIconClose.setOnClickListener(this);
        ViewUtil.expandTouchArea(imageAccountClear, getContext().getResources().getDimensionPixelSize(R.dimen.dp_px_15));
        ViewUtil.expandTouchArea(imagePasswordHide, getContext().getResources().getDimensionPixelSize(R.dimen.dp_px_15));
    }

    private boolean netAvailable() {
        return NetUtils.isNetworkAvailable(getContext());
    }

    /**
     * 判断手机号是否符合规范
     * Whether the phone number conforms to the specification
     *
     * @param phone String 输入的手机号 The phone number
     */
    private boolean isPhoneNumber(String phone) {
        if (TextUtils.isEmpty(phone)) {
            return false;
        }
        if (phone.length() == 11) {
            for (int i = 0; i < 11; i++) {
                if (!PhoneNumberUtils.isISODigit(phone.charAt(i))) {
                    return false;
                }
            }
            return true;
        }
        return false;
    }

    /**
     * 判断邮箱是否合法
     *
     * @param email 邮箱 email
     */
    public boolean isEmail(String email) {
        if (null == email || "".equals(email)) return false;
        //Pattern p = Pattern.compile("\\w+@(\\w+.)+[a-z]{2,3}"); //简单匹配
        Pattern p = Pattern.compile("\\w+([-+.]\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*");//复杂匹配
        Matcher m = p.matcher(email);
        return m.matches();
    }


    private final TextWatcher mTextWatcher = new TextWatcher() {
        @Override
        public void beforeTextChanged(CharSequence s, int start, int count, int after) {

        }

        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {

        }

        @Override
        public void afterTextChanged(Editable s) {
            if (TextUtils.isEmpty(mEtAccount.getText())) {
                if (imageAccountClear.getVisibility() == View.VISIBLE) {
                    imageAccountClear.setVisibility(View.GONE);
                }
            } else {
                if (imageAccountClear.getVisibility() != View.VISIBLE) {
                    imageAccountClear.setVisibility(View.VISIBLE);
                }
            }
            if (TextUtils.isEmpty(mEtPassword.getText())) {
                if (imagePasswordHide.getVisibility() == View.VISIBLE) {
                    imagePasswordHide.setVisibility(View.GONE);
                }
            } else {
                if (imagePasswordHide.getVisibility() != View.VISIBLE) {
                    imagePasswordHide.setVisibility(View.VISIBLE);
                }
            }
            changeUploadView();
        }
    };

    /**
     * 改变上传view的状态
     * Change the status of the uploaded view
     */
    private void changeUploadView() {
        Editable account = mEtAccount.getText();
        Editable password = mEtPassword.getText();
        if (account != null && password != null && (!TextUtils.isEmpty(password.toString()))) {
            if (!mTvUpload.isEnabled()) {
                mTvUpload.setBackgroundResource(R.drawable.bg_round_clickable);
                mTvUpload.setEnabled(true);
            }
        } else {
            if (mTvUpload.isEnabled()) {
                mTvUpload.setBackgroundResource(R.drawable.bg_round_default);
                mTvUpload.setEnabled(false);
            }
        }
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.tv_upload) {
            if (!netAvailable()) {
                ToastUtils.showShort(getContext().getResources().getString(R.string.user_login_net_error));
                return;
            }
            mErrorHintView.setVisibility(View.INVISIBLE);
            String account = mEtAccount.getText().toString();
            String password = mEtPassword.getText().toString();
            if (TextUtils.isEmpty(account) || TextUtils.isEmpty(password)) {
                return;
            }
            final String pass = AES.getInstance().aesEncrypt(password);
            UserUtil.startLogin(account, pass, mIsSubAccount, "649c42dce714423fb42860e483316a50", new IUserPlugin.ILoginCallBack() {
                @Override
                public void onLoginSuccess(String token) {
                    if (mLoginCallBack != null) {
                        mLoginCallBack.onLoginSuccess(token);
                    }
                    dismiss();
                    ToastUtils.make()
                            .setGravity(Gravity.CENTER, -1, -1)
                            .setDurationIsLong(false)
                            .show(R.string.user_login_success);
                }

                @Override
                public void onLoginFailed(int code) {
                    if (mLoginCallBack != null) {
                        mLoginCallBack.onLoginFailed(code);
                    }
                    if (code == UserConstant.ResultCode.ACCOUNT_PW_ERROR || code == UserConstant.ResultCode.ACCOUNT_ERROR) {
                        mErrorHintView.setVisibility(View.VISIBLE);
                    }
                }
            });
        } else if (id == R.id.user_login_close) {
            dismiss();
        } else if (id == R.id.image_account_clear) {
            mEtAccount.setText("");
        } else if (id == R.id.image_password_hide) {
            if (!isPsdVisible) {
                //明文,设置密码可见
                //Text, visible when setting password
                imagePasswordHide.setImageResource(R.mipmap.icon_password_hide_selected);
                mEtPassword.setTransformationMethod(HideReturnsTransformationMethod.getInstance());
                isPsdVisible = true;
            } else {
                //密码，设置密码不可见
                //Password, set password to be invisible
                imagePasswordHide.setImageResource(R.mipmap.icon_password_hide_normal);
                mEtPassword.setTransformationMethod(PasswordTransformationMethod.getInstance());
                isPsdVisible = false;
            }
        }
    }
}
