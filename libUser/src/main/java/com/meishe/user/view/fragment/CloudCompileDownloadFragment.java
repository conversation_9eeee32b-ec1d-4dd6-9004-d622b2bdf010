package com.meishe.user.view.fragment;

import com.meishe.base.view.PullToRefreshAndPushToLoadView;
import com.meishe.engine.DownloadManager;
import com.meishe.user.R;
import com.meishe.user.bean.VideoCompileBean;
import com.meishe.user.manager.CloudCompileManager;
import com.meishe.user.view.iview.CloudDraftView;

import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/3/9 13:28
 * @Description :
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class CloudCompileDownloadFragment extends BaseCloudCompileFragment implements CloudDraftView {
    private int mCurrentClickedPosition;
    private CloudCompileManager.CompileUpDownloadDataObserver mCompileUploadDataObserver;
    private CloudCompileManager.CompileDownloadObserver mDownloadObserver;

    public static CloudCompileDownloadFragment create() {
        CloudCompileDownloadFragment cloudUploadFragment = new CloudCompileDownloadFragment();
        return cloudUploadFragment;
    }

    @Override
    public void initListener() {
        CloudCompileManager.getInstance().registerUpDownloadDataObserver(mCompileUploadDataObserver = new CloudCompileManager.CompileUpDownloadDataObserver(){
            @Override
            public void onDownloadDataChanged(List<VideoCompileBean> data) {
                onNewDataBack(data);
            }

            @Override
            public void onUploadDataChanged(List<VideoCompileBean> data) {
                onNewDataBack(data);
            }
        });
        CloudCompileManager.getInstance().registerDownloadObserver(mDownloadObserver = new CloudCompileManager.CompileDownloadObserver(){

            @Override
            public void onProgress(String tag, int progress){
                List<VideoCompileBean> data = mAdapter.getData();
                for (int index = 0; index < data.size(); index++) {
                    VideoCompileBean bean = data.get(index);
                    if (tag.equals(bean.getJobId())) {
                        bean.setDownloadProgress(progress);
                        mAdapter.notifyItemChanged(index, 2);
                    }
                }
            }

            @Override
            public void onSuccess(String tag, DownloadManager.DownloadParam<String> param){

            }

            @Override
            public void onFailed(String tag){

            }
        });
        mRefreshLayout.setOnRefreshAndLoadMoreListener(new PullToRefreshAndPushToLoadView.PullToRefreshAndPushToLoadMoreListener() {
            @Override
            public void onRefresh() {
                finishRefresh();
            }

            @Override
            public void onLoadMore() {
                finishLoading();
            }
        });

        mAdapter.setOnItemChildClickListener((adapter, view, position) -> {
            if (view.getId() == R.id.iv_delete) {
                mCurrentClickedPosition = position;
                VideoCompileBean item = mAdapter.getItem(position);
                mPresenter.deleteDownloadingJob(item.getCoverUrl());
                onDelete(null);
            }
        });
    }

    @Override
    public void onResume() {
        super.onResume();
        mPresenter.getDownloadingData();
    }

    @Override
    protected void onLazyLoad() {
        super.onLazyLoad();
    }

    @Override
    public void onDelete(String tag) {
        mAdapter.remove(mCurrentClickedPosition);
    }

    @Override
    public void onDownload() {

    }

    @Override
    public void refreshData(boolean isByUser) {
        if (isByUser) {
            mRefreshLayout.autoRefresh();
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        CloudCompileManager.getInstance().unregisterUpDownloadDataObserver(mCompileUploadDataObserver);
        CloudCompileManager.getInstance().unregisterDownloadObserver(mDownloadObserver);
    }

    @Override
    protected void initData() {
    }
}
