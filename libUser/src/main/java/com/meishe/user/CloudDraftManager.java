package com.meishe.user;

import android.text.TextUtils;
import android.view.Gravity;

import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.ThreadUtils;
import com.meishe.base.utils.ToastUtils;
import com.meishe.draft.DraftManager;
import com.meishe.draft.data.DraftData;
import com.meishe.draft.db.DraftDbManager;
import com.meishe.draft.db.LocalDraftDao;
import com.meishe.draft.db.LocalDraftEntity;
import com.meishe.draft.db.ProjectDao;
import com.meishe.draft.db.ProjectEntity;
import com.meishe.draft.db.ResourceDao;
import com.meishe.draft.db.ResourceEntity;
import com.meishe.engine.DownloadManager;
import com.meishe.engine.EngineNetApi;
import com.meishe.engine.bean.bridges.FileInfoBridge;
import com.meishe.engine.db.AssetDao;
import com.meishe.engine.db.AssetEntity;
import com.meishe.engine.db.DbManager;
import com.meishe.engine.observer.DownLoadObserver;
import com.meishe.libplugin.PluginManager;
import com.meishe.libplugin.user.IUserPlugin;
import com.meishe.net.custom.BaseResponse;
import com.meishe.net.custom.RequestCallback;
import com.meishe.user.bean.CloudDraftData;
import com.meishe.user.bean.CloudDraftListBean;
import com.meishe.user.bean.ResourceInfo;
import com.meishe.user.manager.CloudUploadManager;
import com.meishe.user.manager.ali.ALiHelper;
import com.meishe.user.manager.observer.CloudDataObservable;
import com.meishe.user.manager.observer.DataObserver;

import java.util.ArrayList;
import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/3/14 11:15
 * @Description :云草稿管理类 The manager of cloud draft.
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class CloudDraftManager {

    private static final CloudDraftManager INSTANCE = new CloudDraftManager();
    private final CloudDataObservable mObservable;
    private final DownloadManager mDownloadManager = new DownloadManager();
    private final CloudUploadManager mUploadManager = new CloudUploadManager();
    private final List<CloudDraftData> mDownloadData = new ArrayList<>();
    private final LocalDraftDao mLocalDraftDao;
    private final AssetDao mAssetDao;
    private final ResourceDao mResourceDao;
    private final ProjectDao mProjectDao;
    private List<DraftData> mUploadData = new ArrayList<>();

    /**
     * Clear data.
     */
    public void clearData() {
        mUploadData.clear();
        mDownloadData.clear();
    }

    /**
     * Register download observer.
     * 注册下载监听
     *
     * @param observer the observer
     */
    public void registerDownloadObserver(DownLoadObserver observer) {
        mDownloadManager.registerDownloadObserver(observer);
    }

    /**
     * Unregister download observer.
     * 解注册下载监听
     *
     * @param observer the observer
     */
    public void unRegisterDownloadObserver(DownLoadObserver observer) {
        mDownloadManager.unRegisterDownloadObserver(observer);
    }

    /**
     * Download file.
     * 下载文件
     *
     * @param downloadParam the download param 下载参数
     */
    public <T> void downloadFile(DownloadManager.DownloadParam<T> downloadParam, int maxProgress) {
        mDownloadManager.downloadFile(downloadParam, maxProgress);
    }

    /**
     * Download file.
     * 下载文件
     *
     * @param draftData     the draft data 草稿数据
     * @param downloadParam the download param 下载参数
     * @param observer      the observer 监听
     */
    public <T> void downloadFile(CloudDraftData draftData, DownloadManager.DownloadParam<T> downloadParam, int maxProgress, DownLoadObserver<T> observer) {
        //同一时间，不允许重复下载
        //At the same time, duplicate downloads are not allowed
        if (!CommonUtils.isEmpty(mDownloadData)) {
            for (CloudDraftData mUploadDatum : mDownloadData) {
                if (TextUtils.equals(mUploadDatum.getProjectId(), draftData.getProjectId())) {
                    return;
                }
            }
        }
        addDownloadData(draftData);
        mDownloadManager.downloadFile(downloadParam, maxProgress, observer);
    }

    /**
     * Sets max download progress.
     * 设置最大的下载显示进度
     *
     * @param maxProgress the max progress
     */
    public void setMaxDownloadProgress(int maxProgress) {
        mDownloadManager.setMaxProgress(maxProgress);
    }

    /**
     * Upload draft.
     * 上传草稿
     *
     * @param draftData the draft data 草稿数据
     * @param token     the token 用户token
     */
    public void uploadDraft(DraftData draftData, String token, boolean isUpdate) {
        //同一时间内，不允许重复上传
        //It is not allowed to upload repeatedly at the same time
        if (!isUpdate) {
           DraftManager.getInstance().copyDraft(draftData, new DraftManager.DraftChangedListener() {
               @Override
               public void onCopySuccess(DraftData draftData) {
                   uploadNow(draftData, token);
               }
            });
        } else {
            uploadNow(draftData, token);
        }
    }

    private void uploadNow(DraftData draftData, String token) {
        if (!CommonUtils.isEmpty(mUploadData)) {
            for (DraftData mUploadDatum : mUploadData) {
                if (TextUtils.equals(mUploadDatum.getProjectId(), draftData.getProjectId())) {
                    return;
                }
            }
        }
        mUploadManager.uploadDraft(draftData, token, new ALiHelper.UploadListener() {
            @Override
            public void onSuccess(DraftData data, String userName) {
                if (data == null) {
                    return;
                }
                removeUploadDraftData(data.getProjectId());
                //每次上传，将编辑时间更新为云端编辑时间
                //Each time you upload, update the editing time to the cloud editing time.
                updateLocalModifiedTime(data.getProjectId(), data.getLastModifyTimeLong());
            }

            @Override
            public void onFailed(String message, int code) {
                removeUploadDraftData(draftData.getProjectId());
                ToastUtils.make().setGravity(Gravity.CENTER, -1, -1).setDurationIsLong(false).show(R.string.upload_fail);
            }

            @Override
            public void onProgress(long progress, long total) {
                draftData.setProgress((int) progress);
                mObservable.notifyUploadProgressChanged(draftData.getProjectId(), (int) progress);
            }

            @Override
            public void onStart(DraftData data) {
                addUploadDraftData(data);
            }
        });
    }

    private void removeUploadDraftData(String projectId) {
        for (int index = mUploadData.size() - 1; index >= 0; index--) {
            if (TextUtils.equals(mUploadData.get(index).getProjectId(), projectId)) {
                mUploadData.remove(index);
                break;
            }
        }
        mObservable.notifyUploadDataChanged(mUploadData);
    }

    private void addUploadDraftData(DraftData draftData) {
        mUploadData.add(draftData);
        mObservable.notifyUploadDataChanged(mUploadData);
    }


    private CloudDraftManager() {
        mLocalDraftDao = DraftDbManager.get().getLocalDraftDao();
        mAssetDao = DbManager.get().getAssetDao();
        mResourceDao = DraftDbManager.get().getResourceDao();
        mProjectDao = DraftDbManager.get().getProjectDao();
        mObservable = new CloudDataObservable();
        registerDownloadObserver(new DownLoadObserver<Object>() {
            @Override
            public void onStateChanged(int count) {
            }

            @Override
            public void onProgress(String tag, int progress) {
            }

            @Override
            public void onSuccess(String tag, DownloadManager.DownloadParam<Object> param) {
                removeDownloadData(tag);
            }

            @Override
            public void onFailed(String tag) {
                super.onFailed(tag);
                removeDownloadData(tag);
            }
        });
    }

    /**
     * Gets instance.
     *
     * @return the instance
     */
    public static CloudDraftManager getInstance() {
        return INSTANCE;
    }


    public synchronized void updateLocalModifiedTime(String uuid, long modifiedAt) {
        LocalDraftEntity local = mLocalDraftDao.getDraft(uuid);
        if (local != null) {
            local.setModifiedAt(modifiedAt);
            local.setCloudModifiedAt(modifiedAt);
            local.setCreateAt(modifiedAt);
            local.setCloud(true);
            mLocalDraftDao.updateDraft(local);
            DraftManager.getInstance().notifyDataChanged(null);
        }
    }

    /**
     * Remove download data.
     * 删除下载数据
     *
     * @param uuid the uuid
     */
    public void removeDownloadData(String uuid) {
        for (int index = 0; index < mDownloadData.size(); index++) {
            CloudDraftData cloudDraftData = mDownloadData.get(index);
            if (TextUtils.equals(cloudDraftData.getUuid(), uuid)) {
                mDownloadData.remove(index);
                break;
            }
        }
        mObservable.notifyDownloadDataChanged(mDownloadData);
    }

    private void addDownloadData(CloudDraftData draftData) {
        if (draftData != null) {
            mDownloadData.add(draftData);
            mObservable.notifyDownloadDataChanged(mDownloadData);
        }
    }

    /**
     * Is downloading boolean.
     * 草稿是否正在下载
     *
     * @param draftData the draft data
     * @return the boolean
     */
    public boolean isDownloading(CloudDraftData draftData) {
        if (mDownloadData.isEmpty()) {
            return false;
        }
        for (CloudDraftData downloadDatum : mDownloadData) {
            if (TextUtils.equals(downloadDatum.getProjectId(), draftData.getProjectId())) {
                return true;
            }
        }
        return false;
    }

    /**
     * Gets cloud draft.
     * 获取云端草稿信息
     *
     * @param token    the token 用户token
     * @param page     the page 请求页数
     * @param pageSize the page size 每页请求数量
     * @param callBack the call back 回调
     */
    public void getCloudDraft(String token, int page, int pageSize, RequestCallBack callBack) {
        EngineNetApi.getCloudDraftList("", token, page, pageSize, new RequestCallback<CloudDraftListBean>() {
            @Override
            public void onSuccess(BaseResponse<CloudDraftListBean> response) {
                if (response != null && response.getCode() == 0) {
                    CloudDraftListBean data = response.getData();
                    if (data == null) {
                        if (callBack != null) {
                            callBack.onError();
                        }
                        return;
                    }
                    ThreadUtils.getIoPool().execute(() -> {
                        List<CloudDraftListBean.Project> projectList = data.getProjectList();
                        List<CloudDraftData> draftData = new ArrayList<>();
                        if (!CommonUtils.isEmpty(projectList)) {
                            for (CloudDraftListBean.Project project : projectList) {
                                CloudDraftData cloudDraftData = CloudDraftData.create(project);
                                String uuid = cloudDraftData.getUuid();
                                try {
                                    LocalDraftEntity localDraft = mLocalDraftDao.getDraft(uuid);
                                    if (localDraft != null) {
                                        //本地草稿存在。检查是否需要更新
                                        //Local draft exists. Check if updates are needed
                                        cloudDraftData.setHaveOld(localDraft.isCloud());
                                        if (Math.abs(localDraft.getCloudModifiedAt() / 1000L - Long.parseLong(project.getModifyAt())) < 2) {
                                            //不需要更新数据
                                            //No need to update data
                                            cloudDraftData.setNeedUpdate(false);
                                            cloudDraftData.setState(CloudDraftData.STATE_DOWNLOADED);
                                        } else {
                                            cloudDraftData.setNeedUpdate(true);
                                            cloudDraftData.setState(CloudDraftData.STATE_UPLOADED);
                                        }
                                    } else {
                                        //本地草稿没有
                                        //Local draft not available
                                        cloudDraftData.setHaveOld(false);
                                        cloudDraftData.setState(CloudDraftData.STATE_UPLOADED);
                                    }
                                } catch (Exception e) {
                                }
                                List<CloudDraftData> downloadData = getDownloadData();
                                if (!downloadData.isEmpty()) {
                                    for (CloudDraftData downloadDatum : downloadData) {
                                        if (TextUtils.equals(downloadDatum.getUuid(), cloudDraftData.getUuid())) {
                                            cloudDraftData.setState(CloudDraftData.STATE_DOWNLOADING);
                                            cloudDraftData.setProgress(downloadDatum.getProgress());
                                        }
                                    }
                                }
//                                List<DraftData> uploadData = getUploadData();
//                                if (!uploadData.isEmpty()) {
//                                    for (DraftData uploadDatum : uploadData) {
//                                        if (TextUtils.equals(uploadDatum.getProjectId(), cloudDraftData.getUuid())) {
//                                            cloudDraftData.setState(CloudDraftData.STATE_UPLOADING);
//                                            cloudDraftData.setProgress(uploadDatum.getProgress());
//                                        }
//                                    }
//                                }
                                draftData.add(cloudDraftData);
                            }

                        }
                        ThreadUtils.runOnUiThread(() -> {
                            if (callBack != null) {
                                callBack.onSuccess(draftData, data.getProjectCount());
                            }
                        });
                    });
                } else {
                    if (callBack != null) {
                        callBack.onError();
                    }
                }
            }

            @Override
            public void onError(BaseResponse<CloudDraftListBean> response) {
                if (callBack != null) {
                    callBack.onError();
                }
            }
        });
    }

    /**
     * Delete draft.
     * 删除草稿
     *
     * @param draftList the draft list 草稿列表
     * @param callBack  the call back 回调
     */
    public void deleteDraft(String[] draftList, CloudDeleteCallBack callBack) {
        IUserPlugin userPlugin = PluginManager.get().getUserPlugin();
        if (userPlugin == null) {
            if (callBack != null) {
                callBack.onError();
            }
            return;
        }
        EngineNetApi.deleteDraft(userPlugin.getToken(), draftList, new RequestCallback<CloudDraftListBean>() {
            @Override
            public void onSuccess(BaseResponse<CloudDraftListBean> response) {
                if (response != null && response.getCode() == 0) {
                    if (callBack != null) {
                        callBack.onSuccess();
                    }
                }

            }

            @Override
            public void onError(BaseResponse<CloudDraftListBean> response) {
                if (callBack != null) {
                    callBack.onError();
                }
            }
        });
    }

    /**
     * Insert assets.
     * 插入资源数据
     *
     * @param entity the entity
     */
    public void insertAssets(AssetEntity entity) {
        mAssetDao.insertAsset(entity);
    }

    /**
     * Update assets.
     * 更新资源数据
     *
     * @param entity the entity
     */
    public void updateAssets(AssetEntity entity) {
        mAssetDao.updateAsset(entity);
    }

    /**
     * Get assets asset entity.
     * 获取资源数据
     *
     * @param uuid the uuid
     * @return the asset entity
     */
    public AssetEntity getAssets(String uuid) {
        return mAssetDao.getAsset(uuid);
    }

    /**
     * Get project resource info.
     * 获取工程的资源信息
     *
     * @param projectId the project id 工程id
     * @param callback  the callback 回调
     */
    public void getProjectResourceInfo(String projectId, ResourceInfoCallback callback) {
        IUserPlugin userPlugin = PluginManager.get().getUserPlugin();
        if (userPlugin == null) {
            if (callback != null) {
                callback.onError();
            }
            return;
        }
        EngineNetApi.getProjectResourceInfo(userPlugin.getToken(), projectId, new RequestCallback<ResourceInfo>() {
            @Override
            public void onSuccess(BaseResponse<ResourceInfo> response) {
                if (response == null || response.getCode() != 0) {
                    if (callback != null) {
                        callback.onError();
                    }
                    return;
                }
                if (callback != null) {
                    callback.onSuccess(response.getData());
                }
            }

            @Override
            public void onError(BaseResponse<ResourceInfo> response) {
                if (callback != null) {
                    callback.onError();
                }
            }
        });
    }

    /**
     * Register data observer.
     * 注册数据监听
     *
     * @param observer the observer
     */
    public void registerDataObserver(DataObserver observer) {
        mObservable.registerObserver(observer);
    }

    /**
     * Unregister data observer.
     * 解注册数据监听
     *
     * @param observer the observer
     */
    public void unRegisterDataObserver(DataObserver observer) {
        mObservable.unregisterObserver(observer);
    }

    /**
     * Gets download data.
     * 获取下载数据
     *
     * @return the download data
     */
    public List<CloudDraftData> getDownloadData() {
        return mDownloadData;
    }

    /**
     * Gets upload data.
     * 获取上传数据
     *
     * @return the upload data
     */
    public List<DraftData> getUploadData() {
        return mUploadData;
    }

    /**
     * Cancel upload.
     * 取消上传
     *
     * @param uuidList the uuid list
     */
    public void cancelUpload(List<String> uuidList) {
        mUploadManager.cancelTask(uuidList);
        for (String uuid : uuidList) {
            removeUploadDraftData(uuid);
        }
    }

    /**
     * Cancel download.
     * 取消下载
     *
     * @param uuidList the uuid list
     */
    public void cancelDownload(List<String> uuidList) {
        mDownloadManager.cancelDownload(uuidList);
    }

    public void updateResourceData(String projectId, FileInfoBridge.FileInfo fileInfo){
        String resourceId = fileInfo.key;
        ResourceEntity resourceEntity = mResourceDao.get(resourceId);
        if (resourceEntity == null) {
            mResourceDao.insert(new ResourceEntity(fileInfo));
        } else {
            mResourceDao.update(resourceEntity.copy(fileInfo));
        }
        ProjectEntity project = mProjectDao.getProject(projectId, resourceId);
        if (project == null) {
            project = new ProjectEntity();
            project.setResourceId(resourceId);
            project.setProjectId(projectId);
            project.setProjectResourceId(projectId+resourceId);
            mProjectDao.insert(project);
        } else {
            project.setResourceId(resourceId);
            project.setProjectId(projectId);
            mProjectDao.update(project);
        }
    }

    /**
     * The interface Request call back.
     */
    public interface RequestCallBack {
        /**
         * On success.
         *
         * @param draftData    the draft data
         * @param projectCount the project count
         */
        void onSuccess(List<CloudDraftData> draftData, int projectCount);

        /**
         * On error.
         */
        void onError();
    }

    /**
     * The interface Request call back.
     */
    public interface CloudDeleteCallBack {
        /**
         * On success.
         */
        void onSuccess();

        /**
         * On error.
         */
        void onError();
    }

    public interface ResourceInfoCallback {
        /**
         * On success.
         *
         * @param resourceInfo the resource information
         */
        void onSuccess(ResourceInfo resourceInfo);

        /**
         * On error.
         */
        void onError();
    }
}
