package com.meishe.user.activity;

import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;

import com.meishe.base.model.BaseActivity;
import com.meishe.base.utils.ImageLoader;
import com.meishe.base.view.CustomCompileParamView;
import com.meishe.base.view.bean.CompileParamData;
import com.meishe.draft.DraftManager;
import com.meishe.draft.data.DraftData;
import com.meishe.libplugin.PluginManager;
import com.meishe.libplugin.user.IUserPlugin;
import com.meishe.user.R;
import com.meishe.user.manager.CloudCompileManager;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;


/**
 * 云导出视频页面
 * Compile Video Page
 */
public class CloudCompileActivity extends BaseActivity implements View.OnClickListener {
    private ImageView mIvBack;
    private ImageView mImageCover;
    private Button mTvCompile;
    private CustomCompileParamView mCustomResolution;
    private CustomCompileParamView mCustomFrameRate;
    private CloudCompileManager mCloudCompiler;
    private EditText mEditView;

    @Override
    protected int bindLayout() {
        return R.layout.activity_cloud_compile;
    }

    @Override
    protected void initData(Bundle savedInstanceState) {
        mCloudCompiler = CloudCompileManager.getInstance();
    }

    @Override
    protected void initView() {
        mIvBack = findViewById(R.id.iv_compile_back);
        mImageCover = findViewById(R.id.iv_cover);
        mTvCompile = findViewById(R.id.tv_compile);
        mCustomResolution = findViewById(R.id.custom_resolution);
        mCustomFrameRate = findViewById(R.id.custom_frame_rate);
        mEditView = findViewById(R.id.et_job_name);
        DraftData draftData = DraftManager.getInstance().getCurrentDraftData();
        if (draftData != null) {
            String fileName = draftData.getFileName();
            mCloudCompiler.setTitle(fileName);
            mEditView.setText(fileName);
        }
        initListener();
    }

    /**
     * Init listener.
     * 初始化监听
     */
    private void initListener() {
        mIvBack.setOnClickListener(this);
        mEditView.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                mCloudCompiler.setTitle(mEditView.getText().toString());
            }
        });
        mCustomResolution.setOnFunctionSelectedListener(new CustomCompileParamView.OnFunctionSelectedListener() {
            @Override
            public void onSelected(CompileParamData itemData) {
                mCloudCompiler.setSizeLevel(Integer.parseInt(itemData.getShowData().replace("p", "")));
            }

            @Override
            public void onTouched() {
            }

            @Override
            public void onRelease() {
            }
        });
        mCustomFrameRate.setOnFunctionSelectedListener(new CustomCompileParamView.OnFunctionSelectedListener() {
            @Override
            public void onSelected(CompileParamData itemData) {
                mCloudCompiler.setFps(Integer.parseInt(itemData.getShowData()));
            }

            @Override
            public void onTouched() {
            }

            @Override
            public void onRelease() {
            }

        });
        mTvCompile.setOnClickListener(this);
    }

    @Override
    protected void requestData() {
        DraftData draftData = DraftManager.getInstance().getCurrentDraftData();
        ImageLoader.loadUrl(this, draftData.getCoverPath(), mImageCover);
        mCustomResolution.post(new Runnable() {
            @Override
            public void run() {
                mCustomResolution.setSelectedData(getCompileParams(getResources().
                        getStringArray(R.array.custom_resolution), getResources().getString(R.string.int720)));
                mCustomFrameRate.setSelectedData(getCompileParams(getResources().
                        getStringArray(R.array.custom_frame_rate), getResources().getString(R.string.frame_rate_30)));
            }
        });
    }

    /**
     * 获取导出文件参数列表
     * Gets the compile params
     *
     * @param originalArray String[] The original data
     * @param recommend     true is recommend ,false not
     */
    public List<CompileParamData> getCompileParams(String[] originalArray, String recommend) {
        if (originalArray == null) {
            return null;
        }
        List<CompileParamData> resolutionList = new ArrayList<>(originalArray.length);
        if (originalArray.length > 0) {
            for (String s : originalArray) {
                CompileParamData compileParamData = new CompileParamData();
                compileParamData.setShowData(s);
                compileParamData.setRecommend(s.equals(recommend));
                resolutionList.add(compileParamData);
            }
        }
        return resolutionList;
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.iv_compile_back) {
            finish();
        } else if (id == R.id.tv_compile) {
            DraftData draftData = DraftManager.getInstance().getCurrentDraftData();
            if (mCloudCompiler.isCloud(draftData.getProjectId())) {
                IUserPlugin userPlugin = PluginManager.get().getUserPlugin();
                userPlugin.showCloudUploadConfirmPop(this, new IUserPlugin.OnEventListener() {
                    @Override
                    public void onConfirm(Object o) {
                        mCloudCompiler.compileDraft(draftData);
                        setResult(RESULT_OK);
                        finish();
                    }

                    @Override
                    public void onCancel() {
                        draftData.setProjectId(UUID.randomUUID().toString().toUpperCase());
                        mCloudCompiler.compileDraft(draftData);
                        setResult(RESULT_OK);
                        finish();
                    }
                });
            } else {
                mCloudCompiler.compileDraft(draftData);
                setResult(RESULT_OK);
                finish();
            }
        }
    }
}