package com.meishe.user;

import com.meishe.logic.manager.PreferencesManager;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2021/3/22 14:23
 * @Description :记录用户状态 State of user
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class UserState {

    private UserState() {
    }

    private static class SingletonHolder {
        private static UserState INSTANCE = new UserState();
    }

    public static UserState get() {
        return SingletonHolder.INSTANCE;
    }

    /**
     * 是否登录 Whether is login or not.
     */
    private boolean isLogin;

    /**
     * token
     */
    private String token;

    private String password;
    private boolean isSubAccount;

    private String account;

    public boolean isLogin() {
        return isLogin;
    }

    public void setLogin(boolean login) {
        PreferencesManager.get().putBoolean(UserDataKey.IS_LOGIN, login);
        isLogin = login;
    }

    public String getToken() {
        return token;
    }

    public String getTempToken() {
        return PreferencesManager.get().getString(UserDataKey.TOKEN);
    }

    public void setToken(String token) {
        PreferencesManager.get().putString(UserDataKey.TOKEN, token);
        this.token = token;
    }

    public boolean isSubAccount() {
        return isSubAccount;
    }

    public void setSubAccount(boolean subAccount) {
        isSubAccount = subAccount;
    }

    public void setUserInfo(String account, String passWord, boolean isSubAccount) {
        PreferencesManager.get().putString(UserDataKey.ACCOUNT, account);
        PreferencesManager.get().putString(UserDataKey.PASSWORD, passWord);
        PreferencesManager.get().putBoolean(UserDataKey.IS_SUB_ACCOUNT, isSubAccount);
        this.password = passWord;
        this.account = account;
        this.isSubAccount = isSubAccount;
    }

    public String getUserID() {
        String account = PreferencesManager.get().getString(UserDataKey.ACCOUNT);
        if (account != null) {
            boolean isSubCount = PreferencesManager.get().putBoolean(UserDataKey.IS_SUB_ACCOUNT, isSubAccount);
           return account + (isSubCount? 0 : 1);
        }
        return null;
    }

    public static class UserDataKey{
        public static final String IS_LOGIN = "isLogin";
        public static final String PASSWORD = "password";
        public static final String ACCOUNT = "account";
        public static final String IS_SUB_ACCOUNT = "isSubAccount";
        public static final String TOKEN = "token";
    }
}
