package com.meishe.user.encrypt;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;


/**
 * JAVA AES加密工具类，此类主要用于其它端数据传输加密
 * JAVA AES encryption tool class, mainly used for data transmission encryption on other ends。
 */
public class AES {

	private static AES mInstance;
	private SecretKey sSecretKey = null;
	private Cipher sCipher = null;
	private String sKeyString = "MKMlUH4D9FWABkPJ";

	public synchronized static AES getInstance() {
		if (mInstance == null) {
			mInstance = new AES();
		}

		return mInstance;
	}

	private AES() {
		try {
			sSecretKey = new SecretKeySpec(sKeyString.getBytes(), "AES");
			sCipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 加密数据
	 * 
	 * @param message
	 * @return
	 */
	public synchronized String aesEncrypt(String message) {
		String result = "";
		String newResult = "";
		try {
			sCipher.init(Cipher.ENCRYPT_MODE, sSecretKey);
			byte[] resultBytes = sCipher.doFinal(message.getBytes("UTF-8"));
			result = new String(Base64.encode(resultBytes, Base64.DEFAULT));
			newResult = filter(result);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return newResult;
	}

	/**
	 *解密数据
	 * 
	 * @param message
	 * @return
	 * @throws Exception
	 */
	public synchronized String aesDecrypt(String message) {
		String result = "";
		try {
			byte[] messageBytes = Base64.decode(message, Base64.DEFAULT);
			sCipher.init(Cipher.DECRYPT_MODE, sSecretKey);
			byte[] resultBytes = sCipher.doFinal(messageBytes);
			result = new String(resultBytes, "UTF-8");
		} catch (Exception e) {
			e.printStackTrace();
		}
		return result;
	}

	/**
	 * filter
	 * 过滤
	 *
	 * @param str
	 * @return
	 */
	public synchronized static String filter(String str) {
		String output = "";
		StringBuffer sb = new StringBuffer();
		for (int i = 0; i < str.length(); i++) {
			int asc = str.charAt(i);
			if (asc != 10 && asc != 13) {
				sb.append(str.subSequence(i, i + 1));
			}
		}
		output = new String(sb);
		return output;
	}

	
	public static void main(String[] args) {

		AES instance = AES.getInstance();
		String mawenqing = instance.aesEncrypt("mawenqing");
		System.out.println(mawenqing);
		String s = instance.aesDecrypt(mawenqing);
		System.out.println(s);


	}
}
