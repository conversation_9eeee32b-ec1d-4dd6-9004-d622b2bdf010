<?xml version="1.0" encoding="utf-8"?>
<!-- 底层下滑条的样式选择器，可控制Switch在不同状态下，底下下滑条的颜色 -->
<selector xmlns:android="http://schemas.android.com/apk/res/android" >
    <item android:state_pressed="true"
        android:drawable="@drawable/bg_round_a4a4a4_2_default" />
    <item android:state_selected="true"
        android:drawable="@drawable/bg_round_a4a4a4_2_default" />
    <item android:drawable="@drawable/bg_round_fc2b55_2_clickable" />
</selector>