<resources>

    <style name="MySwitch" parent="Theme.AppCompat.Light">
        <!-- Active thumb color & Active track color(30% transparency) switch 打开时的拇指按钮的颜色 轨迹颜色默认为30%这个颜色 -->
        <item name="colorControlActivated">@color/color_ffffffff</item>

        <!-- Inactive thumb color switch关闭时的拇指按钮的颜色 -->
        <item name="colorSwitchThumbNormal">@color/color_ffffffff</item>

        <!-- Inactive track color(30% transparency) switch关闭时的轨迹的颜色 30%这个颜色 -->
        <item name="android:colorForeground">@color/color_ffffffff</item>

        <!--使用方法： 在布局文件中给需要自定义控件中增加app:theme="@style/MySwitch"-->
    </style>

    <style name="MEditText" parent="Theme.AppCompat.Light">
      <!--  <item name="colorControlNormal">#707070</item>
        <item name="colorControlActivated">#707070</item>
        <item name="editTextBackground">@drawable/bg_edit_line</item>-->
    </style>
    <style name="subTabLayoutTextStyle">
        <item name="android:textSize">11sp</item>
    </style>

    <style name="cloudDraftTabLayoutTextStyle">
        <item name="android:textSize">@dimen/sp_px_30</item>
    </style>

    <style name="StyleProgressBar" parent="@android:style/Widget.ProgressBar">
        <item name="android:maxHeight">50dip</item>
        <item name="android:minHeight">10dip</item>
        <item name="android:indeterminateOnly">false</item>
        <item name="android:indeterminateBehavior">repeat</item>
    </style>

    <style name="DialogEditText" parent="Theme.AppCompat.Light">
        <item name="colorControlNormal">@color/white</item>
        <item name="colorControlActivated">@color/white</item>
    </style>

    <style name="draftTabLayoutTextStyle">
        <item name="android:textSize">@dimen/sp_px_42</item>
    </style>
    <style name="StyleProgressBarMini" parent="@android:style/Widget.ProgressBar.Horizontal">
        <item name="android:maxHeight">50dip</item>
        <item name="android:minHeight">10dip</item>
        <item name="android:indeterminateOnly">false</item>
        <item name="android:indeterminateDrawable">
            @android:drawable/progress_indeterminate_horizontal
        </item>
        <item name="android:progressDrawable">@drawable/shape_progressbar_mini</item>
    </style>
</resources>
