<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <ImageView
        android:contentDescription="@null"
        android:id="@+id/iv_cover"
        android:layout_width="@dimen/dp_px_315"
        android:layout_height="@dimen/dp_px_315"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        android:scaleType="centerCrop"
        app:layout_constraintRight_toRightOf="parent"/>
    <ImageView
        android:contentDescription="@null"
        android:id="@+id/iv_select"
        android:padding="@dimen/dp_px_6"
        android:layout_width="@dimen/dp_px_45"
        android:layout_height="@dimen/dp_px_45"
        android:layout_marginTop="@dimen/dp_px_9"
        android:layout_marginEnd="@dimen/dp_px_9"
        android:layout_marginRight="@dimen/dp_px_9"
        app:layout_constraintRight_toRightOf="parent"
        android:background="@drawable/bg_draft_annulus_white"
        app:layout_constraintTop_toTopOf="parent"/>
    <View
        android:id="@+id/iv_select_clicked"
        android:padding="@dimen/dp_px_15"
        android:layout_width="@dimen/dp_px_75"
        android:layout_height="@dimen/dp_px_75"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <com.meishe.user.view.CustomProgress
        android:id="@+id/progress_bar"
        android:layout_width="@dimen/dp_px_72"
        android:layout_height="@dimen/dp_px_72"
        app:layout_constraintTop_toTopOf="@+id/iv_cover"
        app:layout_constraintBottom_toBottomOf="@+id/iv_cover"
        app:layout_constraintLeft_toLeftOf="@+id/iv_cover"
        app:layout_constraintRight_toRightOf="@+id/iv_cover"
        android:max="100"
        android:progress="50"
        app:progressWidth="@dimen/dp_px_6"
        app:progressBackgroundColor="@color/white_5"
        app:progressColor="@color/color_ffff365e"
        android:visibility="gone"/>

    <ImageView
        android:contentDescription="@null"
        android:id="@+id/iv_cloud_icon"
        android:layout_width="@dimen/dp_px_90"
        android:layout_height="@dimen/dp_px_90"
        app:layout_constraintTop_toTopOf="@+id/iv_cover"
        app:layout_constraintBottom_toBottomOf="@+id/iv_cover"
        app:layout_constraintLeft_toLeftOf="@+id/iv_cover"
        app:layout_constraintRight_toRightOf="@+id/iv_cover"
        android:src="@mipmap/ic_draft_download" />
    <TextView
        android:id="@+id/tv_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="@dimen/sp_px_36"
        android:textColor="@color/white"
        app:layout_constraintTop_toBottomOf="@+id/iv_cover"
        app:layout_constraintLeft_toLeftOf="parent"
        android:layout_marginTop="@dimen/dp_px_27"/>
    <ImageView
        android:contentDescription="@null"
        android:layout_width="@dimen/dp_px_6"
        android:layout_height="@dimen/dp_px_33"
        android:src="@mipmap/ic_cloud_draft_more"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_name"
        app:layout_constraintBottom_toBottomOf="@+id/tv_name"/>

    <View
        android:id="@+id/more_click_view"
        android:layout_width="@dimen/dp_px_315"
        android:layout_height="@dimen/dp_px_80"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_name"
        app:layout_constraintBottom_toBottomOf="@+id/tv_name"/>
    <TextView
        android:visibility="gone"
        android:id="@+id/tv_size"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="@dimen/sp_px_33"
        android:textColor="@color/white_5"
        app:layout_constraintTop_toBottomOf="@+id/tv_name"
        android:layout_marginTop="@dimen/dp_px_16"
        app:layout_constraintLeft_toLeftOf="parent"/>

    <TextView
        android:visibility="gone"
        android:id="@+id/tv_line"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="@dimen/sp_px_33"
        android:textColor="@color/white_5"
        android:text=" | "
        app:layout_constraintTop_toTopOf="@+id/tv_size"
        app:layout_constraintTop_toBottomOf="@+id/tv_size"
        app:layout_constraintLeft_toRightOf="@+id/tv_size"/>
    <TextView
        android:visibility="gone"
        android:id="@+id/tv_duration"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="@dimen/sp_px_33"
        android:textColor="@color/white_5"
        app:layout_constraintTop_toTopOf="@+id/tv_size"
        app:layout_constraintTop_toBottomOf="@+id/tv_size"
        app:layout_constraintLeft_toRightOf="@+id/tv_line"/>

</androidx.constraintlayout.widget.ConstraintLayout>