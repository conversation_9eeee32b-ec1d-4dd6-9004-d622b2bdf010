<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:id="@+id/parent_layout"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.meishe.base.view.PullToRefreshAndPushToLoadView
        android:layout_marginLeft="@dimen/dp_px_23"
        android:layout_marginRight="@dimen/dp_px_23"
        android:id="@+id/ptl_recyclerView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginBottom="@dimen/dp_px_200"
        android:layout_gravity="center_horizontal">
        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:minHeight="@dimen/dp_px_250">
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_list"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />
            <TextView
                android:id="@+id/tv_hint"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:gravity="center"
                android:textColor="@color/color_ff808080"
                android:visibility="gone" />
        </FrameLayout>
    </com.meishe.base.view.PullToRefreshAndPushToLoadView>

</FrameLayout>