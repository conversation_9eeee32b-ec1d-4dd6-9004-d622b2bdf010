<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="513dp"
    android:orientation="vertical"
    android:background="@drawable/bg_round_corners_solid_white_ten">

    <ImageView
        android:layout_gravity="end"
        android:paddingRight="@dimen/dp_px_15"
        android:layout_marginEnd="@dimen/dp_px_27"
        android:layout_marginRight="@dimen/dp_px_27"
        android:layout_marginTop="@dimen/dp_px_42"
        android:id="@+id/iv_cancel"
        android:layout_width="@dimen/dp_px_48"
        android:layout_height="@dimen/dp_px_48"
        android:src="@mipmap/ic_assets_check_back"/>

    <TextView
        android:paddingLeft="@dimen/dp_px_87"
        android:paddingStart="@dimen/dp_px_87"
        android:paddingEnd="@dimen/dp_px_87"
        android:paddingRight="@dimen/dp_px_87"
        android:id="@+id/tv_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:layout_gravity="center"
        android:textColor="@color/color_ff252525"
        android:text="@string/user_dialog_assets_unavailable_title"
        android:textSize="@dimen/sp_px_48" />

    <TextView
        android:visibility="gone"
        android:paddingLeft="@dimen/dp_px_87"
        android:paddingStart="@dimen/dp_px_87"
        android:paddingEnd="@dimen/dp_px_87"
        android:paddingRight="@dimen/dp_px_87"
        android:layout_marginTop="@dimen/dp_px_15"
        android:id="@+id/tv_sub_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:layout_gravity="center"
        android:text="@string/user_dialog_assets_unavailable_subtitle"
        android:textSize="@dimen/sp_px_36" />

   <androidx.recyclerview.widget.RecyclerView
       android:layout_marginTop="@dimen/dp_px_60"
       android:paddingLeft="@dimen/dp_px_66"
       android:paddingStart="@dimen/dp_px_66"
       android:paddingEnd="@dimen/dp_px_66"
       android:paddingRight="@dimen/dp_px_66"
       android:id="@+id/rc_content"
       android:layout_width="match_parent"
       android:layout_height="@dimen/dp_px_0"
       android:layout_marginBottom="@dimen/dp_px_45"
       android:layout_weight="1">
   </androidx.recyclerview.widget.RecyclerView>

    <TextView
        android:id="@+id/tv_upload"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_114"
        android:layout_marginLeft="@dimen/dp_px_90"
        android:layout_marginRight="@dimen/dp_px_90"
        android:background="@drawable/user_selector_upload_button_bg"
        android:gravity="center"
        android:text="@string/user_dialog_assets_unavailable_submit"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_px_39" />

    <TextView
        android:id="@+id/tv_cancel"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="@string/cancel"
        android:textColor="@color/color_ff4b4b4b"
        android:layout_marginTop="@dimen/dp_px_30"
        android:layout_marginBottom="@dimen/dp_px_56"
        android:textSize="@dimen/sp_px_39" />
</LinearLayout>