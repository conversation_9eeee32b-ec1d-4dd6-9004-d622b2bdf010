<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/colorTranslucent"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/user_login_close"
        android:layout_width="@dimen/dp_px_72"
        android:layout_height="@dimen/dp_px_99"
        android:layout_gravity="top|end"
        android:layout_marginEnd="@dimen/dp_px_53"
        android:layout_marginRight="@dimen/dp_px_53"
        android:src="@mipmap/user_login_close" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/top_round_corners_solid_white"
        android:orientation="vertical">

        <Switch
            android:id="@+id/checkbox"
            android:layout_width="@dimen/dp_px_450"
            android:layout_height="@dimen/dp_px_66"
            android:layout_gravity="center"
            android:layout_marginTop="@dimen/dp_px_48"
            android:background="@drawable/track_login_main"
            android:gravity="center"
            android:showText="true"
            android:switchMinWidth="@dimen/dp_px_450"
            android:textOff="@string/user_account_main"
            android:textOn="@string/user_account_sub"
            android:textSize="@dimen/dp_px_36"
            android:theme="@style/MySwitch"
            android:thumb="@drawable/thumb_login_main"
            android:track="@drawable/track_login_main" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_marginTop="@dimen/dp_px_102"
            android:layout_height="wrap_content">

            <EditText
                android:layout_centerVertical="true"
                android:id="@+id/et_account"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_px_105"
                android:layout_marginStart="@dimen/dp_px_6"
                android:layout_marginLeft="@dimen/dp_px_6"
                android:layout_marginEnd="@dimen/dp_px_90"
                android:layout_marginRight="@dimen/dp_px_90"
                android:layout_toEndOf="@+id/tv_account"
                android:layout_toRightOf="@+id/tv_account"
                android:background="@drawable/bg_edit_bg_underline_selector"
                android:gravity="center_vertical"
                android:hint="@string/user_login_input_phone_email"
                android:importantForAutofill="no"
                android:singleLine="true"
                android:textColorHint="@color/color_ffa4a4a4"
                android:textSize="@dimen/sp_px_36"
                android:theme="@style/MEditText"
                android:inputType="text" />

            <ImageView
                android:contentDescription="@null"
                android:layout_centerVertical="true"
                android:id="@+id/image_account_clear"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_px_45"
                android:layout_alignParentRight="true"
                android:paddingRight="@dimen/dp_px_90"
                android:src="@mipmap/icon_account_clear_normal"
                android:visibility="gone" />

            <TextView
                android:layout_centerVertical="true"
                android:id="@+id/tv_account"
                android:layout_width="@dimen/dp_px_150"
                android:layout_height="@dimen/dp_px_100"
                android:layout_marginStart="@dimen/dp_px_90"
                android:layout_marginLeft="@dimen/dp_px_90"
                android:gravity="center_vertical"
                android:text="@string/dialog_account_number"
                android:textSize="@dimen/sp_px_39" />
        </RelativeLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_marginTop="@dimen/dp_px_24"
            android:layout_height="wrap_content">

            <EditText
                android:layout_centerVertical="true"
                android:id="@+id/et_password"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_px_105"
                android:layout_marginStart="@dimen/dp_px_6"
                android:layout_marginLeft="@dimen/dp_px_6"
                android:layout_marginEnd="@dimen/dp_px_90"
                android:layout_marginRight="@dimen/dp_px_90"
                android:layout_toEndOf="@+id/tv_password"
                android:layout_toRightOf="@+id/tv_password"
                android:background="@drawable/bg_edit_bg_underline_selector"
                android:gravity="center_vertical"
                android:hint="@string/dialog_hint_input_pwd"
                android:importantForAutofill="no"
                android:inputType="textPassword"
                android:textColorHint="@color/color_ffa4a4a4"
                android:textSize="@dimen/sp_px_36"
                android:theme="@style/MEditText" />

            <ImageView
                android:contentDescription="@null"
                android:layout_centerVertical="true"
                android:id="@+id/image_password_hide"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_px_33"
                android:layout_alignParentRight="true"
                android:paddingRight="@dimen/dp_px_90"
                android:src="@mipmap/icon_password_hide_normal"
                android:visibility="invisible"/>

            <TextView
                android:layout_centerVertical="true"
                android:id="@+id/tv_password"
                android:layout_width="@dimen/dp_px_150"
                android:layout_height="@dimen/dp_px_100"
                android:layout_marginStart="@dimen/dp_px_90"
                android:layout_marginLeft="@dimen/dp_px_90"
                android:gravity="center_vertical"
                android:text="@string/dialog_account_pwd"
                android:textSize="@dimen/sp_px_39" />
        </RelativeLayout>

        <TextView
            android:id="@+id/tv_error_hint"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/user_login_error_hint"
            android:textColor="@color/color_ffe14333"
            android:textSize="@dimen/sp_px_33"
            android:layout_marginTop="@dimen/dp_px_15"
            android:visibility="invisible" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/user_login_business_hint"
            android:layout_marginLeft="@dimen/dp_px_90"
            android:layout_marginRight="@dimen/dp_px_90"
            android:layout_marginBottom="@dimen/dp_px_15"
            android:layout_marginTop="@dimen/dp_px_15"
            android:layout_gravity="center"
            android:gravity="center"
            android:textColor="@color/black"
            android:visibility="visible"
            android:textSize="@dimen/sp_px_33"/>
        <TextView
            android:id="@+id/tv_upload"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_px_114"
            android:layout_marginLeft="@dimen/dp_px_90"
            android:layout_marginRight="@dimen/dp_px_90"
            android:layout_marginBottom="@dimen/dp_px_72"
            android:background="@drawable/bg_round_default"
            android:gravity="center"
            android:text="@string/user_login"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_px_39" />
    </LinearLayout>
</LinearLayout>