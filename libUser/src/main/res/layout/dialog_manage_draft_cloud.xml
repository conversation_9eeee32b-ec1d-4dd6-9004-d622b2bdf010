<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/black">

    <ImageView
        android:id="@+id/iv_delete"
        android:layout_width="@dimen/dp_px_45"
        android:layout_height="@dimen/dp_px_45"
        android:layout_marginStart="@dimen/dp_px_45"
        android:layout_marginLeft="@dimen/dp_px_45"
        android:layout_marginTop="@dimen/dp_px_49"
        android:contentDescription="@null"
        android:src="@mipmap/draft_cloud_manager_delete"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"/>

    <TextView
        android:id="@+id/tv_delete"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_px_45"
        android:layout_marginStart="@dimen/dp_px_45"
        android:layout_marginLeft="@dimen/dp_px_45"
        android:gravity="center_vertical"
        android:text="@string/draft_manager_delete"
        android:textColor="@color/white_8"
        app:layout_constraintTop_toTopOf="@+id/iv_delete"
        app:layout_constraintBottom_toBottomOf="@+id/iv_delete"
        app:layout_constraintLeft_toRightOf="@+id/iv_delete"
        app:layout_constraintStart_toEndOf="@+id/iv_delete"
        app:layout_constraintEnd_toEndOf="parent"
        android:textSize="@dimen/sp_px_36" />

    <ImageView
        android:id="@+id/iv_download"
        android:layout_width="@dimen/dp_px_45"
        android:layout_height="@dimen/dp_px_45"
        android:layout_marginStart="@dimen/dp_px_45"
        android:layout_marginLeft="@dimen/dp_px_45"
        android:layout_marginTop="@dimen/dp_px_78"
        android:contentDescription="@null"
        android:src="@mipmap/ic_draft_download"
        app:layout_constraintTop_toBottomOf="@+id/tv_delete"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginBottom="@dimen/dp_px_83"/>

    <TextView
        android:id="@+id/tv_download"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_px_45"
        android:layout_marginStart="@dimen/dp_px_45"
        android:layout_marginLeft="@dimen/dp_px_45"
        android:gravity="center_vertical"
        android:text="@string/draft_manager_download"
        android:textColor="@color/white_8"
        app:layout_constraintTop_toTopOf="@+id/iv_download"
        app:layout_constraintBottom_toBottomOf="@+id/iv_download"
        app:layout_constraintLeft_toRightOf="@+id/iv_download"
        app:layout_constraintStart_toEndOf="@+id/iv_download"
        app:layout_constraintEnd_toEndOf="parent"
        android:textSize="@dimen/sp_px_36" />

</androidx.constraintlayout.widget.ConstraintLayout>
