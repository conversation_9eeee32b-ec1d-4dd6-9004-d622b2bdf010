<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/fl_root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/video_fragment_play_bar_bg">

    <com.meicam.sdk.NvsLiveWindowExt
        android:id="@+id/liveWindow"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center" />

    <com.meishe.player.view.OperationBox
        android:id="@+id/draw_rect"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:visibility="gone" />

    <com.meishe.player.view.mask.MaskZoomView
        android:id="@+id/mask_zoom_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:visibility="gone" />
    
<FrameLayout
        android:id="@+id/fl_player_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:visibility="gone">

        <com.meishe.player.view.PipTransformView
            android:id="@+id/pip_transform_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <TextView
            android:id="@+id/tv_scale_tip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/dp_px_120"
            android:text="@string/tv_two_finger_scale"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_px_30" />
    </FrameLayout>

    <com.meishe.player.view.ColorPicker
        android:id="@+id/color_picker"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:visibility="gone"/>

</FrameLayout>
