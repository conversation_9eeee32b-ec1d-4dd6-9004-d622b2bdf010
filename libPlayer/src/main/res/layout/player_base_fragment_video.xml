<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:id="@+id/fragment_base_parent"
    android:gravity="center">
    <com.meicam.sdk.NvsLiveWindowExt
        android:id="@+id/fragment_base_live_window"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <ImageView
        android:id="@+id/fragment_base_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:background="@mipmap/fragment_base_video"
        android:contentDescription="@null"
        android:visibility="gone" />
</FrameLayout>