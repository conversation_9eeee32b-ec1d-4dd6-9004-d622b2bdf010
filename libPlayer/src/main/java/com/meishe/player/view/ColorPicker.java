package com.meishe.player.view;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.PointF;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffXfermode;
import android.graphics.RectF;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;

import androidx.annotation.Nullable;

import com.meishe.base.bean.FloatPoint;
import com.meishe.base.utils.LogUtils;
import com.meishe.player.R;

import java.util.List;

public class ColorPicker extends View {
    private Paint mPaint;
    private int mOuterCircleHalfSize;
    private int mInnerCircleHalfSize;
    private int mCenterRectHalfSize;
    private float mCenterX, mCenterY;
    private Bitmap mBitmap;
    private List<PointF> mEffectRectF;
    private int mPixel;
    private OnColorChangedListener mOnColorChangedListener;
    private Bitmap mMosaicBit;
    private PorterDuffXfermode mClearXfermode;
    private PorterDuffXfermode mSRCXfermode;
    private final RectF mLayerRectF = new RectF();
    private boolean mCanMove;
    private float mBitRectWidth, mBitRectHeight;


    public void setOnColorChangedListener(OnColorChangedListener listener) {
        this.mOnColorChangedListener = listener;
    }

    public ColorPicker(Context context) {
        super(context);
    }

    public ColorPicker(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public ColorPicker(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    private void init(Context context) {
        mPaint = new Paint();
        mPaint.setColor(Color.WHITE);
        mPaint.setStyle(Paint.Style.STROKE);
        mPaint.setAntiAlias(true);
        mPaint.setStrokeWidth(context.getResources().getDimensionPixelSize(R.dimen.dp_px_5));
        mOuterCircleHalfSize = context.getResources().getDimensionPixelSize(R.dimen.dp_px_135);
        mInnerCircleHalfSize = context.getResources().getDimensionPixelSize(R.dimen.dp_px_105);
        mCenterRectHalfSize = (int) (context.getResources().getDimensionPixelSize(R.dimen.dp_px_21) / 2F);
        mMosaicBit = scaleImgMax(BitmapFactory.decodeResource(getResources(), R.mipmap.mosaic), (int) (mOuterCircleHalfSize * 2F), (int) (mOuterCircleHalfSize * 2F));
        mClearXfermode = new PorterDuffXfermode(PorterDuff.Mode.CLEAR);
        mSRCXfermode = new PorterDuffXfermode(PorterDuff.Mode.SRC_IN);
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if (event.getAction() == MotionEvent.ACTION_MOVE) {
            if (mCanMove) {
                float deltaX = event.getX() - mCenterX;
                if (canTrans(deltaX, 0)) {
                    mCenterX = event.getX();
                } else {
                    deltaX = getMaxTransX(deltaX);
                    mCenterX += deltaX;
                }
                float deltaY = event.getY() - mCenterY;
                if (canTrans(0, deltaY)) {
                    mCenterY = event.getY();
                } else {
                    deltaY = getMaxTransY(deltaY);
                    mCenterY += deltaY;
                }
                getBitmapPixel();
                invalidate();
            }
        } else if (event.getAction() == MotionEvent.ACTION_DOWN) {
            mCanMove = isInRect(event.getX(), event.getY());
            return true;
        }
        return super.onTouchEvent(event);
    }

    private void getBitmapPixel() {
        int[] location = getLocationInBitmap(mCenterX, mCenterY);
        try {
            mPixel = mBitmap.getPixel(location[0], location[1]);
            if (mOnColorChangedListener != null) {
                mOnColorChangedListener.onColorChanged(Color.alpha(mPixel), Color.red(mPixel), Color.green(mPixel), Color.blue(mPixel), new FloatPoint(mCenterX, mCenterY));
            }
        } catch (Exception e) {
            LogUtils.e(e);
        }
    }

    private float getMaxTransY(float trans) {
        while (trans > 3) {
            if (!canTrans(0, trans)) {
                trans /= 2F;
            } else {
                return trans;
            }
        }
        return 0;
    }

    private float getMaxTransX(float trans) {
        while (trans > 3) {
            if (!canTrans(trans, 0)) {
                trans /= 2F;
            } else {
                return trans;
            }
        }
        return 0;
    }

    private int[] getLocationInBitmap(float centerX, float centerY) {
        int[] location = new int[2];
        if (mBitmap != null) {
            int[] locationInBitRect = getLocationInBitRect(centerX, centerY);
            location[0] = (int) (locationInBitRect[0] / mBitRectWidth * mBitmap.getWidth());
            location[1] = (int) (locationInBitRect[1] / mBitRectHeight * mBitmap.getHeight());
        }
        return location;
    }

    private int[] getLocationInBitRect(float x, float y) {
        if ((mBitRectHeight == 0 || mBitRectWidth == 0) && mBitmap != null) {
            int bitWidth = mBitmap.getWidth();
            int bitHeight = mBitmap.getHeight();
            float bitRatio = bitWidth * 1F / bitHeight;
            int viewWidth = getWidth();
            int viewHeight = getHeight();
            mBitRectWidth = 0;
            mBitRectHeight = 0;
            if (bitRatio > viewWidth * 1F / viewHeight) {
                mBitRectWidth = bitWidth;
                mBitRectHeight = mBitRectWidth / bitRatio;
            } else {
                mBitRectHeight = viewHeight;
                mBitRectWidth = mBitRectHeight * bitRatio;
            }
        }
        int[] location = new int[2];
        //视图上的坐标，减去视图原点的差值，即为bitmap范围内的坐标
        //The coordinates on the view minus the difference of the view origin are the coordinates within the bitmap range.
        location[0] = (int) (x - (getWidth() - mBitRectWidth) / 2F);
        location[1] = (int) (y - (getHeight() - mBitRectHeight) / 2F);
        return location;
    }

    private boolean canTrans(float deltaX, float deltaY) {
        if (mEffectRectF == null) {
            return true;
        }
        //判断中心点是否超出坐标范围外
        // Determine if the center point is outside the coordinate range。
        PointF point = new PointF();
        point.x = mCenterX + deltaX;
        point.y = mCenterY + deltaY;
        boolean inRect = isInRect(mEffectRectF.get(0), mEffectRectF.get(1), mEffectRectF.get(2), mEffectRectF.get(3), point);
        if (inRect) {
            if (point.x < 0 || point.x > getWidth() || point.y < 0 || point.y > getHeight()) {
                return false;
            }
        }
        return inRect;
    }

    /**
     * 判断点p是否在p1p2p3p4的矩形内
     * Determine whether point p is within the rectangle of p1p2p3p4
     * @param p1 the p1
     * @param p2 the p2
     * @param p3 the p3
     * @param p4 the p4
     * @param p the p
     * @return the boolean
     */
    private boolean isInRect(PointF p1, PointF p2, PointF p3, PointF p4, PointF p) {
        return getCross(p1, p2, p) * getCross(p3, p4, p) >= 0 && getCross(p2, p3, p) * getCross(p4, p1, p) >= 0;
    }

    /**
     * 计算 |p1 p2| X |p1 p|
     * Compute |p1 p2| X |p1 p|
     * @param p1 the p1
     * @param p2 the p2
     * @param p the p
     * @return the value
     */
    private float getCross(PointF p1, PointF p2, PointF p) {
        return (p2.x - p1.x) * (p.y - p1.y) - (p.x - p1.x) * (p2.y - p1.y);
    }

    private boolean isInRect(float x, float y) {
        float left = mCenterX - mOuterCircleHalfSize;
        float top = mCenterY - mOuterCircleHalfSize;
        float right = mCenterX + mOuterCircleHalfSize;
        float bottom = mCenterY + mOuterCircleHalfSize;
        return x >= left && x <= right && y >= top && y <= bottom;
    }

    @Override
    protected void onDraw(Canvas canvas) {
        mLayerRectF.left = 0;
        mLayerRectF.top = 0;
        mLayerRectF.right = getWidth();
        mLayerRectF.bottom = getHeight();
        int layerId = canvas.saveLayer(mLayerRectF, mPaint);

        if (mPixel == Integer.MIN_VALUE) {
            mPaint.setStyle(Paint.Style.FILL_AND_STROKE);
            canvas.drawCircle(mCenterX, mCenterY, mOuterCircleHalfSize, mPaint);
            mPaint.setXfermode(mSRCXfermode);
            canvas.drawBitmap(mMosaicBit, mCenterX - mOuterCircleHalfSize, mCenterY - mOuterCircleHalfSize, mPaint);
        } else {
            mPaint.setStyle(Paint.Style.FILL_AND_STROKE);
            mPaint.setColor(Color.argb(Color.alpha(mPixel), Color.red(mPixel), Color.green(mPixel), Color.blue(mPixel)));
            canvas.drawCircle(mCenterX, mCenterY, mOuterCircleHalfSize, mPaint);
        }
        mPaint.setStyle(Paint.Style.FILL_AND_STROKE);
        mPaint.setXfermode(mClearXfermode);
        canvas.drawCircle(mCenterX, mCenterY, mInnerCircleHalfSize, mPaint);
        mPaint.setXfermode(null);
        canvas.restoreToCount(layerId);

        mPaint.setStyle(Paint.Style.STROKE);
        mPaint.setColor(Color.WHITE);
        mPaint.setStyle(Paint.Style.STROKE);
        canvas.drawCircle(mCenterX, mCenterY, mInnerCircleHalfSize, mPaint);
        canvas.drawCircle(mCenterX, mCenterY, mOuterCircleHalfSize, mPaint);
        canvas.drawRect(mCenterX - mCenterRectHalfSize, mCenterY - mCenterRectHalfSize, mCenterX + mCenterRectHalfSize, mCenterY + mCenterRectHalfSize, mPaint);
        super.onDraw(canvas);
    }

    public void setBitmap(Bitmap bitmap) {
        mBitmap = bitmap;
        mBitRectHeight = 0;
        mBitRectWidth = 0;
    }

    public boolean hasBitmap() {
        return mBitmap != null;
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        release();
        if (mMosaicBit != null) {
            mMosaicBit.recycle();
            mMosaicBit = null;
        }
    }

    public void setEffectRectF(List<PointF> rectF) {
        this.mEffectRectF = rectF;
        mCenterX = (mEffectRectF.get(0).x + mEffectRectF.get(2).x) / 2F;
        mCenterY = (mEffectRectF.get(0).y + mEffectRectF.get(2).y) / 2F;
        mPixel = Integer.MIN_VALUE;
    }

    /**
     * Reset.
     * 重置
     */
    public void reset() {
        mCenterX = (mEffectRectF.get(0).x + mEffectRectF.get(2).x) / 2F;
        mCenterY = (mEffectRectF.get(0).y + mEffectRectF.get(2).y) / 2F;
        mPixel = Integer.MIN_VALUE;
        invalidate();
    }

    /**
     * 宽高比取最大值缩放图片.
     *
     * @param bitmap     加载的图片
     * @param widthSize  缩放之后的图片宽度,一般就是屏幕的宽度.
     * @param heightSize 缩放之后的图片高度,一般就是屏幕的高度.
     */
    private Bitmap scaleImgMax(Bitmap bitmap, int widthSize, int heightSize) {
        int bmpW = bitmap.getWidth();
        int bmpH = bitmap.getHeight();
        float scaleW = ((float) widthSize) / bmpW;
        float scaleH = ((float) heightSize) / bmpH;
        //取宽高最大比例来缩放图片Take the maximum ratio of width to height to scale the image.
        float max = Math.max(scaleW, scaleH);
        Matrix matrix = new Matrix();
        matrix.postScale(max, max);
        return Bitmap.createBitmap(bitmap, 0, 0, bmpW, bmpH, matrix, true);
    }

    public void release() {
        if (mBitmap != null) {
            mBitmap.recycle();
            mBitmap = null;
        }
    }

    public void setColorPosition(FloatPoint colorPosition) {
        if (colorPosition != null) {
            mCenterX = colorPosition.x;
            mCenterY = colorPosition.y;
            post(() -> {
                int[] location = getLocationInBitmap(mCenterX, mCenterY);
                try {
                    mPixel = mBitmap.getPixel(location[0], location[1]);
                } catch (Exception e) {
                    LogUtils.e(e);
                }
                invalidate();
            });
        } else {
            reset();
            invalidate();
            mPixel = Integer.MIN_VALUE;
        }
    }

    public void setColor(int color) {
        mPixel = color;
        invalidate();
    }

    public interface OnColorChangedListener {
        /**
         * On color changed.
         *
         * @param a the a
         * @param r the r
         * @param g the g
         * @param b the b
         */
        void onColorChanged(int a, int r, int g, int b, FloatPoint point);
    }
}
