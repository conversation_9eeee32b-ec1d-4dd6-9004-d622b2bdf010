package com.meishe.player.view;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.RelativeLayout;

import com.meicam.sdk.NvsMultiThumbnailSequenceView;
import com.meishe.base.utils.LogUtils;
import com.meishe.base.utils.ScreenUtils;
import com.meishe.engine.bean.MeicamVideoClip;
import com.meishe.engine.bean.MeicamVideoTrack;
import com.meishe.engine.view.MultiThumbnailSequenceView;
import com.meishe.player.R;

import java.util.ArrayList;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/12/16 14:06
 * @Description :时间线帧选择View The view for selecting frame of timeline
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class TimelineFrameSelectView extends RelativeLayout {
    private static final float COVER_MARGIN = 0.5f;
    private MultiThumbnailSequenceView mNvsMultiThumbnailSequenceView;
    private int mCoverMargin;
    private OnScrollListener mOnScrollListener;

    public TimelineFrameSelectView(Context context) {
        super(context);
        init(context);
    }

    public TimelineFrameSelectView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public TimelineFrameSelectView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    private void init(Context context) {
        mCoverMargin = (int) (ScreenUtils.getScreenWidth() * COVER_MARGIN);
        LayoutInflater inflater = LayoutInflater.from(context);
        View parentView = inflater.inflate(R.layout.timeline_frame_select_view, this);
        mNvsMultiThumbnailSequenceView = parentView.findViewById(R.id.tailor_view_sequence);
        mNvsMultiThumbnailSequenceView.setStartPadding(mCoverMargin);
        mNvsMultiThumbnailSequenceView.setEndPadding(mCoverMargin);
        mNvsMultiThumbnailSequenceView.setOnScrollChangeListenser(new MultiThumbnailSequenceView.OnScrollChangeListener() {
            @Override
            public void onScrollChanged(MultiThumbnailSequenceView view, int x, int oldx) {
                if (mOnScrollListener != null) {
                    mOnScrollListener.onScrollChanged(x, oldx);
                }
            }
        });
    }

    /**
     * Smooth scroll to.
     * 滑动到某个时间点
     *
     * @param duration the duration
     */
    public void smoothScrollTo(long duration){
        mNvsMultiThumbnailSequenceView.smoothScrollTo(durationToLength(duration, mNvsMultiThumbnailSequenceView.getPixelPerMicrosecond()), 0);
    }

    private void refreshVideoView(MeicamVideoTrack videoTrack) {
        if (videoTrack == null || videoTrack.getClipCount() == 0) {
            LogUtils.e("refreshVideoView is null!");
            return;
        }
        mNvsMultiThumbnailSequenceView.setThumbnailImageFillMode(NvsMultiThumbnailSequenceView.THUMBNAIL_IMAGE_FILLMODE_ASPECTCROP);
        if (mNvsMultiThumbnailSequenceView != null) {
            ArrayList<MultiThumbnailSequenceView.ThumbnailSequenceDesc> sequenceDescsArray = new ArrayList<>();

            for (int i = 0; i < videoTrack.getClipCount(); i++) {
                MeicamVideoClip videoClip = videoTrack.getVideoClip(i);
                MultiThumbnailSequenceView.ThumbnailSequenceDesc sequenceDescs = new MultiThumbnailSequenceView.ThumbnailSequenceDesc();
                sequenceDescs.mediaFilePath = videoClip.getFilePath();
                sequenceDescs.trimIn = videoClip.getTrimIn();
                sequenceDescs.trimOut = videoClip.getTrimOut();
                sequenceDescs.inPoint = videoClip.getInPoint();
                sequenceDescs.stillImageHint = false;
                sequenceDescs.onlyDecodeKeyFrame = true;
                sequenceDescs.outPoint = videoClip.getOutPoint();
                sequenceDescsArray.add(sequenceDescs);
            }
            mNvsMultiThumbnailSequenceView.setThumbnailSequenceDescArray(sequenceDescsArray);
        }
    }

    public void setTrackData(MeicamVideoTrack videoTrack){
        refreshVideoView(videoTrack);
    }

    public void setOnScrollListener(OnScrollListener onScrollListener) {
        mOnScrollListener = onScrollListener;
    }

    private double getPixelPerMicrosecond() {
        if (mNvsMultiThumbnailSequenceView == null) {
            LogUtils.e("getPixelPerMicrosecond: mNvsMultiThumbnailSequenceView is null!");
            return 0;
        }
        return mNvsMultiThumbnailSequenceView.getPixelPerMicrosecond();
    }


    /**
     * 获取滑动的在timeline上的时间
     * Get scroll time in timeline long.
     *
     * @param dx the dx
     * @return the long
     */
    public long getScrollTimeInTimeline(int dx){
        return lengthToDuration(dx, getPixelPerMicrosecond());
    }

    /**
     * 缩略图控件中，view中的长度转化为时间线时间
     * In thumbnail view, the view length convert to the timeline time
     *
     * @param dx                   the dx
     * @param mPixelPerMicrosecond 比例尺。the m pixel per microsecond
     * @return the long
     */
    private long lengthToDuration(int dx, double mPixelPerMicrosecond) {
        return (long) Math.floor(dx / mPixelPerMicrosecond + 0.5D);
    }

    /**
     * Duration to length int.
     * 持续时间到长度
     * @param duration the duration  持续时间
     * @param mPixelPerMicrosecond the pixelPerMicrosecond  持续时间
     * @return the int
     */
    public static int durationToLength(long duration, double mPixelPerMicrosecond) {
        return (int) Math.floor(duration * mPixelPerMicrosecond + 0.5D);
    }

    /**
     * The interface On scroll listener.
     * 滑动监听的接口
     */
    public interface OnScrollListener {

        /**
         * On scroll changed.
         * 滑动改变
         * @param dx    the dx
         * @param oldDx the old dx
         */
        void onScrollChanged(int dx, int oldDx);
    }
}
