package com.meishe.player.view;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.DashPathEffect;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.PointF;
import android.graphics.Rect;
import android.graphics.RectF;
import android.graphics.Region;
import android.os.Vibrator;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;

import androidx.annotation.Nullable;

import com.meishe.base.constants.Constants;
import com.meishe.player.R;
import com.meishe.player.common.utils.ImageConverter;

import java.util.ArrayList;
import java.util.List;

import static com.meishe.base.constants.Constants.HAND_CLICK_DURATION;
import static com.meishe.base.constants.Constants.HAND_MOVE_DISTANCE;


/**
 * Created by Administrator on 2018/6/20.
 */

public class DrawRect extends View {
    private OnTouchListener mListener;
    private onDrawRectClickListener mDrawRectClickListener;
    private onStickerMuteListenser mStickerMuteListenser;
    private PointF prePointF = new PointF(0, 0);
    private RectF alignRectF = new RectF();
    private RectF horizFlipRectF = new RectF();
    private RectF rotationRectF = new RectF();
    private RectF deleteRectF = new RectF();
    private RectF muteRectF = new RectF();
    private List<PointF> mListPointF = new ArrayList<>();
    private List<List<PointF>> mSubListPointF;
    private Path rectPath = new Path();
    private boolean canScalOrRotate = false;
    private boolean canHorizFlipClick = false;
    private boolean canMuteClick = false;
    private boolean isInnerDrawRect = false;
    private boolean canDel = false;
    private boolean canAlignClick = false;
    private int mIndex = 0;
    private int viewMode = 0;
    private int mStickerMuteIndex = 0;
    private boolean mHasAudio = false;
    private Bitmap mRotationBitmap = BitmapFactory.decodeResource(getResources(), R.mipmap.scale);
    private Bitmap[] mTextAlignBitmap = {BitmapFactory.decodeResource(getResources(), R.mipmap.left_align), BitmapFactory.decodeResource(getResources(), R.mipmap.center_align), BitmapFactory.decodeResource(getResources(), R.mipmap.right_align)};
    private Bitmap mDeleteBitmap = BitmapFactory.decodeResource(getResources(), R.mipmap.delete);
    private Bitmap mFlipHorizontalBitmap = BitmapFactory.decodeResource(getResources(), R.mipmap.horizontal_flip);
    private Bitmap[] mMuteBitmap = {BitmapFactory.decodeResource(getResources(), R.mipmap.non_silence), BitmapFactory.decodeResource(getResources(), R.mipmap.silence)};
    private long mPrevMillionSecond = 0;
    private double mClickMoveDistance = 0.0D;
    private Paint mRectPaint = new Paint();
    private Paint mSubRectPaint = new Paint();
    private boolean mMoveOutScreen = false;
    /**
     * 在方框内绘制的图片路径
     * Picture path drawn within the box.
     */
    private Bitmap waterMarkBitmap;
    private boolean mIsVisible = true;

    private int subCaptionIndex = -1;
    /**
     * 震动器
     * The vibrator
     */
    private Vibrator mVibrator;
    private float currMoveXDx;
    private float preMoveXDx;
    private float preMoveX;
    private float mTotalMoveX;
    boolean isTurnMoveX = true;

    private float currMoveYDx;
    private float preMoveYDx;
    private float preMoveY;
    private float mTotalMoveY;
    boolean isTurnMoveY = true;

    private float mTotalRotateDegree = 0;
    /**
     * 是否已经吸附
     * Has it been adsorbed
     */
    private boolean isAdsorbRotate = false;

    private boolean isTurnRotate = false;
    private final float mDegreeAdsorb = 90;
    private float lastMoveDegree = 0.0f;
    /**
     * 震动以后移动的距离
     * Distance moved after vibration
     */
    private float totleMoveAdsorb = 0;

    private static long lastClickTime;

    public DrawRect(Context context) {
        this(context, null);
    }

    public DrawRect(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initRectPaint();
        initSubRectPaint();
        mVibrator = (Vibrator) context.getSystemService(Context.VIBRATOR_SERVICE);
    }


    public void cleanUp() {
        bitmapRecycle(mRotationBitmap);
        mRotationBitmap = null;

        int alignImgCount = mTextAlignBitmap.length;
        for (int idx = 0; idx < alignImgCount; idx++) {
            bitmapRecycle(mTextAlignBitmap[idx]);
            mTextAlignBitmap[idx] = null;
        }

        bitmapRecycle(mDeleteBitmap);
        mDeleteBitmap = null;

        bitmapRecycle(mFlipHorizontalBitmap);
        mFlipHorizontalBitmap = null;

        int muteImgCount = mMuteBitmap.length;
        for (int idx = 0; idx < muteImgCount; idx++) {
            bitmapRecycle(mMuteBitmap[idx]);
            mMuteBitmap[idx] = null;
        }

        bitmapRecycle(waterMarkBitmap);
        waterMarkBitmap = null;
    }


    private void bitmapRecycle(Bitmap bitmap) {
        if (bitmap != null && !bitmap.isRecycled()) {
            bitmap.recycle();
        }
    }

    private void setRectPath(List<PointF> listPointF) {
        rectPath.reset();
        rectPath.moveTo(listPointF.get(0).x, listPointF.get(0).y);
        rectPath.lineTo(listPointF.get(1).x, listPointF.get(1).y);
        rectPath.lineTo(listPointF.get(2).x, listPointF.get(2).y);
        rectPath.lineTo(listPointF.get(3).x, listPointF.get(3).y);
        rectPath.close();
    }

    private void initRectPaint() {
        // 设置颜色 Set color
        mRectPaint.setColor(Color.parseColor("#4A90E2"));
        // 设置抗锯齿 Set antiAlias
        mRectPaint.setAntiAlias(true);
        // 设置线宽 Set stroke width
        mRectPaint.setStrokeWidth(8);
        // 设置非填充 Set style
        mRectPaint.setStyle(Paint.Style.STROKE);
    }

    private void initSubRectPaint() {
        int dashWidth = 4;
        int dashGap = 2;
        // 设置颜色 Set color
        mSubRectPaint.setColor(Color.parseColor("#9B9B9B"));
        // 设置抗锯齿 Set antiAlias
        mSubRectPaint.setAntiAlias(true);
        // 设置线宽 Set stroke width
        mSubRectPaint.setStrokeWidth(dashWidth);
        // 设置非填充 Set style
        mSubRectPaint.setStyle(Paint.Style.STROKE);
        //设置虚线效果 Set path effect
        mSubRectPaint.setPathEffect(new DashPathEffect(new float[]{dashWidth, dashGap}, 0));
    }

    private int getSubCaptionIndex(int x, int y) {
        if (mSubListPointF == null) {
            return -1;
        }
        int subCount = mSubListPointF.size();
        for (int idx = 0; idx < subCount; idx++) {
            if (insideOperationBox(mSubListPointF.get(idx), x, y)) {
                return idx;
            }
        }
        return -1;
    }

    public void setAlignIndex(int index) {
        mIndex = index;
        invalidate();
    }

    public void setStickerMuteIndex(int index) {
        mStickerMuteIndex = index;
        invalidate();
    }

    public void setMuteVisible(boolean hasAudio) {
        mHasAudio = hasAudio;
        invalidate();
    }

    public void setBoxPointList(List<PointF> list, int mode) {
        setBoxPointList(list, null, mode);
    }

    public void setBoxPointList(List<PointF> list, List<List<PointF>> subList, int mode) {
        setDrawRectVisible(true);
        setVisibility(VISIBLE);
        mSubListPointF = subList;
        mListPointF = list;
        viewMode = mode;
        invalidate();
    }

    public void setWaterMackType(int waterMackType) {
        this.waterMackType = waterMackType;
    }

    public List<PointF> getDrawRect() {
        return mListPointF;
    }


    public void setOnTouchListener(OnTouchListener listener) {
        mListener = listener;
    }

    public void setDrawRectClickListener(onDrawRectClickListener drawRectClickListener) {
        this.mDrawRectClickListener = drawRectClickListener;
    }

    public void setStickerMuteListenser(onStickerMuteListenser stickerMuteListenser) {
        this.mStickerMuteListenser = stickerMuteListenser;
    }

    @Override
    @SuppressLint("DrawAllocation")
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
//        Log.e(TAG, "onDraw: "+  !mIsVisible+" "+ mIsOutOfTime+" "+(mListPointF == null || mListPointF.size() != 4));
        if (!mIsVisible) {
            return;
        }
        if (mListPointF == null || mListPointF.size() != 4) {
            return;
        }
        setRectPath(mListPointF);
        canvas.drawPath(rectPath, mRectPaint);
        if (viewMode == Constants.EDIT_MODE_CAPTION) {
            //绘制字幕对其按钮 Draw subtitles and their buttons.
            canvas.drawBitmap(mTextAlignBitmap[mIndex], mListPointF.get(0).x - mTextAlignBitmap[mIndex].getHeight() / 2f, mListPointF.get(0).y - mTextAlignBitmap[mIndex].getWidth() / 2f, mRectPaint);
            alignRectF.set(mListPointF.get(0).x - mTextAlignBitmap[mIndex].getWidth() / 2f, mListPointF.get(0).y - mTextAlignBitmap[mIndex].getHeight() / 2f, mListPointF.get(0).x + mTextAlignBitmap[mIndex].getWidth() / 2f, mListPointF.get(0).y + mTextAlignBitmap[mIndex].getWidth() / 2f);
        } else if (viewMode == Constants.EDIT_MODE_STICKER) {
            //绘制水平翻转按钮 Draw Horizontal Flip Button
            canvas.drawBitmap(mFlipHorizontalBitmap, mListPointF.get(0).x - mFlipHorizontalBitmap.getHeight() / 2f, mListPointF.get(0).y - mFlipHorizontalBitmap.getWidth() / 2f, mRectPaint);
            horizFlipRectF.set(mListPointF.get(0).x - mFlipHorizontalBitmap.getWidth() / 2f, mListPointF.get(0).y - mFlipHorizontalBitmap.getHeight() / 2f, mListPointF.get(0).x + mFlipHorizontalBitmap.getWidth() / 2f, mListPointF.get(0).y + mFlipHorizontalBitmap.getHeight() / 2f);
            if (mHasAudio) {
                canvas.drawBitmap(mMuteBitmap[mStickerMuteIndex], mListPointF.get(1).x - mMuteBitmap[mStickerMuteIndex].getHeight() / 2f, mListPointF.get(1).y - mMuteBitmap[mStickerMuteIndex].getWidth() / 2f, mRectPaint);
                muteRectF.set(mListPointF.get(1).x - mMuteBitmap[mStickerMuteIndex].getWidth() / 2f, mListPointF.get(1).y - mMuteBitmap[mStickerMuteIndex].getHeight() / 2f, mListPointF.get(1).x + mMuteBitmap[mStickerMuteIndex].getWidth() / 2f, mListPointF.get(1).y + mMuteBitmap[mStickerMuteIndex].getHeight() / 2f);
            } else {
                muteRectF.set(0, 0, 0, 0);
            }
        } else if (viewMode == Constants.EDIT_MODE_WATERMARK) {
            if (waterMarkBitmap != null && (waterMackType == Constants.WATER)) {
                canvas.drawBitmap(waterMarkBitmap, new Rect(0, 0, waterMarkBitmap.getWidth(), waterMarkBitmap.getHeight()), new RectF(mListPointF.get(0).x, mListPointF.get(0).y, mListPointF.get(2).x, mListPointF.get(2).y), null);
            }
        } else if (viewMode == Constants.EDIT_MODE_COMPOUND_CAPTION) {
            if (mSubListPointF != null) {
                int subCount = mSubListPointF.size();
                for (int idx = 0; idx < subCount; idx++) {
                    List<PointF> listPointF = mSubListPointF.get(idx);
                    if (listPointF == null || listPointF.size() != 4) {
                        continue;
                    }
                    setRectPath(listPointF);
                    canvas.drawPath(rectPath, mSubRectPaint);
                }
            }
        }

        if (viewMode == Constants.EDIT_MODE_THEME_CAPTION) {
            //主题字幕，不绘制编辑按钮
            // Theme subtitles, do not draw edit buttons.
            return;
        }

        //绘制删除按钮 Draw Delete Button.
        canvas.drawBitmap(mDeleteBitmap, mListPointF.get(3).x - mDeleteBitmap.getWidth() / 2f, mListPointF.get(3).y - mDeleteBitmap.getHeight() / 2f, mRectPaint);
        deleteRectF.set(mListPointF.get(3).x - mDeleteBitmap.getWidth() / 2f, mListPointF.get(3).y - mDeleteBitmap.getHeight() / 2f, mListPointF.get(3).x + mDeleteBitmap.getWidth() / 2f, mListPointF.get(3).y + mDeleteBitmap.getHeight() / 2f);

        // 绘制旋转放缩按钮 Draw Rotate Zoom Button.
        canvas.drawBitmap(mRotationBitmap, mListPointF.get(2).x - mRotationBitmap.getHeight() / 2f, mListPointF.get(2).y - mRotationBitmap.getWidth() / 2f, mRectPaint);
        rotationRectF.set(mListPointF.get(2).x - mRotationBitmap.getWidth() / 2f, mListPointF.get(2).y - mRotationBitmap.getHeight() / 2f, mListPointF.get(2).x + mRotationBitmap.getWidth() / 2f, mListPointF.get(2).y + mRotationBitmap.getHeight() / 2f);
    }


    public boolean insideOperationBox(int x, int y) {
        // 判断手指是否在字幕框内 Determine if the finger is inside the subtitle box.
        return insideOperationBox(mListPointF, x, y);
    }

    public boolean insideOperationBox(List<PointF> pointFList, int x, int y) {
        if (pointFList == null || pointFList.size() != 4) {
            return false;
        }
        // 判断手指是否在编辑框内 Determine if the finger is in the edit box.
        RectF r = new RectF();
        Path path = new Path();
        path.moveTo(pointFList.get(0).x, pointFList.get(0).y);
        path.lineTo(pointFList.get(1).x, pointFList.get(1).y);
        path.lineTo(pointFList.get(2).x, pointFList.get(2).y);
        path.lineTo(pointFList.get(3).x, pointFList.get(3).y);
        path.close();
        path.computeBounds(r, true);
        Region region = new Region();
        region.setPath(path, new Region((int) r.left, (int) r.top, (int) r.right, (int) r.bottom));
        return region.contains(x, y);
    }

    @SuppressLint("ClickableViewAccessibility")
    @Override
    public boolean onTouchEvent(MotionEvent event) {
        float targetX = event.getX();
        float targetY = event.getY();
        currMoveXDx = targetX - preMoveX;
        currMoveYDx = targetY - preMoveY;
        float xDx = currMoveXDx;
        float yDx = currMoveYDx;
        if (mListPointF != null) {

            int action = event.getAction();
            if (action == MotionEvent.ACTION_DOWN) {
                mPrevMillionSecond = System.currentTimeMillis();
                canScalOrRotate = rotationRectF.contains(targetX, targetY);
                canDel = deleteRectF.contains(targetX, targetY);
                if (viewMode == Constants.EDIT_MODE_CAPTION) {
                    canAlignClick = alignRectF.contains(targetX, targetY);
                } else if (viewMode == Constants.EDIT_MODE_STICKER) {
                    canHorizFlipClick = horizFlipRectF.contains(targetX, targetY);
                    canMuteClick = muteRectF.contains(targetX, targetY);
                }

                if (mListener != null && !canDel && !canScalOrRotate && !canAlignClick && !canHorizFlipClick && !canMuteClick) {
                    mListener.onTouchDown(new PointF(targetX, targetY));
                }

                if (mListPointF != null && mListPointF.size() == 4) {
                    // 判断手指是否在字幕框内 Determine if the finger is inside the subtitle box.
                    isInnerDrawRect = insideOperationBox((int) targetX, (int) targetY);
                }
                if (isInnerDrawRect) {
                    subCaptionIndex = getSubCaptionIndex((int) targetX, (int) targetY);
                }
                prePointF.set(targetX, targetY);
                preMoveX = targetX;
                preMoveY = targetY;
            } else if (action == MotionEvent.ACTION_UP) {
                //作个缩放操作选中判断，优先执行缩放操作，再执行删除操作。因为操作框很小的时候，会出现操作混乱的现象（本身做缩放操作，却出现删除操作）
                // Perform a scaling operation, select and judge, prioritize the scaling operation,
                // and then perform the deletion operation.
                // Because when the operation box is very small, there may be confusion in the
                // operation (scaling operation itself, but deletion operation occurs)
                if (!canScalOrRotate && canDel && mListener != null) {
                    //删除时，不作其他操作 When deleting, do nothing else.
                    isInnerDrawRect = false;
                    mListener.onDel();
                }
                if (viewMode == Constants.EDIT_MODE_CAPTION) {
                    if (canAlignClick && mListener != null) {
                        isInnerDrawRect = false;
                        mListener.onAlignClick();
                    }
                } else if (viewMode == Constants.EDIT_MODE_STICKER) {
                    if (canHorizFlipClick && mListener != null) {
                        mListener.onHorizontalFlipClick();
                    }
                    if (canMuteClick && mStickerMuteListenser != null) {
                        mStickerMuteListenser.onStickerMute();
                    }
                }
                long moveTime_up = System.currentTimeMillis() - mPrevMillionSecond;
//                    Log.e(TAG, "onTouchEvent: "+  moveTime_up+"  "+mClickMoveDistance+"  "+viewMode+" "+isInnerDrawRect+" "+
//                            !(canScalOrRotate || canDel || canHorizFlipClick || canMuteClick) );
                if (mClickMoveDistance < HAND_MOVE_DISTANCE && moveTime_up <= HAND_CLICK_DURATION) {
                    if (viewMode == Constants.EDIT_MODE_CAPTION) {
                        if (isInnerDrawRect && (!(canScalOrRotate || canDel || canAlignClick))) {
                            if (mDrawRectClickListener != null) {
                                if (isFastDoubleClick()) {
                                    mDrawRectClickListener.onDrawRectDoubleClick(0);//点击操作框
                                } else {
                                    mDrawRectClickListener.onDrawRectClick(0);//点击操作框
                                }
                            }
                        } else if (!(canScalOrRotate || canDel || canAlignClick)) {
                            if (mListener != null) {
                                //点击操作框以外的区域 Click on the area outside the action box.
                                mListener.onBeyondDrawRectClick();
                            }
                        }
                    } else if (viewMode == Constants.EDIT_MODE_STICKER) {
                        if (!isInnerDrawRect && !(canScalOrRotate || canDel || canHorizFlipClick || canMuteClick)) {
                            if (mListener != null) {
                                //点击操作框以外的区域
                                // Click on the area outside the action box、
                                mListener.onBeyondDrawRectClick();
                            }
                        }
                    } else if (viewMode == Constants.EDIT_MODE_THEME_CAPTION) {
                        if (!isInnerDrawRect) {
                            if (mListener != null) {
                                //点击操作框以外的区域
                                // Click on the area outside the action box.
                                mListener.onBeyondDrawRectClick();
                            }
                        }
                    } else if (viewMode == Constants.EDIT_MODE_WATERMARK) {
                        if (!isInnerDrawRect) {
                            if (mListener != null) {
                                //点击操作框以外的区域
                                // Click on the area outside the action box.
                                mListener.onBeyondDrawRectClick();
                            }
                        }
                    } else if (viewMode == Constants.EDIT_MODE_COMPOUND_CAPTION) {
                        //组合字幕操作
                        //Combination subtitle operation
                        if (isInnerDrawRect && (!(canScalOrRotate || canDel))) {
                            if (mDrawRectClickListener != null) {
                                if (isFastDoubleClick()) {
                                    //点击操作框
                                    // Click on the action box/
                                    mDrawRectClickListener.onDrawRectDoubleClick(subCaptionIndex);
                                } else {
                                    //点击操作框
                                    // Click on the action box/
                                    mDrawRectClickListener.onDrawRectClick(subCaptionIndex);
                                }
                            }
                        } else if (!(canScalOrRotate || canDel || canAlignClick)) {
                            if (mListener != null) {
                                //点击操作框以外的区域
                                // Click on the area outside the action box.
                                mListener.onBeyondDrawRectClick();
                            }
                        }
                    }
                }
                canDel = false;
                canScalOrRotate = false;
                isInnerDrawRect = false;

                canAlignClick = false;
                canHorizFlipClick = false;
                canMuteClick = false;
                mClickMoveDistance = 0.0D;
                mListener.onTouchUp();
            } else if (action == MotionEvent.ACTION_MOVE) {
                mClickMoveDistance = Math.sqrt(Math.pow(targetX - prePointF.x, 2) + Math.pow(targetY - prePointF.y, 2));
                // 防止移出屏幕Prevent screen removal.
                if (targetX <= 100 || targetX >= getWidth() || targetY >= getHeight() || targetY <= 20) {
                    mMoveOutScreen = true;
                    return true;
                }
                if (mMoveOutScreen) {
                    mMoveOutScreen = false;
                    return true;
                }

                // 计算字幕框中心点 Calculate the center point of the subtitle box.
                PointF centerPointF = new PointF();
                if (mListPointF != null && mListPointF.size() == 4) {
                    centerPointF.x = (mListPointF.get(0).x + mListPointF.get(2).x) / 2f;
                    centerPointF.y = (mListPointF.get(0).y + mListPointF.get(2).y) / 2f;
                }

                if (mListener != null && canScalOrRotate) {
                    isInnerDrawRect = false;
                    // 计算手指在屏幕上滑动的距离比例
                    // Calculate the proportion of distance fingers slide on the screen.
                    double temp = Math.pow(prePointF.x - centerPointF.x, 2) + Math.pow(prePointF.y - centerPointF.y, 2);
                    double preLength = Math.sqrt(temp);
                    double temp2 = Math.pow(targetX - centerPointF.x, 2) + Math.pow(targetY - centerPointF.y, 2);
                    double length = Math.sqrt(temp2);
                    float offset = (float) (length / preLength);

                    //宽高缩放，即可以改变形状
                    // Zoom in width and height, that is, you can change the shape.
                    float xOffset = (targetX - centerPointF.x) / (prePointF.x - centerPointF.x);
                    float yOffset = (targetY - centerPointF.y) / (prePointF.y - centerPointF.y);
                    mListener.onScaleXAndY(xOffset, yOffset, new PointF(centerPointF.x, centerPointF.y));


                    // 计算手指滑动的角度
                    // Calculate the angle of finger sliding.
                    float currAngle = (float) Math.atan2(targetY - centerPointF.y, targetX - centerPointF.x);
                    float preAngle = (float) Math.atan2(prePointF.y - centerPointF.y, prePointF.x - centerPointF.x);

                    float radian = currAngle - preAngle;
                    // 弧度转换为角度Convert radians to angles.
                    float angle = (float) (radian * 180 / Math.PI);
                    if ((lastMoveDegree > 0 && angle < 0) || (lastMoveDegree < 0 && angle > 0)) {
                        isTurnRotate = true;
                    }
                    lastMoveDegree = angle;

                    //存在某种情况下旋转角度突然差值增大
                    // There is a situation where the sudden difference in rotation angle increases.
                    if (Math.abs(angle) > 300) {
                        return false;
                    }

                    mTotalRotateDegree = mTotalRotateDegree + angle;
                    if (getRotateAdsorbStatus()) {
                        isAdsorbRotate = true;
                        isTurnRotate = false;
                        mTotalRotateDegree -= angle;
                        if (Math.abs(mTotalRotateDegree) < mDegreeAdsorb / 2f) {
                            //靠近0度 Approaching 0 degrees.
                            if (angle < 0) {
                                angle = -Math.abs(mTotalRotateDegree);
                            } else {
                                angle = Math.abs(mTotalRotateDegree);
                            }
                        } else {
                            //靠近90度 Approaching 90 degrees.
                            if (angle < 0) {
                                angle = Math.abs(mTotalRotateDegree) - mDegreeAdsorb;
                            } else {
                                angle = mDegreeAdsorb - Math.abs(mTotalRotateDegree);
                            }
                        }
                        mTotalRotateDegree = 0;
                    }
                    if (isAdsorbRotate) {
                        totleMoveAdsorb += angle;
                    }

                    if (isAdsorbRotate && mTotalRotateDegree != 0 && Math.abs(mTotalRotateDegree) < Constants.ADSORB_DEGREE) {

                    } else {
                        if (mTotalRotateDegree != 0) {
                            isAdsorbRotate = false;
                        }

                        if (totleMoveAdsorb != 0) {
                            mListener.onScaleAndRotate(offset, new PointF(centerPointF.x, centerPointF.y), -totleMoveAdsorb);
                            totleMoveAdsorb = 0;
                        } else {
                            mListener.onScaleAndRotate(offset, new PointF(centerPointF.x, centerPointF.y), -angle);
                        }

                    }

                }

                if (mListener != null && isInnerDrawRect && mIsVisible) {
                    int viewCenterX = getWidth() / 2;
                    int viewCenterY = getHeight() / 2;
                    mTotalMoveX = mTotalMoveX + currMoveXDx;
                    //竖直方向移动 Vertical movement.
                    mTotalMoveY = mTotalMoveY + currMoveYDx;
                    if (Math.abs(currMoveXDx) > Math.abs(currMoveYDx)) {
                        if ((currMoveXDx > 0 && preMoveXDx < 0) || (currMoveXDx < 0 && preMoveXDx > 0)) {
                            isTurnMoveX = true;
                        }
                        if (Math.abs(viewCenterX - centerPointF.x) < Constants.RECT_NEARBY_DISTANCE) {
                            if (isTurnMoveX) {
                                isTurnMoveX = false;
                                if (currMoveXDx > 0) {
                                    xDx = Math.abs(centerPointF.x - viewCenterX);
                                } else {
                                    xDx = -Math.abs(centerPointF.x - viewCenterX);
                                }
                                mVibrator.vibrate(50);
                                mTotalMoveY = 0;
                                mTotalMoveX = 0;
                            }
                        }
                        if (!isTurnMoveX && mTotalMoveX != 0 && Math.abs(mTotalMoveX) < Constants.RECT_NEARBY_DISTANCE) {
                        } else {
                            mListener.onDrag(prePointF, new PointF(prePointF.x + xDx, prePointF.y + yDx));
                        }
                    } else {
                        if ((currMoveYDx > 0 && preMoveYDx < 0) || (currMoveYDx < 0 && preMoveYDx > 0)) {
                            isTurnMoveY = true;
                        }
                        if (Math.abs(viewCenterY - centerPointF.y) < Constants.RECT_NEARBY_DISTANCE) {
                            if (isTurnMoveY) {
                                isTurnMoveY = false;
                                if (currMoveYDx > 0) {
                                    yDx = Math.abs(centerPointF.y - viewCenterY);
                                } else {
                                    yDx = -Math.abs(centerPointF.y - viewCenterY);
                                }
                                mVibrator.vibrate(50);
                                mTotalMoveY = 0;
                                mTotalMoveX = 0;
                            }
                        }
                        if (!isTurnMoveY && mTotalMoveY != 0 && Math.abs(mTotalMoveY) < Constants.RECT_NEARBY_DISTANCE) {
                        } else {
                            mListener.onDrag(prePointF, new PointF(prePointF.x + xDx, prePointF.y + yDx));
                        }
                    }
                }
                preMoveYDx = currMoveYDx;
                preMoveXDx = currMoveXDx;
                preMoveX = event.getX();
                preMoveY = event.getY();
                prePointF.set(prePointF.x + xDx, prePointF.y + yDx);
            }
        }

        return true;
    }

    /**
     * 获取吸附状态
     *
     * @return 吸附状态
     */
    private boolean getRotateAdsorbStatus() {
        if (mDegreeAdsorb - Math.abs(mTotalRotateDegree) <= Constants.ADSORB_DEGREE) {
            //震动一下 Shake it up
            mVibrator.vibrate(50);
            return true;
        }
        if ((!isAdsorbRotate) && isTurnRotate && Math.abs(mTotalRotateDegree) < Constants.ADSORB_DEGREE) {
            //震动一下 Shake it up
            mVibrator.vibrate(50);
            return true;
        }
        return false;
    }


    public static boolean isFastDoubleClick() {
        long time = System.currentTimeMillis();
        long timeD = time - lastClickTime;
        if (0 < timeD && timeD < 800) {
            return true;
        }
        lastClickTime = time;
        return false;
    }

    public void setDrawRectVisible(boolean show) {
        if (mIsVisible == show) {
            return;
        }
        mIsVisible = show;
        invalidate();
    }

    public boolean isVisible() {
        return mIsVisible;
    }

    /**
     * Sets total rotate degree.
     * 设置滑动的角度
     *
     * @param mTotalRotateDegree the m total rotate degree
     */
    public void setTotalRotateDegree(float mTotalRotateDegree) {
        this.mTotalRotateDegree = mTotalRotateDegree;
    }

    public void resetTotalRotateDegree() {
        totleMoveAdsorb = 0;
        isAdsorbRotate = false;
        lastMoveDegree = 0;
        isTurnRotate = false;
        currMoveXDx = 0.0f;
        preMoveXDx = 0.0f;
        preMoveX = 0.0f;
        mTotalMoveX = 0.0f;
        isTurnMoveX = true;

        currMoveYDx = 0.0f;
        preMoveYDx = 0.0f;
        preMoveY = 0.0f;
        mTotalMoveY = 0.0f;
        isTurnMoveY = true;
    }

    public interface OnTouchListener {
        void onDrag(PointF prePointF, PointF nowPointF);

        void onScaleAndRotate(float scaleFactor, PointF anchor, float rotation);

        void onScaleXAndY(float xScaleFactor, float yScaleFactor, PointF anchor);

        void onDel();

        void onTouchDown(PointF curPoint);

        void onAlignClick();

        void onHorizontalFlipClick();

        /**
         * On beyond draw rect click.
         * 超出字幕或者贴纸框部分点击回调
         */
        void onBeyondDrawRectClick();//超出字幕或者贴纸框部分点击回调

        /**
         * On touch up.Leave the touch event to save data after movement.
         * 离开触摸事件，为了移动后保存数据
         */
        void onTouchUp();
    }

    public interface onDrawRectClickListener {
        /**
         * On draw rect click.Just for modifying subtitles
         * 矩形框点击,只是用于修改字幕
         * @param captionIndex the caption index
         */
        void onDrawRectClick(int captionIndex);

        /**
         * On draw rect double click.Just for modifying subtitles
         *
         * @param captionIndex the caption index
         */
        void onDrawRectDoubleClick(int captionIndex);
    }

    public interface onStickerMuteListenser {
        /**
         * On sticker mute.
         * 贴纸静音回调
         */
        void onStickerMute();
    }


    private int waterMackType = -1;

    public void setWaterMarkBitmap(String path, int width) {
        waterMarkBitmap = ImageConverter.convertImageScaleByWidth(getContext(), path, width);
    }

    public int getViewMode() {
        return viewMode;
    }

    public void setViewMode(int viewMode) {
        this.viewMode = viewMode;
    }
}
