package com.meishe.player.view.mask;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.Point;
import android.graphics.PointF;
import android.graphics.Rect;
import android.graphics.RectF;
import android.graphics.Region;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;

import androidx.annotation.Nullable;

import com.meicam.sdk.NvsLiveWindowExt;
import com.meicam.sdk.NvsVideoResolution;
import com.meishe.base.utils.LogUtils;
import com.meishe.base.utils.SizeUtils;
import com.meishe.engine.bean.MeicamVideoClip;
import com.meishe.engine.bean.NvMaskModel;
import com.meishe.engine.bean.Transform;
import com.meishe.player.R;

/**
 * All rights Reserved, Designed By www.meishesdk.com
 *
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2021/7/21 10:18
 * @Description:
 * @Copyright: www.meishesdk.com Inc. All rights reserved.
 */
public class MaskZoomView extends View {
    private Paint mLinePaint;
    private final static int ONE_FINGER = 1;
    private final static int TWO_FINGER = 2;
    private boolean mIsTwoFingerEvent = false;
    private float targetX, targetY;

    public MaskZoomView(Context context) {
        this(context, null);
    }

    public MaskZoomView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public MaskZoomView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initPaint();
    }

    private void initPaint() {
        mLinePaint = new Paint();
        mLinePaint.setColor(getResources().getColor(R.color.mask_line));
        mLinePaint.setStrokeWidth(SizeUtils.dp2px(1f));
        // 设置抗锯齿 Set anti-aliasing
        mLinePaint.setAntiAlias(true);
        // 设置非填充 Set Non-padding
        mLinePaint.setStyle(Paint.Style.STROKE);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        if (isHidden || mPath == null || maskModel == null) {
            return;
        }
        canvas.drawPath(mPath, mLinePaint);
        int type = maskModel.maskType;
        Bitmap bitmap;
        if (type != MaskType.TEXT) {
            bitmap = getFeatherQuadrantBitmap();
            if (bitmap != null) {
                canvas.drawBitmap(bitmap, featherQuadrant.left, featherQuadrant.top, mLinePaint);
            }
        }
        if (type == MaskType.RECT || type == MaskType.CIRCLE) {
            bitmap = getHorizontalScaleQuadrantBitmap();
            if (bitmap != null) {
                canvas.drawBitmap(bitmap, horizontalScaleQuadrant.left, horizontalScaleQuadrant.top, mLinePaint);
            }
            bitmap = getVerticalScaleQuadrantBitmap();
            if (bitmap != null) {
                canvas.drawBitmap(bitmap, verticalScaleQuadrant.left, verticalScaleQuadrant.top, mLinePaint);
            }
        }
        if (type == MaskType.RECT) {
            bitmap = getCornerRadiusQuadrantQuadrantBitmap();
            if (bitmap != null) {
                canvas.drawBitmap(bitmap, cornerRadiusQuadrant.left, cornerRadiusQuadrant.top, mLinePaint);
            }
        }
    }

    /**
     * 获取羽化的图片
     * Get the first quadrant icon
     */
    private Bitmap getFeatherQuadrantBitmap() {
        Bitmap bitmap = BitmapFactory.decodeResource(getContext().getResources(), R.mipmap.icon_mask_feather);
        featherQuadrant.setEmpty();
        if (bitmap != null) {
            featherQuadrant.set(pathCenter.x - btSize * 0.5f, pathCenter.y + size.y * 0.5f + featherDistance * (maskModel.feather / maxFeather) + btInterval,
                    pathCenter.x + btSize * 0.5f, pathCenter.y + size.y * 0.5f + btSize + featherDistance * (maskModel.feather / maxFeather) + btInterval);
            float rotation = maskModel.transform.rotation - propertyTransform.rotation;
            NvMaskHelper.transformRect(featherQuadrant, pathCenter, rotation);
            bitmap = NvMaskHelper.transformBitmap(bitmap, rotation);
        }
        return bitmap;
    }

    /**
     * 获取水平缩放的图片
     * Get the first quadrant icon
     */
    private Bitmap getHorizontalScaleQuadrantBitmap() {
        Bitmap bitmap = BitmapFactory.decodeResource(getContext().getResources(), R.mipmap.icon_mask_width);
        horizontalScaleQuadrant.setEmpty();
        if (bitmap != null) {
            horizontalScaleQuadrant.set(pathCenter.x + size.x * 0.5f + btInterval, pathCenter.y - btSize * 0.5f,
                    pathCenter.x + size.x * 0.5f + btSize + btInterval, pathCenter.y + btSize * 0.5f);
            float rotation = maskModel.transform.rotation - propertyTransform.rotation;
            NvMaskHelper.transformRect(horizontalScaleQuadrant, pathCenter, rotation);
            bitmap = NvMaskHelper.transformBitmap(bitmap, rotation);
        }
        return bitmap;
    }

    /**
     * 获取竖直缩放的图片
     * Get the first quadrant icon
     */
    private Bitmap getVerticalScaleQuadrantBitmap() {
        Bitmap bitmap = BitmapFactory.decodeResource(getContext().getResources(), R.mipmap.icon_mask_height);
        verticalScaleQuadrant.setEmpty();
        if (bitmap != null) {
            verticalScaleQuadrant.set(pathCenter.x - btSize * 0.5f, pathCenter.y - size.y * 0.5f - btSize - btInterval,
                    pathCenter.x + btSize * 0.5f, pathCenter.y - size.y * 0.5f - btInterval);
            float rotation = maskModel.transform.rotation - propertyTransform.rotation;
            NvMaskHelper.transformRect(verticalScaleQuadrant, pathCenter, rotation);
            bitmap = NvMaskHelper.transformBitmap(bitmap, rotation);
        }
        return bitmap;
    }

    /**
     * 获取圆角的图片
     * Get the first quadrant icon
     */
    private Bitmap getCornerRadiusQuadrantQuadrantBitmap() {
        Bitmap bitmap = BitmapFactory.decodeResource(getContext().getResources(), R.mipmap.icon_mask_round_corner);
        cornerRadiusQuadrant.setEmpty();
        if (bitmap != null) {
            float maxCorner = size.y * 0.5f;
            float cornerRadius = maskModel.cornerRadiusRate * size.y * 0.5f;
            float length = cornerRadius / maxCorner * featherDistance;
            length = (float) Math.sqrt(length * length / 2.0f);
            cornerRadiusQuadrant.set(pathCenter.x - size.x * 0.5f - btInterval * 0.3f - btSize - length, pathCenter.y - size.y * 0.5f - btInterval * 0.3f - btSize - length,
                    pathCenter.x - size.x * 0.5f - btInterval * 0.3f - length, pathCenter.y - size.y * 0.5f - btInterval * 0.3f - length);
            float rotation = maskModel.transform.rotation - propertyTransform.rotation;
            NvMaskHelper.transformRect(cornerRadiusQuadrant, pathCenter, rotation);
            bitmap = NvMaskHelper.transformBitmap(bitmap, rotation);
        }
        return bitmap;
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        int pointerCount = event.getPointerCount();
        if (pointerCount > TWO_FINGER) {
            return false;
        }
        if (videoClip == null || maskModel == null) {
            return false;
        }
        int action = event.getAction();
        if (((action & MotionEvent.ACTION_MASK) == MotionEvent.ACTION_DOWN) && (pointerCount == ONE_FINGER)) {
            mIsTwoFingerEvent = false;
        }
        if (pointerCount == TWO_FINGER) {
            if ((action & MotionEvent.ACTION_MASK) == MotionEvent.ACTION_POINTER_DOWN) {
                if (maskOperateListener != null) {
                    maskOperateListener.onOperateStart(videoClip);
                }
            } else if ((action & MotionEvent.ACTION_MASK) == MotionEvent.ACTION_POINTER_UP){
                if (maskOperateListener != null) {
                    maskOperateListener.onOperateEnd();
                }
            }
            mIsTwoFingerEvent = true;
            twoFingerTouch(event);
        } else {
            if (action == MotionEvent.ACTION_DOWN) {
                if (maskOperateListener != null) {
                    maskOperateListener.onOperateStart(videoClip);
                }
            } else if (action == MotionEvent.ACTION_UP){
                if (maskOperateListener != null) {
                    maskOperateListener.onOperateEnd();
                }
            }
            oneFingerTouch(event);
        }
        return true;
    }

    private double mTwoFingerStartLength = 0;
    private float minMaskScale = 0.1f;

    private float maxMaskScale = 10f;
    private float minMaskDirectionScale = 0.5f;
    private float maxMaskDirectionScale = 2f;
    private float maxFeather = 1000f;
    float downRotation = 0;

    private void twoFingerTouch(MotionEvent event) {
        int action = event.getAction();
        if (maskModel == null || maskModel.maskType == MaskType.NONE) {
            return;
        }
        if ((action & MotionEvent.ACTION_MASK) == MotionEvent.ACTION_POINTER_DOWN) {
            float xLen = event.getX(0) - event.getX(1);
            float yLen = event.getY(0) - event.getY(1);
            mTwoFingerStartLength = Math.sqrt((xLen * xLen) + (yLen * yLen));
            downRotation = getDegree(event);
        } else if ((action & MotionEvent.ACTION_MASK) == MotionEvent.ACTION_MOVE) {
            float xLen = event.getX(0) - event.getX(1);
            float yLen = event.getY(0) - event.getY(1);
            double mTwoFingerEndLength = Math.sqrt(xLen * xLen + yLen * yLen);
            float scale = (float) (mTwoFingerEndLength / mTwoFingerStartLength);
            if (maskModel.maskType == MaskType.LINE) {
                scale = 1;
            }
            maskModel.transform.scaleX *= scale;
            maskModel.transform.scaleX = Math.min(maskModel.transform.scaleX, maxMaskScale);
            maskModel.transform.scaleX = Math.max(maskModel.transform.scaleX, minMaskScale);
            maskModel.transform.scaleY = maskModel.transform.scaleX;

            float moveRotation = getDegree(event);
            float rotation = moveRotation - downRotation;
            maskModel.transform.rotation += rotation;
            refreshMaskLinePreview();
            downRotation = moveRotation;
            mTwoFingerStartLength = mTwoFingerEndLength;
        }
    }

    private int mQuadrantIndex;
    private boolean pointIsInPath;

    private void oneFingerTouch(MotionEvent event) {
        if (mIsTwoFingerEvent) {
            return;
        }
        if (maskModel == null || maskModel.maskType == MaskType.NONE) {
            return;
        }
        int action = event.getAction();
        if (action == MotionEvent.ACTION_DOWN) {
            targetX = event.getRawX();
            targetY = event.getRawY();
            mQuadrantIndex = getQuadrantIndex(targetX, targetY);
            pointIsInPath = pointIsInPath(targetX, targetY, mPath);
        } else if (action == MotionEvent.ACTION_MOVE) {
            int translationX = (int) (event.getRawX() - targetX);
            int translationY = (int) (event.getRawY() - targetY);

            targetX = event.getRawX();
            targetY = event.getRawY();

            if (mQuadrantIndex != NONE_QUADRANT) {
                PointF rotationTranslationPoint = NvMaskHelper.getPointByAngle(new PointF(translationX, translationY),
                        new PointF(0, 0), -(maskModel.transform.rotation - propertyTransform.rotation));
                translationX = (int) rotationTranslationPoint.x;
                translationY = (int) rotationTranslationPoint.y;
                if (mQuadrantIndex == FEATHER_QUADRANT) {
                    //羽化 feather
                    maskModel.feather += translationY / featherDistance * maxFeather;
                    maskModel.feather = maskModel.feather < 0 ? 0 : maskModel.feather;
                    maskModel.feather = Math.min(maskModel.feather, maxFeather);
                } else if (mQuadrantIndex == HORIZONTAL_SCALE_QUADRANT) {
                    //水平缩放 horizontal scale
                    maskModel.horizontalScale += translationX / size.x;
                    maskModel.horizontalScale = Math.max(maskModel.horizontalScale, minMaskDirectionScale);
                    maskModel.horizontalScale = Math.min(maskModel.horizontalScale, maxMaskDirectionScale);
                } else if (mQuadrantIndex == VERTICAL_SCALE_QUADRANT) {
                    //竖直缩放 vertical scale
                    maskModel.verticalScale -= translationY / size.y;
                    maskModel.verticalScale = Math.max(maskModel.verticalScale, minMaskDirectionScale);
                    maskModel.verticalScale = Math.min(maskModel.verticalScale, maxMaskDirectionScale);
                } else if (mQuadrantIndex == CORNER_RADIUS_QUADRANT) {
                    //圆角 corner radius
                    float maxCorner = size.y * 0.5f;
                    float trans = (float) (Math.sqrt(translationX * translationX + translationY * translationY) * (translationX > 0 ? -1 : 1));
                    if (trans == 0) {
                        return;
                    }
                    float cornerRadius = maskModel.cornerRadiusRate * maxCorner;
                    cornerRadius += trans / featherDistance * maxCorner;
                    cornerRadius = cornerRadius < 0 ? 0 : cornerRadius;
                    cornerRadius = Math.min(cornerRadius, maxCorner);
                    maskModel.cornerRadiusRate = cornerRadius / maxCorner;
                }
                refreshMaskLinePreview();
                targetX = event.getRawX();
                targetY = event.getRawY();
                return;
            }
            if (!pointIsInPath && (maskModel.maskType != MaskType.LINE)) {
                return;
            }
            //视频片段缩放时，蒙版运动过快 Masking motion too fast when scaling video clips.
            float transformModelScale = Math.abs(propertyTransform.scaleX);
            translationX /= transformModelScale;
            translationY /= transformModelScale;
            //旋转以后应该平移的距离 The distance that should be translated after rotation.
            PointF rotationTranslationPoint = NvMaskHelper.getPointByAngle(new PointF(translationX, translationY),
                    new PointF(0, 0), propertyTransform.rotation);
            float transX = maskModel.transform.transformX + rotationTranslationPoint.x;
            float transY = maskModel.transform.transformY + rotationTranslationPoint.y;
            pathCenter = getMaskCenterPoint(transX, transY, timelineResolutionSize, liveWindrowSize);
            if (pathCenter.x <= (zoomViewSize.x - liveWindrowSize.x) / 2
                    || pathCenter.x >= (zoomViewSize.x + liveWindrowSize.x) / 2
                    || pathCenter.y <= (zoomViewSize.y - liveWindrowSize.y) / 2
                    || pathCenter.y >= (zoomViewSize.y + liveWindrowSize.y) / 2) {
                return;
            }
            maskModel.transform.transformX = transX;
            maskModel.transform.transformY = transY;
            refreshMaskLinePreview();
        }

    }


    private PointF timelineResolutionSize;
    private boolean isHidden = false;
    private PointF textSize;
    private MeicamVideoClip videoClip;
    private PointF assetSize;
    private PointF liveWindrowSize;
    private PointF assetResolution;
    /**
     * zoomView的大小
     * The zoom view size
     */
    private Point zoomViewSize;
    private Transform propertyTransform;
    private NvMaskModel maskModel;
    /**
     * 羽化的图标区域
     * the first quadrant icon rect
     */
    private RectF featherQuadrant = new RectF();

    /**
     * 水平缩放的图标区域
     * the second quadrant icon rect
     */
    private RectF horizontalScaleQuadrant = new RectF();

    /**
     * 竖直缩放的图标区域
     * the third quadrant icon rect
     */
    private RectF verticalScaleQuadrant = new RectF();

    /**
     * 矩形角度的图标区域
     * the fourth quadrant icon rect
     */
    private RectF cornerRadiusQuadrant = new RectF();

    public void refreshMaskLinePreview() {
        if (videoClip != null) {
            loadMaskModel(maskModel, assetResolution, assetSize, true);
        }
    }

    public void loadMaskModel(MeicamVideoClip meicamVideoClip, NvsLiveWindowExt
            liveWindow, NvsVideoResolution timelineResolution, Transform transform, boolean needCallBack) {
        textSize = null;
        if (meicamVideoClip == null) {
            return;
        }
        zoomViewSize = new Point(liveWindow.getWidth(), liveWindow.getHeight());
        videoClip = meicamVideoClip;
        maskModel = videoClip.maskModel;
        propertyTransform = transform;
        float rectWidth = videoClip.getRectWidth();
        float rectHeight = videoClip.getRectHeight();
        float assetAspectRatio;
        if (rectHeight != 0 && rectWidth != 0) {
            assetResolution = new PointF(rectWidth, rectHeight);
            assetAspectRatio = rectWidth / rectHeight;
        } else {
            assetResolution = new PointF(videoClip.getOriginalWidth(), videoClip.getOriginalHeight());
            assetAspectRatio = videoClip.getOriginalWidth() * 1.0f / videoClip.getOriginalHeight();
        }
        timelineResolutionSize = new PointF(timelineResolution.imageWidth, timelineResolution.imageHeight);
        if (maskModel.maskType == MaskType.NONE) {
            isHidden = true;
            invalidate();
        } else {
            isHidden = false;
            liveWindrowSize = frameForTimeline(timelineResolutionSize, zoomViewSize.x, zoomViewSize.y);
            if (liveWindrowSize == null) {
                return;
            }
            assetSize = assetSizeInBox(liveWindrowSize, assetAspectRatio);
            loadMaskModel(maskModel, assetResolution, assetSize, needCallBack);
        }
    }

    /**
     * 按钮到线的距离
     * Distance from button to line
     */
    private int btInterval = 60;
    /**
     * 按钮的大小
     * The size of the button
     */
    private int btSize = 60;
    private float featherDistance = 60.0f;
    private Path mPath;

    private PointF size = null;
    private PointF pathCenter;

    private void loadMaskModel(NvMaskModel maskModel, PointF
            assetResolution, PointF assetSize, boolean needCallback) {
        int maskType = maskModel.maskType;
        if (maskType == MaskType.TEXT) {
            if (textSize == null) {
                textSize = getTextSize();
            }
            if (textSize == null) {
                return;
            }
            size = new PointF(textSize.x * maskModel.transform.scaleX, textSize.y * maskModel.transform.scaleY);
        } else {
            size = NvMaskHelper.boxMaskSize(maskModel, liveWindrowSize, assetResolution, assetSize);
        }
        float transformModelScale = Math.abs(propertyTransform.scaleX);
        size.x *= transformModelScale;
        size.y *= transformModelScale;
        pathCenter = getMaskCenterPoint(maskModel.transform.transformX, maskModel.transform.transformY, timelineResolutionSize, liveWindrowSize);
        if (maskType == MaskType.LINE) {
            mPath = NvMaskHelper.linePath(pathCenter, liveWindrowSize, 0);
        } else if (maskType == MaskType.MIRROR) {
            mPath = NvMaskHelper.mirrorPath(pathCenter, size, 0);
        } else if (maskType == MaskType.RECT) {
            mPath = NvMaskHelper.rectPath(pathCenter, size, maskModel.cornerRadiusRate);
        } else if (maskType == MaskType.CIRCLE) {
            mPath = NvMaskHelper.circlePath(pathCenter, size, 0);
        } else if (maskType == MaskType.STAR) {
            mPath = NvMaskHelper.startPath(pathCenter, size.x, 0);
        } else if (maskType == MaskType.HEART) {
            mPath = NvMaskHelper.heartPath(pathCenter, size.x, 0);
        } else if (maskType == MaskType.TEXT) {
            mPath = NvMaskHelper.textPath(pathCenter, size);
        }
        NvMaskHelper.transformPath(mPath, pathCenter, maskModel.transform.rotation - propertyTransform.rotation);
        invalidate();
        if (maskOperateListener != null && needCallback) {
            NvMaskHelper.prepareMaskRegionPoints(maskModel, size, liveWindrowSize, assetSize, assetResolution, propertyTransform);
            maskOperateListener.onOperate(videoClip);
        }
    }

    private PointF getMaskCenterPoint(float maskTransformX,
                                      float maskTransformY, PointF
                                              timelineResolutionSize, PointF liveWindowSize) {
        float transformRate = liveWindowSize.x / timelineResolutionSize.x;
        //属性特效平移在liveWindow上的平移
        // Translation of attribute effects on liveWindow
        PointF transform = new PointF(propertyTransform.transformX * transformRate,
                propertyTransform.transformY * transformRate);
        PointF tmpPoint = new PointF(maskTransformX * Math.abs(propertyTransform.scaleX),
                maskTransformY * Math.abs(propertyTransform.scaleY));
        PointF tmpPoint2 = NvMaskHelper.getPointByAngle(tmpPoint, new PointF(0, 0), -propertyTransform.rotation);
        PointF maskCenter = new PointF(zoomViewSize.x / 2.0f, zoomViewSize.y / 2.0f);
        maskCenter.x += transform.x + tmpPoint2.x;
        maskCenter.y += -transform.y + tmpPoint2.y;
        return maskCenter;
    }

    private PointF getTextSize() {
        if (videoClip == null) {
            return null;
        }
        if (maskModel == null || maskModel.maskType != MaskType.TEXT) {
            return null;
        }
        if (TextUtils.isEmpty(maskModel.text)) {
            return null;
        }
        Paint pFont = new Paint();
        Rect rect = new Rect();
        float fontSize = zoomViewSize.x * maskModel.heightRateOfWidth;
        pFont.setTextSize(fontSize);
        pFont.getTextBounds(maskModel.text, 0, maskModel.text.length(), rect);
        float textInterval = 10;
        return new PointF(rect.width() + textInterval, rect.height() + textInterval);
    }

    /**
     * 素材在livewindow 展示的size
     * Asset size in box point f.
     *
     * @param boxSize          preview Rect
     * @param assetAspectRatio the asset aspect ratio  素材原始宽高比
     * @return the point f  素材size
     */
    public static PointF assetSizeInBox(PointF boxSize, float assetAspectRatio) {
        PointF pointF = new PointF();
        float boxSizeRate = boxSize.x * 1.0f / boxSize.y;
        if (boxSizeRate > assetAspectRatio) {
            pointF.y = boxSize.y;
            pointF.x = pointF.y * assetAspectRatio;
        } else {
            pointF.x = boxSize.x;
            pointF.y = pointF.x / assetAspectRatio;
        }
        return pointF;
    }

    /**
     * Frame for timeline point f.
     * 根据timeline比例计算timeline 展示size
     *
     * @return the point f
     */
    private static PointF frameForTimeline(PointF videoResolution, int width, int height) {
        if (videoResolution == null) {
            LogUtils.e("videoResolution is null");
            return null;
        }
        float timelineRatio = videoResolution.x * 1.0F / videoResolution.y;
        float viewRatio = width * 1.0F / height;
        PointF rect = new PointF();
        if (timelineRatio > viewRatio) {
            //宽对齐Wide alignment
            rect.x = width;
            rect.y = width / timelineRatio;
        } else {
            rect.y = height;
            rect.x = height * timelineRatio;
        }
        return rect;
    }

    /**
     * 取旋转角度
     * Take the rotation degree
     *
     * @param event the event
     * @return the degree
     */
    private float getDegree(MotionEvent event) {
        //得到两个手指间的旋转角度 You get the rotation Angle between the two fingers
        double delta_x = event.getX(0) - event.getX(1);
        double delta_y = event.getY(0) - event.getY(1);
        double radians = Math.atan2(delta_y, delta_x);
        return (float) Math.toDegrees(radians);
    }


    public static final int NONE_QUADRANT = 0;
    public static final int FEATHER_QUADRANT = 1;
    public static final int HORIZONTAL_SCALE_QUADRANT = 2;
    public static final int VERTICAL_SCALE_QUADRANT = 3;
    public static final int CORNER_RADIUS_QUADRANT = 4;

    /**
     * 获取象限索引
     * Get the quadrant index
     */
    private int getQuadrantIndex(float upX, float upY) {
        if (featherQuadrant.contains(upX, upY)) {
            return FEATHER_QUADRANT;
        }
        if (horizontalScaleQuadrant.contains(upX, upY)) {
            return HORIZONTAL_SCALE_QUADRANT;
        }
        if (verticalScaleQuadrant.contains(upX, upY)) {
            return VERTICAL_SCALE_QUADRANT;
        }
        if (cornerRadiusQuadrant.contains(upX, upY)) {
            return CORNER_RADIUS_QUADRANT;
        }
        return NONE_QUADRANT;
    }

    /**
     * The type Mask type.
     * 类型掩码类型
     */
    public class MaskType {
        /**
         * The constant NONE.
         * 常数没有
         */
        public static final int NONE = 0;
        /**
         * The constant LINE.
         * 线
         */
        public static final int LINE = 1;
        /**
         * The constant MIRROR.
         * 镜像
         */
        public static final int MIRROR = 2;
        /**
         * The constant CIRCLE.
         * 圆
         */
        public static final int CIRCLE = 3;
        /**
         * The constant RECT.
         * 矩形
         */
        public static final int RECT = 4;
        /**
         * The constant HEART.
         * 心形
         */
        public static final int HEART = 5;
        /**
         * The constant STAR.
         * 星形
         */
        public static final int STAR = 6;
        /**
         * The text
         * 文字
         */
        public static final int TEXT = 7;

    }

    private MaskOperateListener maskOperateListener;

    public interface MaskOperateListener {
        void onOperate(MeicamVideoClip meicamVideoClip);
        void onOperateStart(MeicamVideoClip meicamVideoClip);
        void onOperateEnd();
    }

    public void setMaskOperateListener(MaskOperateListener maskOperateListener) {
        this.maskOperateListener = maskOperateListener;
    }

    private boolean pointIsInPath(float x, float y, Path path) {
        if (path == null) {
            return false;
        }
        RectF bounds = new RectF();
        path.computeBounds(bounds, true);
        Region region = new Region();
        region.setPath(path, new Region((int) bounds.left, (int) bounds.top, (int) bounds.right, (int) bounds.bottom));
        return region.contains((int) x, (int) y);
    }
}
