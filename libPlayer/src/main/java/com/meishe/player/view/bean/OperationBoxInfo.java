package com.meishe.player.view.bean;

import android.graphics.PointF;


import com.meishe.player.view.OperationBox;

import java.util.List;

import static com.meishe.player.view.OperationBox.NONE_QUADRANT;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateDate :2021/2/1 10:07
 * @Description :特效操作框专用实体类 Special operation box entity class
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class OperationBoxInfo {
    /**
     * 第一象限角的图标
     * The first quadrant Angle icon
     */
    private int firstQuadrantIcon;
    /**
     * 第二象限角的图标
     * The second quadrant Angle icon
     */
    private int secondQuadrantIcon;
    /**
     * 第三象限角的图标
     * The third quadrant Angle icon
     */
    private int thirdQuadrantIcon;
    /**
     * 第四象限角的图标
     * The fourth quadrant Angle icon
     */
    private int fourthQuadrantIcon;
    /**
     * 四个角的坐标集合
     * The set of coordinates of four angles
     */
    private List<PointF> cornerPointList;
    /**
     * 内部边框的四个角坐标集合
     * Set of four angular coordinates of the inner border
     */
    private List<List<PointF>> insidePointList;
    /**
     * 内部封面资源路径
     * The resource path of the internal cover
     */
    private String insideCover;
    /**
     * 类型
     * The type
     */
    private int type;

    /**
     * 缩放、旋转图标所在象限
     * The quadrant where the icon is scaled and rotated
     */
    private @OperationBox.Quadrant
    int scaleQuadrant = NONE_QUADRANT;

    public int getFirstQuadrantIcon() {
        return firstQuadrantIcon;
    }

    public OperationBoxInfo setFirstQuadrantIcon(int firstQuadrantIcon) {
        this.firstQuadrantIcon = firstQuadrantIcon;
        return this;
    }

    public int getSecondQuadrantIcon() {
        return secondQuadrantIcon;
    }

    public OperationBoxInfo setSecondQuadrantIcon(int secondQuadrantIcon) {
        this.secondQuadrantIcon = secondQuadrantIcon;
        return this;
    }

    public int getThirdQuadrantIcon() {
        return thirdQuadrantIcon;
    }

    public OperationBoxInfo setThirdQuadrantIcon(int thirdQuadrantIcon) {
        this.thirdQuadrantIcon = thirdQuadrantIcon;
        return this;
    }

    public int getFourthQuadrantIcon() {
        return fourthQuadrantIcon;
    }

    public OperationBoxInfo setFourthQuadrantIcon(int fourthQuadrantIcon) {
        this.fourthQuadrantIcon = fourthQuadrantIcon;
        return this;
    }

    public List<PointF> getCornerPointList() {
        return cornerPointList;
    }

    public OperationBoxInfo setCornerPointList(List<PointF> cornerPointList) {
        this.cornerPointList = cornerPointList;
        return this;
    }

    public List<List<PointF>> getInsidePointList() {
        return insidePointList;
    }

    public OperationBoxInfo setInsidePointList(List<List<PointF>> insidePointList) {
        this.insidePointList = insidePointList;
        return this;
    }

    public String getInsideCover() {
        return insideCover;
    }

    public void setInsideCover(String insideCover) {
        this.insideCover = insideCover;
    }

    public int getType() {
        return type;
    }

    public OperationBoxInfo setType(int type) {
        this.type = type;
        return this;
    }

    public int getScaleQuadrant() {
        return scaleQuadrant;
    }

    public OperationBoxInfo setScaleQuadrant(int scaleQuadrant) {
        this.scaleQuadrant = scaleQuadrant;
        return this;
    }

    public PointF getCornerPointF(int index) {
        return cornerPointList.get(index);
    }

    public PointF getCenterPointF() {
        PointF pointF = new PointF();
        if (cornerPointList != null && cornerPointList.size() >= 4) {
            pointF.x = (cornerPointList.get(0).x + cornerPointList.get(2).x) / 2f;
            pointF.y = (cornerPointList.get(0).y + cornerPointList.get(2).y) / 2f;
        }
        return pointF;
    }

    public void clear() {
        if (cornerPointList != null) {
            cornerPointList.clear();
        }
        if (insidePointList != null) {
            insidePointList.clear();
        }
        firstQuadrantIcon = 0;
        secondQuadrantIcon = 0;
        thirdQuadrantIcon = 0;
        fourthQuadrantIcon = 0;
        insideCover = "";
        type = -1;
        scaleQuadrant = NONE_QUADRANT;
    }
}
