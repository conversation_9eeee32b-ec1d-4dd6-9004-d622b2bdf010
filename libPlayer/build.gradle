apply plugin: 'com.android.library'

android {
    compileSdkVersion rootProject.android.extCompileSdkVersion
    defaultConfig {
        minSdkVersion rootProject.android.extMinSdkVersion
        targetSdkVersion rootProject.android.extTargetSdkVersion
        versionCode rootProject.config.extVersionCode
        versionName rootProject.config.extVersionName

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    testImplementation rootProject.ext.dependencies.extTestJunit
    androidTestImplementation rootProject.ext.dependencies.extAndroidTestRunner
    androidTestImplementation rootProject.ext.dependencies.extTestEspresso
    implementation project(path: ':libBase')
    implementation project(path: ':libEngine')
}
