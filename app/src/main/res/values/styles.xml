<resources>

    <!-- Base application theme. -->
    <style name="AppTheme" parent="Theme.AppCompat.Light.DarkActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
    </style>

    <style name="TranslucentFullScreenTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowBackground">@color/edit_activity_main_color</item>
    </style>

    <style name="BaseAnimationTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/ff000000</item>
        <item name="colorPrimaryDark">@color/ff000000</item>
        <item name="colorAccent">@color/colorAccent</item>
        <!--activity的动画-->
        <item name="android:windowAnimationStyle">@style/HoloThemeActivityAnimation</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
        <!--<item name="android:windowBackground">@android:color/transparent</item>-->

    </style>

    <!--Activity入场与退出动画-->
    <style name="HoloThemeActivityAnimation" parent="@android:style/Animation.Activity">
        <item name="android:activityOpenEnterAnimation">@anim/slide_left</item>
        <!--<item name="android:activityOpenExitAnimation">@anim/slide_right</item>-->
        <!--<item name="android:activityCloseEnterAnimation">@anim/slide_left</item>-->
        <item name="android:activityCloseExitAnimation">@anim/slide_right</item>
    </style>


    <declare-styleable name="CompileProgress">
        <attr name="edit_progress" format="integer" />
        <attr name="maxProgress" format="integer" />
        <attr name="minProgress" format="integer" />
        <attr name="progressColor" format="color" />
        <attr name="progressBackgroundColor" format="color" />
        <attr name="progressWidth" format="dimension" />
    </declare-styleable>


    <!--参数设置checkbox样式-->
    <style name="CustomCheckBoxTheme" parent="@android:style/Widget.CompoundButton.CheckBox">
        <item name="android:button">@null</item>
    </style>

    <style name="AppTheme.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar" />

    <style name="AppTheme.PopupOverlay" parent="ThemeOverlay.AppCompat.Light" />

</resources>
