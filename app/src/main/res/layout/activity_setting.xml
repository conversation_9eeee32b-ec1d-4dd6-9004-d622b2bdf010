<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/menu_bg"
    android:orientation="vertical"
    android:paddingLeft="@dimen/dp_px_45"
    android:paddingTop="@dimen/dp_px_105"
    android:paddingRight="@dimen/dp_px_45">

    <ImageView
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        android:id="@+id/iv_back"
        android:layout_width="@dimen/dp_px_48"
        android:layout_height="@dimen/dp_px_48"
        android:background="@mipmap/icon_setting_close"
        android:contentDescription="@null" />

    <TextView
        app:layout_constraintTop_toBottomOf="@+id/iv_back"
        app:layout_constraintLeft_toLeftOf="parent"
        android:id="@+id/tv_setting_resolution"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_px_72"
        android:text="@string/captureResolution"
        android:textColor="@color/white_8"
        android:textSize="@dimen/sp_px_36" />

    <RadioGroup
        app:layout_constraintTop_toBottomOf="@+id/tv_setting_resolution"
        android:id="@+id/rg_ratio"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_px_26"
        android:layout_marginEnd="@dimen/dp_px_30"
        android:layout_marginRight="@dimen/dp_px_30"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingTop="@dimen/dp_px_22"
            android:paddingBottom="@dimen/dp_px_22">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/int1080"
                android:textColor="@color/white_8"
                android:textSize="@dimen/sp_px_42"
                tools:ignore="RelativeOverlap" />

            <RadioButton
                android:id="@+id/rb_ratio_1080"
                style="@style/CustomCheckBoxTheme"
                android:layout_width="@dimen/dp_px_45"
                android:layout_height="@dimen/dp_px_45"
                android:layout_alignParentEnd="true"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_gravity="center_vertical"
                android:background="@drawable/setting_radio_button" />
        </RelativeLayout>

        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingTop="@dimen/dp_px_22"
            android:paddingBottom="@dimen/dp_px_22">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/int720"
                android:textColor="@color/white_8"
                android:textSize="@dimen/sp_px_42"
                tools:ignore="RelativeOverlap" />

            <RadioButton
                android:id="@+id/rb_ratio_720"
                style="@style/CustomCheckBoxTheme"
                android:layout_width="@dimen/dp_px_45"
                android:layout_height="@dimen/dp_px_45"
                android:layout_alignParentEnd="true"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_gravity="center"
                android:background="@drawable/setting_radio_button" />
        </RelativeLayout>
    </RadioGroup>

    <View
        app:layout_constraintTop_toBottomOf="@+id/rg_ratio"
        android:id="@+id/v_line5"
        android:layout_marginTop="@dimen/dp_px_32"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_2"
        android:layout_marginEnd="@dimen/dp_px_30"
        android:layout_marginRight="@dimen/dp_px_30"
        android:background="@color/color_white_33" />
    <TextView
        android:id="@+id/tv_hdr_import"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/v_line5"
        android:layout_marginTop="@dimen/dp_px_49"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/setting_hdr_import"
        android:textSize="@dimen/sp_px_36"
        android:textColor="@color/white"/>

    <TextView
        android:id="@+id/tv_preview_mode"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_hdr_import"
        android:layout_marginTop="@dimen/dp_px_60"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/setting_hdr_preview_mode"
        android:textSize="@dimen/sp_px_36"
        android:textColor="@color/white_8"/>

    <RadioGroup
        app:layout_constraintTop_toBottomOf="@+id/tv_preview_mode"
        android:id="@+id/rg_preview_mode"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_px_48"
        android:gravity="center"
        android:orientation="horizontal">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:orientation="horizontal">
            <RadioButton
                android:id="@+id/rb_preview_mode_sdr"
                style="@style/CustomCheckBoxTheme"
                android:layout_width="@dimen/dp_px_45"
                android:layout_height="@dimen/dp_px_45"
                android:layout_gravity="center_vertical"
                android:background="@drawable/setting_radio_button" />
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_px_15"
                android:text="@string/setting_hdr_preview_mode_sdr"
                android:textColor="@color/white_8"
                android:textSize="@dimen/sp_px_42"
                tools:ignore="RelativeOverlap" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_weight="1">
            <RadioButton
                android:id="@+id/rb_preview_mode_device"
                style="@style/CustomCheckBoxTheme"
                android:layout_width="@dimen/dp_px_45"
                android:layout_height="@dimen/dp_px_45"
                android:layout_gravity="center_vertical"
                android:background="@drawable/setting_radio_button" />
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_px_15"
                android:text="@string/setting_hdr_preview_mode_device"
                android:textColor="@color/white_8"
                android:textSize="@dimen/sp_px_42"
                tools:ignore="RelativeOverlap" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal">
            <RadioButton
                android:id="@+id/rb_preview_mode_auto"
                style="@style/CustomCheckBoxTheme"
                android:layout_width="@dimen/dp_px_45"
                android:layout_height="@dimen/dp_px_45"
                android:layout_gravity="center_vertical"
                android:background="@drawable/setting_radio_button" />
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_px_15"
                android:text="@string/setting_hdr_preview_mode_auto"
                android:textColor="@color/white_8"
                android:textSize="@dimen/sp_px_42"
                tools:ignore="RelativeOverlap" />
        </LinearLayout>
    </RadioGroup>

    <TextView
        android:id="@+id/tv_color_gain"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/rg_preview_mode"
        android:layout_marginTop="@dimen/dp_px_60"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/setting_hdr_sdr_to_hdr_color_gain"
        android:textSize="@dimen/sp_px_36"
        android:textColor="@color/white_8"/>

    <com.meishe.myvideo.view.MYSeekBarTopTextView
        android:id="@+id/seek_bar_color_gain"
        app:layout_constraintTop_toBottomOf="@+id/tv_color_gain"
        android:maxHeight="@dimen/dp_px_6"
        android:max="100"
        android:layout_marginStart="-8dp"
        android:layout_marginEnd="-8dp"
        android:layout_marginTop="@dimen/dp_px_75"
        android:progressDrawable="@drawable/edit_seek_bar_ff365e"
        android:thumb="@drawable/edit_seek_bar_ball_ff365e"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>

    <TextView
        android:id="@+id/tv_bit_depth"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/seek_bar_color_gain"
        android:layout_marginTop="@dimen/dp_px_60"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/setting_hdr_bit_depth"
        android:textSize="@dimen/sp_px_36"
        android:textColor="@color/white_8"/>

    <RadioGroup
        app:layout_constraintTop_toBottomOf="@+id/tv_bit_depth"
        android:id="@+id/rg_bit_depth"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_px_48"
        android:gravity="center"
        android:orientation="horizontal">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:orientation="horizontal">
            <RadioButton
                android:id="@+id/rb_bit_depth_8"
                style="@style/CustomCheckBoxTheme"
                android:layout_width="@dimen/dp_px_45"
                android:layout_height="@dimen/dp_px_45"
                android:layout_gravity="center_vertical"
                android:background="@drawable/setting_radio_button" />
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_px_15"
                android:text="@string/setting_hdr_bit_depth_8"
                android:textColor="@color/white_8"
                android:textSize="@dimen/sp_px_42"
                tools:ignore="RelativeOverlap" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_weight="1">
            <RadioButton
                android:id="@+id/rb_bit_depth_16"
                style="@style/CustomCheckBoxTheme"
                android:layout_width="@dimen/dp_px_45"
                android:layout_height="@dimen/dp_px_45"
                android:layout_gravity="center_vertical"
                android:background="@drawable/setting_radio_button" />
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_px_15"
                android:text="@string/setting_hdr_bit_depth_16"
                android:textColor="@color/white_8"
                android:textSize="@dimen/sp_px_42"
                tools:ignore="RelativeOverlap" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp_px_72"
            android:orientation="horizontal">
            <RadioButton
                android:id="@+id/rb_bit_depth_auto"
                style="@style/CustomCheckBoxTheme"
                android:layout_width="@dimen/dp_px_45"
                android:layout_height="@dimen/dp_px_45"
                android:layout_gravity="center_vertical"
                android:background="@drawable/setting_radio_button" />
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_px_15"
                android:text="@string/setting_hdr_bit_depth_auto"
                android:textColor="@color/white_8"
                android:textSize="@dimen/sp_px_42"
                tools:ignore="RelativeOverlap" />
        </LinearLayout>
    </RadioGroup>

    <View
        app:layout_constraintTop_toBottomOf="@+id/rg_bit_depth"
        android:id="@+id/v_line1"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_2"
        android:layout_marginTop="@dimen/dp_px_48"
        android:layout_marginEnd="@dimen/dp_px_30"
        android:layout_marginRight="@dimen/dp_px_30"
        android:background="@color/color_white_33" />

    <TextView
        app:layout_constraintTop_toBottomOf="@+id/v_line1"
        android:id="@+id/tv_feed_back"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_140"
        android:gravity="center_vertical"
        android:text="@string/setting_feed_back"
        android:textColor="@color/white_8"
        android:textSize="@dimen/sp_px_36" />

    <ImageView
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_feed_back"
        app:layout_constraintBottom_toBottomOf="@+id/tv_feed_back"
        android:layout_width="@dimen/dp_px_40"
        android:layout_height="@dimen/dp_px_40"
        android:layout_below="@+id/v_line1"
        android:layout_alignTop="@+id/tv_feed_back"
        android:layout_alignParentEnd="true"
        android:layout_alignParentRight="true"
        android:layout_marginTop="@dimen/dp_px_50"
        android:layout_marginEnd="@dimen/dp_px_30"
        android:layout_marginRight="@dimen/dp_px_30"
        android:contentDescription="@null"
        android:src="@mipmap/setting_next" />

    <View
        app:layout_constraintTop_toBottomOf="@+id/tv_feed_back"
        android:id="@+id/v_line2"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_2"
        android:layout_below="@+id/tv_feed_back"
        android:layout_marginEnd="@dimen/dp_px_30"
        android:layout_marginRight="@dimen/dp_px_30"
        android:background="@color/color_white_33" />

    <TextView
        app:layout_constraintTop_toBottomOf="@+id/v_line2"
        android:id="@+id/tv_user_agreements"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_140"
        android:layout_below="@+id/v_line2"
        android:gravity="center_vertical"
        android:text="@string/setting_user_agreements"
        android:textColor="@color/white_8"
        android:textSize="@dimen/sp_px_36" />

    <ImageView
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_user_agreements"
        app:layout_constraintBottom_toBottomOf="@+id/tv_user_agreements"
        android:layout_width="@dimen/dp_px_40"
        android:layout_height="@dimen/dp_px_40"
        android:layout_below="@+id/v_line2"
        android:layout_alignTop="@+id/tv_user_agreements"
        android:layout_alignParentEnd="true"
        android:layout_alignParentRight="true"
        android:layout_marginTop="@dimen/dp_px_50"
        android:layout_marginEnd="@dimen/dp_px_30"
        android:layout_marginRight="@dimen/dp_px_30"
        android:contentDescription="@null"
        android:src="@mipmap/setting_next" />

    <View
        app:layout_constraintTop_toBottomOf="@+id/tv_user_agreements"
        android:id="@+id/v_line3"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_2"
        android:layout_below="@+id/tv_user_agreements"
        android:layout_marginEnd="@dimen/dp_px_30"
        android:layout_marginRight="@dimen/dp_px_30"
        android:background="@color/color_white_33" />

    <TextView
        app:layout_constraintTop_toBottomOf="@+id/v_line3"
        android:id="@+id/tv_privacy_policy"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_140"
        android:layout_below="@+id/v_line3"
        android:gravity="center_vertical"
        android:text="@string/setting_privacy_policy"
        android:textColor="@color/white_8"
        android:textSize="@dimen/sp_px_36" />

    <ImageView
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_privacy_policy"
        app:layout_constraintBottom_toBottomOf="@+id/tv_privacy_policy"
        android:layout_width="@dimen/dp_px_40"
        android:layout_height="@dimen/dp_px_40"
        android:layout_below="@+id/v_line3"
        android:layout_alignTop="@+id/tv_privacy_policy"
        android:layout_alignParentEnd="true"
        android:layout_alignParentRight="true"
        android:layout_marginTop="@dimen/dp_px_50"
        android:layout_marginEnd="@dimen/dp_px_30"
        android:layout_marginRight="@dimen/dp_px_30"
        android:contentDescription="@null"
        android:src="@mipmap/setting_next" />

    <View
        app:layout_constraintTop_toBottomOf="@+id/tv_privacy_policy"
        android:id="@+id/v_line4"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_2"
        android:layout_below="@+id/tv_privacy_policy"
        android:layout_marginEnd="@dimen/dp_px_30"
        android:layout_marginRight="@dimen/dp_px_30"
        android:background="@color/color_white_33" />

    <TextView
        android:id="@+id/tv_hint_sdk_version_code_value"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/v_line4"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_px_140"
        android:gravity="center_vertical"
        android:text="@string/setting_SDK_version_code"
        android:textColor="@color/white_8"
        android:textSize="@dimen/sp_px_36" />

    <TextView
        app:layout_constraintTop_toBottomOf="@+id/v_line4"
        app:layout_constraintRight_toRightOf="parent"
        android:id="@+id/tv_sdk_version_code_value"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_px_140"
        android:layout_below="@+id/v_line4"
        android:layout_alignParentEnd="true"
        android:layout_alignParentRight="true"
        android:layout_marginEnd="@dimen/dp_px_30"
        android:layout_marginRight="@dimen/dp_px_30"
        android:gravity="center_vertical"
        android:textColor="@color/white_8"
        android:textSize="@dimen/sp_px_36" />

    <View
        app:layout_constraintTop_toBottomOf="@+id/tv_hint_sdk_version_code_value"
        android:id="@+id/v_line6"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_2"
        android:layout_marginEnd="@dimen/dp_px_30"
        android:layout_marginRight="@dimen/dp_px_30"
        android:background="@color/color_white_33" />

    <TextView
        android:id="@+id/tv_hint_version_code_value"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/v_line6"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_px_140"
        android:gravity="center_vertical"
        android:text="@string/setting_version_code"
        android:textColor="@color/white_8"
        android:textSize="@dimen/sp_px_36" />

    <TextView
        app:layout_constraintTop_toBottomOf="@+id/v_line6"
        app:layout_constraintRight_toRightOf="parent"
        android:id="@+id/tv_version_code_value"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_px_140"
        android:layout_marginEnd="@dimen/dp_px_30"
        android:layout_marginRight="@dimen/dp_px_30"
        android:gravity="center_vertical"
        android:textColor="@color/white_8"
        android:textSize="@dimen/sp_px_36" />

</androidx.constraintlayout.widget.ConstraintLayout >

