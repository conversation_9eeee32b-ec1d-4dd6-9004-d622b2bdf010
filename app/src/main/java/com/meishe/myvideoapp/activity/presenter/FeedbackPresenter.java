package com.meishe.myvideoapp.activity.presenter;

import android.text.TextUtils;
import android.util.Base64;

import com.meicam.sdk.NvsStreamingContext;
import com.meishe.base.model.Presenter;
import com.meishe.base.utils.FileIOUtils;
import com.meishe.base.utils.LogUtils;
import com.meishe.logic.utils.AppNetAPi;
import com.meishe.myvideoapp.activity.iview.FeedbackView;
import com.meishe.myvideoapp.bean.FeedbackParams;
import com.meishe.net.custom.BaseResponse;
import com.meishe.net.custom.RequestCallback;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.zip.GZIPOutputStream;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> LiHangZhou
 * @CreateDate :2020/12/9 14:54
 * @Description :
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 * 反馈的presenter
 */
public class FeedbackPresenter extends Presenter<FeedbackView> {
    /**
     * 意见反馈
     * feedback
     *
     * @param feedbackContent String The content of feedback
     * @param contactWay      String Contact way
     * @param picturePath     String The picture path
     */
    public void feedback(String feedbackContent, String contactWay, String picturePath) {
        FeedbackParams params = new FeedbackParams();
        params.content = feedbackContent;
        params.contact = contactWay;
        if (!TextUtils.isEmpty(picturePath)) {
            params.imageContent = getPictureContent(picturePath);
        }
        NvsStreamingContext.SdkVersion sdkVersion = NvsStreamingContext.getInstance().getSdkVersion();
        params.sdkVersion = sdkVersion.majorVersion + "." + sdkVersion.minorVersion +
                "." + sdkVersion.revisionNumber;
        AppNetAPi.uploadFeedback(null, params, new RequestCallback<Object>() {

            @Override
            public void onSuccess(BaseResponse<Object> response) {

                if (getView() != null) {
                    getView().onFeedbackSuccess();
                }
            }

            @Override
            public void onError(BaseResponse<Object> response) {
            }
        });
    }

    /**
     * 获取压缩后的图片内容
     * Gets the compressed image content
     *
     * @param picturePath String The picture path
     */
    private String getPictureContent(String picturePath) {
        try {
            return zipBase64(Base64.encodeToString(FileIOUtils.readFile2BytesByStream(picturePath), Base64.NO_CLOSE));
        } catch (Exception e) {
            LogUtils.e(e);
        }
        return "";
    }

    /**
     * 字符串的压缩
     * String compression
     *
     * @param content 待压缩的内容 Content to be compressed
     * @return the compressed  content
     */
    private String zipBase64(String content) {
        if (null == content || content.length() <= 0) {
            return content;
        }
        // 创建一个新的 byte 数组输出流 Creates a new byte array output stream
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        // 使用默认缓冲区大小创建新的输出流 Create a new output stream using the default buffer size
        GZIPOutputStream gzip;
        try {
            gzip = new GZIPOutputStream(out);
            // 将 b.length 个字节写入此输出流 Writes b bytes to the output stream
            gzip.write(content.getBytes());
            gzip.close();
            // 使用指定的 charsetName，通过解码字节将缓冲区内容转换为字符串 Converts the contents of the buffer to a string by decoding the bytes, using the specified CharsetName
            return out.toString("ISO-8859-1");
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                out.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return "";
    }

}
