package com.meishe.myvideoapp.activity.presenter;

import android.text.TextUtils;

import com.facebook.drawee.backends.pipeline.Fresco;
import com.iflytek.cloud.SpeechConstant;
import com.iflytek.cloud.SpeechUtility;
import com.meicam.sdk.NvsStreamingContext;
import com.meishe.base.BuildConfig;
import com.meishe.base.constants.Constants;
import com.meishe.base.model.Presenter;
import com.meishe.base.utils.FileUtils;
import com.meishe.base.utils.LogUtils;
import com.meishe.base.utils.ResourceUtils;
import com.meishe.base.utils.ThreadUtils;
import com.meishe.base.utils.Utils;
import com.meishe.engine.EditorEngine;
import com.meishe.engine.util.PathUtils;
import com.meishe.engine.util.WhiteList;
import com.meishe.libplugin.PluginManager;
import com.meishe.libplugin.user.IUserPlugin;
import com.meishe.logic.manager.PreferencesManager;
import com.meishe.logic.utils.AppNetAPi;
import com.meishe.logic.utils.NvsServerClient;
import com.meishe.logic.utils.UMengUtils;
import com.meishe.myvideoapp.activity.iview.MainView;
import com.meishe.myvideoapp.application.MeiSheApplication;
import com.meishe.myvideoapp.bean.LicenseInfo;
import com.meishe.myvideoapp.util.ConfigUtil;
import com.meishe.net.custom.BaseResponse;
import com.meishe.net.custom.RequestCallback;
import com.meishe.net.custom.SimpleDownListener;
import com.meishe.net.model.Progress;
import com.umeng.analytics.MobclickAgent;
import com.umeng.commonsdk.UMConfigure;

import java.io.File;

import static com.meishe.engine.constant.NvsConstants.HUMAN_AI_TYPE_MS;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> LiHangZhou
 * @CreateDate :2020/12/3 9:52
 * @Description :首页逻辑处理类 Home logic processing class
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class MainPresenter extends Presenter<MainView> {
    /**
     * 初始化人脸
     * init AR Scene
     */
    public void initARScene() {

        if (Constants.USE_MS_FACE) {
            //美摄人脸 Ms Face init
            initARScene(ConfigUtil.getLicPath());
            return;
        }
        //商汤授权 Thomson authorization
        long authorEndTime = PreferencesManager.get().getAuthorEndTime();
        long currentTimeMillis = System.currentTimeMillis();
        if (authorEndTime <= 0) {
            getSdkLicense();
            return;
        }
        if (currentTimeMillis < authorEndTime) {
            String licenseFilePath = PreferencesManager.get().getAuthorFilePath();
            if (TextUtils.isEmpty(licenseFilePath) || !FileUtils.isFileExists(licenseFilePath)) {
                getSdkLicense();
                return;
            }
            initARScene(licenseFilePath);
        } else {
            getSdkLicense();
        }

    }

    /**
     * 获取授权文件
     * Gets the authorization file of sdk
     */
    private void getSdkLicense() {
        AppNetAPi.getSdkLicense(null, new RequestCallback<LicenseInfo>() {
            @Override
            public void onSuccess(BaseResponse<LicenseInfo> response) {
                if (response != null && response.getData() != null) {
                    PreferencesManager.get().setAuthorEndTime(response.getData().getEndTimestamp());
                    String authorizationFileUrl = response.getData().getAuthorizationFileUrl();
                    if (TextUtils.isEmpty(authorizationFileUrl)) {
                        return;
                    }
                    downloadAuthorFile(authorizationFileUrl);
                }

            }

            @Override
            public void onError(BaseResponse<LicenseInfo> response) {

            }
        });
    }

    /**
     * 下载授权文件
     * Download authorization file
     *
     * @param fileUrl String  The url of authorization file
     */
    private void downloadAuthorFile(String fileUrl) {
        AppNetAPi.download(fileUrl, fileUrl, PathUtils.getLicenseFileFolder(), "", new SimpleDownListener(fileUrl) {
            @Override
            public void onFinish(File file, Progress progress) {
                PreferencesManager.get().setAuthorFilePath(file.getAbsolutePath());
                //下载成功之后进行授权
                //Authorize after successful download
                initARScene(file.getAbsolutePath());
            }
        });
    }

    /**
     * 初始化人脸
     * init AR Scene
     *
     * @param licenseFilePath String The path of authorization file
     */
    private void initARScene(final String licenseFilePath) {
        if (NvsStreamingContext.hasARModule() == HUMAN_AI_TYPE_MS) {
            ThreadUtils.getIoPool().execute(() -> {
                String assetPath = "facemode/ms_face240_v2.0.6.model";
                String desPath = PathUtils.getFaceModelDir() + "/ms_face240_v2.0.6.model";
                boolean copySuccess = ResourceUtils.copyFileFromAssets(assetPath, desPath);
                ThreadUtils.runOnUiThread(() -> {
                    boolean initSuccess = NvsStreamingContext.initHumanDetection(Utils.getApp(),
                            desPath, licenseFilePath,
                            NvsStreamingContext.HUMAN_DETECTION_FEATURE_FACE_LANDMARK |
                                    NvsStreamingContext.HUMAN_DETECTION_FEATURE_FACE_ACTION |
                                    NvsStreamingContext.HUMAN_DETECTION_FEATURE_IMAGE_MODE
                                    | NvsStreamingContext.HUMAN_DETECTION_FEATURE_SEMI_IMAGE_MODE
                                    | NvsStreamingContext.HUMAN_DETECTION_FEATURE_VIDEO_MODE);
                    String fakeFacePath = "assets:/facemode/fakeface.dat";
                    boolean fakeFaceSuccess = NvsStreamingContext.setupHumanDetectionData(NvsStreamingContext.HUMAN_DETECTION_DATA_TYPE_FAKE_FACE, fakeFacePath);
                    /**
                     * 这个跟人脸240点位相关，如果使用240点位，必须加这个否则可能导致不生效
                     * This is related to the 240 points of the face.
                     * If 240 points are used, this must be added, otherwise it may not take effect.
                     */
                    String pePath = "assets:/facemode/pe240_ms_v1.0.2.dat";
                    boolean peSuccess = NvsStreamingContext.setupHumanDetectionData(NvsStreamingContext.HUMAN_DETECTION_DATA_TYPE_PE240, pePath);
                    /**
                     * 美妆
                     */
                    String makeupPath = "assets:/facemode/makeup2_240_v2.1.0.dat";
                    boolean makeupSuccess = NvsStreamingContext.setupHumanDetectionData(NvsStreamingContext.HUMAN_DETECTION_DATA_TYPE_MAKEUP2, makeupPath);
                    /**
                     * 手势点位模型，比心等效果会使用到这个模型
                     * Gesture point model, heart and other effects will use this model
                     */
                    String handPath = "assets:/facemode/ms_hand_v1.0.0.model";
                    boolean handSuccess = NvsStreamingContext.initHumanDetectionExt(Utils.getApp(),
                            handPath, null, NvsStreamingContext.HUMAN_DETECTION_FEATURE_HAND_LANDMARK
                                    | NvsStreamingContext.HUMAN_DETECTION_FEATURE_HAND_ACTION);

                    /**
                     * 人像背景分割模型
                     * Portrait Background Segmentation Model
                     */
                    String segPath = "assets:/facemode/ms_humanseg_v1.0.11.model";
                    NvsStreamingContext.initHumanDetectionExt(Utils.getApp(), segPath, licenseFilePath, NvsStreamingContext.HUMAN_DETECTION_FEATURE_SEGMENTATION_BACKGROUND);
                    LogUtils.d("copySuccess=" + copySuccess + "--initSuccess-->" + initSuccess +
                            "--fakeFaceSuccess-->" + fakeFaceSuccess + "--makeupSuccess-->" + makeupSuccess
                            + "---peSuccess---" + peSuccess
                            + "---handSuccess---" + handSuccess);
                });
            });
        }
    }

    /**
     * 初始化第三方sdk相关
     */
    public void initSDK() {
        if (WhiteList.isNeedSetMaxIconReaderWhiteList()) {
            NvsStreamingContext.setMaxIconReader(2);
        }
            /*sdk内部默认是7个，设置成8个的原因是;主轨道+三个画中画轨道，使用设置Alpha特效的方式实现抠像，
            最后一个画中画轨道画面会透明。*/
        NvsStreamingContext.setMaxReaderCount(8);
        /**
         * 多轨道有可能不显示，默认4
         */
        NvsStreamingContext.setMaxImageReaderCount(6);
        //初始化
        EditorEngine.init(ConfigUtil.getLicPath());
        if (!BuildConfig.DEBUG) {
            if (ConfigUtil.hasLog()) {
                //友盟
                //预初始化
                //UM Pre init
                UMConfigure.preInit(Utils.getApp(), UMengUtils.APPID, "android");
                UMConfigure.init(Utils.getApp(), UMengUtils.APPID, "android", UMConfigure.DEVICE_TYPE_PHONE, null);
                UMConfigure.setLogEnabled(false);
                MobclickAgent.setPageCollectionMode(MobclickAgent.PageMode.LEGACY_MANUAL);
            }
            //bugly异常上报 是否开启debug模式，true表示打开debug模式，false表示关闭调试模式
            //Bugly report whether the debug mode is enabled.
            // True indicates that the debug mode is enabled, and false indicates that the debug mode is turned off.
            //Bugly.init(mContext, com.meishe.myvideo.BuildConfig.buglyAppID, false);
        } else {
            initMemeLeaK();
        }
        Fresco.initialize(Utils.getApp());
        NvsServerClient.get().initConfig(MeiSheApplication.getInstance(), "");
        dealXFSpeech();
    }

    private void initMemeLeaK() {
    }

    /**
     * 讯飞语音初始化
     */
    private void dealXFSpeech() {
        // 应用程序入口处调用,避免手机内存过小,杀死后台进程后通过历史intent进入Activity造成SpeechUtility对象为null
        // 注意：此接口在非主进程调用会返回null对象，如需在非主进程使用语音功能，请增加参数：SpeechConstant.FORCE_LOGIN+"=true"
        // 参数间使用“,”分隔。
        // 设置你申请的应用appid

        // 注意： appid 必须和下载的SDK保持一致，否则会出现10407错误
        try {
            String param = "appid=5f4dbb29" + "," + SpeechConstant.ENGINE_MODE + "=" + SpeechConstant.MODE_MSC;
            // 设置使用v5+
            SpeechUtility.createUtility(Utils.getApp(), param);
        } catch (Exception e) {
            LogUtils.e("SpeechUtility init fail  " + e.getMessage());
        }

    }

    /**
     * Whether is user login
     * <p>
     * 用户是否登录
     *
     * @return true：yes false：no
     */
    public boolean isLogin() {
        IUserPlugin userPlugin = PluginManager.get().getUserPlugin();
        if (userPlugin == null) {
            return false;
        }
        return userPlugin.isLogin();
    }

    /**
     * User login
     * <P></>
     * 用户登录
     */
    public void login() {
        IUserPlugin userPlugin = PluginManager.get().getUserPlugin();
        if (userPlugin != null) {
            userPlugin.login(new IUserPlugin.ILoginCallBack() {
                @Override
                public void onLoginSuccess(String token) {
                    if (getView() != null) {
                        getView().onLoginBack(true);
                    }
                }

                @Override
                public void onLoginFailed(int code) {
                    if (getView() != null) {
                        getView().onLoginBack(false);
                    }
                }
            });
        }
    }

}
