package com.meishe.myvideoapp.activity;

import android.annotation.SuppressLint;
import android.content.DialogInterface;
import android.net.http.SslError;
import android.os.Build;
import android.os.Bundle;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.SslErrorHandler;
import android.webkit.WebChromeClient;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.widget.Toolbar;

import com.meishe.base.model.BaseActivity;
import com.meishe.base.view.LollipopFixedWebView;
import com.meishe.myvideoapp.R;

import static com.meishe.logic.constant.PagerConstants.URL;

/**
 * 网页加载页面
 * WebView page
 */
public class WebViewActivity extends BaseActivity {
    private LollipopFixedWebView mWebView;
    private String mUrl;
    private Toolbar mToolbar;
    private TextView mmTvTitle;

    @Override
    protected void onResume() {
        if (mWebView != null) {
            mWebView.onResume();
        }
        super.onResume();
    }

    @Override
    protected void onPause() {
        if (mWebView != null) {
            mWebView.onPause();
        }
        super.onPause();
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.webview_refresh, menu);
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        if (item.getItemId() == R.id.webView_refresh) {
            if (isFastClick()) {
                return true;
            }
            if ((mWebView != null)) {
                mWebView.reload();
            }
        }
        return true;
    }

    @Override
    protected int bindLayout() {
        return R.layout.activity_web_view;
    }

    @Override
    protected void initData(Bundle savedInstanceState) {
        if (getIntent() != null && getIntent().getExtras() != null) {
            mUrl = getIntent().getExtras().getString(URL);
        }
    }

    @Override
    protected void initView() {
        mToolbar = findViewById(R.id.toolbar);
        mWebView = findViewById(R.id.web_view);
        /*
         * Above KITKAT need set this.Or it has a TimeoutException here.
         */
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            mWebView.setLayerType(View.LAYER_TYPE_SOFTWARE, null);
        }
        mmTvTitle = findViewById(R.id.tv_page_title);
        mToolbar.setTitle("");
        setSupportActionBar(mToolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        }
        mToolbar.setNavigationIcon(R.drawable.main_webview_back);
        initWebSetting();
        initListener();
    }

    @SuppressLint("SetJavaScriptEnabled")
    private void initWebSetting() {
        WebSettings settings = mWebView.getSettings();
        settings.setDomStorageEnabled(true);
        /*
         * 设置允许加载混合内容
         * Setting to allow mixed content to load
         * */
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            settings.setMixedContentMode(WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);
        }
        /*
         * 解决一些图片加载问题
         * Solve some image loading problems
         * */
        settings.setJavaScriptEnabled(true);//android 4.4一下会有问题
        settings.setBlockNetworkImage(false);
        /*
         * 断网情况下加载本地缓存
         * Load local cache in case of network disconnection
         * */
        settings.setCacheMode(WebSettings.LOAD_CACHE_ELSE_NETWORK);
        /*
         * 让WebView支持DOM storage API
         * Make WebView support DOM storage API
         * */
        settings.setDomStorageEnabled(true);
        settings.setSaveFormData(false);
    }

    protected void initListener() {
        // toolbar
        mToolbar.setNavigationOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
        mWebView.setWebChromeClient(new WebChromeClient() {
            @Override
            public void onReceivedTitle(WebView view, String title) {
                super.onReceivedTitle(view, title);
                if (mmTvTitle != null) {
                    mmTvTitle.setText(title);
                }
            }
        });
        mWebView.setWebViewClient(new WebViewClient() {
            @Override
            public boolean shouldOverrideUrlLoading(WebView view, String url) {
                view.loadUrl(url);
                return true;
            }

            @Override
            public void onReceivedSslError(WebView view, final SslErrorHandler handler, SslError error) {
                AlertDialog.Builder builder = new AlertDialog.Builder(view.getContext());
                builder.setMessage(R.string.ssl_error_prompt);
                builder.setPositiveButton(R.string.confirm, new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        handler.proceed();
                    }
                });

                builder.setNegativeButton(R.string.activity_cut_export_template_cancel, new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        handler.cancel();
                    }
                });
                AlertDialog dialog = builder.create();
                dialog.show();
            }
        });
    }

    @Override
    protected void requestData() {
        mWebView.loadUrl(mUrl);
    }

    @Override
    protected void onDestroy() {
        if (mWebView != null) {
            mWebView.loadDataWithBaseURL(null, "", "text/html", "utf-8", null);
            mWebView.setTag(null);
            mWebView.clearHistory();
            ((ViewGroup) mWebView.getParent()).removeView(mWebView);
            mWebView.destroy();
            mWebView = null;
        }
        super.onDestroy();
    }

    private long lastClickTime;

    /**
     * 判断是否快速点击，避免点击方法短时间多次触发
     * Determining whether to click quickly, to avoid the click method in a short time triggered many times
     *
     * @return
     */
    public boolean isFastClick() {
        boolean flag = true;
        long currentClickTime = System.currentTimeMillis();
        if ((currentClickTime - lastClickTime) >= 1000) {
            flag = false;
        }
        lastClickTime = currentClickTime;
        return flag;
    }
}
