package com.meishe.myvideoapp.activity;

import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import com.meishe.base.model.BaseMvpActivity;
import com.meishe.base.bean.MediaData;
import com.meishe.base.utils.BarUtils;
import com.meishe.base.utils.ImageLoader;
import com.meishe.base.utils.Utils;
import com.meishe.myvideo.activity.MaterialSingleSelectActivity;
import com.meishe.myvideoapp.R;
import com.meishe.base.manager.AppManager;
import com.meishe.myvideoapp.activity.iview.FeedbackView;
import com.meishe.myvideoapp.activity.presenter.FeedbackPresenter;

import androidx.annotation.Nullable;

import static com.meishe.logic.constant.PagerConstants.BUNDLE_DATA;
import static com.meishe.logic.constant.PagerConstants.MEDIA_TYPE;
import static com.meishe.logic.constant.PagerConstants.REQUEST_CODE_1;


/**
 * 意见反馈页面
 * Feedback page
 */
public class FeedBackActivity extends BaseMvpActivity<FeedbackPresenter> implements FeedbackView, View.OnClickListener {

    private EditText mEtFeedbackContent, mEtContactWay;
    private ImageView mIvSelectPicture;
    private TextView mTvSubmit;
    private String mPicturePath;
    private ImageView mIvBack;
    private ImageLoader.Options mOptions;

    @Override
    protected int bindLayout() {
        return R.layout.activity_feed_back;
    }

    @Override
    protected void initData(Bundle savedInstanceState) {
        mOptions = new ImageLoader.Options()
                .centerCrop()
                .placeholder(R.mipmap.icon_feed_back_pic)
                .dontAnimate();
    }

    @Override
    protected void initView() {
        BarUtils.setStatusBarColor(this, getResources().getColor(R.color.menu_bg));
        mIvBack = findViewById(R.id.iv_back);
        mEtFeedbackContent = findViewById(R.id.et_feed_back_content);
        mEtContactWay = findViewById(R.id.et_contact_way);
        mIvSelectPicture = findViewById(R.id.iv_selected_picture);
        mTvSubmit = findViewById(R.id.tv_submit);

        initListener();
    }

    private void initListener() {
        mIvBack.setOnClickListener(this);
        mTvSubmit.setOnClickListener(this);
        mIvSelectPicture.setOnClickListener(this);
        mEtFeedbackContent.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                if (s == null) {
                    return;
                }
                if (TextUtils.isEmpty(s.toString())) {
                    mTvSubmit.setClickable(false);
                    mTvSubmit.setBackgroundResource(R.drawable.feed_back_commit_bg);
                } else {
                    mTvSubmit.setClickable(true);
                    mTvSubmit.setBackgroundResource(R.drawable.feed_back_commit_bg_clickable);
                }
            }

            @Override
            public void afterTextChanged(Editable s) {

            }
        });
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.iv_back) {
            onBackPressed();
        } else if (id == R.id.iv_selected_picture) {
            if (Utils.isFastClick()) {
                return;
            }
            Bundle bundle = new Bundle();
            bundle.putInt(MEDIA_TYPE, MediaData.TYPE_PHOTO);
            AppManager.getInstance().jumpActivityForResult(this, MaterialSingleSelectActivity.class, bundle, REQUEST_CODE_1);
        } else if (id == R.id.tv_submit) {
            Editable feedback = mEtFeedbackContent.getText();
            Editable contact = mEtContactWay.getText();
            String feedbackContent = feedback == null ? "" : feedback.toString();
            String contactWay = contact == null ? "" : contact.toString();
            mPresenter.feedback(feedbackContent, contactWay, mPicturePath);
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == REQUEST_CODE_1 && data != null) {
            MediaData mediaData = data.getParcelableExtra(BUNDLE_DATA);
            if (mediaData != null && !TextUtils.isEmpty(mediaData.getPath())) {
                mPicturePath = mediaData.getPath();
                ImageLoader.loadUrl(this, mediaData.getPath(), mIvSelectPicture, mOptions);
            }
        }
    }


    @Override
    public void onFeedbackSuccess() {
        finish();
    }
}
