package com.meishe.myvideoapp.activity.iview;

import com.meishe.base.model.IBaseView;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> LiHangZ<PERSON>
 * @CreateDate :2020/12/3 9:53
 * @Description : 首页view  the view of main page
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public interface MainView extends IBaseView {
    /**
     * Callback of login
     * <p>
     * 登录返回callback
     * @param isLogin 是否登录成功 Whether is login successful.
     */
    void onLoginBack(boolean isLogin);
}
