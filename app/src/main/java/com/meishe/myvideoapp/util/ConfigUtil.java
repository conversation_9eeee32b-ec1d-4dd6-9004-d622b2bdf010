package com.meishe.myvideoapp.util;

import com.meishe.base.utils.LogUtils;
import com.meishe.base.utils.Utils;
import com.meishe.engine.asset.AssetsManager;
import com.meishe.myvideoapp.BuildConfig;

import java.lang.reflect.Field;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2021/3/22 10:17
 * @Description :
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class ConfigUtil {
    public static final String BUILD_TYPE_TOB = "2B";
    public static final String BUILD_TYPE_TOC = "2C";

    public static boolean isToC() {
        String className = Utils.getApp().getPackageName() + ".BuildConfig";
        try {
            Class<?> aClass = Class.forName(className);
            Field flavor = aClass.getField("FLAVOR");
            Object obj = flavor.get(null);
            return (obj instanceof String && ((String)obj).contains(BUILD_TYPE_TOC));
        } catch (Exception e) {
            LogUtils.e(e);
        }
        return true;
    }

    public static boolean hasLog() {
        return "true".equals(BuildConfig.HAS_LOG);
    }

    public static String getLicPath() {
        return isToC() ? "assets:/meishesdk.lic" : "assets:/meishesdk_asset.lic";
    }

    public static boolean isNewAssets() {
        return AssetsManager.IS_NEW_ASSETS;
    }

    /**
     * Need convert boolean.
     * 是否需要转码的开关
     * @return the boolean
     */
    public static boolean needConvert(){
        return true;
    }
}
