package com.meishe.myvideoapp.utils;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.provider.Settings;

import androidx.annotation.NonNull;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;

import com.meishe.base.utils.PermissionConstants;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Custom Permission Helper to handle Android permissions properly
 * Handles the maxSdkVersion attribute issue by directly checking permissions
 */
public class CustomPermissionHelper {
    
    private static final int REQUEST_CODE_PERMISSION = 1000;
    
    /**
     * Interface for permission callbacks
     */
    public interface PermissionCallback {
        /**
         * Called when all requested permissions are granted
         */
        void onAllPermissionsGranted();
        
        /**
         * Called when some permissions are denied
         * @param deniedPermissions List of denied permissions
         * @param permanentlyDenied List of permanently denied permissions (user clicked "never ask again")
         */
        void onPermissionsDenied(List<String> deniedPermissions, List<String> permanentlyDenied);
    }
    
    private Fragment fragment;
    private Activity activity;
    private String[] permissions;
    private PermissionCallback callback;
    
    /**
     * Create a new instance for a Fragment
     * @param fragment The fragment requesting permissions
     */
    public CustomPermissionHelper(Fragment fragment) {
        this.fragment = fragment;
        this.activity = null;
    }
    
    /**
     * Create a new instance for an Activity
     * @param activity The activity requesting permissions
     */
    public CustomPermissionHelper(Activity activity) {
        this.activity = activity;
        this.fragment = null;
    }
    
    /**
     * Set the permissions to request
     * @param permissionGroups Permission groups from PermissionConstants
     * @return This instance for chaining
     */
    public CustomPermissionHelper withPermissions(@PermissionConstants.Permission String... permissionGroups) {
        List<String> permissionList = new ArrayList<>();
        
        for (String group : permissionGroups) {
            String[] groupPermissions = PermissionConstants.getPermissions(group);
            permissionList.addAll(Arrays.asList(groupPermissions));
        }
        
        this.permissions = permissionList.toArray(new String[0]);
        return this;
    }
    
    /**
     * Set the callback for permission results
     * @param callback The callback
     * @return This instance for chaining
     */
    public CustomPermissionHelper withCallback(PermissionCallback callback) {
        this.callback = callback;
        return this;
    }
    
    /**
     * Check if all permissions are granted
     * @return true if all permissions are granted, false otherwise
     */
    public boolean areAllPermissionsGranted() {
        if (permissions == null || permissions.length == 0) {
            return true;
        }
        
        Context context = getContext();
        if (context == null) return false;
        
        // For Android 6.0 and above, check permissions at runtime
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            for (String permission : permissions) {
                // Special handling for storage permissions with maxSdkVersion="22"
                if (isStoragePermission(permission) && Build.VERSION.SDK_INT > 22) {
                    // Check if we have the appropriate storage permission based on Android version
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                        // For Android 13+, check media permissions
                        if (!hasMediaPermissions(context)) {
                            return false;
                        }
                    } else {
                        // For Android 6-12, check storage permissions directly
                        if (ContextCompat.checkSelfPermission(context, permission) != PackageManager.PERMISSION_GRANTED) {
                            return false;
                        }
                    }
                } 
                // For all other permissions, check normally
                else if (ContextCompat.checkSelfPermission(context, permission) != PackageManager.PERMISSION_GRANTED) {
                    return false;
                }
            }
            return true;
        } else {
            // For Android 5.1 and below, permissions are granted at install time
            return true;
        }
    }
    
    /**
     * Check if we have the media permissions for Android 13+
     */
    private boolean hasMediaPermissions(Context context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            return ContextCompat.checkSelfPermission(context, Manifest.permission.READ_MEDIA_IMAGES) == PackageManager.PERMISSION_GRANTED
                    && ContextCompat.checkSelfPermission(context, Manifest.permission.READ_MEDIA_VIDEO) == PackageManager.PERMISSION_GRANTED
                    && ContextCompat.checkSelfPermission(context, Manifest.permission.READ_MEDIA_AUDIO) == PackageManager.PERMISSION_GRANTED;
        }
        return true;
    }
    
    /**
     * Check if a permission is a storage permission
     */
    private boolean isStoragePermission(String permission) {
        return Manifest.permission.READ_EXTERNAL_STORAGE.equals(permission) 
                || Manifest.permission.WRITE_EXTERNAL_STORAGE.equals(permission);
    }
    
    /**
     * Request the permissions
     */
    public void request() {
        if (areAllPermissionsGranted()) {
            if (callback != null) {
                callback.onAllPermissionsGranted();
            }
            return;
        }
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (fragment != null) {
                fragment.requestPermissions(permissions, REQUEST_CODE_PERMISSION);
            } else if (activity != null) {
                ActivityCompat.requestPermissions(activity, permissions, REQUEST_CODE_PERMISSION);
            }
        } else {
            // For Android 5.1 and below, permissions are granted at install time
            if (callback != null) {
                callback.onAllPermissionsGranted();
            }
        }
    }
    
    /**
     * Handle the permission request result
     * This should be called from onRequestPermissionsResult in the fragment or activity
     */
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        if (requestCode != REQUEST_CODE_PERMISSION || callback == null) {
            return;
        }
        
        List<String> deniedPermissions = new ArrayList<>();
        List<String> permanentlyDeniedPermissions = new ArrayList<>();
        
        for (int i = 0; i < permissions.length; i++) {
            if (grantResults[i] != PackageManager.PERMISSION_GRANTED) {
                deniedPermissions.add(permissions[i]);
                
                // Check if permission is permanently denied
                boolean showRationale = false;
                if (fragment != null) {
                    showRationale = fragment.shouldShowRequestPermissionRationale(permissions[i]);
                } else if (activity != null) {
                    showRationale = ActivityCompat.shouldShowRequestPermissionRationale(activity, permissions[i]);
                }
                
                if (!showRationale) {
                    permanentlyDeniedPermissions.add(permissions[i]);
                }
            }
        }
        
        if (deniedPermissions.isEmpty()) {
            callback.onAllPermissionsGranted();
        } else {
            callback.onPermissionsDenied(deniedPermissions, permanentlyDeniedPermissions);
        }
    }
    
    /**
     * Open app settings to allow the user to grant permissions
     */
    public void openAppSettings() {
        Context context = getContext();
        if (context == null) return;
        
        Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
        Uri uri = Uri.fromParts("package", context.getPackageName(), null);
        intent.setData(uri);
        if (fragment != null) {
            fragment.startActivity(intent);
        } else if (activity != null) {
            activity.startActivity(intent);
        }
    }
    
    private Context getContext() {
        if (fragment != null) {
            return fragment.getContext();
        } else if (activity != null) {
            return activity;
        }
        return null;
    }
}
