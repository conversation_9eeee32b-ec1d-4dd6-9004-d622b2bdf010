package com.meishe.myvideoapp.view.pop;

import android.content.Context;
import android.os.Bundle;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.TextPaint;
import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.text.style.ClickableSpan;
import android.text.style.ForegroundColorSpan;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.meishe.base.manager.AppManager;
import com.meishe.base.utils.Utils;
import com.meishe.logic.constant.PagerConstants;
import com.meishe.logic.manager.PreferencesManager;
import com.meishe.myvideoapp.R;
import com.meishe.myvideoapp.activity.WebViewActivity;
import com.meishe.myvideoapp.util.NetConstants;
import com.meishe.third.pop.XPopup;
import com.meishe.third.pop.core.CenterPopupView;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.meishe.base.utils.StringUtils.getString;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> LiHangZhou
 * @CreateDate :2020/12/3 19:51
 * @Description :隐私条款弹窗 Privacy Policy Pop
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class PrivacyPolicyPop extends CenterPopupView {
    /*
     * 《服务协议》及《隐私政策》中文匹配，?表示尽量少匹配
     * "Service Agreement" and "Privacy Policy" are matched in Chinese,? Means as few matches as possible
     * */
    private static final String PRIVACY_MATCH_RULE_CH = "《.*?》";
    /*
     * 《服务协议》及《隐私政策》英文匹配，?表示尽量少匹配
     * "Service Agreement" and "Privacy Policy" are matched in English,? Means as few matches as possible
     * */
    private static final String PRIVACY_MATCH_RULE_EN = "\".*?\"";
    private TextView mTvStatement;

    public PrivacyPolicyPop(@NonNull Context context) {
        super(context);
    }

    public static PrivacyPolicyPop create(Context context, EventListener listener) {
        return (PrivacyPolicyPop) new XPopup.Builder(context)
                .dismissOnTouchOutside(false)
                .dismissOnBackPressed(false)
                .asCustom(new PrivacyPolicyPop(context)
                        .setEventListener(listener));
    }

    @Override
    protected int getImplLayoutId() {
        return R.layout.dialog_privacy_policy;
    }

    @Override
    protected void onCreate() {
        super.onCreate();
        mTvStatement = findViewById(R.id.tv_statement);
        TextView tvDisagree = findViewById(R.id.tv_disagree);
        tvDisagree.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                PreferencesManager.get().setAgreePrivacy(false);
                //System.exit(0);
                dismiss();
            }
        });
        TextView tvAgree = findViewById(R.id.tv_agree);
        tvAgree.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                PreferencesManager.get().setAgreePrivacy(true);
                if (mListener != null) {
                    mListener.onConfirm();
                }
                dismiss();
            }
        });
        initData();
    }

    private void initData() {
        String statementContent = getResources().getString(R.string.statement_content);
        final SpannableStringBuilder contentStyle = new SpannableStringBuilder();
        contentStyle.append(statementContent);
        String regex = Utils.isZh() ? PRIVACY_MATCH_RULE_CH : PRIVACY_MATCH_RULE_EN;
        //正则表达式，匹配搜索关键字
        //Regular expressions, matching search keywords
        Pattern p = Pattern.compile(regex);
        Matcher matcher = p.matcher(statementContent);
        while (matcher.find()) {
            int startIndex = matcher.start();
            int endIndex = matcher.end();
            final String groupContent = matcher.group();
            //设置部分文字点击事件
            final ClickableSpan clickableSpan = new ClickableSpan() {
                @Override
                public void onClick(@NonNull View widget) {
                    String serviceAgreement = getString(R.string.service_agreement);
                    String privacyPolicy = getString(R.string.privacy_policy);
                    String visitUrl = "";
                    if (groupContent.contains(serviceAgreement)) {
                        if (Utils.isZh()) {
                            visitUrl = NetConstants.USER_AGREEMENTS;
                        } else {
                            visitUrl = NetConstants.USER_AGREEMENTS_EN;
                        }
                    } else if (groupContent.contains(privacyPolicy)) {
                        if (Utils.isZh()) {
                            visitUrl = NetConstants.PRIVACY_POLICY_URL;
                        } else {
                            visitUrl = NetConstants.PRIVACY_POLICY_URL_EN;
                        }

                    }
                    if (TextUtils.isEmpty(visitUrl)) {
                        return;
                    }
                    Bundle bundle = new Bundle();
                    bundle.putString(PagerConstants.URL, visitUrl);
                    AppManager.getInstance().jumpActivity(getContext(), WebViewActivity.class, bundle);
                }

                @Override
                public void updateDrawState(TextPaint ds) {
                    ds.setUnderlineText(false);//去掉下划线
                }
            };
            contentStyle.setSpan(clickableSpan, startIndex, endIndex, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            //设置部分文字颜色
            ForegroundColorSpan foregroundColorSpan = new ForegroundColorSpan(getResources().getColor(R.color.color_red_fc2b));
            contentStyle.setSpan(foregroundColorSpan, startIndex, endIndex, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        }
        mTvStatement.setMovementMethod(LinkMovementMethod.getInstance());//实现点击事件
        mTvStatement.setText(contentStyle);
        mTvStatement.setHighlightColor(getResources().getColor(R.color.colorTranslucent));//去掉点击背景色
    }

    private EventListener mListener;

    /**
     * 设置事件监听
     * Set event listener
     *
     * @param listener EventListener the listener
     */
    public PrivacyPolicyPop setEventListener(EventListener listener) {
        mListener = listener;
        return this;
    }

    public interface EventListener {
        /**
         * 确定
         * Confirm
         */
        void onConfirm();
    }
}
