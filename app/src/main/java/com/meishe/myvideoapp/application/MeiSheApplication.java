package com.meishe.myvideoapp.application;

import android.app.Activity;
import android.content.Context;
import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.meishe.base.manager.AppManager;
import com.meishe.base.model.BaseApplication;
import com.meishe.base.utils.LogUtils;
import com.meishe.base.utils.PermissionUtils;
import com.meishe.base.utils.UtilsTransActivity;
import com.meishe.logic.manager.PreferencesManager;
import com.meishe.myvideo.activity.MaterialSingleSelectActivity;
import com.meishe.myvideoapp.activity.FeedBackActivity;
import com.meishe.myvideoapp.activity.MainActivity;
import com.meishe.myvideoapp.activity.SettingActivity;
import com.meishe.myvideoapp.activity.WebViewActivity;

import static com.meishe.base.utils.PermissionConstants.LOCATION;
import static com.meishe.base.utils.PermissionConstants.STORAGE;
import static com.meishe.logic.constant.PagerConstants.COMMON_NEED_GO_MAIN;

/**
 * The type Mei she application.
 * 此类为 美摄权限
 */
public class MeiSheApplication extends BaseApplication {

    private static MeiSheApplication mApplication = null;
    private static Context mContext;

    /**
     * Gets context.
     * 获取上下文
     *
     * @return the context
     */
    public static Context getContext() {
        return mContext;
    }

    @Override
    public void initApplication(boolean isMainProcess) {
        if (isMainProcess) {
            mApplication = this;
            mContext = getApplicationContext();
            initActivityLifecycleCallbacks();
            PreferencesManager.get().init(this);
        }
    }


    /**
     * Gets instance.
     * 获取实例
     *
     * @return the instance
     */
    public static MeiSheApplication getInstance() {
        return mApplication;
    }


    /**
     * 监听所有activity生命周期的回调
     */
    private void initActivityLifecycleCallbacks() {
        registerActivityLifecycleCallbacks(new ActivityLifecycleCallbacks() { //添加监听
            @Override
            public void onActivityCreated(Activity activity, Bundle savedInstanceState) {
                //activity创建生命周期
                LogUtils.d("onActivityCreated:" + activity.getLocalClassName());
            }

            @Override
            public void onActivityStarted(Activity activity) {
                //activity启动生命周期

            }

            @Override
            public void onActivityResumed(Activity activity) {
                //activity恢复生命周期
                Class<? extends Activity> aClass = activity.getClass();
                String canonicalName = aClass.getCanonicalName();
                if (canonicalName.equals(UtilsTransActivity.class.getCanonicalName())) {
                    return;
                }
                if (canonicalName.equals(WebViewActivity.class.getCanonicalName())) {
                    return;
                }
                if (canonicalName.equals(SettingActivity.class.getCanonicalName())) {
                    return;
                }

                if (canonicalName.equals(MaterialSingleSelectActivity.class.getCanonicalName())) {
                    if (!PermissionUtils.isGroupGranted(STORAGE)) {
                        AppManager.getInstance().backToActivity(MainActivity.class);
                    }
                    return;
                }

                if (canonicalName.equals(FeedBackActivity.class.getCanonicalName())) {
                    if (!PermissionUtils.isGroupGranted(STORAGE)) {
                        AppManager.getInstance().backToActivity(MainActivity.class);
                    }
                    return;
                }

                if (!PermissionUtils.isGroupGranted(STORAGE, LOCATION)) {
                    MainActivity.hasStoragePermission = false;
                    if (!canonicalName.equals(MainActivity.class.getCanonicalName())) {
                        AppManager.getInstance().backToActivity(MainActivity.class);
                    }
                } else {
                    MainActivity.hasStoragePermission = true;
                }
            }

            @Override
            public void onActivityPaused(Activity activity) {
                //activity暂停生命周期

            }

            @Override
            public void onActivityStopped(Activity activity) {
                //activity停止生命周期

            }

            @Override
            public void onActivityPostCreated(@NonNull Activity activity, @Nullable Bundle savedInstanceState) {
                LogUtils.d("onActivityPostCreated:" + activity.getLocalClassName());
                if (savedInstanceState != null) {
                    if (savedInstanceState.getBoolean(COMMON_NEED_GO_MAIN, false)) {
                        AppManager.getInstance().backToActivity(MainActivity.class);
                    }
                }
            }

            @Override
            public void onActivitySaveInstanceState(Activity activity, Bundle outState) {
                LogUtils.d("onActivitySaveInstanceState:" + activity.getLocalClassName());
                //保存activity实例状态
                Class<? extends Activity> aClass = activity.getClass();
                if (aClass.getCanonicalName().equals("com.meishe.myvideo.activity.MaterialPreviewActivity")
                        || aClass.getCanonicalName().equals("com.meishe.myvideo.activity.MaterialSelectActivity")) {
                    outState.putBoolean(COMMON_NEED_GO_MAIN, true);
                }
            }

            @Override
            public void onActivityDestroyed(Activity activity) {
                //activity销毁生命周期

            }
        });
    }

}
