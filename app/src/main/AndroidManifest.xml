<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.meishe.myvideoapp"
    android:installLocation="auto">

    <uses-permission android:name="android.permission.RECORD_AUDIO" /> <!-- 用于录音 -->
    <uses-permission android:name="android.permission.ACCESS_NOTIFICATION_POLICY" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <!--    googleplay上线问题，增加权限-->
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.BODY_SENSORS" />

    <!--讯飞权限 -->
    <!--连接网络权限，用于执行云端语音能力 -->
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE"/>

    <!--配置权限，用来记录应用配置信息 -->
    <!--手机定位信息，用来为语义等功能提供定位，提供更精准的服务-->
    <!--定位信息是敏感信息，可通过Setting.setLocationEnable(false)关闭定位请求 -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"/>
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO"/>
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES"/>
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO"/>
    <uses-permission android:name="android.permission.NEARBY_WIFI_DEVICES"/>

   <!-- <uses-permission
        android:name="android.permission.WRITE_SETTINGS"
        tools:ignore="ProtectedPermissions" />-->
    <!--讯飞权限 -->
    <!--阿里云权限-->
    <uses-permission
        android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS"
        tools:ignore="ProtectedPermissions" />
    <application
        android:name="com.meishe.myvideoapp.application.MeiSheApplication"
        android:allowBackup="false"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:networkSecurityConfig="@xml/network_security_config"
        android:requestLegacyExternalStorage="true"
        android:preserveLegacyExternalStorage="true"
        android:roundIcon="@mipmap/ic_launcher"
        android:supportsRtl="false"
        android:theme="@style/AppTheme"
        tools:ignore="GoogleAppIndexingWarning"
        tools:replace="android:supportsRtl,android:allowBackup">
        <activity
            android:name="com.meishe.myvideoapp.activity.SettingActivity"
            android:screenOrientation="portrait"
            android:theme="@style/TranslucentFullScreenTheme"
            tools:ignore="LockedOrientationActivity" />
        <activity
            android:name="com.meishe.myvideoapp.activity.FeedBackActivity"
            android:screenOrientation="portrait"
            android:theme="@style/TranslucentFullScreenTheme"
            tools:ignore="LockedOrientationActivity" />
        <activity
            android:name="com.meishe.myvideoapp.activity.WebViewActivity"
            android:screenOrientation="portrait"
            android:theme="@style/BaseAnimationTheme"
            tools:ignore="LockedOrientationActivity" />
        <activity
            android:name="com.meishe.myvideoapp.activity.MainActivity"
            android:screenOrientation="portrait"
            android:theme="@style/BaseAnimationTheme"
            android:exported="true"
            tools:ignore="LockedOrientationActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>

            <intent-filter>
                <action android:name="com.meishe.myvideo.action.MAIN" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>

        </activity>

        <!--<activity
            android:name="com.tencent.bugly.beta.ui.BetaActivity"
            android:configChanges="keyboardHidden|orientation|screenSize|locale"
            android:theme="@android:style/Theme.Translucent" />-->


    </application>

</manifest>