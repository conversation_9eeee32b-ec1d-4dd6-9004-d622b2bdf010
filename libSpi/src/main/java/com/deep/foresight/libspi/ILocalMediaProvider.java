package com.deep.foresight.libspi;

import com.meishe.base.bean.MediaData;

import java.util.List;

/**
 * Interface for accessing local media files.
 *
 * This interface defines a contract for classes that provide access to media files
 * stored on the local device.
 */
public interface ILocalMediaProvider extends IBusinessDataProvider {
    /**
     * Get local media files
     * @return List of media data or null if not available
     */
    List<MediaData> getLocalMedia();
}