apply plugin: 'com.android.library'

android {
    compileSdkVersion rootProject.android.extCompileSdkVersion
    buildToolsVersion rootProject.android.extBuildToolsVersion

    defaultConfig {
        minSdkVersion rootProject.android.extMinSdkVersion
        targetSdkVersion rootProject.android.extTargetSdkVersion
        versionCode rootProject.config.extVersionCode
        versionName rootProject.config.extVersionName


        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

    }

    buildTypes {

        release {
            buildConfigField "String", "HAS_LOG", "\"true\""
            minifyEnabled true
            matchingFallbacks = ['release']
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }

        release_no_log {
            buildConfigField "String", "HAS_LOG", "\"false\""
            minifyEnabled true
            matchingFallbacks = ['release']
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }

        debug {
            buildConfigField "String", "HAS_LOG", "\"true\""
            minifyEnabled false
            signingConfig signingConfigs.debug
            matchingFallbacks = ['debug']
            debuggable true
        }
    }

}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])

    implementation 'androidx.appcompat:appcompat:1.0.2'
    implementation 'androidx.constraintlayout:constraintlayout:1.1.3'
    testImplementation 'junit:junit:4.12'
    androidTestImplementation 'androidx.test:runner:1.1.1'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.1.1'
    implementation 'androidx.recyclerview:recyclerview:1.0.0'

    implementation project(path: ':libLogic')
    implementation project(path: ':libEngine')
    implementation project(path: ':libPlayer')
    implementation project(path: ':libAssetsView')
    implementation project(path: ':editModule')

    implementation 'com.facebook.fresco:fresco:1.12.0'
    // 支持 GIF 动图，需要添加
    implementation 'com.facebook.fresco:animated-gif:1.12.0'
    // 支持 WebP （静态图+动图），需要添加
    implementation 'com.facebook.fresco:animated-webp:1.12.0'
    // 仅支持 WebP 静态图，需要添加
    implementation 'com.facebook.fresco:webpsupport:1.12.0'
    implementation rootProject.ext.dependencies.extBumptechGlide
    implementation rootProject.ext.dependencies.extSupportDesign
    implementation rootProject.ext.dependencies.extOkhttp
    implementation rootProject.ext.dependencies.extSwiperefreshlayout
}
repositories {
    mavenCentral()
}
