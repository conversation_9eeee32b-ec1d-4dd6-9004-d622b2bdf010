package com.meishe.capturemodule.utils;

import android.view.Gravity;

import com.meishe.base.utils.SizeUtils;
import com.meishe.base.utils.ToastUtils;
import com.meishe.capturemodule.R;

/**
 * All rights Reserved, Designed By www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate : 2022/05/05 9:58
 * @Description :拍摄提示工具类 the capture toast Util
 * @Copyright : www.meishesdk.com Inc. All rights reserved.
 */
public class CaptureToastUtil {
    /**
     * 拍摄专用toast
     * <p>
     * Centered color background shows Toast
     *
     * @param s The hint content
     */
    public static void showCaptureToast(String s) {
        ToastUtils.make()
                .setBgResource(R.drawable.bg_toast_capture)
                .setTextSize(SizeUtils.sp2px(13))
                .setGravity(Gravity.CENTER, 0, 0)
                .show(s);
    }

}
