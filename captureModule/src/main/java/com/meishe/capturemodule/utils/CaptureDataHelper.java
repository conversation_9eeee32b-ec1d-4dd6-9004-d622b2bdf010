package com.meishe.capturemodule.utils;

import android.content.Context;
import android.text.TextUtils;

import com.google.gson.reflect.TypeToken;
import com.meicam.sdk.NvsAssetPackageManager;
import com.meicam.sdk.NvsStreamingContext;
import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.FileIOUtils;
import com.meishe.base.utils.GsonUtils;
import com.meishe.capturemodule.R;
import com.meishe.capturemodule.bean.BeautyShapeDataItem;
import com.meishe.capturemodule.makeup.Makeup;
import com.meishe.capturemodule.makeup.MakeupArgs;
import com.meishe.capturemodule.makeup.MakeupEffectContent;
import com.meishe.engine.constant.NvsConstants;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;


/**
 * All rights Reserved, Designed By www.meishesdk.com
 *
 * <AUTHOR> LiuPanFeng
 * @CreateDate : 2021/2/7 9:58
 * @Description :  获取美颜美型数据帮助类;Get Beauty and Beauty Data Helper
 * @Copyright : www.meishesdk.com Inc. All rights reserved.
 */
public class CaptureDataHelper {
    public static final String ASSETS_MAKEUP_PATH = "beauty/makeup";
    private static final String ASSETS_MAKEUP_COMPOSE_PATH = ASSETS_MAKEUP_PATH + "/compose";
    private static final String ASSETS_MAKEUP_COSTOM_PATH = ASSETS_MAKEUP_PATH + "/custom";
    private static final String ASSETS_MAKEUP_COMPOSE_PATH_240 = ASSETS_MAKEUP_PATH + "/compose240";
    private static final String ASSETS_MAKEUP_COSTOM_PATH_240 = ASSETS_MAKEUP_PATH + "/custom240";
    private static final String ASSETS_MAKEUP_RECORD_NAME = "/info.json";

    private static final String SD_MAKEUP_COSTOM_PATH = "makeup/custom";
    private static final String SD_MAKEUP_COMPOSE_PATH = "makeup/compose";

    private static final float DEFAULT_INTENSITY_STRENGTH = 0.6f;
    private static final float DEFAULT_EYE_SIZE_DEGREE = 0.5f;
    private static final float DEFAULT_NOSE_WIDTH_DEGREE = 0.4f;
    private static final float DEFAULT_FACE_SIZE_DEGREE = 0.4f;
    private static final float DEFAULT_FACE_LENGTH_DEGREE = 0.2f;
    private static final float DEFAULT_DARK_CIRCLES = 1f;
    private static final float DEFAULT_BRIGHTEN_EYES = 0.6f;
    private static final float DEFAULT_NASOLABIAL_FOLDS = 1f;
    private static final float DEFAULT_WHITEN_TEETH = 0.6f;
    private static final float DEFAULT_DEFINITION = 0.0f;
    public static final float DEFAULT_MARKUP = 0.6f;
    public static final float DEFAULT_SHARPEN = 0.33f;

    /**
     * @param context
     * @return 美颜数据集合;Beauty Data Collection
     */
    public ArrayList<BeautyShapeDataItem> getBeautyDataListByType(Context context) {
        ArrayList<BeautyShapeDataItem> list = new ArrayList<>();
        /*
         * 磨皮
         * strength
         * */
        BeautyShapeDataItem beauty_strength = new BeautyShapeDataItem();
        beauty_strength.name = context.getResources().getString(R.string.strength);
        beauty_strength.resId = R.drawable.beauty_strength_selector;
        beauty_strength.beautyShapeId = "Advanced Beauty Intensity";
        beauty_strength.strength = DEFAULT_INTENSITY_STRENGTH;
        beauty_strength.defaultStrength = DEFAULT_INTENSITY_STRENGTH;
        beauty_strength.effectType = BeautyShapeDataItem.EFFECT_TYPE_STRENGTH;
        list.add(beauty_strength);
        /*
         * 美型
         * Beauty shape
         */
        list.addAll(getShapeDataList(context));
        /*
         * 单妆
         *Single makeup
         */
        ArrayList<BeautyShapeDataItem> makeupDataListFromAssets = getMakeupDataListFromAssets(context);
        if (!CommonUtils.isEmpty(makeupDataListFromAssets)) {
            list.addAll(makeupDataListFromAssets);
        }
        /*
         * 微整形
         * Small beauty shape
         */
        list.addAll(getSmallShapeDataList(context));


        /*
         * 锐度
         * sharpness
         * */
        BeautyShapeDataItem sharpen = new BeautyShapeDataItem();
        sharpen.name = context.getResources().getString(R.string.sharpness);
        sharpen.resId = R.drawable.beauty_sharpen_selector;
        sharpen.effectType = BeautyShapeDataItem.EFFECT_TYPE_SHARPNESS;
        sharpen.strength = DEFAULT_SHARPEN;
        sharpen.defaultStrength = DEFAULT_SHARPEN;
        sharpen.beautyShapeId = "Amount";
        list.add(sharpen);

        /*
         * 清晰度
         * Definition Intensity
         * */
        BeautyShapeDataItem clarity = new BeautyShapeDataItem();
        clarity.name = context.getResources().getString(R.string.clarity);
        clarity.resId = R.drawable.beauty_clarity_selector;
        clarity.strength = DEFAULT_DEFINITION;
        clarity.defaultStrength = DEFAULT_DEFINITION;
        clarity.effectType = BeautyShapeDataItem.EFFECT_TYPE_DEFINITION;
        clarity.beautyShapeId = "Intensity";
        list.add(clarity);
        return list;
    }


    /**
     * 获取美型数据集
     * Get shape data list
     *
     * @param context The context
     * @return 美型数据集合；Beauty data collection
     */
    public static ArrayList<BeautyShapeDataItem> getShapeDataList(Context context) {
        ArrayList<BeautyShapeDataItem> list = new ArrayList<>();

        /*
         * 大眼
         * Eye enlarging
         * */
        BeautyShapeDataItem eye_enlarging = new BeautyShapeDataItem();
        eye_enlarging.name = context.getResources().getString(R.string.eye_enlarging);
        eye_enlarging.resId = R.drawable.beauty_big_eye_selector;
        eye_enlarging.faceMeshId = NvsConstants.BeautyShapeID.Mesh_Eye_Size;
        eye_enlarging.setFaceMeshPath("assets:/beauty/shapePackage/71C4CF51-09D7-4CB0-9C24-5DE9375220AE.1.facemesh");
        eye_enlarging.licPath = "assets:/beauty/shapePackage/facemesh/71C4CF51-09D7-4CB0-9C24-5DE9375220AE.lic";
        eye_enlarging.faceUUID = NvsConstants.BeautyShapePackageID.Mesh_Eye_Size;
        eye_enlarging.faceDegree = NvsConstants.BeautyShapeDegree.Mesh_Eye_Size;
        eye_enlarging.type = "Default";
        eye_enlarging.beautyShapeId = NvsConstants.BeautyShapeDegree.Mesh_Eye_Size;
        eye_enlarging.strength = DEFAULT_EYE_SIZE_DEGREE;
        eye_enlarging.defaultStrength = DEFAULT_EYE_SIZE_DEGREE;
        eye_enlarging.effectType = BeautyShapeDataItem.EFFECT_TYPE_SHAPE;
        list.add(eye_enlarging);

        /*
         * 瘦脸
         * Thin face
         * */
        BeautyShapeDataItem cheek_thinning = new BeautyShapeDataItem();
        cheek_thinning.name = context.getResources().getString(R.string.cheek_thinning);
        cheek_thinning.resId = R.drawable.beauty_thin_face_selector;
        cheek_thinning.faceMeshId = NvsConstants.BeautyShapeID.Mesh_Face_Size;
        cheek_thinning.setFaceMeshPath("assets:/beauty/shapePackage/63BD3F32-D01B-4755-92D5-0DE361E4045A.1.facemesh");
        cheek_thinning.licPath = "assets:/beauty/shapePackage/facemesh/63BD3F32-D01B-4755-92D5-0DE361E4045A.lic";
        cheek_thinning.faceUUID = NvsConstants.BeautyShapePackageID.Mesh_Eye_Size;
        cheek_thinning.faceDegree = NvsConstants.BeautyShapeDegree.Mesh_Face_Size;
        cheek_thinning.beautyShapeId = NvsConstants.BeautyShapeDegree.Mesh_Face_Size;
        cheek_thinning.strength = DEFAULT_FACE_SIZE_DEGREE;
        cheek_thinning.defaultStrength = DEFAULT_FACE_SIZE_DEGREE;
        cheek_thinning.effectType = BeautyShapeDataItem.EFFECT_TYPE_SHAPE;
        list.add(cheek_thinning);


        /*
         * 瘦鼻
         * Nose width
         * */
        BeautyShapeDataItem intensity_nose = new BeautyShapeDataItem();
        intensity_nose.name = context.getResources().getString(R.string.intensity_nose);
        intensity_nose.resId = R.drawable.beauty_thin_nose_selector;
        intensity_nose.faceMeshId = NvsConstants.BeautyShapeID.Mesh_Nose_Width;
        intensity_nose.setFaceMeshPath("assets:/beauty/shapePackage/8D676A5F-73BD-472B-9312-B6E1EF313A4C.1.facemesh");
        intensity_nose.licPath = "assets:/beauty/shapePackage/facemesh/8D676A5F-73BD-472B-9312-B6E1EF313A4C.lic";
        intensity_nose.faceUUID = NvsConstants.BeautyShapePackageID.Mesh_Nose_Width;
        intensity_nose.faceDegree = NvsConstants.BeautyShapeDegree.Mesh_Nose_Width;
        intensity_nose.beautyShapeId = NvsConstants.BeautyShapeDegree.Mesh_Nose_Width;
        intensity_nose.strength = DEFAULT_NOSE_WIDTH_DEGREE;
        intensity_nose.defaultStrength = DEFAULT_NOSE_WIDTH_DEGREE;
        intensity_nose.effectType = BeautyShapeDataItem.EFFECT_TYPE_SHAPE;
        list.add(intensity_nose);

        /*
         * 小脸
         * face length
         * */
        BeautyShapeDataItem beautyShapeDataItem = new BeautyShapeDataItem();
        beautyShapeDataItem.name = context.getResources().getString(R.string.face_small);
        beautyShapeDataItem.resId = R.drawable.beauty_little_face_selector;
        beautyShapeDataItem.faceMeshId = NvsConstants.BeautyShapeID.Mesh_Face_Length;
        beautyShapeDataItem.setFaceMeshPath("assets:/beauty/shapePackage/B85D1520-C60F-4B24-A7B7-6FEB0E737F15.1.facemesh");
        beautyShapeDataItem.licPath = "assets:/beauty/shapePackage/facemesh/B85D1520-C60F-4B24-A7B7-6FEB0E737F15.1.lic";
        beautyShapeDataItem.faceUUID = NvsConstants.BeautyShapePackageID.Mesh_Face_Length;
        beautyShapeDataItem.faceDegree = NvsConstants.BeautyShapeDegree.Mesh_Face_Length;
        beautyShapeDataItem.beautyShapeId = NvsConstants.BeautyShapeDegree.Mesh_Face_Length;
        beautyShapeDataItem.strength = DEFAULT_FACE_LENGTH_DEGREE;
        beautyShapeDataItem.defaultStrength = DEFAULT_FACE_LENGTH_DEGREE;
        beautyShapeDataItem.effectType = BeautyShapeDataItem.EFFECT_TYPE_SHAPE;
        list.add(beautyShapeDataItem);

        return list;
    }


    public ArrayList<BeautyShapeDataItem> getMakeupDataListFromAssets(Context context) {
        String path = ASSETS_MAKEUP_PATH;
        InputStream assetsInput = null;
        try {
            assetsInput = context.getAssets().open(path + ASSETS_MAKEUP_RECORD_NAME);
        } catch (IOException e) {
        }
        if (assetsInput == null) {
            return null;
        }
        String readInfo = FileIOUtils.readFile2String(assetsInput, "utf-8");
        if (TextUtils.isEmpty(readInfo)) {
            return null;
        }
        ArrayList<BeautyShapeDataItem> list = new ArrayList<>();
        ArrayList<Makeup> makeups = GsonUtils.fromJson(readInfo, new TypeToken<List<Makeup>>() {
        }.getType());
        for (Makeup makeup : makeups) {
            BeautyShapeDataItem item = new BeautyShapeDataItem();
            item.resId = R.drawable.beauty_stereo_selector;
            item.name = makeup.getName();
            item.strength = DEFAULT_MARKUP;
            item.defaultStrength = DEFAULT_MARKUP;
            item.effectType = BeautyShapeDataItem.EFFECT_TYPE_MAKEUP;
            item.setMakeup(makeup);
            MakeupEffectContent content = makeup.getEffectContent();
            if (content == null) {
                continue;
            }
            List<MakeupArgs> makeupArgs = content.getMakeupArgs();
            if (makeupArgs.isEmpty()) {
                continue;
            }
            for (MakeupArgs args : makeupArgs) {
                if (args == null) {
                    continue;
                }
                if (!TextUtils.isEmpty(args.getMakeupUrl())) {
                    String markUrl = "assets:/" + path + File.separator + args.getMakeupUrl();
                    String markLic = "assets:/" + path + File.separator + args.getLicPath();

                    installMakeupPkg(markUrl, markLic);
                }
            }
            list.add(item);
        }
        return list;
    }

    /**
     * 获取微整形数据
     * Get small shape data list
     *
     * @param context The context
     * @return The data
     */
    public List<BeautyShapeDataItem> getSmallShapeDataList(Context context) {
        List<BeautyShapeDataItem> list = new ArrayList<>();

        /*
         * 亮眼
         * beauty brighten eye
         * */
        BeautyShapeDataItem beauty_brighten = new BeautyShapeDataItem();
        beauty_brighten.name = context.getResources().getString(R.string.beauty_brighten_eye);
        beauty_brighten.resId = R.drawable.beauty_bright_eye_selector;
        beauty_brighten.beautyShapeId = "Advanced Beauty Brighten Eyes Intensity";
        beauty_brighten.strength = DEFAULT_BRIGHTEN_EYES;
        beauty_brighten.defaultStrength = DEFAULT_BRIGHTEN_EYES;
        beauty_brighten.effectType = BeautyShapeDataItem.EFFECT_TYPE_SMALL_SHAPE;
        list.add(beauty_brighten);
        /*
         * 黑眼圈
         * beauty dark circles
         *
         * */
        BeautyShapeDataItem beauty_dark = new BeautyShapeDataItem();
        beauty_dark.name = context.getResources().getString(R.string.beauty_dark_circles);
        beauty_dark.resId = R.drawable.beauty_dark_circles_selector;
        beauty_dark.beautyShapeId = "Advanced Beauty Remove Dark Circles Intensity";
        beauty_dark.strength = DEFAULT_DARK_CIRCLES;
        beauty_dark.defaultStrength = DEFAULT_DARK_CIRCLES;
        beauty_dark.effectType = BeautyShapeDataItem.EFFECT_TYPE_SMALL_SHAPE;
        list.add(beauty_dark);


        /*
         * 法令纹
         * nasolabial folds
         * */
        BeautyShapeDataItem beauty_nasolabial = new BeautyShapeDataItem();
        beauty_nasolabial.name = context.getResources().getString(R.string.beauty_nasolabial);
        beauty_nasolabial.resId = R.drawable.beauty_nasolabial_selector;
        beauty_nasolabial.beautyShapeId = "Advanced Beauty Remove Nasolabial Folds Intensity";
        beauty_nasolabial.strength = DEFAULT_NASOLABIAL_FOLDS;
        beauty_nasolabial.defaultStrength = DEFAULT_NASOLABIAL_FOLDS;
        beauty_nasolabial.effectType = BeautyShapeDataItem.EFFECT_TYPE_SMALL_SHAPE;
        list.add(beauty_nasolabial);


        /*
         * 美牙
         * beauty tooth
         * */
        BeautyShapeDataItem beauty_tooth = new BeautyShapeDataItem();
        beauty_tooth.name = context.getResources().getString(R.string.beauty_tooth);
        beauty_tooth.resId = R.drawable.beauty_tooth_selector;
        beauty_tooth.strength = DEFAULT_WHITEN_TEETH;
        beauty_tooth.defaultStrength = DEFAULT_WHITEN_TEETH;
        beauty_tooth.effectType = BeautyShapeDataItem.EFFECT_TYPE_SMALL_SHAPE;
        beauty_tooth.beautyShapeId = "Advanced Beauty Whiten Teeth Intensity";
        list.add(beauty_tooth);

        return list;
    }

    /**
     * 安装
     * install
     *
     * @param url 安装路径 the package path
     * @param licPath license路径 the license path
     */
    private void installMakeupPkg(String url, String licPath) {
        if (TextUtils.isEmpty(url)) {
            return;
        }
        NvsAssetPackageManager assetPackageManager = NvsStreamingContext.getInstance().getAssetPackageManager();
        assetPackageManager.installAssetPackage(url, licPath, NvsAssetPackageManager.ASSET_PACKAGE_TYPE_MAKEUP, true, new StringBuilder());
    }
}
