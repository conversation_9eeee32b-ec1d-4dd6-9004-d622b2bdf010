package com.meishe.capturemodule.view;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.RectF;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;

import com.meishe.base.utils.SizeUtils;
import com.meishe.capturemodule.R;


/**
 * All rights Reserved, Designed By www.meishesdk.com
 *
 * <AUTHOR> LiuPanFeng
 * @CreateDate : 2021/2/19 14:16
 * @Description : 纵向滑动的seekbar Seekbar sliding vertically
 * @Copyright : www.meishesdk.com Inc. All rights reserved.
 */
public class VerticalSeekBar extends View {
    private Context context;
    private int height;
    private int width;
    private Paint paint;
    private int maxProgress = 100;
    private int progress = 50;

    /**
     * The M thumb.
     */
    protected Bitmap mThumb;
    private int intrinsicHeight;
    private int intrinsicWidth;
    private boolean isInnerClick;

    private int locationX;
    private int locationY = -1;

    private int mInnerProgressWidth = 4;
    private int mInnerProgressWidthPx;

    private int unSelectColor = 0xcc888888;
    private RectF mDestRect;

    /**
     * 设置未选中的颜色
     * Set unchecked colors
     *
     * @param uNSelectColor the u n select color
     */
    public void setUnSelectColor(int uNSelectColor) {
        this.unSelectColor = uNSelectColor;
    }

    private int selectColor = 0xaa0980ED;

    /**
     * 设置选中线条的颜色
     * Set the color of selected lines
     *
     * @param selectColor the select color
     */
    public void setSelectColor(int selectColor) {
        this.selectColor = selectColor;
    }

    /**
     * 设置进度条的宽度 单位是px
     * Set the width of the progress bar in px
     *
     * @param mInnerProgressWidthPx the m inner progress width px
     */
    public void setmInnerProgressWidthPx(int mInnerProgressWidthPx) {
        this.mInnerProgressWidthPx = mInnerProgressWidthPx;
    }

    /**
     * 设置进度条的宽度 ，单位是dp;默认是4dp
     * Set the width of the progress bar, the unit is dp; the default is 4dp
     *
     * @param mInnerProgressWidth the m inner progress width
     */
    public void setmInnerProgressWidthDp(int mInnerProgressWidth) {
        this.mInnerProgressWidth = mInnerProgressWidth;
        mInnerProgressWidthPx = mInnerProgressWidth;
    }

    /**
     * 设置图片
     * Set picture
     *
     * @param id the id
     */
    public void setThumb(int id) {

        mThumb = BitmapFactory.decodeResource(getResources(), id);
        intrinsicHeight = mThumb.getHeight();
        intrinsicWidth = mThumb.getWidth();
        mDestRect.set(0, 0, intrinsicWidth, intrinsicHeight);
        invalidate();
    }

    /**
     * 设置滑动图片的大小 单位是dp
     * Set the size of the sliding image. The unit is dp.
     *
     * @param width  the width
     * @param height the height
     */
    public void setThumbSizeDp(int width, int height) {
        setThumbSizePx(SizeUtils.dp2px(width), SizeUtils.dp2px(height));
    }

    /**
     * 设置滑动图片的大小 单位是px
     * Set the size of the sliding image. The unit is px.
     *
     * @param width  the width
     * @param height the height
     */
    public void setThumbSizePx(int width, int height) {
        intrinsicHeight = width;
        intrinsicWidth = height;
        mDestRect.set(0, 0, width, height);
        invalidate();
    }

    /**
     * Instantiates a new Vertical seek bar.
     *
     * @param context the context
     */
    public VerticalSeekBar(Context context) {
        super(context);
        init(context, null, 0);
    }

    /**
     * Instantiates a new Vertical seek bar.
     *
     * @param context the context
     * @param attrs   the attrs
     */
    public VerticalSeekBar(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context, attrs, 0);
    }

    /**
     * Instantiates a new Vertical seek bar.
     *
     * @param context      the context
     * @param attrs        the attrs
     * @param defStyleAttr the def style attr
     */
    public VerticalSeekBar(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs, defStyleAttr);
    }

    private void init(Context context, AttributeSet attrs, int defStyleAttr) {
        this.context = context;
        paint = new Paint();
        mThumb = BitmapFactory.decodeResource(getResources(), R.mipmap.seek_thumb_blue);
        intrinsicHeight = mThumb.getHeight();
        intrinsicWidth = mThumb.getWidth();
        mDestRect = new RectF(0, 0, intrinsicWidth, intrinsicHeight);
        mInnerProgressWidthPx = SizeUtils.dp2px(mInnerProgressWidth);
    }


    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        height = getMeasuredHeight();
        width = getMeasuredWidth();
        if (locationY == -1) {
            locationX = width / 2;
            locationY = height / 2;
        }

    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                //判断点击点是否在圈圈上
                // Determine if the click point is on the circle
                isInnerClick = isInnerMthum(event);
                if (isInnerClick) {
                    if (listener != null) {
                        listener.onStart(this, progress);
                    }
                }
                break;
            case MotionEvent.ACTION_MOVE:
                if (isInnerClick) {
                    locationY = (int) event.getY();
                    fixLocationY();
                    progress = (int) (maxProgress - (locationY - intrinsicHeight * 0.5) / (height - intrinsicHeight) * maxProgress);
                    if (listener != null) {
                        listener.onProgress(this, progress);
                    }
                    invalidate();
                }
                break;
            case MotionEvent.ACTION_UP:
                if (isInnerClick) {
                    if (listener != null) {
                        listener.onStop(this, progress);
                    }
                }
                break;
        }
        return true;
    }

    private void fixLocationY() {
        if (locationY <= intrinsicHeight / 2) {
            locationY = intrinsicHeight / 2;
        } else if (locationY >= height - intrinsicHeight / 2) {
            locationY = height - intrinsicHeight / 2;
        }
    }

    /**
     * 是否点击了图片
     * Whether the picture was clicked
     */
    private boolean isInnerMthum(MotionEvent event) {
        return event.getX() >= width / 2.0f - intrinsicWidth / 2.0f && event.getX() <= width / 2.0f + intrinsicWidth / 2.0f && event.getY() >= locationY - intrinsicHeight / 2.0f && event.getY() <= locationY + intrinsicHeight / 2.0f;
    }

    @Override
    protected void onDraw(Canvas canvas) {
        locationY = (int) (intrinsicHeight * 0.5f + (maxProgress - progress) * (height - intrinsicHeight) / maxProgress);
        if (locationY > mDestRect.height()) {
            paint.setColor(unSelectColor);
            canvas.drawRoundRect(width / 2.0f - mInnerProgressWidthPx / 2.0f, mDestRect.height() / 2, width / 2.0f + mInnerProgressWidthPx / 2.0f, locationY - mDestRect.height() / 2,2,2, paint);
        }
        if (locationY + mDestRect.height() < height) {
            paint.setColor(selectColor);
            canvas.drawRoundRect(width / 2.0f - mInnerProgressWidthPx / 2.0f, locationY + mDestRect.height() / 2, width / 2.0f + mInnerProgressWidthPx / 2.0f, height - mDestRect.height() / 2,2,2, paint);
        }
        canvas.save();
        canvas.translate(width * 1f / 2 - mDestRect.width() / 2, locationY - mDestRect.height() / 2.0f);
        paint.reset();
        canvas.drawBitmap(mThumb, null, mDestRect, paint);
        canvas.restore();
        super.onDraw(canvas);
    }

    /**
     * Sets progress.
     *
     * @param progress the progress
     */
    public void setProgress(int progress) {
        if (height == 0) {
            height = getMeasuredHeight();
        }
        this.progress = progress;
        invalidate();
    }

    /**
     * Gets progress.
     *
     * @return the progress
     */
    public int getProgress() {
        return progress;
    }

    @Override
    protected void onDetachedFromWindow() {
        /*if (mThumb != null) {
            mThumb.recycle();
        }*/
        super.onDetachedFromWindow();
    }


    /**
     * Sets max progress.
     *
     * @param maxProgress the max progress
     */
    public void setMaxProgress(int maxProgress) {
        this.maxProgress = maxProgress;
    }

    /**
     * Gets max progress.
     *
     * @return the max progress
     */
    public int getMaxProgress() {
        return maxProgress;
    }

    private SlideChangeListener listener;

    /**
     * Sets on slide change listener.
     *
     * @param l the l
     */
    public void setOnSlideChangeListener(SlideChangeListener l) {
        this.listener = l;
    }

    /**
     * Gets location y.
     *
     * @return the location y
     */
    public int getLocationY() {
        return locationY;
    }

    /**
     * 添加监听接口
     * Add listening interface
     */
    public interface SlideChangeListener {
        /**
         * On start.
         *
         * @param slideView the slide view
         * @param progress  the progress
         */
        void onStart(VerticalSeekBar slideView, int progress);

        /**
         * On progress.
         *
         * @param slideView the slide view
         * @param progress  the progress
         */
        void onProgress(VerticalSeekBar slideView, int progress);

        /**
         * On stop.
         *
         * @param slideView the slide view
         * @param progress  the progress
         */
        void onStop(VerticalSeekBar slideView, int progress);
    }
}
