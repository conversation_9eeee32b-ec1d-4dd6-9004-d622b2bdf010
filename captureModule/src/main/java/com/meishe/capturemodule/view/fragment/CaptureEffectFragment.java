package com.meishe.capturemodule.view.fragment;


import android.content.Context;
import android.graphics.PointF;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.facebook.drawee.backends.pipeline.Fresco;
import com.facebook.drawee.backends.pipeline.PipelineDraweeControllerBuilder;
import com.facebook.drawee.controller.AbstractDraweeController;
import com.facebook.drawee.drawable.ScalingUtils;
import com.facebook.drawee.generic.GenericDraweeHierarchy;
import com.facebook.drawee.generic.GenericDraweeHierarchyBuilder;
import com.facebook.drawee.generic.RoundingParams;
import com.facebook.drawee.interfaces.DraweeController;
import com.facebook.drawee.view.SimpleDraweeView;
import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.ImageLoader;
import com.meishe.base.utils.SizeUtils;
import com.meishe.business.assets.AssetUtils;
import com.meishe.business.assets.fragment.FlowFragment;
import com.meishe.business.assets.fragment.adapter.CommonAdapter;
import com.meishe.business.assets.iview.AssetsView;
import com.meishe.business.assets.presenter.FlowPresenter;
import com.meishe.capturemodule.R;
import com.meishe.engine.asset.bean.AssetInfo;
import com.meishe.engine.asset.bean.RequestParam;
import com.meishe.engine.interf.IBaseInfo;
import com.meishe.logic.constant.PagerConstants;
import com.meishe.third.adpater.BaseViewHolder;

/**
 * 特效fragment
 * EffectFragment
 */
public class CaptureEffectFragment extends FlowFragment<FlowPresenter> implements AssetsView {

    private static final String SELECT_UUID = "select_uuid";

    private OnStateChangedListener mOnItemClickedListener;

    public CaptureEffectFragment() {
        leftItemDecoration = SizeUtils.dp2px(10);
        topItemDecoration = SizeUtils.dp2px(20);
    }


    public static CaptureEffectFragment create(RequestParam param, String selectedId, OnStateChangedListener listener) {
        CaptureEffectFragment fragment = new CaptureEffectFragment();
        fragment.mOnItemClickedListener = listener;
        Bundle bundle = new Bundle();
        if (param != null) {
            bundle.putInt(PagerConstants.BUNDLE_KEY_ASSET_TYPE, param.type);
            bundle.putInt(PagerConstants.BUNDLE_KEY_ASSET_TYPE_NEW, param.type);
            bundle.putInt(PagerConstants.BUNDLE_KEY_ASSET_TYPE_CATEGORY, param.categoryId);
            bundle.putInt(PagerConstants.BUNDLE_KEY_ASSET_TYPE_KIND, param.kind);
            bundle.putString(SELECT_UUID, selectedId);
        }
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected void initData() {
        super.initData();
        Bundle arguments = getArguments();
        if (arguments != null) {
            setSelected(arguments.getString(SELECT_UUID));
        }
    }

    @Override
    protected int getItemLayoutResId() {
        return 0;
    }

    @Override
    protected void onAdapterItemClick(int position) {
        if (mOnItemClickedListener != null) {
            mOnItemClickedListener.onItemClicked(mAdapter.getItem(position));
        }
    }

    @Override
    protected CommonAdapter getAdapter() {
        return new EffectAdapter(getContext());
    }


    private static class EffectAdapter extends CommonAdapter {
        private final PipelineDraweeControllerBuilder mPipelineBuilder;
        private final ImageLoader.Options mOptions;
        private EffectAdapter(Context context) {
            super(R.layout.capture_effect_item);
            mContext = context;
            mPipelineBuilder = Fresco.newDraweeControllerBuilder();
            mOptions = new ImageLoader.Options()
                    .centerCrop()
                    .dontAnimate();
        }

        protected void loadImage(ImageView ivCover, String coverPath) {
            if (ivCover instanceof SimpleDraweeView
                    && !TextUtils.isEmpty(coverPath) && coverPath.startsWith("http")) {
                RoundingParams roundingParams = new RoundingParams();
                roundingParams.setCornersRadius(6);
                PointF pf = new PointF(0.5f, 0.5f);
                GenericDraweeHierarchyBuilder builder =
                        new GenericDraweeHierarchyBuilder(mContext.getResources());
                GenericDraweeHierarchy hierarchy = builder.setActualImageScaleType(ScalingUtils.ScaleType.CENTER_CROP)
                        .setActualImageFocusPoint(pf)
                        .setRoundingParams(roundingParams).build();
                ((SimpleDraweeView) ivCover).setHierarchy(hierarchy);
                DraweeController controller = Fresco.newDraweeControllerBuilder()
                        .setUri(coverPath)
                        .setAutoPlayAnimations(true)
                        .setOldController(((SimpleDraweeView) ivCover).getController())
                        .build();
                ((SimpleDraweeView) ivCover).setController(controller);
            } else {
                ImageLoader.loadUrl(mContext, coverPath, ivCover);
            }
        }

        @Override
        protected void convert(@NonNull BaseViewHolder helper, AssetInfo item) {
            SimpleDraweeView ivCover = helper.getView(R.id.iv_cover);
            TextView tvAssetPurchased = helper.getView(R.id.tv_asset_purchased);
            if (AssetUtils.assetPurchasedVisible(tvAssetPurchased, mAssetSubType, item.isAuthorized())) {
                tvAssetPurchased.setVisibility(View.VISIBLE);
            } else {
                if (tvAssetPurchased != null) {
                    tvAssetPurchased.setVisibility(View.GONE);
                }
            }
            String coverPath = item.getCoverPath();
            if (!TextUtils.isEmpty(coverPath) && coverPath.startsWith("http")) {
                AbstractDraweeController controller = mPipelineBuilder
                        .setUri(coverPath)
                        .setOldController(ivCover.getController())
                        .setAutoPlayAnimations(true)
                        .build();
                ivCover.setController(controller);
            } else {
                ImageLoader.loadUrl(mContext, coverPath, ivCover, mOptions);
            }
            if (helper.getAdapterPosition() == mSelectedPosition) {
                ivCover.setBackground(CommonUtils.getRadiusDrawable(3, mContext.getResources().getColor(R.color.color_fffc2b55), 6, -1));
            } else {
                ivCover.setBackgroundResource(0);
            }
            helper.setText(R.id.tv_name, item.getName());
            ImageView ivDownload = helper.getView(R.id.iv_downloading);
            if (!item.isHadDownloaded() || item.needUpdate()) {
                ImageLoader.loadUrl(mContext, R.mipmap.downloading, ivDownload);
                int progress = item.getDownloadProgress();
                if (progress >= 0 && progress < 100) {
                    ivDownload.setVisibility(View.VISIBLE);
                    ivCover.setVisibility(View.GONE);
                } else {
                    ivDownload.setVisibility(View.GONE);
                    ivCover.setVisibility(View.VISIBLE);
                }
            } else {
                ivDownload.setVisibility(View.GONE);
                ivCover.setVisibility(View.VISIBLE);
            }
        }
    }

    public interface OnStateChangedListener {
        /**
         * On item clicked.
         *
         * @param baseInfo the base info
         */
        void onItemClicked(IBaseInfo baseInfo);

        void onDismiss();
    }
}
