package com.meishe.capturemodule.view;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Rect;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;

import androidx.annotation.Nullable;

import com.meishe.base.utils.SizeUtils;
import com.meishe.capturemodule.R;


public class MagicProgress extends View {
    /**
     * 滚动条背景色
     * The progress background color
     */
    private int progressBackGround;
    /**
     * 滚动条宽度
     * The progress width
     */
    private int progressWidth;
    /**
     * 滚动条颜色
     * The progress forward color
     */
    private int progressForwardGround;
    /**
     * 指示器背景色
     * The thumb color
     */
    private int thumbColor;
    /**
     * 吸附点颜色
     * Adsorption point color
     */
    private int pointColor;

    /**
     * 指示器半径
     * The thumb radios
     */
    private int thumbRadios;

    /**
     * 圆点半径
     * Adsorption point radios
     */
    private int pointRadios;

    /**
     * 最大值
     * max
     */
    private int max;

    /**
     * 最小值
     * min
     */
    private int min;

    /**
     * 当前值
     * Current progress
     */
    private int progress;

    /**
     * 吸附点位置
     * Adsorption point progress
     */
    private int pointProgress;

    /**
     * 距离吸附点多远吸附
     * The range of adsorption point
     */
    private int pointRange;

    /**
     * 是否有显示断点功能
     * Whether there is a breakpoint display function
     */
    private boolean pointEnable = true;

    /**
     * 是否有吸附功能
     * Whether  it has adsorption function
     */
    private boolean isAuto = true;

    /**
     * 布局宽
     * View width
     */
    private int viewWidth;

    /**
     * 布局高
     * View height
     */
    private int viewHeight;
    private Paint backGroundPaint;

    /**
     * 断点画笔
     * The paint for break point
     */
    private Paint pointPaint;

    /**
     * 滚动条画笔
     * The paint for progress
     */
    private Paint progressPaint;

    /**
     * 指示器画笔
     * The paint for thumb
     */
    private Paint thumbPaint;

    /**
     * 当前指示器的坐标x
     * Current x of thumb
     */
    private float thumbX;

    /**
     * 当前指示器的坐标y
     * Current y of thumb
     */
    private float thumbY;

    /**
     * 是否可以移动指示器
     * Whether the indicator can be moved.
     */
    private boolean canMoveThumb = false;

    /**
     * 是否可以显示文字
     * Whether the text can be showed.
     */
    private boolean showTextEnable = true;

    /**
     * 是否有折中值
     * Whether shows the break progress
     */
    private int breakProgress = 100;
    private Paint textPaint;
    private float textSize;
    private int textColor;
    private float textSpace;
    private float paddingSpace;
    private float progressLength;

    /**
     * 显示字体透明度
     * The text alpha
     */
    private int textAlpha = 0;
    private int shadowColor;
    private float shadowRadios;

    /**
     *调节偏移的触摸偏移量（按压灵敏度）
     * The value  for adjusting the touch offset for offset (press sensitivity)
     */
    private float touchRatio = 2;

    public void setPointEnable(boolean pointEnable) {
        this.pointEnable = pointEnable;
        setAuto(pointEnable);
    }

    public void setBreakProgress(int breakProgress) {
        this.breakProgress = breakProgress;
    }

    /**
     * 调节按压灵敏度
     * Set touch ratio
     *
     * @param touchRatio 触摸半径
     */
    public void setTouchRatio(float touchRatio) {
        this.touchRatio = touchRatio;
    }

    public void setShowTextEnable(boolean showTextEnable) {
        this.showTextEnable = showTextEnable;
    }

    private OnProgressChangeListener listener;

    public void setOnProgressChangeListener(OnProgressChangeListener listener) {
        this.listener = listener;
    }

    public void setMax(int max) {
        this.max = max;
    }

    public boolean isAuto() {
        return isAuto;
    }

    public void setAuto(boolean auto) {
        isAuto = auto;
    }

    public MagicProgress(Context context) {
        this(context, null);
    }

    public MagicProgress(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public MagicProgress(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initAttr(context, attrs);
        initData();
    }

    private void initAttr(Context context, AttributeSet attrs) {
        TypedArray array = context.obtainStyledAttributes(attrs, R.styleable.MagicProgress);
        progressBackGround = array.getColor(R.styleable.MagicProgress_progressBackGround, Color.parseColor("#1Affffff"));
        progressWidth = array.getDimensionPixelSize(R.styleable.MagicProgress_progressWidthMagic, SizeUtils.dp2px(2));
        progressForwardGround = array.getColor(R.styleable.MagicProgress_progressBackGround, Color.parseColor("#ffffffff"));
        thumbColor = array.getColor(R.styleable.MagicProgress_progressBackGround, Color.parseColor("#ffffffff"));
        pointColor = array.getColor(R.styleable.MagicProgress_progressBackGround, Color.parseColor("#ffffffff"));
        thumbRadios = array.getDimensionPixelSize(R.styleable.MagicProgress_progressWidthMagic, SizeUtils.dp2px(8));
        pointRadios = array.getDimensionPixelSize(R.styleable.MagicProgress_progressWidthMagic, SizeUtils.dp2px(3));
        max = array.getInteger(R.styleable.MagicProgress_max, 100);
        min = array.getInteger(R.styleable.MagicProgress_min, 0);
        progress = array.getInteger(R.styleable.MagicProgress_edit_progress, 50);
        pointProgress = array.getInteger(R.styleable.MagicProgress_pointProgress, 80);
        pointRange = array.getInteger(R.styleable.MagicProgress_pointRange, 3);
        textSize = array.getDimension(R.styleable.MagicProgress_textSizeMagic, 22);
        textColor = array.getColor(R.styleable.MagicProgress_textColorMagic, Color.parseColor("#ffffff"));
        textSpace = SizeUtils.dp2px( 5);
        paddingSpace = SizeUtils.dp2px(5);
        shadowColor = array.getColor(R.styleable.MagicProgress_shadowColor, Color.parseColor("#aaaaaa"));
        if (progress < min) {
            progress = min;
        }
        if (progress > max) {
            progress = max;
        }
        if (pointProgress < min || pointProgress > max) {
            pointEnable = false;
        }
        array.recycle();
    }

    /**
     * 是否支持渐隐动画
     * Whether it supports fading animation
     */
    boolean animalAlpha = false;

    private void initData() {
        //初始化背景画图 Initialize background
        backGroundPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        backGroundPaint.setStrokeCap(Paint.Cap.ROUND);
        backGroundPaint.setStyle(Paint.Style.STROKE);
        backGroundPaint.setAntiAlias(true);
        backGroundPaint.setColor(progressBackGround);
        backGroundPaint.setStrokeWidth(progressWidth);
        backGroundPaint.setShadowLayer(20, 0, 0, shadowColor);
        //初始化小圆点 Initialize point
        pointPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        pointPaint.setStyle(Paint.Style.FILL);
        pointPaint.setColor(pointColor);
        pointPaint.setAntiAlias(true);
        pointPaint.setShadowLayer(2, 0, 0, shadowColor);
        //初始化progress Initialize progress
        progressPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        progressPaint.setColor(progressForwardGround);
        progressPaint.setAntiAlias(true);
        progressPaint.setStrokeWidth(progressWidth);
        //初始化Thumb Initialize thumb
        thumbPaint = new Paint();
        thumbPaint.setColor(thumbColor);
        thumbPaint.setStyle(Paint.Style.FILL);
        thumbPaint.setAntiAlias(true);
        setLayerType(LAYER_TYPE_SOFTWARE, null);
        thumbPaint.setShadowLayer(2, 0, 0, shadowColor);
        //数字显示 Show text
        textPaint = new Paint();
        textPaint.setTextSize(textSize);
        textPaint.setColor(textColor);
        textPaint.setStrokeWidth(2f);
        textPaint.setAntiAlias(true);
        textPaint.setStyle(Paint.Style.FILL);
        textPaint.setShadowLayer(2, 0, 0, shadowColor);
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
    }

    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        if (w > 0 && h > 0) {
            viewWidth = w;
            viewHeight = h;
            thumbY = viewHeight - thumbRadios * 2;
            progressLength = viewWidth - paddingSpace * 2 - thumbRadios * 2;
        }
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        drawBackGround(canvas);
        if (pointEnable) {
            drawPoint(canvas);
        }
        drawProgress(canvas);
        drawThumb(canvas);
        if (showTextEnable) {
            drawText(canvas);
        }
    }

    private void drawText(Canvas canvas) {
        String text = String.valueOf(progress - breakProgress);
        float textWidth = textPaint.measureText(text);
        Rect rect = new Rect();
        if (canMoveThumb) {
            animalAlpha = false;
            textAlpha = 255;
            shadowRadios = 2f;
        }
        if (animalAlpha) {
            textAlpha -= 5;
            shadowRadios = 0;
            if (textAlpha <= 0) {
                shadowRadios = 0;
                textAlpha = 0;
                animalAlpha = false;
            }
            postInvalidateDelayed(5);
        }
        textPaint.setShadowLayer(shadowRadios, 0, 0, shadowColor);
        textPaint.setAlpha(textAlpha);
        textPaint.getTextBounds(text, 0, text.length(), rect);
        float textBoundsHeight = rect.bottom - rect.top;
        float x = (float) progress / (float) (max - min) * progressLength + paddingSpace + thumbRadios - textWidth / 2;
        float y = thumbY - textSpace - textBoundsHeight;
        canvas.drawText(text, x, y, textPaint);

    }

    /**
     * 画指示器
     *
     * Draw thumb
     *
     * @param canvas The canvas
     */
    private void drawThumb(Canvas canvas) {
        thumbX = progress / (float) (max - min) * progressLength + thumbRadios + paddingSpace;
        canvas.drawCircle(thumbX, thumbY, thumbRadios, thumbPaint);
    }

    /**
     * 画进度条
     * Draw progress
     *
     * @param canvas the canvas
     */
    private void drawProgress(Canvas canvas) {
        float stopX = progress / (float) (max - min) * progressLength + thumbRadios + paddingSpace;
        canvas.drawLine(thumbRadios + paddingSpace, thumbY, stopX, thumbY, progressPaint);
    }

    /**
     * 画小圆点
     * Draw rect
     *
     * @param canvas the canvas
     */
    private void drawPoint(Canvas canvas) {
//        if (progress < pointProgress) {
            float x = pointProgress / (float) (max - min) * progressLength + thumbRadios + paddingSpace;
            float y = thumbY;
            canvas.drawCircle(x, y, pointRadios, pointPaint);
//        }
    }

    /**
     * 画背景
     * Draw background
     *
     * @param canvas the canvas
     */
    private void drawBackGround(Canvas canvas) {
        canvas.drawLine(thumbRadios + paddingSpace, thumbY, thumbRadios +
                paddingSpace + progressLength, thumbY, backGroundPaint);
    }


    /**
     * 设置断点圆点的位置点
     * Set point progress
     *
     * @param pointProgress The point progress
     */
    public void setPointProgress(int pointProgress) {
        this.pointProgress = pointProgress;
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                if (touchOnThumb(event.getX(), event.getY())) {
                    canMoveThumb = true;
                    invalidate();
                } else {
                    canMoveThumb = false;
                }
                break;
            case MotionEvent.ACTION_MOVE:
                if (canMoveThumb) {
                    moveThumb(event.getX());
                }
                break;
            case MotionEvent.ACTION_UP:
                if (canMoveThumb) {
                    startAnimal();
                }
                canMoveThumb = false;
                break;
        }
        return true;
    }

    /**
     * 移动指示器
     * Move thumb
     *
     * @param x the x value
     */
    private void moveThumb(float x) {
        float nowX = x - thumbRadios;
        int nowProgress = (int) (((float) (max - min) * (nowX / (float) (viewWidth - 2 * thumbRadios))) + 0.5f);
        if (nowProgress < min) {
            nowProgress = min;
        }
        if (nowProgress > max) {
            nowProgress = max;
        }

        boolean adsorbEnable = false;
        if (isAuto) {
            adsorbEnable = Math.abs(nowProgress - pointProgress) > pointRange ? false : true;
        }
        if (nowProgress != progress) {
            if (adsorbEnable) {
                if (progress == pointProgress) {
                    return;
                }
                progress = pointProgress;
                if (progress < 10 || progress > 90)
                    if (listener != null) {
                        listener.onProgressChange(progress, false);
                    }
            } else {
                progress = nowProgress;
                if (listener != null) {
                    listener.onProgressChange(progress, false);
                }
            }
            invalidate();
        }

    }

    private boolean touchOnThumb(float x, float y) {
        //判断如果在圆点之外 Judge if return false is outside the dot.
        float spaceX = Math.abs(thumbX - x);
        float spaceY = Math.abs(thumbY - y);
        if (spaceX > thumbRadios * touchRatio) {
            return false;
        }
        if (spaceY > thumbRadios * touchRatio) {
            return false;
        }
        return true;
    }

    public void setProgress(int progress) {
        this.progress = progress;
        if (listener != null) {
            listener.onProgressChange(progress, true);
        }
        invalidate();
    }

    public int getProgress() {
        return progress;
    }

    public void setPointProgressByNewMax(int maxProgress) {
        this.pointProgress = (int) ((float) this.pointProgress / (float) this.max * (float) maxProgress);
    }


    /**
     * The interface On progress change listener.
     * 当状态改变时回调
     */
    public interface OnProgressChangeListener {
        /**
         * On progress change.
         *
         * @param progress the progress
         * @param fromUser the from user
         */
        void onProgressChange(int progress, boolean fromUser);
    }

    public void startAnimal() {
        if (animalAlpha) {
            return;
        }
        textAlpha = 255;
        animalAlpha = true;
        postInvalidateDelayed(10);
    }
}
