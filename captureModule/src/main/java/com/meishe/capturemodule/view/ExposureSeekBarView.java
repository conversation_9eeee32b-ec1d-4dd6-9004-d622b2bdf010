package com.meishe.capturemodule.view;

import android.animation.Animator;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.content.Context;
import android.graphics.PointF;
import android.graphics.RectF;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.ViewGroup;
import android.view.animation.AlphaAnimation;
import android.widget.ImageView;
import android.widget.LinearLayout;

import androidx.annotation.Nullable;

import com.meishe.base.utils.ScreenUtils;
import com.meishe.capturemodule.R;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/4/21 13:30
 * @Description :带seekBar的曝光控件 The exposure view with seekbar
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class ExposureSeekBarView extends LinearLayout {

    private static final boolean IS_TEST = false;
    /**
     * 用来设置调节进度条的手势敏感度，数据越大，调节越敏感
     * Used to set the gesture sensitivity of the adjustment progress bar.
     * The larger the data, the more sensitive the adjustment is。
     */
    private static final float FACTOR = 1.5F;

    private final static int ONE_FINGER = 1;
    private final static int TWO_FINGER = 2;
    private boolean mIsTwoFingerEvent = false;
    private VerticalSeekBar mExposeSeekBar;
    private VerticalSeekBar mExposeSeekBarLeft;
    private ImageView mImageAutoFocusRect;
    private AlphaAnimation mFocusAnimation;
    private View mExposureLayout;
    private int mSlop;
    private View mImageExposureIcon, mImageExposureIconLeft;
    private OnSeekBarChangedListener mOnSeekBarChangedListener;
    private int mScreenHeight = ScreenUtils.getScreenHeight();
    private float touchDownX;
    private float touchDownY;
    private int mMaxProgress;
    private PointF mLastScalePointF = new PointF();
    private boolean mTouchAble = true;
    private double mTwoFingerStartLength;
    private float targetX, targetY;
    private boolean mIsLeft;

    private RectF mTouchRectF = new RectF();
    private boolean mTouchInner;
    private View mTestView;

    public void setOnSeekBarChangedListener(OnSeekBarChangedListener listener) {
        this.mOnSeekBarChangedListener = listener;
    }

    public ExposureSeekBarView(Context context) {
        this(context, null);
    }

    public ExposureSeekBarView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public ExposureSeekBarView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initData();
        initView();
    }

    private void initData() {
        mSlop = ViewConfiguration.get(getContext()).getScaledTouchSlop();
        mFocusAnimation = new AlphaAnimation(1f, 0f);
        mFocusAnimation.setDuration(2000);
        mFocusAnimation.setFillAfter(true);
    }

    private void initView() {
        Context context = getContext();
        View rootView = LayoutInflater.from(context).inflate(R.layout.view_exposure_seek_bar_layout, this);
        mExposureLayout = rootView.findViewById(R.id.ll_exposure);
        mImageAutoFocusRect = rootView.findViewById(R.id.iv_focus);
        mImageExposureIcon = rootView.findViewById(R.id.iv_focus_exposure);
        mImageExposureIconLeft = rootView.findViewById(R.id.iv_focus_exposure_left);
        mExposeSeekBar = rootView.findViewById(R.id.sb_exposure_bar);
        mExposeSeekBarLeft = rootView.findViewById(R.id.sb_exposure_bar_left);
        mTestView = rootView.findViewById(R.id.testView);
        initSeekBar(context, mExposeSeekBar);
        initSeekBar(context, mExposeSeekBarLeft);
        mMaxProgress = mExposeSeekBar.getMaxProgress();
    }

    private void initSeekBar(Context context, VerticalSeekBar seekBar) {
        seekBar.setThumb(R.mipmap.capture_exposure_bar);
        seekBar.setSelectColor(context.getResources().getColor(R.color.white));
        seekBar.setUnSelectColor(context.getResources().getColor(R.color.white));
        seekBar.setThumbSizeDp(30, 30);
        seekBar.setmInnerProgressWidthDp(3);
        seekBar.setMaxProgress(100);
        seekBar.setEnabled(false);
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if (!mTouchAble) {
            return super.onTouchEvent(event);
        }
        int pointerCount = event.getPointerCount();
        if (pointerCount > TWO_FINGER) {
            return false;
        }

        if (((event.getAction() & MotionEvent.ACTION_MASK) == MotionEvent.ACTION_DOWN) && (pointerCount == ONE_FINGER)) {
            mIsTwoFingerEvent = false;
        }
        targetX = event.getX();
        targetY = event.getY();
        if (pointerCount == TWO_FINGER) {
            mIsTwoFingerEvent = true;
            twoFingerTouch(event);
        } else {
            oneFingerTouch(event);
        }
        return true;
    }

    private void oneFingerTouch(MotionEvent event) {
        if (mIsTwoFingerEvent) {
            if (event.getAction() == MotionEvent.ACTION_UP) {
                mIsTwoFingerEvent = false;
                if (mOnSeekBarChangedListener != null) {
                    mOnSeekBarChangedListener.onTowFingerUp();
                }
            }
        }
        if (event.getAction() == MotionEvent.ACTION_DOWN) {
            touchDownX = event.getX();
            touchDownY = event.getY();
            mTouchInner = isTouchInner(event.getX(), event.getY());
        } else if (event.getAction() == MotionEvent.ACTION_MOVE) {
            float deltaTrans = touchDownY - event.getY();
            if (Math.abs(touchDownX - event.getX()) > mSlop
                    || Math.abs(deltaTrans) > mSlop) {
                if (mExposureLayout.getVisibility() == VISIBLE) {
                    mExposureLayout.clearAnimation();
                    int middleValue = mMaxProgress / 2;
                    float progress = deltaTrans * mMaxProgress * FACTOR / mScreenHeight + middleValue;
                    if (progress > mMaxProgress) {
                        progress = mMaxProgress;
                    }
                    if (progress < 0) {
                        progress = 0;
                    }
                    if (mIsLeft) {
                        mExposeSeekBarLeft.setProgress((int) progress);
                    } else {
                        mExposeSeekBar.setProgress((int) progress);
                    }
                    showSeekBar();
                    if (mOnSeekBarChangedListener != null) {
                        mOnSeekBarChangedListener.onSeekBarChanged((progress - middleValue) / middleValue);
                    }
                }
            }
        } else if (event.getAction() == MotionEvent.ACTION_UP) {
            if (Math.abs(touchDownX - event.getX()) <= mSlop
                    && Math.abs(touchDownY - event.getY()) <= mSlop && mTouchInner) {
                moveByTouchEvent(event);
                if (mOnSeekBarChangedListener != null) {
                    mOnSeekBarChangedListener.onTouchDown(getExposureRect());
                }
            } else {
                mExposureLayout.setVisibility(INVISIBLE);
            }
        }
    }


    private void twoFingerTouch(MotionEvent event) {
        if ((event.getAction() & MotionEvent.ACTION_MASK) == MotionEvent.ACTION_POINTER_DOWN) {
            mLastScalePointF.set(targetX, targetY);
            float xLen = event.getX(0) - event.getX(1);
            float yLen = event.getY(0) - event.getY(1);
            mTwoFingerStartLength = Math.sqrt((xLen * xLen) + (yLen * yLen));
        } else if ((event.getAction() & MotionEvent.ACTION_MASK) == MotionEvent.ACTION_MOVE) {
            float xLen = event.getX(0) - event.getX(1);
            float yLen = event.getY(0) - event.getY(1);
            double mTwoFingerEndLength = Math.sqrt(xLen * xLen + yLen * yLen);
            if (mOnSeekBarChangedListener != null) {
                float progress = (float) ((mTwoFingerEndLength - mTwoFingerStartLength) * 1f / mScreenHeight);
                mOnSeekBarChangedListener.onTowFingerMoved(progress);
            }
        } else if ((event.getAction() & MotionEvent.ACTION_MASK) == MotionEvent.ACTION_UP) {
            if (mOnSeekBarChangedListener != null) {
                mOnSeekBarChangedListener.onTowFingerUp();
            }
        }
    }

    private void showSeekBar() {
        if (mIsLeft) {
            mExposeSeekBarLeft.setVisibility(VISIBLE);
        } else {
            mExposeSeekBar.setVisibility(VISIBLE);
        }
        mImageExposureIcon.setVisibility(INVISIBLE);
        mImageExposureIconLeft.setVisibility(INVISIBLE);
    }

    private void hideSeekBar() {
        if (mIsLeft) {
            mImageExposureIconLeft.setVisibility(VISIBLE);
            mImageExposureIcon.setVisibility(INVISIBLE);
        } else {
            mImageExposureIcon.setVisibility(VISIBLE);
            mImageExposureIconLeft.setVisibility(INVISIBLE);
        }
        mExposeSeekBar.setVisibility(INVISIBLE);
        mExposeSeekBarLeft.setVisibility(INVISIBLE);
    }

    public RectF getExposureRect() {
        RectF rectFrame = new RectF();
        rectFrame.set(mImageAutoFocusRect.getX(), mImageAutoFocusRect.getY(),
                mImageAutoFocusRect.getX() + mImageAutoFocusRect.getWidth(),
                mImageAutoFocusRect.getY() + mImageAutoFocusRect.getHeight());
        return rectFrame;
    }

    public void moveByTouchEvent(MotionEvent event) {
        float layoutHalfWidth = mExposureLayout.getWidth() / 2F;
        float layoutHalfHeight = mExposureLayout.getHeight() / 2F;
        mIsLeft = event.getX() + layoutHalfWidth > (mTouchRectF.width() + mTouchRectF.left);
        mExposureLayout.setVisibility(VISIBLE);
        mExposureLayout.setX(event.getX() - layoutHalfWidth);
        mExposureLayout.setY(event.getY() - layoutHalfHeight);
        hideSeekBar();
        ObjectAnimator animatorX = ObjectAnimator.ofFloat(mExposureLayout, "scaleX",  1, 0.8F);
        ObjectAnimator animatorY = ObjectAnimator.ofFloat(mExposureLayout, "scaleY",  1, 0.8F);
        ObjectAnimator alpha = ObjectAnimator.ofFloat(mExposureLayout, "alpha",  0,1F);
        AnimatorSet animatorSet = new AnimatorSet();
        animatorSet.setDuration(200);
        animatorSet.playTogether(animatorX, animatorY, alpha);
        animatorSet.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {

            }

            @Override
            public void onAnimationEnd(Animator animation) {
                mExposureLayout.startAnimation(mFocusAnimation);
            }

            @Override
            public void onAnimationCancel(Animator animation) {

            }

            @Override
            public void onAnimationRepeat(Animator animation) {

            }
        });
        animatorSet.start();
    }

    /**
     * Set touch able.
     * 设置是否可点击
     *
     * @param touchAble the touch able
     */
    public void setTouchAble(boolean touchAble) {
        mTouchAble = touchAble;
        if (!mTouchAble && mExposureLayout != null) {
            mExposureLayout.clearAnimation();
            mExposureLayout.setVisibility(INVISIBLE);
        }
    }

    public RectF changeInnerSize(int width, int height, int layoutWidth, int layoutHeight) {
        mTouchRectF.left = (layoutWidth - width) / 2F;
        mTouchRectF.right =  mTouchRectF.left + width;
        mTouchRectF.top =  (layoutHeight - height) / 2F;
        mTouchRectF.bottom = mTouchRectF.top + height;
        if (IS_TEST) {
            mTestView.setVisibility(VISIBLE);
            ViewGroup.LayoutParams layoutParams = mTestView.getLayoutParams();
            layoutParams.width = width;
            layoutParams.height = height;
            mTestView.setLayoutParams(layoutParams);
        }
        return mTouchRectF;
    }

    public RectF getTouchRectF(){
        return mTouchRectF;
    }

    public boolean isTouchInner(float x, float y){
        return x >=  mTouchRectF.left && x <= mTouchRectF.right && y >= mTouchRectF.top && y <= mTouchRectF.bottom;
    }

    public interface OnSeekBarChangedListener {
        /**
         * On seek bar changed.
         *
         * @param progress the progress
         */
        void onSeekBarChanged(float progress);

        /**
         * On touch down.
         *
         * @param rectF the rect f
         */
        void onTouchDown(RectF rectF);


        /**
         * On tow finger move.
         * 双指缩放
         * @param scale the scale
         */
        void onTowFingerMoved(float scale);


        /**
         * On tow finger up.
         * 双手抬起
         */
        void onTowFingerUp();
    }
}
