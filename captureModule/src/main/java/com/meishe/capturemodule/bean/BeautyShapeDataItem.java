package com.meishe.capturemodule.bean;

import com.meishe.capturemodule.makeup.Makeup;

import java.io.Serializable;
import java.util.List;

/**
 * @Class: com.meishe.sdkdemo.capture.BeautyShapeDataItem.java
 * @Time: 2019/3/22 0022 14:18
 * @author: mlj
 * @Description: 拍摄页面美颜美型数据对象类;Beauty data class for capturing video
 */
public class BeautyShapeDataItem implements Serializable {

    /**
     * 磨皮 type strength
     */
    public static final int EFFECT_TYPE_STRENGTH = 0;

    /**
     * 美型 type shape
     */
    public static final int EFFECT_TYPE_SHAPE = 1;

    /**
     * 美妆 type makeup
     */
    public static final int EFFECT_TYPE_MAKEUP = 2;

    /**
     * 微整形 type small shape
     */
    public static final int EFFECT_TYPE_SMALL_SHAPE = 3;
    /**
     * 锐度 type sharpness
     */
    public static final int EFFECT_TYPE_SHARPNESS = 4;
    /**
     * 清晰度 type definition
     */
    public static final int EFFECT_TYPE_DEFINITION = 5;

    private String path;
    public String licPath;
    public String beautyShapeId;
    public double intensity = 0.0d;
    public double thresh = 0.0d;
    public double radius = 0.0d;

    /**
     * 当前强度，真实的数据
     */
    public double strength = 0.0d;
    /**
     * 默认强度，用于reset按钮
     */
    public double defaultStrength = 0.0d;
    public int resId;
    public String name;
    public String type;
    public double defaultValue = 0.0d;
    /**
     * 是否是美型
     */
    public boolean isShape;
    /**
     * 是否是点
     */
    public boolean isPoint;

    public int effectType = 0;

    private boolean visible;
    private String textDefaultColor = "#ff707070";
    private String textSelectColor = "#ff63ABFF";

    /**
     * 最新美妆包 flag  Thew new beauty shape Flag
     */
    public boolean packageShapeFlag = false;
    private String warpPath;
    private String faceMeshPath;
    public String warpId;
    public String faceMeshId;
    public String warpUUID;
    public String faceUUID;
    public String warpDegree;
    public String faceDegree;
    public boolean wrapFlag = false;
    public boolean canReplace = true;

    private Makeup makeup;

    private List<BeautyShapeDataItem> dataItems;

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }


    public List<BeautyShapeDataItem> getDataItems() {
        return dataItems;
    }

    public void setDataItems(List<BeautyShapeDataItem> dataItems) {
        this.dataItems = dataItems;
    }

    public int getResId() {
        return resId;
    }

    public void setResId(int resId) {
        this.resId = resId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Makeup getMakeup() {
        return makeup;
    }

    public void setMakeup(Makeup makeup) {
        this.makeup = makeup;
    }

    public String getTextDefaultColor() {
        return textDefaultColor;
    }

    public String getTextSelectColor() {
        return textSelectColor;
    }

    public void setTextDefaultColor(String textDefaultColor) {
        this.textDefaultColor = textDefaultColor;
    }

    public void setTextSelectColor(String textSelectColor) {
        this.textSelectColor = textSelectColor;
    }

    public String getLicPath() {
        return licPath;
    }

    public void setLicPath(String licPath) {
        this.licPath = licPath;
    }

    public String getFaceMeshPath() {
        return faceMeshPath;
    }

    public void setFaceMeshPath(String faceMeshPath) {
        this.faceMeshPath = faceMeshPath;
        this.packageShapeFlag = true;
        this.wrapFlag = false;
    }

    public String getWarpPath() {
        return warpPath;
    }

    public void setWarpPath(String warpPath) {
        this.packageShapeFlag = false;
        this.wrapFlag = true;
        this.warpPath = warpPath;
    }
}
