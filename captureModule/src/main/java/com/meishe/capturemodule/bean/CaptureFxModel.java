package com.meishe.capturemodule.bean;

import com.meishe.base.utils.LogUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * All rights Reserved, Designed By www.meishesdk.com
 *
 * <AUTHOR> LiuPanFeng
 * @CreateDate : 2021/2/19 14:16
 * @Description : 特效 model
 * @Copyright : www.meishesdk.com Inc. All rights reserved.
 */
public class CaptureFxModel {
    /**
     * 特效类型：滤镜 The filter
     */
    public static final String FX_TYPE_FILTER = "filter";
    /**
     * 特效类型：道具 the prop
     */
    public static final String FX_TYPE_PROP = "prop";

    public static int FXMODE_BUILTIN = 0;
    public static int FXMODE_PACKAGE = 1;

    private Map<String, FxInfo> mFxInfoMap = new HashMap<>();


    public void addFxInfo(String key, FxInfo value){
        mFxInfoMap.put(key, value);
    }

    public FxInfo getFxInfo(String key){
        return mFxInfoMap.get(key);
    }


    public static class FxInfo{

        private int m_fxMode;
        private String m_fxId;
        private float m_fxIntensity;

        public FxInfo() {
            m_fxId = null;
            m_fxMode = FXMODE_BUILTIN;
            m_fxIntensity = 1.0f;
        }

        public void setFxMode(int mode) {
            if (mode != FXMODE_BUILTIN && mode != FXMODE_PACKAGE) {
                LogUtils.e("", "invalid mode data");
                return;
            }
            m_fxMode = mode;
        }

        public int getFxMode() {
            return m_fxMode;
        }

        public void setFxId(String fxId) {
            m_fxId = fxId;
        }

        public String getFxId() {
            return m_fxId;
        }

        public void setFxIntensity(float intensity) {
            m_fxIntensity = intensity;
        }

        public float getFxIntensity() {
            return m_fxIntensity;
        }
    }
}
