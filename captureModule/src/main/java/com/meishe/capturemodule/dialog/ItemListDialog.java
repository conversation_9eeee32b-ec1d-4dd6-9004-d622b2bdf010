package com.meishe.capturemodule.dialog;

import android.content.Context;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.meishe.base.manager.LinearLayoutManagerWrapper;
import com.meishe.capturemodule.R;
import com.meishe.third.adpater.BaseQuickAdapter;
import com.meishe.third.adpater.BaseViewHolder;
import com.meishe.third.pop.XPopup;
import com.meishe.third.pop.core.PositionPopupView;

import java.util.List;
import java.util.Map;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/4/19 16:41
 * @Description :支持多种选项的对话框 Dialog box supporting multiple options.
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class ItemListDialog extends PositionPopupView {
    private int layoutHeight;
    private int layoutWidth;
    private int itemHeight;
    private List<ItemInfo> data;
    private OnItemClickListener mOnItemClickListener;
    private ItemAdapter mItemAdapter;
    private int currentItem = -1;
    private boolean isFrontCamera = false;

    public static class Builder {
        int offX;
        int offY;
        int layoutHeight;
        int itemHeight;
        int layoutWidth;
        int currentItem = -1;

        public Builder setOffX(int offX) {
            this.offX = offX;
            return this;
        }

        public Builder setOffY(int offY) {
            this.offY = offY;
            return this;
        }


        public Builder setItemHeight(int itemHeight) {
            this.itemHeight = itemHeight;
            return this;
        }

        public Builder setLayoutHeight(int layoutHeight) {
            this.layoutHeight = layoutHeight;
            return this;
        }

        public Builder setLayoutWidth(int layoutWidth) {
            this.layoutWidth = layoutWidth;
            return this;
        }

        public Builder setCurrentItem(int currentItem) {
            this.currentItem = currentItem;
            return this;
        }

        public ItemListDialog build(Context context, List<ItemInfo> data, OnItemClickListener listener) {
            return create(context, data, offX, offY, layoutWidth, layoutHeight, itemHeight, currentItem, listener);
        }
    }

    public ItemListDialog(@NonNull Context context) {
        super(context);
    }

    public ItemListDialog setItemHeight(int itemHeight) {
        this.itemHeight = itemHeight;
        return this;
    }

    public ItemListDialog setLayoutHeight(int layoutHeight) {
        this.layoutHeight = layoutHeight;
        return this;
    }

    public ItemListDialog setLayoutWidth(int layoutWidth) {
        this.layoutWidth = layoutWidth;
        return this;
    }

    public ItemListDialog setData(List<ItemInfo> data) {
        this.data = data;
        return this;
    }

    public ItemListDialog setFrontCamera(boolean frontCamera) {
        this.isFrontCamera = frontCamera;
        if (data != null) {
            ItemInfo item = data.get(1);
            if (item != null) {
                if (isFrontCamera) {
                    item.currentIconIndex = 0;
                    item.iconList[0] = R.mipmap.ic_capture_flash_enable;
                } else {
                    item.iconList[0] = R.mipmap.ic_capture_flash_off;
                }
                if (mItemAdapter != null) {
                    mItemAdapter.notifyItemChanged(1);
                }
            }
        }
        return this;
    }

    public ItemListDialog setOnItemClickListener(OnItemClickListener listener) {
        this.mOnItemClickListener = listener;
        return this;
    }

    private static ItemListDialog create(Context context, List<ItemInfo> data,
                                         int offX, int offY, int layoutWidth, int layoutHeight,
                                         int itemHeight, int currentItem,
                                         OnItemClickListener listener) {
        return ((ItemListDialog) new XPopup
                .Builder(context).offsetX(offX).offsetY(offY).isCenterHorizontal(true).hasShadowBg(false)
                .asCustom(new ItemListDialog(context)
                        .setData(data)
                        .setItemHeight(itemHeight)
                        .setLayoutHeight(layoutHeight)
                        .setLayoutWidth(layoutWidth)
                        .setCurrentItem(currentItem)
                        .setOnItemClickListener(listener)));
    }

    public ItemListDialog setCurrentItem(int position) {
        currentItem = position;
        if (mItemAdapter != null) {
            mItemAdapter.selectItem(position);
        }
        return this;
    }

    @Override
    protected void onCreate() {
        super.onCreate();
        RecyclerView recyclerView = findViewById(R.id.rc_list);
        recyclerView.setLayoutManager(new LinearLayoutManagerWrapper(getContext(), LinearLayoutManager.HORIZONTAL, false));
        mItemAdapter = new ItemAdapter();
        mItemAdapter.setNewData(data);
        mItemAdapter.setItemHeight(itemHeight);
        mItemAdapter.setItemWidth(layoutWidth / data.size());
        mItemAdapter.selectItem(currentItem);
        recyclerView.setAdapter(mItemAdapter);
        if (layoutHeight > 0) {
            ViewGroup.LayoutParams layoutParams = recyclerView.getLayoutParams();
            layoutParams.height = layoutHeight;
            layoutParams.width = layoutWidth;
            recyclerView.setLayoutParams(layoutParams);
        }

        mItemAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                ItemInfo item = (ItemInfo) adapter.getItem(position);
                if (position == 1 && isFrontCamera) {
                    //前摄像头 The font camara
                    return;
                }
                item.currentIconIndex++;
                if (item.currentIconIndex >= item.iconList.length) {
                    item.currentIconIndex = 0;
                }
                mItemAdapter.selectItem(position);
                adapter.notifyItemChanged(position);
                //如果点击切换分辨率，闪光灯关闭 If you click to switch resolution, the flash will turn off.
                if (position == 2) {
                    ItemInfo flashItem = (ItemInfo) adapter.getItem(1);
                    if (flashItem != null) {
                        flashItem.currentIconIndex = 0;
                        adapter.notifyItemChanged(1);
                    }
                }

                if (mOnItemClickListener != null) {
                    mOnItemClickListener.onItemClicked(position, item);
                }
            }
        });
    }


    @Override
    public boolean showContextMenuForChild(View originalView) {
        return super.showContextMenuForChild(originalView);
    }

    @Override
    protected int getImplLayoutId() {
        return R.layout.capture_more_view_test;
    }

    public static class ItemInfo {
        public int[] iconList;
        public Object[] tagList;
        public int currentIconIndex;
        public int normalIcon;
        public Map<String, Object> extendParam;

        public ItemInfo(int[] iconId) {
            this.iconList = iconId;
        }
    }

    class ItemAdapter extends BaseQuickAdapter<ItemInfo, BaseViewHolder> {
        private int mItemHeight;
        private int mItemWidth;
        private int mCurrentItem = -1;

        public ItemAdapter() {
            super(R.layout.item_list_dialog);
        }

        public void selectItem(int position) {
            mCurrentItem = position;
            notifyDataSetChanged();
        }

        @Override
        public void onBindViewHolder(@NonNull BaseViewHolder holder, int position) {
            super.onBindViewHolder(holder, position);
            View view = holder.getView(R.id.icon);
            ViewGroup.LayoutParams layoutParams = view.getLayoutParams();
            if (mItemHeight > 0) {
                layoutParams.width = mItemWidth;
                layoutParams.height = mItemHeight;
                view.setLayoutParams(layoutParams);
            }
        }

        @Override
        protected void convertPayloads(@NonNull BaseViewHolder helper, ItemInfo item, @NonNull List<Object> payloads) {
            super.convertPayloads(helper, item, payloads);
            if (payloads.size() < 1) {
                convert(helper, item);
            } else {
                for (Object payload : payloads) {
                    // 刷新倒计时 Refresh countdown.
                    if (payload instanceof Integer && ((int) payload) == 1) {
                        helper.setImageResource(R.id.icon, item.iconList[item.currentIconIndex]);
                    }
                }
            }
        }

        @Override
        protected void convert(@NonNull BaseViewHolder helper, ItemInfo item) {
            ImageView icon = helper.getView(R.id.icon);
            icon.setImageResource(item.iconList[item.currentIconIndex]);
            icon.setEnabled(mCurrentItem == helper.getAdapterPosition());
            icon.setSelected(mCurrentItem == helper.getAdapterPosition());
        }

        public void setItemHeight(int itemHeight) {
            mItemHeight = itemHeight;
        }

        public void setItemWidth(int mItemWidth) {
            this.mItemWidth = mItemWidth;
        }
    }

    public interface OnItemClickListener {
        /**
         * On item clicked.
         *
         * @param position the position
         * @param itemInfo the item info
         */
        void onItemClicked(int position, ItemInfo itemInfo);
    }
}
