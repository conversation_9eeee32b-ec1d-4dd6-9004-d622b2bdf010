package com.meishe.capturemodule.adapter;

import android.graphics.Rect;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;


public class SpaceItemDecoration extends RecyclerView.ItemDecoration{
    private int leftSpace;
    private int rightSpace;

    public SpaceItemDecoration(int left, int right) {
        this.leftSpace = left;
        this.rightSpace = right;
    }

    @Override
    public void getItemOffsets(@NonNull Rect outRect, @NonNull View view, @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
        super.getItemOffsets(outRect, view, parent, state);

        int pos = parent.getChildAdapterPosition(view);
        if(pos != 0) {
            outRect.left = leftSpace;
        } else {
            outRect.left = 0;
        }
        outRect.right = rightSpace;
    }

}