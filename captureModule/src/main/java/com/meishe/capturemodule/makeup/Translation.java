package com.meishe.capturemodule.makeup;

import java.io.Serializable;

public class Translation implements Serializable {
    private String originalText;
    private String targetLanguage;
    private String targetText;

    public String getOriginalText() {
        return originalText;
    }

    public String getTargetLanguage() {
        return targetLanguage;
    }

    public String getTargetText() {
        return targetText;
    }
}
