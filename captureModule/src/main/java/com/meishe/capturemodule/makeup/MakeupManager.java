package com.meishe.capturemodule.makeup;

import android.text.TextUtils;

import java.io.File;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
public class MakeupManager {
    public static final String ASSETS_MAKEUP_PATH = "beauty/makeup";
    /**
     * 整装路径 The compose makeup path
     */
    private static final String ASSETS_MAKEUP_COMPOSE_PATH = ASSETS_MAKEUP_PATH + File.separator + "compose";
    private static final String ASSETS_MAKEUP_COMPOSE_PATH_240 = ASSETS_MAKEUP_PATH + File.separator + "compose240";
    /**
     * 单妆路径 The custom makeup path
     */
    private static final String ASSETS_MAKEUP_COSTOM_PATH = ASSETS_MAKEUP_PATH + File.separator + "custom";
    private static final String ASSETS_MAKEUP_COSTOM_PATH_240 = ASSETS_MAKEUP_PATH + File.separator + "custom240";
    /**
     * 妆容路径 The variable compose makeup path
     */
    private static final String ASSETS_MAKEUP_VARIABLECOMPOSE_PATH = ASSETS_MAKEUP_PATH + File.separator + "variablecompose";
    private static final String ASSETS_MAKEUP_VARIABLECOMPOSE_PATH_240 = ASSETS_MAKEUP_PATH + File.separator + "variablecompose240";
    /**
     * 配置json name The config info path
     */
    private static final String ASSETS_MAKEUP_RECORD_NAME = File.separator + "info.json";
    /**
     * sdcard内置路径 Sdcard built-in path
     */
    private static final String SD_MAKEUP_COSTOM_PATH = "makeup/custom";
    private static final String SD_MAKEUP_COMPOSE_PATH = "makeup/compose";
    private static volatile MakeupManager sMakeupCache;
    /**
     * 记录应用的整妆index Record the overall makeup index of the application
     */
    private int mComposeIndex = 0;
    /**
     * 记录应用的单妆数据 Record the application's single makeup data
     */
    private Map<String, MakeupData> mCustomMakeupArgsMap;


    /**
     * 记录应用的妆容index Record the makeup index of the application
     */
    private int mMakeupIndex = 0;

    /**
     * 滤镜效果 Filter Effects
     */
    private Set<String> mFxSet = new HashSet<>();

    /**
     * 美妆效果中带的美颜，美型
     * The beauty and shape of the makeup effect
     */
    private HashMap<String,String> mFxMap = new HashMap<>();


    /**
     * 记录整妆数据
     * Record makeup data
     */
    private Makeup item;

    /**
     * 记录对单妆参数的调节内容
     * Record the adjustment content of single makeup parameters.
     */
    private HashMap<String, Object> makeupArgs = new HashMap<>();


    private MakeupManager() {
    }

    public static MakeupManager getInstacne() {
        if (sMakeupCache == null) {
            synchronized (MakeupManager.class) {
                if (sMakeupCache == null) {
                    sMakeupCache = new MakeupManager();
                }
            }
        }
        return sMakeupCache;
    }


    public MakeupData getMakeupEffect(String effectId) {
        if (mCustomMakeupArgsMap == null || TextUtils.isEmpty(effectId)) {
            return null;
        }
        return mCustomMakeupArgsMap.get(effectId);
    }

    public void addMakeupEffect(String makeupId, MakeupData data) {
        if (TextUtils.isEmpty(makeupId)) {
            return;
        }
        if (mCustomMakeupArgsMap == null) {
            mCustomMakeupArgsMap = new HashMap<>();
        }
        mCustomMakeupArgsMap.put(makeupId, data);
    }

    public void removeMakeupEffect(String makeupId) {
        if (TextUtils.isEmpty(makeupId) || (mCustomMakeupArgsMap == null)) {
            return;
        }
        mCustomMakeupArgsMap.remove(makeupId);

    }

    public Map<String, MakeupData> getCustomMakeupArgsMap() {
        return mCustomMakeupArgsMap;
    }

    public void clearCustomData() {
        if (mCustomMakeupArgsMap != null) {
            mCustomMakeupArgsMap.clear();
        }
    }

    public int getComposeIndex() {
        return mComposeIndex;
    }

    public void setComposeIndex(int composeIndex) {
        this.mComposeIndex = composeIndex;
    }

    public int getMakeupIndex() {
        return mMakeupIndex;
    }

    public void setMakeupIndex(int makeupIndex) {
        mMakeupIndex = makeupIndex;
    }

    public void clearAllData() {
        mComposeIndex = 0;
        clearCustomData();
        mMakeupIndex = 0;
        clearMapFxData();
        clearMakeupArgs();
        clearData();
        item = null;
    }

    public void putMapFx(String fxName, String value) {
        if (mFxMap!=null){
            mFxMap.put(fxName,value);
        }
    }


    public void removeMapFx(String fxName) {
        if (mFxMap!=null){
            mFxMap.remove(fxName);
        }
    }

    public HashMap<String,String> getMapFxMap() {
        return mFxMap;
    }

    public void clearMapFxData() {
        mFxMap.clear();
    }

    public void putFx(String fxName) {
        if (!mFxSet.contains(fxName)) {
            mFxSet.add(fxName);
        }
    }

    public void removeFx(String fxName) {
        mFxSet.remove(fxName);
    }

    public Set<String> getFxSet() {
        return mFxSet;
    }

    public void clearData() {
        mFxSet.clear();
    }


    public Makeup getItem() {
        return item;
    }

    public void setItem(Makeup item) {
        this.item = item;
    }


    /**
     * 给单妆添加参数
     * Put makeup args
     * @param key the key
     * @param value the value
     */
    public void putMakeupArgs(String key, Object value) {
        makeupArgs.put(key, value);
    }

    public void clearMakeupArgs() {
        if (makeupArgs != null) {
            makeupArgs.clear();
        }
    }

    public HashMap<String, Object> getMakeupArgs() {
        return makeupArgs;
    }


}
