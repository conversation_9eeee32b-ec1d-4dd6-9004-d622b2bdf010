package com.meishe.capturemodule.presenter;

import androidx.fragment.app.Fragment;

import com.meishe.base.utils.CommonUtils;
import com.meishe.business.assets.AssetUtils;
import com.meishe.business.assets.view.MYMultiBottomView;
import com.meishe.capturemodule.view.fragment.CaptureEffectFragment;
import com.meishe.engine.asset.bean.AssetsConstants;
import com.meishe.engine.asset.bean.TabParam;

import java.util.ArrayList;
import java.util.List;

import static com.meishe.business.assets.view.MYMultiBottomView.TYPE_MENU_EFFECT;
import static com.meishe.business.assets.view.MYMultiBottomView.TYPE_MENU_PROP;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> LiHangZhou
 * @CreateDate :2021/1/7 16:47
 * @Description :多类型底部容器显示、刷新帮助类 The MYMultiBottomView Helper
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class MultiBottomHelper {
    private MYMultiBottomView mBottomView;

    public MultiBottomHelper(MYMultiBottomView bottomView) {
        mBottomView = bottomView;
    }

    /**
     * 展示特效列表视图
     * Show effect list view
     */
    public void showEffectView(String selectUuid, final CaptureEffectFragment.OnStateChangedListener listener) {
        List<TabParam> tabList = AssetUtils.getCaptureTabList(mBottomView.getContext(), AssetsConstants.AssetsTypeData.EFFECT_DREAM.type);
        if (!CommonUtils.isEmpty(tabList)) {
            List<Fragment> fragmentList = new ArrayList<>();
            String[] tabs = new String[tabList.size()];
            for (int index = 0; index < tabList.size(); index++) {
                TabParam tabParam = tabList.get(index);
                tabs[index] = tabParam.tabName;
                fragmentList.add(CaptureEffectFragment.create(tabParam.param, selectUuid, listener));
            }
            mBottomView.setOnViewStateListener(new MYMultiBottomView.OnViewStateListener() {
                @Override
                public void onShow() {

                }

                @Override
                public void onHide() {
                    listener.onDismiss();
                }
            });
            mBottomView.showNoConfirm(tabs, fragmentList, 0, TYPE_MENU_EFFECT, true);
        }
    }

    /**
     * 展示特效列表视图
     * Show effect list view
     */
    public void showPropView(String selectUuid, final CaptureEffectFragment.OnStateChangedListener listener) {
        List<TabParam> tabList = AssetUtils.getCaptureTabList(mBottomView.getContext(), AssetsConstants.AssetsTypeData.PROP.type);
        if (!CommonUtils.isEmpty(tabList)) {
            List<Fragment> fragmentList = new ArrayList<>();
            String[] tabs = new String[tabList.size()];
            for (int index = 0; index < tabList.size(); index++) {
                TabParam tabParam = tabList.get(index);
                tabs[index] = tabParam.tabName;
                fragmentList.add(CaptureEffectFragment.create(tabParam.param, selectUuid, listener));
            }
            mBottomView.setOnViewStateListener(new MYMultiBottomView.OnViewStateListener() {
                @Override
                public void onShow() {

                }

                @Override
                public void onHide() {
                    listener.onDismiss();
                }
            });
            mBottomView.showNoConfirm(tabs, fragmentList, 0, TYPE_MENU_PROP, true);
        }
    }

    public void hide() {
        mBottomView.hide();
    }

    public boolean isShow() {
        return mBottomView.isShow();
    }
}
