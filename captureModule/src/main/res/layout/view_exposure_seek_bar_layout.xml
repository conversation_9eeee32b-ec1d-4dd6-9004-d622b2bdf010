<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/ll_exposure"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="invisible"
        android:orientation="horizontal">
        <ImageView
            android:id="@+id/iv_focus_exposure_left"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_width="@dimen/dp_px_90"
            android:layout_height="@dimen/dp_px_90"
            android:layout_marginEnd="@dimen/dp_px_15"
            android:src="@mipmap/capture_exposure_bar"/>
        <com.meishe.capturemodule.view.VerticalSeekBar
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:id="@+id/sb_exposure_bar_left"
            android:layout_width="@dimen/dp_px_90"
            android:layout_height="@dimen/dp_px_672"
            android:layout_marginEnd="@dimen/dp_px_15"
            android:visibility="invisible"/>
        <ImageView
            app:layout_constraintLeft_toRightOf="@+id/sb_exposure_bar_left"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:id="@+id/iv_focus"
            android:layout_width="@dimen/dp_px_225"
            android:layout_height="@dimen/dp_px_225"
            android:contentDescription="@null"
            android:layout_gravity="center_vertical"
            android:src="@mipmap/capture_focus"
            android:scaleType="fitCenter" />
        <ImageView
            android:id="@+id/iv_focus_exposure"
            app:layout_constraintLeft_toRightOf="@+id/iv_focus"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_width="@dimen/dp_px_90"
            android:layout_height="@dimen/dp_px_90"
            android:layout_marginStart="@dimen/dp_px_15"
            android:src="@mipmap/capture_exposure_bar"/>
        <com.meishe.capturemodule.view.VerticalSeekBar
            app:layout_constraintLeft_toRightOf="@+id/iv_focus"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:id="@+id/sb_exposure_bar"
            android:layout_width="@dimen/dp_px_90"
            android:layout_height="@dimen/dp_px_672"
            android:layout_marginStart="@dimen/dp_px_15" />
    </androidx.constraintlayout.widget.ConstraintLayout>
    <View
        android:id="@+id/testView"
        android:background="#44ff0000"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:visibility="invisible"/>
</FrameLayout>