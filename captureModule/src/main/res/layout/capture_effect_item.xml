<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <FrameLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <com.facebook.drawee.view.SimpleDraweeView
            android:id="@+id/iv_cover"
            android:layout_width="@dimen/dp_px_150"
            android:layout_height="@dimen/dp_px_150"
            android:contentDescription="@null"
            android:padding="@dimen/dp_px_3" />

        <TextView
            android:id="@+id/tv_asset_purchased"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end|bottom"
            android:layout_marginEnd="@dimen/asset_purchased_margin_right"
            android:layout_marginRight="@dimen/asset_purchased_margin_right"
            android:layout_marginBottom="@dimen/asset_purchased_margin_right"
            android:background="@drawable/bg_round_corner_66000000_100"
            android:gravity="center"
            android:paddingLeft="@dimen/asset_purchased_padding"
            android:paddingRight="@dimen/asset_purchased_padding"
            android:text="@string/asset_purchased"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_px_27"
            android:visibility="gone" />
    </FrameLayout>


    <ImageView
        android:id="@+id/iv_downloading"
        android:layout_width="@dimen/dp_px_150"
        android:layout_height="@dimen/dp_px_150"
        android:contentDescription="@null"
        android:padding="@dimen/dp_px_3"
        android:visibility="gone" />

    <TextView
        android:id="@+id/tv_name"
        android:layout_width="@dimen/dp_px_150"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/dp_px_177"
        android:ellipsize="end"
        android:gravity="center_horizontal"
        android:maxLines="1"
        android:textColor="@color/color_ffd1d1d1"
        android:textSize="@dimen/sp_px_30" />

</FrameLayout>