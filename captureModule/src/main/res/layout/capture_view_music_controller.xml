<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="@dimen/dp_px_120"
    android:orientation="horizontal"
    android:paddingStart="@dimen/dp_px_36"
    android:background="@drawable/bg_cc505050_8"
    android:paddingEnd="@dimen/dp_px_36">
    <ImageView
        android:id="@+id/iv_select_music"
        android:layout_width="@dimen/dp_px_48"
        android:layout_height="@dimen/dp_px_48"
        android:layout_gravity="center_vertical"
        android:src="@mipmap/ic_capture_add_music"/>
    <com.meishe.myvideo.view.MarqueeTextView
        android:id="@+id/tv_select_music"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/capture_hint_select_music"
        android:textSize="@dimen/dp_px_33"
        android:textColor="@color/white"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="@dimen/dp_px_12"/>

    <ImageView
        android:visibility="gone"
        android:id="@+id/iv_delete_music"
        android:layout_width="@dimen/dp_px_63"
        android:layout_height="@dimen/dp_px_78"
        android:paddingEnd="@dimen/dp_px_15"
        android:paddingTop="@dimen/dp_px_15"
        android:paddingBottom="@dimen/dp_px_15"
        android:layout_marginStart="@dimen/dp_px_36"
        android:layout_gravity="center_vertical"
        android:src="@mipmap/ic_capture_delete_music"/>
</LinearLayout>