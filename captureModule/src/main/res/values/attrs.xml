<?xml version="1.0" encoding="utf-8"?>
<resources>

    <declare-styleable name="CaptureRoundImageView">
        <attr name="x_radius" format="dimension" />
        <attr name="y_radius" format="dimension" />
    </declare-styleable>


    <declare-styleable name="Theme">
        <attr name="customImageViewStyle" format="reference" />
    </declare-styleable>

    <!-- 自定义圆角ImageView -->
    <declare-styleable name="CustomImageView">
        <attr name="circle" format="boolean" />
        <attr name="radius" format="dimension" />
    </declare-styleable>

    <declare-styleable name="CustomSpeedSeekBar">
        <attr name="tickMarkFixed" format="reference" />
    </declare-styleable>

    <declare-styleable name="CustomRoundColorView">
        <attr name="color" format="color" />
    </declare-styleable>

    <declare-styleable name="RoundProgressView">
        <attr name="progressColor" format="color" />
        <attr name="noneProgressColor" format="color" />
        <attr name="progressWidth" format="dimension" />
        <attr name="drawable" format="reference" />
        <attr name="drawableWidth" format="dimension" />
        <attr name="drawableHeight" format="dimension" />
    </declare-styleable>

    <declare-styleable name="CircleBarView">
        <attr name="CBProgressColor" format="color" />
        <attr name="CBNoneProgressColor" format="color" />
    </declare-styleable>

    <declare-styleable name="CircleProgressbar">
        <attr name="strokeWidth" format="dimension" />
        <attr name="radius2" format="dimension" />
        <attr name="textSize" format="dimension" />
        <attr name="ringColor" format="color" />
        <attr name="textColor" format="color" />
        <attr name="ringBgColor" format="color" />
        <attr name="centerText" format="boolean" />
    </declare-styleable>


    <declare-styleable name="RoundColorView">
        <attr name="selectStrokeWidth" format="dimension"/>
        <attr name="selectRoundStrokeColor" format="color"/>
        <attr name="roundTextSize" format="dimension"/>
    </declare-styleable>

    <declare-styleable name="SelectBottomMenu">
        <attr name="item_padding_left" format="dimension"></attr>
        <attr name="item_padding_right" format="dimension"></attr>
    </declare-styleable>

    <declare-styleable name="MagicProgress">
        <attr name="progressBackGround" format="color" />
        <attr name="progressWidthMagic" format="dimension" />
        <attr name="progressForwardGround" format="color" />
        <attr name="thumbColor" format="color" />
        <attr name="pointColor" format="color" />
        <attr name="thumbRadios" format="dimension" />
        <attr name="pointRadios" format="dimension" />
        <attr name="max" format="integer" />
        <attr name="min" format="integer" />
        <attr name="edit_progress" format="integer" />
        <attr name="pointProgress" format="integer" />
        <attr name="pointRange" format="integer" />
        <attr name="textColorMagic" format="color" />
        <attr name="shadowColor" format="color" />
        <attr name="textSizeMagic" format="dimension" />
    </declare-styleable>
    <declare-styleable name="ThemeProgress">
        <attr name="progress2BackGround" format="color" />
        <attr name="progress2WidthMagic" format="dimension" />
        <attr name="progress2ForwardGround" format="color" />
        <attr name="thumb2Color" format="color" />
        <attr name="thumb2Radios" format="dimension" />
        <attr name="max2" format="integer" />
        <attr name="min2" format="integer" />
        <attr name="progress2" format="integer" />
    </declare-styleable>


    <declare-styleable name="HorizontalSeekBar">
        <!--线（进度条）宽度-->
        <attr name="lineHeight" format="dimension" />
        <!--左侧字的大小 100元-->
        <attr name="leftTextSize" format="dimension" />
        <!--左侧字的颜色 100元-->
        <attr name="leftTextColor" format="color" />
        <!--右侧字的大小 100元-->
        <attr name="rightTextSize" format="dimension" />
        <!--右侧侧字的颜色 100元-->
        <attr name="rightTextColor" format="color" />
        <!--两个游标内部 线（进度条）的颜色-->
        <attr name="inColor" format="color" />
        <!--两个游标外部 线（左边进度条）的颜色-->
        <attr name="leftOutColor" format="color" />
        <!--两个游标外部 线（右边进度条）的颜色-->
        <attr name="rightOutColor" format="color" />
        <!--左边图标的图片-->
        <attr name="imageLeft" format="reference"/>
        <!--右边图标 的图片-->
        <attr name="imageRight" format="reference"/>
        <!--游标 图片宽度-->
        <attr name="imageWidth" format="dimension" />
        <!--游标 图片高度-->
        <attr name="imageHeight" format="dimension" />
        <!--中轴线，往下偏移的-->
        <attr name="imageLowPadding" format="dimension" />
        <!--是否有刻度线-->
        <attr name="hasRule" format="boolean" />
        <!--刻度的颜色-->
        <attr name="ruleColor" format="color" />
        <!--刻度上边的字 的颜色-->
        <attr name="ruleTextColor" format="color" />
        <!--单位 元-->
        <attr name="unit" format="string"/>
        <!--单位份数-->
        <attr name="equal" format="integer"/>
        <!--刻度单位 $-->
        <attr name="ruleUnit" format="string"/>
        <!--刻度上边文字的size-->
        <attr name="ruleTextSize" format="dimension" />
        <!--刻度线的高度-->
        <attr name="ruleLineHeight" format="dimension" />
        <!--选择器的最大值-->
        <attr name="bigValue" format="integer"/>
        <!--选择器的最小值-->
        <attr name="smallValue" format="integer"/>
    </declare-styleable>

</resources>