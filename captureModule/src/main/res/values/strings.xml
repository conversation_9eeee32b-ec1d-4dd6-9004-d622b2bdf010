<resources>
    //////////////////////MainPage///////////////////////////////////
    <array name="music_fragment_title">
        <item>@string/localMusic</item>
        <item>@string/myMusic</item>
    </array>
    <string name="app_name">Meicam</string>
    <string name="privacy_statement">Agreement and policy</string>
    <string name="statement_content">Please carefully read and fully understand the terms and conditions of service agreement and privacy policy. Meitu only provides video editing and shooting services, and will not collect your personal information. You can read the \"Service Agreement\" and \"Privacy Policy\" for details. If you agree, please click agree to accept our service.</string>
    <string name="service_agreement">Service Agreement</string>

    <string name="privacy_policy">Privacy Policy</string>
    <string name="not_used">
Not agree
</string>
    <string name="agree">Agree</string>
    <string name="ssl_error_prompt">The SSL certificate failed, whether to continue to visit?</string>

    //////////////////////Setting//////////////////////////////////
    <string name="captureResolution">Resolution</string>
    <string name="int1080">1080</string>
    <string name="int720">720</string>

    //////////////////////FeedBack////////////////////////////////

    //////////////////////VideoCapture//////////////////////////////////
    <string name="photo">Photo</string>
    <string name="video">Video</string>
    <string name="beauty_close">Close</string>
    <string name="beauty_open">Open</string>
    <string name="beauty">Beauty</string>
    <string name="strength">Strength</string>
    <string name="zeroZZZ">00:00</string>
    <string name="more">More</string>

    <string name="cheek_thinning">Face</string>
    <string name="eye_enlarging">Eyes</string>
    <string name="intensity_forehead">Forhead</string>
    <string name="intensity_chin">Chin</string>
    <string name="intensity_nose">Nose</string>
    <string name="intensity_mouth">Mouth</string>
    <string name="face_small">FacLen</string>

    <string name="makeup_null">none</string>


    //////////////////////Selectvideo//////////////////////////////////

    <string name="prompt">Tips</string>
    <string name="single_select_picture">Select Image</string>

    <string name="sixteenTNine">16:9</string>
    <string name="nineTSixteen">9:16</string>
    <string name="fourTThree">4:3</string>
    <string name="threeTFour">3:4</string>
    <string name="oneTone">1:1</string>
    <string name="videoEdit">Video Edit</string>

    //////////////////////VideoEdit//////////////////////////////////
    <string name="filter">Filter</string>
    <string name="stayTuned">Stay tuned!</string>
    <string name="contactBusiness">Move to the official website to contact business people</string>

    <string name="volume">Volume</string>
    //////////////////////Theme//////////////////////////////////
    <string name="delete">Delete</string>

    //////////////////////videoClipEdit////////////////////////////////
    <string name="brightness">Brightness</string>
    <string name="contrast">Contrast</string>
    <string name="saturation">Saturation</string>
    <string name="degree">Degree</string>

    <string name="sharpness">Sharpness</string>
    <string name="clarity">clarity</string>

    <string name="string_format_one_point">"%.1f"</string>

    <string name="add">Add</string>
    <string name="free">Free</string>
    <string name="circle">Circle</string>
    <string name="square">Square</string>

    <string name="customStickerselecteffect">Select actions</string>

    //////////////////////Filter////////////////////////////////

    //////////////////////AnimatedSticker/////////////////////////
    <string name="shadow">Shadow</string>

    //////////////////////Caption//////////////////////////////////
    <string name="confirm">Confirm</string>
    <string name="cancel">Cancel</string>
    <string name="noneMusic">No music</string>
    <string name="localMusic">Local music</string>
    <string name="myMusic">My music</string>
    <string name="music_default_size">00:00/00:10</string>
    <string name="moreTheme">More Themes</string>

    //////////////////////CompoundCaption/////////////////////////////

    //////////////////////WaterMark//////////////////////////////////

    //////////////////////Music////////////////////////////////
    <string name="props_all">ALl</string>
    <string name="props_2d">2D</string>
    <string name="props_3d">3D</string>
    <string name="props_forword">Foreground</string>
    <string name="props_back">Background</string>
    <string name="props_eye">Eye</string>

    ////////////////////////Dub///////////////////////////////////
    <string name="props_mouth">Mouth</string>
    <string name="props_head">Head</string>
    //////////////////////Download////////////////////////////////
    <string name="props_hand">Hand</string>
    <string name="asset_loading">Loading…</string>
    <string name="asset_loadfailed">Failed to load!</string>
    <string name="retry">Retry</string>
    <string name="asset_ratio">General</string>
    <string name="asset_mismatch">Mismatch</string>
    <string name="asset_download">Download</string>
    <string name="asset_downloadfinished">Finished</string>
    <string name="asset_update">Update</string>
    <string name="faceU">Props</string>

    //////////////////////DouYin////////////////////////////////

    <string name="reset">Reset</string>

    <string name="font">Font</string>

    //////////////////////PicInPic////////////////////////////////
    <string name="i_know">Got it</string>

    //////////////////////Particle////////////////////////////////

    //////////////////////CaptureScene////////////////////////////////

    //////////////////////FlipCaption//////////////////////////////////
    <string name="trans_fade">Fade</string>
    <string name="trans_turning">Turning</string>
    <string name="trans_swap">Swap</string>

    //////////////////////MusicLyrics//////////////////////////////////
    <string name="trans_stretch_in">Stretch In</string>

    //////////////////////Common Part//////////////////////////////////
    <string name="trans_page_curl">Page Curl</string>
    <string name="trans_lens_flare">Lens Flare</string>
    <string name="trans_star">Star</string>
    <string name="trans_dip_to_black">Dip To Black</string>
    <string name="trans_dip_to_white">Dip To White</string>

    //////////////////////SuperZoomActivity////////////////////////////////
    <string name="trans_push_to_right">Push To Right</string>
    <string name="trans_push_to_left">Push To Top</string>
    <string name="trans_upper_left_into">Upper Left Into</string>

    <!--Cut To -->

    <!--    mimo-->

    <!--filter key frame-->
    <string name="apply_all_clip">apply all clip</string>

    <string-array name="select_media">
        <item>All</item>
        <item>Video</item>
        <item>Image</item>
        <item>Native</item>
    </string-array>
    <string-array name="permissions_tips">
        <item>@string/prompt</item>
        <item>Please open the relevant permissions (camera, microphone, storage)</item>
    </string-array>

    <string name="video_change_voice">Change Voice</string>

    <string name="tv_point_add">add point</string>
    <string name="tv_point_remove">remove point</string>
    <string name="tv_custom">custom</string>

    <string name="beauty_brighten_eye">BrightenEye</string>
    <string name="beauty_nasolabial">Nasolabial</string>
    <string name="beauty_dark_circles">dark circles</string>
    <string name="beauty_tooth">tooth</string>
    <string name="tv_save_local">save local</string>
    <string name="tv_to_edit">to edit</string>
    <string name="capture_preview_save_local">save local</string>
    <string name="toast_message_support_max">Zoom exceeds maximum</string>
    <string name="toast_message_support">Zoom is not supported</string>
    <string name="zoom">x</string>
    <string name="capture_hint_select_music">Select music</string>
</resources>