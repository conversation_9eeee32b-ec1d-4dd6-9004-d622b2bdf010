# MYVideo Android SDK

This project has been configured to be packaged as an AAR (Android Archive) library, allowing it to be easily integrated into other Android applications.

## Building the AAR

To build the AAR file, run the following command:

```bash
./gradlew clean :app:assembleRelease :app:copyFatAar
```

This will generate the AAR file in the `sdk-output` directory at the root of the project.

## Using the AAR in Another Project

1. Copy the generated AAR file (`MYVideo-x.x.x.aar`) to your project's `libs` directory.

2. Add the following to your app's `build.gradle` file:

```gradle
repositories {
    flatDir {
        dirs 'libs'
    }
}

dependencies {
    implementation(name: 'MYVideo-x.x.x', ext: 'aar')
    
    // Add any dependencies required by the AAR
    implementation 'org.greenrobot:eventbus:3.2.0'
    implementation 'com.facebook.fresco:fresco:1.12.0'
    implementation 'com.facebook.fresco:animated-gif:1.12.0'
    implementation 'com.facebook.fresco:animated-webp:1.12.0'
    implementation 'com.facebook.fresco:webpsupport:1.12.0'
}
```

3. Initialize the SDK in your Application class:

```java
public class YourApplication extends Application {
    @Override
    public void onCreate() {
        super.onCreate();
        
        // Initialize any required components from the SDK
        // For example:
        // MYVideoSDK.init(this);
    }
}
```

4. Use the SDK components in your Activities:

```java
// Example of using the NewMainFragment
public class YourActivity extends AppCompatActivity {
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.your_activity_layout);
        
        // Add the NewMainFragment to your activity
        if (savedInstanceState == null) {
            getSupportFragmentManager().beginTransaction()
                .add(R.id.fragment_container, new NewMainFragment())
                .commit();
        }
    }
}
```

## Publishing to Maven Local

To publish the AAR to your local Maven repository:

```bash
./gradlew :app:publishReleasePublicationToLocalRepoRepository
```

This will publish the AAR to `app/build/repo` which you can then use in other projects by adding this repository to your project's `settings.gradle` or `build.gradle` file.
