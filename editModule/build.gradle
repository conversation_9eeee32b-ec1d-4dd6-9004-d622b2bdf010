apply plugin: 'com.android.library'

android {
    compileSdkVersion rootProject.android.extCompileSdkVersion
    buildToolsVersion rootProject.android.extBuildToolsVersion


    defaultConfig {
        minSdkVersion rootProject.android.extMinSdkVersion
        targetSdkVersion rootProject.android.extTargetSdkVersion
        versionCode rootProject.config.extVersionCode
        versionName rootProject.config.extVersionName

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled true
            matchingFallbacks = ['release']
            consumerProguardFiles 'proguard-rules.pro'
        }

        debug {
            minifyEnabled false
            signingConfig signingConfigs.debug
            matchingFallbacks = ['debug']
            debuggable true
        }
    }

    compileOptions {
        targetCompatibility JavaVersion.VERSION_1_8
        sourceCompatibility JavaVersion.VERSION_1_8
    }
}

dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    testImplementation rootProject.ext.dependencies.extTestJunit
    androidTestImplementation rootProject.ext.dependencies.extAndroidTestRunner
    androidTestImplementation rootProject.ext.dependencies.extTestEspresso
    api project(path: ':libSpeaker')
    implementation project(path: ':libPlayer')
    implementation project(path: ':libEngine')
    api project(path: ':libLogic')
    api project(path: ':draft')
    implementation project(path: ':cutsameModel')
    api project(path: ':libPlugin')
    implementation project(path: ':libAssetsView')
    implementation rootProject.ext.dependencies.extConstraintLayout
    implementation rootProject.ext.dependencies.extSupportDesign
    implementation rootProject.ext.dependencies.extSwiperefreshlayout

    //下边的引用会逐步淘汰
    implementation 'org.greenrobot:eventbus:3.2.0'
    implementation 'com.facebook.fresco:fresco:1.12.0'
    // 支持 GIF 动图，需要添加
    implementation 'com.facebook.fresco:animated-gif:1.12.0'
    // 支持 WebP （静态图+动图），需要添加
    implementation 'com.facebook.fresco:animated-webp:1.12.0'
    // 仅支持 WebP 静态图，需要添加
    implementation 'com.facebook.fresco:webpsupport:1.12.0'
    implementation(project(':libSpi'))
    //正式版本需要去掉这个依赖
    //implementation 'com.squareup.leakcanary:leakcanary-android:1.6.3'
    // 只有在你使用了 support library fragments 的时候才需要下面这一项
    //implementation 'com.squareup.leakcanary:leakcanary-support-fragment:1.6.3'

}
