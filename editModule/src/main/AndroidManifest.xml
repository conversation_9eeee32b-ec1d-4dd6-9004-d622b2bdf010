<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.meishe.myvideo"
    android:installLocation="auto">

    <uses-permission android:name="android.permission.RECORD_AUDIO" /> <!-- 用于录音 -->
    <uses-permission android:name="android.permission.ACCESS_NOTIFICATION_POLICY" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.EAD_MEDIA_VIDEO" />
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.VIBRATE" /> <!-- googleplay上线问题，增加权限 -->
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" /> <!-- bugly增加权限 -->
    <uses-permission
        android:name="android.permission.READ_LOGS"
        tools:ignore="ProtectedPermissions" />
    <!--  <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" /> -->
    <!-- 讯飞权限 -->
    <!-- 连接网络权限，用于执行云端语音能力 -->
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
    <!--
 <uses-permission
        android:name="android.permission.WRITE_SETTINGS"
        tools:ignore="ProtectedPermissions" />
    -->
    <!-- 讯飞权限 -->
    <!-- 阿里云权限 -->
    <uses-permission
        android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS"
        tools:ignore="ProtectedPermissions" />

    <application>
        <activity
            android:name=".activity.ClipReplaceActivity"
            android:screenOrientation="portrait"
            android:theme="@style/TranslucentFullScreenTheme"
            android:exported="false"
            tools:ignore="LockedOrientationActivity">
            <intent-filter>
                <action android:name="com.meishe.myvideo.activity.ClipReplaceActivity" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <activity
            android:name=".activity.MaterialSelectFillActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/FullScreenTheme"
            tools:ignore="LockedOrientationActivity" />
        <activity
            android:name=".activity.FullScreenPreviewActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:theme="@style/TranslucentFullScreenTheme" />
        <activity
            android:name=".activity.MaterialSelectActivity"
            android:screenOrientation="portrait"
            android:exported="true"
            android:theme="@style/FullScreenTheme"
            tools:ignore="LockedOrientationActivity" />
        <activity
            android:name=".activity.MaterialPreviewActivity"
            android:screenOrientation="portrait"
            android:theme="@style/FullScreenTheme"
            tools:ignore="LockedOrientationActivity" />
        <activity
            android:name=".activity.AtomicActivity"
            android:screenOrientation="portrait"
            android:theme="@style/FullScreenTheme"
            tools:ignore="LockedOrientationActivity" />
        <activity
            android:name=".activity.SelectMusicActivity"
            android:screenOrientation="portrait"
            android:theme="@style/TranslucentFullScreenTheme"
            tools:ignore="LockedOrientationActivity" />
        <activity
            android:name=".activity.CompileActivity"
            android:screenOrientation="portrait"
            android:theme="@style/TranslucentFullScreenTheme"
            tools:ignore="LockedOrientationActivity" />
        <activity
            android:name="com.meishe.user.activity.CloudCompileActivity"
            android:screenOrientation="portrait"
            android:theme="@style/TranslucentFullScreenTheme"
            tools:ignore="LockedOrientationActivity" />
        <activity
            android:name=".activity.CustomStickerClipActivity"
            android:screenOrientation="portrait"
            android:exported="false"
            android:theme="@style/TranslucentFullScreenTheme"
            tools:ignore="LockedOrientationActivity">
            <intent-filter>
                <action android:name="com.meishe.video.CustomAnimate" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <activity
            android:name=".downLoad.AssetDownloadActivity"
            android:screenOrientation="portrait"
            android:theme="@style/TranslucentFullScreenTheme"
            tools:ignore="LockedOrientationActivity" />
        <activity
            android:name=".activity.MaterialSingleSelectActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/FullScreenTheme"
            tools:ignore="LockedOrientationActivity" />
        <activity
            android:name=".activity.DraftEditActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/TranslucentFullScreenTheme"
            tools:ignore="LockedOrientationActivity" />
        <activity
            android:name=".activity.ClipCuttingActivity"
            android:screenOrientation="portrait"
            android:theme="@style/BaseAnimationTheme"
            tools:ignore="LockedOrientationActivity" />
        <activity
            android:name=".activity.CustomStickerEffectActivity"
            android:screenOrientation="portrait"
            android:theme="@style/BaseAnimationTheme"
            tools:ignore="LockedOrientationActivity" />

        <activity
            android:name=".activity.CoverEditActivity"
            android:screenOrientation="portrait"
            android:theme="@style/BaseAnimationTheme"
            tools:ignore="LockedOrientationActivity"/>
        <activity
            android:name="com.meishe.player.ImageRectCutActivity"
            android:screenOrientation="portrait"
            android:theme="@style/TranslucentFullScreenTheme"
            tools:ignore="LockedOrientationActivity"/>
        <activity
            android:name=".activity.TestActivity"
            android:screenOrientation="portrait"
            android:theme="@style/FullScreenTheme"/>
    </application> <!-- 用于录音 -->
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.ACCESS_NOTIFICATION_POLICY" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" /> <!-- googleplay上线问题，增加权限 -->
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" /> <!-- bugly增加权限 -->
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission
        android:name="android.permission.READ_LOGS"
        tools:ignore="ProtectedPermissions" /> <!-- 讯飞权限 -->
    <!-- 连接网络权限，用于执行云端语音能力 -->
    <!-- <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />-->
    <!--
 <uses-permission
        android:name="android.permission.WRITE_SETTINGS"
        tools:ignore="ProtectedPermissions" />
    -->
    <!-- 讯飞权限 -->
    <!-- 阿里云权限 -->
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />

    <uses-permission
        android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS"
        tools:ignore="ProtectedPermissions" />

</manifest>