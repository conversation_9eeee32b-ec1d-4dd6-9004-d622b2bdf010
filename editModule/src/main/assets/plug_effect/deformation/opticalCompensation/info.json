{"paramList": [{"name": "畸变程度", "enName": "fov", "coverPath": "plug_fov", "paramName": "FOV", "valueType": "F", "valueDefault": 45, "valueMin": 0, "valueMax": 180}, {"name": "反向补偿", "enName": "invert", "coverPath": "plug_check", "paramName": "Invert", "valueType": "B", "valueDefault": false}, {"name": "畸变中心", "enName": "fovAxis", "coverPath": "plug_zhong", "paramName": "X", "valueType": "S", "valueDefault": "x", "modeList": [{"paramValue": "x", "modeName": "x轴", "modeEnName": "x"}, {"paramValue": "y", "modeName": "y轴", "modeEnName": "y"}]}, {"name": "补边模式", "enName": "wrapMode", "paramName": "Wrap Mode", "coverPath": "plug_bu", "valueType": "S", "valueDefault": "mirror", "modeList": [{"paramValue": "mirror", "modeName": "镜像", "modeEnName": "mirror"}, {"paramValue": "repeat", "modeName": "重复", "modeEnName": "repeat"}, {"paramValue": "clamp", "modeName": "堆叠", "modeEnName": "clamp"}]}, {"name": "X轴补偿", "enName": "enableAxisX", "coverPath": "plug_check", "paramName": "Enable Axis X", "valueType": "B", "valueDefault": true}, {"name": "Y轴补偿", "enName": "enableAxisY", "coverPath": "plug_check", "paramName": "Enable Axis Y", "valueType": "B", "valueDefault": true}, {"name": "补偿模式", "enName": "compensationMode", "paramName": "Compensation Mode", "coverPath": "plug_bu", "valueType": "S", "valueDefault": "wild", "modeList": [{"paramValue": "wild", "modeName": "自然", "modeEnName": "wild"}, {"paramValue": "restraint", "modeName": "约束", "modeEnName": "restraint"}]}, {"name": "X轴中心", "enName": "centerX", "coverPath": "plug_center_x", "paramName": "Center X", "valueType": "F", "valueDefault": 0, "valueMin": -1, "valueMax": 1}, {"name": "Y轴中心", "enName": "centerY", "coverPath": "plug_center_y", "paramName": "Center Y", "valueType": "F", "valueDefault": 0, "valueMin": -1, "valueMax": 1}]}