{"paramList": [{"name": "扭曲程度", "enName": "intensity", "paramName": "Intensity", "coverPath": "plug_intensity", "valueType": "F", "valueDefault": 0.5, "valueMin": 0, "valueMax": 1}, {"name": "补边模式", "enName": "wrapMode", "paramName": "Wrap Mode", "coverPath": "plug_bu", "valueType": "S", "valueDefault": "mirror", "modeList": [{"paramValue": "mirror", "modeName": "镜像", "modeEnName": "mirror"}, {"paramValue": "repeat", "modeName": "重复", "modeEnName": "repeat"}, {"paramValue": "clamp", "modeName": "堆叠", "modeEnName": "clamp"}]}, {"name": "X轴扭曲", "enName": "centerX", "paramName": "Center X", "coverPath": "plug_center_x", "valueType": "F", "valueDefault": 0, "valueMin": -1, "valueMax": 1}, {"name": "Y轴扭曲", "enName": "centerY", "paramName": "Center Y", "coverPath": "plug_center_y", "valueType": "F", "valueDefault": 0, "valueMin": -1, "valueMax": 1}, {"name": "X轴一阶", "enName": "k1X", "paramName": "K1 X", "coverPath": "plug_center_x", "valueType": "F", "valueDefault": 1, "valueMin": -1, "valueMax": 1}, {"name": "Y轴一阶", "enName": "k1Y", "paramName": "K1 Y", "coverPath": "plug_center_y", "valueType": "F", "valueDefault": 1, "valueMin": -1, "valueMax": 1}, {"name": "X轴二阶", "enName": "k2X", "paramName": "K2 X", "coverPath": "plug_center_x", "valueType": "F", "valueDefault": 1, "valueMin": -1, "valueMax": 1}, {"name": "Y轴二阶", "enName": "k2Y", "paramName": "K2 Y", "coverPath": "plug_center_y", "valueType": "F", "valueDefault": 1, "valueMin": -1, "valueMax": 1}, {"name": "X轴三阶", "enName": "k3X", "paramName": "K3 X", "coverPath": "plug_center_x", "valueType": "F", "valueDefault": 1, "valueMin": -1, "valueMax": 1}, {"name": "Y轴三阶", "enName": "k3Y", "paramName": "K3 Y", "coverPath": "plug_center_y", "valueType": "F", "valueDefault": 1, "valueMin": -1, "valueMax": 1}]}