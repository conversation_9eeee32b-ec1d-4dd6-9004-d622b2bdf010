[{"name": "水波纹", "enName": "waterRipple", "plugName": "Water Ripple", "coverPath": "file:///android_asset/plug_effect/deformation/waterRipple/waterRipple.png", "plugDesc": "可对图像添加水形波纹效果", "plugDescEn": "You can add a water ripple effect to the image", "effectPath": "plug_effect/deformation/waterRipple/info.json"}, {"name": "扭曲", "enName": "distortion", "plugName": "Distortion", "coverPath": "file:///android_asset/plug_effect/deformation/distortion/distortion.png", "plugDesc": "可对图像的X轴和Y轴进行扭曲调整", "plugDescEn": "The X and Y axes of the image can be distorted", "effectPath": "plug_effect/deformation/distortion/info.json"}, {"name": "噪波扭曲", "enName": "noiseWave", "plugName": "Noise Wave", "coverPath": "file:///android_asset/plug_effect/deformation/noiseWave/noiseWave.png", "plugDesc": "可实现对图像的波长和幅度进行噪波扭曲效果", "plugDescEn": "It can realize the effect of distorting the wavelength and amplitude of the image", "effectPath": "plug_effect/deformation/noiseWave/info.json"}, {"name": "波浪扭曲", "enName": "signalGenerator", "plugName": "Signal Generator", "coverPath": "file:///android_asset/plug_effect/deformation/signalGenerator/signalGenerator.png", "plugDesc": "可对图像实现波浪放射扭曲效果", "plugDescEn": "Can realize the wave radiation distortion effect on the image", "effectPath": "plug_effect/deformation/signalGenerator/info.json"}, {"name": "镜头扭曲", "enName": "cameraDistortion", "plugName": "Camera Distortion", "coverPath": "file:///android_asset/plug_effect/deformation/cameraDistortion/cameraDistortion.png", "plugDesc": "可对图像模拟镜头畸变而产生的扭曲变形效果", "plugDescEn": "Can simulate the distortion of the image lens distortion and distortion effect", "effectPath": "plug_effect/deformation/cameraDistortion/info.json"}, {"name": "液化失真", "enName": "liquefaction", "plugName": "Liquefaction", "coverPath": "file:///android_asset/plug_effect/deformation/liquefaction/liquefaction.png", "plugDesc": "可对图像的X轴和Y轴为控制点，模拟液化变形效果", "plugDescEn": "The X-axis and Y-axis of the image can be used as control points to simulate the effect of liquefaction deformation", "effectPath": "plug_effect/deformation/liquefaction/info.json"}, {"name": "漩涡效果", "enName": "vortex", "plugName": "Vortex", "coverPath": "file:///android_asset/plug_effect/deformation/vortex/vortex.png", "plugDesc": "可对图像产生螺旋状变形效果", "plugDescEn": "Can produce spiral deformation effect on the image", "effectPath": "plug_effect/deformation/vortex/info.json"}, {"name": "光学补偿", "enName": "opticalCompensation", "plugName": "Optical Compensation", "coverPath": "file:///android_asset/plug_effect/deformation/opticalCompensation/opticalCompensation.png", "plugDesc": "可对图像实现镜头变形效果", "plugDescEn": "Can achieve the image lens deformation effect", "effectPath": "plug_effect/deformation/opticalCompensation/info.json"}, {"name": "卷页", "enName": "pageCurl", "plugName": "<PERSON>", "coverPath": "file:///android_asset/plug_effect/deformation/pageCurl/pageCurl.png", "plugDesc": "可对图像模拟翻阅图书效果", "plugDescEn": "The image can simulate the effect of browsing books", "effectPath": "plug_effect/deformation/pageCurl/info.json"}]