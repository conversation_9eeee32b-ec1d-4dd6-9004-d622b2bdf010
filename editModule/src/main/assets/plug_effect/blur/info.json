[{"name": "快速模糊", "enName": "fastBlur", "plugName": "Fast Blur", "coverPath": "file:///android_asset/plug_effect/blur/fastBlur/fastBlur.png", "plugDesc": "可对图像进行快速模糊处理", "plugDescEn": "The image can be quickly blurred", "effectPath": "plug_effect/blur/fastBlur/info.json"}, {"name": "高斯模糊", "enName": "g<PERSON><PERSON><PERSON><PERSON><PERSON>", "plugName": "Gaussian Blur", "coverPath": "file:///android_asset/plug_effect/blur/gaussianBlur/gaussianBlur.png", "plugDesc": "可对图像进行高斯模糊处理", "plugDescEn": "The image can be processed by Gaussian blur", "effectPath": "plug_effect/blur/gaussianBlur/info.json"}, {"name": "方向模糊", "enName": "<PERSON><PERSON><PERSON><PERSON>", "plugName": "<PERSON><PERSON> Blur", "coverPath": "file:///android_asset/plug_effect/blur/dirBlur/dirBlur.png", "plugDesc": "可按指定方向和角度模糊图像", "plugDescEn": "Blur the image by specified direction and Angle", "effectPath": "plug_effect/blur/dirBlur/info.json"}, {"name": "缩放模糊", "enName": "zoomBlur", "plugName": "Zoom Blur", "coverPath": "file:///android_asset/plug_effect/blur/zoomBlur/zoomBlur.png", "plugDesc": "可对图像按某个中心点的朝向模糊处理", "plugDescEn": "The image can be blurred according to the orientation of a center point", "effectPath": "plug_effect/blur/zoomBlur/info.json"}, {"name": "旋转模糊", "enName": "spiralBlur", "plugName": "Spiral Blur", "coverPath": "file:///android_asset/plug_effect/blur/spiralBlur/spiralBlur.png", "plugDesc": "可对图像以某个点为圆心的漩涡状旋转模糊处理", "plugDescEn": "The image can be blurred with a point as the center of the vortex rotation", "effectPath": "plug_effect/blur/spiralBlur/info.json"}, {"name": "粒子模糊", "enName": "particleBlur", "plugName": "Particle Blur", "coverPath": "file:///android_asset/plug_effect/blur/particleBlur/particleBlur.png", "plugDesc": "可对图像按颗粒大小和强度模糊处理", "plugDescEn": "The image can be blurred according to particle size and intensity", "effectPath": "plug_effect/blur/particleBlur/info.json"}, {"name": "方框模糊", "enName": "boxBlur", "plugName": "Box Blur", "coverPath": "file:///android_asset/plug_effect/blur/boxBlur/boxBlur.png", "plugDesc": "可对图像以方框类型进行模糊处理", "plugDescEn": "The image can be blurred with a box type", "effectPath": "plug_effect/blur/boxBlur/info.json"}]