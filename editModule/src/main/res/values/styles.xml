<resources>

    <!-- Base application theme. -->
    <style name="AppTheme" parent="Theme.AppCompat.Light.DarkActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
    </style>

    <style name="tabLayoutTextStyle">
        <item name="android:textSize">@dimen/tab_layout_text_size</item>
    </style>

    <style name="subTabLayoutTextStyle">
        <item name="android:textSize">@dimen/sub_tab_layout_text_size</item>
    </style>

    <style name="draftTabLayoutTextStyle">
        <item name="android:textSize">@dimen/sp_px_42</item>
    </style>

    <style name="dialog" parent="@android:style/Theme.Dialog">
        <item name="android:windowFrame">@null</item>
        <!--边框-->
        <item name="android:windowIsFloating">true</item>
        <!--是否浮现在activity之上-->
        <item name="android:windowIsTranslucent">true</item>
        <!--半透明-->
        <item name="android:windowNoTitle">true</item>
        <!--无标题-->
        <item name="android:windowBackground">@android:color/transparent</item>
        <!--背景透明-->
        <item name="android:backgroundDimEnabled">true</item>
        <!--模糊-->

    </style>

    <style name="TranslucentFullScreenTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowBackground">@color/edit_activity_main_color</item>
    </style>

    <style name="FullScreenTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowBackground">@color/colorTranslucent</item>
        <item name="android:windowIsTranslucent">false</item>
        <item name="android:windowDisablePreview">true</item>
        <item name="android:windowFullscreen">true</item>
    </style>

    <style name="NoActionBar" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowBackground">@color/colorTranslucent</item>
        <item name="android:windowIsTranslucent">true</item>
        <!-- <item name="android:windowFullscreen">true</item>-->
    </style>

    <style name="BaseAnimationTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/ff000000</item>
        <item name="colorPrimaryDark">@color/ff000000</item>
        <item name="colorAccent">@color/colorAccent</item>
        <!--activity的动画-->
        <item name="android:windowAnimationStyle">@style/HoloThemeActivityAnimation</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
        <!--<item name="android:windowBackground">@android:color/transparent</item>-->

    </style>

    <style name="MyEditText" parent="Theme.AppCompat.Light">
        <item name="colorControlNormal">#707070</item>
        <item name="colorControlActivated">#707070</item>
    </style>

    <!--Activity入场与退出动画-->
    <style name="HoloThemeActivityAnimation" parent="@android:style/Animation.Activity">
        <item name="android:activityOpenEnterAnimation">@anim/slide_left</item>
        <!--<item name="android:activityOpenExitAnimation">@anim/slide_right</item>-->
        <!--<item name="android:activityCloseEnterAnimation">@anim/slide_left</item>-->
        <item name="android:activityCloseExitAnimation">@anim/slide_right</item>
    </style>


    <style name="edit_dlg_style" parent="android:Animation">
        <item name="android:windowEnterAnimation">@anim/view_enter</item>
        <item name="android:windowExitAnimation">@anim/view_exit</item>
    </style>

    <declare-styleable name="CompileProgress">
        <attr name="edit_progress" format="integer" />
        <attr name="maxProgress" format="integer" />
        <attr name="minProgress" format="integer" />
        <attr name="progressColor" format="color" />
        <attr name="progressBackgroundColor" format="color" />
        <attr name="progressWidth" format="dimension" />
    </declare-styleable>

    <style name="CustomBottomDialogAnimator">
        <item name="android:windowEnterAnimation">@anim/view_enter</item>
        <item name="android:windowExitAnimation">@anim/view_exit</item>
    </style>


    <!--参数设置checkbox样式-->
    <style name="CustomCheckBoxTheme" parent="@android:style/Widget.CompoundButton.CheckBox">
        <item name="android:button">@null</item>
    </style>

    <style name="DialogEditText" parent="Theme.AppCompat.Light">
        <item name="colorControlNormal">@color/white</item>
        <item name="colorControlActivated">@color/white</item>
    </style>

    <style name="AppTheme.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar" />

    <style name="AppTheme.PopupOverlay" parent="ThemeOverlay.AppCompat.Light" />

    <style name="StyleProgressBarMini" parent="@android:style/Widget.ProgressBar.Horizontal">
        <item name="android:maxHeight">50dip</item>
        <item name="android:minHeight">10dip</item>
        <item name="android:indeterminateOnly">false</item>
        <item name="android:indeterminateDrawable">
            @android:drawable/progress_indeterminate_horizontal
        </item>
        <item name="android:progressDrawable">@drawable/shape_progressbar_mini</item>
    </style>

    <style name="switchStyle" parent="Theme.AppCompat.Light">
        <item name="colorControlActivated">#00000000</item>
       <item name="colorSwitchThumbNormal">#00000000</item>
       <item name="android:colorForeground">#00000000</item>
    </style>
</resources>
