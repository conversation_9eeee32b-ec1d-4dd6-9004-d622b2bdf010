<shape xmlns:android="http://schemas.android.com/apk/res/android"
    android:shape="rectangle">
    <solid android:color="@color/switch_gray_track_color" />
    <!-- 这个是用来实现轨道高度小于圆钮高度的，值越大轨道越细-->
    <!-- 同理，若thumb有stroke，track没有，可实现圆钮在轨道里的伪效果-->
    <!-- This is used to realize that the track height is smaller than the button height. The higher the value, the thinner the track -->
    <!-- Similarly, if the thumb has a stroke and the track does not, the fake effect of the round button in the track can be achieved -->
    <stroke
        android:width="12dp"
        android:color="#00000000" />
    <corners android:radius="20dp" />
</shape>