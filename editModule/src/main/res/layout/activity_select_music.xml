<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/main_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black"
    android:orientation="vertical">

    <!--<View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_260"
        android:background="@color/black" />-->

    <TextView
        android:id="@+id/tv_select_music"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_165"
        android:layout_marginTop="@dimen/dp_px_96"
        android:background="@color/black"
        android:gravity="center"
        android:text="@string/music_select"
        android:textColor="@color/white_8"
        android:textSize="@dimen/sp_px_48" />

    <ImageView
        android:id="@+id/iv_back"
        android:layout_width="@dimen/dp_px_80"
        android:layout_height="@dimen/dp_px_80"
        android:layout_alignTop="@+id/tv_select_music"
        android:layout_marginStart="@dimen/dp_px_50"
        android:layout_marginLeft="@dimen/dp_px_50"
        android:layout_marginTop="@dimen/dp_px_42"
        android:background="@mipmap/ic_draft_back"
        android:contentDescription="@null"
        android:padding="@dimen/dp_px_15" />


    <com.meishe.third.tablayout.SlidingTabLayout
        android:id="@+id/tab_layout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_165"
        android:layout_below="@+id/tv_select_music"
        android:background="@color/black"
        app:tl_indicator_color="@color/red_fc2b"
        app:tl_indicator_height="@dimen/dp_px_9"
        app:tl_tab_space_equal="true"
        app:tl_textSelectColor="@color/red_fc2b"
        app:tl_textUnselectedColor="@color/white_8" />

    <RelativeLayout
        android:id="@+id/rl_selected_music_parent"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_432"
        android:layout_alignParentBottom="true"
        android:background="@color/black_1c1c"
        android:paddingStart="@dimen/dp_px_40"
        android:paddingEnd="@dimen/dp_px_40"
        android:visibility="gone">

        <Button
            android:id="@+id/bt_use_music"
            android:layout_width="@dimen/dp_px_105"
            android:layout_height="@dimen/dp_px_60"
            android:layout_alignParentEnd="true"
            android:layout_alignParentRight="true"
            android:layout_marginTop="@dimen/dp_px_40"
            android:background="@drawable/bg_round_corners_solid_red_fc"
            android:text="@string/music_use"
            android:textAllCaps="false"
            android:textColor="@color/white_8"
            android:textSize="@dimen/sp_px_30" />

        <ImageView
            android:id="@+id/iv_selected_music"
            android:layout_width="@dimen/dp_px_150"
            android:layout_height="@dimen/dp_px_150"
            android:layout_marginTop="@dimen/dp_px_40"
            android:background="@mipmap/ic_music_single_btn"
            android:contentDescription="@null" />


        <TextView
            android:id="@+id/tv_music_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_px_40"
            android:layout_marginLeft="@dimen/dp_px_40"
            android:layout_marginTop="@dimen/dp_px_40"
            android:layout_toStartOf="@+id/bt_use_music"
            android:layout_toLeftOf="@+id/bt_use_music"
            android:layout_toEndOf="@+id/iv_selected_music"
            android:layout_toRightOf="@+id/iv_selected_music"
            android:ellipsize="end"
            android:singleLine="true"
            android:textColor="@color/white_8"
            android:textSize="@dimen/sp_px_36" />


        <TextView
            android:id="@+id/tv_music_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/tv_music_name"
            android:layout_marginStart="@dimen/dp_px_46"
            android:layout_marginLeft="@dimen/dp_px_46"
            android:layout_toEndOf="@+id/iv_selected_music"
            android:layout_toRightOf="@+id/iv_selected_music"
            android:text="@string/music_default_size"
            android:textColor="@color/white_909"
            android:textSize="@dimen/sp_px_36"
            android:visibility="gone" />

        <TextView
            android:id="@+id/tv_music_author"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/tv_music_time"
            android:layout_marginStart="@dimen/dp_px_40"
            android:layout_marginLeft="@dimen/dp_px_40"
            android:layout_toEndOf="@+id/iv_selected_music"
            android:layout_toRightOf="@+id/iv_selected_music"
            android:textColor="@color/white_909"
            android:textSize="@dimen/sp_px_36" />

        <com.meishe.myvideo.view.CutMusicView
            android:id="@+id/select_music_cut_view"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_px_160"
            android:layout_below="@+id/iv_selected_music"
            android:paddingStart="@dimen/dp_px_15"
            android:paddingEnd="@dimen/dp_px_15" />

    </RelativeLayout>


    <androidx.viewpager.widget.ViewPager
        android:id="@+id/select_music_viewpager"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@+id/rl_selected_music_parent"
        android:layout_below="@+id/tab_layout"
        android:background="@color/black" />
</RelativeLayout>
