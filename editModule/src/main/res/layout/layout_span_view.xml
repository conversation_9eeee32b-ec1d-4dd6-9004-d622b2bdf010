<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <View
        android:id="@+id/v_start_empty"
        android:layout_width="wrap_content"
        android:layout_height="match_parent" />

    <RelativeLayout
        android:id="@+id/rl_content_container"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:background="@drawable/bg_round_corner_white">

        <ImageView
            android:id="@+id/iv_left_hand"
            android:layout_width="@dimen/dp_px_50"
            android:layout_height="match_parent"
            android:contentDescription="@null"
            android:scaleType="fitXY"
            android:src="@mipmap/ic_thumbnail_left_hand" />

        <LinearLayout
            android:layout_marginTop="@dimen/dp_px_9"
            android:layout_toEndOf="@id/iv_left_hand"
            android:layout_toRightOf="@id/iv_left_hand"
            android:layout_width="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_height="@dimen/dp_px_36">
            <TextView
                android:background="@drawable/bg_audio_draw_text"
                android:id="@+id/tv_duration"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginLeft="@dimen/dp_px_9"
                android:layout_marginStart="@dimen/dp_px_9"
                android:gravity="center_vertical"
                android:layout_gravity="center_vertical"
                android:textColor="@color/white_8"
                android:textSize="@dimen/sp_px_24"
                android:paddingStart="@dimen/dp_px_9"
                android:paddingLeft="@dimen/dp_px_9"
                android:paddingEnd="@dimen/dp_px_9"
                android:paddingRight="@dimen/dp_px_9"
                tools:ignore="RelativeOverlap" />

            <LinearLayout
                android:layout_gravity="center_vertical"
                android:id="@+id/ll_speed_container"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:background="@drawable/bg_audio_draw_text"
                android:orientation="horizontal"
                android:paddingStart="@dimen/dp_px_9"
                android:paddingLeft="@dimen/dp_px_9"
                android:paddingEnd="@dimen/dp_px_9"
                android:paddingRight="@dimen/dp_px_9"
                android:layout_marginLeft="@dimen/dp_px_9"
                android:layout_marginStart="@dimen/dp_px_9"
                tools:ignore="RelativeOverlap,UseCompoundDrawables">

                <ImageView
                    android:id="@+id/iv_speed"
                    android:layout_width="@dimen/dp_px_21"
                    android:layout_height="@dimen/dp_px_21"
                    android:layout_gravity="center_vertical"
                    android:contentDescription="@null"
                    android:src="@mipmap/sub_menu_icon_edit_speed" />

                <TextView
                    android:id="@+id/tv_speed"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:singleLine="true"
                    android:ellipsize="end"
                    android:gravity="center_vertical"
                    android:layout_marginStart="@dimen/dp_px_6"
                    android:layout_marginLeft="@dimen/dp_px_6"
                    android:textColor="@color/white_8"
                    android:textSize="@dimen/sp_px_24" />
            </LinearLayout>
            <ImageView
                android:gravity="center"
                android:id="@+id/iv_face_icon"
                android:padding="@dimen/dp_px_3"
                android:background="@drawable/bg_audio_draw_text"
                android:layout_marginLeft="@dimen/dp_px_6"
                android:layout_marginStart="@dimen/dp_px_6"
                android:layout_gravity="center_vertical"
                android:contentDescription="@null"
                android:layout_width="@dimen/dp_px_36"
                android:layout_height="@dimen/dp_px_36"
                android:src="@mipmap/icon_face"/>

            <ImageView
                android:gravity="center"
                android:id="@+id/iv_volume_icon"
                android:padding="@dimen/dp_px_3"
                android:background="@drawable/bg_audio_draw_text"
                android:layout_marginLeft="@dimen/dp_px_6"
                android:layout_marginStart="@dimen/dp_px_6"
                android:layout_gravity="center_vertical"
                android:contentDescription="@null"
                android:layout_width="@dimen/dp_px_36"
                android:layout_height="@dimen/dp_px_36"
                android:src="@mipmap/icon_volume"/>
        </LinearLayout>

        <ImageView
            android:id="@+id/iv_right_hand"
            android:layout_width="@dimen/dp_px_50"
            android:layout_height="match_parent"
            android:layout_alignParentEnd="true"
            android:layout_alignParentRight="true"
            android:contentDescription="@null"
            android:scaleType="fitXY"
            android:src="@mipmap/ic_thumbnail_right_hand" />

    </RelativeLayout>


    <View
        android:id="@+id/v_end_empty"
        android:layout_width="wrap_content"
        android:layout_height="match_parent" />
</merge>