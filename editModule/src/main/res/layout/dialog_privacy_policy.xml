<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="@dimen/dp_px_820"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_round_corners_solid_white">

    <TextView
        android:id="@+id/tv_privacy_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_px_69"
        android:gravity="center"
        android:text="@string/privacy_statement"
        android:textColor="@color/black"
        android:textSize="@dimen/sp_px_45" />

    <TextView
        android:id="@+id/tv_statement"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/tv_privacy_title"
        android:layout_marginLeft="@dimen/dp_px_90"
        android:layout_marginTop="@dimen/dp_px_78"
        android:layout_marginRight="@dimen/dp_px_90"
        android:textColor="@color/black"
        android:textSize="@dimen/sp_px_42" />

    <View
        android:id="@+id/v_base_line"
        android:layout_width="@dimen/dp_px_1"
        android:layout_height="@dimen/dp_px_1"
        android:layout_below="@+id/tv_statement"
        android:layout_centerHorizontal="true" />


    <TextView
        android:id="@+id/tv_disagree"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_px_120"
        android:layout_below="@+id/v_base_line"
        android:layout_alignEnd="@+id/v_base_line"
        android:layout_alignRight="@+id/v_base_line"
        android:layout_alignParentStart="true"
        android:layout_alignParentLeft="true"
        android:layout_gravity="center_horizontal"
        android:gravity="center"
        android:text="@string/not_used"
        android:textColor="@color/color_ff33"
        android:textSize="@dimen/sp_px_48" />

    <TextView
        android:id="@+id/tv_agree"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_px_120"
        android:layout_below="@+id/v_base_line"
        android:layout_alignStart="@+id/v_base_line"
        android:layout_alignLeft="@+id/v_base_line"
        android:layout_alignParentEnd="true"
        android:layout_alignParentRight="true"
        android:layout_gravity="center_horizontal"
        android:background="@color/color_red_fc2b"
        android:gravity="center"
        android:text="@string/agree"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_px_48" />
</RelativeLayout>