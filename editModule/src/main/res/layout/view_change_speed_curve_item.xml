<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:paddingBottom="@dimen/dp_px_30">

    <LinearLayout
        android:id="@+id/rl_content"
        android:layout_width="@dimen/dp49"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        tools:ignore="UseCompoundDrawables">

        <ImageView
            android:id="@+id/icon"
            android:layout_width="@dimen/dp49"
            android:layout_height="@dimen/dp49"
            android:contentDescription="@null" />

        <TextView
            android:id="@+id/name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_px_15"
            android:contentDescription="@null"
            android:ellipsize="end"
            android:singleLine="true"
            android:textColor="@color/white_8"
            android:textSize="@dimen/menu_item_text_size" />
    </LinearLayout>

    <FrameLayout
        android:id="@+id/fl_select_bg"
        android:layout_width="@dimen/dp_px_147"
        android:layout_height="@dimen/dp_px_147">
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:contentDescription="@null"
            android:drawableTop="@mipmap/icon_curve_edit"
            android:drawablePadding="@dimen/dp_px_15"
            android:gravity="center"
            android:text="@string/item_speed_curve_click_edit"
            android:textColor="@color/white"
            android:textSize="@dimen/sp9" />
    </FrameLayout>
</FrameLayout>