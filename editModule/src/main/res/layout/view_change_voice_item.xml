<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="@dimen/dp_px_170"
    android:layout_height="@dimen/dp_px_170"
    android:background="@drawable/bg_round_corner_solid_ff212">

    <ImageView
        android:layout_centerInParent="true"
        android:id="@+id/iv_cover"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:contentDescription="@null"
        android:paddingBottom="@dimen/dp_px_45"/>


    <TextView
        android:id="@+id/tv_name"
        android:layout_width="@dimen/dp_px_170"
        android:layout_height="@dimen/dp_px_45"
        android:layout_alignParentBottom="true"
        android:background="@drawable/shape_corn_half_corners_bottom"
        android:ellipsize="end"
        android:gravity="center"
        android:singleLine="true"
        android:textColor="@color/white_8"
        android:textSize="@dimen/sp_px_30" />

    <View
        android:id="@+id/v_mask"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@mipmap/bg_round_corner_red" />
</RelativeLayout>