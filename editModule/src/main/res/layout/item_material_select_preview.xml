<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/fl_content"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_px_250">

    <ImageView
        android:id="@+id/iv_material_cover"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:contentDescription="@null"
        android:scaleType="centerCrop" />

    <TextView
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:id="@+id/tv_material_type"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/dp_px_15"
        android:shadowColor="@color/black"
        android:shadowDx="2"
        android:shadowDy="2"
        android:shadowRadius="1"
        android:textColor="@color/white_8"
        android:textSize="@dimen/sp_px_27" />

    <ImageView
        android:contentDescription="@null"
        app:layout_constraintTop_toTopOf="@+id/tv_material_type"
        app:layout_constraintRight_toLeftOf="@+id/tv_material_type"
        app:layout_constraintBottom_toBottomOf="@+id/tv_material_type"
        android:id="@+id/iv_duration"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_px_39"
        android:src="@mipmap/material_video"/>

    <TextView
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:id="@+id/tv_selected_num"
        android:layout_width="@dimen/dp_px_60"
        android:layout_height="@dimen/dp_px_60"
        android:layout_gravity="end"
        android:layout_marginTop="@dimen/dp_px_12"
        android:layout_marginEnd="@dimen/dp_px_12"
        android:gravity="center"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_px_30" />

    <View
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:id="@+id/v_touch"
        android:layout_width="@dimen/dp_px_90"
        android:layout_height="@dimen/dp_px_90" />
    <View
        android:id="@+id/mask"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"/>

</androidx.constraintlayout.widget.ConstraintLayout>
