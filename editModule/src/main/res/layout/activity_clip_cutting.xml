<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/menu_bg"
    tools:context=".activity.ClipCuttingActivity">

    <com.meishe.myvideo.view.CuttingMenuView
        android:id="@+id/edit_cutting_menu_view"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp190_5"
        android:gravity="center"
        app:layout_constraintBottom_toBottomOf="parent" />

    <RelativeLayout
        android:id="@+id/edit_preview_view"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/dp32"
        android:paddingStart="@dimen/dp_px_30"
        android:paddingLeft="@dimen/dp_px_30"
        android:paddingRight="@dimen/dp_px_30"
        android:paddingEnd="@dimen/dp_px_30"
        app:layout_constraintBottom_toTopOf="@+id/edit_cutting_menu_view"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>