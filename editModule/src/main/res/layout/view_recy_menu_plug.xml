<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <LinearLayout
        android:layout_marginTop="@dimen/dp_px_9"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        tools:ignore="UseCompoundDrawables">

        <ImageView
            android:id="@+id/icon"
            android:layout_width="@dimen/dp_px_66"
            android:layout_height="@dimen/dp_px_66"
            android:contentDescription="@null" />

        <TextView
            android:id="@+id/name"
            android:layout_marginTop="@dimen/dp_px_6"
            android:layout_marginBottom="@dimen/dp_px_24"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:contentDescription="@null"
            android:ellipsize="end"
            android:singleLine="true"
            android:textColor="@color/white_8"
            android:textSize="@dimen/menu_item_text_size" />
    </LinearLayout>

</FrameLayout>