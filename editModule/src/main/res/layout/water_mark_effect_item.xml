<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/fl_layout"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">


    <ImageView
        android:background="@drawable/asset_effect_bg"
        android:id="@+id/iv_cover"
        android:layout_width="@dimen/dp_px_195"
        android:layout_height="@dimen/dp_px_195"
        android:layout_centerHorizontal="true"
        android:contentDescription="@null" />

    <TextView
        android:layout_below="@+id/iv_cover"
        android:layout_marginTop="@dimen/dp_px_23"
        android:id="@+id/tv_name"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_51"
        android:layout_centerHorizontal="true"
        android:gravity="center"
        android:text="@string/more"
        android:textColor="@color/color_ffd1d1d1"
        android:textSize="@dimen/sp_px_33" />

    <View
        android:layout_width="@dimen/dp_px_195"
        android:layout_height="@dimen/dp_px_195"
        android:id="@+id/v_mask" />
</RelativeLayout>