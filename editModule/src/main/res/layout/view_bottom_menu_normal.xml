<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">
    <View
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_width="@dimen/dp56"
        android:layout_height="1dp"/>
    <View
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_width="1dp"
        android:layout_height="@dimen/dp56"/>
    <TextView
        android:id="@+id/menu_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/white_8"
        android:textSize="@dimen/sp11"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <CheckBox
        android:id="@+id/menu_icon"
        android:button="@null"
        android:layout_width="@dimen/dp20"
        android:layout_height="@dimen/dp20"
        app:layout_constraintBottom_toTopOf="@+id/menu_name"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>