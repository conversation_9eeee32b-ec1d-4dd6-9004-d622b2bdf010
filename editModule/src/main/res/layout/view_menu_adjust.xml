<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">
    <com.meishe.business.assets.view.AssetsTypeTabView
        android:id="@+id/ttv_tab_type"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_102">
    </com.meishe.business.assets.view.AssetsTypeTabView>

    <RelativeLayout
        android:id="@+id/rl_top_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_px_36"
        android:visibility="gone">

        <ImageView
            android:id="@+id/iv_apply_all"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="@dimen/dp_px_30"
            android:layout_marginLeft="@dimen/dp_px_30"
            android:background="@mipmap/ic_multi_trans"
            android:contentDescription="@null" />

        <TextView
            android:id="@+id/tv_apply_all"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="@dimen/dp12"
            android:layout_marginLeft="@dimen/dp_px_36"
            android:layout_toEndOf="@+id/iv_apply_all"
            android:layout_toRightOf="@+id/iv_apply_all"
            android:text="@string/apply_all_clip"
            android:textColor="@color/white_8"
            android:textSize="@dimen/sp_px_30" />

        <View
            android:id="@+id/reset_left_line"
            android:layout_width="@dimen/dp_px_3"
            android:layout_height="@dimen/dp_px_45"
            android:layout_centerVertical="true"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="@dimen/dp_px_15"
            android:layout_marginLeft="@dimen/dp_px_15"
            android:layout_toEndOf="@+id/tv_apply_all"
            android:layout_toRightOf="@+id/tv_apply_all"
            android:background="@color/color_ff979797" />

        <TextView
            android:id="@+id/tv_reset"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_gravity="center"
            android:layout_marginStart="@dimen/dp_px_45"
            android:layout_marginLeft="@dimen/dp_px_45"
            android:layout_toEndOf="@+id/reset_left_line"
            android:layout_toRightOf="@+id/reset_left_line"
            android:text="@string/adjust_reset"
            android:textColor="@color/white_8"
            android:textSize="@dimen/sp_px_30" />

        <com.meishe.myvideo.view.MYSeekBarTextView
            android:id="@+id/view_seek_bar"
            android:layout_marginTop="@dimen/dp_px_12"
            android:layout_marginEnd="@dimen/dp_px_30"
            android:layout_marginRight="@dimen/dp_px_30"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_toEndOf="@+id/tv_reset"
            android:layout_toRightOf="@+id/tv_reset" />
    </RelativeLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/width_confirm_menu_recycleView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_px_30" />

    <TextView
        android:id="@+id/tv_hint"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_152"
        android:layout_marginTop="@dimen/dp_px_30"
        android:textColor="@color/white"
        android:layout_gravity="center"
        android:gravity="center"
        android:visibility="gone">
    </TextView>

    <View
        android:layout_width="match_parent"
        android:layout_marginTop="@dimen/dp_px_24"
        android:layout_height="@dimen/dp_px_1"
        android:layout_above="@+id/fl_confirm"
        android:background="@color/menu_divide_color" />

    <FrameLayout
        android:id="@+id/fl_confirm"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_225">

        <TextView
            android:id="@+id/tv_content"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/dp_px_48"
            android:textColor="@color/white_8"
            android:textSize="@dimen/sp_px_42" />

        <ImageView
            android:id="@+id/iv_confirm"
            android:layout_width="@dimen/dp_px_69"
            android:layout_height="@dimen/dp_px_69"
            android:layout_gravity="end"
            android:layout_marginTop="@dimen/dp_px_48"
            android:layout_marginEnd="@dimen/dp_px_39"
            android:layout_marginRight="@dimen/dp_px_39"
            android:background="@mipmap/ic_confirm"
            android:contentDescription="@null"
            android:padding="@dimen/dp_px_18" />
    </FrameLayout>
</LinearLayout>