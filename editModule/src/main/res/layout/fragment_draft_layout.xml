<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_draft_list"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:scrollbars="none" />

    <LinearLayout
        android:id="@+id/ll_no_draft_hint"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:gravity="center"
            android:text="@string/hint_main_draft_no_draft"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_px_42" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="@dimen/dp_px_45"
            android:gravity="center"
            android:text="@string/hint_main_draft_start_create"
            android:textColor="@color/color_ff6a6a6a"
            android:textSize="@dimen/sp_px_33" />
    </LinearLayout>

    <!--底部公用显示容器,注意，不要添加某一个单一view，只能使用容器-->
    <FrameLayout
        android:id="@+id/fl_bottom_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_gravity="bottom"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />
</FrameLayout>