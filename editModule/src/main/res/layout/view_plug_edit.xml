<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/color_ff181818"
    android:orientation="vertical">

    <RelativeLayout
        android:id="@+id/root_adjust"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_px_30">

        <RelativeLayout
            android:id="@+id/corona_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="invisible">

            <TextView
                android:id="@+id/tv_corona"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="@dimen/dp_px_60"
                android:text=""
                android:textColor="@color/white"
                android:textSize="@dimen/dp_px_30" />

            <com.meishe.base.view.ScaleView
                android:id="@+id/scaleview"
                android:layout_width="@dimen/dp_px_600"
                android:layout_height="@dimen/dp_px_105"
                android:layout_centerInParent="true"
                app:font_visible="false"
                app:high_pointer_margin="20"
                app:high_scale_color="@color/white"
                app:low_pointer_margin="20"
                app:low_scale_color="@color/color_767676"
                app:middle_pointer_margin="20"
                app:scale_ratio="1"
                app:middle_scale_color="@color/white"
                app:pointer_bottom_protruding="true"
                app:pointer_top_protruding="true"
                app:scale_position="center"
                app:show_baseLine="false"
                app:show_pointer_head="false" />

            <View
                android:layout_width="@dimen/dp_px_12"
                android:layout_height="@dimen/dp_px_12"
                android:layout_below="@+id/scaleview"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="@dimen/dp_px_27"
                android:background="@drawable/bg_red_circle_ff365e" />
        </RelativeLayout>

        <LinearLayout
            android:id="@+id/root_key_frame"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="@dimen/dp_px_60">

            <ImageView
                android:id="@+id/iv_add_key_frame_curve"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="@dimen/dp_px_45"
                android:visibility="invisible"
                android:src="@mipmap/icon_add_key_frame_curve_unselect" />

            <ImageView
                android:id="@+id/iv_add_key_frame"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="invisible"
                android:src="@mipmap/icon_add_key_frame" />

        </LinearLayout>

        <com.meishe.myvideo.view.ColorSeekBar
            android:id="@+id/corlor_seekbar"
            android:layout_width="@dimen/dp_px_750"
            android:layout_height="@dimen/dp_px_105"
            android:layout_centerVertical="true"
            android:layout_marginLeft="@dimen/dp_px_48"
            android:visibility="invisible"
            app:barHeight="@dimen/dp_px_48"
            app:thumbHeight="@dimen/dp_px_18" />

        <com.google.android.material.tabs.TabLayout
            android:id="@+id/tab_layout"
            android:layout_width="wrap_content"
            app:tabRippleColor="@null"
            android:layout_height="@dimen/dp_px_105"
            android:visibility="invisible"
            app:tabIndicatorColor="@color/color_ffff365E"
            app:tabIndicatorFullWidth="false"
            app:tabIndicatorHeight="@dimen/dp_px_1"
            app:tabMaxWidth="@dimen/dp_px_240"
            app:tabMinWidth="@dimen/dp_px_120"
            app:tabMode="scrollable"
            app:tabSelectedTextColor="@color/color_ffff365E"
            app:tabTextAppearance="@style/cut_tab_layout_style"
            app:tabTextColor="@color/white" />
    </RelativeLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_174"
        android:layout_marginLeft="@dimen/dp_px_24"
        android:layout_marginTop="@dimen/dp_px_18"
        android:layout_marginBottom="@dimen/dp_px_90"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/iv_back"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@mipmap/icon_back_plug_menu" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerview"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="@dimen/dp_px_75"
            android:layout_toRightOf="@+id/iv_back" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/plug_recyclerview"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_toLeftOf="@+id/image_add_plug_menu"
            android:layout_toRightOf="@+id/iv_back" />

        <TextView
            android:id="@+id/tv_add_plug_desc"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:text="@string/tv_add_plug_desc"
            android:textColor="@color/white"
            android:textSize="@dimen/dp_px_27" />

        <ImageView
            android:id="@+id/image_add_plug_menu"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:src="@mipmap/icon_add_plug_menu" />
    </RelativeLayout>

</LinearLayout>