<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_px_255">

    <ImageView
        android:id="@+id/iv_cover"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"
        android:contentDescription="@null" />

    <TextView
        android:id="@+id/tv_selected"
        android:layout_width="wrap_content"
        android:minWidth="@dimen/dp_px_105"
        android:layout_height="@dimen/dp_px_45"
        android:layout_marginTop="@dimen/dp_px_12"
        android:layout_marginLeft="@dimen/dp_px_12"
        android:layout_marginStart="@dimen/dp_px_12"
        android:background="@drawable/bg_rectangle_round_white"
        android:gravity="center"
        android:text="@string/imported"
        android:textColor="@color/black"
        android:textSize="@dimen/sp_px_27"
        android:visibility="invisible" />

    <TextView
        android:id="@+id/tv_duration"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_alignParentBottom="true"
        android:layout_marginEnd="@dimen/dp_px_12"
        android:layout_marginBottom="@dimen/dp_px_12"
        android:gravity="center_vertical"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_px_30" />
    <ImageView
        android:id="@+id/iv_duration"
        android:layout_toStartOf="@+id/tv_duration"
        android:layout_marginBottom="@dimen/dp_px_12"
        android:layout_alignParentBottom="true"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_px_39"
        android:src="@mipmap/material_video"/>
    <TextView
        android:id="@+id/tv_selected_num"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/color_ccff365e"
        android:gravity="center"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_px_63"
        android:visibility="invisible" />
</RelativeLayout>
