<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/menu_bg">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp36"
        android:layout_marginTop="@dimen/dp20"
        android:orientation="horizontal">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:layout_marginLeft="@dimen/dp17"
            android:text="@string/sub_menu_name_edit_rotation"
            android:textColor="@color/white_8"
            android:textSize="@dimen/sp10"
            android:visibility="gone"/>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingLeft="@dimen/dp_px_90"
            android:paddingRight="@dimen/dp_px_90"
            android:paddingEnd="@dimen/dp_px_90"
            android:paddingStart="@dimen/dp_px_90"
            android:orientation="vertical">
            <TextView
                android:id="@+id/tv_angle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/white"
                android:textSize="11sp"
                android:layout_marginBottom="@dimen/dp5" />
            <com.meishe.myvideo.view.MYSeekBarView
                android:id="@+id/view_seek_bar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"/>
        </LinearLayout>
    </LinearLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/ratio_recyclerView"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp40"
        android:layout_above="@+id/layout_bottom"
        android:layout_marginBottom="@dimen/dp15"
        android:scrollbars="none">
    </androidx.recyclerview.widget.RecyclerView>

    <RelativeLayout
        android:id="@+id/layout_bottom"
        android:layout_width="match_parent"
        android:layout_height="49dp"
        android:layout_alignParentBottom="true">

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp0p2"
            android:layout_alignParentTop="true"
            android:background="@color/menu_divide_color" />

        <TextView
            android:id="@+id/tv_reset"
            android:layout_width="40dp"
            android:gravity="center_vertical|left"
            android:layout_height="match_parent"
            android:layout_marginLeft="@dimen/dp15"
            android:textColor="@color/white_8"
            android:layout_centerVertical="true"
            android:text="@string/adjust_reset"
            android:textSize="@dimen/sp12" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:text="@string/sub_menu_name_edit_cut"
            android:textColor="@color/white_8"
            android:textSize="@dimen/sp14" />

        <ImageView
            android:id="@+id/iv_confirm"
            android:paddingTop="13dp"
            android:paddingBottom="13dp"
            android:layout_width="@dimen/dp35"
            android:layout_height="match_parent"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="@dimen/dp7"
            android:src="@mipmap/ic_confirm" />

    </RelativeLayout>
</RelativeLayout>