<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="center_horizontal">

    <FrameLayout
        android:id="@+id/root_cover"
        android:layout_width="@dimen/dp_px_165"
        android:layout_height="@dimen/dp_px_165"
        android:layout_gravity="center_horizontal"
        android:background="@drawable/asset_effect_bg">

        <ImageView
            android:id="@+id/iv_cover"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:contentDescription="@null"
            android:scaleType="fitXY" />

        <ImageView
            android:id="@+id/iv_downloading"
            android:layout_width="@dimen/dp_px_165"
            android:layout_height="@dimen/dp_px_165"
            android:contentDescription="@null"
            android:visibility="gone" />

        <ProgressBar
            android:id="@+id/pb_download"
            android:layout_width="@dimen/dp_px_36"
            android:layout_height="@dimen/dp_px_36"
            android:layout_gravity="end|bottom"
            android:layout_marginEnd="@dimen/dp_px_6"
            android:layout_marginRight="@dimen/dp_px_6"
            android:layout_marginBottom="@dimen/dp_px_6"
            android:indeterminateDrawable="@drawable/progressbar_refresh"
            android:max="100"
            android:visibility="gone" />
        <ImageView
            android:id="@+id/iv_download"
            android:layout_width="@dimen/asset_purchased_size"
            android:layout_height="@dimen/asset_purchased_size"
            android:layout_gravity="end"
            android:layout_marginTop="@dimen/asset_purchased_margin_top"
            android:layout_marginEnd="@dimen/asset_download_margin_right"
            android:layout_marginRight="@dimen/asset_download_margin_right"
            android:contentDescription="@null"
            android:visibility="gone"
            android:src="@mipmap/animation_download_icon" />
    </FrameLayout>

    <TextView
        android:id="@+id/tv_name"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_54"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/dp_px_180"
        android:background="@color/effect_name_bg"
        android:ellipsize="end"
        android:gravity="center"
        android:singleLine="true"
        android:textColor="@color/white_8"
        android:textSize="@dimen/sp_px_24" />
</FrameLayout>