<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dp_px_36"
        android:layout_marginRight="@dimen/dp_px_36"
        android:background="@drawable/bg_item_plug"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        tools:ignore="MissingConstraints">

        <com.facebook.drawee.view.SimpleDraweeView
            android:id="@+id/image"
            android:layout_width="@dimen/dp_px_180"
            android:layout_height="@dimen/dp_px_180"
            android:layout_marginLeft="@dimen/dp_px_30"
            android:layout_marginTop="@dimen/dp_px_24"
            android:layout_marginBottom="@dimen/dp_px_24" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dp_px_30"
            android:orientation="vertical">

            <TextView
                android:id="@+id/title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/white"
                android:textSize="@dimen/dp_px_42" />

            <TextView
                android:id="@+id/content"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_px_42"
                android:textColor="@color/black_868686"
                android:textSize="@dimen/sp_px_33" />
        </LinearLayout>
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>