<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <ImageView
        android:layout_marginTop="@dimen/dp_px_92"
        android:id="@+id/iv_cover"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:scaleType="centerCrop"
        android:layout_width="@dimen/dp_px_180"
        android:layout_height="@dimen/dp_px_180"/>

    <ImageView
        android:id="@+id/iv_icon"
        app:layout_constraintTop_toTopOf="@+id/iv_cover"
        app:layout_constraintBottom_toTopOf="@+id/tv_hint"
        app:layout_constraintLeft_toLeftOf="@+id/iv_cover"
        app:layout_constraintRight_toRightOf="@+id/iv_cover"
        app:layout_constraintVertical_chainStyle="packed"
        android:layout_width="@dimen/dp_px_54"
        android:layout_height="@dimen/dp_px_54"
        android:src="@mipmap/icon_cover_edit_replace"/>

    <TextView
        android:id="@+id/tv_hint"
        app:layout_constraintTop_toBottomOf="@+id/iv_icon"
        app:layout_constraintBottom_toBottomOf="@+id/iv_cover"
        app:layout_constraintLeft_toLeftOf="@+id/iv_icon"
        app:layout_constraintRight_toRightOf="@+id/iv_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/cover_edit_replace_icon_hint"
        android:textSize="@dimen/sp_px_30"
        android:textColor="@color/white"/>
</androidx.constraintlayout.widget.ConstraintLayout>