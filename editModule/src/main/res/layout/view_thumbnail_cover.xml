<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <View
        android:id="@+id/v_empty_animation_in"
        android:layout_width="@dimen/dp_px_0"
        android:layout_height="match_parent"
        android:layout_gravity="center_vertical"
        android:background="@color/color_55ff0000"
        android:visibility="gone" />
    <View
        android:id="@+id/v_empty_animation_out"
        android:layout_width="@dimen/dp_px_0"
        android:layout_height="match_parent"
        android:layout_gravity="center_vertical"
        android:background="@color/color_8050E3C2"
        android:visibility="gone" />
    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_px_36"
        android:orientation="horizontal"
        android:layout_marginStart="@dimen/dp_px_9"
        android:layout_marginLeft="@dimen/dp_px_9"
        android:layout_marginTop="@dimen/dp_px_9">
        <LinearLayout
            android:id="@+id/ll_speed_container"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:background="@drawable/bg_audio_draw_text"
            android:orientation="horizontal"
            android:paddingStart="@dimen/dp_px_9"
            android:paddingLeft="@dimen/dp_px_9"
            android:paddingEnd="@dimen/dp_px_9"
            android:paddingRight="@dimen/dp_px_9"
            tools:ignore="UseCompoundDrawables">

            <ImageView
                android:id="@+id/iv_speed"
                android:layout_width="@dimen/dp_px_21"
                android:layout_height="@dimen/dp_px_21"
                android:layout_gravity="center_vertical"
                android:contentDescription="@null"
                android:src="@mipmap/sub_menu_icon_edit_speed" />

            <TextView
                android:id="@+id/tv_speed"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="@dimen/dp_px_6"
                android:ellipsize="end"
                android:singleLine="true"
                android:textColor="@color/white_8"
                android:textSize="@dimen/sp_px_24" />
        </LinearLayout>

        <ImageView
            android:background="@drawable/bg_audio_draw_text"
            android:id="@+id/iv_face_icon"
            android:padding="@dimen/dp_px_3"
            android:layout_marginLeft="@dimen/dp_px_6"
            android:layout_marginStart="@dimen/dp_px_6"
            android:contentDescription="@null"
            android:layout_width="@dimen/dp_px_36"
            android:layout_height="@dimen/dp_px_36"
            android:layout_gravity="center_vertical"
            android:visibility="gone"
            android:src="@mipmap/icon_face"/>

        <ImageView
            android:background="@drawable/bg_audio_draw_text"
            android:id="@+id/iv_volume_icon"
            android:padding="@dimen/dp_px_3"
            android:layout_marginLeft="@dimen/dp_px_6"
            android:layout_marginStart="@dimen/dp_px_6"
            android:contentDescription="@null"
            android:layout_width="@dimen/dp_px_36"
            android:layout_height="@dimen/dp_px_36"
            android:layout_gravity="center_vertical"
            android:visibility="gone"
            android:src="@mipmap/icon_volume"/>
    </LinearLayout>

    <FrameLayout
        android:id="@+id/fl_key_frame_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:visibility="invisible"
        />
</merge>