<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/item_main"
    android:layout_width="@dimen/dp_px_660"
    android:layout_height="@dimen/dp_px_375"
    android:background="@drawable/draft_dialog_bg">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="@dimen/dp_px_90"
        android:text="@string/draft_delete_confirm"
        android:textColor="@color/white_8"
        android:textSize="@dimen/sp_px_42" />

    <TextView
        android:id="@+id/tv_delete"
        android:layout_width="@dimen/dp_px_180"
        android:layout_height="@dimen/dp_px_100"
        android:layout_below="@+id/tv_title"
        android:layout_marginStart="@dimen/dp_px_100"
        android:layout_marginLeft="@dimen/dp_px_100"
        android:layout_marginTop="@dimen/dp_px_50"
        android:background="@color/white"
        android:gravity="center"
        android:text="@string/delete"
        android:textColor="@color/color_333333"
        android:textSize="@dimen/sp_px_42" />

    <TextView
        android:id="@+id/tv_cancel"
        android:layout_width="@dimen/dp_px_180"
        android:layout_height="@dimen/dp_px_100"
        android:layout_below="@+id/tv_title"
        android:layout_alignParentEnd="true"
        android:layout_alignParentRight="true"
        android:layout_marginTop="@dimen/dp_px_50"
        android:layout_marginEnd="@dimen/dp_px_100"
        android:layout_marginRight="@dimen/dp_px_100"
        android:background="@color/colorTranslucent"
        android:gravity="center"
        android:minWidth="@dimen/dp_px_190"
        android:minHeight="@dimen/dp_px_50"
        android:text="@string/activity_cut_export_template_cancel"
        android:textColor="@color/white_8"
        android:textSize="@dimen/sp_px_42" />


</RelativeLayout>
