<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="@dimen/dp_px_720"
    android:layout_height="@dimen/dp_px_690"
    android:background="@color/white">

    <ImageView
        android:id="@+id/iv_close"
        android:layout_width="@dimen/dp_px_36"
        android:layout_height="@dimen/dp_px_36"
        android:layout_marginTop="@dimen/dp_px_30"
        android:layout_marginEnd="@dimen/dp_px_30"
        android:layout_marginRight="@dimen/dp_px_30"
        android:background="@mipmap/close_black"
        android:contentDescription="@null"
        android:padding="@dimen/dp_px_12"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageButton
        android:background="@color/transparent"
        android:src="@mipmap/icon_only_video_selected"
        android:id="@+id/btn_only_video"
        android:layout_width="@dimen/dp_px_135"
        android:layout_height="@dimen/dp_px_135"
        android:layout_marginTop="@dimen/dp_px_78"
        android:contentDescription="@null"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/btn_only_record"
        app:layout_constraintTop_toBottomOf="@+id/tv_top_hint" />

    <ImageButton
        android:background="@color/transparent"
        android:src="@mipmap/icon_only_record_unselected"
        android:layout_marginTop="@dimen/dp_px_78"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_top_hint"
        android:id="@+id/btn_only_record"
        android:layout_width="@dimen/dp_px_135"
        android:layout_height="@dimen/dp_px_135"
        android:contentDescription="@null" />

    <ImageButton
        android:background="@color/transparent"
        android:src="@mipmap/icon_all_unselected"
        android:layout_marginTop="@dimen/dp_px_78"
        app:layout_constraintLeft_toRightOf="@+id/btn_only_record"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_top_hint"
        android:id="@+id/btn_all"
        android:layout_width="@dimen/dp_px_135"
        android:layout_height="@dimen/dp_px_135"
        android:contentDescription="@null"
        app:layout_constraintHorizontal_chainStyle="packed" />

    <TextView
        android:id="@+id/tv_only_video"
        android:layout_width="@dimen/dp_px_135"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:textColor="@color/color_ff101010"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/dp_px_16"
        android:text="@string/identify_caption_pop_selector_video"
        android:textSize="@dimen/sp_px_30"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/tv_only_record"
        app:layout_constraintTop_toBottomOf="@+id/btn_only_video" />

    <TextView
        android:layout_marginTop="@dimen/dp_px_16"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/btn_only_record"
        android:gravity="center"
        android:id="@+id/tv_only_record"
        android:layout_width="@dimen/dp_px_135"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:textColor="@color/color_ffc1c1c1"
        android:text="@string/identify_caption_pop_selector_record"
        android:textSize="@dimen/sp_px_30" />

    <TextView
        android:layout_marginTop="@dimen/dp_px_16"
        app:layout_constraintLeft_toRightOf="@+id/tv_only_record"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/btn_all"
        android:id="@+id/tv_all"
        android:gravity="center"
        android:layout_width="@dimen/dp_px_135"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:textColor="@color/color_ff101010"
        android:text="@string/identify_caption_pop_selector_all"
        android:textSize="@dimen/sp_px_30" />

    <RadioButton
        android:id="@+id/rb_clear_caption"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_px_15"
        android:layout_marginRight="@dimen/dp_px_15"
        android:button="@drawable/selector_rb_red"
        app:layout_constraintBottom_toBottomOf="@+id/tv_clear_caption"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintRight_toLeftOf="@+id/tv_clear_caption"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_clear_caption" />


    <TextView
        android:id="@+id/tv_top_hint"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_px_45"
        android:text="@string/identify_caption_pop_hint_top"
        android:textColor="@color/black"
        android:textSize="@dimen/sp_px_36"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <Button
        android:id="@+id/bt_start_identify"
        android:layout_width="@dimen/dp_px_480"
        android:layout_height="@dimen/dp_px_90"
        android:layout_marginBottom="@dimen/dp_px_42"
        android:background="@color/color_fffc2b55"
        android:text="@string/start_identify"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_px_36"
        app:layout_constraintBottom_toTopOf="@+id/tv_clear_caption"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <TextView
        android:id="@+id/tv_clear_caption"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="@dimen/dp_px_51"
        android:layout_marginLeft="@dimen/dp_px_51"
        android:layout_marginBottom="@dimen/dp_px_54"
        android:text="@string/clear_caption"
        android:textColor="@color/black"
        android:textSize="@dimen/sp_px_36"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>