<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/black">
    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tab_layout"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_px_132"
        app:tabIndicator="@drawable/layer_tab_indicator_draft"
        app:tabMode="scrollable"
        app:tabIndicatorFullWidth="false"
        app:tabIndicatorColor="@color/white"
        app:tabSelectedTextColor="@color/white"
        app:tabTextAppearance="@style/draftTabLayoutTextStyle"
        app:tabTextColor="@color/white_5" />
    <com.meishe.base.view.MViewPager
        android:id="@+id/vp_pager"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/tab_layout"/>

    <TextView
        android:id="@+id/tv_draft"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_margin="@dimen/dp_px_45"
        android:text="@string/draft_box"
        android:textColor="@color/white_8"
        android:textSize="@dimen/sp_px_42"
        android:visibility="gone"/>


    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_px_132"
        android:layout_alignParentEnd="true"
        android:orientation="horizontal">
        <ImageButton
            android:contentDescription="@null"
            android:id="@+id/ib_refresh"
            android:layout_width="@dimen/dp_px_132"
            android:layout_height="@dimen/dp_px_132"
            android:background="@android:color/transparent"
            android:layout_marginEnd="@dimen/dp_px_21"
            android:visibility="gone"
            android:src="@mipmap/ic_draft_refresh" />
        <TextView
            android:id="@+id/tv_draft_manager"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_px_132"
            android:gravity="center"
            android:text="@string/draft_manage"
            android:textColor="@color/white_8"
            android:layout_marginEnd="@dimen/dp_px_45"
            android:textSize="@dimen/sp_px_36" />
    </LinearLayout>
</RelativeLayout>