<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="@dimen/dp_px_630"
    android:background="@drawable/bg_top_round_corners_solid_white">


    <TextView
        android:id="@+id/tv_account"
        android:layout_width="@dimen/dp_px_150"
        android:layout_height="@dimen/dp_px_100"
        android:layout_marginStart="@dimen/dp_px_90"
        android:layout_marginLeft="@dimen/dp_px_90"
        android:layout_marginTop="@dimen/dp_px_120"
        android:gravity="center_vertical"
        android:text="@string/dialog_account_number"
        android:textSize="@dimen/sp_px_39" />

    <EditText
        android:id="@+id/et_account"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_100"
        android:layout_marginStart="@dimen/dp_px_15"
        android:layout_marginLeft="@dimen/dp_px_15"
        android:layout_marginTop="@dimen/dp_px_120"
        android:layout_marginEnd="@dimen/dp30"
        android:layout_marginRight="@dimen/dp30"
        android:layout_toEndOf="@+id/tv_account"
        android:layout_toRightOf="@+id/tv_account"
        android:gravity="center_vertical"
        android:hint="@string/dialog_hint_input_phone"
        android:importantForAutofill="no"
        android:inputType="number"
        android:textColorHint="@color/white_ffa4a4"
        android:textSize="@dimen/sp_px_36" />


    <TextView
        android:id="@+id/tv_password"
        android:layout_width="@dimen/dp_px_150"
        android:layout_height="@dimen/dp_px_100"
        android:layout_below="@+id/tv_account"
        android:layout_marginStart="@dimen/dp_px_90"
        android:layout_marginLeft="@dimen/dp_px_90"
        android:layout_marginTop="@dimen/dp_px_24"
        android:gravity="center_vertical"
        android:text="@string/dialog_account_pwd"
        android:textSize="@dimen/sp_px_39" />

    <EditText
        android:id="@+id/et_password"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_100"
        android:layout_below="@+id/tv_account"
        android:layout_marginStart="@dimen/dp_px_15"
        android:layout_marginLeft="@dimen/dp_px_15"
        android:layout_marginTop="@dimen/dp_px_24"
        android:layout_marginEnd="@dimen/dp30"
        android:layout_marginRight="@dimen/dp30"
        android:layout_toEndOf="@+id/tv_password"
        android:layout_toRightOf="@+id/tv_password"
        android:gravity="center_vertical"
        android:hint="@string/dialog_hint_input_pwd"
        android:importantForAutofill="no"
        android:inputType="textPassword"
        android:textColorHint="@color/white_ffa4a4"
        android:textSize="@dimen/sp_px_36" />

    <TextView
        android:id="@+id/tv_upload"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_114"
        android:layout_alignParentBottom="true"
        android:layout_marginLeft="@dimen/dp_px_90"
        android:layout_marginRight="@dimen/dp_px_90"
        android:layout_marginBottom="@dimen/dp_px_90"
        android:background="@drawable/bg_upload_default"
        android:gravity="center"
        android:text="@string/draft_upload"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_px_39" />
</RelativeLayout>