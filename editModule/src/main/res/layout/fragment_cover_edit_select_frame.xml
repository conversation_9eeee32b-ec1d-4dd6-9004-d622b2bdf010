<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <com.meishe.player.view.TimelineFrameSelectView
        android:id="@+id/frame_select_view"
        app:layout_constraintTop_toTopOf="@+id/v_line"
        app:layout_constraintBottom_toBottomOf="@+id/v_line"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_180"/>

    <View
        android:id="@+id/v_line"
        android:layout_marginTop="@dimen/dp_px_84"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_width="@dimen/dp_px_6"
        android:layout_height="@dimen/dp_px_195"
        android:background="@color/white"/>

    <TextView
        app:layout_constraintTop_toBottomOf="@+id/v_line"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        android:layout_marginTop="@dimen/dp_px_30"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/cover_edit_select_frame_hint"
        android:textSize="@dimen/sp_px_30"
        android:textColor="@color/white_4"/>
</androidx.constraintlayout.widget.ConstraintLayout>