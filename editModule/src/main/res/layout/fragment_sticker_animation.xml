<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <com.meishe.business.assets.view.AssetsTypeTabView
        android:id="@+id/ttv_tab_type"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_102" />

    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tab_layout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_132"
        app:tabIndicator="@null"
        app:tabMode="scrollable"
        app:tabSelectedTextColor="@color/color_fffc2b55"
        app:tabTextAppearance="@style/subTabLayoutTextStyle"
        app:tabTextColor="@color/color_ff909293" />

    <com.meishe.base.view.MeasureCustomViewPager
        android:id="@+id/viewPager"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <FrameLayout
        android:layout_marginTop="-70dp"
        android:id="@+id/seek_bar_layout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_210"
        android:visibility="visible">

        <TextView
            android:id="@+id/tv_animation_group_fast"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:gravity="center"
            android:layout_marginStart="@dimen/dp_px_60"
            android:layout_marginTop="@dimen/dp_px_30"
            android:text="@string/fragment_caption_animation_fast"
            android:textColor="@color/white_8"
            android:textSize="@dimen/sp_px_30" />

        <TextView
            android:id="@+id/tv_animation_group_slow"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_px_30"
            android:layout_marginRight="@dimen/dp_px_60"
            android:layout_gravity="center_vertical|right"
            android:gravity="center"
            android:text="@string/fragment_caption_animation_slow"
            android:textColor="@color/white_8"
            android:textSize="@dimen/sp_px_30" />

        <com.meishe.base.view.HorizontalSeekBar
            android:id="@+id/seek_bar"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center_vertical"
            app:bigValue="100"
            app:hasRule="false"
            app:imageHeight="@dimen/dp_px_111"
            app:imageLeft="@mipmap/icon_animation_in"
            app:imageLowPadding="0dp"
            app:imageRight="@mipmap/icon_animation_out"
            app:imageWidth="@dimen/dp_px_69"
            app:inColor="@color/white"
            app:leftOutColor="@color/color_ff50e3c2"
            app:leftTextColor="@color/color_ff50e3c2"
            app:leftTextSize="@dimen/sp_px_30"
            app:lineHeight="@dimen/dp_px_6"
            app:lineLength="@dimen/dp_px_786"
            app:middleOutColor="@color/white"
            app:middleTextColor="@color/white_8"
            app:rightOutColor="@color/color_fffc2b55"
            app:rightTextColor="@color/color_fffc2b55"
            app:rightTextSize="@dimen/sp_px_30"
            app:unit="s" />
    </FrameLayout>

    <View
        android:id="@+id/line"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_1"
        android:layout_marginTop="@dimen/dp_px_24"
        android:background="@color/menu_divide_color" />

    <FrameLayout
        android:id="@+id/fl_confirm"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_225">
        <TextView
            android:id="@+id/tv_content"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/dp_px_48"
            android:textColor="@color/white_8"
            android:text="@string/tab_name_sticker_animation"
            android:textSize="@dimen/sp_px_42" />

        <ImageView
            android:id="@+id/iv_confirm"
            android:layout_width="@dimen/dp_px_69"
            android:layout_height="@dimen/dp_px_69"
            android:layout_gravity="end"
            android:layout_marginTop="@dimen/dp_px_48"
            android:layout_marginEnd="@dimen/dp_px_39"
            android:layout_marginRight="@dimen/dp_px_39"
            android:background="@mipmap/ic_confirm"
            android:contentDescription="@null"
            android:padding="@dimen/dp_px_18" />
    </FrameLayout>
</LinearLayout>