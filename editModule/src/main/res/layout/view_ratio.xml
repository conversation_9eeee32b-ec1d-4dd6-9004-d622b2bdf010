<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp70"
    >

    <HorizontalScrollView
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp45"
        android:layout_centerVertical="true"
        android:orientation="horizontal">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:orientation="horizontal">

            <RelativeLayout
                android:id="@+id/rl_original"
                android:layout_width="@dimen/dp35"
                android:layout_height="@dimen/dp45"
                android:layout_marginLeft="@dimen/dp13"
                android:background="@drawable/ratio_bg">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:shadowColor="#ff000000"
                    android:shadowRadius="3.0"
                    android:text="@string/ratio_original"
                    android:textColor="@drawable/view_ratio_text_color"
                    android:textSize="10sp" />
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/rl_9_16"
                android:layout_width="@dimen/dp30"
                android:layout_height="@dimen/dp45"
                android:layout_marginLeft="@dimen/dp15"
                android:background="@drawable/ratio_bg">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:shadowColor="#ff000000"
                    android:shadowRadius="3.0"
                    android:text="@string/ratio_9_16"
                    android:textColor="@drawable/view_ratio_text_color"
                    android:textSize="10sp" />
            </RelativeLayout>


            <RelativeLayout
                android:id="@+id/rl_3_4"
                android:layout_width="@dimen/dp34"
                android:layout_height="@dimen/dp45"
                android:layout_marginLeft="@dimen/dp15"
                android:background="@drawable/ratio_bg">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:shadowColor="#ff000000"
                    android:shadowRadius="3.0"
                    android:text="@string/ratio_3_4"
                    android:textColor="@drawable/view_ratio_text_color"
                    android:textSize="10sp" />
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/rl_1_1"
                android:layout_width="@dimen/dp45"
                android:layout_height="@dimen/dp45"
                android:layout_marginLeft="@dimen/dp15"
                android:background="@drawable/ratio_bg">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:shadowColor="#ff000000"
                    android:shadowRadius="3.0"
                    android:text="@string/ratio_1_1"
                    android:textColor="@drawable/view_ratio_text_color"
                    android:textSize="10sp" />
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/rl_4_3"
                android:layout_width="@dimen/dp45"
                android:layout_height="@dimen/dp34"
                android:layout_gravity="bottom"
                android:layout_marginLeft="@dimen/dp15"
                android:background="@drawable/ratio_bg">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:shadowColor="#ff000000"
                    android:shadowRadius="3.0"
                    android:text="@string/ratio_4_3"
                    android:textColor="@drawable/view_ratio_text_color"
                    android:textSize="10sp" />
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/rl_16_9"
                android:layout_width="@dimen/dp45"
                android:layout_height="@dimen/dp26"
                android:layout_marginLeft="@dimen/dp15"
                android:layout_marginTop="@dimen/dp19"
                android:layout_marginRight="@dimen/dp30"
                android:background="@drawable/ratio_bg">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:shadowColor="#ff000000"
                    android:shadowRadius="3.0"
                    android:text="@string/ratio_16_9"
                    android:textColor="@drawable/view_ratio_text_color"
                    android:textSize="10sp" />
            </RelativeLayout>

        </LinearLayout>

    </HorizontalScrollView>

</RelativeLayout>