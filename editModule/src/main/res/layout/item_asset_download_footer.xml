<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <LinearLayout
        android:id="@+id/loadLayout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_centerInParent="true">
        <ProgressBar
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:indeterminateDrawable="@drawable/progressbar_refresh" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="15dp"
            android:text="@string/loading"
            android:textColor="#4DB6AC"
            android:textSize="16sp" />
    </LinearLayout>

    <FrameLayout
        android:id="@+id/loadFailTips"
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:visibility="gone">
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="@string/network_strayed_try"
            android:textColor="#4DB6AC"
            android:textSize="16sp" />

    </FrameLayout>

</RelativeLayout>
