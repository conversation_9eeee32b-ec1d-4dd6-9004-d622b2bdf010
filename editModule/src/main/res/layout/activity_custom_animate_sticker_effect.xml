<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tl="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black"
    android:orientation="vertical">

    <com.meishe.base.view.CustomTitleBar
        android:id="@+id/title_bar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_192"
        android:layout_marginTop="@dimen/dp_px_96" />

    <FrameLayout
        android:id="@+id/fl_fragment_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@+id/tab_layout"
        android:layout_below="@+id/title_bar"
        android:background="@color/animate_sticker_middle_bg"
        android:visibility="invisible"
        />

    <ImageView
        android:id="@+id/iv_sticker"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_above="@+id/tab_layout"
        android:layout_below="@+id/title_bar"
        android:layout_centerHorizontal="true"
        android:contentDescription="@null"
        android:scaleType="fitCenter" />

    <com.meishe.third.tablayout.SlidingTabLayout
        android:id="@+id/tab_layout"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_px_132"
        android:layout_above="@+id/v_bottom_line"
        tl:tl_indicator_height="@dimen/dp_px_4"
        tl:tl_indicator_width="@dimen/dp_px_30"
        tl:tl_textSelectColor="@color/white"
        tl:tl_textSize="@dimen/sp_px_36"
        tl:tl_textUnselectedColor="@color/white_909"/>

    <ImageView
        android:id="@+id/iv_confirm"
        android:layout_width="@dimen/dp_px_120"
        android:layout_height="@dimen/dp_px_75"
        android:layout_alignTop="@+id/tab_layout"
        android:layout_alignParentEnd="true"
        android:layout_alignParentRight="true"
        android:layout_marginTop="@dimen/dp_px_28"
        android:layout_marginEnd="@dimen/dp_px_45"
        android:layout_marginRight="@dimen/dp_px_45"
        android:contentDescription="@null"
        android:paddingStart="@dimen/dp_px_22"
        android:paddingLeft="@dimen/dp_px_22"
        android:paddingEnd="@dimen/dp_px_22"
        android:paddingRight="@dimen/dp_px_22"
        android:src="@mipmap/ic_record_confirm" />

    <View
        android:id="@+id/v_bottom_line"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_1"
        android:layout_above="@+id/view_pager"
        android:background="@color/menu_divide_color" />

    <androidx.viewpager.widget.ViewPager
        android:id="@+id/view_pager"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_650"
        android:layout_alignParentBottom="true" />

</RelativeLayout>
