<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_px_760">

    <TextView
        android:id="@+id/tv_point"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_px_75"
        android:layout_alignParentEnd="true"
        android:layout_alignParentRight="true"
        android:layout_marginTop="@dimen/dp_px_24"
        android:layout_marginEnd="@dimen/dp_px_60"
        android:layout_marginRight="@dimen/dp_px_60"
        android:background="@drawable/bg_confirm"
        android:gravity="center_vertical"
        android:minWidth="@dimen/dp_px_180"
        android:paddingStart="@dimen/dp_px_75"
        android:paddingLeft="@dimen/dp_px_75"
        android:paddingEnd="@dimen/dp_px_24"
        android:paddingRight="@dimen/dp_px_24"
        android:text="@string/tv_point_add"
        android:textColor="@color/white"
        android:textSize="@dimen/sp9"
        tools:ignore="RelativeOverlap" />

    <ImageView
        android:id="@+id/iv_point"
        android:layout_width="@dimen/dp_px_27"
        android:layout_height="@dimen/dp_px_27"
        android:layout_alignStart="@+id/tv_point"
        android:layout_alignLeft="@+id/tv_point"
        android:layout_marginStart="@dimen/dp_px_24"
        android:layout_marginLeft="@dimen/dp_px_24"
        android:layout_marginTop="@dimen/dp_px_48"
        android:contentDescription="@null"
        android:src="@mipmap/icon_add_point" />


    <View
        android:id="@+id/v_point_mask"
        android:layout_width="@dimen/dp_px_200"
        android:layout_height="@dimen/dp_px_75"
        android:layout_alignParentEnd="true"
        android:layout_alignParentRight="true"
        android:layout_marginTop="@dimen/dp_px_24"
        android:layout_marginEnd="@dimen/dp_px_60"
        android:layout_marginRight="@dimen/dp_px_60"
        android:background="@color/black_half"
        android:visibility="gone" />

    <com.meishe.base.view.NvPlugBezierView
        android:id="@+id/speed_view"
        android:clickable="true"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_498"
        android:layout_below="@+id/tv_point"
        android:layout_marginLeft="@dimen/dp_px_36"
        android:layout_marginTop="@dimen/dp_px_24"
        android:layout_marginRight="@dimen/dp_px_36"
        android:background="@color/bezier_bg" />

    <View
        android:id="@+id/v_bottom_line"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_1"
        android:layout_below="@+id/speed_view"
        android:layout_marginTop="@dimen/dp_px_24"
        android:background="@color/menu_divide_color" />

    <TextView
        android:id="@+id/tv_reset"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_px_110"
        android:layout_below="@+id/v_bottom_line"
        android:gravity="center"
        android:paddingLeft="@dimen/dp_px_40"
        android:paddingRight="@dimen/dp_px_40"
        android:text="@string/tv_reset"
        android:textColor="@color/white_8"
        android:textSize="@dimen/sp_px_36" />

    <TextView
        android:id="@+id/tv_curve_name"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_px_110"
        android:layout_below="@+id/v_bottom_line"
        android:layout_centerHorizontal="true"
        android:gravity="center"
        android:text="@string/tv_custom"
        android:textColor="@color/white_8"
        android:textSize="@dimen/sp_px_42" />

    <ImageView
        android:id="@+id/iv_confirm"
        android:layout_width="@dimen/dp_px_70"
        android:layout_height="@dimen/dp_px_70"
        android:layout_below="@+id/v_bottom_line"
        android:layout_alignParentEnd="true"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:layout_marginTop="@dimen/dp_px_20"
        android:layout_marginEnd="@dimen/dp_px_40"
        android:layout_marginRight="@dimen/dp_px_40"
        android:background="@mipmap/ic_confirm"
        android:contentDescription="@null"
        android:padding="@dimen/dp_px_18" />


</merge>