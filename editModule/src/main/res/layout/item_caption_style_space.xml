<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_px_45">

        <View
            android:id="@+id/v_line_left"
            android:layout_width="@dimen/dp_px_180"
            android:layout_height="@dimen/dp_px_9"
            android:layout_centerVertical="true"
            android:layout_marginTop="@dimen/dp_px_18"
            android:background="@color/black_2020" />

        <View
            android:id="@+id/v_line_right"
            android:layout_width="@dimen/dp_px_180"
            android:layout_height="@dimen/dp_px_9"
            android:layout_centerVertical="true"
            android:layout_marginTop="@dimen/dp_px_18"
            android:layout_toRightOf="@+id/v_line_left"
            android:background="@color/black_2020" />

        <View
            android:id="@+id/v_point"
            android:layout_width="@dimen/dp_px_45"
            android:layout_height="@dimen/dp_px_45"
            android:layout_centerInParent="true" />
    </RelativeLayout>

    <TextView
        android:id="@+id/tv_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/dp_px_75"
        android:textColor="@color/white_8"
        android:textSize="@dimen/sp_px_30" />
</FrameLayout>
