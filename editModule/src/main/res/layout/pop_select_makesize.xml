<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/pop_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#6c000000"
    android:gravity="center">

    <LinearLayout
        android:layout_width="266dp"
        android:layout_height="296dp"
        android:background="@drawable/shape_corn_select_ratio"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="40dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="@string/select_production_proportion"
                android:textColor="#ffffffff"
                android:textSize="14sp" />
        </RelativeLayout>

        <View
            android:layout_width="240dp"
            android:layout_height="1dp"
            android:layout_gravity="center_horizontal"
            android:background="#1AFFFFFF" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="85dp"
            android:orientation="horizontal">

            <Button
                android:id="@+id/button16v9"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:background="#00000000"
                android:text="@string/sixteenTNine"
                android:textColor="#ffffffff"
                android:textSize="14sp" />

            <View
                android:layout_width="1dp"
                android:layout_height="72dp"
                android:layout_marginTop="13dp"
                android:background="#1AFFFFFF" />
            <Button
                android:id="@+id/button9v16"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:background="#00000000"
                android:text="@string/nineTSixteen"
                android:textColor="#ffffffff"
                android:textSize="14sp" />
        </LinearLayout>

        <View
            android:layout_width="240dp"
            android:layout_height="1dp"
            android:layout_gravity="center_horizontal"
            android:background="#1AFFFFFF" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="85dp"
            android:orientation="horizontal">

            <Button
                android:id="@+id/button4v3"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:background="#00000000"
                android:text="@string/fourTThree"
                android:textColor="#ffffffff"
                android:textSize="14sp" />
            <View
                android:layout_width="1dp"
                android:layout_height="match_parent"
                android:background="#1AFFFFFF" />
            <Button
                android:id="@+id/button3v4"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:background="#00000000"
                android:text="@string/threeTFour"
                android:textColor="#ffffffff"
                android:textSize="14sp" />
        </LinearLayout>
        <View
            android:layout_width="240dp"
            android:layout_height="1dp"
            android:layout_gravity="center_horizontal"
            android:background="#1AFFFFFF" />
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="85dp"
            android:orientation="horizontal">
            <Button
                android:id="@+id/button1v1"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:background="#00000000"
                android:text="@string/oneTone"
                android:textColor="#ffffffff"
                android:textSize="14sp" />
            <View
                android:layout_width="1dp"
                android:layout_height="72dp"
                android:layout_marginBottom="13dp"
                android:background="#1AFFFFFF" />

            <View
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:background="#00000000"/>
        </LinearLayout>
    </LinearLayout>

</LinearLayout>