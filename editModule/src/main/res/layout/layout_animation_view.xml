<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    tools:parentTag="android.widget.RelativeLayout">

    <com.meishe.business.assets.view.AssetsTypeTabView
        android:id="@+id/ttv_tab_type"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_102" />
    <TextView
        android:layout_marginTop="@dimen/dp_px_102"
        android:layout_marginBottom="@dimen/dp_px_226"
        android:id="@+id/tv_hint"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_387"
        android:gravity="center"
        android:visibility="gone"
        android:textColor="@color/color_ff808080" />

    <androidx.recyclerview.widget.RecyclerView
        android:layout_below="@+id/ttv_tab_type"
        android:id="@+id/rv_animation_list"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_225"
        android:layout_marginTop="@dimen/dp_px_12" />

    <LinearLayout
        android:id="@+id/seek_bar_layout"
        android:layout_below="@+id/rv_animation_list"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_150"
        android:paddingEnd="@dimen/dp_px_37"
        android:paddingStart="@dimen/dp_px_37"
        android:gravity="center_vertical"
        android:orientation="horizontal">
        <TextView
            android:id="@+id/tv_duration_hint"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:text="@string/animation_duration"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_px_33"
            android:visibility="invisible" />

        <SeekBar
            android:id="@+id/seek_bar"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_px_15"
            android:layout_marginLeft="@dimen/dp_px_15"
            android:layout_marginEnd="@dimen/dp_px_15"
            android:layout_marginRight="@dimen/dp_px_15"
            android:maxHeight="@dimen/dp2"
            android:layout_gravity="center_vertical"
            android:progressDrawable="@drawable/edit_seek_bar"
            android:thumb="@drawable/edit_seek_bar_ball"
            android:thumbOffset="@dimen/dp_px_0"
            android:visibility="invisible" />

        <TextView
            android:id="@+id/tv_duration"
            android:layout_gravity="right|center_vertical"
            android:layout_width="@dimen/dp_px_86"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:textColor="@color/white"
            android:textSize="@dimen/dp_px_33"
            android:visibility="invisible" />

    </LinearLayout>

    <View
        android:layout_below="@+id/seek_bar_layout"
        android:id="@+id/v_bottom_line"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_1"
        android:background="@color/menu_divide_color" />

    <FrameLayout
        android:layout_below="@+id/v_bottom_line"
        android:id="@+id/bottom_layout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_225">
        <TextView
            android:id="@+id/tv_content"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/dp_px_45"
            android:textColor="@color/white_8"
            android:textSize="@dimen/sp_px_42" />

        <ImageView
            android:id="@+id/iv_confirm"
            android:layout_width="@dimen/dp_px_70"
            android:layout_height="@dimen/dp_px_70"
            android:layout_gravity="right"
            android:layout_marginTop="@dimen/dp_px_30"
            android:layout_marginEnd="@dimen/dp_px_40"
            android:layout_marginRight="@dimen/dp_px_40"
            android:background="@mipmap/ic_confirm"
            android:contentDescription="@null"
            android:padding="@dimen/dp_px_18" />
    </FrameLayout>
</merge>