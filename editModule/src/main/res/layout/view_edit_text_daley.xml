<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    tools:parentTag="android.widget.RelativeLayout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    tools:ignore="RtlCompat">

    <TextView
        android:id="@+id/tv_start_text"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_gravity="center_vertical"
        android:gravity="center"
        android:textColor="@color/white_8"
        android:textSize="@dimen/sp_px_30" />

    <SeekBar
        android:id="@+id/seek_bar_view"
        android:layout_weight="1"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:maxHeight="@dimen/dp_px_6"
        android:max="100"
        android:progressDrawable="@drawable/edit_seek_bar"
        android:thumb="@drawable/edit_seek_bar_ball" />

    <TextView
        android:id="@+id/tv_current_text"
        android:layout_width="@dimen/dp_px_60"
        android:layout_height="wrap_content"
        android:gravity="left|center_vertical"
        android:textColor="@color/white_8"
        android:textSize="@dimen/dp_px_30" />

</LinearLayout>