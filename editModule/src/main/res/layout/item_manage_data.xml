<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="130dp">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:paddingStart="13dp"
        android:paddingEnd="13dp">

        <ImageView
            android:id="@+id/music_image"
            android:layout_width="100dp"
            android:layout_height="100dp"
            android:layout_centerVertical="true"
            android:layout_margin="@dimen/dp15"
            android:background="@mipmap/mv_default" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginStart="8dp"
            android:layout_marginLeft="8dp"
            android:layout_marginTop="15dp"
            android:layout_marginBottom="15dp"
            android:layout_toStartOf="@+id/music_manager"
            android:layout_toLeftOf="@+id/music_manager"
            android:layout_toEndOf="@+id/music_image"
            android:layout_toRightOf="@+id/music_image"
            android:orientation="vertical">

            <TextView
                android:id="@+id/music_name"
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:ellipsize="end"
                android:maxLines="1"
                android:textColor="@color/white_8"
                android:textSize="@dimen/sp12" />

            <TextView
                android:id="@+id/music_update"
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:layout_marginTop="3dp"
                android:layout_weight="1"
                android:ellipsize="end"
                android:maxLines="1"
                android:textColor="#909293"
                android:textSize="@dimen/sp12" />

            <TextView
                android:id="@+id/music_size"
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:ellipsize="end"
                android:maxLines="1"
                android:textColor="@color/white_8"
                android:textSize="@dimen/sp12" />

            <TextView
                android:id="@+id/music_time"
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:layout_marginTop="3dp"
                android:layout_weight="1"
                android:ellipsize="end"
                android:maxLines="1"
                android:textColor="#909293"
                android:textSize="@dimen/sp12" />
        </LinearLayout>

        <ImageButton
            android:id="@+id/music_manager"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:background="@mipmap/manager_item" />
    </RelativeLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_alignParentBottom="true"
        android:background="#12ffffff" />
</RelativeLayout>
