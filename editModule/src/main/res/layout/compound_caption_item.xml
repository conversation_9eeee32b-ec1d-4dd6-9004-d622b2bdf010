<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="@dimen/dp_px_225"
    android:layout_height="@dimen/dp_px_135">

    <com.facebook.drawee.view.SimpleDraweeView
        android:id="@+id/iv_cover"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:contentDescription="@null"
        android:padding="@dimen/dp_px_3" />

    <ImageView
        android:id="@+id/iv_downloading"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:contentDescription="@null"
        android:scaleType="centerCrop"
        android:visibility="gone" />

    <ProgressBar
        android:id="@+id/pb_download"
        android:layout_width="@dimen/asset_purchased_size"
        android:layout_height="@dimen/asset_purchased_size"
        android:layout_gravity="end"
        android:layout_marginTop="@dimen/asset_purchased_margin_top"
        android:layout_marginEnd="@dimen/asset_download_margin_right"
        android:layout_marginRight="@dimen/asset_download_margin_right"
        android:indeterminateDrawable="@drawable/progressbar_refresh"
        android:max="100"
        android:visibility="gone" />

    <ImageView
        android:id="@+id/iv_download"
        android:layout_width="@dimen/asset_purchased_size"
        android:layout_height="@dimen/asset_purchased_size"
        android:layout_gravity="end"
        android:layout_marginTop="@dimen/asset_purchased_margin_top"
        android:layout_marginEnd="@dimen/asset_download_margin_right"
        android:layout_marginRight="@dimen/asset_download_margin_right"
        android:contentDescription="@null"
        android:visibility="gone"
        android:src="@mipmap/animation_download_icon" />

    <TextView
        android:id="@+id/tv_asset_purchased"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="end|bottom"
        android:layout_marginEnd="@dimen/asset_purchased_margin_right"
        android:layout_marginRight="@dimen/asset_purchased_margin_right"
        android:layout_marginBottom="@dimen/asset_purchased_margin_bottom"
        android:background="@drawable/bg_round_corner_solid_66000000"
        android:gravity="center"
        android:paddingLeft="@dimen/asset_purchased_padding"
        android:paddingRight="@dimen/asset_purchased_padding"
        android:text="@string/asset_purchased"
        android:textColor="@color/white"
        android:textSize="@dimen/asset_purchased_text_size"
        android:visibility="gone" />
</FrameLayout>