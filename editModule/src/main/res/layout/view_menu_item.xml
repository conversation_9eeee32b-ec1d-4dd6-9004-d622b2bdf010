<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="@dimen/menu_item_width"
    android:layout_height="match_parent"
    android:background="@android:color/transparent">
    <RelativeLayout
        android:layout_centerInParent="true"
        android:layout_width="@dimen/menu_item_width"
        android:layout_height="wrap_content">
        <CheckBox
            android:layout_centerHorizontal="true"
            android:id="@+id/icon"
            android:button="@null"
            android:layout_width="@dimen/menu_item_image_width"
            android:layout_height="@dimen/menu_item_image_height">
        </CheckBox>
        <TextView
            android:layout_below="@+id/icon"
            android:id="@+id/name"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/menu_item_text_margin_top"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/white_8"
            android:textSize="@dimen/sp10">
        </TextView>
    </RelativeLayout>
</RelativeLayout>