<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">
    <ImageView
        android:id="@+id/menu_icon"
        android:layout_width="@dimen/dp56"
        android:layout_height="@dimen/dp56"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">
    </ImageView>

    <ImageView
        android:id="@+id/menu_select_bg"
        android:layout_width="@dimen/dp56"
        android:background="@color/red_half_trans"
        android:layout_height="@dimen/dp56"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/menu_name"
        android:layout_width="@dimen/dp56"
        android:layout_height="wrap_content"
        android:textColor="@color/white_8"
        android:background="#55000000"
        android:gravity="center"
        android:textSize="@dimen/sp10"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>