<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    tools:ignore="UseCompoundDrawables">

    <ImageView
        android:id="@+id/icon"
        android:layout_width="@dimen/dp_px_135"
        android:layout_height="@dimen/dp_px_135"
        android:background="@drawable/bg_shape_edit_mask"
        android:contentDescription="@null"
        android:padding="@dimen/dp_px_33"
        android:src="@mipmap/sub_menu_icon_edit_mask_none" />

    <TextView
        android:id="@+id/name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_px_15"
        android:ellipsize="end"
        android:gravity="center"
        android:singleLine="true"
        android:textColor="@color/white_8"
        android:textSize="@dimen/sp_px_33" />

</LinearLayout>