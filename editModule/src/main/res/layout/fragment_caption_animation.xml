<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="vertical">

        <com.google.android.material.tabs.TabLayout
            android:id="@+id/tab_layout"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_px_132"
            app:tabIndicator="@null"
            app:tabMode="scrollable"
            app:tabSelectedTextColor="@color/color_fffc2b55"
            app:tabTextAppearance="@style/subTabLayoutTextStyle"
            app:tabTextColor="@color/color_ff909293" />

        <com.meishe.base.view.CustomViewPager
            android:id="@+id/viewPager"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            tools:ignore="NestedWeights" />

        <FrameLayout
            android:id="@+id/seek_bar_layout"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_px_210"
            android:layout_marginTop="-100dp"
            android:visibility="invisible">

            <TextView
                android:id="@+id/tv_animation_group_fast"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_px_60"
                android:layout_marginTop="@dimen/dp_px_90"
                android:text="@string/fragment_caption_animation_fast"
                android:textColor="@color/white_8"
                android:textSize="@dimen/sp_px_30" />

            <TextView
                android:id="@+id/tv_animation_group_slow"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="right"
                android:layout_marginTop="@dimen/dp_px_90"
                android:layout_marginRight="@dimen/dp_px_60"
                android:text="@string/fragment_caption_animation_slow"
                android:textColor="@color/white_8"
                android:textSize="@dimen/sp_px_30" />

            <com.meishe.base.view.HorizontalSeekBar
                android:id="@+id/seek_bar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:bigValue="100"
                app:hasRule="false"
                app:imageHeight="@dimen/dp_px_111"
                app:imageLeft="@mipmap/icon_animation_in"
                app:imageLowPadding="0dp"
                app:imageRight="@mipmap/icon_animation_out"
                app:imageWidth="@dimen/dp_px_69"
                app:inColor="@color/white"
                app:leftOutColor="@color/color_ff50e3c2"
                app:leftTextColor="@color/color_ff50e3c2"
                app:leftTextSize="@dimen/sp_px_30"
                app:lineHeight="@dimen/dp_px_6"
                app:lineLength="@dimen/dp_px_786"
                app:middleOutColor="@color/white"
                app:middleTextColor="@color/white_8"
                app:rightOutColor="@color/color_fffc2b55"
                app:rightTextColor="@color/color_fffc2b55"
                app:rightTextSize="@dimen/sp_px_30"
                app:unit="s" />
        </FrameLayout>

    </LinearLayout>

    <com.meishe.business.assets.view.AssetsTypeTabView
        android:id="@+id/ttv_tab_type"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_102" />
</LinearLayout>