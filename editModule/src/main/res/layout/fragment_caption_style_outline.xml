<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_px_36"
        android:layout_marginLeft="@dimen/dp_px_36"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/iv_clear_color"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp_px_36"
            android:layout_marginRight="@dimen/dp_px_36"
            android:contentDescription="@null"
            android:src="@mipmap/icon_color_none" />

        <com.meishe.myvideo.view.MYMultiColorView
            android:id="@+id/multi_color_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/linear_outline_sb_width"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_px_99"
        android:layout_marginLeft="@dimen/dp_px_99"
        android:orientation="horizontal">

        <TextView
            android:layout_width="@dimen/dp_px_132"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:text="@string/color_width"
            android:textColor="@color/white_8"
            android:textSize="@dimen/sp_px_33" />

        <com.meishe.base.view.SeekBarTextView
            android:id="@+id/outline_sb_width"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="@dimen/dp_px_57"
            android:layout_marginLeft="@dimen/dp_px_57"
            android:layout_marginEnd="@dimen/dp_px_99"
            android:layout_marginRight="@dimen/dp_px_99"/>

    </LinearLayout>

    <LinearLayout
        android:id="@+id/linear_outline_sb_opacity"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_px_99"
        android:layout_marginLeft="@dimen/dp_px_99"
        android:orientation="horizontal">

        <TextView
            android:layout_width="@dimen/dp_px_132"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:text="@string/color_opacity"
            android:textColor="@color/white_8"
            android:textSize="@dimen/sp_px_33" />

        <com.meishe.base.view.SeekBarTextView
            android:id="@+id/outline_sb_opacity"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="@dimen/dp_px_57"
            android:layout_marginLeft="@dimen/dp_px_57"
            android:layout_marginEnd="@dimen/dp_px_99"
            android:layout_marginRight="@dimen/dp_px_99"/>

    </LinearLayout>
</LinearLayout>