<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#00000000">

    <RelativeLayout
        android:layout_width="240dp"
        android:layout_height="127dp"
        android:layout_centerInParent="true"
        android:layout_centerVertical="true"
        android:background="@drawable/bg_round_corners_solid_white"
        android:orientation="vertical">

        <TextView
            android:id="@+id/statementContent"
            android:layout_width="match_parent"
            android:gravity="center"
            android:layout_height="wrap_content"
            android:text="@string/tv_confim_rest"
            android:layout_marginLeft="29dp"
            android:layout_marginTop="39dp"
            android:layout_marginRight="29dp"
            android:textColor="@color/black"
            android:textSize="@dimen/sp12"/>

        <View
            android:id="@+id/line"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_below="@+id/statementContent"
            android:layout_marginTop="@dimen/dp27"
            android:background="#d4d4d4"/>

        <LinearLayout

            android:id="@+id/bottomLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@+id/line"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/notUsedButton"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_gravity="center_horizontal"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/draft_manage_cancel"
                android:textColor="@color/black"
                android:textSize="@dimen/sp12" />

            <View
                android:layout_width="1dp"
                android:layout_height="match_parent"
                android:background="#d4d4d4"/>

            <TextView
                android:id="@+id/agreeButton"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_gravity="center_horizontal"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/draft_confirm"
                android:textColor="#ff365e"
                android:textSize="@dimen/sp12" />
        </LinearLayout>
    </RelativeLayout>
</RelativeLayout>