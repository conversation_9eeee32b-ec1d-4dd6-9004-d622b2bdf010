<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/menu_bg"
    android:orientation="vertical">

    <com.meishe.base.view.CustomTitleBar
        android:id="@+id/title_bar"
        android:layout_width="match_parent"
        android:layout_height="64dp"
        app:layout_constraintTop_toTopOf="parent">

    </com.meishe.base.view.CustomTitleBar>

    <RelativeLayout
        android:id="@+id/spaceLayout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

    </RelativeLayout>
</LinearLayout>
