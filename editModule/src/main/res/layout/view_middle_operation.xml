<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/iv_operation_play"
        android:layout_width="@dimen/dp_px_54"
        android:layout_height="@dimen/dp_px_54"
        android:layout_centerVertical="true"
        android:layout_marginStart="@dimen/dp_px_42"
        android:layout_marginLeft="@dimen/dp_px_42"
        android:contentDescription="@null"
        app:srcCompat="@mipmap/control_bar_ic_play" />

    <ImageView
        android:id="@+id/iv_operation_zoom"
        android:layout_width="@dimen/dp_px_54"
        android:layout_height="@dimen/dp_px_54"
        android:layout_alignParentEnd="true"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:layout_marginEnd="@dimen/dp_px_45"
        android:layout_marginRight="@dimen/dp_px_45"
        android:contentDescription="@null"
        app:srcCompat="@mipmap/ic_middle_operation_zoom" />

    <ImageView
        android:id="@+id/iv_operation_recover"
        android:layout_width="@dimen/dp_px_54"
        android:layout_height="@dimen/dp_px_54"
        android:layout_centerVertical="true"
        android:layout_marginEnd="@dimen/dp_px_50"
        android:layout_marginRight="@dimen/dp_px_50"
        android:layout_toStartOf="@+id/iv_operation_zoom"
        android:layout_toLeftOf="@+id/iv_operation_zoom"
        android:contentDescription="@null"
        app:srcCompat="@mipmap/ic_operate_recover_enable" />

    <ImageView
        android:id="@+id/iv_operation_cancel"
        android:layout_width="@dimen/dp_px_54"
        android:layout_height="@dimen/dp_px_54"
        android:layout_centerVertical="true"
        android:layout_marginEnd="@dimen/dp_px_50"
        android:layout_marginRight="@dimen/dp_px_50"
        android:layout_toStartOf="@+id/iv_operation_recover"
        android:layout_toLeftOf="@+id/iv_operation_recover"
        android:contentDescription="@null"
        app:srcCompat="@mipmap/ic_operate_cancel_enable" />

    <ImageView
        android:id="@+id/iv_add_key_frame"
        android:layout_width="@dimen/dp_px_54"
        android:layout_height="@dimen/dp_px_54"
        android:layout_centerVertical="true"
        android:layout_marginEnd="@dimen/dp_px_50"
        android:layout_toStartOf="@+id/iv_operation_cancel"
        android:contentDescription="@null"
        android:visibility="gone"
        app:srcCompat="@mipmap/icon_add_key_frame" />

    <ImageView
        android:id="@+id/iv_add_key_frame_curve"
        android:layout_width="@dimen/dp_px_54"
        android:layout_height="@dimen/dp_px_54"
        android:layout_centerVertical="true"
        android:layout_marginEnd="@dimen/dp_px_50"
        android:layout_toStartOf="@+id/iv_add_key_frame"
        android:contentDescription="@null"
        android:visibility="gone"
        app:srcCompat="@mipmap/icon_add_key_frame_curve_select" />

    <TextView
        android:id="@+id/tv_operate_time"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_px_49"
        android:layout_centerVertical="true"
        android:layout_marginStart="@dimen/dp_px_30"
        android:layout_marginLeft="@dimen/dp_px_30"
        android:layout_toEndOf="@+id/iv_operation_play"
        android:textColor="@color/white_half"
        android:textSize="@dimen/sp_px_36" />
</merge>
