<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_px_783"
    android:background="@color/black">

    <TextView
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        android:id="@+id/tv_content"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_px_135"
        android:layout_marginStart="@dimen/dp_px_39"
        android:gravity="center_vertical"
        android:text="@string/sub_menu_audio_transition_all"
        android:textColor="@color/white_8"
        android:textSize="@dimen/sp_px_42"
        tools:ignore="RelativeOverlap" />

    <ImageView
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:id="@+id/iv_confirm"
        android:layout_width="@dimen/dp_px_75"
        android:layout_height="@dimen/dp_px_75"
        android:layout_alignParentEnd="true"
        android:layout_marginTop="@dimen/dp_px_30"
        android:layout_marginEnd="@dimen/dp_px_45"
        android:contentDescription="@null"
        android:src="@mipmap/ic_confirm" />
    <View
        app:layout_constraintTop_toBottomOf="@+id/tv_content"
        android:id="@+id/v_top_line"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_1"
        android:background="@color/menu_divide_color" />
    <com.meishe.base.view.HorizontalSeekBar
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:id="@+id/seek_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        app:bigValue="100"
        app:hasRule="false"
        app:imageHeight="@dimen/dp_px_111"
        app:imageLeft="@mipmap/icon_animation_in"
        app:imageLowPadding="0dp"
        app:imageRight="@mipmap/icon_animation_out"
        app:imageWidth="@dimen/dp_px_69"
        app:inColor="@color/white"
        app:leftOutColor="@color/color_ff50e3c2"
        app:leftTextColor="@color/color_ff50e3c2"
        app:leftTextSize="@dimen/sp_px_30"
        app:lineHeight="@dimen/dp_px_6"
        app:lineLength="@dimen/dp_px_786"
        app:middleOutColor="@color/white"
        app:middleTextColor="@color/white_8"
        app:rightOutColor="@color/color_fffc2b55"
        app:rightTextColor="@color/color_fffc2b55"
        app:rightTextSize="@dimen/sp_px_30"
        app:layout_constraintHorizontal_weight="1"
        app:unit="s" />
    <TextView
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_centerVertical="true"
        android:id="@+id/tv_fade_in"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_px_45"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="@dimen/dp_px_80"
        android:layout_marginTop="@dimen/dp_px_42"
        android:gravity="center"
        android:text="@string/fade_in_time"
        android:textColor="@color/white_8"
        android:textSize="@dimen/sp_px_30" />
    <TextView
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:id="@+id/tv_fade_out"
        android:layout_centerVertical="true"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_px_45"
        android:layout_marginEnd="@dimen/dp_px_80"
        android:layout_marginTop="@dimen/dp_px_42"
        android:gravity="center"
        android:text="@string/fade_out_time"
        android:textColor="@color/white_8"
        android:textSize="@dimen/sp_px_30" />
</androidx.constraintlayout.widget.ConstraintLayout>
