<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/fl_confirm"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_px_225">
    <View
        android:id="@+id/line"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_1"
        android:background="@color/menu_divide_color" />
    <TextView
        android:id="@+id/tv_content"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/dp_px_48"
        android:textColor="@color/white_8"
        android:textSize="@dimen/sp_px_42" />

    <ImageView
        android:id="@+id/iv_confirm"
        android:layout_width="@dimen/dp_px_69"
        android:layout_height="@dimen/dp_px_69"
        android:layout_gravity="end"
        android:layout_marginTop="@dimen/dp_px_48"
        android:layout_marginEnd="@dimen/dp_px_39"
        android:layout_marginRight="@dimen/dp_px_39"
        android:background="@mipmap/ic_confirm"
        android:contentDescription="@null"
        android:padding="@dimen/dp_px_18" />
</FrameLayout>
