<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp92"
    android:background="@color/black"
    android:orientation="vertical">

    <RelativeLayout
        android:id="@+id/rl_with_confirm_menu"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp92">

        <LinearLayout
            android:id="@+id/ll_top_seek_menu"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp56"
            android:orientation="horizontal">

            <com.meishe.myvideo.view.MYSeekBarView
                android:id="@+id/view_seek_bar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical" />

        </LinearLayout>


    </RelativeLayout>
</LinearLayout>