<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/editor_timeline_view_left_handle"
        android:layout_width="@dimen/editor_timeline_view_hand_width"
        android:layout_height="match_parent"
        android:layout_alignParentLeft="true"
        android:background="@mipmap/ic_thumbnail_left_hand"
        android:contentDescription="@null" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_toEndOf="@id/editor_timeline_view_left_handle"
        android:layout_toRightOf="@id/editor_timeline_view_left_handle"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_editor_timeline_time_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/dp3"
            android:paddingLeft="3dp"
            android:paddingRight="3dp"
            android:textAlignment="gravity"
            android:textColor="@color/white_8"
            android:textSize="8sp" />

        <LinearLayout
            android:id="@+id/layout_editor_timeline_speed"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="3dp"
            android:background="@drawable/bg_audio_draw_text"
            android:gravity="center"
            android:orientation="horizontal"
            android:paddingLeft="3dp"
            android:paddingRight="3dp">

            <ImageView
                android:layout_width="7dp"
                android:layout_height="7dp"
                android:layout_gravity="center_vertical"
                android:background="@mipmap/sub_menu_icon_edit_speed" />

            <TextView
                android:id="@+id/tv_editor_timeline_speed_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="2dp"
                android:textColor="@color/white_8"
                android:textSize="8sp" />
        </LinearLayout>
    </LinearLayout>

    <ImageView
        android:id="@+id/editor_timeline_view_right_handle"
        android:layout_width="@dimen/editor_timeline_view_hand_width"
        android:layout_height="match_parent"
        android:layout_alignParentEnd="true"
        android:layout_alignParentRight="true"
        android:background="@mipmap/ic_thumbnail_right_hand"
        android:contentDescription="@null" />

    <View
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_alignParentLeft="true"
        android:layout_alignParentRight="true"
        android:layout_marginLeft="@dimen/editor_timeline_view_hand_width"
        android:layout_marginRight="@dimen/editor_timeline_view_hand_width"
        android:background="@drawable/editor_drawable_corner_white" />
</RelativeLayout>