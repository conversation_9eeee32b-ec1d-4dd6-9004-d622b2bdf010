<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="@dimen/dp_px_195"
    android:layout_height="@dimen/dp_px_195"
    android:layout_gravity="center_horizontal"
    android:id="@+id/layout">


    <FrameLayout
        android:id="@+id/fl_add"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/asset_effect_bg"
        android:visibility="gone">

        <ImageView
            android:layout_width="@dimen/dp_px_60"
            android:layout_height="@dimen/dp_px_60"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/dp_px_42"
            android:src="@mipmap/ic_add"
            android:background="@drawable/asset_effect_bg"
            android:contentDescription="@null" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_px_54"
            android:layout_gravity="bottom"
            android:gravity="center"
            android:text="@string/item_hint_custom_water"
            android:textColor="@color/white_8"
            android:textSize="@dimen/sp_px_30" />

    </FrameLayout>


    <ImageView
        android:background="@drawable/asset_effect_bg"
        android:id="@+id/iv_cover"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:contentDescription="@null" />

    <View
        android:id="@+id/v_mask"
        android:layout_width="@dimen/dp_px_195"
        android:layout_height="@dimen/dp_px_195" />
</FrameLayout>