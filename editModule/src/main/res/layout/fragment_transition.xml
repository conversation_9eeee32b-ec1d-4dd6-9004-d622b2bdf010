<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <com.meishe.business.assets.view.AssetsTypeTabView
        android:id="@+id/ttv_tab_type"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_102" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_px_15" />

    <TextView
        android:id="@+id/tv_hint"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:gravity="center"
        android:textColor="@color/color_ff808080"
        android:visibility="gone" />

    <RelativeLayout
        android:id="@+id/rl_top_view"
        android:visibility="invisible"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_transition_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_px_43"
            android:layout_marginLeft="@dimen/dp_px_43"
            android:layout_marginTop="@dimen/dp_px_39"
            android:text="@string/trans_time"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_px_33" />

        <TextView
            android:id="@+id/start_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_px_99"
            android:layout_marginLeft="@dimen/dp_px_99"
            android:layout_marginTop="@dimen/dp_px_39"
            android:layout_toRightOf="@+id/tv_transition_time"
            android:text="0.1s"
            android:textColor="@color/white_8"
            android:textSize="@dimen/sp_px_30" />

        <com.meishe.base.view.SeekBarTextView
            android:id="@+id/seek_bar_progress"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginRight="@dimen/dp_px_48"
            android:layout_toLeftOf="@+id/end_text"
            android:layout_toRightOf="@+id/start_text" />

        <TextView
            android:id="@+id/end_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_marginTop="@dimen/dp_px_39"
            android:layout_marginEnd="@dimen/dp_px_92"
            android:layout_marginRight="@dimen/dp_px_92"
            android:text="1s"
            android:textColor="@color/white_8"
            android:textSize="@dimen/sp_px_30" />
    </RelativeLayout>


</LinearLayout>