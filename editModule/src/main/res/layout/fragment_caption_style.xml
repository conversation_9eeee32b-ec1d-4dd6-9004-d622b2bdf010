<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">


        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_font_list"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_px_90"
            android:layout_marginStart="@dimen/dp_px_45"
            android:layout_marginLeft="@dimen/dp_px_45"
            android:layout_marginTop="@dimen/dp_px_60" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_px_60"
            android:layout_marginStart="@dimen/dp_px_45"
            android:layout_marginLeft="@dimen/dp_px_45"
            android:layout_marginTop="@dimen/dp_px_39"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_font_bold"
                android:layout_width="@dimen/dp_px_120"
                android:layout_height="@dimen/dp_px_60"
                android:background="@drawable/select_bg_caption_style"
                android:gravity="center"
                android:text="@string/caption_font_bold"
                android:textColor="@color/white_8"
                android:textSize="@dimen/sp_px_30" />

            <TextView
                android:id="@+id/tv_font_italics"
                android:layout_width="@dimen/dp_px_120"
                android:layout_height="@dimen/dp_px_60"
                android:layout_marginStart="@dimen/dp_px_30"
                android:layout_marginLeft="@dimen/dp_px_30"
                android:background="@drawable/select_bg_caption_style"
                android:gravity="center"
                android:text="@string/font_italics"
                android:textColor="@color/white_8"
                android:textSize="@dimen/sp_px_30" />

            <TextView
                android:id="@+id/tv_font_shadow"
                android:layout_width="@dimen/dp_px_120"
                android:layout_height="@dimen/dp_px_60"
                android:layout_marginStart="@dimen/dp_px_30"
                android:layout_marginLeft="@dimen/dp_px_30"
                android:background="@drawable/select_bg_caption_style"
                android:gravity="center"
                android:text="@string/font_shadow"
                android:textColor="@color/white_8"
                android:textSize="@dimen/sp_px_30" />

        </LinearLayout>

        <View
            android:id="@+id/menu_divide"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp0p2"
            android:layout_marginTop="@dimen/dp18"
            android:background="@color/menu_divide_color" />


        <com.google.android.material.tabs.TabLayout
            android:id="@+id/tab_layout"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_px_132"
            app:tabIndicator="@null"
            app:tabMode="scrollable"
            app:tabSelectedTextColor="@color/color_fffc2b55"
            app:tabTextAppearance="@style/subTabLayoutTextStyle"
            app:tabTextColor="@color/color_ff909293" />


        <com.meishe.base.view.CustomViewPager
            android:id="@+id/viewPager"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_px_510" />

    </LinearLayout>

</ScrollView>