<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_px_399"
    android:background="@color/transparent"
    android:layout_gravity="center_horizontal">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <TextView
        android:layout_centerInParent="true"
        android:id="@+id/tv_hint"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:gravity="center"
        android:textColor="@color/color_ff808080"
        android:layout_marginBottom="@dimen/dp_px_102"
        android:visibility="gone" />
</RelativeLayout>