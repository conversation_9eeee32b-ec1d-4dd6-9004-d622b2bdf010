package com.meishe.myvideo.ui.trackview;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Typeface;
import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.meicam.sdk.NvsMultiThumbnailSequenceView;
import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.FormatUtils;
import com.meishe.base.utils.ImageLoader;
import com.meishe.base.utils.LogUtils;
import com.meishe.base.utils.SizeUtils;
import com.meishe.engine.bean.CommonData;
import com.meishe.engine.bean.MeicamAudioClip;
import com.meishe.engine.interf.IClip;
import com.meishe.engine.view.MultiThumbnailSequenceView;
import com.meishe.myvideo.R;
import com.meishe.myvideo.audio.AudioWaveView;
import com.meishe.myvideo.ui.bean.BaseUIClip;
import com.meishe.myvideo.ui.bean.KeyFrameInfo;
import com.meishe.myvideo.util.PixelPerMicrosecondUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by CaoZhiChao on 2020/6/17 16:08
 * 基础模型视图类
 * Base model view class
 */
public class BaseItemView extends FrameLayout {
    private BaseUIClip mBaseUIClip;
    private int mViewHeight = 0;
    private Context mContext;
    private ImageView mIconImageView;
    private TextView mTextView;
    private View mSpeedRelative;
    private TextView mSpeedTextView;
    private TextView mDuringTextView;
    private View mFacePropIcon;
    private View mVolumeIcon;
    private View mImageSpeed;
    private String mText;
    private Paint mTextPaint;
    private MultiThumbnailSequenceView mNvsMultiThumbnailSequenceView;
    private NvsWaveformViewWrap mNvsWaveformView = null;
    private AudioWaveView mAudioWaveView = null;
    private HandView handView = null;
    private View mVideoAnimationIn, mVideoAnimationOut;
    private int width;
    public static final int BASE_ITEM_MARGIN = 5;
    private ImageLoader.Options options;
    private RelativeLayout mItemViewContainer;
    private TrackViewCover mTrackViewCover;
    private AudioWaveView mCustomWaveView;
    private TextView mTagTextView;

    private WaveformHelper mWaveformHelper;

    public void setWaveformHelper(WaveformHelper helper) {
        this.mWaveformHelper = helper;
    }

    public BaseItemView(Context context) {
        super(context);
        init(context, null, 0);
    }

    public BaseItemView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context, attrs, 0);
    }

    public BaseItemView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs, defStyleAttr);
    }

    private void init(Context context, AttributeSet attrs, int defStyleAttr) {
        addView(mItemViewContainer = new RelativeLayout(context));
        addView(mTrackViewCover = new TrackViewCover(context));
        options = new ImageLoader.Options().centerCrop();
        mContext = context;
        mViewHeight = context.getResources().getDimensionPixelOffset(R.dimen.track_view_real_height);
        mTextPaint = new Paint();
        mTextPaint.setTextSize(SizeUtils.dp2px(12));
        mTextPaint.setColor(Color.WHITE);
        mTextPaint.setAntiAlias(false);
        mTextPaint.setStyle(Paint.Style.FILL);
    }

    /**
     * Sets data.
     * 设置数据
     *
     * @param baseUIClip the base ui clip
     */
    public void setData(BaseUIClip baseUIClip) {
        if (baseUIClip == null) {
            LogUtils.e("baseUIClip is null");
            return;
        }
        mBaseUIClip = baseUIClip;

        width = 100;

        long duration = (long) ((mBaseUIClip.getTrimOut() - mBaseUIClip.getTrimIn()) / mBaseUIClip.getSpeed());
        width = PixelPerMicrosecondUtil.durationToLength(duration);
        if (!TextUtils.isEmpty(mBaseUIClip.getIconFilePath())) {
           // LogUtils.d("setData: 图片路径 " + mBaseUIClip.getIconFilePath());
            addImageView(mBaseUIClip.getIconFilePath());
        }
        mText = baseUIClip.getDisplayName();
        String type = baseUIClip.getType();
        Drawable drawable = CommonUtils.getRadiusDrawable(-1, -1,
                getResources().getDimensionPixelOffset(R.dimen.track_view_background_radius), getBackgroundColor(baseUIClip));
        setBackground(drawable);
        if (type.equals(CommonData.CLIP_VIDEO) || type.equals(CommonData.CLIP_IMAGE)) {
            addTimeLineView();
            addVideoAnimationView();
            mItemViewContainer.addView(mVideoAnimationIn);
            mItemViewContainer.addView(mVideoAnimationOut);
            addSpeedText();
        } else if (type.equals(CommonData.CLIP_AUDIO) && baseUIClip.getSubType() == MeicamAudioClip.AUDIO_RECORD_ING) {
            addRecordingView();
        } else if (type.equals(CommonData.CLIP_AUDIO) &&
                baseUIClip.getSubType() != MeicamAudioClip.AUDIO_RECORD_ING) {
            addAudioView();
            addAudioFadeView();
        }
        List<String> tag = baseUIClip.getTag();
        if (tag != null && tag.size() > 0) {
            addFxTargetTag(tag.get(0), baseUIClip.getBackGroundColor());
        }
        addTextView();
        KeyFrameInfo keyFrameInfo = baseUIClip.getKeyFrameInfo();
        if (keyFrameInfo != null) {
            mTrackViewCover.setKeyFrameInfo(keyFrameInfo, keyFrameInfo.getSelectedPoint());
        }
        invalidate();
    }

    private void addSpeedText() {
        if (mSpeedRelative == null) {
            mSpeedRelative = LayoutInflater.from(mContext).inflate(R.layout.base_item_view_speed_layout, mItemViewContainer);
            mSpeedTextView = mSpeedRelative.findViewById(R.id.base_item_view_speed_text);
            mDuringTextView = mSpeedRelative.findViewById(R.id.base_item_view_time_text);
            mFacePropIcon = mSpeedRelative.findViewById(R.id.base_item_view_prop_icon);
            mVolumeIcon = mSpeedRelative.findViewById(R.id.base_item_view_volume_icon);
            mImageSpeed = mSpeedRelative.findViewById(R.id.ll_speed);
            mFacePropIcon.setVisibility(mBaseUIClip.isHasProp() ? VISIBLE : GONE);
            mVolumeIcon.setVisibility(CommonData.CLIP_VIDEO.equals(mBaseUIClip.getType()) && mBaseUIClip.getVolume() == 0 ? VISIBLE : GONE);
            if (TextUtils.isEmpty(mBaseUIClip.getCurveSpeedName())) {
                if (mBaseUIClip.getSpeed() != 1.0f) {
                    mSpeedTextView.setVisibility(VISIBLE);
                    mImageSpeed.setVisibility(VISIBLE);
                } else {
                    mSpeedTextView.setVisibility(GONE);
                    mImageSpeed.setVisibility(GONE);
                }
                mSpeedTextView.setText(FormatUtils.objectFormat2String(mBaseUIClip.getSpeed()) + "x");
            } else {
                mSpeedTextView.setVisibility(VISIBLE);
                mImageSpeed.setVisibility(VISIBLE);
                mSpeedTextView.setText(mBaseUIClip.getCurveSpeedName());
            }
            mDuringTextView.setText(FormatUtils.duration2Text((long) ((mBaseUIClip.getTrimOut() - mBaseUIClip.getTrimIn()) / mBaseUIClip.getSpeed())));

        }
    }

    /**
     * 添加音频褪色的视图
     * Add audio fade view
     * */
    private void addAudioFadeView() {
        float fadeIn = mBaseUIClip.getFadeIn() * 1.0f / CommonData.TIMEBASE;
        float fadeOut = mBaseUIClip.getFadeOut() * 1.0f / CommonData.TIMEBASE;

        if (fadeIn != 0) {
            View topView = new View(getContext());
            topView.setBackgroundResource(R.drawable.bg_audio_fade_top);

            RelativeLayout.LayoutParams layoutParams = new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, (int) getResources().getDimension(R.dimen.dp6));
            topView.setLayoutParams(layoutParams);
            mItemViewContainer.addView(topView);
        }

        if (fadeOut != 0) {
            View bottomView = new View(getContext());
            bottomView.setBackgroundResource(R.drawable.bg_audio_fade_bottom);
            RelativeLayout.LayoutParams layoutParams = new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, (int) getResources().getDimension(R.dimen.dp6));
            layoutParams.addRule(RelativeLayout.ALIGN_PARENT_BOTTOM);
            bottomView.setLayoutParams(layoutParams);
            mItemViewContainer.addView(bottomView);
        }
    }

    /**
     * Update fx target tag.
     * 更新tag
     */
    public void updateFxTargetTag() {
        if (mTagTextView != null && mBaseUIClip != null) {
            List<String> tag = mBaseUIClip.getTag();
            if (!CommonUtils.isEmpty(tag)) {
                mTagTextView.setText(tag.get(0));
                RelativeLayout.LayoutParams layoutParams = (RelativeLayout.LayoutParams) mTextView.getLayoutParams();
                if (mTextView != null) {
                    String tagText = mTagTextView.getText().toString();
                    int length = tagText.length();
                    int margin;
                    int width;
                    if (length == 1) {
                        margin =  (int) getResources().getDimension(R.dimen.dp21);
                        width =  (int) getResources().getDimension(R.dimen.dp12);
                    } else {
                        margin = (int) getResources().getDimension(R.dimen.dp28);
                        width =  (int) getResources().getDimension(R.dimen.dp20);
                    }
                    ViewGroup.LayoutParams params = mTagTextView.getLayoutParams();
                    params.width = width;
                    mTagTextView.setLayoutParams(params);
                    layoutParams.leftMargin = margin;
                    layoutParams.topMargin = (int) getResources().getDimension(R.dimen.dp5);
                    mTextView.setLayoutParams(layoutParams);
                }
            }
        }
    }

    private void addFxTargetTag(String tagText, int textColor) {
        if (mTagTextView == null) {
            mTagTextView = new TextView(getContext());
            mTagTextView.setSingleLine(true);
            mTagTextView.setEllipsize(TextUtils.TruncateAt.END);
            mTagTextView.setTextColor(textColor);
            mTagTextView.setTextSize(TypedValue.COMPLEX_UNIT_PX, getResources().getDimension(R.dimen.sp8));
            mTagTextView.setText(tagText);
            mTagTextView.setTypeface(null, Typeface.BOLD);
            mTagTextView.setGravity(Gravity.CENTER);
            mTagTextView.setBackgroundResource(R.drawable.bg_rectangle_round_white_d2);
            int height = (int) getResources().getDimension(R.dimen.dp12);
            int length = tagText.length();
            int width;
            if (length == 1) {
                width = height;
            } else {
                width = (int) getResources().getDimension(R.dimen.dp20);
            }
            RelativeLayout.LayoutParams layoutParams = new RelativeLayout.LayoutParams(width, height);
            layoutParams.leftMargin = (int) getResources().getDimension(R.dimen.dp5);
            layoutParams.topMargin = (int) getResources().getDimension(R.dimen.dp5);
            mTagTextView.setLayoutParams(layoutParams);
            layoutParams.addRule(RelativeLayout.CENTER_VERTICAL);
            mItemViewContainer.addView(mTagTextView);
        }
    }

    private void addTextView() {
        if (mTextView == null) {
            mTextView = new TextView(getContext());
            mTextView.setTextColor(getResources().getColor(R.color.white_8));
            mTextView.setSingleLine(true);
            mTextView.setEllipsize(TextUtils.TruncateAt.END);
            mTextView.setTextSize(TypedValue.COMPLEX_UNIT_PX, getResources().getDimension(R.dimen.sp10));
            mTextView.setText(mText);
            if (CommonData.CLIP_AUDIO.equals(mBaseUIClip.getType())) {
                RelativeLayout.LayoutParams layoutParamsAudio = new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
                mTextView.setBackgroundResource(R.drawable.bg_audio_draw_text);
                int padding = SizeUtils.dp2px(3);
                mTextView.setPadding(padding, padding, padding, padding);
                mTextView.setSingleLine(true);
                layoutParamsAudio.addRule(RelativeLayout.ALIGN_PARENT_BOTTOM);
                mTextView.setLayoutParams(layoutParamsAudio);
            } else {
                RelativeLayout.LayoutParams layoutParams = new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
                if (mTagTextView != null) {
                    String tagText = mTagTextView.getText().toString();
                    int length = tagText.length();
                    int margin;
                    if (length == 1) {
                        margin =  (int) getResources().getDimension(R.dimen.dp21);;
                    } else {
                        margin = (int) getResources().getDimension(R.dimen.dp28);
                    }
                    layoutParams.leftMargin = margin;
                    layoutParams.topMargin = (int) getResources().getDimension(R.dimen.dp5);
                } else {
                    layoutParams.leftMargin = (int) getResources().getDimension(R.dimen.dp5);
                    layoutParams.topMargin = (int) getResources().getDimension(R.dimen.dp5);
                }
                layoutParams.addRule(RelativeLayout.CENTER_VERTICAL);
                mTextView.setLayoutParams(layoutParams);
            }
            mItemViewContainer.addView(mTextView);
        }
    }


    private void setText(String text) {
        if (mTextView != null) {
            mText = text;
            mTextView.setText(mText);
        }
    }

    private void addImageView(final String filePath) {
        if (mIconImageView == null) {
            mIconImageView = new ImageView(getContext());
            RelativeLayout.LayoutParams layoutParams = new RelativeLayout.LayoutParams(mViewHeight, mViewHeight);
            mIconImageView.setLayoutParams(layoutParams);
            mItemViewContainer.addView(mIconImageView);
        }
        ImageLoader.loadUrl(getContext(), filePath, mIconImageView, options);
    }

    /**
     * 录音波形图
     * Recording waveform
     */
    private void addRecordingView() {
        View view = LayoutInflater.from(mContext).inflate(R.layout.item_add_recording_view, mItemViewContainer);
        mAudioWaveView = view.findViewById(R.id.audio_view);
        mAudioWaveView.setWaveColor(getResources().getColor(R.color.audio_record));
        mAudioWaveView.setMaxGroupData(0.9f);
    }

    /**
     * Refresh.
     * 刷新
     *
     * @param baseUIClip the base ui clip 基本ui剪辑
     * @param isLeft     the is left 是否是左边
     */
    public void refresh(BaseUIClip baseUIClip, boolean isLeft) {
        mBaseUIClip = baseUIClip;
        mText = baseUIClip.getDisplayName();
        String type = baseUIClip.getType();
        if (type.equals(CommonData.CLIP_VIDEO) || type.equals(CommonData.CLIP_IMAGE)) {
            refreshVideoView();
            setPipDuringVisiableStatus(true);
        } else if (type.equals(CommonData.CLIP_AUDIO)) {
            if (baseUIClip.getSubType() == MeicamAudioClip.AUDIO_RECORD_ING) {
                if (mAudioWaveView != null) {
                    mAudioWaveView.setWidth(baseUIClip.getDuration());
                    mAudioWaveView.addWaveData(baseUIClip.getRecordArray(), baseUIClip.getTrimIn(), baseUIClip.getTrimOut(), baseUIClip.getOriginalDuration());
                }
            } else {
                refreshAudioView(isLeft);
            }
        }
        /*刷新view时需要更新view 的宽度，重新计算轨道的宽  onMeasure时使用到了
         * The width of the view needs to be updated when refreshing the view, which is used when recalculating the width of the track onMeasure
         * */
        if (mBaseUIClip != null) {
            long duration = (long) ((mBaseUIClip.getTrimOut() - mBaseUIClip.getTrimIn()) / mBaseUIClip.getSpeed());
            width = PixelPerMicrosecondUtil.durationToLength(duration);
        }
        KeyFrameInfo keyFrameInfo = baseUIClip.getKeyFrameInfo();
        if (keyFrameInfo != null) {
            mTrackViewCover.setKeyFrameInfo(keyFrameInfo, keyFrameInfo.getSelectedPoint());
        }
        invalidate();
    }

    /**
     * Sets pip during visiable status.
     * 在可视状态下设置画中画
     *
     * @param isVisiable the is visiable 可视状态
     */
    public void setPipDuringVisiableStatus(boolean isVisiable) {
        if (mBaseUIClip.getType().equals(CommonData.CLIP_VIDEO) || mBaseUIClip.getType().equals(CommonData.CLIP_IMAGE)) {
            if (mDuringTextView != null) {
                if (isVisiable) {
                    mDuringTextView.setVisibility(VISIBLE);
                } else {
                    mDuringTextView.setVisibility(GONE);
                }
                mDuringTextView.setText(FormatUtils.duration2Text((long) ((mBaseUIClip.getTrimOut() - mBaseUIClip.getTrimIn()) / mBaseUIClip.getSpeed())));
                //invalidate();
            }
            addVideoAnimationView();
        }
    }

    private void refreshVideoView() {
        if (mNvsMultiThumbnailSequenceView != null) {
            ArrayList<MultiThumbnailSequenceView.ThumbnailSequenceDesc> sequenceDescsArray = new ArrayList<>();
            MultiThumbnailSequenceView.ThumbnailSequenceDesc sequenceDescs = new MultiThumbnailSequenceView.ThumbnailSequenceDesc();
            sequenceDescs.mediaFilePath = mBaseUIClip.getFilePath();
            sequenceDescs.trimIn = mBaseUIClip.getTrimIn();
            sequenceDescs.trimOut = mBaseUIClip.getTrimOut();
            sequenceDescs.inPoint = 0;

            sequenceDescs.stillImageHint = false;
            sequenceDescs.onlyDecodeKeyFrame = true;
            sequenceDescs.outPoint = (long) ((mBaseUIClip.getTrimOut() - mBaseUIClip.getTrimIn()) / mBaseUIClip.getSpeed());
            IClip.ThumbNailInfo thumbNailInfo = mBaseUIClip.getThumbNailInfo();
            if (thumbNailInfo != null) {
                sequenceDescs.thumbNailInfo = new MultiThumbnailSequenceView.ThumbNailInfo(thumbNailInfo.urlPrefix, thumbNailInfo.interval, thumbNailInfo.extension, thumbNailInfo.isImage);
            }
            sequenceDescsArray.add(sequenceDescs);
            mNvsMultiThumbnailSequenceView.setThumbnailSequenceDescArray(sequenceDescsArray);
        }

    }

    /**
     * 刷新音频视图
     * Refresh the audio view
     * */
    private void refreshAudioView(boolean isLeft) {
        if (mNvsWaveformView != null && mNvsWaveformView.getVisibility() == View.VISIBLE) {
            if (isLeft) {
//                Log.e(TAG, "refreshAudioView: setTrimIn " +mNvsWaveformView.getTrimIn()+" "+mBaseUIClip.getTrimIn() );
                mNvsWaveformView.setTrimIn(mBaseUIClip.getTrimIn());
            } else {
//                Log.e(TAG, "refreshAudioView: setTrimOut 111 " +mNvsWaveformView.getTrimOut()+" "+ mBaseUIClip.getTrimOut() );
                mNvsWaveformView.setTrimOut(mBaseUIClip.getTrimOut());
//                Log.e(TAG, "refreshAudioView: setTrimOut 222 " +mNvsWaveformView.getTrimOut()+" "+ mBaseUIClip.getTrimOut() );
            }
            mNvsWaveformView.setInPoint(mBaseUIClip.getInPoint());
        }
        if (mCustomWaveView != null && mCustomWaveView.getVisibility() == View.VISIBLE) {
            //mCustomWaveView.changeWidth(mBaseUIClip.getTrimIn(), mBaseUIClip.getTrimOut(), mBaseUIClip.getOriginalDuration());
        }
    }

    /**
     * 添加音频视图
     * Add audio view
     * */
    private void addAudioView() {
        setPadding(5, 0, 5, 0);
        View view = LayoutInflater.from(mContext).inflate(R.layout.item_add_audio_view, mItemViewContainer);
        String leftChannelDataPath = mBaseUIClip.getLeftChannelDataPath();
        mNvsWaveformView = view.findViewById(R.id.view_sdk_wave_form);
        mCustomWaveView = view.findViewById(R.id.view_sdk_wave_form_custom);
        if (TextUtils.isEmpty(leftChannelDataPath)) {
            //mNvsWaveformView.setSingleChannelMode(true);
            if (mBaseUIClip.getSubType() == MeicamAudioClip.AUDIO_RECORD_FILE) {
                mNvsWaveformView.setWaveformColor(getResources().getColor(R.color.audio_record));
            } else {
                mNvsWaveformView.setWaveformColor(getResources().getColor(R.color.audio_music));
            }
            mNvsWaveformView.setBackgroundColor(getResources().getColor(R.color.track_background_color_audio));
            mNvsWaveformView.setData(mBaseUIClip.getFilePath(), mBaseUIClip.getInPoint(),  mBaseUIClip.getTrimIn(), mBaseUIClip.getTrimOut());
           /* mNvsWaveformView.setTrimIn(mBaseUIClip.getTrimIn());
            mNvsWaveformView.setTrimOut(mBaseUIClip.getTrimOut());*/
            mNvsWaveformView.setVisibility(VISIBLE);
            mWaveformHelper.addView(mNvsWaveformView);
            mCustomWaveView.setVisibility(GONE);
        } else {
            mCustomWaveView.setVisibility(VISIBLE);
            mNvsWaveformView.setVisibility(GONE);
            mCustomWaveView.setMaxGroupData(0.5f);
            mCustomWaveView.setWidth(mBaseUIClip.getDuration() / 1000);
            mCustomWaveView.setWaveColor(getResources().getColor(R.color.audio_music));
            mCustomWaveView.addWaveData(leftChannelDataPath, mBaseUIClip.getTrimIn(), mBaseUIClip.getTrimOut(), mBaseUIClip.getOriginalDuration());
        }
    }

    private void addTimeLineView() {
        if (mNvsMultiThumbnailSequenceView != null) {
            refreshVideoView();
        } else {
            View view = LayoutInflater.from(mContext).inflate(R.layout.timeline_editor_pip_view, mItemViewContainer);
            mNvsMultiThumbnailSequenceView = view.findViewById(R.id.pip_multi_thumbnail_sequence_view);
            mNvsMultiThumbnailSequenceView.setStartPadding(0);
            refreshVideoView();
            double pixelPerMicrosecond = PixelPerMicrosecondUtil.getPixelPerMicrosecond(mContext);
            mNvsMultiThumbnailSequenceView.setPixelPerMicrosecond(pixelPerMicrosecond);
            mNvsMultiThumbnailSequenceView.setClickable(true);
            mNvsMultiThumbnailSequenceView.setScrollEnabled(false);
            mNvsMultiThumbnailSequenceView.setThumbnailImageFillMode(NvsMultiThumbnailSequenceView.THUMBNAIL_IMAGE_FILLMODE_ASPECTCROP);
        }
    }

    public MultiThumbnailSequenceView getNvsMultiThumbnailSequenceView() {
        return mNvsMultiThumbnailSequenceView;
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
//        if (mText != null) {
//            canvas.drawText(mText, mTextStartX, mTextStartY + textSize, mTextPaint);
//        }
    }

    public BaseUIClip getBaseUIClip() {
        return mBaseUIClip;
    }

    /**
     * 获取关键帧的选中时间点
     * Get the selected time point of key frame
     *
     * @return the time point时间点
     */
    public long getKeyFrameSelectedPoint() {
        if (mBaseUIClip != null) {
            KeyFrameInfo keyFrameInfo = mBaseUIClip.getKeyFrameInfo();
            if (keyFrameInfo != null) {
                return keyFrameInfo.getSelectedPoint();
            }
        }
        return -1;
    }

    public HandView getHandView() {
        return handView;
    }

    public void setHandView(HandView handView) {
        this.handView = handView;
    }

    /**
     * 添加关键帧
     * Add key frame
     *
     * @param selected true selected选中，false not 不选中
     */
    public boolean addKeyFrameFromTimestamp(long timestamp, boolean selected) {
        if (mBaseUIClip == null) {
            LogUtils.d("add key frame failed,mBaseUIClip is null!!!");
            return false;
        }
        return addKeyFrame(timestamp - mBaseUIClip.getInPoint(), selected);
    }

    /**
     * 更新关键帧信息
     * Update key frame info
     *
     * @param keyFrameInfo the key frame info
     */
    public void updateKeyFrame(KeyFrameInfo keyFrameInfo, long checkTimestamp) {
        if (mBaseUIClip != null) {
            mBaseUIClip.setKeyFrameInfo(keyFrameInfo);
            mTrackViewCover.setKeyFrameInfo(mBaseUIClip.getKeyFrameInfo(), -1);
            if (checkTimestamp >= 0) {
                mTrackViewCover.checkSelectedKeyFrame(mBaseUIClip.getInPoint(), checkTimestamp);
            }
        }

    }

    /**
     * 更新关键帧视图的位置
     * Update key frame view position
     */
    public void updateKeyFramePosition() {
        mTrackViewCover.updateKeyFramePosition();
    }

    /**
     * 添加关键帧
     * Add key frame
     *
     * @param inPoint the in point 入点
     */
    public boolean addKeyFrame(long inPoint, boolean selected) {
        if (mBaseUIClip == null) {
            LogUtils.d("add key frame failed,mBaseUIClip is null!!!");
            return false;
        }
        if (inPoint >= 0 && inPoint <= mBaseUIClip.getOutPoint() - mBaseUIClip.getInPoint()) {
            if (mBaseUIClip.getKeyFrameInfo() == null) {
                mBaseUIClip.setKeyFrameInfo(new KeyFrameInfo());
                mTrackViewCover.setKeyFrameInfo(mBaseUIClip.getKeyFrameInfo(), -1);
            }
            mTrackViewCover.addKeyFrame(inPoint, selected);
            return true;
        }
        LogUtils.d("add key frame failed,in point is illegal!!!");
        return false;
    }

    /**
     * 检查时间戳附近是否有关键帧，如果有就选中，没有则不选中
     */
    public void checkKeyFrame(long timestamp) {
        if (mBaseUIClip == null) {
            LogUtils.d("check key frame,mBaseUIClip is null!!!");
            return;
        }
        if (timestamp >= mBaseUIClip.getInPoint() && timestamp <= mBaseUIClip.getOutPoint()) {
            mTrackViewCover.checkSelectedKeyFrame(mBaseUIClip.getInPoint(), timestamp);
        } else {
            mTrackViewCover.checkSelectedKeyFrame(mBaseUIClip.getInPoint(), -1);
        }
    }

    /**
     * 设置关键帧信息是否可用，可用则显示，不可用不显示
     * Enable Key frame info
     *
     * @param enable true enable ,false not
     */
    public void enableKeyFrame(boolean enable) {
        mTrackViewCover.enableKeyFrame(enable);
    }


    /**
     * 更新音量icon状态
     * Update volume state.
     */
    public void updateVolumeState() {
        if (mVolumeIcon != null) {
            mVolumeIcon.setVisibility((mBaseUIClip != null
                    && CommonData.CLIP_VIDEO.equals(mBaseUIClip.getType())
                    && mBaseUIClip.getVolume() == 0) ? VISIBLE : GONE);

        }
    }

    /**
     * 删除关键帧视图
     * Delete key frame view
     *
     * @param inPoint the in point 入点
     */
    public void deleteKeyFrame(long inPoint) {
        if (mBaseUIClip == null) {
            LogUtils.d("delete key frame failed,mBaseUIClip is null!!!");
            return;
        }
        mTrackViewCover.deleteKeyFrame(mBaseUIClip.getInPoint(), inPoint);
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        width = 100;
        if (mBaseUIClip != null) {
            long duration = (long) ((mBaseUIClip.getTrimOut() - mBaseUIClip.getTrimIn()) / mBaseUIClip.getSpeed());
            width = PixelPerMicrosecondUtil.durationToLength(duration);
        }
        widthMeasureSpec = MeasureSpec.makeMeasureSpec(width - BASE_ITEM_MARGIN,
                MeasureSpec.EXACTLY);
        heightMeasureSpec = MeasureSpec.makeMeasureSpec(mViewHeight,
                MeasureSpec.EXACTLY);
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
    }

    /**
     * Gets background color
     * 获取背景颜色
     *
     * @return the background color by track type
     */
    public int getBackgroundColor(BaseUIClip baseUIClip) {
        try {
            return getResources().getColor(baseUIClip.getBackGroundColor());
        } catch (Exception e) {
            LogUtils.e(e.getMessage());
        }
        return getResources().getColor(R.color.black);
    }

    /**
     * Add cover view.
     * 添加封面视图
     */
    public void addVideoAnimationView() {
        if (mVideoAnimationIn == null) {
            mVideoAnimationIn = new View(mContext);
        }
        double trimIn = mBaseUIClip.getAnimationTrimIn();
        double trimOut = mBaseUIClip.getAnimationTrimOut();
        long duration = mBaseUIClip.getDuration();
        long animationDuration = (long) (trimOut - trimIn);
        if (animationDuration <= 0) {
            RelativeLayout.LayoutParams innerLayoutParams = new RelativeLayout.LayoutParams(0, ViewGroup.LayoutParams.MATCH_PARENT);
            mVideoAnimationIn.setLayoutParams(innerLayoutParams);
        } else {
            int width = PixelPerMicrosecondUtil.durationToLength(duration);
            int leftMargin = (int) (trimIn * 1.0F / duration * width);
            int rightMargin = PixelPerMicrosecondUtil.durationToLength((long) (duration - trimOut));
            RelativeLayout.LayoutParams innerLayoutParams = new RelativeLayout.LayoutParams(width, ViewGroup.LayoutParams.MATCH_PARENT);
            innerLayoutParams.leftMargin = leftMargin;
            innerLayoutParams.rightMargin = rightMargin;
            mVideoAnimationIn.setLayoutParams(innerLayoutParams);
            mVideoAnimationIn.setBackgroundResource(R.drawable.animation_cover_in);
        }

        if (mVideoAnimationOut == null) {
            mVideoAnimationOut = new View(mContext);
        }
        double trimIn2 = mBaseUIClip.getAnimationTrimIn2();
        double trimOut2 = mBaseUIClip.getAnimationTrimOut2();
        long animationDuration2 = (long) (trimOut2 - trimIn2);
        if (animationDuration2 <= 0) {
            RelativeLayout.LayoutParams innerLayoutParams = new RelativeLayout.LayoutParams(0, ViewGroup.LayoutParams.MATCH_PARENT);
            mVideoAnimationOut.setLayoutParams(innerLayoutParams);
        } else {
            int width = PixelPerMicrosecondUtil.durationToLength(duration);
            int leftMargin = (int) (trimIn2 * 1.0F / duration * width);
            int rightMargin = PixelPerMicrosecondUtil.durationToLength((long) (duration - trimOut2));
            RelativeLayout.LayoutParams innerLayoutParams = new RelativeLayout.LayoutParams(width, ViewGroup.LayoutParams.MATCH_PARENT);
            innerLayoutParams.leftMargin = leftMargin;
            innerLayoutParams.rightMargin = rightMargin;
            mVideoAnimationOut.setLayoutParams(innerLayoutParams);
            mVideoAnimationOut.setBackgroundResource(R.drawable.animation_cover_out);
        }

        invalidate();
    }
}
