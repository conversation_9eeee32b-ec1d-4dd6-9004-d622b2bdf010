package com.meishe.myvideo.ui.bean;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateDate :2020/10/29 14:54
 * @Description :缩略图序列所需的实体类 The entity class required for the thumbnail sequence
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class ThumbnailClip extends BaseTrackClip {
    private String assetPath;
    /**
     * 是否是静态图片
     * Is it still picture？
     */
    private boolean staticMap;
    /**
     * 是否是只解码关键帧
     * Whether to decode only key frames
     */
    private boolean onlyDecodeKeyFrame;
    /**
     * 当前序列的缩略图横纵比，为0表示使用控件的缩略图横纵比
     * The horizontal and vertical ratio of the thumbnail of the current sequence.
     * A value of 0 indicates that the horizontal and vertical ratio of the thumbnail of the control is used
     */
    private float thumbnailAspectRatio;
    /**
     * 尾部信息
     * Tail information
     */
    private TailInfo tailInfo;
    /**
     * 动画信息
     * Animation information
     */
    private AnimationInfo animationInfo;

    /**
     * 缩略图信息，用于互通版本
     * Thumbnail information for inter working version
     */
    private ThumbNailInfo thumbNailInfo;

    public boolean isStaticMap() {
        return staticMap;
    }

    @Override
    public void setAssetPath(String assetPath) {
        this.assetPath = assetPath;
    }

    @Override
    public String getAssetPath() {
        return assetPath;
    }

    public ThumbnailClip setStaticMap(boolean staticMap) {
        this.staticMap = staticMap;
        return this;
    }

    public boolean isOnlyDecodeKeyFrame() {
        return onlyDecodeKeyFrame;
    }

    public ThumbnailClip setOnlyDecodeKeyFrame(boolean onlyDecodeKeyFrame) {
        this.onlyDecodeKeyFrame = onlyDecodeKeyFrame;
        return this;
    }

    public float getThumbnailAspectRatio() {
        return 1;
    }

    public ThumbnailClip setThumbnailAspectRatio(float thumbnailAspectRatio) {
        this.thumbnailAspectRatio = thumbnailAspectRatio;
        return this;
    }

    public TailInfo getTailInfo() {
        if (tailInfo == null) {
            tailInfo = new TailInfo();
        }
        return tailInfo;
    }

    public ThumbnailClip setTailInfo(TailInfo tailInfo) {
        this.tailInfo = tailInfo;
        return this;
    }

    public AnimationInfo getAnimationInfo() {
        if(animationInfo == null){
            animationInfo = new AnimationInfo();
        }
        return animationInfo;
    }

    public void setAnimationInfo(AnimationInfo animationInfo) {
        this.animationInfo = animationInfo;
    }

    @Override
    public ThumbNailInfo getThumbNailInfo() {
        return thumbNailInfo;
    }

    @Override
    public void setThumbNailInfo(ThumbNailInfo thumbNailInfo) {
        this.thumbNailInfo = thumbNailInfo;
    }

    public static class TailInfo {
        /**
         * 封面路径
         * Cover path
         */
        private String coverPath;
        /**
         * 封面资源Id
         * Cover resource id
         */
        private int coverId;
        /**
         * 类型
         * type
         */
        private int type = -1;
        /**
         * id
         */
        private String id;

        public String getCoverPath() {
            return coverPath;
        }

        public TailInfo setCoverPath(String coverPath) {
            this.coverPath = coverPath;
            return this;
        }

        public int getCoverId() {
            return coverId;
        }

        public TailInfo setCoverId(int coverId) {
            this.coverId = coverId;
            return this;
        }

        public int getType() {
            return type;
        }

        public TailInfo setType(int type) {
            this.type = type;
            return this;
        }

        public String getId() {
            return id;
        }

        public TailInfo setId(String id) {
            this.id = id;
            return this;
        }

        public void clear() {
            setCoverPath("");
            setCoverId(0);
            setId("");
            setType(-1);
        }

        public void update(TailInfo info) {
            setCoverPath(info.getCoverPath());
            setId(info.getId());
            setType(info.getType());
        }
    }
}
