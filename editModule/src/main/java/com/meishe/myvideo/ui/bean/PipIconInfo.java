package com.meishe.myvideo.ui.bean;

import android.graphics.Bitmap;
import android.graphics.Rect;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> LiHangZhou
 * @CreateDate :2021/4/15 14:40
 * @Description :画中画图标相关信息 PIP line info
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class PipIconInfo {
    /**
     * 图标区域
     * Icon area
     */
    private Rect rect;
    /**
     * 图标
     * Icon
     */
    private Bitmap bitmap;
    /**
     * 该图标所对应的画中画片段所在轨道索引
     * This icon corresponds to the track index of the pip clip
     */
    private int trackIndex;
    /**
     * 该图标所对应的画中画片段的入点
     * This icon corresponds to the in point of the pip clip
     */
    private long inPoint;
    /**
     * 该图标所对应的画中画片段的出点
     * This icon corresponds to the out point of the pip clip
     */
    private long outPoint;

    public Rect getRect() {
        return rect;
    }

    public void setRect(Rect rect) {
        this.rect = rect;
    }

    public Bitmap getBitmap() {
        return bitmap;
    }

    public void setBitmap(Bitmap bitmap) {
        this.bitmap = bitmap;
    }

    public int getTrackIndex() {
        return trackIndex;
    }

    public void setTrackIndex(int trackIndex) {
        this.trackIndex = trackIndex;
    }


    public long getInPoint() {
        return inPoint;
    }

    public void setInPoint(long inPoint) {
        this.inPoint = inPoint;
    }

    public long getOutPoint() {
        return outPoint;
    }

    public void setOutPoint(long outPoint) {
        this.outPoint = outPoint;
    }

    public boolean isAvailable() {
        return bitmap != null && !bitmap.isRecycled();
    }
}
