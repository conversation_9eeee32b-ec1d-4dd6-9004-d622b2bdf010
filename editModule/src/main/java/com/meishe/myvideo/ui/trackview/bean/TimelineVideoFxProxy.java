package com.meishe.myvideo.ui.trackview.bean;

import com.meishe.base.utils.Utils;
import com.meishe.engine.EditorEngine;
import com.meishe.engine.bean.CommonData;
import com.meishe.engine.bean.KeyFrameProcessor;
import com.meishe.engine.bean.MeicamTimeline;
import com.meishe.engine.bean.MeicamTimelineVideoFxClip;
import com.meishe.engine.bean.MeicamTrackVideoFx;
import com.meishe.engine.bean.MeicamVideoClip;
import com.meishe.engine.bean.MeicamVideoFx;
import com.meishe.engine.bean.MeicamVideoTrack;
import com.meishe.engine.command.ClipCommand;
import com.meishe.myvideo.R;
import com.meishe.myvideo.ui.bean.BaseUIClip;

import java.util.ArrayList;
import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2021/5/25 11:04
 * @Description :时间线特效代理 Timeline fx proxy
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class TimelineVideoFxProxy extends BaseUIClip {

    private MeicamTimelineVideoFxClip mFxClip;

    public TimelineVideoFxProxy(MeicamTimelineVideoFxClip captionClip, int trackIndex) {
        super(CommonData.CLIP_TIMELINE_FX, trackIndex);
        this.mFxClip = captionClip;
        if (captionClip != null) {
            super.setInPoint(captionClip.getInPoint());
            super.setOutPoint(captionClip.getOutPoint());
        }
    }

    @Override
    public int getSubType() {
//        if (mFxClip != null) {
//            return mFxClip.getAudioType();
//        }
        return INVALID;
    }

    @Override
    public int getClipIndexInTrack() {
        if (mFxClip != null) {
            return mFxClip.getIndex();
        }
        return INVALID;
    }

    @Override
    public void setInPoint(long inPoint) {
        if (mFxClip != null) {
            ClipCommand.setInPoint(mFxClip, inPoint);
        }
        super.setInPoint(inPoint);
    }

    @Override
    public void setOutPoint(long outPoint) {
        if (mFxClip != null) {
            ClipCommand.setOutPoint(mFxClip, outPoint);
        }
        super.setOutPoint(outPoint);
    }

    @Override
    public long getOutPoint() {
        if (mFxClip != null) {
            return mFxClip.getOutPoint();
        }
        return super.getOutPoint();
    }

    @Override
    public long getInPoint() {
        if (mFxClip != null) {
            return mFxClip.getInPoint();
        }
        return super.getInPoint();
    }

    @Override
    public double getSpeed() {
        return 1.0D;
    }

    @Override
    public String getIconFilePath() {
        return "";
    }

    @Override
    public String getFilePath() {
        return "";
    }


    @Override
    public float[] getRecordArray() {
        return new float[0];
    }

    @Override
    public void setDuration(long duration) {
        super.setDuration(duration);
    }

    @Override
    public long getDuration() {
        if (mFxClip != null) {
            return mFxClip.getOutPoint() - mFxClip.getInPoint();
        }
        return super.getOutPoint() - super.getInPoint();
    }

    @Override
    public long getFadeIn() {
        return INVALID;
    }

    @Override
    public long getFadeOut() {
        return INVALID;
    }

    @Override
    public String getDisplayName() {
        if (mFxClip != null) {
            return mFxClip.getDisplayName();
        }
        return null;
    }

    @Override
    public boolean canExceedLength() {
        return true;
    }

    @Override
    public boolean canDrag() {
        return true;
    }

    @Override
    public int getBackGroundColor() {
        if (mFxClip.isBuildFx()){
            return R.color.track_background_color_fx_build;
        }
        return R.color.track_background_color_fx;
    }

    @Override
    public KeyFrameProcessor keyFrameProcessor() {
        if (mFxClip != null) {
            return mFxClip.keyFrameProcessor();
        }
        return super.keyFrameProcessor();
    }

    @Override
    public List<String> getTag() {
        if (mFxClip.isBuildFx()){
            return super.getTag();
        }
        if (mFxClip != null) {
            List<String> tagList = new ArrayList<>(1);
            if (mFxClip.getIntensity() == 1) {
                tagList.add(Utils.getApp().getString(R.string.track_view_clip_tag_all));
                return tagList;
            }
            MeicamTimeline currentTimeline = EditorEngine.getInstance().getCurrentTimeline();
            if (currentTimeline != null) {
                MeicamVideoTrack videoTrack = currentTimeline.getVideoTrack(0);
                if (videoTrack != null) {
                    int fxCount = videoTrack.getVideoFxCount();
                    for (int index = 0; index < fxCount; index++) {
                        MeicamTrackVideoFx videoFx = videoTrack.getVideoFx(index);
                        if (videoFx.hasTag(mFxClip.getExtraTag())) {
                            tagList.add(Utils.getApp().getString(R.string.track_view_clip_tag_main));
                            return tagList;
                        }
                    }
                }
                int trackCount = currentTimeline.videoTrackCount();
                if (trackCount > 1) {
                    for (int trackIndex = 1; trackIndex < trackCount; trackIndex++) {
                        MeicamVideoTrack track = currentTimeline.getVideoTrack(trackIndex);
                        for (int clipIndex = 0; clipIndex < track.getClipCount(); clipIndex++) {
                            MeicamVideoClip videoClip = track.getVideoClip(clipIndex);
                            if (videoClip == null) {
                                continue;
                            }
                            for (int fxIndex = 0; fxIndex < videoClip.getVideoFxCount(); fxIndex++) {
                                MeicamVideoFx videoFx = videoClip.getVideoFx(fxIndex);
                                if (videoFx.hasTag(mFxClip.getExtraTag())) {
                                    tagList.add(Utils.getApp().getString(R.string.track_view_clip_tag_pip));
                                    return tagList;
                                }
                            }
                        }
                    }
                }
            }
        }
        return super.getTag();
    }
}
