package com.meishe.myvideo.ui.trackview;

import java.util.HashSet;
import java.util.Set;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2023/12/14 10:41
 * @Description :
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class WaveformHelper {
    private Set<NvsWaveformViewWrap> mWaveformViewWrapSet = new HashSet<>();

    public void addView(NvsWaveformViewWrap view){
        mWaveformViewWrapSet.add(view);
    }
    public void notifyScrollChanged(int x, int oldX) {
        for (NvsWaveformViewWrap nvsWaveformViewWrap : mWaveformViewWrapSet) {
            nvsWaveformViewWrap.onScrollChanged(x, oldX);
        }
    }

    public void notifyScaleChanged(float scale) {
        for (NvsWaveformViewWrap nvsWaveformViewWrap : mWaveformViewWrapSet) {
            nvsWaveformViewWrap.scale(scale);
        }
    }
}
