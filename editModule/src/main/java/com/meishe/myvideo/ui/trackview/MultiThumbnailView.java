//================================================================================
//
// (c) Copyright China Digital Video (Beijing) Limited, 2017. All rights reserved.
//
// This code and information is provided "as is" without warranty of any kind,
// either expressed or implied, including but not limited to the implied
// warranties of merchantability and/or fitness for a particular purpose.
//
//--------------------------------------------------------------------------------
//   Birth Date:    Aug 30. 2017
//   Author:        NewAuto video team
//================================================================================
package com.meishe.myvideo.ui.trackview;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Rect;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.os.Handler;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.Log;
import android.view.Gravity;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.HorizontalScrollView;
import android.widget.ImageView;
import android.widget.LinearLayout;

import androidx.annotation.RequiresApi;

import com.meicam.sdk.NvsAVFileInfo;
import com.meicam.sdk.NvsIconGenerator;
import com.meicam.sdk.NvsRational;
import com.meicam.sdk.NvsStreamingContext;
import com.meicam.sdk.NvsUtils;
import com.meishe.base.utils.ImageLoader;
import com.meishe.base.utils.LogUtils;
import com.meishe.engine.constant.NvsConstants;
import com.meishe.myvideo.R;
import com.meishe.myvideo.ui.bean.AnimationInfo;
import com.meishe.myvideo.ui.bean.BaseTrackClip;
import com.meishe.myvideo.ui.bean.ITrackClip;
import com.meishe.myvideo.ui.bean.KeyFrameInfo;
import com.meishe.myvideo.ui.bean.SpeedInfo;
import com.meishe.myvideo.ui.bean.ThumbnailClip;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.SortedMap;
import java.util.TreeMap;

import static com.meishe.myvideo.ui.bean.BaseTrackClip.MIN_DURATION;


/*! \if ENGLISH
 *   \brief Multiple thumbnail sequence
 *
 *   A multi-thumbnail sequence displays a sequence of thumbnails of multiple segments within a timeline. It supports the adjustment of the thumbnail time scale, and supports scrolling when the effective content is too long.
 *   \warning In the NvsMultiThumbnailSequenceView class, all public APIs are used in the UI thread! ! !
 *   \else
 *   \brief 多缩略图序列
 *
 *   多缩略图序列，可以显示一个时间线内多个片段的缩略图序列。支持缩略图时间比例尺的调节，当有效内容超长时支持滚动浏览。
 *   \warning NvsMultiThumbnailSequenceView类中，所有public API都在UI线程使用！！！
 *   \endif
 *   \since 1.10.0
 */
public class MultiThumbnailView extends HorizontalScrollView
        implements NvsIconGenerator.IconCallback {
    private static final String TAG = "MultiThumbnailView";
    private NvsIconGenerator m_iconGenerator = null;
    private boolean m_scrollEnabled = true;
    private boolean needUpdate = true;
    /*! \anchor THUMBNAIL_IMAGE_FILLMODE */
    /*!
     *  \if ENGLISH
     *   @name image fill mode
     *  \else
     *   @name 图片填充模式
     *  \endif
     */
    /*! @name 图片填充模式 */
    //!@{

    public static final int THUMBNAIL_IMAGE_FILLMODE_STRETCH = 0;    //!< \if ENGLISH Image zoom to fill the full window without maintaining the original scale (default mode) \else 图片缩放来填充满窗口，不保持原比例 (默认模式) \endif
    public static final int THUMBNAIL_IMAGE_FILLMODE_ASPECTCROP = 1; //!< \if ENGLISH The image fills the full window evenly and scales if necessary \else 图片按比例均匀填充满窗口，必要时进行裁剪 \endif

    // These two flags control the cached keyframe only mode and whether it is still valid
    private static final int THUMBNAIL_SEQUENCE_FLAGS_CACHED_KEYFRAME_ONLY = 1;
    private static final int THUMBNAIL_SEQUENCE_FLAGS_CACHED_KEYFRAME_ONLY_VALID = 2;

    /*! \if ENGLISH
     *   \brief Interface for monitoring horizontal scrolling.
     *   \else
     *   \brief 用于监听水平滚动的接口
     *   \endif
     */
    public interface OnScrollChangeListener {
        void onScrollChanged(MultiThumbnailView view, int x, int oldx);
    }

    private OnScrollChangeListener m_scrollChangeListener;

    private boolean mIsTrimming = false;

    /*! \if ENGLISH
     *   \brief Multi-thumbnail sequence information description
     *   \else
     *   \brief 多缩略图序列信息描述
     *   \endif
     *   \since 1.10.0
     */
    public static class ThumbnailSequenceDesc {
        public String mediaFilePath;        //!< \if ENGLISH Video file path \else 视频文件路径 \endif
        public long inPoint;                //!< \if ENGLISH Timeline in point (in microseconds) \else 时间线上入点(单位微秒) \endif
        public long outPoint;               //!< \if ENGLISH Timeline out point (in microseconds) \else 时间线上出点(单位微秒) \endif
        public long trimIn;                 //!< \if ENGLISH Trim in point (in microseconds) \else 裁剪入点(单位微秒) \endif
        public long trimOut;                //!< \if ENGLISH Trim out point (in microseconds) \else 裁剪出点(单位微秒) \endif
        public boolean stillImageHint;      //!< \if ENGLISH Whether it is a static picture \else 是否是静态图片 \endif
        public boolean onlyDecodeKeyFrame;  //!< \if ENGLISH Whether decode only key frames \else 是否是只解码关键帧 \endif
        public float thumbnailAspectRatio;  //!< \if ENGLISH Thumbnail's aspect ratio of this sequence, 0 means comply with the thumbnail's aspect ratio of the view \else 当前序列的缩略图横纵比，为0表示使用控件的缩略图横纵比 \endif

        public ThumbnailSequenceDesc() {
            inPoint = 0;
            outPoint = 4000000;
            trimIn = 0;
            trimOut = 4000000;
            stillImageHint = false;
            onlyDecodeKeyFrame = false;
            thumbnailAspectRatio = 0;
        }
    }

    private List<ITrackClip> mTrackClipList;
    private float m_thumbnailAspectRatio = 1;
    private double m_pixelPerMicrosecond = 1080.0 / 15000000;
    private int m_startPadding = 0;
    private int m_endPadding = 0;
    private int m_thumbnailImageFillMode = THUMBNAIL_IMAGE_FILLMODE_STRETCH;
    private long m_maxTimelinePosToScroll = 0;

    private static class ThumbnailSequence {
        int m_index;
        String type;
        String m_mediaFilePath;
        long m_inPoint;
        long m_outPoint;
        long m_trimIn;
        long m_trimDuration;
        boolean m_stillImageHint;
        boolean m_onlyDecodeKeyFrame;
        public float m_thumbnailAspectRatio;
        private ThumbnailClip.TailInfo tailInfo;
        private AnimationInfo animationInfo;
        private KeyFrameInfo keyFrameInfo;
        private SpeedInfo speedInfo;
        private boolean hasProp;
        private boolean hasAudio;
        private ITrackClip.ThumbNailInfo thumbNailInfo;

        int m_flags;

        int m_x; // Relative to content view
        int m_width;
        int m_thumbnailWidth;


        public ThumbnailSequence() {
            m_index = 0;
            m_inPoint = 0;
            m_outPoint = 0;
            m_trimIn = 0;
            m_trimDuration = 1;
            m_stillImageHint = false;
            m_onlyDecodeKeyFrame = false;
            m_thumbnailAspectRatio = 0;
            m_flags = 0;
            m_x = 0;
            m_width = 0;
            m_thumbnailWidth = 0;
        }

        public long calcTimestampFromX(int x, long duratinPerThumbnail) {
            long timestamp = m_trimIn + (long) ((double) (x - m_x) / m_width * m_trimDuration + 0.5);
            long timestamp1 = (long) (((double) timestamp / duratinPerThumbnail)) * duratinPerThumbnail;
            return timestamp1;
        }

        public long calcTimestampFromX(int x) {
            long timestamp = m_trimIn + (long) (Math.floor(x - m_x) / m_width * m_trimDuration + 0.5);
            return timestamp;
        }
    }

    private ArrayList<ThumbnailSequence> m_thumbnailSequenceArray = new ArrayList<ThumbnailSequence>();
    private TreeMap<Integer, ThumbnailSequence> m_thumbnailSequenceMap = new TreeMap<Integer, ThumbnailSequence>();
    private int m_contentWidth = 0;

    private static class ThumbnailId implements Comparable<ThumbnailId> {
        public int m_seqIndex;
        public long m_timestamp;

        public ThumbnailId(int seqIndex, long timestamp) {
            m_seqIndex = seqIndex;
            m_timestamp = timestamp;
        }

        @Override
        public int compareTo(ThumbnailId o) {
            if (m_seqIndex < o.m_seqIndex) {
                return -1;
            } else if (m_seqIndex > o.m_seqIndex) {
                return 1;
            } else {
                if (m_timestamp < o.m_timestamp)
                    return -1;
                else if (m_timestamp > o.m_timestamp)
                    return 1;
                else
                    return 0;
            }
        }
    }

    private static class Thumbnail {
        ThumbnailSequence m_owner;
        long m_timestamp;
        ImageView m_imageView;
        long m_iconTaskId;
        boolean m_imageViewUpToDate;
        boolean m_touched;

        public Thumbnail() {
            m_timestamp = 0;
            m_iconTaskId = 0;
            m_imageViewUpToDate = false;
            m_touched = false;
        }
    }

    private TreeMap<ThumbnailId, Thumbnail> m_thumbnailMap = new TreeMap<ThumbnailId, Thumbnail>();
    Bitmap m_placeholderBitmap;
    private int m_maxThumbnailWidth = 0;
    private boolean m_updatingThumbnail = false;

    private class ContentView extends ViewGroup {
        public ContentView(Context context) {
            super(context);
        }

        /**
         * Any layout manager that doesn't scroll will want this.
         */
        @Override
        public boolean shouldDelayChildPressedState() {
            return false;
        }

        @Override
        protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
            // NOTE: At the time our size is being measured
            // The content width may not be ready!
            int w = m_contentWidth, h;

            int heightMode = MeasureSpec.getMode(heightMeasureSpec);
            int heightSize = MeasureSpec.getSize(heightMeasureSpec);
            if (heightMode == MeasureSpec.EXACTLY || heightMode == MeasureSpec.AT_MOST)
                h = heightSize;
            else
                h = MultiThumbnailView.this.getHeight(); // Shouldn't reach here

            // Check against our minimum height and width
            w = Math.max(w, getSuggestedMinimumWidth());
            h = Math.max(h, getSuggestedMinimumHeight());

            w = resolveSizeAndState(w, widthMeasureSpec, 0);
            h = resolveSizeAndState(h, heightMeasureSpec, 0);

            setMeasuredDimension(w, h);
        }

        @Override
        protected void onLayout(boolean changed, int left, int top, int right, int bottom) {
            if (needUpdate) {
                updateThumbnails();
            }
        }

        @Override
        protected void onSizeChanged(int w, int h, int oldw, int oldh) {
            if (h != oldh)
                requestUpdateThumbnailSequenceGeometry();

            super.onSizeChanged(w, h, oldw, oldh);
        }
    }

    private ContentView m_contentView;

    public MultiThumbnailView(Context context) {
        super(context);
        NvsUtils.checkFunctionInMainThread();
        init(context);
    }

    public MultiThumbnailView(Context context, AttributeSet attrs) {
        super(context, attrs);
        NvsUtils.checkFunctionInMainThread();
        init(context);
    }

    public MultiThumbnailView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        NvsUtils.checkFunctionInMainThread();
        init(context);
    }

    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    public MultiThumbnailView(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        NvsUtils.checkFunctionInMainThread();
        init(context);
    }

    /*! \if ENGLISH
     *   \brief Sets the thumbnail sequence description array
     *   \param descArray The thumbnail sequence describes the array. Note: Once it is set, modifying the contents of the array will not work unless thumbnail sequence description is set array again.
     *   \else
     *   \brief 设置缩略图序列描述数组
     *   \param descArray 缩略图序列描述数组。注意：一旦设置，再修改数组里面的内容是不起作用的，除非再次设置缩略图序列描述数组
     *   \endif
     *   \sa getThumbnailSequenceDescArray
     */
    public void setThumbnailList(List<ITrackClip> trackClipList) {
        NvsUtils.checkFunctionInMainThread();
        if (trackClipList == mTrackClipList)
            return;

        m_thumbnailSequenceArray.clear();
        m_placeholderBitmap = null;

        mTrackClipList = trackClipList;
        if (trackClipList != null) {
            long lastOutPoint = 0;
            int index = 0;
            for (int i = 0; i < mTrackClipList.size(); i++) {
                ITrackClip uiClip = mTrackClipList.get(i);
                //重新调整索引位置
                uiClip.setIndexInTrack(i);
                if (!(uiClip instanceof ThumbnailClip) || TextUtils.isEmpty(uiClip.getAssetPath()) ||
                        uiClip.getInPoint() < lastOutPoint || uiClip.getOutPoint() <= uiClip.getInPoint() ||
                        uiClip.getTrimIn() < 0 || uiClip.getTrimOut() <= uiClip.getTrimIn()) {
                    Log.e(TAG, "Invalid Thumbnail uiClip!=" + uiClip);
//                    mTrackClipList.remove(i);
//                    --i;
//                    continue;
                }
                if (uiClip.getIndexInTrack() != index) {
                    uiClip.setIndexInTrack(index);
                }
                m_thumbnailSequenceArray.add(getThumbnailSequence((ThumbnailClip) uiClip));
                lastOutPoint = uiClip.getOutPoint();
                ++index;
            }
        }

        updateThumbnailSequenceGeometry();
    }

    private void setThumbnailSequence(ThumbnailClip thumbnailClip, ThumbnailSequence sequence) {
        sequence.m_index = thumbnailClip.getIndexInTrack();
        sequence.type = thumbnailClip.getType();
        sequence.m_mediaFilePath = thumbnailClip.getAssetPath();
        sequence.m_inPoint = thumbnailClip.getInPoint();
        sequence.m_outPoint = thumbnailClip.getOutPoint();
        sequence.m_trimIn = thumbnailClip.getTrimIn();
        sequence.m_trimDuration = Math.max(thumbnailClip.getTrimOut() - thumbnailClip.getTrimIn(), 1);
        sequence.m_stillImageHint = thumbnailClip.isStaticMap();
        sequence.m_onlyDecodeKeyFrame = thumbnailClip.isOnlyDecodeKeyFrame();
        sequence.m_thumbnailAspectRatio = thumbnailClip.getThumbnailAspectRatio();
        sequence.tailInfo = thumbnailClip.getTailInfo();
        sequence.animationInfo = thumbnailClip.getAnimationInfo();
        sequence.speedInfo = thumbnailClip.getSpeedInfo();
        sequence.keyFrameInfo = thumbnailClip.getKeyFrameInfo();
        sequence.hasProp = thumbnailClip.hasProp();
        sequence.hasAudio = thumbnailClip.getVolume() != 0;
        sequence.thumbNailInfo = thumbnailClip.getThumbNailInfo();
    }

    private ThumbnailSequence getThumbnailSequence(ThumbnailClip thumbnailClip) {
        ThumbnailSequence sequence = new ThumbnailSequence();
        setThumbnailSequence(thumbnailClip, sequence);
        return sequence;
    }

    /*! \if ENGLISH
     *   \brief Gets the thumbnail sequence description array
     *   \return Returns the obtained thumbnail sequence description array.
     *   \else
     *   \brief 获取缩略图序列描述数组
     *   \return 返回获取的缩略图序列描述数组
     *   \endif
     *   \sa setThumbnailSequenceDescArray
     */
    public List<ITrackClip> getThumbnailList() {
        return mTrackClipList;
    }

    /**
     * 分割缩略图片段
     * Split thumbnail clip
     *
     * @param outP1     the out point of pre-clip 前片段的出点
     * @param trimOutP1 the trim out point of pre-clip 前片段的裁出点
     * @param inP2      the in point of post-clip 后片段的出点
     * @param trimInP2  the trim in point of post-clip 后片段的裁出点
     * @param splitClip ITrackClip the The clip to be split 要分割的片段
     */
    public ITrackClip splitThumbnail(long outP1, long trimOutP1, long inP2, long trimInP2, ITrackClip splitClip) {
        if (splitClip != null && splitClip.getIndexInTrack() >= 0 && splitClip.getIndexInTrack() < m_thumbnailSequenceArray.size()) {
            /*新建后片段
            * New clip
            * */
            ThumbnailClip secondClip = (ThumbnailClip) splitClip.copy();
            secondClip.setIndexInTrack(splitClip.getIndexInTrack() + 1);
            secondClip.setInPoint(inP2);
            secondClip.setTrimIn(trimInP2);


            /*更改分割片段成为前片段
            * Change the split clip to the previous clip
            * */
            ThumbnailClip splitThumbnailClip = (ThumbnailClip) splitClip;
            splitThumbnailClip.setTrimOut(trimOutP1);
            splitThumbnailClip.setOutPoint(outP1);
            /*前片段的尾部信息清空，因为尾部信息跟后片段
            * The tail information of the previous segment is cleared because the tail information follows the following segment
            * */
            splitThumbnailClip.getTailInfo().clear();
            AnimationInfo animationInfo = splitThumbnailClip.getAnimationInfo();
            ThumbnailSequence splitThumbnail = m_thumbnailSequenceArray.get(splitThumbnailClip.getIndexInTrack());
            splitThumbnail.m_trimDuration = Math.max(trimOutP1 - splitThumbnailClip.getTrimIn(), 1);
            splitThumbnail.m_outPoint = outP1;
            splitThumbnail.m_width = calculateThumbnailX(splitThumbnail.m_outPoint) - calculateThumbnailX(splitThumbnail.m_inPoint);
            if (!animationInfo.isEmpty()) {
                //存在入场动画或者组合动画
                // There is entrance animation or combination animation
                if (animationInfo.hasGroupOrInAnimation()) {
                    /*入场、组合动画，跟前片段
                    * Entrance, combination of animation, follow the clip
                    * */
                    if (splitClip.getOutPoint() < (animationInfo.getOutPoint() - animationInfo.getInPoint())) {
                        animationInfo.setOutPoint(splitClip.getOutPoint());
                    }
                    splitThumbnail.animationInfo = animationInfo;
                    secondClip.setAnimationInfo(new AnimationInfo());
                }

                //存在出动画，目前出动画后半段视频是不存在的
                // There is animation, and the second half of the video does not exist
                if (animationInfo.hasOutAnimation()) {

                    if (splitClip.getOutPoint() < (animationInfo.getOutPoint2() - animationInfo.getInPoint2())) {
                        animationInfo.setOutPoint(splitClip.getOutPoint());
                    }
                    splitThumbnail.animationInfo = animationInfo;
                    secondClip.setAnimationInfo(new AnimationInfo());

//                    animationInfo = secondClip.getAnimationInfo();
//                    /*出场动画，跟后片段 Exit animation, followed by clip*/
//                    if (secondClip.getInPoint() > animationInfo.getInPoint2()) {
//                        animationInfo.setInPoint2(0);
//                    } else {
//                        animationInfo.setInPoint2((secondClip.getOutPoint() - secondClip.getInPoint()) -
//                                (animationInfo.getOutPoint2() - animationInfo.getInPoint2()));
//                    }
//                    splitThumbnail.animationInfo = new AnimationInfo();
//                    splitThumbnailClip.setAnimationInfo(splitThumbnail.animationInfo);
                }
            }
            KeyFrameInfo keyFrameInfo = splitThumbnailClip.getKeyFrameInfo();
            Map<Long, KeyFrameInfo.Info> keyFrameMap = keyFrameInfo.getKeyFrameMap();
            boolean needAddExtraKeyFrame = false;
            if (keyFrameMap != null && keyFrameMap.size() > 0) {
                long atTime = splitThumbnailClip.getOutPoint() - splitThumbnailClip.getInPoint();
                /*删除后边的关键帧
                * Delete the keys of the rear edge
                * */
                Iterator<Map.Entry<Long, KeyFrameInfo.Info>> it = keyFrameMap.entrySet().iterator();
                while (it.hasNext()) {
                    Map.Entry<Long, KeyFrameInfo.Info> entry = it.next();
                    if (entry.getValue().getAtTime() > atTime) {
                        it.remove();
                        needAddExtraKeyFrame = true;
                    }
                }
                /*在两个关键帧之间分割，前一片段的尾部需要添加关键帧
                * Split between two keyframes, and add keyframes at the end of the previous clip
                * */
                if (needAddExtraKeyFrame && keyFrameMap.size() > 0) {
                    keyFrameMap.put(atTime, KeyFrameInfo.Info.create(atTime));
                }

            }
            /*新建后片段对应的缩略图序列，计算其相关属性并添加到集合中
            *The thumbnail sequence corresponding to the new clip is calculated and added to the collection.
            * */
            ThumbnailSequence secondSequence = getThumbnailSequence(secondClip);
            secondSequence.m_index = secondClip.getIndexInTrack();
            secondSequence.m_flags &= ~THUMBNAIL_SEQUENCE_FLAGS_CACHED_KEYFRAME_ONLY_VALID;
            secondSequence.m_x = calculateThumbnailX(secondSequence.m_inPoint);
            secondSequence.m_width = calculateThumbnailX(secondSequence.m_outPoint) - secondSequence.m_x;
            float thumbnailAspectRatio = secondSequence.m_thumbnailAspectRatio > 0 ?
                    secondSequence.m_thumbnailAspectRatio : m_thumbnailAspectRatio;
            secondSequence.m_thumbnailWidth = (int) Math.floor(getHeight() * thumbnailAspectRatio + 0.5);
            secondSequence.m_thumbnailWidth = Math.max(secondSequence.m_thumbnailWidth, 1);
            m_thumbnailSequenceArray.add(secondClip.getIndexInTrack(), secondSequence);
            m_thumbnailSequenceMap.put(secondSequence.m_x, secondSequence);
            mTrackClipList.add(secondClip.getIndexInTrack(), secondClip);
            /*更改新建后片段后边的索引
            * Change the index after the new clip
            * */

            int next = secondClip.getIndexInTrack();

            Map<ThumbnailId, Thumbnail> tempMap = new TreeMap<>();
            Set<Map.Entry<ThumbnailId, Thumbnail>> entries = m_thumbnailMap.entrySet();
            for (Map.Entry<ThumbnailId, Thumbnail> entry : entries) {
                ThumbnailId key = entry.getKey();
                Thumbnail value = entry.getValue();
                if (key.m_seqIndex >= next - 1) {
                    tempMap.put(key, value);
                }
            }
            Set<Map.Entry<ThumbnailId, Thumbnail>> tempEntries = tempMap.entrySet();
            if (!tempEntries.isEmpty()) {
                for (Map.Entry<ThumbnailId, Thumbnail> tempEntry : tempEntries) {
                    ThumbnailId key = tempEntry.getKey();
                    ThumbnailId tid = new ThumbnailId(key.m_seqIndex + 1, key.m_timestamp);
                    m_thumbnailMap.remove(key);
                    m_thumbnailMap.put(tid, tempEntry.getValue());
                }
            }
            for (int i = next; i < mTrackClipList.size(); i++) {
                mTrackClipList.get(i).setIndexInTrack(i);
                m_thumbnailSequenceArray.get(i).m_index = i;
            }
            /*处理片尾view*/
            int lastPosition = splitThumbnail.m_index;
            if (lastPosition + 1 < m_thumbnailSequenceArray.size()) {
                /*片尾视图跟随分割片段的后段
                * Trailer view follows the back segment of the split clip
                * */
                ThumbnailSequence nextThumbnail = m_thumbnailSequenceArray.get(lastPosition + 1);
                View child = mLlTailViewContainer.getChildAt(lastPosition);
                /*取出上一次片段的片尾view，更改其左间隔。
                * Take out the trailer view of the last clip and change its left interval.
                * */
                if (child != null) {
                    LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) child.getLayoutParams();
                    /*这里sequence已经增加过了，所以使用nextThumbnail
                    * The sequence has been added here, so nextThumbnail is used
                    * */
                    layoutParams.leftMargin = getTailViewLeft(nextThumbnail);
                    child.setLayoutParams(layoutParams);
                }
            }
            //LogUtils.d("tailInfo=" + ((ThumbnailClip) splitClip).getTailInfo().getCoverPath());
            /*给分割片段的前段添加新的片尾view
            * Add a new trailer view to the front segment of the split segment
            * */
            addTailView(lastPosition, getTailViewLeft(splitThumbnail), splitThumbnailClip.getTailInfo());

          /*  keyFrameInfo = secondClip.getKeyFrameInfo();
            keyFrameMap = keyFrameInfo.getKeyFrameMap();
            KeyFrameInfo nextKeyFrame = new KeyFrameInfo();
            *//*因为是clone的，所以atTime需要这样算
            Because it is clone, atTime needs to be calculated as follows*//*
            long atTime = splitThumbnailClip.getOutPoint() - splitThumbnailClip.getInPoint();
            *//*删除前边的关键帧
            Delete the keys of the front edge*//*
            Iterator<Map.Entry<Long, KeyFrameInfo.Info>> it = keyFrameMap.entrySet().iterator();
            while (it.hasNext()) {
                Map.Entry<Long, KeyFrameInfo.Info> entry = it.next();
                if (entry.getValue().getAtTime() <= atTime) {
                    it.remove();
                } else {
                    nextKeyFrame.addKeyFrame(entry.getValue().getAtTime() - atTime);
                }
            }
            *//*在两个关键帧之间分割，后一片段的头部需要添加关键帧
            Split between two keyframes, and add keyframes to the head of the next clip*//*
            if (needAddExtraKeyFrame && keyFrameMap.size() > 0) {
                nextKeyFrame.addKeyFrame(0);
            }
            secondClip.setKeyFrameInfo(nextKeyFrame);
            secondSequence.keyFrameInfo = nextKeyFrame;*/

            updateThumbnailCover(splitThumbnail, false);
            for (int i = splitThumbnail.m_index + 1; i < m_thumbnailSequenceArray.size(); i++) {
                updateThumbnailCover(m_thumbnailSequenceArray.get(i), true);
            }
            return secondClip;
        }
        return null;
    }

    /**
     * 添加缩略图片段
     * Add thumbnail clip list
     *
     * @param index         int the add index 添加到的位置索引
     * @param trackClipList List<ITrackClip> the add track clip list 要添加的片段集合
     */
    public void addThumbnail(int index, List<ITrackClip> trackClipList) {
        if (trackClipList != null && trackClipList.size() > 0) {
            if (index >= 0) {
                if (index < mTrackClipList.size()) {
                    mTrackClipList.addAll(index, trackClipList);
                } else if (mTrackClipList.size() == index) {
                    if (mTrackClipList.size() >= 2) {
                        ITrackClip lastTrackClip = mTrackClipList.get(mTrackClipList.size() - 1);
                        if (BaseTrackClip.CLIP_HOLDER.equals(lastTrackClip.getType())) {
                            //补黑永远在最后。
                            // Black patch is always at the end
                            mTrackClipList.addAll(mTrackClipList.size() - 2, trackClipList);
                        } else {
                            mTrackClipList.addAll(trackClipList);
                        }
                    } else {
                        mTrackClipList.addAll(trackClipList);
                    }
                } else {
                    Log.e(TAG, "addThumbnail,error,check it!!!");
                    return;
                }
                m_thumbnailSequenceArray.clear();
                m_placeholderBitmap = null;
                long lastOutPoint = 0;
                for (int i = 0; i < mTrackClipList.size(); i++) {
                    ITrackClip uiClip = mTrackClipList.get(i);
                    //重新调整索引位置
                    // Reposition index
                    uiClip.setIndexInTrack(i);
                    if (i > index) {
                        //调整后边片段的出入点
                        // Adjust the entry and exit points of the rear clip
                        long duration = uiClip.getOutPoint() - uiClip.getInPoint();
                        uiClip.setInPoint(lastOutPoint);
                        uiClip.setOutPoint(lastOutPoint + duration);
                    }
                    if (!(uiClip instanceof ThumbnailClip) || TextUtils.isEmpty(uiClip.getAssetPath()) ||
                            uiClip.getInPoint() < lastOutPoint || uiClip.getOutPoint() <= uiClip.getInPoint() ||
                            uiClip.getTrimIn() < 0 || uiClip.getTrimOut() <= uiClip.getTrimIn()) {
                        Log.e(TAG, "Invalid ThumbnailClip!!! " + uiClip);
                        mTrackClipList.remove(i);
                        --i;
                        continue;
                    }
                    m_thumbnailSequenceArray.add(getThumbnailSequence((ThumbnailClip) uiClip));
                    lastOutPoint = uiClip.getOutPoint();
                }
                //Log.d(TAG, "mTrackClipList.size=" + mTrackClipList.size());
                updateThumbnailSequenceGeometry();
            }
        }

    }

    /**
     * 添加缩略图片段
     * Add thumbnail clip
     *
     * @param index  int the add index ,添加到的位置索引
     * @param uiClip ITrackClip the add track clip,要添加的片段
     */
    public void addThumbnail(int index, ITrackClip uiClip) {
        addThumbnail(index, uiClip, false);
    }

    /**
     * 添加缩略图片段
     * Add thumbnail clip
     *
     * @param index  int the add index ,添加到的位置索引
     * @param uiClip ITrackClip the add track clip, 要添加的片段
     * @param update boolean true true update now ,false not.立即更新相应的缩略图，false不更新。
     */
    public void addThumbnail(int index, ITrackClip uiClip, boolean update) {
        if (uiClip instanceof ThumbnailClip) {
            ThumbnailClip thumbnailClip = (ThumbnailClip) uiClip;
            if (mTrackClipList == null) {
                mTrackClipList = new ArrayList<>();
            }
            ThumbnailSequence sequence;
            if (index >= 0) {
                if (index < mTrackClipList.size()) {
                    mTrackClipList.add(index, thumbnailClip);
                } else if (mTrackClipList.size() == index) {
                    ITrackClip lastTrackClip = mTrackClipList.get(mTrackClipList.size() - 1);
                    if (BaseTrackClip.CLIP_HOLDER.equals(lastTrackClip.getType())) {
                        if (BaseTrackClip.CLIP_HOLDER.equals(uiClip.getType())) {
                            LogUtils.e("error,holder clip are unique!!!");
                            return;
                        }
                        //补黑永远在最后。
                        // Black patch is always at the end.
                        mTrackClipList.add(mTrackClipList.size() - 2, thumbnailClip);
                    } else {
                        mTrackClipList.add(thumbnailClip);
                    }
                } else {
                    Log.e(TAG, "addThumbnail,error,check it!!!");
                    return;
                }
                /*
                 * 纠正一下索引
                 * Correct the index
                 */
                thumbnailClip.setIndexInTrack(index);
                sequence = getThumbnailSequence(thumbnailClip);
                sequence.m_index = index;
                sequence.m_flags &= ~THUMBNAIL_SEQUENCE_FLAGS_CACHED_KEYFRAME_ONLY_VALID;
                sequence.m_x = calculateThumbnailX(sequence.m_inPoint);
                sequence.m_width = calculateThumbnailX(sequence.m_outPoint) - sequence.m_x;
                final float thumbnailAspectRatio = sequence.m_thumbnailAspectRatio > 0 ?
                        sequence.m_thumbnailAspectRatio : m_thumbnailAspectRatio;
                sequence.m_thumbnailWidth = (int) Math.floor(getHeight() * thumbnailAspectRatio + 0.5);
                sequence.m_thumbnailWidth = Math.max(sequence.m_thumbnailWidth, 1);
                long offsetDuration = thumbnailClip.getOutPoint() - thumbnailClip.getInPoint();
                m_thumbnailSequenceArray.add(index, sequence);
                m_thumbnailSequenceMap.put(sequence.m_x, sequence);
                for (int i = index + 1; i < mTrackClipList.size(); i++) {
                    ITrackClip trackClip = mTrackClipList.get(i);
                    trackClip.setIndexInTrack(i);
                    trackClip.setInPoint(trackClip.getInPoint() + offsetDuration);
                    trackClip.setOutPoint(trackClip.getOutPoint() + offsetDuration);
                    sequence = m_thumbnailSequenceArray.get(i);
                    sequence.m_index = i;
                    sequence.m_inPoint = trackClip.getInPoint();
                    sequence.m_outPoint = trackClip.getOutPoint();
                    sequence.m_x = calculateThumbnailX(sequence.m_inPoint);
                    m_thumbnailSequenceMap.put(sequence.m_x, sequence);
                }
            }
            // LogUtils.d("trackClip=" + thumbnailClip + ",trackSize=" + mTrackClipList.size() + ",sequenceSize=" + m_thumbnailSequenceArray.size());
            needUpdate = update;
            if (update) {
                updateThumbnailSequenceGeometry();
            }
        }
    }

    /**
     * 更新缩略图某一片段的出入点和裁入裁出点
     * Update the in and out ,trim in and trim out point of the track clip
     *
     * @param uiClip     ITrackClip the update clip 要调整的片段
     * @param updateLeft boolean true update left ,false right是更改片段的左部分，false是更改片段的右部分
     * @param stamp      long the timestamp need update 要调整的时间戳
     */
    public void updateThumbnail(ITrackClip uiClip, boolean updateLeft, long stamp) {
        if (mTrackClipList != null && uiClip != null && stamp != 0) {
            double speed = uiClip.getSpeedInfo().getSpeed();

            if (m_thumbnailSequenceArray == null || uiClip.getIndexInTrack() < 0 || uiClip.getIndexInTrack() >= m_thumbnailSequenceArray.size()) {
                Log.e(TAG, "m_thumbnailSequenceArray IndexOutOfBoundsException");
                return;
            }
            ThumbnailSequence item = m_thumbnailSequenceArray.get(uiClip.getIndexInTrack());
            //boolean isPicture = BaseTrackClip.CLIP_IMAGE.equals(uiClip.getType());
            boolean isPicture = false;
            if (updateLeft && !isPicture) {
                //调整trimIn Adjust trimIn
                long trimIn = (long) (uiClip.getTrimIn() + stamp * speed);
                long speedTrimOut = (long) (uiClip.getTrimOut() / speed);
                if (trimIn < 0) {
                    /*trim in左边界处理
                    * Trim in left boundary processing
                    * */
                    stamp = -Math.abs(speedTrimOut + uiClip.getInPoint() - uiClip.getOutPoint());
                    trimIn = 0;
                    //LogUtils.d("trimIn, left max " + uiClip);
                } else if (trimIn > uiClip.getTrimOut()) {
                    Log.e(TAG, "clip trimIn > trimOut ,check it!!!");
                    return;
                } else if (uiClip.getTrimOut() - trimIn <= MIN_DURATION * speed) {
                    /*trim in右边界处理'
                    * Trim in right boundary processing
                    * */
                    trimIn = (long) (uiClip.getTrimOut() - MIN_DURATION * speed);
                    stamp = (long) (Math.abs(uiClip.getTrimIn() - trimIn) / speed);
                    //LogUtils.d("trimIn,right max " + uiClip);
                }
                //左向右调整，stamp是正值，但是需要减少outPoint，后边的clip的int\out都要减少，因为下边用的都是+，所以这里变为负值
                //左向左调整 ，则相反
                // Adjust from left to right. Stamp is a positive value, but outPoint needs
                // to be reduced. The int  out of the following clip should be reduced.
                // Because the following clip uses+, it becomes a negative value.
                // Adjust left to left, otherwise
                stamp = -stamp;
                long duration = uiClip.getOutPoint() + stamp - uiClip.getInPoint();
                //LogUtils.d("duration=" + duration + ",speedTrimOut=" + speedTrimOut + ",trimIn=" + trimIn + ",uiClip=" + uiClip);
                if (duration <= MIN_DURATION) {
                    /*out point 最小边界处理
                    * Out point minimum boundary processing
                    * */
                    stamp = MIN_DURATION + uiClip.getInPoint() - uiClip.getOutPoint();
                } else if (duration > speedTrimOut) {
                    /*out point 最大边界处理
                    * Out point maximum boundary processing
                    * */
                    stamp = speedTrimOut - uiClip.getOutPoint() + uiClip.getInPoint();
                }
                uiClip.setOutPoint(uiClip.getOutPoint() + stamp);
                uiClip.setTrimIn(trimIn);
                item.m_trimIn = uiClip.getTrimIn();
            } else {
                if (isPicture && updateLeft) {
                    /*图片只更新trim out
                    * The picture only updates trim out
                    * */
                    if (stamp < 0) {
                        /*剪裁图片的左边，只允许往右(减小)，不允许往左(增大)
                        * Cut the left side of the picture. Only right (decrease) is allowed, not left (increase)
                        * */
                        return;
                    }
                    stamp = -stamp;
                }
                //调整trimOut
                // Adjust trimOut
                long trimOut = (long) (uiClip.getTrimOut() + stamp * speed);
                long speedOriginalDuration = (long) (uiClip.getOriginalDuration() / speed);
                if (trimOut < uiClip.getTrimIn()) {
                    Log.e(TAG, "clip trimIn > trimOut ,check it!!!");
                    return;
                } else if (trimOut - uiClip.getTrimIn() <= MIN_DURATION * speed) {
                    /*trim out左边界处理
                    * Trim out left boundary processing
                    * */
                    trimOut = (long) (uiClip.getTrimIn() + MIN_DURATION * speed);
                    stamp = (long) ((trimOut - uiClip.getTrimOut()) / speed);
                    //LogUtils.d("trimOut, left max " + uiClip);
                } else if (trimOut > uiClip.getOriginalDuration()) {
                    /*trim out右边界处理
                    * Trim out right boundary processing
                    * */
                    trimOut = uiClip.getOriginalDuration();
                    stamp = (long) Math.abs((trimOut - uiClip.getTrimIn()) / speed + uiClip.getInPoint() - uiClip.getOutPoint());
                    //LogUtils.d("trimOut, right max " + uiClip);
                }
                long duration = uiClip.getOutPoint() + stamp - uiClip.getInPoint();
                //LogUtils.d("speedOriginalDuration=" + speedOriginalDuration + ",duration=" + duration + "trimOut=" + trimOut + ",stamp=" + stamp + ",uiClip=" + uiClip);
                //右向左或者右向左，时间戳和out成正比所以不需要更改正负
                // Right to left or right to left, the timestamp is proportional to out,
                // so it is not necessary to change the positive and negative
                if (duration < MIN_DURATION) {
                    /*out point 最小边界处理
                    * Out point minimum boundary processing.
                    * */
                    stamp = MIN_DURATION + uiClip.getInPoint() - uiClip.getOutPoint();
                } else if (duration > speedOriginalDuration) {
                    /*out point 最大边界处理
                    * Out point maximum boundary processing.
                    * */
                    stamp = speedOriginalDuration - uiClip.getOutPoint() + uiClip.getInPoint();
                }
                uiClip.setOutPoint(uiClip.getOutPoint() + stamp);
                uiClip.setTrimOut(trimOut);
            }
            KeyFrameInfo keyFrameInfo = uiClip.getKeyFrameInfo();
            if (keyFrameInfo != null && updateLeft) {
                /*更改关键帧的入点,注意如果更新右边就不再更新关键帧的位置点了
                * Change the entry point of the key frame. Note that if you update the
                * right side, the position point of the key frame will not be updated
                * */
                keyFrameInfo.moveKeyFrame(stamp, true);
                item.keyFrameInfo = keyFrameInfo;
            }
            //调整outPoint
            // Adjust outPoint
            item.m_outPoint = uiClip.getOutPoint();
            item.m_trimDuration = Math.max(uiClip.getTrimOut() - uiClip.getTrimIn(), 1);
            //LogUtils.d("stamp=" + stamp + ",uiClip=" + uiClip + ",trimDuration=" + item.m_trimDuration);
            //调整该片段后边的各个片段的入出点。
            // Adjust the entry and exit points of each clip behind the clip.
            if (uiClip.getIndexInTrack() >= 0 && uiClip.getIndexInTrack() < mTrackClipList.size()) {
                ThumbnailSequence thumbnailSequence;
                for (int i = uiClip.getIndexInTrack() + 1; i < mTrackClipList.size(); i++) {
                    uiClip = mTrackClipList.get(i);
                    uiClip.setInPoint(uiClip.getInPoint() + stamp);
                    uiClip.setOutPoint(uiClip.getOutPoint() + stamp);
                    thumbnailSequence = m_thumbnailSequenceArray.get(i);
                    thumbnailSequence.m_inPoint = uiClip.getInPoint();
                    thumbnailSequence.m_outPoint = uiClip.getOutPoint();
                }
            }
            updateThumbnailSequenceGeometry();
        }
    }

    /**
     * 刷新某一个片段
     * Update the thumbnail clip
     *
     * @param trackClip           the track clip 片段
     * @param updateInAndOutPoint true update in and out point 更新出入点，false not 不更新.
     **/
    public void updateThumbnail(ITrackClip trackClip, boolean updateInAndOutPoint) {
        if (trackClip != null && trackClip.getIndexInTrack() >= 0 && trackClip.getIndexInTrack() < m_thumbnailSequenceArray.size()) {
            ThumbnailSequence thumbnailSequence = m_thumbnailSequenceArray.get(trackClip.getIndexInTrack());
            long offsetDuration = (thumbnailSequence.m_outPoint - thumbnailSequence.m_inPoint) - (trackClip.getOutPoint() - trackClip.getInPoint());
            setThumbnailSequence((ThumbnailClip) trackClip, thumbnailSequence);
            if (updateInAndOutPoint) {
                for (int i = trackClip.getIndexInTrack() + 1; i < mTrackClipList.size(); i++) {
                    trackClip = mTrackClipList.get(i);
                    trackClip.setInPoint(trackClip.getInPoint() - offsetDuration);
                    trackClip.setOutPoint(trackClip.getOutPoint() - offsetDuration);
                    thumbnailSequence = m_thumbnailSequenceArray.get(i);
                    thumbnailSequence.m_inPoint = trackClip.getInPoint();
                    thumbnailSequence.m_outPoint = trackClip.getOutPoint();
                }
            }
            updateThumbnailSequenceGeometry();
        }
    }

    /**
     * 删除所给的缩略图片段
     * Delete the thumbnail clip
     *
     * @param trackClip the trackClip need delete 要删除的片段
     */
    public void deleteThumbnail(ITrackClip trackClip) {
        if (trackClip != null) {
            deleteThumbnail(trackClip.getIndexInTrack());
        }
    }

    /**
     * 删除所给索引对应的缩略图片段
     * Delete the thumbnail by the index
     *
     * @param index the index need delete 需要删除的片段索引
     */
    public void deleteThumbnail(int index) {
        if (index >= 0 && index < mTrackClipList.size()) {
            ITrackClip deletedClip = mTrackClipList.remove(index);
            m_thumbnailSequenceArray.remove(index);
            long deleteDuration = deletedClip.getOutPoint() - deletedClip.getInPoint();
            //调整该片段后边的各个片段的入出点。
            // Adjust the entry and exit points of each clip behind the clip.
            if (index < mTrackClipList.size() && deleteDuration > 0) {
                // LogUtils.d("index=" + index + ",deletedClip" + deletedClip + ",trackSize=" + mTrackClipList.size() + ",sequenceSize=" + m_thumbnailSequenceArray.size());
                ThumbnailSequence thumbnailSequence;
                for (int i = index; i < mTrackClipList.size(); i++) {
                    ITrackClip nextClip = mTrackClipList.get(i);
                    nextClip.setInPoint(nextClip.getInPoint() - deleteDuration);
                    nextClip.setOutPoint(nextClip.getOutPoint() - deleteDuration);
                    nextClip.setIndexInTrack(i);
                    thumbnailSequence = m_thumbnailSequenceArray.get(i);
                    thumbnailSequence.m_inPoint = nextClip.getInPoint();
                    thumbnailSequence.m_outPoint = nextClip.getOutPoint();
                    thumbnailSequence.m_index = i;
                }
            }
           /* for (int i = 0; i < mTrackClipList.size(); i++) {
                LogUtils.d("trackClip="+mTrackClipList.get(i));
            }*/
            updateThumbnailSequenceGeometry();
        }

    }

    /**
     * 移动缩略图片段
     * Move thumbnail clip
     *
     * @param from from position 移动开始的位置
     * @param to   to position 移动结束的位置
     */
    public void moveThumbnail(int from, int to) {
        if (from == to) {
            return;
        }
        if (from >= 0 && from < mTrackClipList.size() && to >= 0 && to < mTrackClipList.size()) {
            ThumbnailSequence sequence;
            ITrackClip toClip = mTrackClipList.get(to);
            if (BaseTrackClip.CLIP_HOLDER.equals(toClip.getType())) {
                LogUtils.e("error, CLIP_HOLDER type can not move,check it!!!");
                return;
            }
            ITrackClip fromClip = mTrackClipList.remove(from);
            mTrackClipList.add(to, fromClip);
            sequence = m_thumbnailSequenceArray.remove(from);
          /*  if (to == mTrackClipList.size() - 1 || BaseTrackClip.CLIP_HOLDER.equals(mTrackClipList.get(to + 1).getType())) {
                //移动到最后一个非补黑的片段清除尾部信息
                //Move to the last non-black segment to clear the tail information
                sequence.tailInfo.clear();
                ((ThumbnailClip) fromClip).getTailInfo().clear();
            }*/
            m_thumbnailSequenceArray.add(to, sequence);

            int start;
            int end;
            long startPoint;
            long duration;
            ITrackClip lastClip = null;
            if (from < to) {
                start = from;
                end = to;
                startPoint = fromClip.getInPoint();
            } else {
                start = to;
                end = from;
                startPoint = toClip.getInPoint();
            }
            for (int i = start; i <= end; i++) {
                ITrackClip trackClip = mTrackClipList.get(i);
                sequence = m_thumbnailSequenceArray.get(i);
                duration = trackClip.getOutPoint() - trackClip.getInPoint();
                if (lastClip == null) {
                    trackClip.setInPoint(startPoint);
                } else {
                    trackClip.setInPoint(lastClip.getOutPoint());
                }
                trackClip.setOutPoint(trackClip.getInPoint() + duration);
                trackClip.setIndexInTrack(i);
                sequence.m_index = i;
                sequence.m_inPoint = trackClip.getInPoint();
                sequence.m_outPoint = trackClip.getOutPoint();
                lastClip = trackClip;
            }

            ITrackClip lastNotHolderClip = mTrackClipList.get(mTrackClipList.size() - 1);
            if (BaseTrackClip.CLIP_HOLDER.equals(lastNotHolderClip.getType())) {
                lastNotHolderClip = mTrackClipList.get(mTrackClipList.size() - 2);
            }
            //清除最后一个非补黑的片段清除尾部信息
            // Clear the last non-black segment Clear the tail information
            sequence = m_thumbnailSequenceArray.get(lastNotHolderClip.getIndexInTrack());
            sequence.tailInfo.clear();
            ((ThumbnailClip) lastNotHolderClip).getTailInfo().clear();

            updateThumbnailSequenceGeometry();
        }
    }

    /**
     * 根据时间戳找到目标ITrackClip
     * Find the target ITrackClip according to the timestamp
     *
     * @param timestamp timestamp 时间戳
     */
    public ITrackClip findTargetClip(long timestamp) {
        if (mTrackClipList != null) {
            for (ITrackClip uiClip : mTrackClipList) {
                if (timestamp >= uiClip.getInPoint() && timestamp < uiClip.getOutPoint()) {
                    return uiClip;
                }
            }
        }
        return null;
    }

    /**
     * 根据索引找到目标ITrackClip
     * Find the target ITrackClip according to the index
     *
     * @param index the index 索引
     */
    public ITrackClip findTargetClip(int index) {
        if (mTrackClipList != null && index >= 0 && index < mTrackClipList.size()) {
            return mTrackClipList.get(index);
        }
        return null;
    }

    /**
     * 所给片段是否是最后边可用的片段
     * Whether the given clip is the clip available for the last edge
     *
     * @param trackClip the given clip 所给片段
     */
    public boolean isLastAvailableTrackClip(ITrackClip trackClip) {
        if (trackClip != null) {
            if (trackClip.getIndexInTrack() == mTrackClipList.size() - 1 && !BaseTrackClip.CLIP_HOLDER.equals(trackClip.getType())) {
                return true;
            }
            if (trackClip.getIndexInTrack() == mTrackClipList.size() - 2) {
                return !BaseTrackClip.CLIP_HOLDER.equals(mTrackClipList.get(mTrackClipList.size() - 2).getType());
            }
        }
        return false;
    }

    /**
     * 寻找最后边可用的轨道片段
     * Find the last available track clip
     *
     * @return the last available track clip
     */
    public ITrackClip findLastAvailableTrackClip() {
        if (mTrackClipList != null && mTrackClipList.size() > 0) {
            ITrackClip lastTrackClip = mTrackClipList.get(mTrackClipList.size() - 1);
            if (BaseTrackClip.CLIP_HOLDER.equals(lastTrackClip.getType())) {
                lastTrackClip = mTrackClipList.get(mTrackClipList.size() - 2);
            }
            return lastTrackClip;
        }
        return null;
    }

    /**
     * 更新缩略图尾部信息
     * Update thumbnail tail info
     *
     * @param index the tail index 索引
     */
    public void updateThumbnailTailInfo(int index) {
        if (index >= 0 && index < m_thumbnailSequenceArray.size()) {
            updateTailVieContent(index, m_thumbnailSequenceArray.get(index).tailInfo);
        }
    }

    /**
     * 设置所有缩略图尾部信息
     * Set all thumbnail tail info
     *
     * @param tailInfo the tail info 尾部信息
     */
    public void setAllThumbnailTailInfo(ThumbnailClip.TailInfo tailInfo) {
        if (tailInfo == null) {
            return;
        }
        for (int i = 0; i < mTrackClipList.size(); i++) {
            ThumbnailClip thumbnailClip = (ThumbnailClip) mTrackClipList.get(i);
            if (!BaseTrackClip.CLIP_HOLDER.equals(thumbnailClip.getType())) {
                thumbnailClip.getTailInfo().update(tailInfo);
                m_thumbnailSequenceArray.get(i).tailInfo.update(tailInfo);
                updateTailVieContent(i, tailInfo);
            }
        }
    }

    /**
     * 设置缩略图尾部信息
     * Set thumbnail tail info
     *
     * @param index    the tail index 索引
     * @param tailInfo the tail info 尾部信息
     */
    public void setThumbnailTailInfo(int index, ThumbnailClip.TailInfo tailInfo) {
        if (index >= 0 && index < mTrackClipList.size()) {
            ThumbnailClip thumbnailClip = (ThumbnailClip) mTrackClipList.get(index);
            thumbnailClip.setTailInfo(tailInfo);
            m_thumbnailSequenceArray.get(index).tailInfo = tailInfo;
            updateTailVieContent(index, tailInfo);
        }
    }

    /**
     * 根据索引找到目标tail info
     * Find the target TailInfo according to the index
     *
     * @param index the index 索引
     */
    public ThumbnailClip.TailInfo findThumbnailTailInfo(int index) {
        ITrackClip targetClip = findTargetClip(index);
        if (targetClip != null) {
            return ((ThumbnailClip) targetClip).getTailInfo();
        }
        return new ThumbnailClip.TailInfo();
    }

    /**
     * 设置缩略图片尾视图的可见性
     * Set tail view visibility
     *
     * @param visibility the visibility
     */
    public void setTailViewVisibility(int visibility) {
        mLlTailViewContainer.setVisibility(visibility);
    }

    /**
     * 选中所给缩略图序列
     * Select the thumbnail sequence given
     *
     * @param trackClip the thumbnail clip 缩略图片段
     */
    public void selectedThumbnail(ITrackClip trackClip) {
        selectedThumbnailCover(trackClip == null ? -1 : trackClip.getIndexInTrack());
    }

    /**
     * 获取选中的缩略图序列片段
     * Gets the selected thumbnail sequence clip
     *
     * @return the thumbnail clip
     */
    public ITrackClip getSelectedThumbnailClip() {
        if (mSelectedCoverPosition >= 0 && mSelectedCoverPosition < mTrackClipList.size()) {
            return mTrackClipList.get(mSelectedCoverPosition);
        }
        return null;
    }

    /**
     * 设置缩略图序列的封面动画信息是否可用,可用会显示，不可用不会显示
     * Sets whether the cover animation information for the thumbnail sequence is available
     *
     * @param enable true will show it,false not.真会显示，假则不显示
     */
    public void enableThumbnailAnimation(boolean enable) {
        int childCount = mLlThumbnailCoverContainer.getChildCount();
        for (int i = 0; i < childCount; i++) {
            View child = mLlThumbnailCoverContainer.getChildAt(i);
            if (child != null) {
                ThumbnailCoverView coverView = (ThumbnailCoverView) child;
                coverView.enableAnimation(enable);
            }
        }
    }

    /**
     * 设置所给缩略图序列的封面动画信息是否可用,可用会显示，不可用不会显示
     * Sets whether the cover animation information for the given thumbnail sequence is available
     *
     * @param trackClip the given thumbnail clip.所给的缩略图片段
     * @param enable    true will show it,false not.真会显示，假则不显示
     */
    public void enableThumbnailAnimation(ITrackClip trackClip, boolean enable) {
        if (trackClip != null) {
            View child = mLlThumbnailCoverContainer.getChildAt(trackClip.getIndexInTrack());
            if (child != null) {
                ThumbnailCoverView coverView = (ThumbnailCoverView) child;
                coverView.enableAnimation(enable);
            }
        }
    }

    /**
     * 更新缩略图序列的封面动画信息
     * Update the cover animation information for the thumbnail sequence
     *
     * @param trackClip the track clip 轨道缩略图片段
     */
    public void updateThumbnailAnimation(ITrackClip trackClip) {
        if (trackClip != null) {
            View child = mLlThumbnailCoverContainer.getChildAt(trackClip.getIndexInTrack());
            if (child != null) {
                ThumbnailCoverView coverView = (ThumbnailCoverView) child;
                coverView.setAnimationInfo(((ThumbnailClip) trackClip).getAnimationInfo());
            }
        }
    }

    /**
     * 设置缩略图序列的封面变速信息是否可用,可用会显示，不可用不会显示
     * Sets whether the cover speed information for the thumbnail sequence is available
     *
     * @param enable true will show it,false not.真会显示，假则不显示
     */
    public void enableThumbnailSpeed(boolean enable) {
        int childCount = mLlThumbnailCoverContainer.getChildCount();
        for (int i = 0; i < childCount; i++) {
            View child = mLlThumbnailCoverContainer.getChildAt(i);
            if (child != null) {
                ThumbnailCoverView coverView = (ThumbnailCoverView) child;
                coverView.enableSpeed(enable);
            }
        }
    }

    /**
     * 设置所给缩略图序列的封面变速信息是否可用,可用会显示，不可用不会显示
     * Sets whether the cover speed information for the given thumbnail sequence is available
     *
     * @param trackClip the given thumbnail clip.所给的轨道缩略图片段
     * @param enable    true will show it,false not.真会显示，假则不显示
     */
    public void enableThumbnailSpeed(ITrackClip trackClip, boolean enable) {
        if (trackClip != null) {
            View child = mLlThumbnailCoverContainer.getChildAt(trackClip.getIndexInTrack());
            if (child != null) {
                ThumbnailCoverView coverView = (ThumbnailCoverView) child;
                coverView.enableSpeed(enable);
                coverView.enableFaceProp(enable && trackClip.hasProp());
                coverView.enableVolume(enable && trackClip.getVolume() == 0);
            }
        }
    }

    /**
     * 更新缩略图序列的封面变速信息
     * Update the cover animation information for the thumbnail sequence
     *
     * @param trackClip the given thumbnail clip.所给的轨道缩略图片段
     */
    public void updateThumbnailSpeed(ITrackClip trackClip) {
        if (trackClip != null) {
            View child = mLlThumbnailCoverContainer.getChildAt(trackClip.getIndexInTrack());
            if (child != null) {
                ThumbnailCoverView coverView = (ThumbnailCoverView) child;
                coverView.setSpeedInfo(trackClip.getSpeedInfo());
            }
        }
    }

    /**
     * 设置所给缩略图序列的封面关键帧标记是否可用,可用会显示，不可用不会显示
     * Sets whether the cover key frame information for the given thumbnail sequence is available
     *
     * @param trackClip the given thumbnail clip.所给的轨道缩略图片段
     * @param enable    true will show it,false not.真会显示，假则不显示
     */
    public void enableThumbnailKeyFrameTag(ITrackClip trackClip, boolean enable) {
        if (trackClip != null) {
            View child = mLlThumbnailCoverContainer.getChildAt(trackClip.getIndexInTrack());
            if (child != null) {
                ThumbnailCoverView coverView = (ThumbnailCoverView) child;
                coverView.enableKeyFrame(enable);
            }
        }
    }

    /**
     * 给缩略图序列的封面添加关键帧标记
     * add key frame information for the given thumbnail sequence
     *
     * @param trackClip the given thumbnail clip.所给的轨道缩略图片段
     * @param inPoint   the key frame in point
     */
    public void addThumbnailKeyFrame(ITrackClip trackClip, long inPoint) {
        if (trackClip != null) {
            View child = mLlThumbnailCoverContainer.getChildAt(trackClip.getIndexInTrack());
            if (child != null) {
                ThumbnailCoverView coverView = (ThumbnailCoverView) child;
                coverView.addKeyFrame(inPoint, true);
            }
        }
    }

    /**
     * 删除缩略图序列的封面的关键帧标记
     * delete key frame information for the given thumbnail sequence
     *
     * @param trackClip the given thumbnail clip.所给的轨道缩略图片段
     * @param inPoint   the key frame in point
     */
    public void deleteThumbnailKeyFrame(ITrackClip trackClip, long inPoint) {
        if (trackClip != null) {
            View child = mLlThumbnailCoverContainer.getChildAt(trackClip.getIndexInTrack());
            if (child != null) {
                ThumbnailCoverView coverView = (ThumbnailCoverView) child;
                coverView.deleteKeyFrame(trackClip.getInPoint(), inPoint);
            }
        }
    }

    /**
     * 检查是否选中缩略图序列的封面的关键帧标记
     * Selected key frame information for the given thumbnail sequence
     *
     * @param trackClip the given thumbnail clip.所给的轨道缩略图片段
     * @param inPoint   the key frame in point
     */
    public void checkThumbnailKeyFrame(ITrackClip trackClip, long inPoint) {
        if (trackClip != null) {
            View child = mLlThumbnailCoverContainer.getChildAt(trackClip.getIndexInTrack());
            if (child != null) {
                ThumbnailCoverView coverView = (ThumbnailCoverView) child;
                // coverView.selectedKeyFrame(inPoint);
                coverView.checkSelectedKeyFrame(trackClip.getInPoint(), inPoint);
            }
        }
    }

    /*! \if ENGLISH
     *   \brief Sets the image fill mode of the thumbnail
     *   \param fillMode [image fill mode] (@ref THUMBNAIL_IMAGE_FILLMODE)
     *   \else
     *   \brief 设置缩略图的图片填充模式
     *   \param fillMode [图片填充模式] (@ref THUMBNAIL_IMAGE_FILLMODE)
     *   \endif
     *   \sa getThumbnailImageFillMode
     */
    public void setThumbnailImageFillMode(int fillMode) {
        NvsUtils.checkFunctionInMainThread();
        if (m_thumbnailImageFillMode != THUMBNAIL_IMAGE_FILLMODE_ASPECTCROP &&
                m_thumbnailImageFillMode != THUMBNAIL_IMAGE_FILLMODE_STRETCH) {
            m_thumbnailImageFillMode = THUMBNAIL_IMAGE_FILLMODE_STRETCH;
        }

        if (m_thumbnailImageFillMode == fillMode)
            return;

        m_thumbnailImageFillMode = fillMode;
        updateThumbnailSequenceGeometry();
    }

    /*! \if ENGLISH
     *   \brief Gets the image fill mode of the thumbnail
     *   \return Returns the obtained image fill mode of the thumbnail
     *   \else
     *   \brief 获取缩略图的图片填充模式
     *   \return 返回获取的缩略图的图片填充模式
     *   \endif
     *   \sa setThumbnailImageFillMode
     */
    public int getThumbnailImageFillMode() {
        return m_thumbnailImageFillMode;
    }

    /*! \if ENGLISH
     *   \brief Sets thumbnail aspect ratio.
     *   \param thumbnailAspectRatio aspect ratio
     *   \else
     *   \brief 设置缩略图横纵比
     *   \param thumbnailAspectRatio 横纵比
     *   \endif
     *   \sa getThumbnailAspectRatio
     */
    public void setThumbnailAspectRatio(float thumbnailAspectRatio) {
        NvsUtils.checkFunctionInMainThread();
        if (thumbnailAspectRatio < 0.1f)
            thumbnailAspectRatio = 0.1f;
        else if (thumbnailAspectRatio > 10)
            thumbnailAspectRatio = 10;

        if (Math.abs(m_thumbnailAspectRatio - thumbnailAspectRatio) < 0.001f)
            return;

        m_thumbnailAspectRatio = thumbnailAspectRatio;
        updateThumbnailSequenceGeometry();
    }

    /*! \if ENGLISH
     *   \brief Gets thumbnail aspect ratio.
     *   \return Returns the thumbnail aspect ratio.
     *   \else
     *   \brief 获取缩略图横纵比
     *   \return 返回缩略图横纵比值
     *   \endif
     *   \sa setThumbnailAspectRatio
     */
    public float getThumbnailAspectRatio() {
        return m_thumbnailAspectRatio;
    }

    /*! \if ENGLISH
     *   \brief Sets the scale.
     *   \param pixelPerMicrosecond The number of pixels per subtle
     *   \else
     *   \brief 设置比例尺
     *   \param pixelPerMicrosecond 每微妙所占用的像素数
     *   \endif
     *   \sa getPixelPerMicrosecond
     */
    public void setPixelPerMicrosecond(double pixelPerMicrosecond) {
        NvsUtils.checkFunctionInMainThread();
        if (pixelPerMicrosecond <= 0 || isEqual(pixelPerMicrosecond, m_pixelPerMicrosecond))
            return;

        m_pixelPerMicrosecond = pixelPerMicrosecond;
        updateThumbnailSequenceGeometry();
    }

    private boolean isEqual(double a, double b) {
        return Math.abs(a - b) <= 1e-6;
    }

    /*! \if ENGLISH
     *   \brief Gets the current scale.
     *   \return Returns the number of pixels per subtle.
     *   \else
     *   \brief 获取当前比例尺
     *   \return 返回每微妙所占用的像素数
     *   \endif
     *   \sa setPixelPerMicrosecond
     */
    public double getPixelPerMicrosecond() {
        return m_pixelPerMicrosecond;
    }

    /*! \if ENGLISH
     *   \brief Sets the starting padding.
     *   \param startPadding Starting padding(in pixels)
     *   \else
     *   \brief 设置起始边距
     *   \param startPadding 起始边距（单位是像素）
     *   \endif
     *   \sa getStartPadding
     */
    public void setStartPadding(int startPadding) {
        setStartPadding(startPadding, true);
    }

    public void setStartPadding(int startPadding, boolean update) {
        NvsUtils.checkFunctionInMainThread();
        if (startPadding < 0 || startPadding == m_startPadding)
            return;
        m_startPadding = startPadding;
        LayoutParams layoutParams = (LayoutParams) mLlThumbnailCoverContainer.getLayoutParams();
        layoutParams.leftMargin = m_startPadding;
        mLlThumbnailCoverContainer.setLayoutParams(layoutParams);
        if (update) {
            updateThumbnailSequenceGeometry();
        }
    }

    /*! \if ENGLISH
     *   \brief Gets the current starting padding.
     *   \return Returns the starting padding(in pixels).
     *   \else
     *   \brief 获取当前起始边距
     *   \return 返回起始边距（单位是像素）
     *   \endif
     *   \sa setStartPadding
     */
    public int getStartPadding() {
        return m_startPadding;
    }

    /*! \if ENGLISH
     *   \brief Sets end padding.
     *   \param endPadding Ends padding(in pixels)
     *   \else
     *   \brief 设置结束边距。
     *   \param endPadding 结束边距（单位为像素）
     *   \endif
     *   \sa getEndPadding
     */
    public void setEndPadding(int endPadding) {
        setEndPadding(endPadding, true);
    }

    /*! \if ENGLISH
     *   \brief Sets end padding.
     *   \param endPadding Ends padding(in pixels)
     *   \else
     *   \brief 设置结束边距。
     *   \param endPadding 结束边距（单位为像素）
     *   \endif
     *   \sa getEndPadding
     */
    public void setEndPadding(int endPadding, boolean updateNow) {
        NvsUtils.checkFunctionInMainThread();
        if (endPadding < 0 || endPadding == m_endPadding)
            return;

        m_endPadding = endPadding;
        if (updateNow) {
            updateThumbnailSequenceGeometry();
        }
    }

    /*! \if ENGLISH
     *   \brief Gets the current ending padding.
     *   \return Returns the ending padding(in pixels)
     *   \else
     *   \brief 获取当前结束边距。
     *   \return 返回结束边距，单位为像素
     *   \endif
     *   \sa setEndPadding
     */
    public int getEndPadding() {
        return m_endPadding;
    }

    /*! \if ENGLISH
     *   \brief Sets the maximum timeline position that allows scrolling.
     *   \param maxTimelinePosToScroll The maximum timeline position that is allowed to scroll(in microseconds).
     *   \else
     *   \brief 设置允许滚动的最大时间线位置
     *   \param maxTimelinePosToScroll 允许滚动的最大时间线位置，单位为微秒
     *   \endif
     *   \sa getMaxTimelinePosToScroll
     *   \since 1.17.0
     */
    public void setMaxTimelinePosToScroll(int maxTimelinePosToScroll) {
        NvsUtils.checkFunctionInMainThread();
        maxTimelinePosToScroll = Math.max(maxTimelinePosToScroll, 0);
        if (maxTimelinePosToScroll == m_maxTimelinePosToScroll)
            return;

        m_maxTimelinePosToScroll = maxTimelinePosToScroll;

        updateThumbnailSequenceGeometry();
    }

    /*! \if ENGLISH
     *   \brief Gets the maximum timeline position that allows scrolling.
     *   \return Returns the maximum timeline position that is allowed to scroll(in microseconds).
     *   \else
     *   \brief 获取允许滚动的最大时间线位置
     *   \return 返回允许滚动的最大时间线位置，单位为微秒
     *   \endif
     *   \sa setMaxTimelinePosToScroll
     *   \since 1.17.0
     */
    public long getMaxTimelinePosToScroll() {
        return m_maxTimelinePosToScroll;
    }

    /*! \if ENGLISH
     *   \brief Maps the X coordinate of the control to the timeline position.
     *   \param x The X coordinate of the control(in pixels)
     *   \return Returns the timeline position of the map(in microseconds).
     *   \else
     *   \brief 将控件的X坐标映射到时间线位置
     *   \param x 控件的X坐标（单位为像素）
     *   \return 返回映射的时间线位置（单位为微秒）
     *   \endif
     *   \sa mapXFromTimelinePos
     */
    public long mapTimelinePosFromX(int x) {
        NvsUtils.checkFunctionInMainThread();
        final int scrollX = getScrollX();
        x = x + scrollX - m_startPadding;
        return (long) Math.floor(x / m_pixelPerMicrosecond + 0.5);
    }

    /*! \if ENGLISH
     *   \brief Maps the timeline position to the X coordinate of the control.
     *   \param timelinePos Timeline position(in microseconds)
     *   \return Returns the X coordinate of the mapped control(in pixels).
     *   \else
     *   \brief 将时间线位置映射到控件的X坐标
     *   \param timelinePos 时间线位置（单位为微秒）
     *   \return 返回映射的控件的X坐标（单位为像素）
     *   \endif
     *   \sa mapTimelinePosFromX
     */
    public int mapXFromTimelinePos(long timelinePos) {
        NvsUtils.checkFunctionInMainThread();
        int x = (int) Math.floor(timelinePos * m_pixelPerMicrosecond + 0.5);
        final int scrollX = getScrollX();
        return x + m_startPadding - scrollX;
    }

    /*! \if ENGLISH
     *   \brief Zooms the current scale.
     *   \param scaleFactor Scale ratio
     *   \param anchorX Scaled anchor X coordinate(in pixels).
     *   \else
     *   \brief 缩放当前比例尺
     *   \param scaleFactor 缩放的比例
     *   \param anchorX 缩放的锚点X坐标（单位为像素）
     *   \endif
     */
    public void scaleWithAnchor(double scaleFactor, int anchorX) {
        NvsUtils.checkFunctionInMainThread();
        if (scaleFactor <= 0)
            return;

        final long anchorTimelinePos = mapTimelinePosFromX(anchorX);
        m_pixelPerMicrosecond *= scaleFactor;

        updateThumbnailSequenceGeometry();
        final int newAnchorX = mapXFromTimelinePos(anchorTimelinePos);
        final int scrollX = getScrollX() + newAnchorX - anchorX;
        // According to android developer document, this version of scrollTo()
        // also clamps the scrolling to the bounds of our child.
        scrollTo(scrollX, 0);
    }

    /*! \if ENGLISH
     *   \brief Sets the scroll listener interface.
     *   \param listener Rolling monitor interface
     *   \else
     *   \brief 设置滚动监听接口
     *   \param listener 滚动监听接口
     *   \endif
     *   \sa getOnScrollChangeListenser
     */
    public void setOnScrollChangeListener(OnScrollChangeListener listener) {
        NvsUtils.checkFunctionInMainThread();
        m_scrollChangeListener = listener;
    }

    /*! \if ENGLISH
     *   \brief Gets the current scrolling listener interface.
     *   \return Returns the current scrolling listener interface.
     *   \else
     *   \brief 获取当前滚动监听接口
     *   \return 返回当前滚动监听接口
     *   \endif
     *   \sa setOnScrollChangeListenser
     */
    public OnScrollChangeListener getOnScrollChangeListenser() {
        NvsUtils.checkFunctionInMainThread();
        return m_scrollChangeListener;
    }

    /*! \if ENGLISH
     *   \brief Sets whether to start scroll preview.
     *   \param enable Whether to start scroll preview.
     *   \else
     *   \brief 设置是否开启滚动预览
     *   \param enable 是否开启滚动预览
     *   \endif
     *   \sa getScrollEnabled
     *   \since 1.11.0
     */
    public void setScrollEnabled(boolean enable) {
        m_scrollEnabled = enable;
    }

    /*! \if ENGLISH
     *   \brief Gets whether scroll preview has started.
     *   \return Returns whether scroll preview has started.
     *   \else
     *   \brief 获取当前是否开启了滚动预览
     *   \return 返回当前是否开启了滚动预览
     *   \endif
     *   \sa setScrollEnabled
     *   \since 1.11.0
     */
    public boolean getScrollEnabled() {
        return m_scrollEnabled;
    }

    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
    }

    @Override
    protected void onLayout(boolean changed, int left, int top, int right, int bottom) {
        super.onLayout(changed, left, top, right, bottom);
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();

        if (!isInEditMode()) {
            m_iconGenerator = new NvsIconGenerator();
            m_iconGenerator.setIconCallback(this);
        }
    }

    @Override
    protected void onDetachedFromWindow() {
        cancelIconTask();

        m_scrollChangeListener = null;

        if (m_iconGenerator != null) {
            m_iconGenerator.setIconCallback(null);
            m_iconGenerator.release();
            m_iconGenerator = null;
        }

        super.onDetachedFromWindow();
    }

    @Override
    protected void onScrollChanged(int l, int t, int oldl, int oldt) {
        super.onScrollChanged(l, t, oldl, oldt);
        if (!needUpdate) {
            needUpdate = true;
        }
        if (m_scrollChangeListener != null)
            m_scrollChangeListener.onScrollChanged(this, l, oldl);
        updateThumbnails();
    }

    /*! \cond */
    @Override
    public boolean onInterceptTouchEvent(MotionEvent ev) {
        if (m_scrollEnabled)
            return super.onInterceptTouchEvent(ev);
        else
            return false;
    }

    @Override
    public boolean onTouchEvent(MotionEvent ev) {
        if (m_scrollEnabled)
            return super.onTouchEvent(ev);
        else
            return false;
    }

    private LinearLayout mLlTailViewContainer;
    private LinearLayout mLlThumbnailCoverContainer;

    private void init(Context context) {
        setVerticalScrollBarEnabled(false);
        setHorizontalScrollBarEnabled(false);
        /*根布局 Root*/
        FrameLayout flRootView = new FrameLayout(context);
        //mFlRootView.setBackgroundColor(getResources().getColor(R.color.red_fc2b));
        /*尾部视图容器
        * Tail view container
        * */
        mLlTailViewContainer = new LinearLayout(getContext());
        mLlTailViewContainer.setOrientation(LinearLayout.HORIZONTAL);
        /*素略图序列封面视图容器
        * Thumbnail Sequence Cover View Container
        * */
        mLlThumbnailCoverContainer = new LinearLayout(getContext());
        mLlThumbnailCoverContainer.setOrientation(LinearLayout.HORIZONTAL);
        // mLlThumbnailCoverContainer.setBackgroundColor(getResources().getColor(R.color.gray_a4a));
        /*素略图序列视图容器
        * Thumbnail sequence view container
        * */
        m_contentView = new ContentView(context);
        //m_contentView.setBackgroundColor(getResources().getColor(R.color.red_fc2b));
        flRootView.addView(m_contentView, new LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.MATCH_PARENT));
        flRootView.addView(mLlTailViewContainer, new LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.MATCH_PARENT));
        flRootView.addView(mLlThumbnailCoverContainer, new LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.MATCH_PARENT));
        addView(flRootView, new LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT));
    }

    /**
     * 更新片尾视图
     * Update tail view
     *
     * @param thumbnailSequence th thumbnail sequence缩略图序列
     */
    private void updateTailView(ThumbnailSequence thumbnailSequence) {
        int lastOne = m_thumbnailSequenceArray.size() - 1;
        if (thumbnailSequence == null || thumbnailSequence.m_index == lastOne) {
            //空或者最后一个不添加片尾视图
            // Empty or the last one without adding trailer view
            return;
        }
        if (thumbnailSequence.m_index == lastOne - 1 && BaseTrackClip.CLIP_HOLDER.equals(m_thumbnailSequenceArray.get(lastOne).type)) {
            //最后一个是补黑，则其前一个片段不添加片尾视图
            // The last one is to fill in the black, then the previous segment does not
            // add the trailer view
            return;
        }
        int leftMargin = getTailViewLeft(thumbnailSequence);
        View child = mLlTailViewContainer.getChildAt(thumbnailSequence.m_index);
        if (child != null) {
            LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) child.getLayoutParams();
            if (layoutParams.leftMargin != leftMargin) {
                layoutParams.leftMargin = leftMargin;
                child.setLayoutParams(layoutParams);
            }
            updateTailVieContent((FrameLayout) child, thumbnailSequence.tailInfo);
        } else {
            addTailView(leftMargin, thumbnailSequence.tailInfo);
        }
    }

    /**
     * 获取片尾视图的左间隔
     * Get tail view left margin
     *
     * @param thumbnailSequence thumbnail sequence 缩略图序列
     */
    private int getTailViewLeft(ThumbnailSequence thumbnailSequence) {
        int leftMargin;
        int tailViewW = (int) getResources().getDimension(R.dimen.dp_px_78);
        int tempW = tailViewW;
        if (thumbnailSequence.m_index == 0) {
            leftMargin = thumbnailSequence.m_x + thumbnailSequence.m_width;
            tempW = tailViewW / 2;
        } else {
            leftMargin = thumbnailSequence.m_width;
        }
        return leftMargin - tempW;
    }

    /**
     * 添加片尾视图
     * Add tail view
     *
     * @param leftMargin left margin 左间隔
     * @param tailInfo   tail info 尾部视图对应的信息
     */
    private void addTailView(int leftMargin, ThumbnailClip.TailInfo tailInfo) {
        addTailView(mLlTailViewContainer.getChildCount(), leftMargin, tailInfo);
    }

    /**
     * 添加片尾视图
     *
     * @param childIndex add index 添加的位置
     * @param leftMargin left margin 左间隔
     * @param tailInfo   tail info 尾部视图对应的信息
     */
    private void addTailView(int childIndex, int leftMargin, ThumbnailClip.TailInfo tailInfo) {
        if (childIndex >= 0 && childIndex <= mLlTailViewContainer.getChildCount()) {
            FrameLayout imageContainer = new FrameLayout(getContext());
            ImageView imageView = new ImageView(getContext());
            imageView.setScaleType(ImageView.ScaleType.FIT_XY);
            LayoutParams imageViewParams = new LayoutParams(LayoutParams.WRAP_CONTENT,
                    (int) getResources().getDimension(R.dimen.dp_px_30));
            imageViewParams.gravity = Gravity.CENTER;
            imageViewParams.leftMargin = (int) getResources().getDimension(R.dimen.dp_px_12);
            imageViewParams.rightMargin = imageViewParams.leftMargin;
            if (TextUtils.isEmpty(tailInfo.getId())) {
                tailInfo.setCoverId(0);
                tailInfo.setCoverPath("");
            }
            if (TextUtils.isEmpty(tailInfo.getCoverPath())) {
                if (tailInfo.getCoverId() == 0) {
                    imageView.setImageResource(R.drawable.icon_transtion_default);
                } else {
                    imageView.setImageResource(tailInfo.getCoverId());
                }
            } else {
                ImageLoader.loadUrl(getContext(), tailInfo.getCoverPath(), imageView);
            }
            imageContainer.addView(imageView, imageViewParams);

            imageContainer.setOnClickListener(mTailViewClick);
            LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams((int) getResources().getDimension(R.dimen.dp_px_78),
                    LayoutParams.MATCH_PARENT);
            layoutParams.leftMargin = leftMargin;
            imageContainer.setBackgroundResource(R.mipmap.ic_transition_bg);
            mLlTailViewContainer.addView(imageContainer, childIndex, layoutParams);
        }
    }

    /**
     * 更新尾部视图的内容
     * Update tail view content
     *
     * @param index    the tail view index
     * @param tailInfo the tail info
     */
    private void updateTailVieContent(int index, ThumbnailClip.TailInfo tailInfo) {
        updateTailVieContent((FrameLayout) mLlTailViewContainer.getChildAt(index), tailInfo);
    }

    /**
     * 更新尾部视图的内容
     * Update tail view content
     *
     * @param tailViewParent the tail view parent layout
     * @param tailInfo       the tail info
     */
    private void updateTailVieContent(FrameLayout tailViewParent, ThumbnailClip.TailInfo tailInfo) {
        if (tailViewParent != null) {
            ImageView imageView = (ImageView) tailViewParent.getChildAt(0);
            ViewGroup.LayoutParams layoutParams = imageView.getLayoutParams();
            if (TextUtils.isEmpty(tailInfo.getId())) {
                tailInfo.setCoverId(0);
                tailInfo.setCoverPath("");
            }
            if (TextUtils.isEmpty(tailInfo.getCoverPath())) {
                layoutParams.width = LayoutParams.WRAP_CONTENT;
                if (tailInfo.getCoverId() == 0) {
                    imageView.setImageResource(R.drawable.icon_transtion_default);
                } else {
                    imageView.setImageResource(tailInfo.getCoverId());
                }
            } else {
                layoutParams.width = (int) getResources().getDimension(R.dimen.dp_px_30);
                ImageLoader.loadUrl(getContext(), tailInfo.getCoverPath(), imageView);
            }
            imageView.setLayoutParams(layoutParams);
        }
    }

    /**
     * 更新缩略图封面
     * Update thumbnail cover
     *
     * @param sequence the thumbnail sequence 缩略图序列
     */
    private void updateThumbnailCover(ThumbnailSequence sequence, boolean checkSelectedKeyFrame) {
        View child = mLlThumbnailCoverContainer.getChildAt(sequence.m_index);
        ThumbnailCoverView thumbnailCoverView;
        if (child != null) {
            thumbnailCoverView = (ThumbnailCoverView) child;
            thumbnailCoverView.setWidth(sequence.m_width);
        } else {
            LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(sequence.m_width,
                    LayoutParams.MATCH_PARENT);
            mLlThumbnailCoverContainer.addView(thumbnailCoverView = new ThumbnailCoverView(getContext()), layoutParams);
        }
        thumbnailCoverView.setSpeedInfo(sequence.speedInfo);
        thumbnailCoverView.enableVolume(!sequence.hasAudio);
        //thumbnailCoverView.enableFaceProp(sequence.hasProp);
        thumbnailCoverView.setAnimationInfo(sequence.animationInfo);
        long selectedPoint = sequence.keyFrameInfo.getSelectedPoint();
        if (selectedPoint >= 0) {
            thumbnailCoverView.setKeyFrameInfo(sequence.keyFrameInfo, selectedPoint);
        } else {
            thumbnailCoverView.setKeyFrameInfo(sequence.keyFrameInfo, checkSelectedKeyFrame ? (lengthToDuration(getScrollX()) - sequence.m_inPoint) : -1);
        }
        //trackMaskView.enableSpeed(true);
    }

    private int mSelectedCoverPosition = -1;

    /**
     * 获取当前选中的view的位置
     * Gets selected cover position.
     *
     * @return the selected cover position
     */
    public int getSelectedCoverPosition() {
        return mSelectedCoverPosition;
    }

    /**
     * 更新缩略图封面
     * Update thumbnail cover
     *
     * @param position the selected thumbnail cover position 选中缩图略封面的位置
     */
    private void selectedThumbnailCover(int position) {
        if (position == mSelectedCoverPosition) {
            return;
        }
        ThumbnailCoverView thumbnailCoverView;
        if (mSelectedCoverPosition >= 0) {
            View child = mLlThumbnailCoverContainer.getChildAt(mSelectedCoverPosition);
            if (child != null) {
                thumbnailCoverView = (ThumbnailCoverView) child;
                thumbnailCoverView.unselected();
            }
        }
        View child = mLlThumbnailCoverContainer.getChildAt(position);
        if (child != null) {
            thumbnailCoverView = (ThumbnailCoverView) child;
            thumbnailCoverView.selected();
            mSelectedCoverPosition = position;
        } else {
            mSelectedCoverPosition = -1;
        }
    }

    private int calculateThumbnailX(long timestamp) {
        return (int) Math.floor(timestamp * m_pixelPerMicrosecond + 0.5) + m_startPadding;
    }


    private long lengthToDuration(int length) {
        return (int) Math.floor(length / m_pixelPerMicrosecond + 0.5);
    }

    private void requestUpdateThumbnailSequenceGeometry() {
        new Handler().post(new Runnable() {
            @Override
            public void run() {
                updateThumbnailSequenceGeometry();
            }
        });
    }

    private void updateThumbnailSequenceGeometry() {
        cancelIconTask();

        // Clear thumbnails since their geometry is subject to change
        clearThumbnails();

        // Calculate thumbnail width in pixel
        final int h = getHeight();
        if (h == 0)
            return;
        needUpdate = true;
        m_thumbnailSequenceMap.clear();

        int lastX = m_startPadding;
        m_maxThumbnailWidth = 0;
        boolean hasHolder = false;
        for (ThumbnailSequence thumbnailSequence : m_thumbnailSequenceArray) {
            // Mark cached keyframe only mode as invalid
            thumbnailSequence.m_flags &= ~THUMBNAIL_SEQUENCE_FLAGS_CACHED_KEYFRAME_ONLY_VALID;

            final int x = calculateThumbnailX(thumbnailSequence.m_inPoint);
            final int x2 = calculateThumbnailX(thumbnailSequence.m_outPoint);
            if (x2 <= x) {
                // For current scale ratio, this thumbnail sequence can't be represented, just ignore it
                continue;
            }
            if (!hasHolder) {
                hasHolder = BaseTrackClip.CLIP_HOLDER.equals(thumbnailSequence.type);
            }
            thumbnailSequence.m_x = x;
            thumbnailSequence.m_width = x2 - x;
            updateTailView(thumbnailSequence);
            updateThumbnailCover(thumbnailSequence, true);
            // Calculate thumbnail width in pixel
            final float thumbnailAspectRatio = thumbnailSequence.m_thumbnailAspectRatio > 0 ?
                    thumbnailSequence.m_thumbnailAspectRatio : m_thumbnailAspectRatio;
            thumbnailSequence.m_thumbnailWidth = (int) Math.floor(h * thumbnailAspectRatio + 0.5);
            thumbnailSequence.m_thumbnailWidth = Math.max(thumbnailSequence.m_thumbnailWidth, 1);
            m_maxThumbnailWidth = Math.max(thumbnailSequence.m_thumbnailWidth, m_maxThumbnailWidth);

            m_thumbnailSequenceMap.put(x, thumbnailSequence);

            lastX = x2;
        }
        /*删除多于的尾部视图,尾部视图的数量是等于有效片段数量-1的
        * Delete more than tail views. The number of tail views is equal to the number of effective clips - 1
        * */
        int extraIndex = mLlTailViewContainer.getChildCount() - 1;
        int size = hasHolder ? m_thumbnailSequenceArray.size() - 1 : m_thumbnailSequenceArray.size();
        while (extraIndex >= size - 1 && extraIndex >= 0 && extraIndex < mLlTailViewContainer.getChildCount()) {
            mLlTailViewContainer.removeViewAt(extraIndex);
            extraIndex--;
        }
        /*删除多于的封面视图,封面视图数量是等于有效片段数量的
        * Delete more than cover views. The number of cover views is equal to the number of valid fragments
        * */
        extraIndex = mLlThumbnailCoverContainer.getChildCount() - 1;
        while (extraIndex >= size && extraIndex < mLlThumbnailCoverContainer.getChildCount()) {
            mLlThumbnailCoverContainer.removeViewAt(extraIndex);
            extraIndex--;
        }
        // Update desired content (view) width
        int contentWidth = lastX;
        if (m_maxTimelinePosToScroll <= 0) {
            contentWidth += m_endPadding;
        } else {
            int len = calculateThumbnailX(m_maxTimelinePosToScroll);
            if (len < contentWidth)
                contentWidth = len;
        }
        m_contentWidth = contentWidth;
        ViewGroup.LayoutParams layoutParams = mLlTailViewContainer.getLayoutParams();
        if (layoutParams.width != m_contentWidth - m_startPadding) {
            layoutParams.width = m_contentWidth - m_startPadding;
            mLlTailViewContainer.setLayoutParams(layoutParams);
        }
        layoutParams = mLlThumbnailCoverContainer.getLayoutParams();
        if (layoutParams.width != m_contentWidth - m_startPadding) {
            layoutParams.width = m_contentWidth - m_startPadding;
            mLlThumbnailCoverContainer.setLayoutParams(layoutParams);
        }
//        m_contentView.layout(0, 0, m_contentWidth, getHeight());
        m_contentView.requestLayout(); // updateThumbnails() will be called during layout
        if (getWidth() + getScrollX() > m_contentWidth) {
            final int newScrollX = Math.max(getScrollX() - (getWidth() + getScrollX() - m_contentWidth), 0);
            if (newScrollX != getScrollX())
                scrollTo(newScrollX, 0);
        }
    }

    private static class ClipImageView extends androidx.appcompat.widget.AppCompatImageView {
        private int m_clipWidth;
        private Rect mRect;

        ClipImageView(Context ctx, int clipWidth) {
            super(ctx);
            m_clipWidth = clipWidth;
        }

        @Override
        protected void onDraw(Canvas canvas) {
            if(mRect == null) {
                mRect = new Rect();
            }
            mRect.left = 0;
            mRect.top = 0;
            mRect.right = m_clipWidth;
            mRect.bottom = getHeight();
            canvas.clipRect(mRect);
            super.onDraw(canvas);
        }
    }

    private void updateThumbnails() {
        if (m_iconGenerator == null)
            return;

        if (m_thumbnailSequenceMap.isEmpty()) {
            clearThumbnails();
            return;
        }

        final int guardLength = m_maxThumbnailWidth;
        final int scrollX = getScrollX();
        final int width = getWidth();
        final int visibleLeftBound = Math.max(scrollX - guardLength, m_startPadding);
        final int visibleRightBound = visibleLeftBound + width + guardLength;
//        LogUtils.d("guardLength=" + guardLength + ",scrollX=" + scrollX + ",width=" + width + ",visibleLeftBound=" + visibleLeftBound + ",visibleRightBound=" + visibleRightBound);
        if (visibleRightBound <= visibleLeftBound) {
            clearThumbnails();
            return;
        }

        Integer startKey = m_thumbnailSequenceMap.floorKey(visibleLeftBound);
        if (startKey == null)
            startKey = m_thumbnailSequenceMap.firstKey();

        SortedMap<Integer, ThumbnailSequence> sortedMap = m_thumbnailSequenceMap.tailMap(startKey);
        for (Map.Entry<Integer, ThumbnailSequence> entry : sortedMap.entrySet()) {
            ThumbnailSequence seq = entry.getValue();
            if (seq.m_x + seq.m_width < visibleLeftBound)
                continue;
            if (seq.m_x >= visibleRightBound)
                break;

            //防止做除法的时候报错  Prevent division error
            seq.m_thumbnailWidth = Math.max(seq.m_thumbnailWidth, 1);
            seq.m_width = Math.max(seq.m_width, 1);

            int thumbnailX;
            if (seq.m_x < visibleLeftBound)
                thumbnailX = seq.m_x + (visibleLeftBound - seq.m_x) / seq.m_thumbnailWidth * seq.m_thumbnailWidth;
            else
                thumbnailX = seq.m_x;

            boolean outOfBound = false;
            final int seqEndX = seq.m_x + seq.m_width;
//            final long timeStep = Math.max((long) ((double) seq.m_thumbnailWidth / seq.m_width * seq.m_trimDuration), 1);
//            long seqPointsPerMicrosecond =seq.m_width/seq.m_trimDuration;
            double seqPointsPerMicrosecond = divide(seq.m_width, seq.m_trimDuration, 10);
            //Clip 上，单位时间的长度，不是timeline上的长度
            // The length of unit time on the clip, not the length on the timeline
            //防止做除法的时候报错  Prevent division error
            seqPointsPerMicrosecond = Math.max(seqPointsPerMicrosecond, 1D);
            //每个Item对应的clip上的的时间，不是timeline上的时间
            // The time on the clip corresponding to each item, not the time on the timeline
            int thumbnailItemTime = (int) (seq.m_thumbnailWidth / seqPointsPerMicrosecond);
            long firstImageWidth = 0;
            if (mIsTrimming) {
                firstImageWidth = (long) ((thumbnailItemTime - seq.m_trimIn % thumbnailItemTime) * seqPointsPerMicrosecond);
            }
            while (thumbnailX < seqEndX) {
                if (thumbnailX >= visibleRightBound) {
                    outOfBound = true;
                    break;
                }

                int thumbnailWidth = seq.m_thumbnailWidth;

                // Calculate timestamp of this thumbnail
                long timestamp = seq.calcTimestampFromX(thumbnailX);
                if (firstImageWidth > 0) {
                    timestamp = seq.calcTimestampFromX((int) (thumbnailX - (thumbnailWidth - firstImageWidth)));
                    if (timestamp < 0) {
                        timestamp = 0;
                    }
                    thumbnailWidth = (int) firstImageWidth;
                } else if (thumbnailX + thumbnailWidth > seqEndX) {
                    thumbnailWidth = seqEndX - thumbnailX;
                }

                if (thumbnailItemTime > seq.m_trimDuration) {
                    int tempWith = (int) (seq.m_trimDuration * seqPointsPerMicrosecond);
                    if (tempWith > 0) {
                        thumbnailWidth = tempWith;
                    }
                }

                if (thumbnailX + thumbnailWidth > seqEndX) {
                    int tempValue = seqEndX - thumbnailX;
                    if (tempValue > 0) {
                        thumbnailWidth = seqEndX - thumbnailX;
                    }
                }

                // Find the thumbnail from the current thumbnail map first
                ThumbnailId tid = new ThumbnailId(seq.m_index, timestamp);
                Thumbnail thumbnail = m_thumbnailMap.get(tid);
                if (thumbnail == null) {
                    // Create a new thumbnail
                    thumbnail = new Thumbnail();
                    thumbnail.m_owner = seq;
                    thumbnail.m_timestamp = timestamp;
                    thumbnail.m_imageViewUpToDate = false;
                    thumbnail.m_touched = true;

                    m_thumbnailMap.put(tid, thumbnail);

                    if (thumbnailWidth == seq.m_thumbnailWidth)
                        thumbnail.m_imageView = new ImageView(this.getContext());
                    else
                        thumbnail.m_imageView = new ClipImageView(this.getContext(), thumbnailWidth);

                    if (m_thumbnailImageFillMode == THUMBNAIL_IMAGE_FILLMODE_STRETCH)
                        thumbnail.m_imageView.setScaleType(ImageView.ScaleType.FIT_XY);
                    else if (m_thumbnailImageFillMode == THUMBNAIL_IMAGE_FILLMODE_ASPECTCROP)
                        thumbnail.m_imageView.setScaleType(ImageView.ScaleType.CENTER_CROP);

                    m_contentView.addView(thumbnail.m_imageView);
                    thumbnail.m_imageView.layout(thumbnailX, 0, thumbnailX + seq.m_thumbnailWidth, m_contentView.getHeight());
                } else {
                    thumbnail.m_touched = true;
                }

                if (firstImageWidth > 0) {
                    firstImageWidth = -1;
                }

                thumbnailX += thumbnailWidth;
            }

            if (outOfBound)
                break;
        }

        //
        // Remove untouched thumbnail objects and collect icons from cache
        //
        m_updatingThumbnail = true;

        boolean hasDirtyThumbnail = false;
        TreeMap<ThumbnailId, Bitmap> iconMap = new TreeMap<ThumbnailId, Bitmap>();
        Set<Map.Entry<ThumbnailId, Thumbnail>> thumbnailSet = m_thumbnailMap.entrySet();
        Iterator<Map.Entry<ThumbnailId, Thumbnail>> itrThumbnail = thumbnailSet.iterator();
        while (itrThumbnail.hasNext()) {
            Map.Entry<ThumbnailId, Thumbnail> entry = itrThumbnail.next();
            Thumbnail thumbnail = entry.getValue();

            // Update placeholder bitmap
            if (thumbnail.m_imageView != null) {
                Drawable drawable = thumbnail.m_imageView.getDrawable();
                if (drawable != null) {
                    Bitmap bitmap = ((BitmapDrawable) drawable).getBitmap();
                    if (bitmap != null)
                        m_placeholderBitmap = bitmap;
                }
            }

            if (!thumbnail.m_touched) {
                // These thumbnail hasn't been touched, remove it
                if (thumbnail.m_iconTaskId != 0)
                    m_iconGenerator.cancelTask(thumbnail.m_iconTaskId);

                m_contentView.removeView(thumbnail.m_imageView);
                itrThumbnail.remove();
                continue;
            }

            // Reset touched flag for later use
            thumbnail.m_touched = false;

            if (thumbnail.m_imageViewUpToDate) {
                if (thumbnail.m_imageView != null) {
                    Bitmap bitmap = ((BitmapDrawable) thumbnail.m_imageView.getDrawable()).getBitmap();
                    iconMap.put(entry.getKey(), bitmap);
                }
            } else {
                final long realTimestamp = thumbnail.m_owner.m_stillImageHint ? 0 : thumbnail.m_timestamp;
                updateKeyframeOnlyModeForThumbnailSequence(thumbnail.m_owner);
                final int flags = (thumbnail.m_owner.m_flags & THUMBNAIL_SEQUENCE_FLAGS_CACHED_KEYFRAME_ONLY) != 0 ? 1 : 0;
                ITrackClip.ThumbNailInfo thumbNailInfo = thumbnail.m_owner.thumbNailInfo;
                if (thumbNailInfo != null) {
                    //假设要获取第is的缩略图, %07d 表示最少7位，不足补0 ，d表示正整数
                    // Suppose you want to get the thumbnail of the is,% 07d represents
                    // at least 7 digits, and if it is not enough, fill in 0, and d represents a positive integer
                    //url = urlPrefix + "-" + String.format("%07d",interval * i) + "." + extension;
                    thumbNailInfo.interval = Math.max(thumbNailInfo.interval, 1);
                    int i = thumbNailInfo.isImage ?  0: (int) (realTimestamp / thumbNailInfo.interval / 1000);
                    String url = thumbNailInfo.urlPrefix + "-" + String.format("%07d",thumbNailInfo.interval * i) +"."+thumbNailInfo.extension;
                    ImageLoader.loadUrl(getContext(), url,  thumbnail.m_imageView);
                } else {
                    Bitmap bitmap = m_iconGenerator.getIconFromCache(thumbnail.m_owner.m_mediaFilePath, realTimestamp, flags);
                    if (bitmap != null) {
                        iconMap.put(entry.getKey(), bitmap);
                        if (setBitmapToThumbnail(bitmap, thumbnail)) {
                            thumbnail.m_imageViewUpToDate = true;
                            thumbnail.m_iconTaskId = 0;
                        }
                    } else {
                        hasDirtyThumbnail = true;
                        thumbnail.m_iconTaskId = m_iconGenerator.getIcon(thumbnail.m_owner.m_mediaFilePath, realTimestamp, flags);
                    }
                }
            }
        }

        m_updatingThumbnail = false;
       /* int childCount = m_contentView.getChildCount();
        float x = -1;
        int[] local1 = new int[2];
        int[] local2 = new int[2];
        if (childCount > 0) {
            View child = m_contentView.getChildAt(0);
            child.getLocationInWindow(local1);
            child.getLocationOnScreen(local2);
            x = child.getX();
        }
       LogUtils.d("count=" + childCount + ",w1=" + local1[0] + ",s2=" + local2[0]+",x="+x+",hasDirtyThumbnail="+hasDirtyThumbnail);*/
        if (!hasDirtyThumbnail)
            return;

        if (iconMap.isEmpty()) {
            // Now we set placeholder image to thumbnail whose ImageView was not up to date yet
            if (m_placeholderBitmap != null) {
                for (Map.Entry<ThumbnailId, Thumbnail> entry : m_thumbnailMap.entrySet()) {
                    Thumbnail thumbnail = entry.getValue();
                    if (!thumbnail.m_imageViewUpToDate)
                        setBitmapToThumbnail(m_placeholderBitmap, thumbnail);
                }
            }

            return;
        }

        // Now we set image to thumbnail whose ImageView was not up to date yet
        for (Map.Entry<ThumbnailId, Thumbnail> entry : m_thumbnailMap.entrySet()) {
            Thumbnail thumbnail = entry.getValue();
            if (thumbnail.m_imageViewUpToDate)
                continue;

            // We fail to find an image with the given timestamp value,
            // To make thumbnail sequence looks better we use an image whose
            // timestamp is close to the given timestamp
            Map.Entry<ThumbnailId, Bitmap> ceilingEntry = iconMap.ceilingEntry(entry.getKey());

            if (ceilingEntry != null) {
                setBitmapToThumbnail(ceilingEntry.getValue(), thumbnail);
            } else{
                Map.Entry<ThumbnailId, Bitmap> bitmapEntry = iconMap.lastEntry();
                if (bitmapEntry != null) {
                    setBitmapToThumbnail(bitmapEntry.getValue(), thumbnail);
                }
            }
        }
    }

    public double divide(double v1, double v2, int scale) {
        if (scale <= 0) {
            scale = 1;
        }
        if (v2 <= 0) {
            v2 = 1;
        }
        BigDecimal b1 = new BigDecimal(Double.toString(v1));
        BigDecimal b2 = new BigDecimal(Double.toString(v2));
        try {
            return b1.divide(b2, scale, BigDecimal.ROUND_HALF_UP).doubleValue();
        } catch (Exception e) {
            LogUtils.e(e);
        }
        return v1;
    }

    private void updateKeyframeOnlyModeForThumbnailSequence(ThumbnailSequence seq) {
        if ((seq.m_flags & THUMBNAIL_SEQUENCE_FLAGS_CACHED_KEYFRAME_ONLY_VALID) != 0)
            return;

        if (seq.m_onlyDecodeKeyFrame) {
            // We always respect the user's option
            seq.m_flags |= THUMBNAIL_SEQUENCE_FLAGS_CACHED_KEYFRAME_ONLY |
                    THUMBNAIL_SEQUENCE_FLAGS_CACHED_KEYFRAME_ONLY_VALID;
            return;
        }

        final long timeStep = Math.max((long) (seq.m_thumbnailWidth / m_pixelPerMicrosecond + 0.5), 1);
        final boolean keyFrameOnly = shouldDecodecKeyFrameOnly(seq.m_mediaFilePath, timeStep);
        if (keyFrameOnly)
            seq.m_flags |= THUMBNAIL_SEQUENCE_FLAGS_CACHED_KEYFRAME_ONLY;
        else
            seq.m_flags &= ~THUMBNAIL_SEQUENCE_FLAGS_CACHED_KEYFRAME_ONLY;
        seq.m_flags |= THUMBNAIL_SEQUENCE_FLAGS_CACHED_KEYFRAME_ONLY_VALID;
    }

    private boolean shouldDecodecKeyFrameOnly(String filePath, long timeStep) {
        if (filePath == null || filePath.startsWith(NvsConstants.HTTP)) {
            return false;
        }
        NvsStreamingContext streamingContext = NvsStreamingContext.getInstance();
        if (streamingContext == null)
            return false;

        NvsAVFileInfo fileInfo = streamingContext.getAVFileInfo(filePath);
        if (fileInfo == null)
            return false;

        if (fileInfo.getVideoStreamCount() < 1)
            return false;

        NvsRational fps = fileInfo.getVideoStreamFrameRate(0);
        if (fps == null)
            return false;

        if (fps.den <= 0 || fps.num <= 0)
            return false;

        long videoDuration = fileInfo.getVideoStreamDuration(0);
        if (videoDuration < timeStep)
            return false;

        int keyframeInterval = streamingContext.detectVideoFileKeyframeInterval(filePath);
        if (keyframeInterval == 0)
            keyframeInterval = 30; // We can't detect its GOP size, just guess it
        else if (keyframeInterval == 1)
            return false; // Keyframe only, no need to use keyframe only mode

        int keyframeIntervalTime = (int) (keyframeInterval * ((double) fps.den / fps.num) * 1000000);
        if (keyframeInterval <= 30) {
            if (timeStep > keyframeIntervalTime * 0.9)
                return true;
        } else if (keyframeInterval <= 60) {
            if (timeStep > keyframeIntervalTime * 0.8)
                return true;
        } else if (keyframeInterval <= 100) {
            if (timeStep > keyframeIntervalTime * 0.7)
                return true;
        } else if (keyframeInterval <= 150) {
            if (timeStep > keyframeIntervalTime * 0.5)
                return true;
        } else if (keyframeInterval <= 250) {
            if (timeStep > keyframeIntervalTime * 0.3)
                return true;
        } else {
            if (timeStep > keyframeIntervalTime * 0.2)
                return true;
        }

        return false;
    }

    public boolean isIsTrimming() {
        return mIsTrimming;
    }

    public void setIsTrimming(boolean mIsTrimming) {
        this.mIsTrimming = mIsTrimming;
    }

    private boolean setBitmapToThumbnail(Bitmap bitmap, Thumbnail thumbnail) {
        if (bitmap == null || thumbnail.m_imageView == null)
            return false;

        thumbnail.m_imageView.setImageBitmap(bitmap);
        return true;
    }

    private void clearThumbnailSequences() {
        cancelIconTask();
        clearThumbnails();

        m_thumbnailSequenceArray.clear();
        m_thumbnailSequenceMap.clear();
        m_contentWidth = 0;
    }

    private void clearThumbnails() {
        for (Map.Entry<ThumbnailId, Thumbnail> entry : m_thumbnailMap.entrySet())
            m_contentView.removeView(entry.getValue().m_imageView);

        m_thumbnailMap.clear();
    }

    private void cancelIconTask() {
        if (m_iconGenerator != null)
            m_iconGenerator.cancelTask(0);
    }


    @Override
    public void onIconReady(final Bitmap icon, long timestamp, final long taskId) {
        post(new Runnable() {
            @Override
            public void run() {
                // updateThumbnails();
                updateOne(icon, taskId);
            }
        });
    }

    private void updateOne(Bitmap bitmap, long taskId) {
        for (Map.Entry<ThumbnailId, Thumbnail> entry : m_thumbnailMap.entrySet()) {
            Thumbnail thumbnail = entry.getValue();
            if (thumbnail.m_iconTaskId == taskId) {
                // LogUtils.d("updateOne,find,taskId="+taskId+",timestamp="+thumbnail.m_timestamp);
                setBitmapToThumbnail(bitmap, thumbnail);
                break;
            }

        }
    }

    private OnTailViewClickListener mOnTailViewListener;

    /**
     * 设置片段尾部视图点击监听
     */
    public void setOnTailViewClickListener(OnTailViewClickListener listener) {
        mOnTailViewListener = listener;
    }

    public interface OnTailViewClickListener {
        void onClick(int index, ThumbnailClip.TailInfo tailInfo);
    }

    private OnClickListener mTailViewClick = new OnClickListener() {
        @Override
        public void onClick(View v) {
            if (mOnTailViewListener != null) {
                int index = mLlTailViewContainer.indexOfChild(v);
                mOnTailViewListener.onClick(index, m_thumbnailSequenceArray.get(index).tailInfo);
            }
        }
    };
}