package com.meishe.myvideo.ui.trackview;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffXfermode;
import android.graphics.Rect;
import android.util.AttributeSet;
import android.util.Log;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;

import androidx.annotation.Nullable;

import com.meicam.sdk.NvsWaveformDataGenerator;
import com.meishe.base.utils.ScreenUtils;
import com.meishe.myvideo.util.PixelPerMicrosecondUtil;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2023/12/13 17:07
 * @Description :自定义波形图
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class NvsWaveformViewWrap extends LinearLayout implements NvsWaveformDataGenerator.WaveformDataCallback {

    private long m_currentTaskId = 0;

    // Current waveform data
    private byte[] m_leftWaveformData;
    private byte[] m_rightWaveformData;
    private long m_samplesPerGroup = 0;

    private long mTrimIn = 0;
    private long mTrimOut;

    private long mInPoint;

    private int mItemWidth;
    private String mAudioFilePath;
    private int mWaveformColor;
    private int mBackgroundColor;
    private NvsWaveformDataGenerator m_waveformDataGenerator;
    private long m_audioFileDuration;
    private long m_audioFileSampleCount;
    private int mX;

    public NvsWaveformViewWrap(Context context) {
        this(context, null);
    }

    public NvsWaveformViewWrap(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    private void init(Context context) {
        setOrientation(LinearLayout.HORIZONTAL);
        mItemWidth = ScreenUtils.getScreenWidth() * 2;
        m_waveformDataGenerator = new NvsWaveformDataGenerator();
        m_waveformDataGenerator.setWaveformDataCallback(this);
    }

    public void setTrimIn(long trimIn) {
        int index = getIndex(trimIn);
        NvsWaveformView childAt = (NvsWaveformView) getChildAt(index);
        if (childAt != null) {
            ViewGroup.LayoutParams layoutParams = childAt.getLayoutParams();
            childAt.m_trimIn = trimIn;
            layoutParams.width = PixelPerMicrosecondUtil.durationToLength(childAt.m_trimOut - childAt.m_trimIn);
            childAt.setLayoutParams(layoutParams);
            childAt.forceDraw();
            childAt.startDraw();
        }
        mTrimIn = trimIn;
    }

    private int getIndex(long trimIn) {
        int width = PixelPerMicrosecondUtil.durationToLength(trimIn);
        return (int) Math.floor(width * 1F / mItemWidth);
    }

    @Override
    public void setBackgroundColor(int color) {
        mBackgroundColor = color;
    }


    public void setTrimOut(long trimOut) {
        ViewGroup.LayoutParams layoutParams = getLayoutParams();
        layoutParams.width = PixelPerMicrosecondUtil.durationToLength(mTrimOut - mTrimIn);
        setLayoutParams(layoutParams);
        mTrimOut = trimOut;
    }

    public void setInPoint(long inPoint) {
        this.mInPoint = inPoint;
    }

    private long calcExpectedSamplesPerGroup(long trimIn, long trimOut, long duration) {
        double ratio = (double) (trimOut - trimIn) / (double) duration;
        long sampleCount = (long) (m_audioFileSampleCount * ratio);
        int w = PixelPerMicrosecondUtil.durationToLength(duration);
        if (w <= 0)
            return 444;

        long samplesPerGroup = (sampleCount + w / 2) / w;
        return Math.max(samplesPerGroup, 1);
    }

    public void setWaveformColor(int waveformColor) {
        mWaveformColor = waveformColor;
    }

    public void setData(String audioFilePath, long inPoint, long trimIn, long trimOut) {
        mAudioFilePath = audioFilePath;
        mTrimIn = trimIn;
        mTrimOut = trimOut;
        mInPoint = inPoint;
        long duration = trimOut - mTrimIn;
        int maxWidth = PixelPerMicrosecondUtil.durationToLength(duration);
        mX = PixelPerMicrosecondUtil.durationToLength(mInPoint);
        ViewGroup.LayoutParams layoutParams1 = getLayoutParams();
        if (layoutParams1 == null) {
            layoutParams1 = new ViewGroup.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.MATCH_PARENT);
        }
        layoutParams1.width = maxWidth;
        setLayoutParams(layoutParams1);

        long audioFileDuration = m_waveformDataGenerator.getAudioFileDuration(mAudioFilePath);
        long sampleCount = m_waveformDataGenerator.getAudioFileSampleCount(mAudioFilePath);
        if (audioFileDuration <= 0 || sampleCount <= 0)
            return;

        m_audioFileDuration = audioFileDuration;
        m_audioFileSampleCount = sampleCount;
        //mTrimIn = 0;
        //mTrimOut = audioFileDuration;
        maxWidth = PixelPerMicrosecondUtil.durationToLength(m_audioFileDuration);
        float value = maxWidth * 1F / mItemWidth;
        int count = (int) Math.ceil(value);
        for (int i = 0; i < count; i++) {
            LayoutParams layoutParams = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.MATCH_PARENT);
            NvsWaveformView waveformViewBack = new NvsWaveformView(getContext());
            waveformViewBack.setSingleChannelMode(true);
            waveformViewBack.setBackgroundColor(mBackgroundColor);
            waveformViewBack.setWaveformColor(mWaveformColor);
            waveformViewBack.setTrimIn(mTrimIn + i * PixelPerMicrosecondUtil.lengthToDuration(mItemWidth));
            waveformViewBack.setTrimOut((i + 1) * PixelPerMicrosecondUtil.lengthToDuration(mItemWidth));
            layoutParams.gravity = Gravity.CENTER_VERTICAL;
            layoutParams.width = mItemWidth;
            addView(waveformViewBack);
            waveformViewBack.setLayoutParams(layoutParams);
        }
        if (value - count > 0) {
          /*  int lastWidth = (int) ((value - count) * mItemWidth);
            LayoutParams layoutParams = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.MATCH_PARENT);
            NvsWaveformView waveformViewBack = new NvsWaveformView(getContext());
            waveformViewBack.setSingleChannelMode(true);
            waveformViewBack.setBackgroundColor(mBackgroundColor);
            waveformViewBack.setWaveformColor(mWaveformColor);
            long trimIn1 = mTrimIn + count * PixelPerMicrosecondUtil.lengthToDuration(mItemWidth);
            waveformViewBack.setTrimIn(trimIn1);
            waveformViewBack.setTrimOut(trimIn1 + PixelPerMicrosecondUtil.lengthToDuration(lastWidth));
            layoutParams.gravity = Gravity.CENTER_VERTICAL;
            layoutParams.width = lastWidth;
            addView(waveformViewBack);
            waveformViewBack.setLayoutParams(layoutParams);*/
        }

        setTrimIn(trimIn);

        WaveformDataCache.WaveData waveData = WaveformDataCache.getInstance().getData(mAudioFilePath);
        if (waveData != null) {
            m_leftWaveformData = waveData.leftWave;
            m_samplesPerGroup = waveData.m_samplesPerGroup;
            m_currentTaskId = 0;
            onScrollChanged(mX, mX);
        } else {
            post(new Runnable() {
                @Override
                public void run() {
                    long l = calcExpectedSamplesPerGroup(0, m_audioFileDuration, m_audioFileDuration);
                    m_currentTaskId = m_waveformDataGenerator.generateWaveformData(mAudioFilePath, l, 0, 0, 0);
                }
            });
        }
    }

    public void onScrollChanged(int x, int oldX) {
        mX = x;
        x -= PixelPerMicrosecondUtil.durationToLength(mInPoint);
        int index = getIndex(x);
        int nextIndex = index + 1;
        int preIndex = index - 1;
        if (nextIndex >= getChildCount()) {
            nextIndex = index;
        }
        if (preIndex < 0) {
            preIndex = 0;
        }
        drawChildView(preIndex);
        drawChildView(index);
        drawChildView(nextIndex);
    }

    private void drawChildView(int nextIndex) {
        NvsWaveformView childAt = (NvsWaveformView) getChildAt(nextIndex);
        if (childAt != null) {
            childAt.startDraw();
        }
    }

    @Override
    protected void onDetachedFromWindow() {
        cancelCurrentTask();
        if (m_waveformDataGenerator != null) {
            m_waveformDataGenerator.setWaveformDataCallback(null);
            m_waveformDataGenerator.release();
            m_waveformDataGenerator = null;
        }

        super.onDetachedFromWindow();
    }

    private void cancelCurrentTask() {
        if (!isInEditMode() && m_currentTaskId != 0) {
            if (m_waveformDataGenerator != null)
                m_waveformDataGenerator.cancelTask(m_currentTaskId);
            m_currentTaskId = 0;
        }
    }

    @Override
    public void onWaveformDataReady(long taskId,
                                    String audioFilePath,
                                    long audioFileSampleCount,
                                    long samplesPerGroup,
                                    byte[] leftWaveformData,
                                    byte[] rightWaveformData) {
        m_leftWaveformData = leftWaveformData;
        m_rightWaveformData = rightWaveformData;
        m_samplesPerGroup = samplesPerGroup;
        m_currentTaskId = 0;
        onScrollChanged(mX, mX);
        WaveformDataCache.getInstance().addData(mAudioFilePath, new WaveformDataCache.WaveData(leftWaveformData, samplesPerGroup));
    }

    private int getIndex(int left) {
        int index = (int) Math.floor(left * 1F / mItemWidth);
        if (index >= getChildCount()) {
            index = 0;
        }
        return index;
    }

    public void scale(float scale){
        Log.e("tell", "scale: scale = "+scale );
        int childCount = getChildCount();
        ViewGroup.LayoutParams layoutParams1 = getLayoutParams();
        layoutParams1.width =  PixelPerMicrosecondUtil.durationToLength(m_audioFileDuration);
        setLayoutParams(layoutParams1);
        for (int i = 0; i < childCount; i++) {
            NvsWaveformView childAt = (NvsWaveformView) getChildAt(i);
            ViewGroup.LayoutParams layoutParams = childAt.getLayoutParams();
            layoutParams.width = (int) (mItemWidth * scale);
            childAt.setLayoutParams(layoutParams);
            childAt.forceDraw();
        }
        onScrollChanged(mX, mX);
    }

    @Override
    public void onWaveformDataGenerationFailed(long l, String s, long l1) {

    }

    class NvsWaveformView extends View {
        private long m_trimIn = 0; // In millisecond
        private long m_trimOut = 0; // In millisecond
        private int m_waveformColor = Color.BLACK;
        private boolean m_singleChannelMode = false;

        private boolean startDraw = false;
        private boolean drawFinished = false;

        public NvsWaveformView(Context context) {
            super(context);
        }


        public void startDraw() {
            if (drawFinished) {
                return;
            }
            this.startDraw = true;
            invalidate();
        }

        public void stopDraw() {
            this.startDraw = false;
            invalidate();
        }

        public void setTrimIn(long trimIn) {
            trimIn = Math.max(trimIn, 0);
            m_trimIn = trimIn;
        }

        public void setTrimOut(long trimOut) {
            //trimOut = Math.max(trimOut, m_trimIn + 1);
            //trimOut = Math.min(trimOut, m_audioFileDuration);
            m_trimOut = trimOut;
        }

        public void setWaveformColor(int waveformColor) {
            if (waveformColor == m_waveformColor)
                return;

            m_waveformColor = waveformColor;
        }

        public void setSingleChannelMode(boolean singleChannelMode) {
            if (singleChannelMode == m_singleChannelMode)
                return;

            m_singleChannelMode = singleChannelMode;
        }

        private long calcExpectedSamplesPerGroup() {
            double ratio = (double) (m_trimOut - m_trimIn) / (double) m_audioFileDuration;
            long sampleCount = (long) (m_audioFileSampleCount * ratio);
            int w = getWidth();
            if (w <= 0)
                return 1;

            long samplesPerGroup = (sampleCount + w / 2) / w;
            return Math.max(samplesPerGroup, 1);
        }

        private void validateWaveformData() {
            if (m_samplesPerGroup <= 0) {
                return;
            }

            long expectedSamplesPerGroup = calcExpectedSamplesPerGroup();
            if (expectedSamplesPerGroup != m_samplesPerGroup) {
                invalidate();
            }
        }

        @Override
        protected void onSizeChanged(int w, int h, int oldw, int oldh) {
            if (oldw != w)
                validateWaveformData();

            super.onSizeChanged(w, h, oldw, oldh);
        }

        private float GetFloatWaveformDataValue(byte d) {
            return d / 128.0f;
        }

        @Override
        protected void onDraw(Canvas canvas) {
            super.onDraw(canvas);
            if (!startDraw) {
                return;
            }

            if (isInEditMode()){
                return;
            }

            if (m_audioFileDuration <= 0) {
                return;
            }

            if (m_samplesPerGroup <= 0 || m_leftWaveformData == null)
                return;

            final int leftDataCount = m_leftWaveformData.length / 2;
            final int rightDataCount = (m_rightWaveformData != null && !m_singleChannelMode) ? (m_rightWaveformData.length / 2) : 0;
            if (leftDataCount == 0)
                return;

            int w = this.getWidth();
            int h = this.getHeight();
            int waveformHeight = rightDataCount != 0 ? (h / 2) : h;
            Rect lineRect = new Rect();
            Paint p = new Paint();
            p.setStyle(Paint.Style.FILL);
            p.setAntiAlias(false);
            p.setColor(m_waveformColor);
            if (Color.alpha(m_waveformColor) == 255) ;
            p.setXfermode(new PorterDuffXfermode(PorterDuff.Mode.SRC));

            long startSampleIndex = (long) ((double) m_trimIn / m_audioFileDuration * m_audioFileSampleCount);
            long totalSampleCount = (long) ((double) (m_trimOut - m_trimIn) / m_audioFileDuration * m_audioFileSampleCount);
            if (totalSampleCount == 0)
                return;

            for (int x = 0; x < w; ++x) {
                double r = (double) x / w;
                long sampleIndex = startSampleIndex + (long) (r * totalSampleCount);
                int groupIndex = (int) (sampleIndex / m_samplesPerGroup);
                if (groupIndex < leftDataCount) {
                    int top = (int) (waveformHeight * (1.0f - (GetFloatWaveformDataValue(m_leftWaveformData[groupIndex * 2 + 1]) + 1.0f) / 2.0f));
                    int bottom = (int) (waveformHeight * (1.0f - (GetFloatWaveformDataValue(m_leftWaveformData[groupIndex * 2]) + 1.0f) / 2.0f));
                    lineRect.set(x, top, x + 1, bottom);
                    canvas.drawLine(x, top, x + 1, bottom, p);
                }

                if (groupIndex < rightDataCount) {
                    int top = (int) (waveformHeight * (1.0f - (GetFloatWaveformDataValue(m_rightWaveformData[groupIndex * 2 + 1]) + 1.0f) / 2.0f));
                    int bottom = (int) (waveformHeight * (1.0f - (GetFloatWaveformDataValue(m_rightWaveformData[groupIndex * 2]) + 1.0f) / 2.0f));
                    lineRect.set(x, top + waveformHeight, x + 1, bottom + waveformHeight);
                    canvas.drawRect(lineRect, p);
                }
            }
            drawFinished = true;
        }

        public void forceDraw() {
            drawFinished = false;
        }
    }
}
