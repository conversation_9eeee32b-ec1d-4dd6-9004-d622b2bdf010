package com.meishe.myvideo.ui.tools;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.text.TextUtils;

import com.meicam.sdk.NvsAVFileInfo;
import com.meicam.sdk.NvsIconGenerator;
import com.meicam.sdk.NvsStreamingContext;
import com.meicam.sdk.NvsVideoFrameRetriever;
import com.meishe.base.utils.ImageUtils;
import com.meishe.base.utils.LogUtils;
import com.meishe.engine.EditorEngine;
import com.meishe.engine.bean.MeicamVideoClip;
import com.meishe.myvideo.R;
import com.meishe.myvideo.ui.bean.LineRegionClip;

import static com.meicam.sdk.NvsAVFileInfo.AV_FILE_TYPE_IMAGE;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2020/7/2 14:25
 * 转换工具类
 * Conversion tool class
 */
public class ConvertUtil {

    public static Bitmap getBitmapFromClipInfo(Context context, MeicamVideoClip clipInfo) {
        Bitmap bitmap = null;
        String clipPath = clipInfo.getFilePath();
        if (TextUtils.isEmpty(clipPath)) {
            return null;
        }

        long timeStamp = clipInfo.getTrimIn();
        if (timeStamp < 0) {
            timeStamp = 0;
        }

        NvsStreamingContext streamingContext = NvsStreamingContext.getInstance();
        if (context == null) {
            return null;
        }
        int width = context.getResources().getDimensionPixelOffset(R.dimen.line_view_bitmap_width);
        int height = context.getResources().getDimensionPixelOffset(R.dimen.line_view_bitmap_height);
        NvsAVFileInfo fileInfo = streamingContext.getAVFileInfo(clipPath);
        if (fileInfo == null) {
            bitmap = BitmapFactory.decodeResource(context.getResources(), R.mipmap.lineview_bitmap_cover);
            bitmap = Bitmap.createScaledBitmap(bitmap, width, height, true);
            return bitmap;
        }
        if (fileInfo.getAVFileType() == AV_FILE_TYPE_IMAGE) {
            BitmapFactory.Options opt = new BitmapFactory.Options();
            opt.inJustDecodeBounds = true;
            ImageUtils.decodeBitmap(clipPath, opt, context);
            int picWidth = opt.outWidth;
            int picHeight = opt.outHeight;
            opt.inPreferredConfig = Bitmap.Config.RGB_565;
            opt.inSampleSize = getSampleSize(picWidth, picHeight, width, height);
            opt.inJustDecodeBounds = false;
            bitmap = ImageUtils.decodeBitmap(clipPath, opt, context);
            bitmap = centerSquareScaleBitmap(bitmap, width, height);
        } else {
            NvsVideoFrameRetriever videoFrameRetriever = streamingContext.createVideoFrameRetriever(clipPath);
            if (videoFrameRetriever != null) {
                bitmap = videoFrameRetriever.getFrameAtTime(timeStamp, NvsVideoFrameRetriever.VIDEO_FRAME_HEIGHT_GRADE_480);
                bitmap = centerSquareScaleBitmap(bitmap, width, height);
                videoFrameRetriever.release();
            }

        }
        return bitmap;
    }

    public static class BitmapResult{
        public Bitmap bitmap;
        public long task;

        public BitmapResult(Bitmap bitmap, long task) {
            this.bitmap = bitmap;
            this.task = task;
        }
    }

    public static BitmapResult getBitmapFromClipInfoAsync(Context context, LineRegionClip.BitmapDesc bitmapDesc) {
        Bitmap bitmap;
        String clipPath = bitmapDesc.filePath;
        if (TextUtils.isEmpty(clipPath)) {
            return null;
        }
        long timeStamp = bitmapDesc.trimIn;
        if (timeStamp < 0) {
            timeStamp = 0;
        }

        NvsStreamingContext streamingContext = NvsStreamingContext.getInstance();
        if (context == null) {
            return null;
        }
        int width = context.getResources().getDimensionPixelOffset(R.dimen.line_view_bitmap_width);
        int height = context.getResources().getDimensionPixelOffset(R.dimen.line_view_bitmap_height);
        NvsAVFileInfo fileInfo = streamingContext.getAVFileInfo(clipPath);
        if (fileInfo == null) {
            return null;
        }
        if (fileInfo.getAVFileType() == AV_FILE_TYPE_IMAGE) {
            BitmapFactory.Options opt = new BitmapFactory.Options();
            opt.inJustDecodeBounds = true;
            ImageUtils.decodeBitmap(clipPath, opt, context);
            int picWidth = opt.outWidth;
            int picHeight = opt.outHeight;
            opt.inPreferredConfig = Bitmap.Config.RGB_565;
            opt.inSampleSize = getSampleSize(picWidth, picHeight, width, height);
            opt.inJustDecodeBounds = false;
            bitmap = ImageUtils.decodeBitmap(clipPath, opt, context);
            bitmap = centerSquareScaleBitmap(bitmap, width, height);
            return new BitmapResult(bitmap, -1);
        } else {
            NvsIconGenerator iconGenerator = EditorEngine.getInstance().getIconGenerator();
            Bitmap iconFromCache = iconGenerator.getIconFromCache(clipPath, timeStamp, 0);
            if (iconFromCache != null) {
                return new BitmapResult(iconFromCache, -1);
            } else {
                long taskId = iconGenerator.getIcon(clipPath, timeStamp, 0);
                return new BitmapResult(null, taskId);
            }
        }
    }

    private static int getSampleSize(int picWidth, int picHeight, int width, int height) {
        int inSampleSize = 1;
        int wRatio = (int) Math.ceil(picWidth / width);
        int hRatio = (int) Math.ceil(picHeight / height);
        //如果超出指定大小，则缩小相应的比例传
        // If the specified size is exceeded, reduce the corresponding scale
        if (wRatio > 1 && hRatio > 1) {
            if (wRatio > hRatio) {
                inSampleSize = wRatio;
            } else {
                inSampleSize = hRatio;
            }
        }
        return inSampleSize;
    }

    private static Bitmap centerSquareScaleBitmap(Bitmap bitmap, int width, int height) {
        if (null == bitmap || width <= 0 || height <= 0) {
            return null;
        }

        Bitmap result = bitmap;
        int widthOrg = bitmap.getWidth();
        int heightOrg = bitmap.getHeight();

        if (widthOrg > width && heightOrg > height) {
            /*
             * 压缩到一个最小长度是edgeLength的bitmap
             * Compressed to a bitmap with a minimum length of edgeLength
             * */
            float wRemainder = widthOrg / (float) width;
            float hRemainder = heightOrg / (float) height;

            int scaledWidth;
            int scaledHeight;
            if (wRemainder > hRemainder) {
                scaledWidth = (int) (widthOrg / hRemainder);
                scaledHeight = (int) (heightOrg / hRemainder);
            } else {
                scaledWidth = (int) (widthOrg / wRemainder);
                scaledHeight = (int) (heightOrg / wRemainder);
            }

            Bitmap scaledBitmap;
            try {
                scaledBitmap = Bitmap.createScaledBitmap(bitmap, scaledWidth, scaledHeight, true);
            } catch (Exception e) {
                LogUtils.e("Exception: " + e);
                return null;
            }
            /*
             * 从图中截取正中间的部分。
             * Take the middle part from the figure.
             * */
            if (width > scaledWidth) {
                width = scaledWidth;
            }
            if (height > scaledHeight) {
                height = scaledHeight;
            }
            int xTopLeft = (scaledWidth - width) / 2;
            int yTopLeft = (scaledHeight - height) / 2;
            try {
                result = Bitmap.createBitmap(scaledBitmap, xTopLeft, yTopLeft, width, height);
                scaledBitmap.recycle();
            } catch (Exception e) {
                LogUtils.e("Exception2: " + e);
                return null;
            }
        }

        return result;
    }
}
