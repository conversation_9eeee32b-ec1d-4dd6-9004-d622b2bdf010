package com.meishe.myvideo.ui.trackview.bean;

import com.meishe.base.constants.Constants;
import com.meishe.engine.bean.CommonData;
import com.meishe.engine.bean.KeyFrameProcessor;
import com.meishe.engine.bean.MeicamCaptionClip;
import com.meishe.engine.command.ClipCommand;
import com.meishe.engine.command.KeyFrameHolderCommand;
import com.meishe.myvideo.R;
import com.meishe.myvideo.ui.bean.BaseUIClip;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2021/5/25 11:04
 * @Description :字幕代理 Caption proxy
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class CaptionProxy extends BaseUIClip {

    private MeicamCaptionClip mCaptionClip;

    public CaptionProxy(MeicamCaptionClip captionClip, int trackIndex) {
        super(CommonData.CLIP_CAPTION, trackIndex);
        this.mCaptionClip = captionClip;
        if (captionClip != null) {
            super.setInPoint(captionClip.getInPoint());
            super.setOutPoint(captionClip.getOutPoint());
        }
    }

    @Override
    public int getSubType() {
        if (mCaptionClip != null) {
            return mCaptionClip.getOperationType() == Constants.TYPE_AI_CAPTION ?
                    CommonData.TYPE_AI_CAPTION:CommonData.TYPE_COMMON_CAPTION;
        }
        return CommonData.TYPE_COMMON_CAPTION;
    }

    @Override
    public int getClipIndexInTrack() {
        if (mCaptionClip != null) {
            return mCaptionClip.getIndex();
        }
        return INVALID;
    }

    @Override
    public void setInPoint(long inPoint) {
        if (mCaptionClip != null) {
            ClipCommand.setInPoint(mCaptionClip, inPoint);
        }
        super.setInPoint(inPoint);
    }

    @Override
    public long getInPoint() {
        if (mCaptionClip != null) {
            return mCaptionClip.getInPoint();
        }
        return super.getInPoint();
    }

    @Override
    public void setOutPoint(long outPoint) {
        if (mCaptionClip != null) {
            mCaptionClip.setOutPoint(outPoint);
            ClipCommand.setOutPoint(mCaptionClip, outPoint);
            KeyFrameHolderCommand.updateKeyFrameControlPoints(mCaptionClip);
        }
        super.setOutPoint(outPoint);
    }

    @Override
    public long getOutPoint() {
        if (mCaptionClip != null) {
            return mCaptionClip.getOutPoint();
        }
        return super.getOutPoint();
    }

    @Override
    public String getIconFilePath() {
        return "";
    }

    @Override
    public String getFilePath() {
        return "";
    }

    @Override
    public KeyFrameProcessor keyFrameProcessor() {
        if (mCaptionClip != null) {
            return mCaptionClip.keyFrameProcessor();
        }
        return super.keyFrameProcessor();
    }

    @Override
    public float[] getRecordArray() {
        return new float[0];
    }

    @Override
    public void setDuration(long duration) {
    }

    @Override
    public long getDuration() {
        if (mCaptionClip != null) {
           return mCaptionClip.getOutPoint() - mCaptionClip.getInPoint();
        }
        return super.getOutPoint() - super.getInPoint();
    }

    @Override
    public long getFadeIn() {
        return 0;
    }

    @Override
    public long getFadeOut() {
        return 0;
    }

    @Override
    public String getDisplayName() {
        if (mCaptionClip != null) {
            return mCaptionClip.getText();
        }
        return "";
    }

    @Override
    public boolean canExceedLength() {
        return (getSubType() != CommonData.TYPE_AI_CAPTION);
    }

    @Override
    public boolean canDrag() {
        return (getSubType() != CommonData.TYPE_AI_CAPTION);
    }

    @Override
    public int getBackGroundColor() {
        return (getSubType() == CommonData.TYPE_AI_CAPTION) ? R.color.track_background_color_ai_caption
                : R.color.track_background_color_caption;
    }
}
