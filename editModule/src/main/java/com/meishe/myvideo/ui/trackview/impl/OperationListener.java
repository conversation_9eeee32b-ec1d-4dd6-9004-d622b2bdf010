package com.meishe.myvideo.ui.trackview.impl;


import com.meishe.myvideo.ui.bean.ITrackClip;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> LiHangZhou
 * @CreateDate :2021/3/9 11:23
 * @Description :操作面板的事件监听 Event listening for the action panel
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public abstract class OperationListener {
    /**
     * 时间轴滚动
     * On time roller scroll
     *
     * @param timestamp scroll timestamp 滚动到的时间戳
     * @param fromUser  true from user scroll ,false not .true 来自用户滚动，false 不是
     * @param newX      the new scroll x ,滚动到的x坐标
     * @param oldX      the old scroll x ,上次滚动到的x坐标
     */
    public void onTimeScroll(long timestamp, boolean fromUser, int newX, int oldX) {
    }

    /**
     * 移动缩略图片段
     * Move thumbnail clip
     *
     * @param from from position 移动开始的位置
     * @param to   to position 移动结束的位置
     */
    public boolean onThumbnailMove(int from, int to) {
        return true;
    }


    public void onSelectedChanged(ITrackClip trackClip){}

}
