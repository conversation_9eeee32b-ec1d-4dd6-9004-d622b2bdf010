package com.meishe.myvideo.ui.trackview;

import android.content.Context;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.meishe.base.utils.FormatUtils;
import com.meishe.myvideo.R;
import com.meishe.myvideo.ui.bean.AnimationInfo;
import com.meishe.myvideo.ui.bean.KeyFrameInfo;
import com.meishe.myvideo.ui.bean.SpeedInfo;
import com.meishe.myvideo.util.PixelPerMicrosecondUtil;

import java.util.Map;


/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> LiHangZhou
 * @CreateDate :2021/4/12 16:57
 * @Description :主轨道缩略图序列封面 Main track thumbnail sequence cover
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class ThumbnailCoverView extends FrameLayout {
    private AnimationInfo mAnimationInfo;
    private KeyFrameInfo mKeyFrameInfo;
    private View mAnimationCoverViewIn;
    private View mAnimationCoverViewOut;
    private FrameLayout mFlKeyFrameContainer;
    private LinearLayout mLlSpeedContainer;
    private TextView mTvSpeed;
    private int mSelectedLineHeight;
    private int mKeyFrameViewWidth;
    private int mKeyFrameViewHeight;
    private ImageView mIvSelectedKeyFrame;
    private boolean mSelected;
    private View mFaceIcon;
    private View mVolumeIcon;

    public ThumbnailCoverView(@NonNull Context context) {
        this(context, null);
    }

    public ThumbnailCoverView(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public ThumbnailCoverView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView();
    }

    private void initView() {
        View view = LayoutInflater.from(getContext()).inflate(R.layout.view_thumbnail_cover, this);
        mAnimationCoverViewIn = view.findViewById(R.id.v_empty_animation_in);
        mAnimationCoverViewOut = view.findViewById(R.id.v_empty_animation_out);
        mFlKeyFrameContainer = view.findViewById(R.id.fl_key_frame_container);
        mFaceIcon = view.findViewById(R.id.iv_face_icon);
        mVolumeIcon = view.findViewById(R.id.iv_volume_icon);
        mLlSpeedContainer = view.findViewById(R.id.ll_speed_container);
        mTvSpeed = view.findViewById(R.id.tv_speed);
        mSelectedLineHeight = (int) getResources().getDimension(R.dimen.editor_timeline_span_line_height);
        mKeyFrameViewWidth = (int) getResources().getDimension(R.dimen.dp_px_54);
        mKeyFrameViewHeight = (int) getResources().getDimension(R.dimen.dp_px_66);
    }

    /**
     * 设置变速信息是否可用,可用则显示，不可用不显示
     * Enable speed info
     *
     * @param enable true enable ,false not
     */
    public void enableSpeed(boolean enable) {
        CharSequence text = mTvSpeed.getText();
        enable = enable && text != null && text.length() > 0;
        mLlSpeedContainer.setVisibility(enable ? VISIBLE : GONE);
    }

    /**
     * 设置人脸道具是否可用,可用则显示，不可用不显示
     * Enable face prop.
     *
     * @param enable the enable true enable ,false not
     */
    public void enableFaceProp(boolean enable) {
        mFaceIcon.setVisibility(enable ? VISIBLE : GONE);
    }

    /**
     * 设置音量是否可用,可用则显示，不可用不显示
     * Enable volume.
     *
     * @param enable the enable true enable ,false not
     */
    public void enableVolume(boolean enable) {
        mVolumeIcon.setVisibility(enable ? VISIBLE : GONE);
    }

    /**
     * 设置动画信息是否可用，可用则显示，不可用不显示
     * Enable animation info
     *
     * @param enable true enable ,false not
     */
    public void enableAnimation(boolean enable) {
        mAnimationCoverViewIn.setVisibility(enable ? VISIBLE : INVISIBLE);
        mAnimationCoverViewOut.setVisibility(enable ? VISIBLE : INVISIBLE);
    }

    /**
     * 设置关键帧信息是否可用，可用则显示，不可用不显示
     * Enable Key frame info
     *
     * @param enable true enable ,false not
     */
    public void enableKeyFrame(boolean enable) {
        mFlKeyFrameContainer.setVisibility(enable ? VISIBLE : INVISIBLE);
    }

    /**
     * 设置变速信息
     * Set speed info
     *
     * @param speedInfo the speed info
     */
    public void setSpeedInfo(SpeedInfo speedInfo) {
        if (speedInfo.getSpeed() != 1) {
            //mLlSpeedContainer.setVisibility(View.VISIBLE);
            if (TextUtils.isEmpty(speedInfo.getSpeedName())) {
                mTvSpeed.setText(getSpeedText(speedInfo));
            } else {
                mTvSpeed.setText(speedInfo.getSpeedName());
            }
        } else {
            mTvSpeed.setText("");
            mLlSpeedContainer.setVisibility(View.GONE);
        }
    }

    /**
     * 设置动画信息
     * Set animation info
     *
     * @param animationInfo the animation info
     */
    public void setAnimationInfo(AnimationInfo animationInfo) {
        mAnimationInfo = animationInfo;
        int leftMargin = 0;
        int width = 0;
        int height = 0;
        FrameLayout.LayoutParams layoutParams = (FrameLayout.LayoutParams) mAnimationCoverViewIn.getLayoutParams();
        if (animationInfo != null && !animationInfo.isEmpty()) {
            leftMargin = PixelPerMicrosecondUtil.durationToLength(animationInfo.getInPoint());
            width = PixelPerMicrosecondUtil.durationToLength(animationInfo.getOutPoint() - animationInfo.getInPoint());
            height = LayoutParams.MATCH_PARENT;
            if (mSelected) {
                //存在入场动画或者出场动画
                // There is entrance animation or exit animation
                if (animationInfo.hasGroupOrInAnimation()) {
//                    if (animationInfo.isAnimationIn()) {
//                        leftMargin = mSelectedLineHeight + leftMargin;
//                        width = width - mSelectedLineHeight;
//                    } else {
                    leftMargin = mSelectedLineHeight + leftMargin;
                    width = width - 2 * mSelectedLineHeight;
//                    }
                }
                height = getHeight() - 2 * mSelectedLineHeight;
                if (width < 0) {
                    width = 0;
                }
            }
        }
        if (layoutParams.leftMargin != leftMargin || layoutParams.width != width) {
            layoutParams.width = width;
            layoutParams.leftMargin = leftMargin;
            layoutParams.height = height;
            mAnimationCoverViewIn.setLayoutParams(layoutParams);
        }

        //出动画 Out animate
        leftMargin = 0;
        width = 0;
        height = 0;
        FrameLayout.LayoutParams layoutParamsOut = (FrameLayout.LayoutParams) mAnimationCoverViewOut.getLayoutParams();
        if (animationInfo != null && !animationInfo.isEmpty()) {
            leftMargin = PixelPerMicrosecondUtil.durationToLength(animationInfo.getInPoint2());
            width = PixelPerMicrosecondUtil.durationToLength(animationInfo.getOutPoint2() - animationInfo.getInPoint2());
            height = LayoutParams.MATCH_PARENT;
            if (mSelected) {
                if (animationInfo.hasOutAnimation()) {
                    width = width - 2 * mSelectedLineHeight;
                }
                height = getHeight() - 2 * mSelectedLineHeight;
                if (width < 0) {
                    width = 0;
                }
            }
        }
        if (layoutParamsOut.leftMargin != leftMargin || layoutParamsOut.width != width) {
            layoutParamsOut.width = width;
            layoutParamsOut.leftMargin = leftMargin;
            layoutParamsOut.height = height;
            mAnimationCoverViewOut.setLayoutParams(layoutParamsOut);
        }
    }

    /**
     * 设置关键帧信息
     * Set key frame info
     *
     * @param keyFrameInfo the key frame info
     */
    public void setKeyFrameInfo(KeyFrameInfo keyFrameInfo, long selectedPoint) {
        mKeyFrameInfo = keyFrameInfo;
        Map<Long, KeyFrameInfo.Info> keyFrameMap = keyFrameInfo.getKeyFrameMap();
        mFlKeyFrameContainer.removeAllViews();
        for (Map.Entry<Long, KeyFrameInfo.Info> entry : keyFrameMap.entrySet()) {
            KeyFrameInfo.Info info = entry.getValue();
            if (info.isValid()) {
                addKeyFrameView(info.getAtTime(), selectedPoint == info.getAtTime());
            }
        }
    }

    /**
     * 检测是否需要选中关键帧视图
     * Checks if the keyframe view needs to be selected
     *
     * @param clipInPoint the clip int point 片段的入点
     * @param timestamp   the timestamp 时间戳
     */
    public void checkSelectedKeyFrame(long clipInPoint, long timestamp) {
        int childCount = mFlKeyFrameContainer.getChildCount();
        long selectedPoint = mKeyFrameInfo.getSelectedPoint();
        long minTime = Long.MAX_VALUE;
        for (int i = 0; i < childCount; i++) {
            ImageView keyFrameView = (ImageView) mFlKeyFrameContainer.getChildAt(i);
            Long point = (Long) keyFrameView.getTag();
            int selectedX = PixelPerMicrosecondUtil.durationToLength(timestamp);
            int viewX = PixelPerMicrosecondUtil.durationToLength(point + clipInPoint);
            if (selectedX >= viewX - mKeyFrameViewWidth / 2 && selectedX <= viewX + mKeyFrameViewWidth / 2) {
                minTime = Math.min(selectedPoint - timestamp, point - timestamp);
                if (selectedPoint == -1 || selectedPoint != minTime) {
                    if (mIvSelectedKeyFrame != null) {
                        mIvSelectedKeyFrame.setImageResource(R.mipmap.key_frame_icon);
                    }
                    mIvSelectedKeyFrame = keyFrameView;
                    keyFrameView.setImageResource(R.mipmap.key_frame_icon_selected);
                    selectedPoint = point;
                }
            } else {
                keyFrameView.setImageResource(R.mipmap.key_frame_icon);
            }
        }
        if (minTime == Long.MAX_VALUE) {
            selectedPoint = -1;
        }
        mKeyFrameInfo.setSelectedPoint(selectedPoint);
    }

    /**
     * 添加关键帧
     * Add key frame
     *
     * @param inPoint the in point 入点
     */
    public void addKeyFrame(long inPoint, boolean selected) {
        if (mKeyFrameInfo.hasKeyFrame(inPoint)) {
            return;
        }
        addKeyFrameView(inPoint, selected);
        mKeyFrameInfo.addKeyFrame(inPoint);
    }

    /**
     * 添加关键帧视图
     * Add key frame view
     *
     * @param inPoint the in point 入点
     */
    private void addKeyFrameView(long inPoint, boolean selected) {
        ImageView keyFrameView = new ImageView(getContext());
        keyFrameView.setTag(inPoint);
        if (selected) {
            mKeyFrameInfo.setSelectedPoint(inPoint);
            keyFrameView.setImageResource(R.mipmap.key_frame_icon_selected);
        } else {
            keyFrameView.setImageResource(R.mipmap.key_frame_icon);
        }
        LayoutParams layoutParams = new LayoutParams(mKeyFrameViewWidth, mKeyFrameViewHeight);
        layoutParams.leftMargin = PixelPerMicrosecondUtil.durationToLength(inPoint) - mKeyFrameViewWidth / 2;
        layoutParams.gravity = Gravity.CENTER_VERTICAL;
        mFlKeyFrameContainer.addView(keyFrameView, layoutParams);
    }

    /**
     * 删除关键帧视图
     * Delete key frame view
     *
     * @param inPoint     the in point 入点
     * @param clipInPoint the clip int point 片段的入点
     */
    public void deleteKeyFrame(long clipInPoint, long inPoint) {
        int childCount = mFlKeyFrameContainer.getChildCount();
        for (int i = 0; i < childCount; i++) {
            ImageView keyFrameView = (ImageView) mFlKeyFrameContainer.getChildAt(i);
            Long point = (Long) keyFrameView.getTag();
            if (point == inPoint) {
                mFlKeyFrameContainer.removeViewAt(i);
                mKeyFrameInfo.deleteKeyFrame(inPoint);
                if (mKeyFrameInfo.getSelectedPoint() == inPoint) {
                    mKeyFrameInfo.setSelectedPoint(-1);
                }
                checkSelectedKeyFrame(clipInPoint, clipInPoint + inPoint);
                break;
            }
        }
    }

    /**
     * 设置视图宽度
     * Set this view width
     *
     * @param width the width
     */
    public void setWidth(int width) {
        ViewGroup.LayoutParams layoutParams = getLayoutParams();
        if (layoutParams.width != width) {
            layoutParams.width = width;
            setLayoutParams(layoutParams);
        }
    }

    /**
     * 选中
     * Selected
     */
    public void selected() {
        mSelected = true;
        setBackgroundResource(R.drawable.editor_drawable_corner_white);
        setAnimationInfo(mAnimationInfo);
    }

    /**
     * 取消选中
     * Unselected
     */
    public void unselected() {
        mSelected = false;
        setBackgroundResource(0);
        setAnimationInfo(mAnimationInfo);
    }

    /**
     * 获取加速值
     * Get speed text
     */
    private String getSpeedText(SpeedInfo speedInfo) {
        if (speedInfo != null) {
            return FormatUtils.objectFormat2String(speedInfo.getSpeed()) + "x";
        }
        return "";
    }
}
