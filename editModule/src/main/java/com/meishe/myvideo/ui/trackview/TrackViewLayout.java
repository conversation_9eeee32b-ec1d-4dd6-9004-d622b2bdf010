package com.meishe.myvideo.ui.trackview;

import android.content.Context;
import android.graphics.Rect;
import android.os.Vibrator;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.ViewGroup;
import android.view.animation.TranslateAnimation;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.core.view.ViewCompat;

import com.meishe.base.constants.Constants;
import com.meishe.base.interfaces.OnScrollViewListener;
import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.LogUtils;
import com.meishe.base.utils.ScreenUtils;
import com.meishe.base.view.MYHorizontalScrollView;
import com.meishe.base.view.MYScrollView;
import com.meishe.engine.EditorEngine;
import com.meishe.engine.bean.CommonData;
import com.meishe.engine.bean.MeicamAudioClip;
import com.meishe.engine.bean.MeicamAudioTrack;
import com.meishe.engine.bean.MeicamTimeline;
import com.meishe.engine.bean.MeicamVideoClip;
import com.meishe.engine.bean.MeicamVideoTrack;
import com.meishe.engine.view.MultiThumbnailSequenceView;
import com.meishe.myvideo.R;
import com.meishe.myvideo.audio.AudioWaveView;
import com.meishe.myvideo.ui.bean.BaseUIClip;
import com.meishe.myvideo.util.PixelPerMicrosecondUtil;

import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.meishe.base.constants.Constants.TRACK_INDEX_MAIN;
import static com.meishe.myvideo.ui.trackview.BaseItemView.BASE_ITEM_MARGIN;

/**
 * Created by CaoZhiChao on 2020/6/17 16:07
 * 轨道布局控件
 * Track control layout
 */
public class TrackViewLayout extends RelativeLayout implements HandView.OnDownToGetNextClip, PixelPerMicrosecondUtil.PixelPerMicrosecondChangeListener {
    private static final String TAG = "TrackViewLayout";
    private Context mContext;
    private MYHorizontalScrollView mHorizontalScrollView;
    private MYScrollView mTrackViewVerticalScroll;
    private FrameLayout mTrackViewVerticalParent;
    private RelativeLayout track_view_horizontal_layout;
    private HashMap<Integer, List<BaseUIClip>> mIntegerListHashMap = new HashMap<>();
    private long mTimelineDuration;

    private int mTrackHeight = 0;
    private int mTrackViewMarginTop = 0;

    // 记录手指上次触摸的坐标 Record the coordinates of the last touch of the finger
    private float mLastPointX;
    private float mLastPointY;

    // 记录被拖拽之前 child 的位置坐标 Records the position coordinates of the child before it is dragged
    private float mDragViewOrigX;
    private float mDragViewOrigY;
    // 用于轨道绑定使用 Used for orbital binding
    private float mDragTotalY;
    //用于识别最小的滑动距离 Used to identify the minimum slip distance
    private int mSlop;

    // 用于标识正在被拖拽的 child，为 null 时表明没有 child 被拖拽
    //Used to identify the child being dragged, or null to indicate that no child is being dragged
    private BaseItemView mDragView;
    private BaseItemView mNewDragView;
    private State mCurrentState;
    private boolean mIsDragSuccess = true;
    private long mDownTime = 0;
    private LinearLayout linearScroll;

    /**
     * 是否手动拖动到最底部
     * Whether to manually drag to the end
     */
    private boolean mIsDragToEnd;
    /**
     * 是否可以垂直滚动
     * Whether the view can scroll vertically.
     */
    private boolean mCanVerticalScroll;
    private WaveformHelper mWaveformHelper;

    @Override
    public void onPixelPerMicrosecondChange(double pixelPerMicrosecond, float scale) {
        MeicamTimeline meicamTimeline = EditorEngine.getInstance().getCurrentTimeline();
        if (meicamTimeline != null) {
            if (meicamTimeline.isAddTitleTheme()) {
                int offset = PixelPerMicrosecondUtil.durationToLength(meicamTimeline.getTitleThemeDuration());
                initWidth(mTimelineDuration, offset);
            } else {
                initWidth(mTimelineDuration);
            }
        }


        for (int i = mTrackViewVerticalParent.getChildCount() - 1; i >= 0; i--) {
            BaseItemView itemView = (BaseItemView) mTrackViewVerticalParent.getChildAt(i);
            BaseUIClip baseUIClip = itemView.getBaseUIClip();
            FrameLayout.LayoutParams layoutParams2 = new FrameLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
            layoutParams2.leftMargin = getChildTopMarginFromDuration(baseUIClip.getInPoint());
            layoutParams2.topMargin = mTrackHeight * baseUIClip.getTrackIndex() + mTrackViewMarginTop;
            itemView.setLayoutParams(layoutParams2);
            itemView.updateKeyFramePosition();
            MultiThumbnailSequenceView multiThumbnailSequenceView = itemView.getNvsMultiThumbnailSequenceView();
            if (multiThumbnailSequenceView != null) {
                multiThumbnailSequenceView.setPixelPerMicrosecond(pixelPerMicrosecond);
            }
        }
        mWaveformHelper.notifyScaleChanged(scale);

        //缩放时，也要修改HandView的宽度 When you scale, you also change the width of the HandView
        if (mDragView == null) {
            return;
        }
        HandView handView = mDragView.getHandView();
        BaseUIClip baseUIClip = mDragView.getBaseUIClip();
        if (handView == null || baseUIClip == null) {
            return;
        }
        handView.setBaseUIClip(baseUIClip);
    }

    /**
     * 根据view显示判断子轨是否显示
     * Is show track view boolean.
     *
     * @return the boolean
     */
    public boolean isShowTrackView() {
        return !mAddVoice.isShown() && mTrackViewVerticalParent.getChildCount()>0;
    }

    /**
     * 子轨展示的是否是画中画
     * Is show pip track view boolean.
     *
     * @return the boolean
     */
    public boolean isShowPipTrackView() {
        return isShowTrackView() && (CommonData.CLIP_IMAGE.equals(trackType) || CommonData.CLIP_VIDEO.equals(trackType));
    }

    // 状态分别空闲、拖拽两种 The states are idle and drag and drop

    private enum State {
        /**
         * Idle state.
         * 空闲状态
         */
        IDLE,
        /**
         * Dragging state.
         * 拖动状态
         */
        DRAGGING
    }

    /**
     * 最大轨道数常量
     * The max track count
     */
    private static final int MAX_TRACK_COUNT= 4;
    private boolean mLongPressEnd = false;
    private int mStartPadding;
    private OnTrackViewScrollListener mOnTrackViewScrollListener;
    private OnTrackViewDragListener mOnTrackViewDragListener;
    private HandView.OnHandChangeListener mOnHandChangeListener;
    private RelativeLayout mAddVoice;
    private ImageView mImageAudio;
    private TextView mTvMusicDesc;
    private RelativeLayout realAudio;
    private int totalMoveX = -1;
    private int lengthTimelineLeft = -1;
    private int lengthTimelineRight = -1;
    private long mCurrTimelinePosition = -1;
    private long mLongPressBaseUIClipInpoint;
    private long mLongPressBaseUIClipOutpoint;
    private BaseUIClip mLongPressBaseUIClip;
    //刚开始按下时，距离左边的距离 The distance to the left when you first press down
    private int mLongPressStartMarginLeft;
    /**
     * 判断是否已吸附
     * Determine if adsorption has occurred
     */
    private boolean hasNearByBaseItem = false;
    private int nearByIndexBaseItem = -1;

    private Vibrator vibrator;
    /**
     * 判断是否靠近某一个ItemView或者基准线
     * Determine if it is near an ItemView or a reference line

     */
    private boolean hasNearByBaseLine = false;


    private boolean hasNearByVideoLeft = false;
    private boolean hasNearByVideoRight = false;
    private int nearByVideoIndexLeft = -1;
    private int nearByVideoIndexRight = -1;

    /**
     * 最大的轨道数
     */
    private int mMaxTrackCount = MAX_TRACK_COUNT;

    /**
     * 是否可以扩充轨道数
     * Whether the number of tracks can be expanded
     */
    private boolean mCanIncreaseTrackCount = true;

    /**
     * Sets can increase track count.
     * 设置是否可以增加轨道数量
     * @param canIncreaseTrackCount the can increase track count
     */
    public void setCanIncreaseTrackCount(boolean canIncreaseTrackCount) {
        this.mCanIncreaseTrackCount = canIncreaseTrackCount;
    }

    /**
     * Increase max track count.
     * 增加最大轨道数
     */
    public void increaseMaxTrackCount(){
        mMaxTrackCount ++;
    }

    /**
     * Instantiates a new Track view layout.
     *
     * @param context the context
     */
    public TrackViewLayout(Context context) {
        super(context);
        init(context, null, 0);
    }

    /**
     * Instantiates a new Track view layout.
     *
     * @param context the context
     * @param attrs   the attrs
     */
    public TrackViewLayout(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context, attrs, 0);
    }

    /**
     * Instantiates a new Track view layout.
     *
     * @param context      the context
     * @param attrs        the attrs
     * @param defStyleAttr the def style attr
     */
    public TrackViewLayout(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs, defStyleAttr);
    }

    private void init(Context context, AttributeSet attrs, int defStyleAttr) {
        vibrator = (Vibrator) context.getSystemService(Context.VIBRATOR_SERVICE);
        PixelPerMicrosecondUtil.addPixelPerMicrosecondChangeListener(this);
        mContext = context;
        mStartPadding = ScreenUtils.getScreenWidth() / 2;
        mTrackHeight = mContext.getResources().getDimensionPixelOffset(R.dimen.track_view_height);
        mTrackViewMarginTop = mContext.getResources().getDimensionPixelOffset(R.dimen.track_view_real_margin_top);
        LayoutInflater inflater = LayoutInflater.from(mContext);
        View parentView = inflater.inflate(R.layout.track_view_layout, this);
        mHorizontalScrollView = parentView.findViewById(R.id.track_view_horizontal_scroll);
        mTrackViewVerticalScroll = parentView.findViewById(R.id.track_view_vertical_scroll);
        mTrackViewVerticalParent = parentView.findViewById(R.id.track_view_vertical_parent);
        linearScroll = parentView.findViewById(R.id.linear_scroll);
        track_view_horizontal_layout = parentView.findViewById(R.id.track_view_horizontal_layout);
        mImageAudio = parentView.findViewById(R.id.tv_add);
        mTvMusicDesc = parentView.findViewById(R.id.tv_music_desc);
        mAddVoice = parentView.findViewById(R.id.ll_add_voice);
        realAudio = parentView.findViewById(R.id.real_audio);
        mWaveformHelper = new WaveformHelper();
        mTrackViewVerticalScroll.setOnTouchListener(new OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                return !mCanVerticalScroll;
            }
        });
        mHorizontalScrollView.setOnScrollViewListener(new OnScrollViewListener() {
            @Override
            public void onScrollChanged(int l, int t, int oldl, int oldt) {
                if (mOnTrackViewScrollListener != null) {
                    mOnTrackViewScrollListener.scrollX(l, oldl, isScrollFromMainTrack());
                }
                mWaveformHelper.notifyScrollChanged(l, oldl);
              /*  int childCount = mTrackViewVerticalParent.getChildCount();
                for (int index = childCount; index > 0; index--) {
                    View childAt = mTrackViewVerticalParent.getChildAt(index);
                    if (childAt instanceof BaseItemView) {
                        BaseItemView baseItemView = (BaseItemView) childAt;
                    }
                }*/
            }
        });
        mAddVoice.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mOnTrackViewScrollListener != null) {
                    mOnTrackViewScrollListener.clickToMusicMenu();
                }
            }
        });
        mSlop = ViewConfiguration.get(getContext()).getScaledTouchSlop();
    }

    /**
     * Init width.
     * 初始化宽度
     *
     * @param timelineDuration the timeline duration
     */

    public void initWidth(long timelineDuration) {
        initWidth(timelineDuration, 0);
    }

    /**
     * Init width.
     * 初始化宽度
     *
     * @param timelineDuration the timeline duration
     * @param leftMargin       the left margin
     */

    public void initWidth(long timelineDuration, int leftMargin) {
        mTimelineDuration = timelineDuration;
        LayoutParams layoutParams = (LayoutParams) mTrackViewVerticalParent.getLayoutParams();
        layoutParams.width = PixelPerMicrosecondUtil.durationToLength(timelineDuration) + ScreenUtils.getScreenWidth();
        mTrackViewVerticalParent.setLayoutParams(layoutParams);

        LayoutParams layoutParams2 = (LayoutParams) mAddVoice.getLayoutParams();
        layoutParams2.leftMargin = mStartPadding + leftMargin;
        //防止因视频切割过短，导致“添加音频”看不到 Prevent the "add audio" from not being seen because the video cut is too short
        int length = PixelPerMicrosecondUtil.durationToLength(timelineDuration);
        if (length < getResources().getDimension(R.dimen.add_voice_width)) {
            layoutParams2.width = (int) getResources().getDimension(R.dimen.add_voice_width);
        } else {
            layoutParams2.width = length - leftMargin;
        }
        mAddVoice.setLayoutParams(layoutParams2);
        Log.d(TAG, "  initWidth  height = " + mTrackViewVerticalParent.getHeight());
    }

    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);

    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
    }

    private int mLastDragTrackIndex = -1;

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        mCanVerticalScroll = true;
        int action = event.getAction();
        // down事件被interrupt拦截，在那里处理。
        // The down event is intercepted by interrupt and processed there.
        if (action == MotionEvent.ACTION_MOVE) {
            int deltaX = (int) (event.getX() - mLastPointX);
            int deltaY = (int) (event.getY() - mLastPointY);
            if (mCurrentState == State.DRAGGING && mDragView != null
                    && (Math.abs(deltaX) > mSlop || Math.abs(deltaY) > mSlop)) {
                BaseUIClip baseUIClip = mDragView.getBaseUIClip();
                if (baseUIClip != null && !baseUIClip.canDrag()) {
                    return true;
                }
                /*计算要移动到的下一个轨道的索引
                Calculate the index of the next track to move to*/
                int nextTrack = (int) (event.getY() + mTrackViewVerticalScroll.getScrollY() - mTrackViewMarginTop) / (mTrackHeight);
                //LogUtils.d("currentTrack=" + currentTrack + ",mDragTrackYIndex=" + mDragTrackYIndex + ",Y=" + moveY + ",scrollY=" + mTrackViewVerticalScroll.getScrollY() + ",h=" + getHeight());
                if (nextTrack >= 0 && nextTrack < mMaxTrackCount && nextTrack != mLastDragTrackIndex) {
                    hasNearByBaseItem = false;
                    hasNearByVideoLeft = false;
                    hasNearByVideoRight = false;
                    nearByIndexBaseItem = -1;
                    nearByVideoIndexLeft = -1;
                    nearByVideoIndexRight = -1;
                    /*视图在新轨道的top值
                    * The top value of the view on the new track
                    * */
                    int newTop = mDragView.getTop() + (nextTrack - mLastDragTrackIndex) * mTrackHeight;
                    /*top差值，如果小于零，说明往上移动，视图从顶部开始被遮盖
                    * The top difference, if less than zero, means that the view is covered from the top
                    * */
                    int deltaTop = newTop - mTrackViewVerticalScroll.getScrollY();
                    /*bottom差值，如果大于零，说明往下移动，视图从底部开始被遮盖
                    * Bottom difference. If it is greater than zero,
                    * it means moving down and the view is covered from the bottom
                    * */
                    int deltaBottom = deltaTop + mTrackHeight - getHeight();
                    //LogUtils.d("may be scroll，top=" + top + ",newTop=" + newTop + ",bottom=" + bottom);
                    if (deltaTop <= 0) {
                        /*往上移，滚动到指定轨道的顶部使视图能显示完全的位置
                        * Move up and scroll to the top of the specified track so that
                        * the view can display the full position
                        * */
                        mTrackViewVerticalScroll.scrollTo(mTrackViewVerticalScroll.getScrollX(), newTop - mTrackViewMarginTop);
                    } else if (deltaBottom >= 0) {
                        /*往下移，滚动到指定轨道的底部使视图能显示完全的位置
                        * Move down and scroll to the bottom of the specified track so that
                        * the view can display the full position
                        * */
                        mTrackViewVerticalScroll.scrollBy(mTrackViewVerticalScroll.getScrollX(), deltaBottom + mTrackViewMarginTop);
                    }
                    /*将视图移动到新轨道
                    * Move the view to the new track
                    * */
                    ViewCompat.offsetTopAndBottom(mDragView, (nextTrack - mLastDragTrackIndex) * mTrackHeight);
                    mLastDragTrackIndex = nextTrack;
                } else {
                    if (mOnTrackViewDragListener != null) {
                        mIsDragToEnd = nextTrack >= mMaxTrackCount;
                        if (mIsDragToEnd) {
                            vibrator.vibrate(30);
                        }
                        mOnTrackViewDragListener.dragToEnd(mIsDragToEnd);
                    }
                }
                MeicamTimeline meicamTimeline = EditorEngine.getInstance().getCurrentTimeline();
                long titleThemeDuration = 0L;
                if (meicamTimeline != null) {
                    titleThemeDuration = meicamTimeline.getTitleThemeDuration();
                }
                if (titleThemeDuration > 0) {
                    int childTopMarginFromDuration = getChildTopMarginFromDuration(titleThemeDuration);
                    //增加判断，不允许view移出画面 Add judgment, do not allow the view to move out of the screen
                    if (mDragView.getLeft() < childTopMarginFromDuration) {
                        mDragView.setLeft(childTopMarginFromDuration);
                    }
                } else {
                    //增加判断，不允许view移出画面 Add judgment, do not allow the view to move out of the screen
                    if (mDragView.getLeft() < mStartPadding) {
                        mDragView.setLeft(mStartPadding);
                    }
                }

                if ((hasNearByBaseItem || hasNearByVideoLeft || hasNearByVideoRight || hasNearByBaseLine) && Math.abs(deltaX) < Constants.SLOPX) {
                    return false;
                }
                totalMoveX = mDragView.getLeft() - mLongPressStartMarginLeft;
                deltaX = baseItemAdsorb(deltaX);
                lastMoveX = totalMoveX;
                //如果符合条件则对被拖拽的 child 进行位置移动 The position of the child being dragged is moved if conditions are met
                if (deltaX + mDragView.getLeft() < mStartPadding) {
                    deltaX = mDragView.getLeft() - mStartPadding;

                } /*else if (deltaX + mDragView.getRight() > (PixelPerMicrosecondUtil.durationToLength(mTimelineDuration) + mStartPadding)) {
                    deltaX = (PixelPerMicrosecondUtil.durationToLength(mTimelineDuration) + mStartPadding) - mDragView.getRight();
                    LogUtils.d("deltaX===" + deltaX + "===mTimelineDuration===" + PixelPerMicrosecondUtil.durationToLength(mTimelineDuration) + "mStartPadding===" + mStartPadding + "mDragView.getRight()===" + mDragView.getRight());
                }
                //不再限制outPoint不可超过timeline时长 No longer limit outPoint to exceed the timeline time
                */
                ViewCompat.offsetLeftAndRight(mDragView, deltaX);
                mLastPointX = event.getX();
                mLastPointY = event.getY();
            }
        } else if (action == MotionEvent.ACTION_CANCEL || action == MotionEvent.ACTION_UP) {
            mDragTotalY = 0;
            mLongPressEnd = false;
            if (mCurrentState == State.DRAGGING) {
                // 标记状态为空闲，并将 mDragView 变量置为 null Mark the state as free and set the mDragView variable to null
                if (mDragView != null) {
                    int newTrackIndex = mDragView.getTop() / mTrackHeight;
                    long newInPoint = PixelPerMicrosecondUtil.lengthToDuration(mDragView.getLeft() - mStartPadding);
                    //一下方式为了解决，存在某些机型，newOutPoint > mTimelineDuration情况，newInPoint值计算有误差，导致不能移动到末尾
                    // The following method is used to solve the problem. For some models, there is an error in the calculation of the newInPoint value, resulting in the inability to move to the end.
                    BaseUIClip baseUIClip = mDragView.getBaseUIClip();
                    long during = baseUIClip.getTrimOut() - baseUIClip.getTrimIn();
                    long newOutPoint = (long) (newInPoint + during / baseUIClip.getSpeed());
                  /*  if (newOutPoint > mTimelineDuration) {
                        newOutPoint = mTimelineDuration ;
                        newInPoint = newOutPoint - during ;
                    }
                    //不再限制outPoint不可超过timeline时长No longer limit outPoint to exceed the timeline time
                    */
                    mDragView.setAlpha(1);
                    if (mIsDragToEnd && mCanIncreaseTrackCount) {
                        newTrackIndex ++;
                    }
                    mIsDragSuccess = checkCanDragToHere(baseUIClip, newTrackIndex, newInPoint, newOutPoint);
                    if (mIsDragSuccess) {
                        baseUIClip = (BaseUIClip) baseUIClip.clone();
                        mDragView = null;
                        if (mOnTrackViewDragListener != null) {
                            mOnTrackViewDragListener.dragEnd(baseUIClip, newTrackIndex, newInPoint, mIsDragToEnd);
                            mOnTrackViewDragListener.dragToEnd(false);
                        }
                    } else {
                        FrameLayout.LayoutParams dragViewLayoutParams = (FrameLayout.LayoutParams) mDragView.getLayoutParams();
                        dragViewLayoutParams.topMargin = (int) mDragViewOrigY;
                        dragViewLayoutParams.leftMargin = (int) mDragViewOrigX;
                        mDragView.setLayoutParams(dragViewLayoutParams);
                        mDragView = null;
                        if (mOnTrackViewDragListener != null) {
                            mOnTrackViewDragListener.dragToEnd(false);
                        }
                    }
                    mIsDragToEnd = false;
                }
                mCurrentState = State.IDLE;
            }
            mCanVerticalScroll = canVerticalScroll(mIntegerListHashMap);
        }
        return true;
    }

    private boolean mScrollFromMainTrack;

    /**
     * 设置是否是来自主轨道关联过来的滚动
     * Set scroll from user
     *
     * @param fromUser true from user ,false not
     */
    private void setScrollFromMainTrack(boolean fromUser) {
        mScrollFromMainTrack = fromUser;
    }

    /**
     * 是否是来自主轨道关联过来的滚动
     * Whether or not the user scrolls
     */
    private boolean isScrollFromMainTrack() {
        return mScrollFromMainTrack;
    }

    private BaseUIClip mLastAdsorbClip;
    private MeicamVideoTrack videoTrack = null;
    private long lastMoveX = -1;

    /**
     * baseItem吸附
     * baseItem Adsorb
     *
     * @param dx 移动距离
     * @return the int
     */
    private int baseItemAdsorb(int dx) {
        if (mDragView == null) {
            return dx;
        }
        //防止轨道滑消失 Prevent the track from slipping away
        if (dx == 0) {
            return dx;
        }
        if (totalMoveX == 0) {
            return dx;
        }
        //判断吸附其他资源的入点和出点 Determine the entry and exit points of other resources
        BaseUIClip tempClip = null;
        int moveDx;
        int newTrackIndex = mDragView.getTop() / mTrackHeight;
        for (Map.Entry<Integer, List<BaseUIClip>> entry : mIntegerListHashMap.entrySet()) {
            List<BaseUIClip> baseClipList = entry.getValue();
            if (baseClipList == null || baseClipList.size() == 0) {
                return dx;
            }
            if (mLastAdsorbClip != null && newTrackIndex != mLastAdsorbClip.getTrackIndex()) {
                //轨道不同则重置 Reset if the track is different
                mLastAdsorbClip = null;
            }
            for (int i = 0; i < baseClipList.size(); i++) {
                BaseUIClip baseClip = baseClipList.get(i);
                if (baseClip.getInPoint() == mLongPressBaseUIClipInpoint && newTrackIndex == mLongPressBaseUIClip.getTrackIndex()) {
                    continue;
                }
                if (nearByIndexBaseItem != -1 && nearByIndexBaseItem != i) {
                    continue;
                }

                long outPoint = (baseClip.getInPoint() + ((baseClip.getTrimOut() - baseClip.getTrimIn())));
                moveDx = PixelPerMicrosecondUtil.durationToLength(outPoint - mLongPressBaseUIClipInpoint) - totalMoveX;
                if (Math.abs(moveDx) >= Constants.START_ADSORB) {
                    moveDx = PixelPerMicrosecondUtil.durationToLength(outPoint - mLongPressBaseUIClipOutpoint) - totalMoveX;
                }
                if (Math.abs(moveDx) >= Constants.START_ADSORB) {
                    moveDx = PixelPerMicrosecondUtil.durationToLength(baseClip.getInPoint() - mLongPressBaseUIClipOutpoint) - totalMoveX;
                }
                if (Math.abs(moveDx) >= Constants.START_ADSORB) {
                    moveDx = PixelPerMicrosecondUtil.durationToLength(baseClip.getInPoint() - mLongPressBaseUIClipInpoint) - totalMoveX;
                }
                if (Math.abs(moveDx) < Constants.START_ADSORB && Math.abs(moveDx) != 1 && moveDx != 0) {
                    if (!hasNearByBaseItem) {
                        mLastAdsorbClip = baseClip;
                        nearByIndexBaseItem = i;
                        hasNearByBaseItem = true;
                        if (vibrator != null) {
                            vibrator.vibrate(30);
                        }
                        if (dx < 0) {
                            return -Math.abs(moveDx) + 1;
                        } else {
                            return Math.abs(moveDx) - 1;
                        }
                    }
                } else {
                    if (tempClip == null && Math.abs(moveDx) < PixelPerMicrosecondUtil.durationToLength(outPoint - baseClip.getInPoint())) {
                        /*找到第一个有重合的clip Find the first clip with overlap*/
                        tempClip = baseClip;
                    }
                    nearByIndexBaseItem = -1;
                    hasNearByBaseItem = false;
                }

            }
        }

        if (mLastAdsorbClip == null) {
            /*第一次、移动过程中换轨道，可能会走这里，用于下边的吸附主轨道片段的判断
            * For the first time, change the track during the movement,
            * and may go here, which is used to judge the adsorption main track segment below
            * */
            mLastAdsorbClip = tempClip;
        }
        //判断吸附基准线 Determine the adsorption baseline
        moveDx = lengthTimelineLeft - totalMoveX;
        if (Math.abs(moveDx) >= Constants.START_ADSORB) {
            moveDx = lengthTimelineRight - totalMoveX;
        }
        if (Math.abs(moveDx) < Constants.START_ADSORB && moveDx != 0 && (dx / moveDx > 0)) {
            if (!hasNearByBaseLine) {
                if (vibrator != null) {
                    vibrator.vibrate(30);
                }
                hasNearByBaseLine = true;
                if (dx < 0) {
                    return -Math.abs(moveDx);
                } else {
                    return Math.abs(moveDx);
                }
            }
        } else {
            hasNearByBaseLine = false;
        }
        //判断吸附主轨各片段InPoint,OutPoint
        // Judge the InPoint and OutPoint of each segment of the main track
        if (videoTrack == null) {
            videoTrack = EditorEngine.getInstance().getVideoTrack(TRACK_INDEX_MAIN);
        }
        if (videoTrack != null) {
            for (int i = 0; i < videoTrack.getClipCount(); i++) {
                MeicamVideoClip videoClip = videoTrack.getVideoClip(i);
                if (nearByVideoIndexLeft != -1 && nearByVideoIndexLeft != i) {
                    continue;
                }

                if (nearByVideoIndexRight != -1 && nearByVideoIndexRight != i) {
                    continue;
                }
                moveDx = PixelPerMicrosecondUtil.durationToLength(videoClip.getInPoint() - mLongPressBaseUIClipInpoint) - totalMoveX;
                if (Math.abs(moveDx) < Constants.START_ADSORB) {
                    if (!hasNearByVideoLeft) {
                        if (mLastAdsorbClip != null) {
                            long outPoint = mLastAdsorbClip.getInPoint() + (mLastAdsorbClip.getTrimOut() - mLastAdsorbClip.getTrimIn());
                            if (videoClip.getInPoint() > mLastAdsorbClip.getInPoint()
                                    && videoClip.getInPoint() < outPoint) {
                                /*主轨道片段相链接处，如果已经存在子轨道片段，就不再吸附
                                * At the link of the main track segment,
                                * if the sub-track segment already exists, it will not be adsorbed
                                * */
                                return dx;
                            }
                        }
                        nearByVideoIndexLeft = i;
                        hasNearByVideoLeft = true;
                        if (vibrator != null) {
                            vibrator.vibrate(30);
                        }
                        if (dx < 0) {
                            return -Math.abs(moveDx);
                        } else {
                            return Math.abs(moveDx);
                        }
                    }
                } else {
                    nearByVideoIndexLeft = -1;
                    hasNearByVideoLeft = false;
                }
                moveDx = PixelPerMicrosecondUtil.durationToLength(videoClip.getOutPoint() - mLongPressBaseUIClipOutpoint) - totalMoveX;
                if (Math.abs(moveDx) < Constants.START_ADSORB) {
                    if (!hasNearByVideoRight) {
                        if (mLastAdsorbClip != null) {
                            long outPoint = mLastAdsorbClip.getInPoint() + (mLastAdsorbClip.getTrimOut() - mLastAdsorbClip.getTrimIn());
                            if (videoClip.getOutPoint() > mLastAdsorbClip.getInPoint()
                                    && videoClip.getOutPoint() < outPoint) {
                                /*主轨道片段相链接处，如果已经存在子轨道片段，就不再吸附
                                * At the link of the main track segment,
                                * if the sub-track segment already exists, it will not be adsorbed
                                * */
                                return dx;
                            }
                        }
                        nearByVideoIndexRight = i;
                        hasNearByVideoRight = true;
                        if (vibrator != null) {
                            vibrator.vibrate(30);
                        }
                        if (dx < 0) {
                            return -Math.abs(moveDx);
                        } else {
                            return Math.abs(moveDx);
                        }
                    }
                } else {
                    nearByVideoIndexRight = -1;
                    hasNearByVideoRight = false;
                }
            }
        }
        return dx;
    }


    private void logAllChildView() {
        int count = mTrackViewVerticalParent.getChildCount();
        for (int i = 0; i < count; i++) {
            View view = mTrackViewVerticalParent.getChildAt(i);
            Log.e(TAG, "logAllChildView: " + view.getLeft() + " " + view.getTop() + "  " + view.getWidth() + "  " + view.getHeight()
                    + "  " + view);
        }

    }

    private boolean checkCanDragToHere(BaseUIClip baseUIClip, int newTrackIndex, long newInPoint, long newOutPoint) {

        List<BaseUIClip> baseUIClipList = mIntegerListHashMap.get(newTrackIndex);

        if (baseUIClipList == null || baseUIClipList.isEmpty()) {
            return true;
        } else {
            for (BaseUIClip clip : baseUIClipList) {
                long inPoint = clip.getInPoint();
                long outPoint = (long) (inPoint + (clip.getTrimOut() - clip.getTrimIn()) / clip.getSpeed());
                if ((newTrackIndex == baseUIClip.getTrackIndex()) && (inPoint == baseUIClip.getInPoint())) {
                    continue;
                }
                if (newInPoint == inPoint && newOutPoint == outPoint) {
                    return false;
                }
                if ((newInPoint <= inPoint && newOutPoint > inPoint)
                        || (newOutPoint > outPoint && newInPoint < outPoint)
                        || (newInPoint > inPoint && newOutPoint < outPoint)) {
                    return false;
                }
            }
        }
        return true;
    }

    private long getClipDuration(BaseUIClip clip) {
        return (long) ((clip.getTrimOut() - clip.getTrimIn()) / clip.getSpeed());
    }

    /**
     * Smooth scroll view.
     * 平滑滚动视图
     *
     * @param x the x
     */
    public void smoothScrollView(int x) {
        setScrollFromMainTrack(false);
        mHorizontalScrollView.smoothScrollTo(x, 0);
    }

    /**
     * Smooth scroll view.
     * 平滑滚动视图
     *
     * @param x the x
     */
    public void smoothScrollToByMainTrack(int x) {
        setScrollFromMainTrack(true);
        mHorizontalScrollView.smoothScrollTo(x, 0);
    }

    /**
     * scroll view.
     * 滚动视图
     *
     * @param x the x
     */
    public void scrollToByMainTrack(int x) {
        setScrollFromMainTrack(true);
        mHorizontalScrollView.scrollTo(x, 0);
    }

    /**
     * Scroll to x view.
     * 滚动到x视图
     *
     * @param x the x
     */
    public void scrollToXView(int x) {
        mHorizontalScrollView.scrollTo(x, 0);

    }

    /**
     * Gets horizontal scroll x.
     * 获得水平滚动x
     *
     * @return the horizontal scroll x
     */
    public int getHorizontalScrollX() {
        return mHorizontalScrollView.getScrollX();
    }


    /**
     * Smooth scroll y.
     * 平滑滚动y
     *
     * @param y the y
     */
    public void smoothScrollY(int y) {
        mTrackViewVerticalScroll.scrollTo(0, y);
    }

    /**
     * Smooth scroll by y.
     * y轴平滑滚动
     *
     * @param y the y
     */
    public void smoothScrollByY(int y) {
        mTrackViewVerticalScroll.scrollBy(0, y);
    }

    /**
     * 移动动画
     * Mobile animation
     */
    TranslateAnimation animation = null;

    /**
     * 音频录制避免遮挡，向上滑动
     * Audio recording to avoid blocking, slide up
     *
     * @param moveY the move y
     */
    public void scrollAnimation(int moveY) {
        linearScroll.clearAnimation();
        animation = new TranslateAnimation(0, 0, 0, -moveY);
        animation.setFillAfter(true);
        animation.setDuration(100);
        linearScroll.setAnimation(animation);
        animation.startNow();
    }

    /**
     * Cancle animation.
     * 取消动画
     */
    public void cancleAnimation() {
        linearScroll.clearAnimation();
    }

    /**
     * 判断触摸的位置是否落在 child 身上
     * Determine if the position of the touch falls on the child
     */
    private BaseItemView isPointOnViews(MotionEvent ev) {
        Rect rect = new Rect();
        BaseItemView dragView = null;
        for (int i = mTrackViewVerticalParent.getChildCount() - 1; i >= 0; i--) {
            View view = mTrackViewVerticalParent.getChildAt(i);
            rect.set((int) view.getX(), (int) view.getY(), (int) view.getX() + (int) view.getWidth()
                    , (int) view.getY() + view.getHeight());
            if (mHorizontalScrollView == null || mTrackViewVerticalScroll == null) {
                continue;
            }
//            Log.e(TAG, "isPointOnViews: 检测中： " + view.getX() + " " + view.getWidth() + " " + ev.getX() + "  " + mHorizontalScrollView.getScrollX());
            if (rect.contains((int) (ev.getX() + mHorizontalScrollView.getScrollX()), (int) (ev.getY() + mTrackViewVerticalScroll.getScrollY()))) {
                //标记被拖拽的child      The tag is being dragged child
                dragView = (BaseItemView) view;
                // 保存拖拽之间 child 的位置坐标   Save the coordinates of the child's position between drags
                mDragViewOrigX = dragView.getX();
                mDragViewOrigY = dragView.getY();
//                Log.e(TAG, "保存拖拽: mDragViewOrigX " + mDragViewOrigX + " mDragViewOrigY " + mDragViewOrigY + "  " + mDragView.getLeft() + "  " + mDragView.getTop());
                break;
            }
        }
//        Log.e(TAG, "isPointOnViews: " + result);
        return dragView;
    }

    /**
     * Sets select drag view.
     * 设置选择拖拽视图
     *
     * @param baseUIClip the base ui clip
     */
    public void setSelectDragView(BaseUIClip baseUIClip, boolean isCallBack) {
        if (baseUIClip == null) {
            return;
        }
        for (int i = mTrackViewVerticalParent.getChildCount() - 1; i >= 0; i--) {
            BaseItemView view = (BaseItemView) mTrackViewVerticalParent.getChildAt(i);
            BaseUIClip baseUIClip1 = view.getBaseUIClip();
            if (baseUIClip1 != null) {
                int trackIndex = baseUIClip1.getTrackIndex();
                long inPoint = baseUIClip1.getInPoint();
                LogUtils.d("setSelectDragView: " + trackIndex + "  " + baseUIClip.getTrackIndex() + "  " + baseUIClip.getInPoint() + "  " + inPoint);
                if (trackIndex == baseUIClip.getTrackIndex() && baseUIClip.getInPoint() == inPoint) {
                    setPipDuringVisiableStatus(false);
                    if (mDragView != null && !mDragView.equals(view)) {
                        mDragView.enableKeyFrame(false);
                    }
                    mDragView = view;
                    addSelectHandView(mDragView,isCallBack);
                    break;
                }
            }
        }
    }

    /**
     * Gets drag view.
     * 被拖动视图
     *
     * @return the drag view
     */
    public BaseItemView getDragView() {
        return mDragView;
    }

    /**
     * Gets top height by track index.
     * 通过轨道索引得到顶部高度
     *
     * @param trackIndex the track index
     * @return the top height by track index
     */
    public int getTopHeightByTrackIndex(int trackIndex) {
        return mTrackHeight * trackIndex;
    }

    @Override
    public boolean dispatchTouchEvent(MotionEvent ev) {
        if (ev.getAction() == MotionEvent.ACTION_MOVE) {
            if (Math.abs(mInterceptDownX - ev.getX()) > mSlop) {
                removeCallbacks(mLongPressRunnable);
            }
        } else if (ev.getAction() == MotionEvent.ACTION_UP || ev.getAction() == MotionEvent.ACTION_CANCEL) {
            removeCallbacks(mLongPressRunnable);
        }
        return super.dispatchTouchEvent(ev);
    }

    private float mInterceptDownX;

    @Override
    public boolean onInterceptTouchEvent(final MotionEvent ev) {
        if (isScrollFromMainTrack()) {
            setScrollFromMainTrack(false);
        }
        if (ev.getAction() == MotionEvent.ACTION_DOWN) {
            mInterceptDownX = ev.getX();
            mDownTime = System.currentTimeMillis();
            HandView handView = getHandView();
            boolean isOnHand = false;
            cancleAnimation();
            if (mOnTrackViewScrollListener != null) {
                mOnTrackViewScrollListener.startScroll();
            }
            //检测是否按在把手上 Check whether to press on the handle
            if (handView != null) {
                isOnHand = handView.isHandDownOnHandView(ev.getRawX(), ev.getRawY());
            }
            if (isOnHand) {
                return false;
            }
            mNewDragView = isPointOnViews(ev);
            if (mNewDragView != null) {
                mLastPointX = ev.getX();
                mLastPointY = ev.getY();
                mLastDragTrackIndex = (int) (ev.getY() + mTrackViewVerticalScroll.getScrollY() - mTrackViewMarginTop) / (mTrackHeight);
                removeCallbacks(mLongPressRunnable);
                postDelayed(mLongPressRunnable, CommonData.CLICK_LONG);
            }

        } else if (ev.getAction() == MotionEvent.ACTION_UP || ev.getAction() == MotionEvent.ACTION_CANCEL) {
            mCurrentState = State.IDLE;
            mLongPressEnd = false;
            mLastDragTrackIndex = -1;
            if (System.currentTimeMillis() - mDownTime < CommonData.CLICK_TIME) {
                setPipDuringVisiableStatus(false);
                //判断点击 Click on the judgment
                if (mNewDragView != null && !mNewDragView.equals(mDragView)) {
                    if (mDragView != null) {
                        mDragView.enableKeyFrame(false);
                    }
                    mDragView = mNewDragView;
                    addSelectHandView(mDragView,true);
                } else {
                    if (mOnTrackViewScrollListener != null) {
                        clickOutSide();
                        mOnTrackViewScrollListener.clickOutSide();
                        mDragView = null;
                    }
                }
            } else {
                if (mDragView != null) {
                    clickOutSide();
                }
            }
        }
        //return mDragView != null && mLongPressEnd;
        return mCurrentState == State.DRAGGING;
    }

    private boolean isSameView() {
        if (mDragView == null || mNewDragView == null) {
            return false;
        }
        BaseUIClip dragBaseUIClip = mDragView.getBaseUIClip();
        BaseUIClip dragNewBaseUIClip = mNewDragView.getBaseUIClip();
        if (dragBaseUIClip == null || dragNewBaseUIClip == null) {
            return false;
        }
        return dragBaseUIClip.getInPoint() == dragNewBaseUIClip.getInPoint() && dragBaseUIClip.getTrackIndex() == dragNewBaseUIClip.getTrackIndex();
    }

    private void addSelectHandView(BaseItemView dragView, boolean isCallBack) {
        //选择子view Select child view
        removeHandView();
        HandView handView = new HandView(getContext());
        dragView.setHandView(handView);
        dragView.setPipDuringVisiableStatus(true);
        BaseUIClip uiClip = dragView.getBaseUIClip();
        handView.setIntegerListHashMap(mIntegerListHashMap);
        handView.setBaseUIClip(uiClip);
        handView.setTimeDuration(mTimelineDuration);
        handView.setOnHandChangeListener(mOnHandChangeListener);
        handView.setOnDownToGetNextClipListener(this);
        track_view_horizontal_layout.addView(handView);
        dragView.enableKeyFrame(true);
        if (mOnTrackViewDragListener != null && isCallBack) {
            mOnTrackViewDragListener.onSelectClip(dragView.getBaseUIClip());
        }
    }

    /**
     * Show keyframe.
     * 显示关键帧
     */
    public void showKeyframe() {
        if (mDragView != null) {
            mDragView.enableKeyFrame(true);
        }
    }

    /**
     * Hide keyframe.
     * 隐藏关键帧
     */
    public void hideKeyframe() {
        if (mDragView != null) {
            mDragView.enableKeyFrame(false);
        }
    }

    public void setTimelineDuration(long mTimelineDuration) {
        this.mTimelineDuration = mTimelineDuration;
    }

    /**
     * Click out side. 单击出界
     */
    public void clickOutSide() {
        removeHandView();
        if (mDragView != null) {
            mDragView.checkKeyFrame(-1);
            mDragView.enableKeyFrame(false);
        }
        if (mOnTrackViewDragListener != null) {
            mOnTrackViewDragListener.onUnselectedClip();
        }
        mDragView = null;
    }

    private void removeHandView() {
        setPipDuringVisiableStatus(false);
        int count = track_view_horizontal_layout.getChildCount();
        for (int i = 0; i < count; i++) {
            View view = track_view_horizontal_layout.getChildAt(i);
            if (view instanceof HandView) {
                track_view_horizontal_layout.removeView(view);
            }
        }
    }

    private HandView getHandView() {
        int count = track_view_horizontal_layout.getChildCount();
        for (int i = 0; i < count; i++) {
            View view = track_view_horizontal_layout.getChildAt(i);
            if (view instanceof HandView) {
                return (HandView) view;
            }
        }
        return null;
    }

    /**
     * 轨道类型
     */
    private String trackType = "";


    /**
     * Sets data.
     * 设置数据
     *
     * @param integerListHashMap the integer list hash map
     * @param timelineDuration   the timeline duration
     */
    public void setData(HashMap<Integer, List<BaseUIClip>> integerListHashMap, long timelineDuration, String trackType) {
        clear();
        if ( this.trackType != trackType) {
            mTrackViewVerticalScroll.scrollTo(mTrackViewVerticalScroll.getScrollX(), 0);
        }
        this.trackType = trackType;
        mTimelineDuration = timelineDuration;
        //此处赋值放在非空判断之后，避免赋值为null This assignment is placed after the non-null judgment to avoid null assignments
        //mIntegerListHashMap = integerListHashMap;
        if (integerListHashMap == null) {
            mIntegerListHashMap.clear();
            return;
        }
        LayoutParams layoutParams = (LayoutParams) mTrackViewVerticalParent.getLayoutParams();
//        LogUtils.d("setData: " + mTimeline.getDuration() + "  " + PixelPerMicrosecondUtil.durationToLength(mTimeline.getDuration()));
        layoutParams.width = PixelPerMicrosecondUtil.durationToLength(timelineDuration) + ScreenUtils.getScreenWidth();
        mMaxTrackCount = MAX_TRACK_COUNT;
        mMaxTrackCount = Math.max(mMaxTrackCount, getMaxTrackIndex(integerListHashMap) + 1);
        layoutParams.height = mMaxTrackCount * mTrackHeight + mTrackViewMarginTop;
        mTrackViewVerticalParent.setLayoutParams(layoutParams);
        mIntegerListHashMap = integerListHashMap;
        Set<Map.Entry<Integer, List<BaseUIClip>>> entries = integerListHashMap.entrySet();
        if (entries.size() == 0) {
            return;
        }
        for (Map.Entry<Integer, List<BaseUIClip>> entry : entries) {
            List<BaseUIClip> clipList = entry.getValue();
            if (clipList != null && !clipList.isEmpty()) {
                for (BaseUIClip baseUIClip : clipList) {
                    // LogUtils.d( "setData: = " + baseUIClip);
                    BaseItemView itemView = new BaseItemView(mContext);
                    itemView.setWaveformHelper(mWaveformHelper);
                    FrameLayout.LayoutParams layoutParams2 = new FrameLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
                    layoutParams2.leftMargin = getChildTopMarginFromDuration(baseUIClip.getInPoint());
                    layoutParams2.topMargin = mTrackHeight * entry.getKey() + mTrackViewMarginTop;
                    itemView.setLayoutParams(layoutParams2);
                    mTrackViewVerticalParent.addView(itemView);
                    itemView.setData(baseUIClip);
                }
            }
        }
        int horizontalScrollX = getHorizontalScrollX();
        mWaveformHelper.notifyScrollChanged(horizontalScrollX, horizontalScrollX);
        post(new Runnable() {
            @Override
            public void run() {
                mCanVerticalScroll = canVerticalScroll(integerListHashMap);
            }
        });
    }

    /**
     * 音频录制过程中也要刷新HandView
     * HandView is also refreshed during audio recording
     *
     * @param baseUIClip the base ui clip
     */
    public void refreshAudioSelectView(BaseUIClip baseUIClip) {
        if (mDragView == null) {
            return;
        }
        HandView handView = mDragView.getHandView();
        if (handView == null) {
            return;
        }
        handView.setBaseUIClip(baseUIClip);
        refreshSelectView(baseUIClip, true);
    }

    /**
     * Refresh select view.
     * 选择刷新视图
     *
     * @param baseUIClip the base ui clip
     * @param isLeft     the is left
     */
    public void refreshSelectView(BaseUIClip baseUIClip, boolean isLeft) {
        if (mDragView != null) {
            setPipDuringVisiableStatus(false);
            mDragView.refresh(baseUIClip, isLeft);
            FrameLayout.LayoutParams layoutParams2 = (FrameLayout.LayoutParams) mDragView.getLayoutParams();
            layoutParams2.leftMargin = getChildTopMarginFromDuration(baseUIClip.getInPoint());
            layoutParams2.topMargin = mTrackHeight * baseUIClip.getTrackIndex() + mTrackViewMarginTop;
            mDragView.setLayoutParams(layoutParams2);
        }
    }

    /**
     * Clear.
     * 清除
     */
    public void clear() {
        mTrackViewVerticalParent.removeAllViews();
        removeHandView();
        mDragView = null;
    }

    private int getChildTopMarginFromDuration(long duration) {
        return mStartPadding + PixelPerMicrosecondUtil.durationToLength(duration);
    }


    @Override
    public BaseUIClip getNextClip(BaseUIClip nowClip) {
        int trackIndex = nowClip.getTrackIndex();
        List<BaseUIClip> baseUIClipList = mIntegerListHashMap.get(trackIndex);
        if (baseUIClipList != null) {
            Collections.sort(baseUIClipList, new Comparator<BaseUIClip>() {
                @Override
                public int compare(BaseUIClip o1, BaseUIClip o2) {
                    return (int) (o1.getInPoint() - o2.getInPoint());
                }
            });
            for (int i = 0; i < baseUIClipList.size(); i++) {
                BaseUIClip baseUIClip = baseUIClipList.get(i);
                if (baseUIClip.getInPoint() > nowClip.getInPoint()) {
                    return baseUIClip;
                }
            }
        } else {
            Log.e(TAG, "getNextClip: list is null! key is " + trackIndex);
        }
        return null;
    }

    @Override
    public BaseUIClip getBeforeClip(BaseUIClip nowClip) {
        int trackIndex = nowClip.getTrackIndex();
        List<BaseUIClip> baseUIClipList = mIntegerListHashMap.get(trackIndex);
        if (baseUIClipList != null) {
            Collections.sort(baseUIClipList, new Comparator<BaseUIClip>() {
                @Override
                public int compare(BaseUIClip o1, BaseUIClip o2) {
                    return (int) (o1.getInPoint() - o2.getInPoint());
                }
            });
            for (int i = baseUIClipList.size() - 1; i >= 0; i--) {
                BaseUIClip baseUIClip = baseUIClipList.get(i);
                if (baseUIClip.getInPoint() < nowClip.getInPoint()) {
                    return baseUIClip;
                }
            }
        } else {
            Log.e(TAG, "getNextClip: list is null! key is " + trackIndex);
        }
        return null;
    }

    /**
     * Sets on track view scroll listener.
     * 设置在跟踪视图滚动监听器
     *
     * @param onTrackViewScrollListener the on track view scroll listener
     */
    public void setOnTrackViewScrollListener(OnTrackViewScrollListener onTrackViewScrollListener) {
        mOnTrackViewScrollListener = onTrackViewScrollListener;
    }

    /**
     * Sets on track view drag listener.
     * 设置跟踪视图拖动监听器
     *
     * @param onTrackViewDragListener the on track view drag listener
     */
    public void setOnTrackViewDragListener(OnTrackViewDragListener onTrackViewDragListener) {
        mOnTrackViewDragListener = onTrackViewDragListener;
    }

    /**
     * Sets on hand change listener.
     * 设置手动更改监听器
     *
     * @param onHandChangeListener the on hand change listener
     */
    public void setOnHandChangeListener(HandView.OnHandChangeListener onHandChangeListener) {
        mOnHandChangeListener = onHandChangeListener;
    }

    /**
     * To main menu.
     * 主菜单
     */
    public void toMainMenu() {
        if (mAddVoice.getVisibility() != VISIBLE) {
            mAddVoice.setVisibility(VISIBLE);
            setAudioView();
            clear();
        }
    }

    public void setAudioView() {
        //存在音频 There is audio
        realAudio.removeAllViews();

        if (hasAudio()) {
            int count = EditorEngine.getInstance().getAudioTrackCount();
            for (int i = 0; i < count; i++) {
                MeicamAudioTrack track = EditorEngine.getInstance().getAudioTrack(i);
                for (int j = 0; j < track.getClipCount(); j++) {
                    MeicamAudioClip audioClip = track.getAudioClip(j);
                    String channelUrl = audioClip.getLeftChannelUrl();
                    if (!TextUtils.isEmpty(channelUrl)) {
                        AudioWaveView waveformView = new AudioWaveView(mContext);
                        waveformView.setWidth((audioClip.getOutPoint() - audioClip.getInPoint()) / 1000);
                        waveformView.setBackgroundResource(R.drawable.bg_round_corner_solid_242424);
                        waveformView.setWaveColor(getResources().getColor(R.color.audio_music));
                        waveformView.setMaxGroupData(0.5F);
                        waveformView.addWaveData(channelUrl, audioClip.getTrimIn(), audioClip.getTrimOut(), audioClip.getOriginalDuration());
                        realAudio.addView(waveformView);
                        LayoutParams layoutParams = (LayoutParams) waveformView.getLayoutParams();
                        layoutParams.leftMargin = PixelPerMicrosecondUtil.durationToLength(audioClip.getInPoint());
                        layoutParams.width = PixelPerMicrosecondUtil.durationToLength((long) ((audioClip.getTrimOut() - audioClip.getTrimIn()) / audioClip.getSpeed())) - BASE_ITEM_MARGIN;
                        layoutParams.height = LayoutParams.MATCH_PARENT;
                        waveformView.setLayoutParams(layoutParams);
                    } else {
                        NvsWaveformViewWrap waveformView = new NvsWaveformViewWrap(mContext);
                        waveformView.setBackgroundResource(R.drawable.bg_round_corner_solid_242424);
                        waveformView.setWaveformColor(getResources().getColor(R.color.audio_music));
                        waveformView.setData(audioClip.getFilePath(), audioClip.getInPoint(), audioClip.getTrimIn(), audioClip.getTrimOut());
                        //waveformView.setTrimIn(audioClip.getTrimIn());
                        //waveformView.setTrimOut(audioClip.getTrimOut());
                        realAudio.addView(waveformView);
                        mWaveformHelper.addView(waveformView);
                        LayoutParams layoutParams = (LayoutParams) waveformView.getLayoutParams();
                        layoutParams.leftMargin = PixelPerMicrosecondUtil.durationToLength(audioClip.getInPoint());
                        layoutParams.width = PixelPerMicrosecondUtil.durationToLength((long) ((audioClip.getTrimOut() - audioClip.getTrimIn()) / audioClip.getSpeed())) - BASE_ITEM_MARGIN;
                        layoutParams.height = LayoutParams.MATCH_PARENT;
                        waveformView.setLayoutParams(layoutParams);
                    }
                }
            }
            mImageAudio.setImageResource(R.mipmap.main_menu_ic_music_flag);
            mTvMusicDesc.setText(R.string.audio_compilation);
        } else {
            mImageAudio.setImageResource(R.mipmap.main_menu_ic_add);
            mTvMusicDesc.setText(getResources().getString(R.string.add_voice));
        }
    }

    private boolean hasAudio() {
        int count = EditorEngine.getInstance().getAudioTrackCount();
        for (int i = 0; i < count; i++) {
            MeicamAudioTrack track = EditorEngine.getInstance().getAudioTrack(i);
            if (track.getClipCount() > 0) {
                return true;
            }
        }
        return false;
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        WaveformDataCache.getInstance().release();
    }

    /**
     * To other menu.
     * 其他菜单
     */
    public void toOtherMenu() {
        mAddVoice.setVisibility(GONE);
    }

    /**
     * The interface On track view scroll listener.
     * 跟踪视图滚动监听器的接口
     */
    public interface OnTrackViewScrollListener {
        /**
         * Scroll x.
         * 滚动x
         *
         * @param dx    the dx
         * @param oldDx the old dx
         */
        void scrollX(int dx, int oldDx, boolean fromMainTrack);

        /**
         * Start scroll.
         * 开始滚动
         */
        void startScroll();

        /**
         * Click out side.
         * 点击外面
         */
        void clickOutSide();

        /**
         * Click to music menu.
         * 点击音乐菜单
         */
        void clickToMusicMenu();

        /**
         * On track view long click.
         * 在轨道视图长点击
         *
         * @param baseUIClip the base ui clip
         */
        void onTrackViewLongClick(BaseUIClip baseUIClip);

    }

    /**
     * The interface On track view drag listener.
     * 跟踪视图拖动监听器的接口
     */
    public interface OnTrackViewDragListener {
        /**
         * Drag end object.
         * 结束拖动对象
         *
         * @param oldUiClip     the old ui clip
         * @param newTrackIndex the new track index
         * @param newInPoint    the new in point
         * @return the object
         */
        Object dragEnd(BaseUIClip oldUiClip, int newTrackIndex, long newInPoint, boolean isToEnd);

        /**
         * Drag to the end
         * 拖动到最后
         */
        void dragToEnd(boolean isToEnd);

        /**
         * On select clip.
         * 选择剪辑
         *
         * @param baseUIClip the base ui clip
         */
        void onSelectClip(BaseUIClip baseUIClip);

        /**
         * 取消选中片段
         * Unselected clip
         */
        void onUnselectedClip();
    }

    /**
     * Has drag view boolean.
     * 有拖拽视图布尔值
     *
     * @return the boolean
     */
    public boolean hasDragView() {
        return mDragView != null;
    }

    /**
     * Gets drag view type.
     * 获取拖拽视图类型
     *
     * @return the drag view type
     */
    public String getDragViewType() {
        String type = "";
        if (mDragView != null && mDragView.getBaseUIClip() != null) {
            return mDragView.getBaseUIClip().getType();
        }
        return "";
    }

    /**
     * 设置画中画时长显示状态
     * Sets the in-picture duration display state
     *
     * @param isVisiable the is visiable
     */
    public void setPipDuringVisiableStatus(boolean isVisiable) {
        if (mDragView != null) {
            mDragView.setPipDuringVisiableStatus(isVisiable);
        }
    }

    /**
     * Update tag view.
     * 更新Tag控件
     */
    public void updateTagView() {
        if (mDragView != null) {
            mDragView.updateFxTargetTag();
        }
    }

    private final Runnable mLongPressRunnable = new Runnable() {
        @Override
        public void run() {
            if (mOnTrackViewScrollListener != null) {
                clickOutSide();
                if (mNewDragView != null) {
                    BaseUIClip baseUIClip = mNewDragView.getBaseUIClip();
                    mOnTrackViewScrollListener.onTrackViewLongClick(baseUIClip);
                }
            }
            if (mNewDragView == null) {
                LogUtils.e("mNewDragView==null");
                return;
            }
            mCurrentState = State.DRAGGING;
            hasNearByBaseItem = false;
            hasNearByVideoRight = false;
            hasNearByVideoLeft = false;
            hasNearByBaseLine = false;
            nearByIndexBaseItem = -1;
            mLongPressEnd = true;
            mDragView = mNewDragView;
            mDragView.bringToFront();
            mDragView.setAlpha(0.8f);
            mLongPressStartMarginLeft = mDragView.getLeft();
            mCurrTimelinePosition = EditorEngine.getInstance().getCurrentTimelinePosition();
            mLongPressBaseUIClip = mDragView.getBaseUIClip();
            mLongPressBaseUIClipInpoint = mLongPressBaseUIClip.getInPoint();
            mLongPressBaseUIClipOutpoint = mLongPressBaseUIClip.getInPoint() + (mLongPressBaseUIClip.getTrimOut() - mLongPressBaseUIClip.getTrimIn());
            lengthTimelineRight = PixelPerMicrosecondUtil.durationToLength(mCurrTimelinePosition - mLongPressBaseUIClipOutpoint);
            lengthTimelineLeft = PixelPerMicrosecondUtil.durationToLength(mCurrTimelinePosition - mLongPressBaseUIClipInpoint);
            if (vibrator != null) {
                vibrator.vibrate(200);
            }
        }
    };

    private boolean canVerticalScroll(HashMap<Integer, List<BaseUIClip>> trackData){
        int max = getMaxTrackIndex(trackData) + 1;
        return mTrackViewVerticalScroll.getScrollY() > 0 || max * mTrackHeight + mTrackViewMarginTop > getHeight();
    }

    private int getMaxTrackIndex(HashMap<Integer, List<BaseUIClip>> trackData) {
        if (CommonUtils.isEmpty(trackData)) {
            return 0;
        }
        Set<Integer> keySet = trackData.keySet();
        int max = 0;
        for (Integer integer : keySet) {
            if (max < integer) {
                max = integer;
            }
        }
        return max;
    }
}
