package com.meishe.myvideo.ui.trackview;

import static com.meishe.base.constants.Constants.TRACK_INDEX_MAIN;

import android.annotation.SuppressLint;
import android.content.Context;
import android.os.Vibrator;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RelativeLayout;

import com.meicam.sdk.NvsAVFileInfo;
import com.meicam.sdk.NvsStreamingContext;
import com.meishe.base.constants.Constants;
import com.meishe.base.utils.LogUtils;
import com.meishe.base.utils.ScreenUtils;
import com.meishe.engine.EditorEngine;
import com.meishe.engine.bean.CommonData;
import com.meishe.engine.bean.MeicamVideoClip;
import com.meishe.engine.bean.MeicamVideoTrack;
import com.meishe.myvideo.R;
import com.meishe.myvideo.ui.bean.BaseUIClip;
import com.meishe.myvideo.ui.bean.KeyFrameInfo;
import com.meishe.myvideo.util.PixelPerMicrosecondUtil;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by CaoZhiChao on 2020/6/28 10:30
 * 轨道上裁剪clip的把手控件
 * A handle control for clipping a clip on the track
 */
public class HandView extends RelativeLayout {
    private static final String TAG = "HandView";
    private Context mContext;
    private BaseUIClip mBaseUIClip;
    private ImageView mTrackDragLeftHand;
    private ImageView mTrackDragRightHand;
    private int mStartPadding;
    private float mDownX;
    private OnHandChangeListener mOnHandChangeListener;
    private int mHandWidth;
    private int mHandHeight;
    private long mTimeDuration;
    private BaseUIClip mNextClip = null;
    private BaseUIClip mBeforeClip = null;
    private OnDownToGetNextClip mOnDownToGetNextClipListener;
    /**
     *  入点和基准线的差的距离
     *  The distance between the entry point and the reference line
     */
    private int lengthLeft = -1;
    private int lengthRight = -1;
    private static final int MIN_MARGIN_BASE_LINR = 50;
    private long mCurrTimelinePosition = -1;
    private int totalMoveX = -1;
    private float mStartMarginLeft, mStartMarginRight;
    /**
     * 震动器
     * vibrator
     */
    private Vibrator vibrator;
    private long mStartInpoint, mStartOutpoint;
    private HashMap<Integer, List<BaseUIClip>> mIntegerListHashMap = new HashMap<>();

    public HandView(Context context) {
        super(context);
        init(context);
    }

    public HandView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public HandView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }


    @SuppressLint("ClickableViewAccessibility")
    private void init(Context context) {
        mContext = context;
        mStartPadding = ScreenUtils.getScreenWidth() / 2;
        mHandWidth = getResources().getDimensionPixelOffset(R.dimen.editor_timeline_view_hand_width);
        mHandHeight = getResources().getDimensionPixelOffset(R.dimen.editor_timeline_view_hand_width);
        LayoutInflater inflater = LayoutInflater.from(mContext);
        View parentView = inflater.inflate(R.layout.track_hand_view, this);
        mTrackDragLeftHand = parentView.findViewById(R.id.track_drag_left_hand);
        mTrackDragRightHand = parentView.findViewById(R.id.track_drag_right_hand);
        vibrator = (Vibrator) context.getSystemService(Context.VIBRATOR_SERVICE);
        mTrackDragLeftHand.setOnTouchListener(new OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                int action = event.getAction();
                if (action == MotionEvent.ACTION_DOWN) {
                    handDown(event);
                    mStartMarginLeft = getLeft();
                    mStartInpoint = mBaseUIClip.getInPoint();
                    lengthLeft = PixelPerMicrosecondUtil.durationToLength(mCurrTimelinePosition - mStartInpoint);
                } else if (action == MotionEvent.ACTION_MOVE) {
                    int dx = (int) (event.getRawX() - mDownX);
                    if ((hasNearByBaseItem || hasNearByVideoLeft || hasNearByVideoRight || hasNearByBaseLine) && Math.abs(dx) < Constants.SLOPX) {
                        return false;
                    }
                    totalMoveX = (int) (getLeft() - mStartMarginLeft);
                    dx = baseItemAdsorb(dx, true);
                    lastMoveX = totalMoveX;
                    mDownX = event.getRawX();
                    long changeValue = PixelPerMicrosecondUtil.lengthToDuration(dx);
                    leftHandleMove(dx, changeValue);

                } else if (action == MotionEvent.ACTION_UP) {
                    handUp(true);
                }
                return true;
            }
        });
        mTrackDragRightHand.setOnTouchListener(new OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                int action = event.getAction();
                if (action == MotionEvent.ACTION_DOWN) {
                    handDown(event);
                    mStartMarginRight = getWidth();
                    mStartOutpoint = mBaseUIClip.getInPoint() + mBaseUIClip.getTrimOut() - mBaseUIClip.getTrimIn();
                    lengthRight = PixelPerMicrosecondUtil.durationToLength(mCurrTimelinePosition - mStartOutpoint);
                } else if (action == MotionEvent.ACTION_MOVE) {
                    int dx = (int) (event.getRawX() - mDownX);
                    if ((hasNearByBaseItem || hasNearByVideoLeft || hasNearByVideoRight || hasNearByBaseLine) && Math.abs(dx) < Constants.SLOPX) {
                        return false;
                    }
                    totalMoveX = (int) (getWidth() - mStartMarginRight);
                    dx = baseItemAdsorb(dx, false);
                    lastMoveX = totalMoveX;
                    mDownX = event.getRawX();
                    long changeValue = PixelPerMicrosecondUtil.lengthToDuration(dx);
                    rightHandleMove(dx, changeValue);
                } else if (action == MotionEvent.ACTION_UP) {
                    handUp(false);
                }
                return true;
            }
        });
    }

    private BaseUIClip mLastAdsorbClip;
    private MeicamVideoTrack videoTrack = null;
    private long lastMoveX = -1;
    /**
     * 判断是否已吸附
     * Determine if adsorption has occurred
     */
    private boolean hasNearByBaseItem = false;
    private int nearByIndexBaseItem = -1;
    /**
     * 判断是否靠近某一个ItemView或者基准线
     * Determine if it is near an ItemView or a reference line
     */
    private boolean hasNearByBaseLine = false;


    private boolean hasNearByVideoLeft = false;
    private boolean hasNearByVideoRight = false;
    private int nearByVideoIndexLeft = -1;
    private int nearByVideoIndexRight = -1;
    private long mLongPressBaseUIClipInpoint;
    private long mLongPressBaseUIClipOutpoint;
    private int lengthTimelineLeft = -1;
    private int lengthTimelineRight = -1;

    private int baseItemAdsorb(int dx, boolean isLeft) {
        //防止轨道滑消失 Prevent the track from slipping away
        if (dx == 0) {
            return dx;
        }
        if (totalMoveX == 0) {
            return dx;
        }
        //判断吸附其他资源的入点和出点 Determine the entry and exit points of other resources
        BaseUIClip tempClip = null;
        int moveDx;
        for (Map.Entry<Integer, List<BaseUIClip>> entry : mIntegerListHashMap.entrySet()) {
            List<BaseUIClip> baseClipList = entry.getValue();
            if (baseClipList == null || baseClipList.size() == 0) {
                return dx;
            }
            for (int i = 0; i < baseClipList.size(); i++) {
                BaseUIClip baseClip = baseClipList.get(i);
                if (baseClip.getInPoint() == mLongPressBaseUIClipInpoint && baseClip.getTrackIndex() == mBaseUIClip.getTrackIndex()) {
                    continue;
                }
                if (nearByIndexBaseItem != -1 && nearByIndexBaseItem != i) {
                    continue;
                }

                long outPoint = (baseClip.getInPoint() + ((baseClip.getTrimOut() - baseClip.getTrimIn())));
                if (isLeft) {
                    moveDx = PixelPerMicrosecondUtil.durationToLength(outPoint - mLongPressBaseUIClipInpoint) - totalMoveX;
                    if (Math.abs(moveDx) >= Constants.START_ADSORB) {
                        moveDx = PixelPerMicrosecondUtil.durationToLength(baseClip.getInPoint() - mLongPressBaseUIClipInpoint) - totalMoveX;
                    }

                } else {
                    moveDx = PixelPerMicrosecondUtil.durationToLength(baseClip.getInPoint() - mLongPressBaseUIClipOutpoint) - totalMoveX;
                    if (Math.abs(moveDx) >= Constants.START_ADSORB) {
                        moveDx = PixelPerMicrosecondUtil.durationToLength(outPoint - mLongPressBaseUIClipOutpoint) - totalMoveX;
                    }
                }
                if (Math.abs(moveDx) < Constants.START_ADSORB && Math.abs(moveDx) != 1 && moveDx != 0 && (lastMoveX / moveDx) > 0) {
                    if (!hasNearByBaseItem) {
                        mLastAdsorbClip = baseClip;
                        nearByIndexBaseItem = i;
                        hasNearByBaseItem = true;
                        if (vibrator != null) {
                            vibrator.vibrate(30);
                        }
                        if (dx < 0) {
                            return -Math.abs(moveDx) + 1;
                        } else {
                            return Math.abs(moveDx) - 1;
                        }
                    }
                } else {
                    if (tempClip == null && Math.abs(moveDx) < PixelPerMicrosecondUtil.durationToLength(outPoint - baseClip.getInPoint())) {
                        /*找到第一个有重合的clip
                        * Find the first clip with overlap
                        * */
                        tempClip = baseClip;
                    }
                    nearByIndexBaseItem = -1;
                    hasNearByBaseItem = false;
                }

            }
        }

        if (mLastAdsorbClip == null) {
            /*第一次、移动过程中换轨道，可能会走这里，用于下边的吸附主轨道片段的判断
            * For the first time, change the track during the movement, and may go here,
            * which is used to judge the adsorption main track segment below
            * */
            mLastAdsorbClip = tempClip;
        }
        //判断吸附基准线 Determine the adsorption baseline
        float left = isLeft ? lengthTimelineLeft : lengthTimelineRight;
        moveDx = (int) (left - totalMoveX);
        if (Math.abs(moveDx) < Constants.START_ADSORB && moveDx != 0 && (left / moveDx > 0)) {
            if (!hasNearByBaseLine) {
                if (vibrator != null) {
                    vibrator.vibrate(30);
                }
                hasNearByBaseLine = true;
                if (dx < 0) {
                    return -Math.abs(moveDx);
                } else {
                    return Math.abs(moveDx);
                }
            }
        } else {
            hasNearByBaseLine = false;
        }
        //判断吸附主轨各片段InPoint,OutPoint
        // Judge the InPoint and OutPoint of each segment of the main track
        if (videoTrack == null) {
            videoTrack = EditorEngine.getInstance().getVideoTrack(TRACK_INDEX_MAIN);
        }
        if (videoTrack != null) {
            for (int i = 0; i < videoTrack.getClipCount(); i++) {
                MeicamVideoClip videoClip = videoTrack.getVideoClip(i);
                if (nearByVideoIndexLeft != -1 && nearByVideoIndexLeft != i) {
                    continue;
                }

                if (nearByVideoIndexRight != -1 && nearByVideoIndexRight != i) {
                    continue;
                }
                if (isLeft) {
                    moveDx = PixelPerMicrosecondUtil.durationToLength(videoClip.getInPoint() - mLongPressBaseUIClipInpoint) - totalMoveX;
                    if (Math.abs(moveDx) < Constants.START_ADSORB) {
                        if (!hasNearByVideoLeft) {
                            if (mLastAdsorbClip != null) {
                                long outPoint = mLastAdsorbClip.getInPoint() + (mLastAdsorbClip.getTrimOut() - mLastAdsorbClip.getTrimIn());
                                if (videoClip.getInPoint() > mLastAdsorbClip.getInPoint()
                                        && videoClip.getInPoint() < outPoint) {
                                    /*主轨道片段相链接处，如果已经存在子轨道片段，就不再吸附
                                    * At the link of the main track segment, if the sub-track segment already exists, it will not be adsorbed
                                    * */
                                    return dx;
                                }
                            }
                            nearByVideoIndexLeft = i;
                            hasNearByVideoLeft = true;
                            if (vibrator != null) {
                                vibrator.vibrate(30);
                            }
                            if (dx < 0) {
                                return -Math.abs(moveDx);
                            } else {
                                return Math.abs(moveDx);
                            }
                        }
                    } else {
                        nearByVideoIndexLeft = -1;
                        hasNearByVideoLeft = false;
                    }
                } else {
                    moveDx = PixelPerMicrosecondUtil.durationToLength(videoClip.getOutPoint() - mLongPressBaseUIClipOutpoint) - totalMoveX;
                    if (Math.abs(moveDx) < Constants.START_ADSORB) {
                        if (!hasNearByVideoRight) {
                            if (mLastAdsorbClip != null) {
                                long outPoint = mLastAdsorbClip.getInPoint() + (mLastAdsorbClip.getTrimOut() - mLastAdsorbClip.getTrimIn());
                                if (videoClip.getOutPoint() > mLastAdsorbClip.getInPoint()
                                        && videoClip.getOutPoint() < outPoint) {
                                    /*主轨道片段相链接处，如果已经存在子轨道片段，就不再吸附
                                    * At the link of the main track segment, if the sub-track segment already exists, it will not be adsorbed
                                    * */
                                    return dx;
                                }
                            }
                            nearByVideoIndexRight = i;
                            hasNearByVideoRight = true;
                            if (vibrator != null) {
                                vibrator.vibrate(30);
                            }
                            if (dx < 0) {
                                return -Math.abs(moveDx);
                            } else {
                                return Math.abs(moveDx);
                            }
                        }
                    } else {
                        nearByVideoIndexRight = -1;
                        hasNearByVideoRight = false;
                    }
                }
            }
        }
        return dx;
    }


    private long downDuration;

    private void handDown(MotionEvent event) {
        getParent().requestDisallowInterceptTouchEvent(true);
        mDownX = (int) event.getRawX();
        downDuration = mBaseUIClip.getOutPoint() - mBaseUIClip.getInPoint();
        mCurrTimelinePosition = EditorEngine.getInstance().getCurrentTimelinePosition();
        totalMoveX = 0;
        resetAdsorb();
        if (mOnDownToGetNextClipListener != null) {
            mNextClip = mOnDownToGetNextClipListener.getNextClip(mBaseUIClip);
            mBeforeClip = mOnDownToGetNextClipListener.getBeforeClip(mBaseUIClip);
        }
    }


    private void handUp(boolean isLeft) {
        getParent().requestDisallowInterceptTouchEvent(false);
        mNextClip = null;
        mOnHandChangeListener.handUp(mBaseUIClip, mBaseUIClip.getOutPoint() - mBaseUIClip.getInPoint() - downDuration, isLeft);
        downDuration = 0;
    }

    /**
     * 左边把手移动
     * left Handle Move
     * */
    private void leftHandleMove(int dx, long changeValue) {
        long newTrimIn = (long) (mBaseUIClip.getTrimIn() + changeValue * mBaseUIClip.getSpeed());
        long newInPoint = (long) (mBaseUIClip.getInPoint() + changeValue * mBaseUIClip.getSpeed());
        long outTime = mBaseUIClip.getTrimOut();
        if (newTrimIn <= 0) {
            changeValue = (long) ((- mBaseUIClip.getTrimIn()) / mBaseUIClip.getSpeed());
        } else if (newTrimIn > (outTime - CommonData.MIN_SHOW_LENGTH_DURATION)) {
            changeValue = (long) (((outTime - CommonData.MIN_SHOW_LENGTH_DURATION) - mBaseUIClip.getTrimIn()) / mBaseUIClip.getSpeed());
        } else if (mBeforeClip != null && newInPoint < getClipOutPoint(mBeforeClip)) {
            changeValue = getClipOutPoint(mBeforeClip) - mBaseUIClip.getInPoint();
        } else if (newInPoint < 0) {
            changeValue = (long) (-mBaseUIClip.getInPoint() / mBaseUIClip.getSpeed());
        }

        if (!mBaseUIClip.canExceedLength()) {
            // AI字幕不能超过当前视频片段的左出点 AI subtitles cannot exceed the left exit point of the current video clip
            MeicamVideoClip clipInfo = EditorEngine.getInstance().getVideoClipInRange(TRACK_INDEX_MAIN, mBaseUIClip.getInPoint(),
                    getClipOutPoint(mBaseUIClip));
            if (clipInfo != null && newInPoint < clipInfo.getInPoint()) {
                changeValue = (long) (clipInfo.getInPoint() - mBaseUIClip.getInPoint() / mBaseUIClip.getSpeed());
            }
        }
        dx = PixelPerMicrosecondUtil.durationToLength(changeValue);
        newTrimIn = (long) (mBaseUIClip.getTrimIn() + changeValue * mBaseUIClip.getSpeed());
        mBaseUIClip.setTrimIn(newTrimIn);
        mBaseUIClip.setInPoint((mBaseUIClip.getInPoint() + changeValue));
        KeyFrameInfo keyFrameInfo = mBaseUIClip.getKeyFrameInfo();
        if (keyFrameInfo != null) {
            /*更改关键帧的入点,注意如果更新右边就不再更新关键帧的位置点了
            * Change the entry point of the key frame.
            * Note that if you update the right side, the position point of the key frame
            * will not be updated
            * */
            keyFrameInfo.moveKeyFrame(-changeValue, true);
        }
        RelativeLayout.LayoutParams params = (LayoutParams) getLayoutParams();
        params.leftMargin += dx;
        params.width -= dx;
        setLayoutParams(params);
        handChangeNvsValue(mBaseUIClip, true);
        mOnHandChangeListener.leftHandChange(dx, changeValue, mBaseUIClip);
    }


    /**
     * 获取输入片段出点
     * Gets the clipping point
     * */
    private long getClipOutPoint(BaseUIClip baseUIClip) {
        return (long) (baseUIClip.getInPoint() + (baseUIClip.getTrimOut() - baseUIClip.getTrimIn()) / baseUIClip.getSpeed());
    }

    private void rightHandleMove(int dx, long changeValue) {
        long newTrimOut = (long) (mBaseUIClip.getTrimOut() + changeValue * mBaseUIClip.getSpeed());
        long inTime = mBaseUIClip.getTrimIn();
        long oldDuration = (long) (mBaseUIClip.getTrimOut() - mBaseUIClip.getTrimIn() / mBaseUIClip.getSpeed());
        long newDuration = (long) (newTrimOut - mBaseUIClip.getTrimIn() / mBaseUIClip.getSpeed());
        long origineDuration = CommonData.DEFAULT_TRIM_IN * 2;
        if (mBaseUIClip.getType().equals(CommonData.CLIP_VIDEO) || mBaseUIClip.getType().equals(CommonData.CLIP_AUDIO)) {
            NvsAVFileInfo avFileInfo = NvsStreamingContext.getInstance().getAVFileInfo(mBaseUIClip.getFilePath());
            if (avFileInfo != null) {
                origineDuration = avFileInfo.getDuration();
            } else {
                LogUtils.e("avFileInfo==null");
            }
        }
        if (newTrimOut > origineDuration) {
            changeValue = (long) ((origineDuration - mBaseUIClip.getTrimOut()) / mBaseUIClip.getSpeed());
        } else if ((newTrimOut - inTime) < CommonData.MIN_SHOW_LENGTH_DURATION) {
            changeValue = (long) -((mBaseUIClip.getTrimOut() - inTime - CommonData.MIN_SHOW_LENGTH_DURATION) / mBaseUIClip.getSpeed());
        } else if (mNextClip != null) {
            // 不能超过旁边素材的位置 Do not exceed the position of the adjacent material
            long oldDurationInTimeline = (long) ((mBaseUIClip.getTrimOut() - mBaseUIClip.getTrimIn()) / mBaseUIClip.getSpeed());
            long newDurationInTimeline = oldDurationInTimeline + changeValue;
            if ((newDurationInTimeline + mBaseUIClip.getInPoint() > mNextClip.getInPoint())) {
                long duration = mNextClip.getInPoint() - mBaseUIClip.getInPoint();
                changeValue = duration - oldDurationInTimeline;
            }

        } else if (mTimeDuration < (newDuration + mBaseUIClip.getInPoint())) {
            // 不能超过素材长度，对音视频有限制，同时不能超出时间线长度 Do not exceed the length of the material, there are limits on audio and video, and do not exceed the length of the timeline
            long duration = mTimeDuration - mBaseUIClip.getInPoint();
            changeValue = (long) ((duration - oldDuration) / mBaseUIClip.getSpeed());
        }
        if (!mBaseUIClip.canExceedLength()) {
            // AI字幕不能超过当前视频片段的右出点 AI subtitles cannot exceed the right exit point of the current video clip
            MeicamVideoClip clipInfo = EditorEngine.getInstance().getVideoClipInRange(TRACK_INDEX_MAIN, mBaseUIClip.getInPoint(),
                    getClipOutPoint(mBaseUIClip));
            if (clipInfo != null && (newDuration + mBaseUIClip.getInPoint() > clipInfo.getOutPoint())) {
                long duration = clipInfo.getOutPoint() - mBaseUIClip.getInPoint();
                changeValue = (long) ((duration - oldDuration) / mBaseUIClip.getSpeed());
            }
        }
        dx = PixelPerMicrosecondUtil.durationToLength(changeValue);
        newTrimOut = (long) (mBaseUIClip.getTrimOut() + changeValue * mBaseUIClip.getSpeed());
        mBaseUIClip.setTrimOut(newTrimOut);
        RelativeLayout.LayoutParams params = (LayoutParams) getLayoutParams();
        params.width += dx;
        setLayoutParams(params);
        handChangeNvsValue(mBaseUIClip, false);
        mOnHandChangeListener.rightHandChange(dx, changeValue, mBaseUIClip);
    }

    private void handChangeNvsValue(BaseUIClip baseUIClip, boolean isLeft) {
        long newOutPoint;
        if (isLeft) {
            newOutPoint = baseUIClip.getInPoint();
        } else {
            newOutPoint = (long) (baseUIClip.getInPoint() + (baseUIClip.getTrimOut() - baseUIClip.getTrimIn()) / baseUIClip.getSpeed());
        }

        if (isLeft) {
            baseUIClip.setInPoint(newOutPoint);
        } else {
            baseUIClip.setOutPoint(newOutPoint);
            LogUtils.d("newOutP=" + newOutPoint + ",outP=" + baseUIClip.getOutPoint());
        }

       /* if (CommonData.CLIP_CAPTION.equals(baseUIClip.getType())) {
            NvsTimelineCaption caption = (NvsTimelineCaption) nvsObject;
            if (isLeft) {
                caption.changeInPoint(newOutPoint);
            } else {
                caption.changeOutPoint(newOutPoint);
            }
        } else if (CommonData.CLIP_COMPOUND_CAPTION.equals(baseUIClip.getType())) {
            NvsTimelineCompoundCaption nvsTimelineCompoundCaption = (NvsTimelineCompoundCaption) nvsObject;
            if (isLeft) {
                nvsTimelineCompoundCaption.changeInPoint(newOutPoint);
            } else {
                nvsTimelineCompoundCaption.changeOutPoint(newOutPoint);
            }
        } else if (CommonData.CLIP_STICKER.equals(baseUIClip.getType())) {
            NvsTimelineAnimatedSticker nvsTimelineAnimatedSticker = (NvsTimelineAnimatedSticker) nvsObject;
            if (isLeft) {
                nvsTimelineAnimatedSticker.changeInPoint(newOutPoint);
            } else {
                nvsTimelineAnimatedSticker.changeOutPoint(newOutPoint);
            }
        } else if (CommonData.CLIP_TIMELINE_FX.equals(baseUIClip.getType())) {
            NvsTimelineVideoFx nvsTimelineVideoFx = (NvsTimelineVideoFx) nvsObject;
            if (isLeft) {
                nvsTimelineVideoFx.changeInPoint(newOutPoint);
            } else {
                nvsTimelineVideoFx.changeOutPoint(newOutPoint);
            }
        } else if (CommonData.CLIP_VIDEO.equals(baseUIClip.getType())) {
            NvsVideoClip nvsVideoClip = (NvsVideoClip) nvsObject;
            if (isLeft) {
                nvsVideoClip.changeTrimInPoint(baseUIClip.getTrimIn(), false);
            } else {
                nvsVideoClip.changeTrimOutPoint(baseUIClip.getTrimOut(), false);
            }
        } else if (CommonData.CLIP_IMAGE.equals(baseUIClip.getType())) {
            NvsVideoClip videoClip = (NvsVideoClip) nvsObject;
            if (isLeft) {
                videoClip.changeTrimInPoint(baseUIClip.getTrimIn(), false);
            } else {
                videoClip.changeTrimOutPoint(baseUIClip.getTrimOut(), false);
            }
        } else if (CommonData.CLIP_AUDIO.equals(baseUIClip.getType())) {
            NvsAudioClip nvsAudioClip = (NvsAudioClip) nvsObject;
            if (isLeft) {
                nvsAudioClip.changeTrimInPoint(baseUIClip.getTrimIn(), false);
            } else {
                nvsAudioClip.changeTrimOutPoint(baseUIClip.getTrimOut(), false);
            }
        }*/
    }

    /**
     * Gets base ui clip.
     *
     * @return the base ui clip
     */
    public BaseUIClip getBaseUIClip() {
        return mBaseUIClip;
    }

    /**
     * Sets base ui clip.
     *
     * @param baseUIClip the base ui clip
     */
    public void setBaseUIClip(BaseUIClip baseUIClip) {
        mBaseUIClip = baseUIClip;
        resetAdsorb();
        refreshViewPosition();
    }

    public void resetAdsorb() {
        hasNearByBaseItem = false;
        hasNearByVideoRight = false;
        hasNearByVideoLeft = false;
        hasNearByBaseLine = false;
        nearByIndexBaseItem = -1;
        mLongPressBaseUIClipInpoint = mBaseUIClip.getInPoint();
        mLongPressBaseUIClipOutpoint = mBaseUIClip.getInPoint() + (mBaseUIClip.getTrimOut() - mBaseUIClip.getTrimIn());
        mCurrTimelinePosition = EditorEngine.getInstance().getCurrentTimelinePosition();
        lengthTimelineRight = PixelPerMicrosecondUtil.durationToLength(mCurrTimelinePosition - mLongPressBaseUIClipOutpoint);
        lengthTimelineLeft = PixelPerMicrosecondUtil.durationToLength(mCurrTimelinePosition - mLongPressBaseUIClipInpoint);
    }

    public HashMap<Integer, List<BaseUIClip>> getIntegerListHashMap() {
        return mIntegerListHashMap;
    }

    public void setIntegerListHashMap(HashMap<Integer, List<BaseUIClip>> mIntegerListHashMap) {
        this.mIntegerListHashMap = mIntegerListHashMap;
    }

    /**
     * Sets time duration.
     * 设置时间
     *
     * @param timeDuration the time duration
     */
    public void setTimeDuration(long timeDuration) {
        mTimeDuration = timeDuration;
    }

    private void refreshViewPosition() {
        RelativeLayout.LayoutParams params = new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, getResources().getDimensionPixelOffset(R.dimen.track_view_height));
        params.leftMargin = getChildTopMarginFromDuration(mBaseUIClip.getInPoint());
        long duration = (long) ((mBaseUIClip.getTrimOut() - mBaseUIClip.getTrimIn()) / mBaseUIClip.getSpeed());
        int width = PixelPerMicrosecondUtil.durationToLength(duration);
        params.width = width + mHandWidth * 2 - BaseItemView.BASE_ITEM_MARGIN;
        params.topMargin = getResources().getDimensionPixelOffset(R.dimen.track_view_height) * mBaseUIClip.getTrackIndex()
                + getResources().getDimensionPixelOffset(R.dimen.track_view_real_margin_top);
        setLayoutParams(params);
    }

    private int getChildTopMarginFromDuration(long duration) {
        return mStartPadding + PixelPerMicrosecondUtil.durationToLength(duration) - getResources().getDimensionPixelOffset(R.dimen.editor_timeline_view_hand_width);
    }

    /**
     * Sets on hand change listener.
     * 设置把手移动监听器
     *
     * @param onHandChangeListener the on hand change listener
     */
    public void setOnHandChangeListener(OnHandChangeListener onHandChangeListener) {
        mOnHandChangeListener = onHandChangeListener;
    }

    /**
     * The interface On hand change listener.
     * 手动更改监听的接口
     */
    public interface OnHandChangeListener {
        /**
         * Left hand change.
         * 左边把手移动
         *
         * @param dx          the dx dx
         * @param changeValue the change value 变化的值
         * @param baseUIClip  the base ui clip 基本ui剪辑
         */
        void leftHandChange(int dx, long changeValue, BaseUIClip baseUIClip);

        /**
         * Right hand change.
         * 右边把手移动
         *
         * @param dx          the dx
         * @param changeValue the change value 变化的值
         * @param baseUIClip  the base ui clip 基本ui剪辑
         */
        void rightHandChange(int dx, long changeValue, BaseUIClip baseUIClip);

        /**
         * Hand up.
         * 手指抬起
         *
         * @param baseUIClip     the base ui clip 基本ui剪辑
         * @param changeDuration the change duration改变的时长
         */
        void handUp(BaseUIClip baseUIClip, long changeDuration, boolean isLeft);
    }

    /**
     * Is hand down on hand view boolean.
     * 判断手指按下是否在把手控件上
     *
     * @param x the x
     * @param y the y
     * @return the boolean
     */
    public boolean isHandDownOnHandView(float x, float y) {
        boolean isOnLeft = isTouchPointInView(mTrackDragLeftHand, x, y);
        boolean isOnRight = isTouchPointInView(mTrackDragRightHand, x, y);
        return isOnLeft || isOnRight;
    }

    /**
     * Is touch point in view boolean.
     * 触点在是否在view范围内
     *
     * @param view the view
     * @param x    the x
     * @param y    the y
     * @return the boolean
     */
    public boolean isTouchPointInView(View view, float x, float y) {
        if (view == null) {
            return false;
        }
        int[] location = new int[2];
        view.getLocationOnScreen(location);
        int left = location[0];
        int top = location[1];
        int right = left + view.getMeasuredWidth();
        int bottom = top + view.getMeasuredHeight();
        return y >= top && y <= bottom && x >= left
                && x <= right;
    }

    /**
     * Gets hand width.
     * 获得手指宽度
     *
     * @return the hand width
     */
    public int getHandWidth() {
        return mHandWidth;
    }

    /**
     * Gets hand height.
     * 获得手指高度
     *
     * @return the hand height
     */
    public int getHandHeight() {
        return mHandHeight;
    }

    /**
     * Sets on down to get next clip listener.
     * 设置手指按下得到下一个剪辑监听
     *
     * @param onDownToGetNextClipListener the on down to get next clip listener
     */
    public void setOnDownToGetNextClipListener(OnDownToGetNextClip onDownToGetNextClipListener) {
        mOnDownToGetNextClipListener = onDownToGetNextClipListener;
    }

    /**
     * The interface On down to get next clip.
     * 开始拍摄下一个片段的接口
     */
    public interface OnDownToGetNextClip {
        /**
         * Gets next clip.
         * 获得下一个片段
         *
         * @param nowClip the now clip  现在片段
         * @return the next clip 下一个片段
         */
        BaseUIClip getNextClip(BaseUIClip nowClip);

        /**
         * Gets before clip.
         * 获得之前的片段
         *
         * @param nowClip the now clip 现在的片段
         * @return the before clip 之前的片段
         */
        BaseUIClip getBeforeClip(BaseUIClip nowClip);
    }
}
