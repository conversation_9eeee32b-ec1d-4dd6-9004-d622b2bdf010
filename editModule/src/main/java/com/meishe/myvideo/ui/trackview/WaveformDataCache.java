package com.meishe.myvideo.ui.trackview;

import java.util.HashMap;
import java.util.Map;

public class WaveformDataCache {
   private Map<String, WaveData> mWaveformData = new HashMap<>();
   private static WaveformDataCache sWaveformDataGeneratorWarp;

   public static WaveformDataCache getInstance() {
      if (sWaveformDataGeneratorWarp == null) {
         sWaveformDataGeneratorWarp = new WaveformDataCache();
      }
      return sWaveformDataGeneratorWarp;
   }

   public void addData(String path,  WaveData data){
      mWaveformData.put(path, data);
   }

   public WaveData getData(String key){
      return mWaveformData.get(key);
   }

   public void release(){
      mWaveformData.clear();
   }

   public static class WaveData{
      byte[] leftWave;
      long m_samplesPerGroup;

      public WaveData(byte[] leftWave, long m_samplesPerGroup) {
         this.leftWave = leftWave;
         this.m_samplesPerGroup = m_samplesPerGroup;
      }
   }
}
