package com.meishe.myvideo.ui.bean;

import android.annotation.SuppressLint;

import java.util.Map;
import java.util.HashMap;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> LiHangZhou
 * @CreateDate :2021/5/7 14:11
 * @Description :关键帧信息 The key frame info
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class KeyFrameInfo {
    /**
     * 关键帧集合，不使用List使用HashMap是为了扩展,没有使用UseSparseArrays是因为没有序列化
     * The key frame list
     */
    private final Map<Long, Info> keyFrameMap;
    /**
     * 当前选中的关键帧入点
     * The currently selected keyframe entry point
     */
    private long selectedPoint = -1;

    @SuppressLint("UseSparseArrays")
    public KeyFrameInfo() {
        keyFrameMap = new HashMap<>();
    }

    public Map<Long, Info> getKeyFrameMap() {
        return keyFrameMap;
    }

    /**
     * 添加关键帧
     * Add key frame
     *
     * @param inPoint the in point入点
     */
    public void addKeyFrame(long inPoint) {
        Info info = new Info();
        info.setAtTime(inPoint);
        info.setValid(true);
        keyFrameMap.put(inPoint, info);
    }

    /**
     * 删除关键帧
     * Delete key frame
     *
     * @param inPoint the in point入点
     */
    public void deleteKeyFrame(long inPoint) {
        keyFrameMap.remove(inPoint);
    }

    /**
     * 移动关键帧
     * Move key frame
     *
     * @param offset the move offset 移动时间差
     */
    @SuppressLint("UseSparseArrays")
    public void moveKeyFrame(long offset,boolean changeSelectedPoint) {
        if (offset == 0) {
            return;
        }
        for (Map.Entry<Long, Info> keyFrameEntry : keyFrameMap.entrySet()) {
            long newPoint = keyFrameEntry.getKey() + offset;
            Info info = keyFrameEntry.getValue();
            info.setAtTime(info.getAtTime() + offset);
            info.setValid(newPoint >= 0);
        }
        if(changeSelectedPoint){
            selectedPoint = selectedPoint + offset;
        }
    }

    public boolean hasKeyFrame(long inPoint) {
        return keyFrameMap.containsKey(inPoint);
    }

    public void clear() {
        keyFrameMap.clear();
        selectedPoint = -1;
    }

    public long getSelectedPoint() {
        return selectedPoint;
    }

    public void setSelectedPoint(long selectedPoint) {
        this.selectedPoint = selectedPoint;
    }

    public static class Info {
        /**
         * 是否有效
         * Valid
         */
        private boolean valid;
        /**
         * 关键帧的时间点
         * the key frame time dot
         */
        private long atTime;

        public static Info create(long inPoint) {
            Info info = new Info();
            info.setAtTime(inPoint);
            info.setValid(true);
            return info;
        }

        public boolean isValid() {
            return valid;
        }

        public void setValid(boolean valid) {
            this.valid = valid;
        }

        public long getAtTime() {
            return atTime;
        }

        public void setAtTime(long atTime) {
            this.atTime = atTime;
        }
    }
}
