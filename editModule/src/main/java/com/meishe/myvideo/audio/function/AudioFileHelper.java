package com.meishe.myvideo.audio.function;

import android.media.AudioFormat;
import android.text.TextUtils;
import android.util.Log;

import com.meishe.base.utils.FileUtils;
import com.meishe.base.utils.LogUtils;
import com.meishe.myvideo.audio.AudioRecorder;

import java.io.File;
import java.io.IOException;
import java.io.RandomAccessFile;


/**
 * The voice file help class is used to save related files
 * 语音文件帮助类 用于保存相关文件
 */
public class AudioFileHelper {

    /**
     * The constant TAG.
     * 常量标签
     */
    public static final String TAG = "AudioFileHelper";
    private final AudioFileListener mListener;
    private String mSavePath;
    private String mAudioName;
    private RandomAccessFile mRandomAccessFile;
    private File mTargetFile;
    private AudioRecorder.RecordConfig mConfig;

    /**
     * Instantiates a new Audio file helper.
     * 实例化一个新的音频文件帮助程序
     * @param listener the listener
     */
    public AudioFileHelper(AudioFileListener listener) {
        this.mListener = listener;
    }

    /**
     * Sets save path.
     * 设置保存路径
     * @param savePath the save path
     */
    public void setSavePath(String savePath) {
        this.mSavePath = savePath;
        FileUtils.createOrExistsDir(savePath);
    }

    /**
     * Sets audio name.
     * 设置音频的名字
     * @param name the name
     */
    public void setAudioName(String name) {
        this.mAudioName = name;
    }

    /**
     * Gets audio path.
     * 获取音频路径
     * @return the audio path
     */
    public String getAudioPath() {
        if (!TextUtils.isEmpty(mAudioName)) {
            return mSavePath + File.separator + mAudioName;
        }
        return null;
    }

    /**
     * Sets recorder config.
     * 设置录音配置
     * @param config the config
     */
    public void setRecorderConfig(AudioRecorder.RecordConfig config) {
        this.mConfig = config;
    }


    /**
     * Start.
     * 开始
     */
    public void start() {
        try {
            open(mSavePath + File.separator + mAudioName);
        } catch (IOException e) {
            e.printStackTrace();
            onSaveFileFailure(e.toString());
        }
    }

    /**
     * Save.
     * 保存
     *
     * @param data   the data
     * @param offset the offset
     * @param size   the size
     */
    public void save(byte[] data, int offset, int size) {
        if (mRandomAccessFile == null) {
            return;
        }
        try {
            write(mRandomAccessFile, data, offset, size);
        } catch (IOException e) {
            LogUtils.e(e);
            onSaveFileFailure(e.toString());

        }
    }

    /**
     * Finish.
     * 结束
     *
     * @param duration the duration
     */
    public void finish(long duration) {
        try {
            close(duration);
        } catch (IOException e) {
            LogUtils.e(e);
            onSaveFileFailure(e.toString());
        }
    }

    /**
     * 初始化录音参数
     * Initialize recording parameters
     * @param path 录音文件路径。 record file path
     * @throws IOException The IOException
     */
    private void open(String path) throws IOException {
        if (TextUtils.isEmpty(path)) {
            Log.d(TAG, "Path not set , data will not save");
            return;
        }
        if (this.mConfig == null) {
            Log.d(TAG, "RecordConfig not set , data will not save");
            return;
        }
        mTargetFile = new File(path);

        if (mTargetFile.exists()) {
            mTargetFile.delete();
        } else {
            File parentDir = mTargetFile.getParentFile();
            if (parentDir != null) {
                if (!parentDir.exists()) {
                    parentDir.mkdirs();
                }
            }
        }
        short bSamples;
        short nChannels;
        int sRate;
        if (mConfig.getAudioFormat() == AudioFormat.ENCODING_PCM_16BIT) {
            bSamples = 16;
        } else {
            bSamples = 8;
        }

        if (mConfig.getChannelConfig() == AudioFormat.CHANNEL_IN_MONO) {
            nChannels = 1;
        } else {
            nChannels = 2;
        }
        sRate = mConfig.getSampleRate();
        mRandomAccessFile = new RandomAccessFile(mTargetFile, "rw");
        mRandomAccessFile.setLength(0);
        // Set file length to
        // 0, to prevent unexpected behavior in case the file already existed
        // 16K、16bit、单声道
        /* RIFF header */
        // riff id
        mRandomAccessFile.writeBytes("RIFF");
        //riff chunk size *PLACEHOLDER*
        mRandomAccessFile.writeInt(0);
        //wave type
        mRandomAccessFile.writeBytes("WAVE");

        /* fmt chunk */
        // fmt id
        mRandomAccessFile.writeBytes("fmt ");
        // fmt chunk size
        mRandomAccessFile.writeInt(Integer.reverseBytes(16));
        // AudioFormat,1 for PCM
        mRandomAccessFile.writeShort(Short.reverseBytes((short) 1));
        // Number of channels, 1 for mono, 2 for stereo
        mRandomAccessFile.writeShort(Short.reverseBytes(nChannels));
        // Sample rate
        mRandomAccessFile.writeInt(Integer.reverseBytes(sRate));
        // Byte rate,SampleRate*NumberOfChannels*BitsPerSample/8
        mRandomAccessFile.writeInt(Integer.reverseBytes(sRate * bSamples * nChannels / 8));
        // Block align, NumberOfChannels*BitsPerSample/8
        mRandomAccessFile.writeShort(Short.reverseBytes((short) (nChannels * bSamples / 8)));
        // Bits per sample
        mRandomAccessFile.writeShort(Short.reverseBytes(bSamples));

        /* data chunk */
        // data id
        mRandomAccessFile.writeBytes("data");
        // data chunk size *PLACEHOLDER*
        mRandomAccessFile.writeInt(0);
        LogUtils.d( "wav path: " + path);

    }

    private void write(RandomAccessFile file, byte[] data, int offset, int size) throws IOException {
        file.write(data, offset, size);
        LogUtils.d("fwrite: " + size);
    }

    private void close(long duration) throws IOException {
        try {
            if (mRandomAccessFile == null) {
                onSaveFileFailure("File save error exception occurs");
                return;
            }
            // riff chunk size
            mRandomAccessFile.seek(4);
            mRandomAccessFile.writeInt(Integer.reverseBytes((int) (mRandomAccessFile.length() - 8)));
            // data chunk size
            mRandomAccessFile.seek(40);
            mRandomAccessFile.writeInt(Integer.reverseBytes((int) (mRandomAccessFile.length() - 44)));

            LogUtils.d("wav size: " + mRandomAccessFile.length());
            if (mListener != null) {
                mListener.onSuccess(mSavePath + mAudioName, duration);
            }

        } finally {
            if (mRandomAccessFile != null) {
                mRandomAccessFile.close();
                mRandomAccessFile = null;
            }

        }
    }

    /**
     * Cancel.
     * 取消
     */
    public void cancel() {
        if (mRandomAccessFile == null) {
            return;
        }
        if (mTargetFile == null) {
            return;
        }
        if (mTargetFile.exists()) {
            mTargetFile.delete();
        }
        mRandomAccessFile = null;
        mTargetFile = null;

    }

    private void onSaveFileFailure(String error) {
        if (!TextUtils.isEmpty(mAudioName)) {
            File file = new File(mSavePath + mAudioName);
            if (file.exists()) {
                file.delete();
            }
        }
        if (mListener != null) {
            mListener.onFailure(error);
        }
    }

}
