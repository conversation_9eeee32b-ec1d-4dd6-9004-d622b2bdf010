package com.meishe.myvideo.activity;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.fragment.app.Fragment;
import androidx.viewpager.widget.ViewPager;

import com.meishe.base.adapter.CommonFragmentAdapter;
import com.meishe.base.constants.Constants;
import com.meishe.base.model.BaseActivity;
import com.meishe.base.utils.FormatUtils;
import com.meishe.myvideo.R;
import com.meishe.myvideo.bean.MusicInfo;
import com.meishe.myvideo.fragment.MusicFragment;
import com.meishe.myvideo.util.AudioPlayer;
import com.meishe.myvideo.view.CutMusicView;
import com.meishe.third.tablayout.SlidingTabLayout;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static com.meishe.logic.constant.PagerConstants.BUNDLE_DATA;


/**
 * 本地音乐选择页面
 * Local music selection page
 */
public class SelectMusicActivity extends BaseActivity {
    private RelativeLayout mRlSelectedMusic;
    private TextView mTvSelectedMusicName, mTvAuthor, mTvMusicTime;
    private CutMusicView mCutMusicView;
    private Button mBtUseMusic;
    private List<Fragment> mFragmentList;
    private MusicInfo mMusicInfo;
    private boolean isPlaying;
    private ImageView mIvBack;
    private boolean isBackState;

    @Override
    protected void onResume() {
        super.onResume();
        isBackState = false;
        if (mMusicInfo != null && isPlaying) {
            AudioPlayer.getInstance().startPlay();
        }
    }

    @Override
    protected void onStop() {
        super.onStop();
        if (mMusicInfo != null && isPlaying) {
            AudioPlayer.getInstance().stopPlay();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        AudioPlayer.getInstance().setPlayListener(null);
    }

    @Override
    protected int bindLayout() {
        return R.layout.activity_select_music;
    }

    @Override
    protected void initData(Bundle savedInstanceState) {
        mFragmentList = new ArrayList<>(3);
        mFragmentList.add(MusicFragment.create(false, mListener));
        mFragmentList.add(MusicFragment.create(true, mListener));
    }

    @Override
    protected void initView() {
        SlidingTabLayout tabLayout = findViewById(R.id.tab_layout);
        ViewPager viewPager = findViewById(R.id.select_music_viewpager);
        mRlSelectedMusic = findViewById(R.id.rl_selected_music_parent);
        mTvSelectedMusicName = findViewById(R.id.tv_music_name);
        mTvAuthor = findViewById(R.id.tv_music_author);
        mTvMusicTime = findViewById(R.id.tv_music_time);
        mCutMusicView = findViewById(R.id.select_music_cut_view);
        mBtUseMusic = findViewById(R.id.bt_use_music);
        mIvBack = findViewById(R.id.iv_back);

        viewPager.setAdapter(new CommonFragmentAdapter(getSupportFragmentManager(), mFragmentList));
        tabLayout.setViewPager(viewPager, Arrays.asList(getResources().getStringArray(R.array.music_fragment_title)));
        initListener();
    }

    private void initListener() {
        mBtUseMusic.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                releaseAudioPlayer();
            }
        });
        mCutMusicView.setOnSeekBarChangedListener(new CutMusicView.OnSeekBarChanged() {
            @Override
            public void onLeftValueChange(long var) {
                if (mMusicInfo != null) {
                    String time = FormatUtils.microsecond2Time(var) + "/" +
                            FormatUtils.microsecond2Time(mMusicInfo.getTrimOut());
                    mTvMusicTime.setText(time);
                }
            }

            @Override
            public void onRightValueChange(long var) {
                if (mMusicInfo != null) {
                    String time = FormatUtils.microsecond2Time(mMusicInfo.getTrimIn()) + "/" +
                            FormatUtils.microsecond2Time(var);
                    mTvMusicTime.setText(time);
                }
            }

            @Override
            public void onCenterTouched(long left, long right) {

            }

            @Override
            public void onUpTouched(boolean touch_left, long left, long right) {
                if (mMusicInfo != null) {
                    mMusicInfo.setTrimIn(left);
                    mMusicInfo.setTrimOut(right);
                    if (touch_left) {
                        if (isPlaying) {
                            AudioPlayer.getInstance().seekPosition(left);
                        }
                        mCutMusicView.setIndicator(left);
                    }
                }
            }
        });

        AudioPlayer.getInstance().setPlayListener(new AudioPlayer.OnPlayListener() {
            @Override
            public void onMusicPlay() {

            }

            @Override
            public void onMusicStop() {
                if (isBackState) {
                    Intent intent = new Intent();
                    if (mMusicInfo != null) {
                        intent.putExtra(BUNDLE_DATA, mMusicInfo);
                    }
                    setResult(RESULT_OK, intent);
                    finish();
                }
            }

            @Override
            public void onGetCurrentPos(long curPos) {
                mCutMusicView.setIndicator(curPos);
                if(mMusicInfo != null){
                    mTvMusicTime.setText(String.format("%s/%s", FormatUtils.microsecond2Time(curPos), FormatUtils.microsecond2Time(mMusicInfo.getTrimOut())));
                }
            }
        });

        mIvBack.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onBackPressed();
            }
        });
    }

    private void releaseAudioPlayer() {
        isBackState = true;
        AudioPlayer.getInstance().stopPlay();
        AudioPlayer.getInstance().destroyPlayer();
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
        overridePendingTransition(0, 0);
        mMusicInfo = null;
        releaseAudioPlayer();
    }

    /**
     * 处理音乐播放
     * deal music play
     */
    public void dealMusicPlay() {
        if (mMusicInfo == null) {
            return;
        }
        if (isPlaying) {
            if (mRlSelectedMusic.getVisibility() != View.VISIBLE) {
                mRlSelectedMusic.setVisibility(View.VISIBLE);
            }
            mTvSelectedMusicName.setText(mMusicInfo.getTitle());
            mTvAuthor.setText(mMusicInfo.getArtist());
            mCutMusicView.setMinDuration(Constants.MUSIC_MIN_DURATION);
            mCutMusicView.setCanTouchCenterMove(false);
            mCutMusicView.setMaxDuration(mMusicInfo.getDuration());
            mCutMusicView.setInPoint(0);
            mCutMusicView.setOutPoint(mMusicInfo.getDuration());
            mCutMusicView.postDelayed(new Runnable() {
                @Override
                public void run() {
                    mCutMusicView.setCutLayoutWidth(mCutMusicView.getWidth()
                            - mCutMusicView.getPaddingEnd()
                            - mCutMusicView.getPaddingStart());
                    mCutMusicView.reLayout();
                    // mCutMusicView.requestLayout();
                }
            }, 100);
            mMusicInfo.setTrimIn(0);
            mMusicInfo.setTrimOut(mMusicInfo.getDuration());
            AudioPlayer.getInstance().setCurrentMusic(mMusicInfo, true);
            AudioPlayer.getInstance().startPlay();
        } else {
            if (mRlSelectedMusic.getVisibility() == View.VISIBLE) {
                mRlSelectedMusic.setVisibility(View.GONE);
            }
            AudioPlayer.getInstance().stopPlay();
        }
    }

    private final MusicFragment.EventListener mListener = new MusicFragment.EventListener() {
        @Override
        public void onMusicSelected(MusicInfo info, boolean isLocalMusic) {
            mMusicInfo = info;
            isPlaying = true;
            dealMusicPlay();
        }

        @Override
        public void onMusicUnselected(boolean isLocalMusic) {
            isPlaying = false;
            dealMusicPlay();
        }
    };
}
