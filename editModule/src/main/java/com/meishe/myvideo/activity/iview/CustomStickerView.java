package com.meishe.myvideo.activity.iview;

import com.meishe.base.model.IBaseView;
import com.meishe.engine.interf.IBaseInfo;

import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateDate :2020/12/14 17:09
 * @Description :自定义贴纸View
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public interface CustomStickerView extends IBaseView {
    /**
     * 当贴纸数据返回
     * When the sticker data is returned
     *
     * @param stickerList the sticker list
     */
    void onStickerBack(List<IBaseInfo> stickerList);
}
