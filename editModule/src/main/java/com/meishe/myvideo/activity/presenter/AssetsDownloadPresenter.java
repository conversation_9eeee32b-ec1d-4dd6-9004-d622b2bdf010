package com.meishe.myvideo.activity.presenter;

import com.meishe.base.model.Presenter;
import com.meishe.engine.asset.bean.AssetInfo;
import com.meishe.engine.asset.bean.AssetList;
import com.meishe.engine.asset.AssetsManager;
import com.meishe.engine.asset.bean.RequestParam;
import com.meishe.myvideo.activity.iview.AssetsDownloadView;
import com.meishe.net.custom.BaseResponse;
import com.meishe.net.custom.RequestCallback;
import com.meishe.net.custom.SimpleDownListener;
import com.meishe.net.model.Progress;

import java.io.File;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2020/12/22 14:59
 * @Description :素材获取和下载逻辑处理类 The presenter for getting assets information and downloading assets.
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class AssetsDownloadPresenter extends Presenter<AssetsDownloadView> {
    private AssetsManager mAssetApi;
    private int mCurrentRequestPage = 0;
    private boolean mHasNext;

    public AssetsDownloadPresenter() {
        mAssetApi = AssetsManager.get();
    }

    /**
     * 刷新 refresh
     *
     * @param assetType 素材类型 Type of asset.
     */
    public void refresh(int assetType) {
        mCurrentRequestPage = 0;
        getAssetList(assetType);
    }

    /**
     * 加载更多
     * load more
     *
     * @param assetType 素材类型 Type of asset.
     */
    public void loadMore(int assetType) {
        getAssetList(assetType);
    }

    /**
     * 获取资源列表
     * Get asset list
     *
     * @param assetType asset type
     */
    private void getAssetList(int assetType) {
        mAssetApi.getAssetList(new RequestParam(assetType, -1, 20, -1), AssetInfo.AspectRatio_All, 0, mCurrentRequestPage, new RequestCallback<AssetList>() {
            @Override
            public void onSuccess(BaseResponse<AssetList> response) {
                if (response.getData() != null) {
                    mHasNext = response.getData().hasNext;
                    getView().onAssetListBack(response.getData().realAssetList, mCurrentRequestPage != 0);
                    if (response.getData().list != null) {
                        mCurrentRequestPage++;
                    }
                } else {
                    getView().onLoadAssetFailed(mCurrentRequestPage != 0);
                }
            }

            @Override
            public void onError(BaseResponse<AssetList> response) {
                getView().onLoadAssetFailed(mCurrentRequestPage != 0);
            }
        });
    }

    /**
     * 是否还有数据 Whether does more data exist.
     *
     * @return true: yes; false: no
     */
    public boolean hasNext() {
        return mHasNext;
    }

    /**
     * 下载素材 Download assets
     *
     * @param assetInfo the asset info
     * @param position  the asset index of list
     */
    public void downloadAsset(final AssetInfo assetInfo, final int position) {
        mAssetApi.downloadAsset(assetInfo, true, new SimpleDownListener(assetInfo.getDownloadUrl()) {
            @Override
            public void onProgress(Progress progress) {
                super.onProgress(progress);
                //assetInfo.setDownloadProgress((int) (progress.currentSize/progress.totalSize));
                getView().onDownloadUpdate(position);
            }

            @Override
            public void onError(Progress progress) {
                super.onError(progress);
                getView().onDownloadUpdate(position);
            }

            @Override
            public void onFinish(File file, Progress progress) {
                super.onFinish(file, progress);
                getView().onDownloadUpdate(position);
            }
        });
    }
}
