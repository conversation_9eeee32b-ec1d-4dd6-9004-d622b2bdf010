package com.meishe.myvideo.activity;


import android.content.Intent;
import android.graphics.RectF;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;

import com.meishe.base.model.BaseMvpActivity;
import com.meishe.base.bean.MediaData;

import com.meishe.base.manager.AppManager;
import com.meishe.base.utils.ImageLoader;
import com.meishe.base.utils.LogUtils;
import com.meishe.base.utils.ThreadUtils;
import com.meishe.base.utils.Utils;
import com.meishe.base.view.CustomTitleBar;
import com.meishe.engine.interf.IBaseInfo;
import com.meishe.myvideo.R;
import com.meishe.myvideo.activity.iview.CustomStickerView;
import com.meishe.myvideo.activity.presenter.CustomStickerPresenter;
import com.meishe.myvideo.view.CustomStickerDrawRect;

import java.util.List;

import static com.meishe.logic.constant.PagerConstants.BUNDLE_DATA;
import static com.meishe.logic.constant.PagerConstants.FILE_PATH;
import static com.meishe.myvideo.view.CustomStickerDrawRect.CIRCLE_MODE;
import static com.meishe.myvideo.view.CustomStickerDrawRect.FREEDOM_MODE;
import static com.meishe.myvideo.view.CustomStickerDrawRect.SQUARE_MODE;

/**
 * 自定义贴纸裁剪页面
 * Custom sticker clipping page
 */
public class CustomStickerClipActivity extends BaseMvpActivity<CustomStickerPresenter> implements CustomStickerView, View.OnClickListener {

    private ImageView mIvStickerImage;
    private ImageView mIvFreedom;
    private ImageView mIvCircle;
    private ImageView mIvSquare;
    private CustomStickerDrawRect mStickerRectView;
    private RectF mClipRectF;
    private TextView mTvConfirm;

    private String mPicturePath;


    @Override
    protected int bindLayout() {
        return R.layout.activity_custom_animate_sticker;
    }

    @Override
    protected void initData(Bundle savedInstanceState) {
        Intent intent = getIntent();
        if (intent != null) {
            MediaData data = intent.getParcelableExtra(BUNDLE_DATA);
            if (data != null) {
                mPicturePath = data.getPath();
            }
        }
    }

    @Override
    protected void initView() {
        CustomTitleBar titleBar = findViewById(R.id.title_bar);
        mIvStickerImage = findViewById(R.id.iv_sticker_image);
        mIvFreedom = findViewById(R.id.iv_freedom);
        mIvCircle = findViewById(R.id.iv_circle);
        mIvSquare = findViewById(R.id.iv_square);
        mStickerRectView = findViewById(R.id.customDrawRect);
        mTvConfirm = findViewById(R.id.tv_confirm);
        titleBar.setTextCenter("");
        initListener();
        if (!TextUtils.isEmpty(mPicturePath)) {
            mPresenter.setStickerImageSize(getResources().getDimensionPixelOffset(R.dimen.dp_px_192),
                    getResources().getDimensionPixelOffset(R.dimen.dp_px_440), mPicturePath, mIvStickerImage);
            ImageLoader.loadUrl(this, mPicturePath, mIvStickerImage);
        }
        delayToDealOnUiThread(new Runnable() {
            @Override
            public void run() {
                /*
                 * 用到了一些view的宽高，所以延迟主线程加载，当然也可以使用view.post();
                 * We use some of the width and height of the view, so we can delay loading the main thread, but we can also use View.post ();
                 * */
                initDrawRect();
            }
        });
    }

    private void initListener() {
        mIvFreedom.setOnClickListener(this);
        mIvCircle.setOnClickListener(this);
        mIvSquare.setOnClickListener(this);
        mTvConfirm.setOnClickListener(this);
    }

    private void initDrawRect() {
        mClipRectF = new RectF();
        mStickerRectView.setRectArea(0, 0, mIvStickerImage.getWidth(), mIvStickerImage.getHeight());
        updateStickerView(FREEDOM_MODE);
        ConstraintLayout.LayoutParams drawRectLayoutParams = (ConstraintLayout.LayoutParams) mStickerRectView.getLayoutParams();
        if (drawRectLayoutParams != null) {
            drawRectLayoutParams.width = mIvStickerImage.getWidth() + mStickerRectView.getScaleViewWidth();
            drawRectLayoutParams.height = mIvStickerImage.getHeight() + mStickerRectView.getScaleViewHeight();
            mStickerRectView.setLayoutParams(drawRectLayoutParams);
        }
        mStickerRectView.setX(mIvStickerImage.getX());
        mStickerRectView.setY(mIvStickerImage.getY());
        mStickerRectView.setOnDrawRectListener(new CustomStickerDrawRect.OnDrawRectListener() {
            @Override
            public void onDrawRect(RectF rectF) {
                mClipRectF = rectF;
            }
        });
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
//        if (mPresenter.isFastClick()) {
//            return;
//        }
        if (id == R.id.iv_freedom) {
            if (mStickerRectView.getViewMode() == FREEDOM_MODE) {
                return;
            }
            mIvFreedom.setImageResource(R.mipmap.custom_free_select);
            mIvCircle.setImageResource(R.mipmap.custom_circle);
            mIvSquare.setImageResource(R.mipmap.custom_square);
            updateStickerView(FREEDOM_MODE);
        } else if (id == R.id.iv_circle) {
            if (mStickerRectView.getViewMode() == CIRCLE_MODE) {
                return;
            }
            mIvFreedom.setImageResource(R.mipmap.custom_free);
            mIvCircle.setImageResource(R.mipmap.custom_circle_select);
            mIvSquare.setImageResource(R.mipmap.custom_square);
            updateStickerView(CIRCLE_MODE);
        } else if (id == R.id.iv_square) {
            if (mStickerRectView.getViewMode() == SQUARE_MODE) {
                return;
            }
            mIvFreedom.setImageResource(R.mipmap.custom_free);
            mIvCircle.setImageResource(R.mipmap.custom_circle);
            mIvSquare.setImageResource(R.mipmap.custom_square_select);
            updateStickerView(SQUARE_MODE);
        } else if (id == R.id.tv_confirm) {
            if (Utils.isFastClick()) {
                return;
            }
            mTvConfirm.setClickable(false);
            ThreadUtils.getCachedPool().execute(new Runnable() {
                @Override
                public void run() {
                    LogUtils.d("mClipRectF="+mClipRectF+",getX="+mStickerRectView.getX()+",getY="+mStickerRectView.getY());
                    /*
                     * 同步一下坐标系
                     * Synchronize the coordinate system
                     * */
                    final String picturePath = mPresenter.createAndSaveBitmap(mPicturePath, mIvStickerImage.getWidth(),
                            mIvStickerImage.getHeight(), mClipRectF, mStickerRectView.getViewMode() == CIRCLE_MODE);
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            Bundle bundle = new Bundle();
                            bundle.putString(FILE_PATH, picturePath);
                            AppManager.getInstance().jumpActivity(CustomStickerClipActivity.this, CustomStickerEffectActivity.class, bundle);
                            finish();
                        }
                    });
                }
            });
        }
    }

    /**
     * 更新自定义贴纸选中框
     * update CustomStickerDrawRect
     */
    private void updateStickerView(int viewMode) {
        if (mClipRectF == null) {
            return;
        }
        int offset = 200;
        int minValue = Math.min(mIvStickerImage.getWidth(), mIvStickerImage.getHeight());
        int newOffset = 2 * offset;
        if (minValue >= newOffset) {
            mClipRectF.set(mIvStickerImage.getWidth() / 2.0f - offset,
                    mIvStickerImage.getHeight() / 2.0f - offset,
                    mIvStickerImage.getWidth() / 2.0f + offset,
                    mIvStickerImage.getHeight() / 2.0f + offset);
        } else if (minValue == mIvStickerImage.getWidth()) {
            mClipRectF.set(0, mIvStickerImage.getHeight() * 0.5f - mIvStickerImage.getWidth() * 0.5f,
                    mIvStickerImage.getWidth(), mIvStickerImage.getHeight() * 0.5f + mIvStickerImage.getWidth() * 0.5f);
        } else {
            mClipRectF.set(mIvStickerImage.getWidth() * 0.5f - mIvStickerImage.getHeight() * 0.5f,
                    0, mIvStickerImage.getWidth() * 0.5f + mIvStickerImage.getHeight() * 0.5f,
                    mIvStickerImage.getHeight());
        }
        mStickerRectView.setDrawRect(mClipRectF, viewMode);
    }

    @Override
    public void onStickerBack(List<IBaseInfo> stickerList) {

    }
}
