package com.meishe.myvideo.activity.presenter;

import android.content.Context;
import android.graphics.Bitmap;
import android.text.TextUtils;

import com.meicam.sdk.NvsAVFileInfo;
import com.meicam.sdk.NvsColor;
import com.meicam.sdk.NvsIconGenerator;
import com.meicam.sdk.NvsMediaFileConvertor;
import com.meicam.sdk.NvsRational;
import com.meicam.sdk.NvsSize;
import com.meicam.sdk.NvsStreamingContext;
import com.meicam.sdk.NvsTimeline;
import com.meicam.sdk.NvsTimelineCaption;
import com.meicam.sdk.NvsVideoResolution;
import com.meishe.base.bean.FloatPoint;
import com.meishe.base.bean.MediaData;
import com.meishe.base.constants.Constants;
import com.meishe.base.model.Presenter;
import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.FileUtils;
import com.meishe.base.utils.GsonUtils;
import com.meishe.base.utils.ImageUtils;
import com.meishe.base.utils.LogUtils;
import com.meishe.base.utils.StringUtils;
import com.meishe.base.utils.ThreadUtils;
import com.meishe.base.utils.ToastUtils;
import com.meishe.base.utils.Utils;
import com.meishe.engine.EditorEngine;
import com.meishe.engine.EngineCallbackManager;
import com.meishe.engine.asset.AssetsManager;
import com.meishe.engine.asset.bean.AssetInfo;
import com.meishe.engine.asset.bean.AssetList;
import com.meishe.engine.bean.AnimationData;
import com.meishe.engine.bean.ClipInfo;
import com.meishe.engine.bean.CommonData;
import com.meishe.engine.bean.KeyFrameProcessor;
import com.meishe.engine.bean.MeicamAudioClip;
import com.meishe.engine.bean.MeicamAudioTrack;
import com.meishe.engine.bean.MeicamCaptionClip;
import com.meishe.engine.bean.MeicamCompoundCaptionClip;
import com.meishe.engine.bean.MeicamFxParam;
import com.meishe.engine.bean.MeicamKeyFrame;
import com.meishe.engine.bean.MeicamStickerCaptionTrack;
import com.meishe.engine.bean.MeicamStickerClip;
import com.meishe.engine.bean.MeicamTheme;
import com.meishe.engine.bean.MeicamTimeline;
import com.meishe.engine.bean.MeicamTimelineVideoFilterAndAdjustClip;
import com.meishe.engine.bean.MeicamTimelineVideoFx;
import com.meishe.engine.bean.MeicamTimelineVideoFxClip;
import com.meishe.engine.bean.MeicamTimelineVideoFxTrack;
import com.meishe.engine.bean.MeicamTransition;
import com.meishe.engine.bean.MeicamVideoClip;
import com.meishe.engine.bean.MeicamVideoFx;
import com.meishe.engine.bean.MeicamVideoTrack;
import com.meishe.engine.command.AudioCommand;
import com.meishe.engine.command.AudioTrackCommand;
import com.meishe.engine.command.ClipCommand;
import com.meishe.engine.command.KeyFrameHolderCommand;
import com.meishe.engine.command.TimelineCommand;
import com.meishe.engine.command.TransitionCommand;
import com.meishe.engine.command.VideoClipCommand;
import com.meishe.engine.command.VideoFxCommand;
import com.meishe.engine.command.VideoTrackCommand;
import com.meishe.engine.constant.NvsConstants;
import com.meishe.engine.interf.IKeyFrameProcessor;
import com.meishe.engine.observer.EngineCallbackObserver;
import com.meishe.engine.util.ColorUtil;
import com.meishe.engine.util.ConvertFileManager;
import com.meishe.engine.util.IConvertManager;
import com.meishe.engine.util.PathUtils;
import com.meishe.engine.util.TimelineFxBinder;
import com.meishe.engine.util.WhiteList;
import com.meishe.engine.view.ConvertProgressPop;
import com.meishe.libplugin.PluginManager;
import com.meishe.libplugin.user.IUserPlugin;
import com.meishe.logic.bean.SettingParameter;
import com.meishe.logic.manager.PreferencesManager;
import com.meishe.myvideo.BuildConfig;
import com.meishe.myvideo.R;
import com.meishe.myvideo.activity.iview.DraftEditView;
import com.meishe.myvideo.manager.MenuDataManager;
import com.meishe.myvideo.ui.bean.AnimationInfo;
import com.meishe.myvideo.ui.bean.BaseTrackClip;
import com.meishe.myvideo.ui.bean.BaseUIClip;
import com.meishe.myvideo.ui.bean.ITrackClip;
import com.meishe.myvideo.ui.bean.KeyFrameInfo;
import com.meishe.myvideo.ui.bean.LineRegionClip;
import com.meishe.myvideo.ui.bean.SpeedInfo;
import com.meishe.myvideo.ui.bean.ThumbnailClip;
import com.meishe.myvideo.ui.tools.ConvertUtil;
import com.meishe.myvideo.view.MYEffectTargetMenuView;
import com.meishe.net.custom.BaseResponse;
import com.meishe.net.custom.RequestCallback;
import com.meishe.player.fragment.VideoFragment;
import com.meishe.speaker.VoiceDictationHelperWrapper;
import com.meishe.speaker.bean.VoiceParam;

import java.io.File;
import java.security.InvalidParameterException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.Hashtable;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.meicam.sdk.NvsStreamingContext.STREAMING_ENGINE_COMPILE_FLAG_IGNORE_TIMELINE_VIDEO_SIZE;
import static com.meicam.sdk.NvsStreamingContext.STREAMING_ENGINE_SEEK_FLAG_BUDDY_HOST_VIDEO_FRAME;
import static com.meishe.base.constants.Constants.TRACK_INDEX_MAIN;
import static com.meishe.engine.bean.CommonData.CLIP_HOLDER;
import static com.meishe.engine.bean.CommonData.CLIP_VIDEO;
import static com.meishe.engine.bean.CommonData.MAIN_TRACK_INDEX;
import static com.meishe.engine.bean.CommonData.TYPE_PACKAGE;
import static com.meishe.engine.bean.MeicamVideoFx.SubType.SUB_TYPE_ALPHA;
import static com.meishe.engine.bean.MeicamVideoFx.SubType.SUB_TYPE_MASTER_KEYER;
import static com.meishe.engine.bean.MeicamVideoFx.SubType.SUB_TYPE_SEGMENT;
import static com.meishe.engine.constant.NvsConstants.ALPHA_CLIP_TRIM_USED;
import static com.meishe.engine.constant.NvsConstants.ALPHA_FILE;
import static com.meishe.engine.constant.NvsConstants.INVERSE_SEGMENT;
import static com.meishe.engine.constant.NvsConstants.OUTPUT_MASK;
import static com.meishe.engine.constant.NvsConstants.SEGMENTATION;
import static com.meishe.engine.constant.NvsConstants.SET_ALPHA;
import static com.meishe.engine.constant.NvsConstants.TYPE_BUILD_IN;
import static com.meishe.engine.constant.NvsConstants.TYPE_PROPERTY;
import static com.meishe.engine.constant.NvsConstants.TYPE_RAW_BUILTIN;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> LiHangZhou
 * @CreateDate :2020/12/17 20:22
 * @Description :编辑页面逻辑处理类 DraftEditPresenter
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class DraftEditPresenter extends Presenter<DraftEditView> {
    /**
     * 删除主轨道的补黑片段
     * Delete the black patch clip of the main track
     */
    public static final int DELETE_HOLDER = 0;
    /**
     * 添加主轨道补黑片段
     * Add the black patch clip of the main track
     */
    public static final int ADD_HOLDER = 1;
    /**
     * 更改主轨道补黑片段
     * Change the black patch clip of the main track
     */
    public static final int CHANGE_HOLDER = 2;
    private final EditorEngine mEditorEngine;
    /**
     * 流媒体上下文
     * Streaming context
     */
    private final NvsStreamingContext mStreamingContext;

    /**
     * 时间线，编辑场景的时间轴实体
     * Timeline. Edit the timeline entity of the scene
     */
    private MeicamTimeline mMeicamTimeline;

    public DraftEditPresenter() {
        mEditorEngine = EditorEngine.getInstance();
        mStreamingContext = mEditorEngine.getStreamingContext();
    }

    /**
     * 获取真正的时长
     * Get real duration
     *
     * @param mediaData the media data
     */
    public long getRealDuration(MediaData mediaData) {
        if (TextUtils.isEmpty(mediaData.getPath())) {
            return 0;
        }
        long duration = mediaData.getDuration() * 1000;
        NvsAVFileInfo avFileInfo = mStreamingContext.getAVFileInfo(mediaData.getPath());
        if (mediaData.getType() == MediaData.TYPE_VIDEO) {
            if (avFileInfo != null) {
                duration = avFileInfo.getDuration();
            }
        } else {
            duration = CommonData.DEFAULT_LENGTH;
        }
        return duration;
    }

    public void initEngine(ArrayList<MediaData> mediaList) {
        MeicamTimeline timeline = mEditorEngine.createTimeline(mediaList);
        mEditorEngine.setCurrentTimeline(timeline);
        mMeicamTimeline = timeline;
    }

    public NvsStreamingContext getStreamingContext() {
        return mStreamingContext;
    }

    public EditorEngine getEngine() {
        return mEditorEngine;
    }


    public MeicamTimeline getTimeline() {
        if (mMeicamTimeline == null) {
            mMeicamTimeline = mEditorEngine.getCurrentTimeline();
        }
        return mMeicamTimeline;
    }

    /**
     * 获取轨道数量
     * Get video track count
     */
    public int getVideoTrackCount() {
        if (mMeicamTimeline == null) {
            return -1;
        }
        return mMeicamTimeline.videoTrackCount();
    }

    /**
     * 检查主题字幕
     * Check theme caption
     *
     * @return true had ,false not
     */
    public boolean checkThemeCaption() {
        if (mMeicamTimeline == null) {
            return false;
        }
        MeicamCaptionClip firstCaption = mMeicamTimeline.getFirstCaption();
        if (firstCaption != null && firstCaption.getThemeType() == NvsTimelineCaption.ROLE_IN_THEME_TITLE) {
            if (mMeicamTimeline.getMeicamTheme() != null) {
                firstCaption.setText(mMeicamTimeline.getMeicamTheme().getThemeTitleText());
            }
            return true;
        }
        return false;
    }

    /**
     * 是否添加片头主题
     * Whether to add a title theme
     */
    public boolean isAddTitleTheme() {
        return mMeicamTimeline != null && mMeicamTimeline.isAddTitleTheme();
    }

    /**
     * 设置添加片头主题
     * Set add a title theme
     *
     * @param add true add,false not
     */
    public void setAddTitleTheme(boolean add) {
        if (mMeicamTimeline != null) {
            mMeicamTimeline.setAddTitleTheme(add);
        }
    }

    /**
     * 获取片头主题的时长
     * Get title theme duration
     */
    public long getTitleThemeDuration() {
        return mMeicamTimeline == null ? 0 : mMeicamTimeline.getTitleThemeDuration();
    }

    /**
     * 获取时间线当前的时间点
     * Gets timeline current position
     */
    public long getTimelineCurrentPosition() {
        //return mStreamingContext.getTimelineCurrentPosition(mTimeline);
        return mEditorEngine.getCurrentTimelinePosition();
    }

    /**
     * 获取当前的流媒体引擎状态
     * Gets streaming engine state
     */
    public int getStreamingEngineState() {
        return mStreamingContext.getStreamingEngineState();
    }

    /**
     * 从时间线获取图片
     */
    public Bitmap grabImageFromTimeline(long time) {
        if (TextUtils.isEmpty(mMeicamTimeline.getDraftDir())) {
            return mEditorEngine.grabImageFromTimeline(mMeicamTimeline, time, new NvsRational(1, 3));
        }
        return null;
    }

    /**
     * 获取主轨道的视频片段
     * Gets a video clip of the main track
     */
    public MeicamVideoClip getMainTrackVideoClip() {
        if (mMeicamTimeline != null) {
            long stamp = getTimelineCurrentPosition();
            if (mMeicamTimeline.isAddTitleTheme()) {
                if (stamp < getTitleThemeDuration()) {
                    stamp = getTitleThemeDuration();
                }
            }
            MeicamVideoTrack videoTrack = mMeicamTimeline.getVideoTrack(MAIN_TRACK_INDEX);
            if (videoTrack != null) {
                return videoTrack.getClipByTimelinePosition(stamp);
            }
        }
        return null;
    }

    /**
     * 根据轨道索引和入点获取视频片段
     * Get the video clip by track index and entry point
     *
     * @param trackIndex int 轨道索引 the track index
     * @param inPoint    long 入点 the in point
     */
    public MeicamVideoClip getVideoClip(int trackIndex, long inPoint) {
        if (mMeicamTimeline != null) {
            MeicamVideoTrack videoTrack = mMeicamTimeline.getVideoTrack(trackIndex);
            if (videoTrack != null) {
                return videoTrack.getVideoClip(inPoint);
            }
        }
        return null;
    }


    /**
     * 获取下一个audio clip的track index
     * Get next audio clip track index
     *
     * @param inPoint the in point in timeline
     * @return the track index
     */
    public int getNextAudioClipTrackIndex(long inPoint) {
        int trackIndex = 0;
        /*
         * 循环遍历所有的音乐轨道
         * Loop through all the music tracks
         */
        int audioTrackCount = mMeicamTimeline.getAudioTrackCount();
        boolean isAppend = false;
        for (int i = 0; i < audioTrackCount; i++) {
            MeicamAudioTrack audioTrack = mMeicamTimeline.getAudioTrack(i);
            int clipCount = audioTrack.getClipCount();
            //最后一个音频片段
            MeicamAudioClip audioClip = audioTrack.getAudioClip(clipCount - 1);
            //如果新录入的时间点大于等于最后一个音频最后时间点，就直接插入到该音频轨道内
            if (audioClip == null || inPoint >= audioClip.getOutPoint()) {
                trackIndex = i;
                isAppend = true;
                break;
            }
        }
        //如果所有的轨道都不能添加，就新建轨道添加，这里面包含音频轨道为0的时候
        //If all the tracks can't be added, create a new track and add it, and that includes when the audio track is zero
        if (!isAppend) {
            trackIndex = Math.min(audioTrackCount, CommonData.MAX_AUDIO_COUNT);
        }
        return trackIndex;
    }

    /**
     * 添加音频片段到轨道上
     * Add audio clips to the track
     *
     * @param audioPath String The audio path
     * @param name      String The audio name
     * @param trimIn    long The trim in point
     * @param type      int  The audio type
     */
    public MeicamAudioClip addAudioClipToTrack(String audioPath, String name, long inPoint, long trimIn, long trimOut, int type, int trackIndex) {
        if (trackIndex < 0) {
            trackIndex = getNextAudioClipTrackIndex(inPoint);
        }
        MeicamAudioTrack audioTrack;
        int audioTrackCount = mMeicamTimeline.getAudioTrackCount();
        if (trackIndex >= audioTrackCount) {
            if (audioTrackCount < CommonData.MAX_AUDIO_COUNT) {
                audioTrack = TimelineCommand.appendAudioTrack(mMeicamTimeline);
            } else {
                ToastUtils.showShort(String.format(StringUtils.getString(R.string.audio_max_track), CommonData.MAX_AUDIO_COUNT));
                return null;
            }
        } else {
            audioTrack = mMeicamTimeline.getAudioTrack(trackIndex);
        }
        if (audioTrack != null) {
            MeicamAudioClip meicamAudioClip = AudioTrackCommand.addAudioClip(audioTrack, audioPath, inPoint, trimIn, trimOut);
            if (meicamAudioClip != null) {
                NvsAVFileInfo nvsAVFileInfo = mStreamingContext.getAVFileInfo(audioPath);
                if (nvsAVFileInfo != null) {
                    meicamAudioClip.setOriginalDuration(nvsAVFileInfo.getDuration());
                }
                AudioCommand.setParam(meicamAudioClip, AudioCommand.PARAM_AUDIO_TYPE, type);
                AudioCommand.setParam(meicamAudioClip, AudioCommand.PARAM_DRAW_TEXT, name);
            }
            mMeicamTimeline.setThemeQuiet();
            return meicamAudioClip;
        }
        return null;
    }

    private boolean hasVoice(ClipInfo<?> clipInfo) {
        if (clipInfo instanceof MeicamVideoClip) {
            MeicamVideoClip meicamVideoClip = (MeicamVideoClip) clipInfo;
            String filePth = meicamVideoClip.getFilePath();
            NvsStreamingContext nvsStreamingContext = mEditorEngine.getStreamingContext();
            NvsAVFileInfo nvsAVFileInfo = nvsStreamingContext.getAVFileInfo(filePth);
            if (nvsAVFileInfo == null) {
                return false;
            }
            if ((nvsAVFileInfo.getAVFileType() != 0 || nvsAVFileInfo.getAudioStreamCount() == 0)) {
                return false;
            }
            return meicamVideoClip.getVolume() != 0;
        } else if (clipInfo instanceof MeicamAudioClip) {
            MeicamAudioClip meicamAudioClip = (MeicamAudioClip) clipInfo;
            return meicamAudioClip.getVolume() != 0;
        }
        return true;
    }

    private boolean hasVolumeInVideoClip(MeicamVideoClip meicamVideoClip) {
        String filePth = meicamVideoClip.getFilePath();
        if (filePth.startsWith(NvsConstants.HTTP)) {
            //如果是远程路径，根据channelData判断
            //If it is a remote path, judge according to channelData.
            if (TextUtils.isEmpty(meicamVideoClip.getLeftChannelUrl()) && TextUtils.isEmpty(meicamVideoClip.getRightChannelUrl())) {
                return false;
            }
            return meicamVideoClip.getVolume() != 0;
        }
        NvsStreamingContext nvsStreamingContext = mEditorEngine.getStreamingContext();
        NvsAVFileInfo nvsAVFileInfo = nvsStreamingContext.getAVFileInfo(filePth);
        if (nvsAVFileInfo != null) {
            int avFileType = nvsAVFileInfo.getAVFileType();
            if (avFileType == NvsAVFileInfo.AV_FILE_TYPE_IMAGE) {
                return true;
            }
            if (nvsAVFileInfo.getAudioStreamCount() == 0) {
                return false;
            }
            return meicamVideoClip.getVolume() != 0;
        } else {
            return false;
        }
    }


    private static final long min_space = (long) (0.5 * CommonData.TIMEBASE);

    public List<VoiceParam> getVoiceParamListTemp(int type) {
        List<VoiceParam> paramList = new ArrayList<>();
        if (type == 0) {
            MeicamVideoTrack videoTrack = mMeicamTimeline.getVideoTrack(MAIN_TRACK_INDEX);
            for (int i = 0; i < videoTrack.getClipCount(); i++) {
                List<VoiceParam> tempList = new ArrayList<>();
                MeicamVideoClip mainClip = videoTrack.getVideoClip(i);
                if (!hasVoice(mainClip)) {
                    VoiceParam voiceParam;
                    //如果主轨道不是视频或者没有音频,或者是补黑片段，则从下面的画中画找
                    //If the main track is not video or audio, or is a black patch, look for it from the picture below.
                    for (int j = 1; j < mMeicamTimeline.videoTrackCount(); j++) {
                        MeicamVideoTrack meicamVideoTrack = mMeicamTimeline.getVideoTrack(j);
                        for (int k = 0; k < meicamVideoTrack.getClipCount(); k++) {
                            MeicamVideoClip clip = meicamVideoTrack.getVideoClip(k);
                            //如果子轨道的入点大于主轨道当前片段出点，或者子轨道没有声音直接打断循环
                            //If the entry point of the sub-track is greater than
                            // the exit point of the current clip of the main track,
                            // or if the sub-track has no sound, the cycle will be interrupted directly
                            if (clip.getInPoint() >= mainClip.getOutPoint()) {
                                break;
                            }
                            if (!hasVoice(clip)) {
                                continue;
                            }
                            if (tempList.size() == 0) {
                                //第一次循环先平铺
                                //Tile first for the first cycle
                                voiceParam = new VoiceParam();
                                voiceParam.setPath(clip.getFilePath());
                                voiceParam.setInP(clip.getInPoint());
                                voiceParam.setOutP(Math.min(clip.getOutPoint(), mainClip.getOutPoint()));
                                voiceParam.setTrInP(clip.getTrimIn());
                                voiceParam.setTrOutP(clip.getTrimIn() + (voiceParam.getOutP() - voiceParam.getInP()));
                                tempList.add(voiceParam);
                            } else if (tempList.size() == 1) {
                                //如果只有一个既要考虑前面又要考虑后面
                                // If there is only one to consider both the front and the back
                                VoiceParam currParam = tempList.get(0);
                                //前面
                                // front
                                if (currParam.getInP() - clip.getInPoint() > min_space && currParam.getInP() - mainClip.getInPoint() > min_space) {
                                    voiceParam = new VoiceParam();
                                    voiceParam.setPath(clip.getFilePath());
                                    voiceParam.setInP(clip.getInPoint());
                                    voiceParam.setOutP(Math.min(clip.getOutPoint(), currParam.getInP()));
                                    voiceParam.setTrInP(clip.getTrimIn());
                                    voiceParam.setTrOutP(clip.getTrimIn() + (voiceParam.getOutP() - voiceParam.getInP()));
                                    tempList.add(voiceParam);
                                }
                                //后面
                                //behind
                                if (clip.getOutPoint() - currParam.getOutP() > min_space && mainClip.getOutPoint() - currParam.getOutP() > min_space) {
                                    voiceParam = new VoiceParam();
                                    voiceParam.setPath(clip.getFilePath());
                                    voiceParam.setInP(currParam.getOutP());
                                    voiceParam.setOutP(Math.min(clip.getOutPoint(), mainClip.getOutPoint()));
                                    voiceParam.setTrInP(clip.getTrimIn());
                                    voiceParam.setTrOutP(clip.getTrimIn() + (voiceParam.getOutP() - voiceParam.getInP()));
                                    tempList.add(voiceParam);
                                }
                            } else {
                                //找下面的轨道要根据前面剩余的空间来填空，要考虑第一个空和最后一个空
                                // To find the following track, fill in the blanks according to the remaining space in front,
                                // and consider the first and last blanks
                                List<VoiceParam> temp = new ArrayList<>();
                                for (int m = 0; m < tempList.size(); m++) {

                                    if (m == 0) {
                                        //第一个
                                        //first
                                        VoiceParam firstParam = tempList.get(0);
                                        if (firstParam.getInP() - clip.getInPoint() > min_space && firstParam.getInP() - mainClip.getInPoint() > min_space) {
                                            voiceParam = new VoiceParam();
                                            voiceParam.setPath(clip.getFilePath());
                                            voiceParam.setInP(clip.getInPoint());
                                            voiceParam.setOutP(Math.min(clip.getOutPoint(), firstParam.getInP()));
                                            voiceParam.setTrInP(clip.getTrimIn());
                                            voiceParam.setTrOutP(clip.getTrimIn() + (voiceParam.getOutP() - voiceParam.getInP()));
                                            temp.add(voiceParam);
                                        }
                                    } else if (m == tempList.size() - 1) {
                                        //最后一个
                                        // last
                                        VoiceParam lastParam = tempList.get(tempList.size() - 1);
                                        if (clip.getOutPoint() - lastParam.getOutP() > min_space && mainClip.getOutPoint() - lastParam.getOutP() > min_space) {
                                            voiceParam = new VoiceParam();
                                            voiceParam.setPath(clip.getFilePath());
                                            voiceParam.setInP(lastParam.getOutP());
                                            voiceParam.setOutP(Math.min(clip.getOutPoint(), mainClip.getOutPoint()));
                                            voiceParam.setTrInP(clip.getTrimIn());
                                            voiceParam.setTrOutP(clip.getTrimIn() + (voiceParam.getOutP() - voiceParam.getInP()));
                                            temp.add(voiceParam);
                                        }
                                    } else {
                                        VoiceParam afterParam = tempList.get(m + 1);
                                        VoiceParam currParam = tempList.get(m);
                                        if (afterParam.getInP() - clip.getInPoint() > min_space && afterParam.getInP() - currParam.getOutP() > min_space) {
                                            voiceParam = new VoiceParam();
                                            voiceParam.setPath(clip.getFilePath());
                                            voiceParam.setInP(Math.max(currParam.getOutP(), clip.getInPoint()));
                                            voiceParam.setOutP(Math.min(afterParam.getInP(), clip.getOutPoint()));
                                            voiceParam.setTrInP(clip.getTrimIn());
                                            voiceParam.setTrOutP(clip.getTrimIn() + (voiceParam.getOutP() - voiceParam.getInP()));
                                            temp.add(voiceParam);
                                        }
                                    }
                                }
                                tempList.addAll(temp);
                            }

                        }
                    }
                    paramList.addAll(tempList);
                } else {
                    paramList.add(VoiceParam.create(mainClip));
                }
            }

        } else if (type == 1) {

        } else if (type == 2) {

        }

        return paramList;
    }


    /**
     * Has record boolean.
     * 是否有录音
     *
     * @return the boolean
     */
    public boolean hasRecord() {
        boolean result = false;
        int count = mMeicamTimeline.getAudioTrackCount();
        if (count > 0) {
            for (int index = 0; index < count; index++) {
                MeicamAudioTrack track = mMeicamTimeline.getAudioTrack(index);
                int clipCount = track.getClipCount();
                if (clipCount > 0) {
                    for (int clipIndex = 0; clipIndex < clipCount; clipIndex++) {
                        MeicamAudioClip clip = track.getAudioClip(clipIndex);
                        if (hasVoice(clip)) {
                            result = true;
                            break;
                        }
                    }
                }
            }
        }
        return result;
    }

    /**
     * Has audio in main track boolean.
     * 是否在主轨上有音频
     *
     * @return the boolean
     */
    public boolean hasAudioInMainTrack() {
        boolean result = false;
        MeicamVideoTrack videoTrack = mMeicamTimeline.getVideoTrack(MAIN_TRACK_INDEX);
        int clipCount = videoTrack.getClipCount();
        if (clipCount > 0) {
            for (int clipIndex = 0; clipIndex < clipCount; clipIndex++) {
                MeicamVideoClip videoClip = videoTrack.getVideoClip(clipIndex);
                if (hasVolumeInVideoClip(videoClip)) {
                    result = true;
                    break;
                }
            }
        }
        return result;
    }

    /**
     * Gets voice param list.
     * 获取音频参数
     *
     * @param type             the type 类型
     * @param timelineDuration the timeline duration timeline时长
     * @return the voice param list 音频参数列表
     */
    public List<VoiceParam> getVoiceParamList(int type, long timelineDuration) {
        List<List<ClipInfo<?>>> clipTrack;
        if (type == 0) {
            clipTrack = getVideoTrackClip();
        } else if (type == 1) {
            clipTrack = getRecordTrackClip();
        } else {
            clipTrack = getAllClipTrack();
        }
        return getVoiceParamList(clipTrack, timelineDuration);
    }

    /**
     * Gets voice param list.
     * 获取音频参数
     *
     * @param type the type 类型
     * @return the voice param list 音频参数列表
     */
    public List<List<VoiceParam>> getVoiceParamList(int type) {
        List<List<ClipInfo<?>>> clipTrack;
        if (type == 0) {
            clipTrack = getVideoTrackClip();
        } else if (type == 1) {
            clipTrack = getRecordTrackClip();
        } else {
            clipTrack = getAllClipTrack();
        }
        return getVoiceParamList(clipTrack);
    }


    /**
     * Gets video track clip.
     * 获取所有视频的轨道clip
     *
     * @return the video track clip 所有视频的轨道clip
     */
    private List<List<ClipInfo<?>>> getVideoTrackClip() {
        List<List<ClipInfo<?>>> result = new ArrayList<>();
        List<List<ClipInfo<?>>> mainVideoTrackClip = getMainVideoTrackClip();
        if (!CommonUtils.isEmpty(mainVideoTrackClip)) {
            result.addAll(mainVideoTrackClip);
        }
        List<List<ClipInfo<?>>> pipVideoTrackClip = getPipVideoTrackClip();
        if (!CommonUtils.isEmpty(pipVideoTrackClip)) {
            result.addAll(pipVideoTrackClip);
        }
        return result;
    }

    /**
     * Gets record track clip.
     * 获取音频轨道clip
     *
     * @return the record track clip 音频轨道clip
     */
    private List<List<ClipInfo<?>>> getRecordTrackClip() {
        int count = mMeicamTimeline.getAudioTrackCount();
        List<List<ClipInfo<?>>> clipTrack = new ArrayList<>();
        if (count > 0) {
            for (int index = 0; index < count; index++) {
                MeicamAudioTrack track = mMeicamTimeline.getAudioTrack(index);
                int clipCount = track.getClipCount();
                if (clipCount > 0) {
                    List<ClipInfo<?>> clipInfoList = new ArrayList<>();
                    for (int clipIndex = 0; clipIndex < clipCount; clipIndex++) {
                        MeicamAudioClip clip = track.getAudioClip(clipIndex);
                        if (!hasVoice(clip)) {
                            continue;
                        }
                        clipInfoList.add(clip);
                    }
                    if (!clipInfoList.isEmpty()) {
                        clipTrack.add(clipInfoList);
                    }
                }
            }
        }
        return clipTrack;
    }

    /**
     * Gets main video track clip.
     * 获取主轨轨道clip
     *
     * @return the main video track clip 主轨轨道clip
     */
    private List<List<ClipInfo<?>>> getMainVideoTrackClip() {
        List<List<ClipInfo<?>>> clipTrack = new ArrayList<>();
        MeicamVideoTrack videoTrack = mMeicamTimeline.getVideoTrack(MAIN_TRACK_INDEX);
        int clipCount = videoTrack.getClipCount();
        if (clipCount > 0) {
            List<ClipInfo<?>> clipInfoList = new ArrayList<>();
            for (int clipIndex = 0; clipIndex < clipCount; clipIndex++) {
                MeicamVideoClip videoClip = videoTrack.getVideoClip(clipIndex);
                if (!hasVoice(videoClip)) {
                    continue;
                }
                clipInfoList.add(videoClip);
            }
            if (!clipInfoList.isEmpty()) {
                clipTrack.add(clipInfoList);
            }
        }
        return clipTrack;
    }

    /**
     * Gets pip video track clip.
     * 获取画中画轨道clip
     *
     * @return the pip video track clip 画中画轨道clip
     */
    private List<List<ClipInfo<?>>> getPipVideoTrackClip() {
        int count = mMeicamTimeline.videoTrackCount();
        List<List<ClipInfo<?>>> clipTrack = new ArrayList<>();
        if (count > 1) {
            for (int index = 1; index < count; index++) {
                MeicamVideoTrack videoTrack = mMeicamTimeline.getVideoTrack(index);
                int clipCount = videoTrack.getClipCount();
                if (clipCount > 0) {
                    List<ClipInfo<?>> clipInfoList = new ArrayList<>();
                    for (int clipIndex = 0; clipIndex < clipCount; clipIndex++) {
                        MeicamVideoClip videoClip = videoTrack.getVideoClip(clipIndex);
                        if (!hasVoice(videoClip)) {
                            continue;
                        }
                        clipInfoList.add(videoClip);
                    }
                    if (!clipInfoList.isEmpty()) {
                        clipTrack.add(clipInfoList);
                    }
                }
            }
        }
        return clipTrack;
    }

    private List<List<ClipInfo<?>>> getAllClipTrack() {
        List<List<ClipInfo<?>>> result = new ArrayList<>();
        List<List<ClipInfo<?>>> mainVideoTrackClip = getMainVideoTrackClip();
        if (!CommonUtils.isEmpty(mainVideoTrackClip)) {
            result.addAll(mainVideoTrackClip);
        }
        List<List<ClipInfo<?>>> recordClipTrack = getRecordTrackClip();
        if (!CommonUtils.isEmpty(recordClipTrack)) {
            result.addAll(recordClipTrack);
        }
        List<List<ClipInfo<?>>> pipVideoTrackClip = getPipVideoTrackClip();
        if (!CommonUtils.isEmpty(pipVideoTrackClip)) {
            result.addAll(pipVideoTrackClip);
        }
        return result;
    }

    private List<List<VoiceParam>> getVoiceParamList(List<List<ClipInfo<?>>> clipTracks) {
        if (CommonUtils.isEmpty(clipTracks)) {
            return new ArrayList<>(0);
        }
        List<List<VoiceParam>> result = new ArrayList<>(clipTracks.size());
        for (List<ClipInfo<?>> clipTrack : clipTracks) {
            List<VoiceParam> trackVoiceList = new ArrayList<>();
            result.add(trackVoiceList);
            for (ClipInfo<?> clipInfo : clipTrack) {
                VoiceParam voiceParam = new VoiceParam();
                voiceParam.setPath(clipInfo.getFilePath());
                voiceParam.setInP(clipInfo.getInPoint());
                voiceParam.setOutP(clipInfo.getOutPoint());
                voiceParam.setTrInP(clipInfo.getTrimIn());
                voiceParam.setTrOutP((long) (clipInfo.getTrimIn() + (voiceParam.getOutP() - voiceParam.getInP()) * clipInfo.getSpeed()));
                trackVoiceList.add(voiceParam);
            }
        }
        return result;
    }

    private List<VoiceParam> getVoiceParamList(List<List<ClipInfo<?>>> clipTracks, long duration) {
        if (CommonUtils.isEmpty(clipTracks)) {
            return new ArrayList<>();
        }
        List<VoiceParam> tempList = new ArrayList<>();
        for (List<ClipInfo<?>> clipTrack : clipTracks) {
            if (CommonUtils.isEmpty(clipTrack)) {
                continue;
            }
            //每次查询，检查时间间隔
            //Each query, check interval
            List<TimeSpace> spaceList = getTimeSpace(tempList, duration);
            //填充时间间隔格子
            //Fill the spaces
            for (TimeSpace space : spaceList) {
                //只要有一部分在空格里，则把这部分截取
                //As long as there is a part in the space, intercept this part
                for (ClipInfo<?> clipInfo : clipTrack) {
                    boolean isCrossInPoint = space.inPoint >= clipInfo.getInPoint() && space.inPoint <= clipInfo.getOutPoint();
                    boolean isCrossOutPoint = space.outPoint >= clipInfo.getInPoint() && space.outPoint <= clipInfo.getOutPoint();
                    VoiceParam voiceParam = null;
                    if (isCrossInPoint) {
                        voiceParam = new VoiceParam();
                        voiceParam.setPath(clipInfo.getFilePath());
                        voiceParam.setInP(space.inPoint);
                        if (!isCrossOutPoint) {
                            //出点不在格子里，入点在格子里
                            //The exit point is not in the space, and the entry point is in the space
                            voiceParam.setOutP(clipInfo.getOutPoint());
                        } else {
                            //出点在格子里，入点在格子里
                            //The exit point is in the space, and the entry point is in the space
                            voiceParam.setOutP(space.outPoint);
                        }
                        voiceParam.setTrInP((long) ((space.inPoint - clipInfo.getInPoint()) * clipInfo.getSpeed() + clipInfo.getTrimIn()));
                        voiceParam.setTrOutP((long) (clipInfo.getTrimIn() + (voiceParam.getOutP() - voiceParam.getInP()) * clipInfo.getSpeed()));
                    } else {
                        //出点在格子里，入点不在格子里
                        //The exit point is in the space, and the entry point is not in the space
                        if (isCrossOutPoint) {
                            voiceParam = new VoiceParam();
                            voiceParam.setPath(clipInfo.getFilePath());
                            voiceParam.setInP(clipInfo.getInPoint());
                            voiceParam.setOutP(space.outPoint);
                            voiceParam.setTrInP(clipInfo.getTrimIn());
                            voiceParam.setTrOutP((long) (clipInfo.getTrimIn() + (voiceParam.getOutP() - voiceParam.getInP()) * clipInfo.getSpeed()));
                        } else {
                            //出点不在格子里，入点不在格子里
                            //The exit point is not in the space, and the entry point is not in the space
                            if (space.inPoint < clipInfo.getInPoint() && space.outPoint > clipInfo.getOutPoint()) {
                                //clip 在格子内部
                                //The clip is in the space
                                voiceParam = new VoiceParam();
                                voiceParam.setPath(clipInfo.getFilePath());
                                voiceParam.setInP(clipInfo.getInPoint());
                                voiceParam.setOutP(clipInfo.getOutPoint());
                                voiceParam.setTrInP(clipInfo.getTrimIn());
                                voiceParam.setTrOutP((long) (clipInfo.getTrimIn() + (voiceParam.getOutP() - voiceParam.getInP()) * clipInfo.getSpeed()));
                            }
                        }
                    }

                    if (voiceParam != null) {
                        tempList.add(voiceParam);
                    }
                }
            }
        }
        if (BuildConfig.DEBUG) {
            for (int index = 0; index < tempList.size(); index++) {
                LogUtils.d("getVoiceParamList: voiceParam = " + tempList.get(index) + ", index = " + index);
            }
        }
        return tempList;
    }

    /**
     * Gets time space.
     * 获取时间空格
     *
     * @param voiceParamList the list of voice parameter 音频参数列表
     * @param duration       the duration 时长
     * @return the time space 时间间隔列表
     */
    public List<TimeSpace> getTimeSpace(List<VoiceParam> voiceParamList, long duration) {
        List<TimeSpace> spaceList = new ArrayList<>();
        if (CommonUtils.isEmpty(voiceParamList)) {
            spaceList.add(new TimeSpace(0, duration));
            return spaceList;
        }
        int size = voiceParamList.size();
        for (int index = 0; index < size; index++) {
            VoiceParam voiceParam = voiceParamList.get(index);
            if (index == 0) {
                long inP = voiceParam.getInP();
                if (inP > 0) {
                    spaceList.add(new TimeSpace(0, inP));
                }
            }
            if (index == size - 1) {
                long outP = voiceParam.getOutP();
                if (outP < duration) {
                    spaceList.add(new TimeSpace(outP, duration));
                }
            }

            int nextIndex = index + 1;
            if (nextIndex < size) {
                VoiceParam nextParam = voiceParamList.get(nextIndex);
                if (nextParam.getInP() > voiceParam.getInP()) {
                    spaceList.add(new TimeSpace(voiceParam.getOutP(), nextParam.getInP()));
                }
            }

        }
        return spaceList;
    }

    /**
     * 获取语音识别的参数
     * Get the param list of phonetic recognition
     *
     * @return list
     */
    public List<VoiceParam> getVoiceParamList() {
        MeicamVideoTrack videoTrack = mMeicamTimeline.getVideoTrack(MAIN_TRACK_INDEX);
        List<VoiceParam> paramList = new ArrayList<>();
        if (videoTrack != null) {
            for (int i = 0; i < videoTrack.getClipCount(); i++) {
                MeicamVideoClip videoClip = videoTrack.getVideoClip(i);
                if (!CommonData.CLIP_VIDEO.equals(videoClip.getVideoType())) {
                    continue;
                }
                paramList.add(VoiceParam.create(videoClip));
            }
        }
        return paramList;
    }

    /**
     * 添加视频片段
     * Add video clip.
     *
     * @param index     the add index 添加的索引
     * @param mediaList the media list 媒体列表
     */
    public void addVideoClip(int index, List<MediaData> mediaList) {
        if (index < 0) {
            return;
        }
        List<MeicamVideoClip> videoClipList = mEditorEngine.addVideoClip(MAIN_TRACK_INDEX, index, mediaList);
        if (videoClipList != null) {
            MeicamVideoClip videoClip = mEditorEngine.getVideoClip(MAIN_TRACK_INDEX, index);
            long seekTime = videoClip == null ? mEditorEngine.getCurrentTimelinePosition() : videoClip.getInPoint();
            LogUtils.d("videoClip=" + videoClip);
            getView().onAddVideoClip(videoClipList, MAIN_TRACK_INDEX, seekTime);
            checkTrackDuration();
            mEditorEngine.seekTimeline(seekTime, 0);
        }
    }

    /**
     * 复制视频片段
     * Copy video clip.
     *
     * @param oldVideoClip video clip that have been copied 被复制的视频片段
     * @param isPip        true is pip 画中画，false not 不是
     */
    public void copyVideoClip(MeicamVideoClip oldVideoClip, boolean isPip) {
        int trackIndex = 0;
        if (isPip) {
            trackIndex = mEditorEngine.getCopyPipTrackIndex(oldVideoClip);
            if (trackIndex == -1) {
                return;
            }
            if (trackIndex >= CommonData.TRACK_MAX) {
                ToastUtils.showShort(String.format(StringUtils.getString(R.string.max_track_pip), CommonData.TRACK_MAX - 1));
                return;
            }
        }
        MeicamVideoClip videoClip = mEditorEngine.copyVideoClip(oldVideoClip, trackIndex);
        if (videoClip != null) {
            getView().onAddVideoClip(videoClip);
        }
    }

    /**
     * 添加画中画视频片段
     * Add pip video clip
     *
     * @param mediaData the media data媒体数据
     * @return MeicamVideoClip 视频片段
     **/
    public MeicamVideoClip addPipVideoClip(MediaData mediaData) {
        if (mediaData != null && !TextUtils.isEmpty(mediaData.getPath())) {
            long inPoint = mEditorEngine.getCurrentTimelinePosition();
            int trackIndex = mEditorEngine.getPipTrackIndex(inPoint, inPoint + getRealDuration(mediaData));
            if (trackIndex >= CommonData.TRACK_MAX) {
                ToastUtils.showShort(String.format(StringUtils.getString(R.string.max_track_pip), CommonData.TRACK_MAX - 1));
                return null;
            }
            MeicamVideoTrack videoTrack = mEditorEngine.getVideoTrack(trackIndex);
            if (videoTrack == null) {
                videoTrack = TimelineCommand.appendVideoTrack(mMeicamTimeline);
            }
            MeicamVideoClip videoClip = mEditorEngine.addVideoClip(videoTrack, inPoint, mediaData);
            if (videoClip != null) {
                MeicamVideoFx videoFx = VideoClipCommand.findPropertyVideoFx(videoClip);
                if (videoFx != null) {
                    VideoFxCommand.setFloatVal(videoFx, NvsConstants.FX_TRANSFORM_2D_SCALE_X, 0.8f);
                    VideoFxCommand.setFloatVal(videoFx, NvsConstants.FX_TRANSFORM_2D_SCALE_Y, 0.8f);
                }
                // 添加画中画 删除主题 Add picture-in-picture-delete theme
                MeicamTheme meicamTheme = mMeicamTimeline.getMeicamTheme();
                if (meicamTheme != null && !TextUtils.isEmpty(meicamTheme.getThemePackageId())) {
                    mMeicamTimeline.removeTheme();
                }
                getView().onAddVideoClip(videoClip);
                return videoClip;
            }
        }
        return null;
    }

    /**
     * 获取插入的视频片段的索引
     * Gets the index of insert video clip
     */
    public int getInsertVideoClipIndex() {
        int index = 0;
        if (mMeicamTimeline == null) {
            return index;
        }
        MeicamVideoTrack videoTrack = mMeicamTimeline.getVideoTrack(TRACK_INDEX_MAIN);
        if (videoTrack == null) {
            return index;
        }
        long currentPosition = mEditorEngine.getCurrentTimelinePosition();
        MeicamVideoClip videoClip = videoTrack.getClipByTimelinePosition(currentPosition);
        if (videoClip == null) {
            LogUtils.e("videoClip is null!");
            return index;
        }
        index = videoClip.getIndex();
        if (CommonData.EMPTY_THUMBNAIL_IMAGE.equals(videoClip.getFilePath()) || CommonData.IMAGE_BLACK_HOLDER.equals(videoClip.getFilePath())) {
            index = videoTrack.getClipCount() - 1;
        } else {
            long duration = videoClip.getOutPoint() - videoClip.getInPoint();
            if (currentPosition > videoClip.getInPoint() + duration / 2) {
                //超过一半
                // More than half
                index++;
            }
        }
        return index;
    }

    /**
     * 检查轨道时长，用于主轨道补黑片段的删除、添加、更改。
     * Check the track duration, which is used to delete, add and change the main track black patch.
     */
    public void checkTrackDuration() {
        long otherTrackMaxDuration = mEditorEngine.getOtherTrackMaxDuration();
        long offsetDuration = otherTrackMaxDuration - mEditorEngine.getVideoTrackDuration(TRACK_INDEX_MAIN);
        LogUtils.d("offsetDuration=" + offsetDuration);
        MeicamVideoClip lastVideoClip = mEditorEngine.getLastVideoClip(MAIN_TRACK_INDEX);
        if (offsetDuration > 0) {
            if (lastVideoClip != null) {
                if (CommonData.CLIP_HOLDER.equals(lastVideoClip.getVideoType())) {
                    VideoClipCommand.setTrimOut(lastVideoClip, lastVideoClip.getTrimOut() + offsetDuration, true);
                    getView().onCheckTrackResult(lastVideoClip, CHANGE_HOLDER);
                } else {
                    MeicamVideoTrack videoTrack = mEditorEngine.getVideoTrack(MAIN_TRACK_INDEX);
                    MeicamVideoClip videoClip = VideoTrackCommand.addVideoClip(videoTrack, CommonData.IMAGE_BLACK_HOLDER,
                            lastVideoClip.getIndex() + 1, 0, offsetDuration);
                    if (videoClip == null) {
                        LogUtils.e("补黑视频片段添加失败");
                        return;
                    }
                    VideoClipCommand.setParam(videoClip, VideoClipCommand.PARAM_VIDEO_TYPE, CommonData.CLIP_HOLDER);
                    VideoClipCommand.setParam(videoClip, VideoClipCommand.PARAM_ORG_DURATION, Long.MAX_VALUE);
                    VideoClipCommand.setOpacity(videoClip, 0);
                    LogUtils.d("add,inPoint=" + videoClip.getInPoint() + ",outPoint=" + videoClip.getOutPoint());
                    getView().onCheckTrackResult(videoClip, ADD_HOLDER);
                }
            }
        } else {
            if (lastVideoClip != null) {
                if (CommonData.CLIP_HOLDER.equals(lastVideoClip.getVideoType())) {
                    //如果子轨的长度小于补黑的入点直接删除
                    // If the length of the sub-rail is less than the black entry point, delete it directly
                    if (otherTrackMaxDuration <= lastVideoClip.getInPoint()) {
                        MeicamVideoTrack videoTrack = mEditorEngine.getVideoTrack(MAIN_TRACK_INDEX);
                        VideoTrackCommand.removeVideoClip(videoTrack, videoTrack.getClipCount() - 1, false);
                        getView().onCheckTrackResult(lastVideoClip, DELETE_HOLDER);
                    } else {
                        VideoClipCommand.setTrimOut(lastVideoClip, lastVideoClip.getTrimOut() + offsetDuration, true);
                        getView().onCheckTrackResult(lastVideoClip, CHANGE_HOLDER);
                    }
                }
            }
        }
    }


    public void dealRecordTimelineDurationChanged() {
        long needDuration = 2 * 60 * Constants.TIME_BASE;
        MeicamVideoClip lastVideoClip = mEditorEngine.getLastVideoClip(MAIN_TRACK_INDEX);
        if (lastVideoClip != null) {
            if (lastVideoClip.getVideoType().equals(CommonData.CLIP_HOLDER)) {
                lastVideoClip.setTrimOut(lastVideoClip.getTrimOut() + needDuration, true);
                lastVideoClip.updateInAndOutPoint();
                LogUtils.d("Length modification: " + lastVideoClip.getTrimIn() + " " + lastVideoClip.getTrimOut() + " " + lastVideoClip.getInPoint() + "  " + lastVideoClip.getOutPoint());
            } else {
                MeicamVideoTrack videoTrack = mEditorEngine.getVideoTrack(MAIN_TRACK_INDEX);
                MeicamVideoClip videoClip = videoTrack.addVideoClip(CommonData.IMAGE_BLACK_HOLDER,
                        lastVideoClip.getIndex() + 1, 0, needDuration);
                if (videoClip == null) {
                    LogUtils.e("Failed to add blackout video clip");
                    return;
                }
                videoClip.setVideoType(CommonData.CLIP_HOLDER);
                videoClip.setOrgDuration(Long.MAX_VALUE);
                videoClip.setOpacity(0);
                LogUtils.d("Add too long: " + videoClip.getTrimIn() + " " + videoClip.getTrimOut() + " " + videoClip.getInPoint() + "  " + videoClip.getOutPoint());
                getView().onCheckTrackResult(videoClip, ADD_HOLDER);
            }
        }
    }


    /**
     * Move caption or sticker.
     * 移动字幕或贴纸
     *
     * @param duration the duration
     */
    public void moveCaptionOrSticker(long duration) {
        for (int i = 0; i < mMeicamTimeline.getStickerCaptionTrackCount(); i++) {
            MeicamStickerCaptionTrack meicamStickerCaptionTrack = mMeicamTimeline.findStickCaptionTrack(i);
            if (meicamStickerCaptionTrack == null) {
                continue;
            }
            for (int j = 0; j < meicamStickerCaptionTrack.getClipCount(); j++) {
                ClipInfo<?> clipInfo = meicamStickerCaptionTrack.getCaptionStickerClip(j);
                if (clipInfo instanceof MeicamStickerClip) {
                    MeicamStickerClip meicamStickerClip = (MeicamStickerClip) clipInfo;
                    if (duration > 0) {
                        meicamStickerClip.setOutPoint(clipInfo.getOutPoint() + duration);
                        meicamStickerClip.setInPoint(clipInfo.getInPoint() + duration);
                    } else {
                        meicamStickerClip.setInPoint(clipInfo.getInPoint() + duration);
                        meicamStickerClip.setOutPoint(clipInfo.getOutPoint() + duration);
                    }
                } else if (clipInfo instanceof MeicamCaptionClip) {
                    MeicamCaptionClip meicamCaptionClip = (MeicamCaptionClip) clipInfo;
                    if (duration > 0) {
                        meicamCaptionClip.setOutPoint(clipInfo.getOutPoint() + duration);
                        meicamCaptionClip.setInPoint(clipInfo.getInPoint() + duration);
                    } else {
                        meicamCaptionClip.setInPoint(clipInfo.getInPoint() + duration);
                        meicamCaptionClip.setOutPoint(clipInfo.getOutPoint() + duration);
                    }
                } else if (clipInfo instanceof MeicamCompoundCaptionClip) {
                    MeicamCompoundCaptionClip meicamCompoundCaptionClip = (MeicamCompoundCaptionClip) clipInfo;
                    if (duration > 0) {
                        meicamCompoundCaptionClip.setOutPoint(clipInfo.getOutPoint() + duration);
                        meicamCompoundCaptionClip.setInPoint(clipInfo.getInPoint() + duration);
                    } else {
                        meicamCompoundCaptionClip.setInPoint(clipInfo.getInPoint() + duration);
                        meicamCompoundCaptionClip.setOutPoint(clipInfo.getOutPoint() + duration);
                    }
                }
            }

        }

    }

    /**
     * 更新或者添加关键帧
     * Update or add key frame
     *
     * @param clipInfo      the clip info片段
     * @param keyFrameInfo  the key frame info视图关键帧信息
     * @param videoFragment the video fragment
     * @param isMainTrack   true is main track clip 主轨道片段，false child track clip子轨道片段
     */
    public void updateOrAddKeyFrame(IKeyFrameProcessor<?> clipInfo, KeyFrameInfo keyFrameInfo,
                                    VideoFragment videoFragment, boolean isMainTrack) {
        if (keyFrameInfo != null && videoFragment != null) {
            MeicamKeyFrame tempKeyFrame = videoFragment.getTempKeyFrame();
            if (keyFrameInfo.getSelectedPoint() < 0) {
                /*没有选中关键帧
                 * No keys selected
                 * */
                if (tempKeyFrame != null) {
                    tempKeyFrame.update(false);
                    videoFragment.notRemoveTempKeyFrame();
                    getView().onAddKeyFrame(tempKeyFrame, isMainTrack);
                }
            } else {
                /*选中关键帧
                 * Select keys
                 * */
                KeyFrameProcessor<?> keyFrameProcessor = clipInfo.keyFrameProcessor();
                MeicamKeyFrame keyFrame = keyFrameProcessor.getKeyFrame(keyFrameInfo.getSelectedPoint());
                if (keyFrame != null) {
                    if (tempKeyFrame == null) {
                        /*走这里说明，当前的时间点刚好有关键帧，可以直接更新
                         * This shows that the current time point just has a key frame and can be updated directly
                         * */
                        keyFrame.update(false);
                    } else {
                        if (keyFrame.getAtTime() == tempKeyFrame.getAtTime()) {
                            /*一般不会出现这种情况
                             *Generally, this will not happen
                             * */
                            videoFragment.notRemoveTempKeyFrame();
                        }
                        //tempKeyFrame.update(false);
                         /*注意这里：因为tempKeyFrame会被VideoFragment内部移除，
                         所以可以直接使用getParams获取的集合去使用而不用重建对象
                         Note here: Because tempKeyFrame will be removed from VideoFragment,
                         Therefore, you can use the collection obtained by getParams directly without rebuilding the object.
                         */
                        keyFrame.setParamList(tempKeyFrame.getParams());
                    }
                    keyFrameProcessor.updateBrothers(keyFrame);
                    keyFrameProcessor.updateKeyFrameControlPoints();
                }
            }
        }
    }

    /**
     * 更新或者添加关键帧
     * Update or add key frame
     *
     * @param clipInfo     the clip info片段
     * @param keyFrameInfo the key frame info视图关键帧信息
     * @param isMainTrack  true is main track clip 主轨道片段，false child track clip子轨道片段
     */
    public void updateOrAddKeyFrame(IKeyFrameProcessor<?> clipInfo, KeyFrameInfo keyFrameInfo, boolean isMainTrack) {
        if (keyFrameInfo != null) {
            MeicamKeyFrame tempKeyFrame = getTempKeyFrame();
            if (keyFrameInfo.getSelectedPoint() < 0) {
                /*没有选中关键帧
                 * No keys selected
                 * */
                if (tempKeyFrame != null) {
                    removeTempKeyFrame(false);
                    getView().onAddKeyFrame(tempKeyFrame, isMainTrack);
                }
            } else {
                /*选中关键帧
                 * Select keys
                 * */
                KeyFrameProcessor<?> keyFrameProcessor = clipInfo.keyFrameProcessor();
                MeicamKeyFrame keyFrame = keyFrameProcessor.getKeyFrame(keyFrameInfo.getSelectedPoint());
                if (keyFrame != null) {
                    if (tempKeyFrame != null) {
                        if (keyFrame.getAtTime() == tempKeyFrame.getAtTime()) {
                            /*一般不会出现这种情况
                             *Generally, this will not happen
                             * */
                            removeTempKeyFrame(false);
                        }
                        //tempKeyFrame.update(false);
                         /*注意这里：因为tempKeyFrame会被VideoFragment内部移除，
                         所以可以直接使用getParams获取的集合去使用而不用重建对象
                         Note here: Because tempKeyFrame will be removed from VideoFragment,
                         Therefore, you can use the collection obtained by getParams directly without rebuilding the object.
                         */
                        keyFrame.setParamList(tempKeyFrame.getParams());
                    }
                    keyFrameProcessor.updateBrothers(keyFrame);
                    keyFrameProcessor.updateKeyFrameControlPoints();
                }
            }
        }
    }

    private KeyFrameProcessor<?> mCheckKeyFrameClip;
    private String mCheckKeyFrameKey;
    private long mCheckKeyFrameAtTime;

    /**
     * Try to add temp key frame.
     * 添加临时关键帧
     *
     * @param keyFrameHolder the key frame holder 关键帧处理对象
     * @param param          the param 关键帧参数
     * @param keyFrameKey    the key frame key 关键帧关键字
     * @param atTime         the at time 时间
     * @param offSetTime     the off set time 偏移时间
     */
    public void tryToAddTempKeyFrame(IKeyFrameProcessor<?> keyFrameHolder, List<MeicamFxParam<?>> param, String keyFrameKey,
                                     long atTime, long offSetTime) {
        if (CommonUtils.isEmpty(param)) {
            throw new InvalidParameterException("Param and param type is invalid");
        }
        KeyFrameProcessor<?> keyFrameProcessor = keyFrameHolder.keyFrameProcessor();
        if (keyFrameProcessor.getKeyFrameCount(keyFrameKey) > 0 && keyFrameProcessor.getKeyFrame(atTime) == null) {
            if (mCheckKeyFrameClip != null) {
                removeTempKeyFrame(true);
            }
            mCheckKeyFrameKey = keyFrameKey;
            /*有关键帧，且当前时间点没有关键帧
             * There are keys, and the current time point has no keys
             * */
            MeicamKeyFrame keyFrame = KeyFrameHolderCommand.addKeyFrame(keyFrameHolder, param, atTime, offSetTime);
            if (keyFrame != null) {
                mCheckKeyFrameClip = keyFrameProcessor;
                mCheckKeyFrameAtTime = atTime;
            }
        }
    }

    /**
     * 获取临时关键帧
     * Get temp key frame
     */
    public MeicamKeyFrame getTempKeyFrame() {
        if (mCheckKeyFrameClip != null) {
            return mCheckKeyFrameClip.getKeyFrame(mCheckKeyFrameAtTime);
        }
        return null;
    }

    /**
     * 移除临时关键帧
     * Remove temp key frame
     */
    public void removeTempKeyFrame(boolean removeKeyFrame) {
        if (mCheckKeyFrameClip != null) {
            if (removeKeyFrame) {
                mCheckKeyFrameClip.removeKeyFrame(getRemoveParamKeys(mCheckKeyFrameKey), mCheckKeyFrameAtTime);
            } else {
                mCheckKeyFrameClip.cutKeyFrameCurve(mCheckKeyFrameClip.getKeyFrame(mCheckKeyFrameAtTime), mCheckKeyFrameKey);
            }
        }
        mCheckKeyFrameKey = null;
        mCheckKeyFrameClip = null;
        mCheckKeyFrameAtTime = -1;
    }

    /**
     * Get remove param keys string [ ].
     * 获取要删除的关键帧帧参数的kye值
     *
     * @param keyOfKeyFrame the key of key frame 关键帧的key
     * @return the string [ ]
     */
    public String[] getRemoveParamKeys(String keyOfKeyFrame) {
        return KeyFrameProcessor.getRemoveParamKeys(keyOfKeyFrame);
    }

    /**
     * 是否需要添加字幕
     * is need add caption
     *
     * @param inPoint  the in point
     * @param outPoint the out point
     */
    public boolean isNeedAddCaption(long inPoint, long outPoint) {
        List<ClipInfo<?>> allAICaption = mEditorEngine.getAllAICaption();
        if (CommonUtils.isEmpty(allAICaption)) {
            return true;
        } else {
            for (int i = 0; i < allAICaption.size(); i++) {
                ClipInfo<?> clipInfo = allAICaption.get(i);
                if (clipInfo == null) {
                    continue;
                }
                if (mEditorEngine.isAICaption(clipInfo)) {
                    if (inPoint == clipInfo.getInPoint() && outPoint == clipInfo.getOutPoint() ||
                            (inPoint > clipInfo.getInPoint() && inPoint < clipInfo.getOutPoint()) ||
                            (outPoint > clipInfo.getInPoint() && outPoint < clipInfo.getOutPoint())) {
                        return false;
                    }
                }
            }
        }
        return true;
    }

    /**
     * 获取主轨道缩略图列表
     * Get main track thumbnail list
     */
    public List<ITrackClip> getMainTrackThumbnailList() {
        List<ITrackClip> data = new ArrayList<>();
        MeicamVideoTrack videoTrack = mEditorEngine.getVideoTrack(TRACK_INDEX_MAIN);
        if (videoTrack == null) {
            LogUtils.e("video track is null");
            return data;
        }
        List<AssetInfo> localTransitionList = MenuDataManager.getTransition(Utils.getApp());
        List<AssetInfo> tempList = AssetsManager.get().getPackageAssetList(AssetInfo.ASSET_VIDEO_TRANSITION);
        if (tempList != null) {
            localTransitionList.addAll(tempList);
        }
        tempList = AssetsManager.get().getPackageAssetList(AssetInfo.ASSET_VIDEO_TRANSITION_3D);
        if (tempList != null) {
            localTransitionList.addAll(tempList);
        }
        tempList = AssetsManager.get().getPackageAssetList(AssetInfo.ASSET_VIDEO_TRANSITION_EFFECT);
        if (tempList != null) {
            localTransitionList.addAll(tempList);
        }
        for (int i = 0; i < videoTrack.getClipCount(); i++) {
            MeicamVideoClip clipInfo = videoTrack.getVideoClip(i);
            ITrackClip thumbnailClip = getThumbnailClip(clipInfo);
            if (thumbnailClip.getIndexInTrack() != i) {
                thumbnailClip.setIndexInTrack(i);
            }
            ThumbnailClip.TailInfo tailInfo = new ThumbnailClip.TailInfo();
            MeicamTransition meicamTransition = videoTrack.getTransition(clipInfo.getIndex());
            if (meicamTransition != null) {
                int coverId = R.mipmap.ic_transition_added;
                /*if (meicamTransition.getIconResourceId() != 0 || !TextUtils.isEmpty(meicamTransition.getIconPath())) {
                    coverId = R.mipmap.ic_transition_added;
                }*/
                tailInfo.setId(meicamTransition.getDesc())
                        .setCoverId(coverId);
                //.setCoverPath(meicamTransition.getIconPath());
                if (!TextUtils.isEmpty(tailInfo.getId())) {
                    for (AssetInfo item : localTransitionList) {
                        if (tailInfo.getId().equals(item.getEffectId()) || tailInfo.getId().equals(item.getPackageId())) {
                            tailInfo.setType(item.getType());
                            break;
                        }
                    }
                }
            }
            ((ThumbnailClip) thumbnailClip).setTailInfo(tailInfo);
            data.add(thumbnailClip);
        }
        return data;
    }

    /**
     * 在数据库中根据转场id获取对应的类型
     * Get transition type by database
     *
     * @param transitionId the transition id
     */
    public int getTransitionTypeByDb(String transitionId) {
        AssetInfo assetInfo = AssetsManager.get().getAssetInfo(transitionId);
        if (assetInfo != null) {
            return assetInfo.getType();

        }
        return -1;
    }

    /**
     * 获取主轨道缩略图片段
     * Get main track thumbnail clip
     *
     * @param videoClip video clip
     */
    public ITrackClip getThumbnailClip(MeicamVideoClip videoClip) {
        ThumbnailClip thumbnailClip = new ThumbnailClip();
        thumbnailClip.setInPoint(videoClip.getInPoint());
        thumbnailClip.setOutPoint(videoClip.getOutPoint());
        thumbnailClip.setTrimIn(videoClip.getTrimIn());
        thumbnailClip.setTrimOut(videoClip.getTrimOut());

        String filePath;
        if (CommonData.CLIP_IMAGE.equals(videoClip.getVideoType())) {
            filePath = videoClip.getFilePath();
        } else {
            filePath = videoClip.getRemotePath();
            if (TextUtils.isEmpty(filePath)) {
                filePath = videoClip.getFilePath();
            }
        }
        MeicamVideoClip.ThumbNailInfo thumbNailInfo = videoClip.getThumbNailInfo();
        if (thumbNailInfo != null) {
            thumbnailClip.setThumbNailInfo(new ThumbnailClip.ThumbNailInfo(thumbNailInfo.urlPrefix, thumbNailInfo.interval, thumbNailInfo.extension, BaseTrackClip.CLIP_IMAGE.equals(videoClip.getVideoType())));
        }
        thumbnailClip.setAssetPath(videoClip.getVideoReverse() ? videoClip.getReverseFilePath() : filePath);
        SpeedInfo speedInfo = thumbnailClip.getSpeedInfo();
        speedInfo.setSpeed(videoClip.getSpeed());
        speedInfo.setSpeedName(videoClip.getCurveSpeedName());
        thumbnailClip.setTrackIndex(MAIN_TRACK_INDEX);
        /*
         *注意一下，这里 videoClip.getIndex()大部分情况是0的,但是thumbnailClip的索引要正确
         *
         * Note that videoClip. getIndex() is 0 in most cases, but ThumbnailClip's index should be correct
         * */
        thumbnailClip.setIndexInTrack(videoClip.getIndex());
        if (CommonData.CLIP_VIDEO.equals(videoClip.getVideoType())) {
            thumbnailClip.setType(BaseTrackClip.CLIP_VIDEO);
        } else if (CommonData.CLIP_IMAGE.equals(videoClip.getVideoType())) {
            thumbnailClip.setType(BaseTrackClip.CLIP_IMAGE);
        } else {
            thumbnailClip.setType(BaseTrackClip.CLIP_HOLDER);
        }
        AnimationData animationData = mEditorEngine.getVideoClipAnimation(videoClip);
        if (animationData != null) {
            AnimationInfo animationInfo = thumbnailClip.getAnimationInfo();
            animationInfo.copy(animationData);
            animationInfo.setTempType();
        }
        thumbnailClip.setOriginalDuration(videoClip.getOrgDuration());
        thumbnailClip.setKeyFrameInfo(getKeyFrameInfo(videoClip.findPropertyVideoFx(), NvsConstants.KEY_CROPPER_TRANS_X));
        thumbnailClip.setHasProp(!TextUtils.isEmpty(videoClip.getPropId()));
        float volume = CLIP_VIDEO.equals(videoClip.getVideoType()) ? videoClip.getVolume() : 1;
        thumbnailClip.setVolume(volume);
        return thumbnailClip;
    }

    /**
     * 获取关键帧信息
     * Get key frame info
     *
     * @param clipInfo the clip info 片段信息
     * @param key      the key 关键字
     * @return KeyFrameInfo the key frame info
     */
    public KeyFrameInfo getKeyFrameInfo(IKeyFrameProcessor<?> clipInfo, String key) {
        KeyFrameInfo keyFrameInfo = new KeyFrameInfo();
        if (clipInfo != null) {
            Map<Long, MeicamKeyFrame> keyFrameMap = clipInfo.keyFrameProcessor().getKeyFrameMap(key);
            if (keyFrameMap != null) {
                for (Map.Entry<Long, MeicamKeyFrame> item : keyFrameMap.entrySet()) {
                    MeicamKeyFrame keyFrame = item.getValue();
                    keyFrameInfo.addKeyFrame(keyFrame.getAtTime());
                }
            }
        }
        return keyFrameInfo;
    }

    /**
     * 获取关键帧信息
     * Get key frame info
     *
     * @param clipInfo the clip info 片段信息
     * @return KeyFrameInfo the key frame info
     */
    public KeyFrameInfo getKeyFrameInfo(IKeyFrameProcessor<?> clipInfo) {
        KeyFrameInfo keyFrameInfo = new KeyFrameInfo();
        if (clipInfo != null) {
            Map<Long, MeicamKeyFrame> keyFrameMap = clipInfo.keyFrameProcessor().getKeyFrameMap(null);
            if (keyFrameMap != null) {
                for (Map.Entry<Long, MeicamKeyFrame> item : keyFrameMap.entrySet()) {
                    MeicamKeyFrame keyFrame = item.getValue();
                    keyFrameInfo.addKeyFrame(keyFrame.getAtTime());
                }
            }
        }
        return keyFrameInfo;
    }

    /**
     * 更新关键帧信息
     * Update key frame info
     *
     * @param keyFrameInfo the key frame info 关键帧信息
     * @param clipInfo     the clip info 片段信息
     */
    public void updateKeyFrameInfo(KeyFrameInfo keyFrameInfo, IKeyFrameProcessor<?> clipInfo, String key) {
        if (keyFrameInfo != null && clipInfo != null) {
            Map<Long, MeicamKeyFrame> keyFrameMap = clipInfo.keyFrameProcessor().getKeyFrameMap(key);
            if (keyFrameMap != null) {
                keyFrameInfo.clear();
                for (Map.Entry<Long, MeicamKeyFrame> item : keyFrameMap.entrySet()) {
                    MeicamKeyFrame keyFrame = item.getValue();
                    keyFrameInfo.addKeyFrame(keyFrame.getAtTime());
                }
            }
        }
    }

    @Override
    public void onPause() {
        if (mStreamingContext != null) {
            mStreamingContext.stop();
        }
        super.onPause();
    }

    public boolean isLogin() {
        IUserPlugin userPlugin = PluginManager.get().getUserPlugin();
        if (userPlugin == null) {
            return false;
        }
        return userPlugin.isLogin();
    }

    public void checkAssets(final boolean isForTemplate) {
        List<String> data = getAssetsList();
        if (CommonUtils.isEmpty(data)) {
            goToActivity(isForTemplate);
            return;
        }
        IUserPlugin userPlugin = PluginManager.get().getUserPlugin();
        if (userPlugin != null) {
            AssetsManager.get().checkUnavailableAssets(userPlugin.getToken(), data, new RequestCallback<AssetList>() {
                @Override
                public void onSuccess(BaseResponse<AssetList> response) {
                    if (response.getData() == null || response.getData().realAssetList == null
                            || response.getData().realAssetList.isEmpty()) {
                        goToActivity(isForTemplate);
                    } else {
                        getView().showUnavailablePop(response.getData(), isForTemplate);
                    }
                }

                @Override
                public void onError(BaseResponse<AssetList> response) {
                    goToActivity(isForTemplate);
                }
            });
        }
    }

    private void goToActivity(boolean isForTemplate) {
        if (isForTemplate) {
            getView().goToExportTemplateView();
        } else {
            getView().goToExportVideoView();
        }
    }

    private List<String> getAssetsList() {
        List<String> listResult = new ArrayList<>();
        if (mMeicamTimeline != null) {
            Set<String> result = new HashSet<>();
            for (int i = 0; i < mMeicamTimeline.videoTrackCount(); i++) {
                MeicamVideoTrack videoTrack = mMeicamTimeline.getVideoTrack(i);
                if (videoTrack != null) {
                    for (int j = 0; j < videoTrack.getClipCount(); j++) {
                        MeicamVideoClip videoClip = videoTrack.getVideoClip(j);
                        for (int k = 0; k < videoClip.getVideoFxCount(); k++) {
                            MeicamVideoFx videoFx = videoClip.getVideoFx(k);
                            if (TYPE_PACKAGE.equals(videoFx.getType())) {
                                result.add(videoFx.getDesc());
                            }
                            if (TYPE_PROPERTY.equals(videoFx.getType())) {
                                AnimationData animationData = mEditorEngine.getVideoClipAnimation(videoClip);
                                if (!TextUtils.isEmpty(animationData.getPackageID())) {
                                    result.add(animationData.getPackageID());
                                }
                                if (!TextUtils.isEmpty(animationData.getPackageID2())) {
                                    result.add(animationData.getPackageID2());
                                }
                            }
                        }
                    }
                    for (int j = 0; j < videoTrack.getTransitionCount(); j++) {
                        MeicamTransition transition = videoTrack.getTransitionByCollectionIndex(j);
                        if (transition != null && TYPE_PACKAGE.equals(transition.getType())) {
                            String desc = transition.getDesc();
                            if (!TextUtils.isEmpty(desc)) {
                                result.add(desc);
                            }
                        }
                    }
                }
            }
            //字幕和贴纸
            //Caption and sticker
            for (int i = 0; i < mMeicamTimeline.getStickerCaptionTrackCount(); i++) {
                MeicamStickerCaptionTrack meicamStickerCaptionTrack = mMeicamTimeline.findStickCaptionTrack(i);
                if (meicamStickerCaptionTrack == null) {
                    continue;
                }
                for (int j = 0; j < meicamStickerCaptionTrack.getClipCount(); j++) {
                    ClipInfo<?> clipInfo = meicamStickerCaptionTrack.getCaptionStickerClip(j);
                    if (clipInfo == null) {
                        continue;
                    }
                    if (clipInfo instanceof MeicamStickerClip) {
                        MeicamStickerClip stickerClip = (MeicamStickerClip) clipInfo;
                        String packageId = stickerClip.getPackageId();
                        if (!TextUtils.isEmpty(packageId)) {
                            result.add(packageId);
                        }
                    } else if (clipInfo instanceof MeicamCaptionClip) {
                        MeicamCaptionClip captionClip = (MeicamCaptionClip) clipInfo;
                        //气泡
                        //Bubble
                        String uuid = captionClip.getBubbleUuid();
                        if (!TextUtils.isEmpty(uuid)) {
                            result.add(uuid);
                        }
                        //组合动画
                        //Combination animation
                        uuid = captionClip.getCombinationAnimationUuid();
                        if (!TextUtils.isEmpty(uuid)) {
                            result.add(uuid);
                        }
                        //入场动画
                        // March in animation
                        uuid = captionClip.getMarchInAnimationUuid();
                        if (!TextUtils.isEmpty(uuid)) {
                            result.add(uuid);
                        }
                        //出场动画
                        // March out animation
                        uuid = captionClip.getMarchOutAnimationUuid();
                        if (!TextUtils.isEmpty(uuid)) {
                            result.add(uuid);
                        }
                        //花字
                        // Rich word
                        uuid = captionClip.getRichWordUuid();
                        if (!TextUtils.isEmpty(uuid)) {
                            result.add(uuid);
                        }
                    } else if (clipInfo instanceof MeicamCompoundCaptionClip) {
                        MeicamCompoundCaptionClip compoundCaptionClip = (MeicamCompoundCaptionClip) clipInfo;
                        String styleDesc = compoundCaptionClip.getStyleDesc();
                        if (!TextUtils.isEmpty(styleDesc)) {
                            result.add(styleDesc);
                        }
                    }
                }
            }
            //timeline轨道上的特效
            // The video fx in timeline
            int count = mMeicamTimeline.getTimelineFxTrackCount();
            for (int i = 0; i < count; i++) {
                MeicamTimelineVideoFxTrack timelineFxTrack = mMeicamTimeline.getTimelineFxTrack(i);
                for (int j = 0; j < timelineFxTrack.getClipCount(); j++) {
                    MeicamTimelineVideoFxClip videoFxClip = timelineFxTrack.getClip(j);
                    if (!TextUtils.isEmpty(videoFxClip.getDesc())) {
                        result.add(videoFxClip.getDesc());
                    }
                }
            }
            //模糊 马赛克等
            // Fuzzy mosaic, etc
            for (int i = 0; i < mMeicamTimeline.getTimelineVideoFxClipCount(); i++) {
                MeicamTimelineVideoFxClip timelineFxFromClip = mMeicamTimeline.getTimelineFxFromClipList(i);
                if (!TextUtils.isEmpty(timelineFxFromClip.getDesc())) {
                    result.add(timelineFxFromClip.getDesc());
                }
            }
            //主题
            // Theme
            MeicamTheme meicamTheme = mMeicamTimeline.getMeicamTheme();
            if (meicamTheme != null) {
                String uuid = meicamTheme.getThemePackageId();
                if (!TextUtils.isEmpty(uuid)) {
                    result.add(uuid);
                }
            }
            //timeline上的滤镜
            // The filter in timeline
            MeicamTimelineVideoFx filterFx = mMeicamTimeline.getFilterFx();
            if (filterFx != null && TYPE_PACKAGE.equals(filterFx.getType())) {
                String uuid = filterFx.getDesc();
                if (!TextUtils.isEmpty(uuid)) {
                    result.add(uuid);
                }
            }

            if (!result.isEmpty()) {
                listResult.addAll(result);
            }
        }
        return listResult;
    }

    /**
     * 处理曲线变速
     * Deal with change curve speed
     *
     * @param meicamVideoClip the video clip
     * @param trackIndex      the track index
     * @param curveSpeed      the curve speed value
     * @param speedName       the curve name
     */
    public void dealWithCurveSpeed(MeicamVideoClip meicamVideoClip, int trackIndex, String curveSpeed, String speedName) {
        mEditorEngine.changeCurveSpeed(trackIndex, meicamVideoClip, curveSpeed, speedName);
        refreshAnimationCover(meicamVideoClip);
    }

    /**
     * 检查语音识别
     * Check voice dictation
     */
    public boolean checkVoiceDictation() {
        boolean isListening = VoiceDictationHelperWrapper.get().isListening();
        if (isListening) {
            ToastUtils.showShort(R.string.identifying_caption);
        }
        return isListening;
    }

    /**
     * 更新识别字幕和变速
     * update AI caption and change speed
     *
     * @param changeSpeedVideoClip the video clip 视频片段
     * @param speed                the video speed 速度
     * @param changeVoice          true change voice ,false not 是否变调
     */
    public void updateAiCaptionAndChaneSpeed(MeicamVideoClip changeSpeedVideoClip, float speed, boolean changeVoice) {
        if (changeSpeedVideoClip == null || mMeicamTimeline == null) {
            return;
        }

        mEditorEngine.changeNormalSpeed(MAIN_TRACK_INDEX, changeSpeedVideoClip, speed, changeVoice);

        refreshAnimationCover(changeSpeedVideoClip);
    }

    private void refreshAnimationCover(MeicamVideoClip changeSpeedVideoClip) {
        MeicamVideoFx videoFx = changeSpeedVideoClip.findPropertyVideoFx();
        if (videoFx == null) {
            return;
        }

        String packageId = videoFx.getStringVal(NvsConstants.PACKAGE_ID);
        String packagePostId = videoFx.getStringVal(NvsConstants.POST_PACKAGE_ID);
        String package2Id = videoFx.getStringVal(NvsConstants.PACKAGE2_ID);
        String package2PostId = videoFx.getStringVal(NvsConstants.POST_PACKAGE2_ID);
        boolean hasAnimation = (!TextUtils.isEmpty(packageId)) || (!TextUtils.isEmpty(packagePostId));
        boolean hasAnimation2 = (!TextUtils.isEmpty(package2Id)) || (!TextUtils.isEmpty(package2PostId));
        if (hasAnimation || hasAnimation2) {
            AnimationData animationData = new AnimationData();
            if (hasAnimation) {
                //入动画或组合动画  In animation or compound animation
                float effectIn = videoFx.getFloatVal(NvsConstants.PACKAGE_EFFECT_IN, -1);
                float effectOut = videoFx.getFloatVal(NvsConstants.PACKAGE_EFFECT_OUT, -1);
                if (effectOut > 0) {
                    animationData.setIsAnimationIn(videoFx.getBooleanVal(NvsConstants.PACKAGE_TYPE_ANIMATION_IN));
                    animationData.setInPoint((long) effectIn);
                    animationData.setOutPoint((long) effectOut);
                    animationData.setInPoint2(0);
                    animationData.setOutPoint2(0);
                }
            }
            if (hasAnimation2) {
                //出动画  Out animation
                float effect2Out = videoFx.getFloatVal(NvsConstants.PACKAGE2_EFFECT_OUT, -1);
                float effect2In = videoFx.getFloatVal(NvsConstants.PACKAGE2_EFFECT_IN, -1);
                if ((effect2Out > 0 && effect2In >= 0)) {
                    animationData.setInPoint2((long) effect2In);
                    animationData.setOutPoint2((long) effect2Out);
                }
            }
            getView().refreshCoverView(animationData);
        }
    }

    /**
     * 还原导出模板页面对时间线做出的改动。
     * Restore the changes made to the timeline by the export template page
     *
     * @param mainTrackMute true open original voice 主轨道是否开启原声
     */
    public void restoreExportTemplateChange(boolean mainTrackMute) {
        if (mMeicamTimeline != null) {
            MeicamVideoTrack videoTrack = mMeicamTimeline.getVideoTrack(TRACK_INDEX_MAIN);
            if (videoTrack != null) {
                videoTrack.setIsMuteByVideoClip(mainTrackMute);
            }
            int videoCount = mMeicamTimeline.videoTrackCount();
            for (int i = 1; i < videoCount; i++) {
                videoTrack = mMeicamTimeline.getVideoTrack(i);
                if (videoTrack != null) {
                    /*画中画轨道，目前永远是无法被静音的
                     * Picture-in-picture track can never be silenced at present
                     * */
                    videoTrack.setIsMuteByVideoClip(false);
                }
            }
        }
    }

    /**
     * 原声是否打开
     * Whether the original sound is turned on
     */
    public boolean originalVoiceIsOpen() {
        MeicamVideoTrack videoTrack = mEditorEngine.getVideoTrack(TRACK_INDEX_MAIN);
        if (videoTrack != null) {
            return !videoTrack.isMute() && hasAudioInMainTrack();
        }
        return false;
    }

    /**
     * 处理视频片段的移动
     * Handle the movement of video clips
     *
     * @param from int from index
     * @param to   int to index
     */
    public boolean dealWithVideoClipMove(int from, int to) {
        MeicamVideoTrack videoTrack = mEditorEngine.getVideoTrack(TRACK_INDEX_MAIN);
        boolean isMoveSuccess = false;
        TimelineFxBinder.updateTimelineFxTargetVideoClipInMain(from, to);
        if (videoTrack != null) {
            MeicamVideoClip beforeVideoClip = videoTrack.getVideoClip(from);
            long originInPoint = beforeVideoClip.getInPoint();
            long originOutPoint = beforeVideoClip.getOutPoint();
            long p1, p2, p4;
            MeicamVideoClip afterVideoClip = videoTrack.getVideoClip(to);
            if (from < to) {
                p1 = originInPoint;
                p2 = originOutPoint;
                p4 = afterVideoClip.getOutPoint();
            } else {
                p1 = afterVideoClip.getInPoint();
                p2 = beforeVideoClip.getInPoint();
                p4 = beforeVideoClip.getOutPoint();
            }
            isMoveSuccess = VideoTrackCommand.moveClip(videoTrack, from, to);
            if (isMoveSuccess) {
                mEditorEngine.timeRangeExchange(p1, p2, p4);
                mEditorEngine.seekTimeline(0, 0);
            }

        }
        return isMoveSuccess;
    }

    /**
     * Get caption sticker line region list.
     * 获取字幕 贴纸，组合字幕线区域
     *
     * @return the list
     */
    public List<LineRegionClip> getCaptionStickerLineRegion(String type) {
        List<LineRegionClip> regionList = new ArrayList<>();
        for (int i = 0; i < mMeicamTimeline.getStickerCaptionTrackCount(); i++) {
            MeicamStickerCaptionTrack stickerCaptionTrack = mMeicamTimeline.findStickCaptionTrack(i);
            if (stickerCaptionTrack == null) {
                continue;
            }
            for (int j = stickerCaptionTrack.getClipCount() - 1; j >= 0; j--) {
                ClipInfo<?> clipInfo = stickerCaptionTrack.getCaptionStickerClip(j);
                if (clipInfo == null) {
                    continue;
                }
                if (type.equals(clipInfo.getType())) {
                    regionList.add(getLineRegionClip(clipInfo, stickerCaptionTrack.getIndex(), false));
                }
            }
        }
        return regionList;
    }

    /**
     * 获取主轨道线图区域坐标
     * Get main track line region
     */
    public void getMainTrackLineRegion() {
        List<LineRegionClip> captionRegion = new ArrayList<>();
        List<LineRegionClip> compoundCaptionRegion = new ArrayList<>();
        List<LineRegionClip> stickerRegion = new ArrayList<>();
        for (int i = 0; i < mMeicamTimeline.getStickerCaptionTrackCount(); i++) {
            MeicamStickerCaptionTrack meicamStickerCaptionTrack = mMeicamTimeline.findStickCaptionTrack(i);
            if (meicamStickerCaptionTrack == null) {
                return;
            }
            for (int j = 0; j < meicamStickerCaptionTrack.getClipCount(); j++) {
                ClipInfo<?> clipInfo = meicamStickerCaptionTrack.getCaptionStickerClip(j);
                LineRegionClip lineRegionClip = getLineRegionClip(clipInfo, meicamStickerCaptionTrack.getIndex(), false);
                if (CommonData.CLIP_CAPTION.equals(clipInfo.getType())) {
                    captionRegion.add(lineRegionClip);
                } else if (CommonData.CLIP_COMPOUND_CAPTION.equals(clipInfo.getType())) {
                    compoundCaptionRegion.add(lineRegionClip);
                } else if (CommonData.CLIP_STICKER.equals(clipInfo.getType())) {
                    stickerRegion.add(lineRegionClip);
                }
            }
        }
        //pipRegion = getPipLineRegion();
        getPipLineRegionAsync(data -> getView().onMainTrackLineRegionChange(captionRegion, compoundCaptionRegion, stickerRegion, data));
    }


    /**
     * 获取画中画字幕线区域
     * Get PIP line region
     */
    public List<LineRegionClip> getPipLineRegion() {
        List<LineRegionClip> pipRegion = new ArrayList<>();
        for (int i = 1; i < mMeicamTimeline.videoTrackCount(); i++) {
            MeicamVideoTrack videoTrack = mMeicamTimeline.getVideoTrack(i);
            if (videoTrack != null) {
                for (int j = 0; j < videoTrack.getClipCount(); j++) {
                    pipRegion.add(getLineRegionClip(videoTrack.getVideoClip(j), i, true));
                }
            }
        }
        return pipRegion;
    }

    /**
     * Change clip to sub track.
     * 主轨且画中画
     *
     * @param selectVideoClip the select video clip 选中的videoClip
     */
    public void changeClipToSubTrack(MeicamVideoClip selectVideoClip) {
        if (mMeicamTimeline == null || selectVideoClip == null) {
            return;
        }

        MeicamVideoTrack mainVideoTrack = mMeicamTimeline.getVideoTrack(MAIN_TRACK_INDEX);
        if (mainVideoTrack.getClipCount() == 1) {
            getView().onClipChangedToSubTrack(null, null);
            return;
        }
        if (mainVideoTrack.getClipCount() == 2) {
            MeicamVideoClip videoClip = mainVideoTrack.getVideoClip(1);
            if (CommonData.CLIP_HOLDER.equals(videoClip.getVideoType())) {
                getView().onClipChangedToSubTrack(null, null);
                return;
            }
        }

        int index = selectVideoClip.getIndex();
        // 删除当前片段对应的转场
        VideoTrackCommand.removeTransition(mainVideoTrack, index);
        getView().onDeleteTransition(selectVideoClip.getIndex());
        if (selectVideoClip.getIndex() > 0) {
            MeicamVideoClip nextVideoClip = mainVideoTrack.getVideoClip(index + 1);
            if (nextVideoClip == null || CLIP_HOLDER.equals(nextVideoClip.getVideoType())) {
                // 如果没有下一个片段或者是下一个是占位片段，则把上一个转场也删除。
                VideoTrackCommand.removeTransition(mainVideoTrack, index - 1);
                getView().onDeleteTransition(index - 1);
            }
        }

        MeicamVideoClip removedClip = VideoTrackCommand.removeVideoClip(mainVideoTrack, index, false);
        //查找空隙 find the space to insert clip
        int count = mMeicamTimeline.videoTrackCount();
        MeicamVideoTrack videoTrack = null;
        long selectClipInPoint = selectVideoClip.getInPoint();
        long selectClipOutPoint = selectVideoClip.getOutPoint();
        if (count <= 1) {
            videoTrack = TimelineCommand.appendVideoTrack(mMeicamTimeline);
        } else {
            for (index = 1; index < count; index++) {
                MeicamVideoTrack trackItem = mMeicamTimeline.getVideoTrack(index);
                int clipCount = trackItem.getClipCount();
                //空轨道 empty track
                if (clipCount <= 0) {
                    videoTrack = trackItem;
                    break;
                }
                long startTime;
                long endTime;
                for (int clipIndex = -1; clipIndex < clipCount; clipIndex++) {
                    MeicamVideoClip perClip = trackItem.getVideoClip(clipIndex);
                    MeicamVideoClip nextClip = trackItem.getVideoClip(clipIndex + 1);
                    endTime = nextClip != null ? nextClip.getInPoint() : mMeicamTimeline.getDuration();
                    startTime = perClip != null ? perClip.getOutPoint() : 0;
                    if (selectClipInPoint >= startTime && selectClipOutPoint <= endTime) {
                        videoTrack = trackItem;
                        break;
                    }
                }
            }
        }
        if (videoTrack == null) {
            videoTrack = TimelineCommand.appendVideoTrack(mMeicamTimeline);
        }
        if (videoTrack == null) {
            return;
        }
        //插入 insert clip
        MeicamVideoClip addedClip = VideoTrackCommand.addVideoClip(videoTrack, selectVideoClip, selectClipInPoint, selectVideoClip.getTrimIn(), selectVideoClip.getTrimOut());
        if (addedClip != null) {
            //删除背景 delete background
            VideoFxCommand.deleteBackground(addedClip.findPropertyVideoFx());
        }
        TimelineFxBinder.updateTimelineFxTarget(addedClip, false);
        getView().onClipChangedToSubTrack(removedClip, addedClip);
    }

    /**
     * Change clip to main track.
     * 画中画clip切主轨clip
     *
     * @param selectVideoClip the select video clip 选中的videoClip
     */
    public void changeClipToMainTrack(MeicamVideoClip selectVideoClip) {
        if (mMeicamTimeline == null || selectVideoClip == null) {
            return;
        }
        long currentPosition = getTimelineCurrentPosition();
        MeicamVideoTrack videoTrack = mMeicamTimeline.getVideoTrack(MAIN_TRACK_INDEX);
        MeicamVideoClip videoClip = videoTrack.getClipByTimelinePosition(currentPosition);
        if (videoClip == null) {
            return;
        }
        if (CommonData.CLIP_HOLDER.equals(videoClip.getVideoType())) {
            videoClip = videoTrack.getVideoClip(videoClip.getIndex() - 1);
        }
        if (videoClip == null) {
            return;
        }
        int insertIndex = videoClip.getIndex();
        //查找插入的index
        //Find the inserted index
        insertIndex = currentPosition - videoClip.getInPoint() <= videoClip.getOutPoint() - currentPosition ? insertIndex : insertIndex + 1;
        //删除clip
        // Remove clip
        MeicamVideoClip removedClip = VideoTrackCommand.removeVideoClipWidthSpace(mMeicamTimeline.getVideoTrack(selectVideoClip.getTrackIndex()), selectVideoClip.getIndex());
        //插入
        // Insert
        MeicamVideoClip addedClip = VideoTrackCommand.insertVideoClip(videoTrack, selectVideoClip, insertIndex, selectVideoClip.getTrimIn(), selectVideoClip.getTrimOut());
        TimelineFxBinder.updateTimelineFxTarget(addedClip, true);
        getView().onClipChangedToMainTrack(removedClip, addedClip);
    }

    /**
     * Replace clip.
     * 替换clip
     *
     * @param replacedVideoClip the replaced video clip
     * @param filePath          the file path
     * @param newTrim           the new trim
     */
    public void replaceClip(Context context, MeicamVideoClip replacedVideoClip, String filePath, long newTrim) {
        NvsAVFileInfo avFileInfo = mEditorEngine.getStreamingContext().getAVFileInfo(filePath);
        if (avFileInfo == null) {
            getView().onReplaceVideoClipFinish(null);
            return;
        }
        if (WhiteList.checkVideoAssetIs4K(avFileInfo)) {
            ConvertFileManager.ConvertParam convertParam = new ConvertFileManager.ConvertParam();
            convertParam.appendParam(filePath, "", false);
            convert(context, convertParam, new ConvertFileManager.EventListener() {
                @Override
                public void onConvertFinish(ConvertFileManager.ConvertParam convertParam, boolean convertSuccess) {
                    if (convertSuccess) {
                        Map<String, ConvertFileManager.ConvertParam.Param> paramMap = convertParam.getParamMap();
                        if (paramMap != null) {
                            ConvertFileManager.ConvertParam.Param param = paramMap.get(filePath);
                            if (param != null) {
                                String dstFile = param.getDstFile();
                                NvsAVFileInfo avFileInfo = mEditorEngine.getStreamingContext().getAVFileInfo(dstFile);
                                doReplaceVideoClip(replacedVideoClip, dstFile, newTrim, avFileInfo);
                                return;
                            }
                        }
                    }
                    getView().onReplaceVideoClipFinish(null);
                }

                @Override
                public void onConvertCancel() {
                    getView().onReplaceVideoClipFinish(null);
                }
            });
        } else {
            doReplaceVideoClip(replacedVideoClip, filePath, newTrim, avFileInfo);
        }
    }

    /**
     * Convert.
     * 转码
     *
     * @param context      the context 上下文
     * @param convertParam the convert parameter 转码参数
     * @param listener     the listener 转码回调
     */
    private void convert(Context context, ConvertFileManager.ConvertParam convertParam, ConvertFileManager.EventListener listener) {
        ConvertProgressPop.create(context, convertParam, listener)
                .setHintText(R.string.video_clip_replace_progress)
                .show();
    }

    private void doReplaceVideoClip(MeicamVideoClip replacedVideoClip, String filePath, long newTrim, NvsAVFileInfo avFileInfo) {
        MeicamVideoFx videoFx = replacedVideoClip.getVideoFx(SUB_TYPE_ALPHA, SET_ALPHA);
        if (videoFx != null) {
            VideoClipCommand.removeFx(replacedVideoClip, videoFx);
        }
        videoFx = replacedVideoClip.getVideoFx(SUB_TYPE_SEGMENT, SEGMENTATION);
        if (videoFx != null) {
            VideoClipCommand.removeFx(replacedVideoClip, videoFx);
        }

        VideoClipCommand.setParam(replacedVideoClip, VideoClipCommand.PARAM_FILE_PATH, filePath);
        int avType = avFileInfo.getAVFileType();
        boolean isVideo = avType == NvsAVFileInfo.AV_FILE_TYPE_AUDIOVIDEO;
        if (isVideo) {
            long duration = replacedVideoClip.getTrimOut() - replacedVideoClip.getTrimIn();
            VideoClipCommand.setTrimIn(replacedVideoClip, 0, true);
            VideoClipCommand.setTrimOut(replacedVideoClip, newTrim + duration, true);
            VideoClipCommand.setTrimIn(replacedVideoClip, newTrim, true);
        } else {
            long duration = replacedVideoClip.getOutPoint() - replacedVideoClip.getInPoint();
            VideoClipCommand.setSpeed(replacedVideoClip, 1.0f, true);
            VideoClipCommand.changeTrimInAndOut(replacedVideoClip, 0,
                    duration, true);
        }

        VideoClipCommand.setParam(replacedVideoClip, VideoClipCommand.PARAM_REVERSE_FILE_PATH, null);
        VideoClipCommand.setParam(replacedVideoClip, VideoClipCommand.PARAM_VIDEO_TYPE, isVideo ? CommonData.CLIP_VIDEO : CommonData.CLIP_IMAGE);
        long duration = CommonData.DEFAULT_LENGTH;
        if (isVideo) {
            duration = avFileInfo.getDuration();
        }
        VideoClipCommand.setParam(replacedVideoClip, VideoClipCommand.PARAM_ORG_DURATION,
                duration);
        NvsSize nvsSize = avFileInfo.getVideoStreamDimension(0);
        int streamRotation = avFileInfo.getVideoStreamRotation(0);
        if (streamRotation == 1 || streamRotation == 3) {
            VideoClipCommand.setParam(replacedVideoClip, VideoClipCommand.PARAM_ORG_WIDTH, nvsSize.height);
            VideoClipCommand.setParam(replacedVideoClip, VideoClipCommand.PARAM_ORG_HEIGHT, nvsSize.width);
        } else {
            VideoClipCommand.setParam(replacedVideoClip, VideoClipCommand.PARAM_ORG_WIDTH, nvsSize.width);
            VideoClipCommand.setParam(replacedVideoClip, VideoClipCommand.PARAM_ORG_HEIGHT, nvsSize.height);
        }
        getView().onReplaceVideoClipFinish(replacedVideoClip);
    }

    public void getCover() {
        String imagePath = mMeicamTimeline.getCoverImagePath();
        if (imagePath != null) {
            getView().setCover(imagePath);
        } else {
            EngineCallbackManager.get().registerCallbackObserver(new EngineCallbackObserver() {
                @Override
                public boolean isActive() {
                    return true;
                }

                @Override
                public void onImageGrabbedArrived(Bitmap bitmap, long time) {
                    super.onImageGrabbedArrived(bitmap, time);
                    EngineCallbackManager.get().unregisterCallbackObserver(this);
                    ThreadUtils.getIoPool().execute(() -> {
                        String draftRootDir = mMeicamTimeline.getDraftDir();
                        if (draftRootDir == null) {
                            LogUtils.e("draftRootDir is null");
                            return;
                        }
                        File folder = new File(draftRootDir + File.separator + System.currentTimeMillis());
                        if (!folder.exists()) {
                            folder.mkdir();
                        }
                        ImageUtils.save(bitmap, folder, Bitmap.CompressFormat.PNG);
                        String coverPath = folder.getAbsolutePath();
                        mMeicamTimeline.setCoverImagePath(coverPath);
                        ThreadUtils.runOnUiThread(() -> getView().setCover(coverPath));
                    });
                }
            });
            mEditorEngine.grabImageFromTimelineAsync(mMeicamTimeline, 0, new NvsRational(1, 1));
            mEditorEngine.stop();
        }
    }

    /**
     * Change color picker param.
     * 修改色度抠图参数
     *
     * @param videoClip the video clip
     * @param key       the key
     * @param value     the value
     */
    public void changeColorPickerParam(MeicamVideoClip videoClip, String key, Object value, FloatPoint colorPosition) {
        if (TextUtils.isEmpty(key) || videoClip == null) {
            LogUtils.e("param is invalid!");
            return;
        }
        MeicamVideoFx videoFx = videoClip.getVideoFxById(NvsConstants.MasterKeyer.NAME);
        if (videoFx == null) {
            videoFx = VideoClipCommand.appendFx(videoClip, TYPE_RAW_BUILTIN, SUB_TYPE_MASTER_KEYER, NvsConstants.MasterKeyer.NAME);
        }
        if (videoFx == null) {
            LogUtils.e("videoFx is null, please check it!");
            return;
        }
        //默认设置强度为350 The default setting strength is 350
        float aperture = videoFx.getFloatVal(NvsConstants.MasterKeyer.KEY_APERTURE);
        if (MeicamVideoFx.INVALID_VALUE == aperture) {
            VideoFxCommand.setFloatVal(videoFx, NvsConstants.MasterKeyer.KEY_APERTURE, NvsConstants.MasterKeyer.APERTURE_MAX);
        }
        VideoFxCommand.setBooleanVal(videoFx, NvsConstants.MasterKeyer.SPILL_REMOVAL, true);
        if (colorPosition != null) {
            VideoFxCommand.setObjectVal(videoFx, NvsConstants.MasterKeyer.KEY_COLOR_POSITION, colorPosition);
        }

        if (NvsConstants.MasterKeyer.KEY_COLOR.equals(key)) {
            if (value instanceof NvsColor) {
                VideoFxCommand.setColorVal(videoFx, key, ColorUtil.nvsColorToHexString((NvsColor) value));
            } else {
                VideoFxCommand.setColorVal(videoFx, key, "#00000000");
            }
        } else {
            if (value instanceof Integer) {
                float fValue;
                if (NvsConstants.MasterKeyer.KEY_APERTURE.equals(key)) {
                    int iValue = (int) value;
                    fValue = iValue / 100F * NvsConstants.MasterKeyer.APERTURE_MAX;
                } else {
                    int iValue = (int) value;
                    fValue = iValue / 100F;
                }
                VideoFxCommand.setFloatVal(videoFx, key, fValue);
            }
        }

        float floatVal = videoFx.getFloatVal(NvsConstants.MasterKeyer.APERTURE, 0);
        if (floatVal == 0) {
            VideoFxCommand.setFloatVal(videoFx, NvsConstants.MasterKeyer.APERTURE, 0);
        }
        mEditorEngine.seekTimeline();
    }

    /**
     * Reset color picker param.
     * 重置色度抠图参数
     *
     * @param videoClip the video clip
     */
    public void resetColorPickerParam(MeicamVideoClip videoClip) {
        if (videoClip == null) {
            LogUtils.e("param is invalid!");
            return;
        }
        MeicamVideoFx videoFx = videoClip.getVideoFxById(NvsConstants.MasterKeyer.NAME);
        if (videoFx == null) {
            return;
        }
        VideoFxCommand.setBooleanVal(videoFx, NvsConstants.MasterKeyer.SPILL_REMOVAL, true);
        VideoFxCommand.setColorVal(videoFx, NvsConstants.MasterKeyer.KEY_COLOR, "#00000000");
        VideoFxCommand.setFloatVal(videoFx, NvsConstants.MasterKeyer.SPILL_REMOVAL_INTENSITY, 0);
        VideoFxCommand.setFloatVal(videoFx, NvsConstants.MasterKeyer.SHRINK_INTENSITY, 0);
        VideoFxCommand.setFloatVal(videoFx, NvsConstants.MasterKeyer.KEY_APERTURE, NvsConstants.MasterKeyer.APERTURE_MAX);
        mEditorEngine.seekTimeline();
    }

    public float[] getMasterKeyerParam(MeicamVideoClip videoClip) {
        if (videoClip == null) {
            return null;
        }
        MeicamVideoFx videoFx = videoClip.getVideoFxById(NvsConstants.MasterKeyer.NAME);
        if (videoFx != null) {
            float[] result = new float[3];
            result[0] = videoFx.getFloatVal(NvsConstants.MasterKeyer.SPILL_REMOVAL_INTENSITY, 0);
            result[1] = videoFx.getFloatVal(NvsConstants.MasterKeyer.SHRINK_INTENSITY, 0);
            float floatVal = videoFx.getFloatVal(NvsConstants.MasterKeyer.KEY_APERTURE, NvsConstants.MasterKeyer.APERTURE_MAX);
            result[2] = floatVal / NvsConstants.MasterKeyer.APERTURE_MAX;
            return result;
        } else {
            float[] result = new float[3];
            result[2] = 1;
            return result;
        }
    }

    /**
     * Change timeline fx to target.
     * 修改timeline 特技到指定目标
     *
     * @param timelineVideoFxClip the timeline video fx clip timeline特技
     * @param targetInfo          the target info 目标信息
     */
    public boolean changeTimelineFxToTarget(MeicamTimelineVideoFxClip timelineVideoFxClip, MYEffectTargetMenuView.TargetInfo targetInfo) {
        if (targetInfo == null) {
            LogUtils.e("Param is invalid !");
            return false;
        }
        return TimelineFxBinder.changeTimelineFxToTarget(timelineVideoFxClip, targetInfo.trackIndex, targetInfo.clipIndex);
    }

    /**
     * Update timeline fx target in and out point.
     * 更新timeline特效作用的目标对象的出入点
     *
     * @param timelineVideoFxClip the timeline video fx clip
     */
    public void updateTimelineFxTargetInAndOutPoint(MeicamTimelineVideoFxClip timelineVideoFxClip) {
        TimelineFxBinder.updateTimelineFxTargetInAndOutPoint(timelineVideoFxClip);
    }

    /**
     * Update timeline fx target when video clip drag.
     * Video clip 拖动时，更新timelineFx绑定的目标的出入点
     *
     * @param dragClip the drag clip
     */
    public void updateTimelineFxTargetVideoClipInPip(MeicamVideoClip dragClip) {
        TimelineFxBinder.updateTimelineFxTargetVideoClipInPip(dragClip);
    }

    /**
     * Update timeline fx target when video clip drag.
     * Video clip 拖动时，更新timelineFx绑定的目标的出入点
     *
     * @param dragClip the drag clip
     * @param offset   the offset of inPoint and outPoint
     */
    public void updateTimelineFxTargetVideoClipInMain(MeicamVideoClip dragClip, long offset) {
        TimelineFxBinder.updateTimelineFxTargetVideoClipInMain(dragClip, offset);
    }


    public void updateTimelineFxTargetWhenVideoClipRemoved(MeicamVideoClip removedClip) {
        TimelineFxBinder.updateTimelineFxTargetWhenVideoClipRemoved(removedClip);
    }

    public interface LineRegionDataCallBack {
        /**
         * On result callback.
         *
         * @param data the data
         */
        void onResultCallback(List<LineRegionClip> data);
    }

    /**
     * 异步获取画中画字幕线区域
     * Get PIP line region
     */
    public void getPipLineRegionAsync(LineRegionDataCallBack callBack) {
        List<LineRegionClip> pipRegion = new ArrayList<>();
        for (int i = 1; i < mMeicamTimeline.videoTrackCount(); i++) {
            MeicamVideoTrack videoTrack = mMeicamTimeline.getVideoTrack(i);
            if (videoTrack != null) {
                for (int j = 0; j < videoTrack.getClipCount(); j++) {
                    MeicamVideoClip videoClip = videoTrack.getVideoClip(j);
                    LineRegionClip regionClip = getLineRegionClip(videoClip, i, true);
                    pipRegion.add(regionClip);
                }
            }
        }
        final int[] count = {pipRegion.size()};
        if (count[0] > 0) {
            NvsIconGenerator iconGenerator = EditorEngine.getInstance().getIconGenerator();
            NvsIconGenerator.IconCallback iconCallback = (bitmap, l, l1) -> {
                LogUtils.d("onIconReady: 查询新数据");
                if (bitmap != null) {
                    for (LineRegionClip lineRegionClip : pipRegion) {
                        if (l1 == lineRegionClip.getBitmapTaskId()) {
                            lineRegionClip.setBitmap(bitmap);
                            count[0]--;
                        }
                    }
                }
                if (count[0] == 0) {
                    if (callBack != null) {
                        callBack.onResultCallback(pipRegion);
                    }
                }
            };
            iconGenerator.setIconCallback(iconCallback);
            for (int index = 0; index < pipRegion.size(); index++) {
                LineRegionClip regionClip = pipRegion.get(index);
                LineRegionClip.BitmapDesc bitmapDesc = regionClip.getBitmapDesc();
                if (bitmapDesc != null) {
                    ConvertUtil.BitmapResult bitmapResult = ConvertUtil.getBitmapFromClipInfoAsync(Utils.getApp(), bitmapDesc);
                    if (bitmapResult != null) {
                        if (bitmapResult.bitmap != null) {
                            LogUtils.d("onIconReady: 查询缓存数据");
                            regionClip.setBitmap(bitmapResult.bitmap);
                            count[0]--;
                        } else {
                            regionClip.setBitmapTaskId(bitmapResult.task);
                        }
                    }
                } else {
                    count[0]--;
                }
            }
        }
        if (callBack != null) {
            callBack.onResultCallback(pipRegion);
        }
    }

    /**
     * 获取线图区域片段
     * Get line view region clip
     *
     * @param clipInfo   the video clip 视频片段
     * @param trackIndex the track index 所在轨道索引
     * @param needBitmap true need bitmap 是否需要bitmap
     */
    public LineRegionClip getLineRegionClip(ClipInfo<?> clipInfo, int trackIndex, boolean needBitmap) {
        LineRegionClip regionClip = new LineRegionClip();
        regionClip.setTrackIndex(trackIndex);
        if (clipInfo != null) {
            regionClip.setInPoint(clipInfo.getInPoint());
            regionClip.setOutPoint(clipInfo.getOutPoint());
        }
        if (needBitmap && clipInfo instanceof MeicamVideoClip) {
            regionClip.setBitmapDesc(new LineRegionClip.BitmapDesc(clipInfo.getFilePath(), clipInfo.getTrimIn()));
            //regionClip.setBitmap(ConvertUtil.getBitmapFromClipInfo(Utils.getApp(), (MeicamVideoClip) clipInfo));
        }
        return regionClip;
    }

    /**
     * 寻找画中画片段
     * Find PIP Video clip
     *
     * @param trackIndex the track index .所在的轨道索引
     * @param inPoint    the in point .入点
     */
    public MeicamVideoClip findPipVideoClip(int trackIndex, long inPoint) {
        return mEditorEngine.getVideoClip(trackIndex, inPoint);
    }

    private NvsStreamingContext mAuxiliaryStreamingContext;
    private MeicamVideoClip mTempSmartKeyerClip;

    /**
     * 开始智能抠像，注意本来智能抠像只需要加一个AR Scene特效然后应用背景分割特效包即可实现(也可以添加SEGMENTATION特效)，
     * 但是这种方式播放的时候卡。所以采用了导出一个智能抠像过的视频然后给视频片段设置Alpha特技的方式去实现。
     * Start smart keyer,Note that originally intelligent image keying only needs to add an AR Scene effect
     * and then apply the background SEGMENTATION effect package to achieve it (also can add SEGMENTATION effect).
     * But there's a lag when it's played this way.Therefore, it uses the method of exporting a smart key-shaped video
     * and then setting Alpha stunt for the video clip to achieve this
     *
     * @param videoClip the video clip视频片段
     */
    public void startSmartKeyer(final MeicamVideoClip videoClip) {
        if (videoClip != null) {
            String videoPath = videoClip.getVideoReverse() ? videoClip.getReverseFilePath() : videoClip.getFilePath();
            if (TextUtils.isEmpty(videoPath)) {
                return;
            }
            if (videoPath.endsWith("m3u8")) {
                MeicamVideoFx videoFx = videoClip.getVideoFxById(SEGMENTATION);
                if (videoFx == null) {
                    videoFx = VideoClipCommand.appendFx(videoClip, TYPE_RAW_BUILTIN, SUB_TYPE_SEGMENT, SEGMENTATION);
                }
                if (videoFx != null) {
                    VideoFxCommand.setBooleanVal(videoFx, INVERSE_SEGMENT, true);
                }
                ToastUtils.showShort(R.string.smart_keyer_success);
                mMeicamTimeline.seekTimeline(mStreamingContext, mMeicamTimeline.getCurrentPosition(), 0);
                getView().onSmartKeyerComplete(true);
                return;
            }
            boolean isVideo = CLIP_VIDEO.equals(videoClip.getVideoType());
            String clipFilePath = videoClip.getVideoReverse() ? videoClip.getReverseFilePath() : videoClip.getFilePath();
            String smartKeyerFilePath = PathUtils.getSmartKeyerFilePath(clipFilePath, isVideo, videoClip.getVideoReverse());
            if (!TextUtils.isEmpty(smartKeyerFilePath) && new File(smartKeyerFilePath).exists()) {
                MeicamVideoFx videoFx = VideoClipCommand.appendFx(videoClip, TYPE_RAW_BUILTIN, SUB_TYPE_ALPHA, SET_ALPHA);
                if (videoFx != null) {
                    //已经有对应的抠像文件，直接设置
                    // There is already a corresponding image file, which can be set directly
                    VideoFxCommand.setStringVal(videoFx, ALPHA_FILE, smartKeyerFilePath);
                    VideoFxCommand.setBooleanVal(videoFx, ALPHA_CLIP_TRIM_USED, true);
                }
                ToastUtils.showShort(R.string.smart_keyer_success);
                mMeicamTimeline.seekTimeline(mStreamingContext, mMeicamTimeline.getCurrentPosition(), 0);
                getView().onSmartKeyerComplete(true);
                return;
            }
            if (videoClip.getOriginalWidth() >= 2160 || videoClip.getOriginalHeight() >= 3840) {
                /*4k以上视频比较耗费性能，内存可能不够，暂时不开放
                 * Videos above 4k consume performance, memory may not be enough, and they are temporarily closed.
                 * */
                ToastUtils.showShort(R.string.not_support_4k);
                return;
            }
            String filePath = PathUtils.getTempSmartKeyerFilePath(videoClip.getFilePath(), isVideo, videoClip.getVideoReverse());
            if (mAuxiliaryStreamingContext == null) {
                mAuxiliaryStreamingContext = mEditorEngine.createAuxiliaryStreamingContext();
            }
            if (mAuxiliaryStreamingContext != null) {
                getView().onSmartKeyerProgress(0);
                NvsVideoResolution videoResolution = new NvsVideoResolution();
                videoResolution.imageHeight = EditorEngine.alignedData(videoClip.getOriginalHeight(), 2);
                videoResolution.imageWidth = EditorEngine.alignedData(videoClip.getOriginalWidth(), 4);
                videoResolution.imagePAR = new NvsRational(1, 1);
                //创建新的时间线
                //Create new timeline
                final MeicamTimeline timeline = new MeicamTimeline.TimelineBuilder(mAuxiliaryStreamingContext, MeicamTimeline.TimelineBuilder.BUILD_NORMAL)
                        .setVideoResolution(videoResolution)
                        .build();
                if (timeline != null) {
                    MeicamVideoTrack videoTrack = timeline.appendVideoTrack();
                    final String compileVideoPath = filePath;
                    if (videoTrack != null) {
                        MeicamVideoClip newVideoClip = videoTrack.appendVideoClip(videoPath);
                        if (newVideoClip != null) {
                            /*原来的clip添加背景分割特效。这里添加是为了预览，导出抠像后的视频应用alpha特效后，背景分割特效还要删除
                             * The original clip adds background segmentation effects.
                             * This is added for preview. After the alpha effect is applied to the exported video,
                             * the background segmentation effect should be deleted
                             * */
                            final MeicamVideoFx videoFx = videoClip.appendVideoFx(TYPE_BUILD_IN, SUB_TYPE_SEGMENT, SEGMENTATION, true);
                            if (videoFx != null) {
                                videoFx.setBooleanVal(INVERSE_SEGMENT, true);
                            }
                            /*新的时间线添加背景分割特效
                             * Add background segmentation effect to the new timeline
                             * */
                            final MeicamTimelineVideoFxClip newVideoFxClip = timeline.addTimelineVideoFxInClipList(TYPE_BUILD_IN, 0, timeline.getDuration(), SEGMENTATION);
                            if (newVideoFxClip != null) {
                                newVideoFxClip.setBooleanVal(INVERSE_SEGMENT, true);
                                newVideoFxClip.setBooleanVal(OUTPUT_MASK, true);
                            }
                            mMeicamTimeline.seekTimeline(mStreamingContext, mMeicamTimeline.getCurrentPosition(), STREAMING_ENGINE_SEEK_FLAG_BUDDY_HOST_VIDEO_FRAME);
                            if (isVideo) {
                                mAuxiliaryStreamingContext.setCompileCallback2((nvsTimeline, cancel) ->
                                        smartKeyerCompileComplete(videoClip, videoFx, compileVideoPath, timeline, cancel, false));
                                mAuxiliaryStreamingContext.setCompileCallback(new NvsStreamingContext.CompileCallback() {
                                    @Override
                                    public void onCompileProgress(NvsTimeline nvsTimeline, int i) {
                                        getView().onSmartKeyerProgress(i);
                                    }

                                    @Override
                                    public void onCompileFinished(NvsTimeline nvsTimeline) {
                                    }

                                    @Override
                                    public void onCompileFailed(NvsTimeline nvsTimeline) {
                                        smartKeyerCompileComplete(videoClip, videoFx, compileVideoPath, timeline, false, true);
                                    }
                                });
                                boolean b = timeline.compileTimeline(mAuxiliaryStreamingContext, 0, timeline.getDuration(),
                                        compileVideoPath, NvsStreamingContext.COMPILE_VIDEO_RESOLUTION_GRADE_CUSTOM, videoResolution.imageHeight, NvsStreamingContext.COMPILE_BITRATE_GRADE_HIGH,
                                        NvsStreamingContext.STREAMING_ENGINE_COMPILE_FLAG_BUDDY_HOST_VIDEO_FRAME | STREAMING_ENGINE_COMPILE_FLAG_IGNORE_TIMELINE_VIDEO_SIZE, null);
                                if (!b) {
                                    smartKeyerCompileComplete(videoClip, videoFx, compileVideoPath, timeline, false, true);
                                } else {
                                    mTempSmartKeyerClip = videoClip;
                                }
                            } else {
                                mAuxiliaryStreamingContext.setImageGrabberCallback((bitmap, l) -> {
                                    ThreadUtils.runOnUiThread(() -> getView().onSmartKeyerProgress(30));
                                    ThreadUtils.getIoPool().execute(() -> {
                                        ImageUtils.save(bitmap, compileVideoPath, Bitmap.CompressFormat.PNG);
                                        ThreadUtils.runOnUiThread(() ->
                                                smartKeyerCompileComplete(videoClip, videoFx, compileVideoPath, timeline, false,
                                                        bitmap == null));
                                    });
                                });
                                mTempSmartKeyerClip = videoClip;
                                timeline.grabImageFromTimelineAsync(mAuxiliaryStreamingContext, 100, null, 0);
                                getView().onSmartKeyerProgress(10);
                            }
                        }
                    }
                } else {
                    smartKeyerCompileComplete(videoClip, null, "", null, false, true);
                }
            }
        }
    }

    /**
     * 智能抠像视频导出完成
     * On smart keyer compile complete
     *
     * @param videoClip        The video clip to add a background split 要添加背景分割的视频片段
     * @param videoFx          The temp video fx 临时背景分割特效
     * @param compileVideoPath the compile file path 导出文件路径
     * @param compileTimeline  the compile timeline 导出抠像文件的时间线
     * @param cancel           true cancel compile 取消导出，false compile success没有取消，即成功
     * @param failed           true compile failed 导出失败，false not
     */
    private void smartKeyerCompileComplete(final MeicamVideoClip videoClip, MeicamVideoFx videoFx,
                                           String compileVideoPath, final MeicamTimeline compileTimeline,
                                           boolean cancel, boolean failed) {
        if (videoClip == null) {
            return;
        }
        mTempSmartKeyerClip = null;
        /*移除临时添加的背景分割特效
         * Remove the temporarily added background split effect
         * */
        if (videoFx != null) {
            VideoClipCommand.removeFx(videoClip, videoFx);
        }
        if (cancel) {
            /*取消
            cancel
            * */
            getView().onSmartKeyerComplete(false);
            FileUtils.delete(compileVideoPath);
            ToastUtils.showShort(R.string.cancel_smart_keyer);
        } else {
            String smartKeyerFilePath = PathUtils.renameSmartKeyerFileFromTemp(compileVideoPath);
            if (TextUtils.isEmpty(smartKeyerFilePath)) {
                failed = true;
            }
            if (failed) {
                /*失败
                 * failed
                 * */
                FileUtils.delete(compileVideoPath);
                FileUtils.delete(smartKeyerFilePath);
                getView().onSmartKeyerComplete(false);
            } else {
                /*成功
                 * Success
                 * */
                videoFx = VideoClipCommand.appendFx(videoClip, TYPE_RAW_BUILTIN, SUB_TYPE_ALPHA, SET_ALPHA);
                /*设置alpha特效*/
                if (videoFx != null) {
                    VideoFxCommand.setStringVal(videoFx, ALPHA_FILE, smartKeyerFilePath);
                    VideoFxCommand.setBooleanVal(videoFx, ALPHA_CLIP_TRIM_USED, true);
                }
                ToastUtils.showShort(R.string.smart_keyer_success);
                getView().onSmartKeyerComplete(true);
            }
        }
        /*销毁辅助Context
         * Destroy auxiliary context
         * */
        ThreadUtils.getMainHandler().post(() -> {
            /*这个时候需要使用Post方式才可以，不然会卡死主线程
             * At this time, you need to use Post mode, otherwise the main thread will be blocked。
             * */
            if (mAuxiliaryStreamingContext != null) {
                mAuxiliaryStreamingContext.stop();
                if (compileTimeline != null) {
                    compileTimeline.removeTimeline(mAuxiliaryStreamingContext);
                }
                mStreamingContext.destoryAuxiliaryStreamingContext(mAuxiliaryStreamingContext);
                mAuxiliaryStreamingContext = null;
            }
        });
        mMeicamTimeline.seekTimeline(mStreamingContext, mMeicamTimeline.getCurrentPosition(), 0);
    }

    /**
     * 是否是正在抠像的片段
     * Whether or not the image is being picked
     *
     * @param videoClip the video clip 视频片段
     */
    public boolean sameSmartKeyerClip(MeicamVideoClip videoClip) {
        return mTempSmartKeyerClip != null && mTempSmartKeyerClip.equals(videoClip);
    }

    /**
     * 取消智能抠像
     * Cancel smart keyer
     */
    public void cancelSmartKeyer() {
        if (mAuxiliaryStreamingContext != null) {
            mAuxiliaryStreamingContext.stop();
            mTempSmartKeyerClip = null;
        }
    }

    /**
     * 检测是否要更新智能抠像，用于切换倒放视频后更新抠像特效
     * Check if you want to update the smart keyer
     *
     * @param videoClip the video clip 视频片段
     */
    public void checkUpdateSmartKeyer(MeicamVideoClip videoClip) {
        if (videoClip != null) {
            String filePath = videoClip.getFilePath();
            if (!filePath.endsWith("m3u8")) {
                //更新alpha特效（即智能抠图）,删除再添加的原因是设置ALPHA_FILE没有更新成功。
                // The reason for updating alpha special effect (i.e. smart matting),
                // deleting and adding is to set ALPHA_ FILE was not updated successfully。
                MeicamVideoFx videoFx = videoClip.getVideoFx(SUB_TYPE_ALPHA, SET_ALPHA);
                if (videoFx != null) {
                    videoClip.removeVideoFx(videoFx);
                    String realPath = videoClip.getVideoReverse() ? videoClip.getReverseFilePath() : filePath;
                    String smartKeyerFilePath = PathUtils.getSmartKeyerFilePath(realPath, CLIP_VIDEO.equals(videoClip.getVideoType()), videoClip.getVideoReverse());
                    if (!TextUtils.isEmpty(smartKeyerFilePath)) {
                        if (new File(smartKeyerFilePath).exists()) {
                            videoClip.appendVideoFxFromFx(videoFx, false);
                            videoFx.setStringVal(ALPHA_FILE, smartKeyerFilePath);
                            videoFx.setBooleanVal(ALPHA_CLIP_TRIM_USED, true);
                        } else {
                            /*没有抠像文件，需要进行抠像
                             * There is no matting file, so matting is required
                             * */
                            startSmartKeyer(videoClip);
                        }
                    }
                }
            }
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (mAuxiliaryStreamingContext != null) {
            mAuxiliaryStreamingContext.setCompileCallback2(null);
            mAuxiliaryStreamingContext.setCompileCallback(null);
            mAuxiliaryStreamingContext.setImageGrabberCallback(null);
            if (mEditorEngine != null) {
                mEditorEngine.destroyAuxiliaryStreamingContext(mAuxiliaryStreamingContext);
            }
            mAuxiliaryStreamingContext = null;
        }
    }

    /**
     * 开始倒放片段
     * Start reverse clip
     *
     * @param videoClip the video clip 视频片段
     */
    public void startReverseClip(MeicamVideoClip videoClip, IConvertManager convertManager) {
        if (videoClip == null) {
            return;
        }
        String srcPath = videoClip.getFilePath();
        boolean isM3U8 = srcPath.endsWith("m3u8");
        String dstPath = PathUtils.getVideoConvertFilePath(isM3U8 ? "mp4" : PathUtils.getFileName(srcPath));
        ConvertFileManager.ConvertParam convertParam = new ConvertFileManager.ConvertParam();
        Hashtable<String, Object> hashtable = null;
        if (!isM3U8 && WhiteList.isCovert4KFileWhiteList(srcPath)) {
            hashtable = new Hashtable<>();
            int height = 720;
            SettingParameter parameter = GsonUtils.fromJson(PreferencesManager.get().getSettingParams(), SettingParameter.class);
            if (parameter != null && parameter.getCompileResolution() == NvsStreamingContext.COMPILE_VIDEO_RESOLUTION_GRADE_1080) {
                height = 1080;
            }
            hashtable.put(NvsMediaFileConvertor.CONVERTOR_CUSTOM_VIDEO_HEIGHT, height);
        }
        convertParam.appendParam(srcPath, dstPath, true, 0, hashtable);
        MeicamVideoFx videoFx = videoClip.getVideoFx(SUB_TYPE_ALPHA, SET_ALPHA);
        if (videoFx != null) {
            String clipFilePath = videoClip.getVideoReverse() ? videoClip.getReverseFilePath() : srcPath;
            String smartKeyerFilePath = PathUtils.getSmartKeyerFilePath(clipFilePath, CLIP_VIDEO.equals(videoClip.getVideoType()), true);
            if (!TextUtils.isEmpty(smartKeyerFilePath) && new File(smartKeyerFilePath).exists()) {
                videoClip.removeVideoFx(videoFx);
                /*目前直接更改ALPHA_FILE无效，所以删除再添加
                 * Currently, ALPHA is directly changed_ FILE is invalid, so delete and add
                 * */
                videoClip.appendVideoFxFromFx(videoFx, false);
                videoFx.setStringVal(ALPHA_FILE, smartKeyerFilePath);
                videoFx.setBooleanVal(ALPHA_CLIP_TRIM_USED, true);
            } else {
                /*倒放alpha文件，即智能抠图的视频文件
                 * Invert the alpha file, that is, the intelligent matting video file
                 * */
                String filePath = videoFx.getStringVal(ALPHA_FILE);
                if (!TextUtils.isEmpty(filePath) && new File(filePath).exists()) {
                    dstPath = PathUtils.getTempSmartKeyerFilePath(dstPath, true, true);
                    if (WhiteList.isCovert4KFileWhiteList(filePath)) {
                        hashtable = new Hashtable<>();
                        int height = 720;
                        SettingParameter parameter = GsonUtils.fromJson(PreferencesManager.get().getSettingParams(), SettingParameter.class);
                        if (parameter != null && parameter.getCompileResolution() == NvsStreamingContext.COMPILE_VIDEO_RESOLUTION_GRADE_1080) {
                            height = 1080;
                        }
                        hashtable.put(NvsMediaFileConvertor.CONVERTOR_CUSTOM_VIDEO_HEIGHT, height);
                    }
                    convertParam.appendParam(filePath, dstPath, true, 0, hashtable);
                }
            }
        }
        convertManager.convertFile(convertParam);
    }

    /**
     * 控制底层对象拖拽变化的方法。非音视频clip修改入出点，音视频clip删除重加。
     * Methods to control the drag and drop of underlying objects. Non-audio and video clip modify the point of entry and exit, audio and video clip delete and add
     *
     * @param type          对象类型。 clip type
     * @param oldUiClip     拖拽前的clip信息. clip infomation before drag
     * @param newInPoint    新的入点. new in point
     * @param newTrackIndex 新的轨道位置，对音视频clip比实际的轨道index少1。因为主轨道的位置是和目前分离的。
     *                      The new track position, clip for audio and video is 1 less than the actual track index.Because the position of the primary orbit is
     *                      separated from the present.
     * @param timeline      时间线，主要用于音视频clip的拖拽变化获取轨道使用。
     *                      Timeline, mainly used for audio and video clip drag change access to track use.
     * @return 返回新的底层对象 。return object
     */
    public Object dealWidthTrackDragEnd(String type, BaseUIClip oldUiClip, long newInPoint, int newTrackIndex, MeicamTimeline timeline) {
        if (oldUiClip == null) {
            LogUtils.e("onDragEnd: oldUiClip is null!");
            return null;
        }
        long oldInPoint = oldUiClip.getInPoint();
        long newOutPoint = newInPoint + oldUiClip.getOutPoint() - oldUiClip.getInPoint();
        if (CommonData.CLIP_CAPTION.equals(type) || CommonData.CLIP_COMPOUND_CAPTION.equals(type) || CommonData.CLIP_STICKER.equals(type)) {
            ClipInfo<?> clipInfo = mEditorEngine.getCaptionStickerData(oldUiClip.getTrackIndex(), oldUiClip.getInPoint());
            //处理字幕贴纸的移动 ,只是上层数据的改变
            // Handling the movement of caption stickers is only the change of upper data
            clipInfo = mEditorEngine.removeAndAddCaptionStickerClip(clipInfo, newTrackIndex);
            if (clipInfo != null) {
                ClipCommand.setZValue(clipInfo, newTrackIndex);
                ClipCommand.changeInAndOutPoint(clipInfo, newInPoint, newOutPoint);
            }
            mEditorEngine.sortCaptionStickerClip(newTrackIndex);
            //删除尾部的空轨道
            //Delete the empty track at the end
            mEditorEngine.removeEmptyStickerCaptionTrackInTheEnd();
        } else if (CommonData.CLIP_TIMELINE_FX.equals(type)) {
            MeicamTimelineVideoFxClip timelineVideoFxClip = mEditorEngine.getTimelineVideoFxClip(oldUiClip.getTrackIndex(), oldUiClip.getInPoint());
            timelineVideoFxClip = mEditorEngine.removeAndAddTimelineClip(timelineVideoFxClip, newTrackIndex);
            if (timelineVideoFxClip != null) {
                ClipCommand.changeInAndOutPoint(timelineVideoFxClip, newInPoint, newOutPoint);
                updateTimelineFxTargetInAndOutPoint(timelineVideoFxClip);
            }
            //删除尾部的空轨道
            //Delete the empty track at the end
            mEditorEngine.removeEmptyTimelineFxTrackInTheEnd();
            return timelineVideoFxClip;
        } else if (CommonData.CLIP_FILTER.equals(type) || CommonData.CLIP_ADJUST.equals(type)) {
            MeicamTimelineVideoFilterAndAdjustClip timelineVideoFxClip = mEditorEngine.getTimelineFilterAndAdjustClip(oldUiClip.getTrackIndex(), oldUiClip.getInPoint());
            timelineVideoFxClip = mEditorEngine.removeAndAddFilterAndAdjustClip(timelineVideoFxClip, newTrackIndex);
            if (timelineVideoFxClip != null) {
                ClipCommand.changeInAndOutPoint(timelineVideoFxClip, newInPoint, newOutPoint);
            }
            //删除尾部的空轨道
            //Delete the empty track at the end
            mEditorEngine.removeEmptyFilterAndAdjustTrackInTheEnd();
            return timelineVideoFxClip;
        } else if (CommonData.CLIP_VIDEO.equals(type) || CommonData.CLIP_IMAGE.equals(type)) {
            MeicamVideoClip videoClip = mEditorEngine.getVideoClip(oldUiClip.getTrackIndex() + 1, oldInPoint);
            if (videoClip != null) {
                MeicamVideoTrack videoTrack = mEditorEngine.getVideoTrack(oldUiClip.getTrackIndex() + 1);
                videoClip = VideoTrackCommand.removeVideoClipWidthSpace(videoTrack, videoClip.getIndex());
                if (videoClip != null) {
                    videoTrack = mEditorEngine.getVideoTrack(newTrackIndex + 1);
                    int insertIndex;
                    if (videoTrack == null) {
                        videoTrack = mEditorEngine.appendVideoTrack();
                        insertIndex = 0;
                    } else {
                        insertIndex = mEditorEngine.findSuitableEmbedIndex(videoTrack, newInPoint, newOutPoint);
                    }
                    if (insertIndex != -1) {
                        //如果速度大于1时，先计算一个合理的trimOut，然后再修改为真实的trimOut，防止后面的clip由于空间不足，被删掉
                        //If the speed is greater than 1, first calculate a reasonable trim out,
                        // and then modify it to a real trim out to prevent the subsequent clip
                        // from being deleted due to insufficient space.
                        long trimOut = videoClip.getTrimOut();
                        String curveSpeed = videoClip.getCurveSpeed();
                        long trimOutBySpeed = trimOut;
                        double speed = videoClip.getSpeed();
                        if (speed != 1) {
                            trimOutBySpeed = videoClip.getTrimIn() + newOutPoint - newInPoint;
                        }
                        VideoClipCommand.setCurveSpeed(videoClip, "", videoClip.getCurveSpeedName());
                        VideoClipCommand.setSpeed(videoClip, 1, videoClip.isKeepAudioPitch());
                        videoClip = VideoTrackCommand.addVideoClip(videoTrack, videoClip, newInPoint, videoClip.getTrimIn(), trimOutBySpeed);
                        if (videoClip != null && trimOutBySpeed != trimOut) {
                            if (!TextUtils.isEmpty(curveSpeed)) {
                                VideoClipCommand.setCurveSpeed(videoClip, curveSpeed, videoClip.getCurveSpeedName());
                            } else {
                                VideoClipCommand.setSpeed(videoClip, speed, videoClip.isKeepAudioPitch());
                            }
                            VideoClipCommand.setTrimOut(videoClip, trimOut, true);
                        }
                    }
                }
            }
            updateTimelineFxTargetVideoClipInPip(videoClip);
            return videoClip;
        } else if (CommonData.CLIP_AUDIO.equals(type)) {
            if (timeline == null) {
                LogUtils.e("timeline is null");
                return null;
            }
            int oldTrackIndex = oldUiClip.getTrackIndex();
            MeicamAudioTrack audioTrack = timeline.getAudioTrack(oldTrackIndex);
            if (audioTrack == null) {
                LogUtils.e(" video -> audioTrack is null! track index = " + (oldTrackIndex) + "  trackCount= " + timeline.videoTrackCount());
            } else {
                MeicamAudioClip newAudioInfo = null;
                int audioTrackCount = timeline.getAudioTrackCount();
                if (audioTrackCount > 0) {
                    for (int index = 0; index < audioTrackCount; index++) {
                        MeicamAudioTrack meicamAudioTrack = timeline.getAudioTrack(index);
                        int clipCount = meicamAudioTrack.getClipCount();
                        if (clipCount > 0) {
                            for (int count = clipCount - 1; count >= 0; count--) {
                                MeicamAudioClip meicamAudioClip = meicamAudioTrack.getAudioClip(count);
                                if (meicamAudioClip.getTrackIndex() == oldTrackIndex && meicamAudioClip.getInPoint() == oldInPoint) {
                                    newAudioInfo = meicamAudioClip;
                                    AudioTrackCommand.removeAudioClip(meicamAudioTrack, count, true);
                                    break;
                                }
                            }
                        }
                    }
                }

                MeicamAudioTrack newVideoTrack = timeline.getAudioTrack(newTrackIndex);
                if (newVideoTrack == null) {
                    //删除尾部的空轨道
                    //Delete the empty track at the end
                    mEditorEngine.removeEmptyAudioTrackInTheEnd();
                    LogUtils.e(" video -> newAudioClip is null! trackindex = " + (newTrackIndex) + "  trackCount= " + timeline.videoTrackCount());
                } else {
                    if (newAudioInfo != null) {

                        //如果速度大于1时，先计算一个合理的trimOut，然后再修改为真实的trimOut，防止后面的clip由于空间不足，被删掉
                        //If the speed is greater than 1, first calculate a reasonable trim out,
                        // and then modify it to a real trim out to prevent the subsequent clip
                        // from being deleted due to insufficient space.
                        long trimOut = newAudioInfo.getTrimOut();
                        long trimOutBySpeed = trimOut;
                        double speed = newAudioInfo.getSpeed();
                        if (speed > 1) {
                            trimOutBySpeed = newAudioInfo.getTrimIn() + newOutPoint - newInPoint;
                        }

                        AudioCommand.setSpeed(newAudioInfo, 1, true);
                        MeicamAudioClip meicamAudioClip = AudioTrackCommand.addAudioClip(newVideoTrack,
                                newAudioInfo, newInPoint, newAudioInfo.getTrimIn(), trimOutBySpeed);
                        if (trimOutBySpeed != trimOut && meicamAudioClip != null) {
                            AudioCommand.setSpeed(meicamAudioClip, speed, true);
                            AudioCommand.setTrimOut(meicamAudioClip, trimOut, true);
                        }
                        //删除尾部的空轨道
                        //Delete the empty track at the end
                        mEditorEngine.removeEmptyAudioTrackInTheEnd();
                        return meicamAudioClip;
                    }
                }
            }
            return null;
        }
        return null;
    }

    /**
     * 定格
     * Freeze frame clip.
     *
     * @param meicamVideoClip the  video clip 视频片段
     * @param trackIndex      the track index 轨道索引
     * @param isPip           the is pip 是否是画中画
     */
    public void freezeFrameClip(MeicamVideoClip meicamVideoClip, int trackIndex, boolean isPip) {
        if (meicamVideoClip == null) {
            return;
        }
        long timeStamp = getTimelineCurrentPosition();
        if (timeStamp <= meicamVideoClip.getInPoint() || timeStamp >= meicamVideoClip.getOutPoint() - 1) {
            ToastUtils.showShort(R.string.current_position_not_allow_freeze);
            return;
        }
        boolean isNeedSplit = timeStamp > meicamVideoClip.getInPoint() + CommonData.FREEZE_MIN_SHOW_LENGTH_DURATION
                && timeStamp < meicamVideoClip.getOutPoint() - CommonData.FREEZE_MIN_SHOW_LENGTH_DURATION;
        if (mEditorEngine.freezeFrameClip(meicamVideoClip, trackIndex, isNeedSplit) == EditorEngine.ReturnCode.CODE_OK) {
            int clipIndex = meicamVideoClip.getIndex();
            if (isNeedSplit) {
                getView().onFreezeFrameComplete(isPip, trackIndex, meicamVideoClip,
                        mEditorEngine.getVideoClip(trackIndex, clipIndex + 2),
                        mEditorEngine.getVideoClip(trackIndex, clipIndex + 1));
            } else {
                getView().onFreezeFrameComplete(isPip, trackIndex, meicamVideoClip, null,
                        mEditorEngine.getVideoClip(trackIndex, clipIndex + 1));
            }
        }

    }

    /**
     * 当视频片段时长改变的时候更新转场:主轨道 分割，定格，裁剪，变速,插入，删除，复制,交换
     * Update transition by video clip duration.
     *
     * @param clipIndex the clip index
     */
    public void updateTransitionByVideoClipDuration(int clipIndex) {
        if (mMeicamTimeline == null) {
            return;
        }
        MeicamVideoTrack videoTrack = mMeicamTimeline.getVideoTrack(MAIN_TRACK_INDEX);
        if (videoTrack == null) {
            return;
        }
        MeicamVideoClip trackClip = videoTrack.getVideoClip(clipIndex);
        if (trackClip == null) {
            return;
        }
        //裁剪到小于6帧时，转场删除
        // When clipping to less than 6 frames, the transition is deleted
        if (trackClip.getOutPoint() - trackClip.getInPoint() < mEditorEngine.getDurationByFrame(6)) {
            VideoTrackCommand.removeTransition(videoTrack, clipIndex);
            VideoTrackCommand.removeTransition(videoTrack, clipIndex - 1);
            getView().updateTransitionByVideoClipDuration(clipIndex);
        } else {
            //转场时间也要跟着变,一个片段移动影响两个转场的时长
            // The transition time should also change. The movement of one segment affects the duration of two transitions.
            MeicamTransition meicamTransition = videoTrack.getTransition(clipIndex);
            if (meicamTransition != null) {
                long maxTransitionDuration = mEditorEngine.getMaxTransitionDurationByVideoClip(clipIndex);
                if (maxTransitionDuration < meicamTransition.getDuration()) {
                    TransitionCommand.setParam(meicamTransition, TransitionCommand.PARAM_DURATION, maxTransitionDuration);
                }
            }
            MeicamTransition meicamTransitionBefore = videoTrack.getTransition(clipIndex - 1);
            if (meicamTransitionBefore != null) {
                long maxTransitionDurationBefore = mEditorEngine.getMaxTransitionDurationByVideoClip(clipIndex);
                if (maxTransitionDurationBefore < meicamTransitionBefore.getDuration()) {
                    TransitionCommand.setParam(meicamTransitionBefore, TransitionCommand.PARAM_DURATION, maxTransitionDurationBefore);
                }
            }
        }
    }

    /**
     * 删除视频片段
     * Delete video clip
     *
     * @param trackClip the track clip
     **/
    public boolean deleteVideoClip(ITrackClip trackClip) {
        if (trackClip == null) {
            return false;
        }
        MeicamVideoClip videoClip = getVideoClip(trackClip.getTrackIndex(), trackClip.getInPoint());
        if (videoClip == null) {
            videoClip = getMainTrackVideoClip();
        }
        return deleteVideoClip(videoClip);
    }

    /**
     * 删除视频片段
     * Delete video clip
     *
     * @param videoClip the video clip
     **/
    public boolean deleteVideoClip(MeicamVideoClip videoClip) {
        if (videoClip != null) {
            MeicamVideoTrack videoTrack = mMeicamTimeline.getVideoTrack(videoClip.getTrackIndex());
            if (videoTrack != null) {
                int index = videoClip.getIndex();
                if (videoTrack.getIndex() == MAIN_TRACK_INDEX) {
                    // 主轨道
                    int clipCount = videoTrack.getClipCount();
                    /*主轨道至少要保留一个有效片段（非补黑片段）*/
                    if (clipCount == 1) {
                        ToastUtils.showShort(R.string.toast_least_one_material);
                        return false;
                    } else if (clipCount == 2) {
                        MeicamVideoClip lastVideoClip = videoTrack.getVideoClip(1);
                        if (CLIP_HOLDER.equals(lastVideoClip.getVideoType())) {
                            ToastUtils.showShort(R.string.toast_least_one_material);
                            return false;
                        }
                    }
                    // 删除当前片段对应的转场
                    VideoTrackCommand.removeTransition(videoTrack, index);
                    getView().onDeleteTransition(index);
                    if (index > 0) {
                        MeicamVideoClip nextVideoClip = videoTrack.getVideoClip(index + 1);
                        if (nextVideoClip == null || CLIP_HOLDER.equals(nextVideoClip.getVideoType())) {
                            // 如果没有下一个片段或者是下一个是占位片段，则把上一个转场也删除。
                            VideoTrackCommand.removeTransition(videoTrack, index - 1);
                            getView().onDeleteTransition(index - 1);
                        }
                    }

                    long startTime = videoClip.getOutPoint();
                    long changeDuration = videoClip.getOutPoint() - videoClip.getInPoint();
                    VideoTrackCommand.removeVideoClip(videoTrack, videoClip.getIndex(), false);
                    updateTimelineFxTargetWhenVideoClipRemoved(videoClip);
                    getView().onDeleteVideoClip(videoClip);
                    mEditorEngine.timelineAddOrSubtract(startTime, -changeDuration);
                    updateTransitionByVideoClipDuration(videoClip.getIndex());
                } else {
                    // 画中画轨道
                    MeicamVideoClip removeClip = VideoTrackCommand.removeVideoClipWidthSpace(videoTrack, index);
                    if (removeClip == null) {
                        return false;
                    }
                    if (videoTrack.getClipCount() == 0) {
                        TimelineCommand.removeVideoTrack(mMeicamTimeline, videoTrack.getIndex());
                    }
                    updateTimelineFxTargetWhenVideoClipRemoved(videoClip);
                    getView().onDeleteVideoClip(videoClip);
                }
                mEditorEngine.seekTimeline(getTimelineCurrentPosition(), 0);
                return true;
            }
        }
        return false;
    }

    private RemoveTimelineInfo mRemoveTimelineInfo;

    /**
     * 删除时间线上的一些特效等
     * Remove some thine from timeline
     */
    public void removeSomeThingFromTimeline() {
        MeicamTimeline timeline = mEditorEngine.getCurrentTimeline();
        if (timeline != null) {
            mRemoveTimelineInfo = new RemoveTimelineInfo();
            int trackCount = timeline.videoTrackCount();
            for (int i = 0; i < trackCount; i++) {
                MeicamVideoTrack videoTrack = timeline.getVideoTrack(i);
                if (videoTrack != null) {
                    int clipCount = videoTrack.getClipCount();
                    for (int j = 0; j < clipCount; j++) {
                        MeicamVideoClip videoClip = videoTrack.getVideoClip(j);
                        if (videoClip != null) {
                            MeicamVideoFx videoFx = videoClip.getVideoFx(SUB_TYPE_ALPHA, SET_ALPHA);
                            if (videoFx != null) {
                                /*删除抠像特效
                                 * Delete matting effect
                                 * */
                                mRemoveTimelineInfo.addVideoFx(i, j, videoFx);
                                videoClip.removeVideoFx(videoFx);
                                final MeicamVideoFx newVideoFx = videoClip.appendVideoFx(TYPE_RAW_BUILTIN, SUB_TYPE_SEGMENT, SEGMENTATION, true);
                                if (newVideoFx != null) {
                                    newVideoFx.setBooleanVal(INVERSE_SEGMENT, true);
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 还原时间线上的一些特效等
     * Restore some thine from timeline
     */
    public void restoreSomeThingFromTimeline() {
        MeicamTimeline timeline = mEditorEngine.getCurrentTimeline();
        if (mRemoveTimelineInfo != null && timeline != null) {
            if (mRemoveTimelineInfo.videoFxList != null) {
                for (RemoveTimelineInfo.Info info : mRemoveTimelineInfo.videoFxList) {
                    MeicamVideoTrack videoTrack = timeline.getVideoTrack(info.trackIndex);
                    if (videoTrack != null) {
                        MeicamVideoClip videoClip = videoTrack.getVideoClip(info.clipIndex);
                        if (videoClip != null) {
                            /*还原导出模板前的删除的特效
                             * Restore the deleted special effect before exporting the template
                             * */
                            videoClip.appendVideoFxFromFx(info.videoFx, true);

                            MeicamVideoFx videoFx = videoClip.getVideoFx(SUB_TYPE_SEGMENT, SEGMENTATION);
                            if (videoFx != null) {
                                /*删除Segment特效
                                 * Delete segment effect
                                 * */
                                videoClip.removeVideoFx(videoFx);
                            }
                        }
                    }
                }
            }
            mRemoveTimelineInfo = null;
        }
    }

    /**
     * Remove mask key frame.
     * 删除蒙版关键帧
     *
     * @param videoClip the video clip
     */
    public void removeMaskKeyFrame(MeicamVideoClip videoClip) {
        MeicamVideoFx maskTargetFx = mEditorEngine.getMaskTargetFx(videoClip);
        if (maskTargetFx != null) {
            maskTargetFx.keyFrameProcessor().removeAllKeyFrame();
        }
    }

    /**
     * Gets opacity at time.
     * 获取在某一时刻的透明度
     *
     * @param videoClip the video clip
     * @param atTime    the at time
     * @return the opacity at time
     */
    public float getOpacityAtTime(MeicamVideoClip videoClip, long atTime) {
        if (videoClip == null) {
            return 0;
        }
        MeicamVideoFx propertyVideoFx = videoClip.findPropertyVideoFx();
        if (propertyVideoFx != null) {
            MeicamKeyFrame keyFrame = propertyVideoFx.keyFrameProcessor().getKeyFrame(atTime);
            List<MeicamFxParam<?>> paramsAtTime;
            if (keyFrame != null) {
                paramsAtTime = keyFrame.getParams();
            } else {
                paramsAtTime = propertyVideoFx.keyFrameProcessor().getNearbyKeyFrameParamsAtTime(NvsConstants.KEY_MASK_OPACITY, atTime);
            }
            if (!CommonUtils.isEmpty(paramsAtTime)) {
                for (MeicamFxParam<?> meicamFxParam : paramsAtTime) {
                    if (NvsConstants.KEY_MASK_OPACITY.equals(meicamFxParam.getKey())) {
                        Object value = meicamFxParam.getValue();
                        if (value instanceof Double) {
                            return ((Double) value).floatValue();
                        } else if (value instanceof Float) {
                            return (float) value;
                        }
                    }
                }
            }
        }
        return videoClip.getOpacity();
    }


    public float getValueAtTime(KeyFrameProcessor<?> processor, String key, long atTime) {
        if (processor == null) {
            return Float.MAX_VALUE;
        }
        MeicamKeyFrame keyFrame = processor.getKeyFrame(atTime);
        List<MeicamFxParam<?>> paramsAtTime;
        if (keyFrame != null) {
            paramsAtTime = keyFrame.getParams();
        } else {
            paramsAtTime = processor.getNearbyKeyFrameParamsAtTime(key, atTime);
        }
        if (!CommonUtils.isEmpty(paramsAtTime)) {
            for (MeicamFxParam<?> meicamFxParam : paramsAtTime) {
                if (key.equals(meicamFxParam.getKey())) {
                    Object value = meicamFxParam.getValue();
                    if (value instanceof Double) {
                        return ((Double) value).floatValue();
                    } else if (value instanceof Float) {
                        return (float) value;
                    }
                }
            }
        }
        return Float.MAX_VALUE;
    }

    private static class RemoveTimelineInfo {
        private List<Info> videoFxList;

        public void addVideoFx(int trackIndex, int clipIndex, MeicamVideoFx videoFx) {
            if (videoFxList == null) {
                videoFxList = new ArrayList<>();
            }
            Info info = new Info();
            info.trackIndex = trackIndex;
            info.clipIndex = clipIndex;
            info.videoFx = videoFx;
            videoFxList.add(info);
        }

        private static class Info {
            private int trackIndex;
            private int clipIndex;
            private MeicamVideoFx videoFx;
        }
    }

    /**
     * The type Time Space.
     * 时间空间描述类
     */
    public static class TimeSpace {
        public TimeSpace(long inPoint, long outPoint) {
            this.inPoint = inPoint;
            this.outPoint = outPoint;
        }

        long inPoint;
        long outPoint;
    }

}
