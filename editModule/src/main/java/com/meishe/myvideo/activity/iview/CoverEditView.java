package com.meishe.myvideo.activity.iview;

import android.graphics.Bitmap;

import com.meishe.base.model.IBaseView;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/12/14 17:48
 * @Description :The cover edit view
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public interface CoverEditView extends IBaseView {
    /**
     * 是否是活动的
     * Is active boolean.
     *
     * @return the boolean
     */
    boolean isActive();

    /**
     * 图片截图获取到了
     * Image grabbed arrived.
     *
     * @param bitmap the bitmap
     */
    void imageGrabbedArrived(Bitmap bitmap);

    /**
     * timeline发生变化通知
     * Notify timeline changed.
     */
    void notifyTimelineChanged();

    /**
     * 贴纸、字幕、画中画特技添加成功调用
     * On add sticker caption pic fx.
     *
     * @param captionClip the caption clip
     */
    void onAddStickerCaptionPicFx(Object captionClip);

    /**
     * 保存草稿完成
     * Save cover finish.
     */
    void saveCoverFinish();

    /**
     * On prepare.
     * 数据准备完毕
     *
     * @param isNewTimeline the is new timeline 是否是新建timeline
     * @param coverPoint    the cover point timeline的封面时间点
     */
    void onPrepare(boolean isNewTimeline, long coverPoint);
}
