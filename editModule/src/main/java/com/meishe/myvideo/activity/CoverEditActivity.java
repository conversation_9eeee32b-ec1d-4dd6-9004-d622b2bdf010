package com.meishe.myvideo.activity;

import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.PointF;
import android.graphics.Rect;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewTreeObserver;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;

import com.google.android.material.tabs.TabLayout;
import com.meicam.sdk.NvsStreamingContext;
import com.meicam.sdk.NvsTrackCompoundCaption;
import com.meicam.sdk.NvsVideoResolution;
import com.meishe.base.adapter.CommonFragmentAdapter;
import com.meishe.base.bean.MediaData;
import com.meishe.base.manager.AppManager;
import com.meishe.base.model.BaseMvpActivity;
import com.meishe.base.utils.BarUtils;
import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.LogUtils;
import com.meishe.base.utils.Utils;
import com.meishe.base.view.MViewPager;
import com.meishe.business.assets.view.MYMultiBottomView;
import com.meishe.engine.EditorEngine;
import com.meishe.engine.asset.bean.AssetInfo;
import com.meishe.engine.bean.ClipInfo;
import com.meishe.engine.bean.CommonData;
import com.meishe.engine.bean.MeicamCaptionClip;
import com.meishe.engine.bean.MeicamCompoundCaptionClip;
import com.meishe.engine.bean.MeicamStickerCaptionTrack;
import com.meishe.engine.bean.MeicamStickerClip;
import com.meishe.engine.bean.MeicamTimeline;
import com.meishe.engine.interf.IBaseInfo;
import com.meishe.myvideo.R;
import com.meishe.myvideo.activity.iview.CoverEditView;
import com.meishe.myvideo.activity.presenter.BottomViewHelper;
import com.meishe.myvideo.activity.presenter.BottomViewModel;
import com.meishe.myvideo.activity.presenter.CoverEditPresenter;
import com.meishe.myvideo.activity.presenter.MultiBottomHelper;
import com.meishe.myvideo.event.MessageEvent;
import com.meishe.myvideo.fragment.CaptionAnimationFragment;
import com.meishe.myvideo.fragment.CaptionBubbleFlowerFragment;
import com.meishe.myvideo.fragment.CoverEditImportFragment;
import com.meishe.myvideo.fragment.CoverEditSelectFrameFragment;
import com.meishe.myvideo.interfaces.BottomEventListener;
import com.meishe.myvideo.view.BottomContainer;
import com.meishe.myvideo.view.MYCompoundCaptionEditView;
import com.meishe.player.ImageRectCutActivity;
import com.meishe.player.fragment.VideoFragment;
import com.meishe.player.view.bean.TransformData;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.List;

import static com.meishe.base.constants.Constants.EDIT_MODE_CAPTION;
import static com.meishe.base.constants.Constants.EDIT_MODE_COMPOUND_CAPTION;
import static com.meishe.business.assets.view.MYMultiBottomView.TYPE_MENU_CAPTION;
import static com.meishe.logic.constant.PagerConstants.BUNDLE_DATA;
import static com.meishe.logic.constant.PagerConstants.MEDIA_FILTER;
import static com.meishe.logic.constant.PagerConstants.MEDIA_TYPE;
import static com.meishe.myvideo.event.MessageEvent.MESSAGE_TYPE_ADD_NORMAL_CAPTION;
import static com.meishe.myvideo.event.MessageEvent.MESSAGE_TYPE_REFRESH_CAPTION_RANGE;
import static com.meishe.player.fragment.presenter.VideoPresenter.EDIT_MODE_EDIT_COVER;

/**
 * Created by yangtailin on 2022-12-14.
 * 封面编辑页面
 * The cover edit activity
 *
 * <AUTHOR>
 * @CreateDate 2022-12-14.
 */
public class CoverEditActivity extends BaseMvpActivity<CoverEditPresenter> implements CoverEditView, View.OnClickListener {

    private static final int REQUEST_CODE_SELECT_FILE = 1;
    private static final int REQUEST_CODE_CUT_RECT_FILE = 2;

    private VideoFragment mVideoFragment;
    private MViewPager mViewPager;
    private List<Fragment> mFragmentList;
    private TabLayout mTabLayout;
    private MultiBottomHelper mMultiHelper;
    private int mKeyboardHeight;
    private View mDecorView;
    private ViewTreeObserver.OnGlobalLayoutListener mOnGlobalLayoutListener = null;
    private boolean mFirstGetKeyboardHeight = true;
    private boolean mIsVisibleForLast = false;
    private MYMultiBottomView mMultiBottomView;
    private BottomViewHelper mBottomViewHelper;
    private BottomContainer mBottomViewContainer;
    private ClipInfo<?> mCurrSelectedCaptionStickClip;
    private String mCurrSelectedClipId;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EventBus.getDefault().register(this);
    }


    @Override
    protected void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
    }

    @Override
    protected int bindLayout() {
        return R.layout.activity_cover_edit;
    }

    @Override
    protected void initData(Bundle savedInstanceState) {

    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == REQUEST_CODE_SELECT_FILE) {
            if (data != null) {
                MediaData mediaData = data.getParcelableExtra(BUNDLE_DATA);
                if (mediaData != null) {
                    Bundle bundle = new Bundle();
                    bundle.putString(ImageRectCutActivity.VIDEO_PATH, mediaData.getPath());
                    NvsVideoResolution videoResolution = EditorEngine.getInstance().getVideoResolution();
                    bundle.putFloat(ImageRectCutActivity.RAW_RATIO, videoResolution.imageWidth * 1F / videoResolution.imageHeight);
                    bundle.putParcelable(ImageRectCutActivity.TRANSFORM_DATA, new TransformData());
                    AppManager.getInstance().jumpActivityForResult(this, ImageRectCutActivity.class, bundle, REQUEST_CODE_CUT_RECT_FILE);
                }
            }
        } else if (requestCode == REQUEST_CODE_CUT_RECT_FILE) {
            if (data != null) {
                String path = data.getStringExtra(ImageRectCutActivity.VIDEO_PATH);
                TransformData transformData = data.getParcelableExtra(ImageRectCutActivity.INTENT_RECT_DATA);
                mTabLayout.selectTab(mTabLayout.getTabAt(1));
                mViewPager.setCurrentItem(1);
                mPresenter.createCover(path, transformData);
            }
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        mVideoFragment.connectTimelineWithLiveWindow();
        EditorEngine.getInstance().seekTimeline(0);
    }


    @Override
    protected void initView() {
        mTabLayout = findViewById(R.id.tab_layout);
        mViewPager = findViewById(R.id.view_pager);
        mMultiBottomView = findViewById(R.id.edit_add_sticker);
        mMultiHelper = new MultiBottomHelper(mMultiBottomView);
        mMultiBottomView.setFragmentManager(getSupportFragmentManager());
        mBottomViewHelper = new BottomViewHelper(new BottomViewModel());
        mBottomViewContainer = findViewById(R.id.fl_bottom_container);
        mBottomViewContainer.setFragmentManager(getSupportFragmentManager());
        mBottomViewHelper.attachView(mBottomViewContainer);
        initCaptionFragment();
        initVideoFragment();
        initListener();
    }

    private void initListener() {
        findViewById(R.id.iv_back_pressed).setOnClickListener(this);
        findViewById(R.id.tv_reset).setOnClickListener(this);
        findViewById(R.id.tv_save).setOnClickListener(this);
        addOnSoftKeyBoardVisibleListener();
        TabLayout.Tab tabAt = mTabLayout.getTabAt(1);
        if (tabAt != null) {
            tabAt.view.setOnTouchListener(new View.OnTouchListener() {
                @Override
                public boolean onTouch(View v, MotionEvent event) {
                    if (event.getAction() == MotionEvent.ACTION_DOWN) {
                        if (!mPresenter.haveFile()) {
                            Bundle bundle = new Bundle();
                            bundle.putInt(MEDIA_TYPE, MediaData.TYPE_PHOTO);
                            ArrayList<String> filter = new ArrayList<>();
                            filter.add(MediaData.TYPE_FILTER_GIF);
                            bundle.putStringArrayList(MEDIA_FILTER, filter);
                            AppManager.getInstance().jumpActivityForResult(CoverEditActivity.this,
                                    MaterialSingleSelectActivity.class, bundle, REQUEST_CODE_SELECT_FILE);
                            return true;
                        }
                    }
                    return false;
                }
            });
        }
        mTabLayout.addOnTabSelectedListener(new TabLayout.OnTabSelectedListener() {
            @Override
            public void onTabSelected(TabLayout.Tab tab) {
                mPresenter.swapTimeline(tab.getPosition());
                mCurrSelectedCaptionStickClip = findSelectedClip();
                if (mCurrSelectedCaptionStickClip instanceof MeicamCompoundCaptionClip) {
                    mVideoFragment.openFxEditMode(EDIT_MODE_COMPOUND_CAPTION, mCurrSelectedCaptionStickClip, true);
                } else if (mCurrSelectedCaptionStickClip instanceof MeicamCaptionClip) {
                    mVideoFragment.openFxEditMode(EDIT_MODE_CAPTION, mCurrSelectedCaptionStickClip, true);
                }
            }

            @Override
            public void onTabUnselected(TabLayout.Tab tab) {

            }

            @Override
            public void onTabReselected(TabLayout.Tab tab) {
            }
        });

        mVideoFragment.setFxEditListener(new VideoFragment.FxEditListener() {
            @Override
            public void onCheckSelected(PointF pointF, boolean isVideoClip) {
                long currentPosition = EditorEngine.getInstance().getCurrentTimeline().getCurrentPosition();

                List<ClipInfo<?>> clipInfos = mPresenter.findAllCaptionStickByTimelinePosition(currentPosition);
                if (!CommonUtils.isEmpty(clipInfos)) {
                    for (ClipInfo<?> clipInfo : clipInfos) {
                        //先判断是否是封面的字幕
                        //First judge whether it is the subtitle of the cover
                        Object attachment = clipInfo.getAttachment(CommonData.ATTACHMENT_KEY_IS_COVER_CAPTION);
                        if (attachment == null) {
                            continue;
                        }
                        List<PointF> boundingRectangleVertices = null;
                        if (clipInfo instanceof MeicamCaptionClip) {
                            boundingRectangleVertices = ((MeicamCaptionClip) clipInfo).getBoundingRectangleVertices();
                        } else if (clipInfo instanceof MeicamStickerClip) {
                            boundingRectangleVertices = ((MeicamStickerClip) clipInfo).getBoundingRectangleVertices();
                        } else if (clipInfo instanceof MeicamCompoundCaptionClip) {
                            boundingRectangleVertices = ((MeicamCompoundCaptionClip) clipInfo).getCompoundBoundingVertices(NvsTrackCompoundCaption.BOUNDING_TYPE_TEXT_FRAME);
                        }
                        boolean b = mVideoFragment.insideOperationBox(boundingRectangleVertices, (int) pointF.x, (int) pointF.y);
                        if (b) {
                            mCurrSelectedCaptionStickClip = clipInfo;
                            mCurrSelectedClipId = (String) attachment;
                            if (!mCurrSelectedCaptionStickClip.equals(mVideoFragment.getEditFx())) {
                                if (mCurrSelectedCaptionStickClip instanceof MeicamCaptionClip) {
                                    if (mMultiBottomView.isShow() && mMultiBottomView.getType() == TYPE_MENU_CAPTION) {
                                        mMultiHelper.updateCaptionView(mCurrSelectedCaptionStickClip);
                                        if (mMultiBottomView.getSelectedFragment() instanceof CaptionBubbleFlowerFragment) {
                                            CaptionBubbleFlowerFragment captionBubbleFlowerFragment = (CaptionBubbleFlowerFragment) mMultiBottomView.getSelectedFragment();
                                            captionBubbleFlowerFragment.setSelected();
                                        }
                                        if (mMultiBottomView.getSelectedFragment() instanceof CaptionAnimationFragment) {
                                            CaptionAnimationFragment captionAnimationFragment = (CaptionAnimationFragment) mMultiBottomView.getSelectedFragment();
                                            captionAnimationFragment.updateSelectedAnimation();
                                        }
                                    } else {
                                        if (mMultiBottomView.isShow()) {
                                            mMultiBottomView.hide();
                                        }
                                        mBottomViewContainer.dismissAll();
                                    }
                                    mVideoFragment.openFxEditMode(EDIT_MODE_CAPTION, mCurrSelectedCaptionStickClip, true);
                                } else if (mCurrSelectedCaptionStickClip instanceof MeicamCompoundCaptionClip) {
                                    if (mMultiBottomView.isShow()) {
                                        mMultiBottomView.hide();
                                    }
                                    mBottomViewContainer.dismissAll();
                                    mVideoFragment.openFxEditMode(EDIT_MODE_COMPOUND_CAPTION, mCurrSelectedCaptionStickClip, true);
                                }
                            }
                            break;
                        }
                    }
                }
            }

            @Override
            public void onDelete(int editMode) {
                mPresenter.deleteCaptionSicker(mCurrSelectedCaptionStickClip);
                mMultiBottomView.hide();
                View showView = mBottomViewContainer.getShowView();
                if (showView instanceof MYCompoundCaptionEditView) {
                    mBottomViewContainer.dismissView();
                }
            }

            @Override
            public void onTranslate(int editMode) {
                mPresenter.translateCaption(mCurrSelectedCaptionStickClip);
            }

            @Override
            public void onRotationAndScale(int editMode) {
                mPresenter.rotationAndScaleCaption(mCurrSelectedCaptionStickClip);
            }

            @Override
            public void onChanged(int quadrant, int editMode) {
                mPresenter.setTextAlignment(mCurrSelectedCaptionStickClip);
            }
        });

        mVideoFragment.setTouchEventListener(new VideoFragment.TouchEventListener() {
            @Override
            public void onLiveWindowClick(int editMode) {
                clickOutSide();
            }

            @Override
            public void onClickBoxOutside(int editMode) {
                clickOutSide();
            }

            @Override
            public void onClickBox(int index, int editMode) {
                if (editMode == EDIT_MODE_COMPOUND_CAPTION) {
                    if (!(mVideoFragment.getEditFx() instanceof MeicamCompoundCaptionClip)) {
                        LogUtils.e("the edit fx is not NvsTrackCompoundCaption");
                        return;
                    }
                    int captionCount = ((MeicamCompoundCaptionClip) mVideoFragment.getEditFx()).getCaptionItemCount();
                    if (index < 0 || index >= captionCount) {
                        LogUtils.e("the NvsTrackCompoundCaption is error! captionIndex: " + index + "  captionCount: " + captionCount);
                        return;
                    }
                    if (!(mCurrSelectedCaptionStickClip instanceof MeicamCompoundCaptionClip)) {
                        return;
                    }
                    final MeicamCompoundCaptionClip meicamCompoundCaptionClip = (MeicamCompoundCaptionClip) mCurrSelectedCaptionStickClip;
                    meicamCompoundCaptionClip.setItemSelectedIndex(index);
                    if (mBottomViewContainer.getShowView() instanceof MYCompoundCaptionEditView) {
                        //如果当前显示编辑view就直接更换内容，不用再消失显示了
                        //If the current display edit view directly change the content, no longer disappear display
                        ((MYCompoundCaptionEditView) mBottomViewContainer.getShowView()).setData(meicamCompoundCaptionClip, null, -1);
                        return;
                    }
                    mBottomViewHelper.showCompoundCaptionEdit(meicamCompoundCaptionClip, mKeyboardHeight,
                            new MYCompoundCaptionEditView.CompoundCaptionListener() {
                                @Override
                                public void onDismiss(boolean confirm) {
                                    if (confirm) {
                                        mVideoFragment.resetFxEditMode();
                                    }
                                    mPresenter.changeParam(mCurrSelectedCaptionStickClip);
                                    mBottomViewContainer.dismissView();
                                }
                            });
                }
            }

            @Override
            public void onDoubleClickBox(final int index, int editMode) {
                if (editMode == EDIT_MODE_CAPTION) {
                    if (mVideoFragment.operationBoxIsVisible()) {
                        showCaptionView();
                    }
                }
            }

            @Override
            public void onTouchBoxUp(PointF pointF, int editMode, boolean needSaveOperation) {
                super.onTouchBoxUp(pointF, editMode, needSaveOperation);
            }
        });

        mMultiBottomView.setMultiBottomEventListener(new MYMultiBottomView.MultiBottomEventListener() {
            @Override
            public void onEditTextChange(String text) {
                mPresenter.changeCaptionText(mVideoFragment.getEditFx(), text, null, false);
                mVideoFragment.openFxEditMode(EDIT_MODE_CAPTION, mCurrSelectedCaptionStickClip, true);
            }

            @Override
            public void onConfirm(int type) {
                mPresenter.onConfirm(mCurrSelectedCaptionStickClip);
                mPresenter.changeParam(mCurrSelectedCaptionStickClip);
                mVideoFragment.resetFxEditMode();
            }

            @Override
            public void onApplyToAll(Fragment fragment) {

            }

            @Override
            public void onErasure(int type) {

            }

            @Override
            public void onFragmentSelected(Fragment fragment, int type) {
                mMultiHelper.updateCaptionView(mCurrSelectedCaptionStickClip);
            }
        });
    }

    private ClipInfo<?> findSelectedClip() {
        if (TextUtils.isEmpty(mCurrSelectedClipId)) {
            return null;
        }
        MeicamTimeline currentTimeline = EditorEngine.getInstance().getCurrentTimeline();
        MeicamStickerCaptionTrack stickCaptionTrack = currentTimeline.findStickCaptionTrack(currentTimeline.getStickerCaptionTrackCount() - 1);
        if (stickCaptionTrack == null) {
            return null;
        }
        int clipCount = stickCaptionTrack.getClipCount();
        if (clipCount > 0) {
            for (int index = 0; index < clipCount; index++) {
                ClipInfo<?> clipInfo = stickCaptionTrack.getCaptionStickerClip(index);
                Object attachment = clipInfo.getAttachment(CommonData.ATTACHMENT_KEY_IS_COVER_CAPTION);
                if (mCurrSelectedClipId.equals(attachment)) {
                    return clipInfo;
                }
            }
        }
        return null;
    }

    private void clickOutSide() {
        mVideoFragment.updateTransformFx(null);
        mVideoFragment.hideOperationBox();
        mCurrSelectedCaptionStickClip = null;
        mCurrSelectedClipId = null;
        hideBottomView();
    }

    private void hideBottomView() {
        mBottomViewHelper.hideIfNeed();
        if (mMultiBottomView != null && mMultiBottomView.isShow()) {
            mMultiBottomView.hide();
        }
    }

    private void initCaptionFragment() {
        mFragmentList = new ArrayList<>();
        CoverEditSelectFrameFragment coverSelectFrameFragment = CoverEditSelectFrameFragment.create();
        coverSelectFrameFragment.setOnTimelineScrollListener(point -> {
            mVideoFragment.seekTimeline(point, NvsStreamingContext.STREAMING_ENGINE_SEEK_FLAG_SHOW_CAPTION_POSTER);
            mPresenter.setCoverPoint(point);
        });
        mFragmentList.add(coverSelectFrameFragment);
        mFragmentList.add(CoverEditImportFragment.create(REQUEST_CODE_SELECT_FILE));
        FragmentPagerAdapter adapter = new CommonFragmentAdapter(getSupportFragmentManager(), mFragmentList);
        mViewPager.setAdapter(adapter);
        mViewPager.setScroll(true);
        mTabLayout.setupWithViewPager(mViewPager);
        final String[] tabs = getResources().getStringArray(R.array.menu_tab_cover_edit);
        mTabLayout.removeAllTabs();
        for (String tab : tabs) {
            mTabLayout.addTab(mTabLayout.newTab().setText(tab));
        }
    }

    private void initVideoFragment() {
        FragmentManager fragmentManager = getSupportFragmentManager();
        mVideoFragment = VideoFragment.create(EDIT_MODE_EDIT_COVER);
        fragmentManager.beginTransaction().add(R.id.edit_preview_view, mVideoFragment).commitAllowingStateLoss();
        fragmentManager.beginTransaction().show(mVideoFragment);
        mVideoFragment.setLifeCycleListener(new VideoFragment.LifeCycleListener() {
            @Override
            public void onPresenterCreate() {
                mPresenter.prepare();
                mVideoFragment.resetFxEditMode();
            }
        });
    }

    /**
     * Edit normal caption.
     * 编辑普通字幕
     *
     * @param view the view
     */
    public void editNormalCaption(View view) {
        showCaptionView();
    }

    /**
     * Edit mould caption.
     * 编辑模块字幕
     *
     * @param view the view
     */
    public void editMouldCaption(View view) {
        mBottomViewHelper.showCaptionMouldView(new BottomEventListener() {
            @Override
            public void onDismiss(boolean confirm) {
                mVideoFragment.resetFxEditMode();
                mBottomViewContainer.dismissFragment();
            }

            @Override
            public void onItemClick(IBaseInfo baseInfo, boolean applyAll) {
                mPresenter.addCompoundCaption(baseInfo.getPackageId());
            }
        });
    }

    private void showCaptionView() {
        String text = "";
        if (mCurrSelectedCaptionStickClip instanceof MeicamCompoundCaptionClip) {
            mVideoFragment.resetFxEditMode();
            mCurrSelectedCaptionStickClip = null;
            mCurrSelectedClipId = null;
        }
        if (mVideoFragment.getEditFx() instanceof MeicamCaptionClip) {
            MeicamCaptionClip editFx = (MeicamCaptionClip) mVideoFragment.getEditFx();
            if (editFx != null) {
                text = (editFx).getText();
            }
        }
        mMultiHelper.showCaptionView(mCurrSelectedCaptionStickClip, mKeyboardHeight, text, 0,
                () -> {
                    if (mCurrSelectedCaptionStickClip == null) {
                        return;
                    }
                    mVideoFragment.openFxEditMode(EDIT_MODE_CAPTION, mCurrSelectedCaptionStickClip, true);
                });
    }

    private void addOnSoftKeyBoardVisibleListener() {
        if (mKeyboardHeight > 0) {
            return;
        }
        mDecorView = getWindow().getDecorView();
        mOnGlobalLayoutListener = () -> {
            if (!mFirstGetKeyboardHeight && mKeyboardHeight > 0) {
                return;
            }
            Rect rect = new Rect();
            mDecorView.getWindowVisibleDisplayFrame(rect);
            //计算出可见屏幕的高度 Calculate the height of the visible screen
            int displayHeight = rect.bottom - rect.top;
            //获得屏幕整体的高度 Get the overall height of the screen
            int height = mDecorView.getHeight();
            boolean visible = (double) displayHeight / height < 0.8;
            if (visible && !mIsVisibleForLast) {
                mKeyboardHeight = height - displayHeight - BarUtils.getStatusBarHeight() - BarUtils.getNavBarHeight(Utils.getApp());
                mFirstGetKeyboardHeight = false;
                if (mMultiBottomView.isShow() && mMultiBottomView.getType() == TYPE_MENU_CAPTION) {
                    mMultiBottomView.setKeyboardHeight(mKeyboardHeight);
                }
                if (mBottomViewContainer.getShowView() instanceof MYCompoundCaptionEditView) {
                    ((MYCompoundCaptionEditView) mBottomViewContainer.getShowView()).setKeyboardHeight(mKeyboardHeight);
                }
            }
            mIsVisibleForLast = visible;
        };
        mDecorView.getViewTreeObserver().addOnGlobalLayoutListener(mOnGlobalLayoutListener);
    }

    @Override
    public boolean isActive() {
        return !isFinishing() && this.equals(AppManager.getInstance().currentActivity());
    }

    @Override
    public void imageGrabbedArrived(Bitmap bitmap) {
        CoverEditImportFragment fragment = (CoverEditImportFragment) mFragmentList.get(1);
        fragment.setCover(bitmap);
    }

    @Override
    public void notifyTimelineChanged() {
        mVideoFragment.notifyTimelineChanged();
    }

    @Override
    public void onAddStickerCaptionPicFx(Object captionClip) {
        if (captionClip instanceof MeicamCaptionClip) {
            mCurrSelectedCaptionStickClip = (ClipInfo<?>) captionClip;
            mVideoFragment.openFxEditMode(EDIT_MODE_CAPTION, mCurrSelectedCaptionStickClip, true);
        } else if (captionClip instanceof MeicamCompoundCaptionClip) {
            mCurrSelectedCaptionStickClip = (ClipInfo<?>) captionClip;
            mVideoFragment.openFxEditMode(EDIT_MODE_COMPOUND_CAPTION, mCurrSelectedCaptionStickClip, true);
        }
        mMultiHelper.updateCaptionView(mCurrSelectedCaptionStickClip);
    }

    @Override
    public void saveCoverFinish() {
        mPresenter.resetTimeline();
        setResult(RESULT_OK);
        AppManager.getInstance().finishActivity();
    }

    @Override
    public void onPrepare(boolean isNewTimeline, long coverPoint) {
        mViewPager.setCurrentItem(isNewTimeline ?  1: 0);
        if (!isNewTimeline) {
            delayToDealOnUiThread(new Runnable() {
                @Override
                public void run() {
                    CoverEditSelectFrameFragment fragment = (CoverEditSelectFrameFragment) mFragmentList.get(0);
                    fragment.smoothScrollTo(coverPoint);
                }
            });
        }
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
        mPresenter.resetTimeline();
        setResult(RESULT_CANCELED);
        AppManager.getInstance().finishActivity();
    }

    /**
     * On message event.
     * 消息事件
     *
     * @param event the event
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onMessageEvent(MessageEvent event) {
        if (isFinishing() || !this.equals(AppManager.getInstance().currentActivity())) {
            return;
        }
        if (event.getEventType() == MESSAGE_TYPE_REFRESH_CAPTION_RANGE) {
            //刷新字幕框，范围
            //Refresh caption range
            mVideoFragment.openFxEditMode(EDIT_MODE_CAPTION, mCurrSelectedCaptionStickClip, true);
        } else if (event.getEventType() == MESSAGE_TYPE_ADD_NORMAL_CAPTION) {
            //新增普通字幕模板
            //Add common caption
            IBaseInfo info = event.getBaseInfo();
            if (info instanceof AssetInfo) {
                AssetInfo assetInfo = (AssetInfo) info;
                mPresenter.changeCaptionText(null, assetInfo.getName(), assetInfo.getPackageId(), true);
            }
        }
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.iv_back_pressed) {
            onBackPressed();
        } else if (id == R.id.tv_reset) {
            mPresenter.reset();
            int tabPosition = mTabLayout.getSelectedTabPosition();
            if (tabPosition != 0) {
                mTabLayout.selectTab(mTabLayout.getTabAt(0));
            } else {
                //重置如果在本页面，需要刷新
                //Reset If you are on this page, you need to refresh
                EditorEngine.getInstance().seekTimeline(0, 0);
                CoverEditSelectFrameFragment fragment = (CoverEditSelectFrameFragment) mFragmentList.get(0);
                fragment.smoothScrollTo(0);
            }
            mVideoFragment.resetFxEditMode();
        } else if (id == R.id.tv_save) {
            mPresenter.saveCover();
        }
    }
}