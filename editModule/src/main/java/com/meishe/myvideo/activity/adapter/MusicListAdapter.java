package com.meishe.myvideo.activity.adapter;

import android.text.TextUtils;
import android.view.ViewGroup;
import android.widget.ImageButton;

import androidx.annotation.NonNull;

import com.meishe.myvideo.R;
import com.meishe.myvideo.bean.MusicInfo;
import com.meishe.third.adpater.BaseQuickAdapter;
import com.meishe.third.adpater.BaseViewHolder;

/**
 * 音乐列表适配器
 * Music List Adapter
 */
public class MusicListAdapter extends BaseQuickAdapter<MusicInfo, BaseViewHolder> {
    private int mSelectedPosition = -1;

    public MusicListAdapter() {
        super(R.layout.item_music);
    }

    /**
     * 选中某一项，再次选中则取消。
     * Selected item ,If selected again, it will be cancelled
     */
    public void selected(int position) {
        if (position >= 0 && position < getData().size()) {
            if (position == mSelectedPosition) {
                //再次选中则取消
                //Check again to cancel
                mSelectedPosition = -1;
                notifyItemChanged(position);
                return;
            }
            if (mSelectedPosition >= 0) {
                notifyItemChanged(mSelectedPosition);
            }
            mSelectedPosition = position;
            notifyItemChanged(position);
        } else {
            //取消选中
            //Uncheck
            if (mSelectedPosition >= 0) {
                notifyItemChanged(mSelectedPosition);
            }
            mSelectedPosition = -1;
        }
    }

    /**
     * 获取选中位置
     * get Selected Position
     */
    public int getSelectedPosition() {
        return mSelectedPosition;
    }

    @NonNull
    @Override
    public BaseViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        BaseViewHolder holder = super.onCreateViewHolder(parent, viewType);
        holder.addOnClickListener(R.id.ibt_play);
        return holder;
    }

    @Override
    protected void convert(@NonNull BaseViewHolder helper, MusicInfo item) {
        if (TextUtils.isEmpty(item.getTitle())) {
            helper.setText(R.id.tv_music_name,"");
        }else{
            helper.setText(R.id.tv_music_name,item.getTitle());
        }
        if (TextUtils.isEmpty(item.getArtist())) {
            helper.setText(R.id.tv_music_author, "");
        }else{
            helper.setText(R.id.tv_music_author, item.getArtist());
        }
        ImageButton playView = helper.getView(R.id.ibt_play);
        if (helper.getAdapterPosition() == mSelectedPosition) {
            playView.setBackgroundResource(R.mipmap.ic_music_pause);
        } else {
            playView.setBackgroundResource(R.mipmap.ic_music_play);
        }
    }
}
