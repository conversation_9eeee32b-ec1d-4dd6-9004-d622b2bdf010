package com.meishe.myvideo.activity;

import android.app.Activity;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.RadioButton;
import android.widget.ScrollView;
import android.widget.TextView;

import androidx.appcompat.widget.SwitchCompat;

import com.meicam.sdk.NvsStreamingContext;
import com.meishe.base.manager.AppManager;
import com.meishe.base.model.BaseMvpActivity;
import com.meishe.base.utils.LogUtils;
import com.meishe.base.utils.Utils;
import com.meishe.base.view.CustomCompileParamView;
import com.meishe.base.view.bean.CompileParamData;
import com.meishe.engine.constant.NvsConstants;
import com.meishe.myvideo.R;
import com.meishe.myvideo.activity.iview.CompileView;
import com.meishe.myvideo.activity.presenter.CompilePresenter;
import com.meishe.myvideo.view.editview.CompileProgress;

import static com.meishe.logic.constant.PagerConstants.BUNDLE_SAVE_DRAFT;


/**
 * 导出视频页面
 * Compile Video Page
 */
public class CompileActivity extends BaseMvpActivity<CompilePresenter> implements CompileView, View.OnClickListener {
    private ImageView mIvBack;
    private ImageView mImageCover;
    private Button mTvCompile;
    private CustomCompileParamView mCustomResolution;
    private CustomCompileParamView mCustomFrameRate;
    private View mLlCompileParent;

    private View mRlCompileProgressParent;
    private CompileProgress mEditCompileProgress;
    private TextView mTvCompileProgress;
    private Button mBtCompileCancel;
    private TextView mTvFileSize;
    private ImageView mIvHomeKey;
    private TextView mTvCompileResult;
    private ScrollView mScrollView;
    private SwitchCompat mSwitchViewToHevc;
    private RadioButton mRadioButtonConfigNone, mRadioButtonConfigHlg, mRadioButtonConfigSt2084, mRadioButtonConfigHdr10plus;
    private String mExportConfig = NvsConstants.HDR_EXPORT_CONFIG_NONE;
    private View mConfigLayoutNone, mConfigLayoutSt2084, mConfigLayoutHlg, mConfigLayoutHdr10Plus, mConfigHintView;
    /**
     * 是否退出和取消compile标记
     * Whether to exit and cancel the compiling flag
     */
    private boolean isBackAndCancel;

    @Override
    protected int bindLayout() {
        return R.layout.activity_compile;
    }

    @Override
    protected void initData(Bundle savedInstanceState) {
        isBackAndCancel = false;
        mPresenter.initTimeline();
    }

    @Override
    protected void initView() {
        mIvBack = findViewById(R.id.iv_compile_back);
        mScrollView = findViewById(R.id.scrollView);
        mLlCompileParent = findViewById(R.id.ll_params);
        mImageCover = findViewById(R.id.iv_cover);
        mTvCompile = findViewById(R.id.tv_compile);
        mCustomResolution = findViewById(R.id.custom_resolution);
        mCustomFrameRate = findViewById(R.id.custom_frame_rate);
        mRlCompileProgressParent = findViewById(R.id.fl_compile_progress);
        mEditCompileProgress = findViewById(R.id.edit_compile_progress);
        mTvCompileProgress = findViewById(R.id.tv_compile_progress);
        mBtCompileCancel = findViewById(R.id.bt_compile_cancel);
        mTvCompileResult = findViewById(R.id.tv_result);
        mIvHomeKey = findViewById(R.id.iv_compile_home);
        mTvFileSize = findViewById(R.id.tv_size);
        mSwitchViewToHevc = findViewById(R.id.switch_to_HEVC);
        mSwitchViewToHevc.setChecked(true);
        mRadioButtonConfigNone = findViewById(R.id.rb_export_config_none);
        mRadioButtonConfigNone.setChecked(true);
        mRadioButtonConfigSt2084 = findViewById(R.id.rb_export_config_st2084);
        mRadioButtonConfigHlg = findViewById(R.id.rb_export_config_hlg);
        mRadioButtonConfigHdr10plus = findViewById(R.id.rb_export_config_hdr10plus);
        mConfigLayoutNone = findViewById(R.id.ll_export_config_none);
        mConfigLayoutSt2084 = findViewById(R.id.ll_export_config_st2084);
        mConfigLayoutHlg = findViewById(R.id.ll_export_config_hlg);
        mConfigLayoutHdr10Plus = findViewById(R.id.ll_export_config_hdr10plus);
        mConfigHintView = findViewById(R.id.tv_export_config);
        int engineHDRCaps = NvsStreamingContext.getInstance().getEngineHDRCaps();
        if ((engineHDRCaps & NvsStreamingContext.HDR_CAPABILITY_FLAG_SUPPORTED_BY_EXPORTER) == 0) {
            goneView(findViewById(R.id.tv_export_switch_to_HEVC), findViewById(R.id.tv_hdr_export_setting),
                    mSwitchViewToHevc, mConfigLayoutNone, mConfigLayoutSt2084, mConfigLayoutHlg,
                    mConfigLayoutHdr10Plus, mConfigHintView);
        }
        initListener();
    }

    /**
     * Init listener.
     * 初始化监听
     */
    private void initListener() {
        mIvHomeKey.setOnClickListener(this);
        mTvCompile.setOnClickListener(this);
        mIvBack.setOnClickListener(this);
        mBtCompileCancel.setOnClickListener(this);
        mCustomResolution.setOnFunctionSelectedListener(new CustomCompileParamView.OnFunctionSelectedListener() {
            @Override
            public void onSelected(CompileParamData itemData) {
                setFileSize(itemData, true);
            }

            @Override
            public void onTouched() {
                mScrollView.requestDisallowInterceptTouchEvent(true);
            }

            @Override
            public void onRelease() {
                mScrollView.requestDisallowInterceptTouchEvent(false);
            }
        });
        mCustomFrameRate.setOnFunctionSelectedListener(new CustomCompileParamView.OnFunctionSelectedListener() {
            @Override
            public void onSelected(CompileParamData itemData) {
                setFileSize(itemData, false);
            }

            @Override
            public void onTouched() {
                mScrollView.requestDisallowInterceptTouchEvent(true);
            }

            @Override
            public void onRelease() {
                mScrollView.requestDisallowInterceptTouchEvent(false);
            }

        });
        mSwitchViewToHevc.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if (isChecked) {
                showView(mConfigLayoutNone, mConfigLayoutSt2084, mConfigLayoutHlg, mConfigLayoutHdr10Plus, mConfigHintView);
            } else {
                hideView(mConfigLayoutNone, mConfigLayoutSt2084, mConfigLayoutHlg, mConfigLayoutHdr10Plus, mConfigHintView);
            }
        });
        mRadioButtonConfigNone.setOnClickListener(this);
        mRadioButtonConfigSt2084.setOnClickListener(this);
        mRadioButtonConfigHlg.setOnClickListener(this);
        mRadioButtonConfigHdr10plus.setOnClickListener(this);
    }

    private void unCheck(RadioButton button) {
        button.setChecked(false);
    }

    private void showView(View... views) {
        for (View view : views) {
            view.setVisibility(View.VISIBLE);
        }
    }

    private void hideView(View... views) {
        for (View view : views) {
            view.setVisibility(View.INVISIBLE);
        }
    }

    private void goneView(View... views) {
        for (View view : views) {
            view.setVisibility(View.GONE);
        }
    }

    @Override
    protected void requestData() {
        mImageCover.setImageBitmap(mPresenter.grabImageFromTimeline());
        setFileSize(null, false);
        mCustomResolution.postDelayed(() -> {
            mCustomResolution.setSelectedData(mPresenter.getCompileParams(getResources().
                    getStringArray(R.array.custom_resolution), getResources().getString(R.string.int720)));
            mCustomFrameRate.setSelectedData(mPresenter.getCompileParams(getResources().
                    getStringArray(R.array.custom_frame_rate), getResources().getString(R.string.frame_rate_30)));
        }, 100);
    }

    private void setFileSize(CompileParamData compileParam, boolean isResolution) {
        mTvFileSize.setText(mPresenter.calculateFileSize(compileParam, isResolution));
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.iv_compile_back) {
            isBackAndCancel = true;
            cancelCompile();
            finish();
        } else if (id == R.id.tv_compile) {
            if (mPresenter != null) {
                int engineHDRCaps = NvsStreamingContext.getInstance().getEngineHDRCaps();
                boolean supportCompileHdr = (engineHDRCaps & NvsStreamingContext.HDR_CAPABILITY_FLAG_SUPPORTED_BY_EXPORTER)
                        == NvsStreamingContext.HDR_CAPABILITY_FLAG_SUPPORTED_BY_EXPORTER;
                if (supportCompileHdr) {
                    mPresenter.putParam(NvsStreamingContext.COMPILE_VIDEO_HDR_COLOR_TRANSFER, mExportConfig);
                }
                mPresenter.compileVideo();
            }
        } else if (id == R.id.iv_compile_home) {
            if (Utils.isFastClick()) {
                return;
            }
            Bundle bundle = new Bundle();
            bundle.putBoolean(BUNDLE_SAVE_DRAFT, true);
            try {
                Class<? extends Activity> aClass =
                        (Class<? extends Activity>) Class.forName("com.meishe.myvideo.activity.MainActivity");
                AppManager.getInstance().jumpActivity(this, aClass, bundle);
            } catch (ClassNotFoundException e) {
                LogUtils.e(e);
            }
            finish();
        } else if (id == R.id.bt_compile_cancel) {
            cancelCompile();
        } else if (id == R.id.rb_export_config_none) {
            mExportConfig = NvsConstants.HDR_EXPORT_CONFIG_NONE;
            unCheck(mRadioButtonConfigHlg);
            unCheck(mRadioButtonConfigSt2084);
            unCheck(mRadioButtonConfigHdr10plus);
        } else if (id == R.id.rb_export_config_st2084) {
            mExportConfig = NvsConstants.HDR_EXPORT_CONFIG_2084;
            unCheck(mRadioButtonConfigHlg);
            unCheck(mRadioButtonConfigNone);
            unCheck(mRadioButtonConfigHdr10plus);
        } else if (id == R.id.rb_export_config_hlg) {
            mExportConfig = NvsConstants.HDR_EXPORT_CONFIG_HLG;
            unCheck(mRadioButtonConfigNone);
            unCheck(mRadioButtonConfigSt2084);
            unCheck(mRadioButtonConfigHdr10plus);
        } else if (id == R.id.rb_export_config_hdr10plus) {
            mExportConfig = NvsConstants.HDR_EXPORT_CONFIG_HDR10PLUS;
            unCheck(mRadioButtonConfigHlg);
            unCheck(mRadioButtonConfigSt2084);
            unCheck(mRadioButtonConfigNone);
        }
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
        isBackAndCancel = true;
        cancelCompile();
    }

    /**
     * 取消导出视频
     */
    private void cancelCompile() {
        if (mPresenter != null) {
            mPresenter.stopCompileVideo();
        }
    }

    @Override
    public void onCompileStart() {
        mRlCompileProgressParent.setFocusable(true);
        mRlCompileProgressParent.requestFocus();
        mRlCompileProgressParent.setVisibility(View.VISIBLE);
        mLlCompileParent.setVisibility(View.GONE);
        mTvCompile.setVisibility(View.GONE);
        mTvFileSize.setVisibility(View.GONE);
    }

    @Override
    public void onCompileProgress(int progress) {
        mEditCompileProgress.setProgress(progress);
        String text = progress + "%";
        mTvCompileProgress.setText(text);
    }

    @Override
    public void onCompileEnd(boolean complete) {
        if (complete) {
            /*
             * 导出视频完成
             * Export video completion
             * */
            mTvCompileResult.setVisibility(View.VISIBLE);
            mIvHomeKey.setVisibility(View.VISIBLE);
            mRlCompileProgressParent.setVisibility(View.GONE);
        } else {
            if (!isFinishing() && !isBackAndCancel) {
                /*
                 * 导出视频未完成
                 * The exported video is not complete
                 * */
                mRlCompileProgressParent.setVisibility(View.GONE);
                mRlCompileProgressParent.setFocusable(false);
                mEditCompileProgress.setProgress(0);
                mTvCompileProgress.setText("0%");

                mLlCompileParent.setVisibility(View.VISIBLE);
                mTvCompile.setVisibility(View.VISIBLE);
                mTvFileSize.setVisibility(View.VISIBLE);
            }
        }
    }

    @Override
    public boolean isActive() {
        Activity activity = AppManager.getInstance().currentActivity();
        return !isFinishing() && this.equals(activity);
    }
}