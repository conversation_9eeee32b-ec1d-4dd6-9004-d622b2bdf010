package com.meishe.myvideo.activity;

import static com.meishe.logic.constant.PagerConstants.BUNDLE_CLIP;
import static com.meishe.logic.constant.PagerConstants.BUNDLE_CLIP_LIST;
import static com.meishe.logic.constant.PagerConstants.BUNDLE_DATA;
import static com.meishe.logic.constant.PagerConstants.MEDIA_DATA;
import static com.meishe.logic.constant.PagerConstants.MEDIA_MAX_NUM;
import static com.meishe.logic.constant.PagerConstants.MEDIA_REQUEST_CODE_PREVIEW;
import static com.meishe.logic.constant.PagerConstants.MEDIA_TAG;
import static com.meishe.logic.constant.PagerConstants.NEXT_PAGE_ACTION;
import static com.meishe.logic.constant.PagerConstants.SELECTED_TYPE;
import static com.meishe.myvideo.activity.DraftEditActivity.REQUEST_CLIP_REPLACE;

import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewpager.widget.ViewPager;

import com.meishe.base.adapter.CommonFragmentAdapter;
import com.meishe.base.bean.MediaData;
import com.meishe.base.bean.MediaTag;
import com.meishe.base.manager.AppManager;
import com.meishe.base.manager.LinearLayoutManagerWrapper;
import com.meishe.base.model.BaseActivity;
import com.meishe.base.utils.LogUtils;
import com.meishe.base.utils.ToastUtils;
import com.meishe.base.utils.Utils;
import com.meishe.base.view.decoration.ItemDecoration;
import com.meishe.myvideo.R;
import com.meishe.myvideo.adapter.MediaSelectedFillAdapter;
import com.meishe.myvideo.bean.MeidaClip;
import com.meishe.myvideo.fragment.MediaFragment;
import com.meishe.third.adpater.BaseQuickAdapter;
import com.meishe.third.tablayout.SlidingTabLayout;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;


/**
 * All rights Reserved, Designed By www.meishesdk.com
 *
 * @Author: ChuChenGuang
 * @CreateDate: 2022/10/31 14:18
 * @Description: clip 素材选择
 * @Copyright: www.meishesdk.com Inc. All rights reserved.
 */
public class MaterialSelectFillActivity extends BaseActivity {
    public final static int TYPE_DEFAULT = 0;
    /**
     * 选中一个一个就跑
     * Pick one and run
     */
    public final static int TYPE_ONE_FINISH = 1;
    private ImageView mIvBack;
    private TextView mTvNext;
    private RecyclerView mRvMediaList;
    private MediaSelectedFillAdapter mSelectedAdapter;
    private final List<Fragment> mFragmentList = new ArrayList<>(3);
    private List<String> mTabTitleList = new ArrayList<>(3);
    private int mType;
    private List<MeidaClip> mClipList = null;
    private String mNextPageAction;

    @Override
    protected int bindLayout() {
        return R.layout.activity_fill_material;
    }

    @Override
    protected void initData(Bundle savedInstanceState) {
        Intent intent = getIntent();
        if (intent != null) {
            Bundle bundle = intent.getExtras();
            if (bundle != null) {
                //类型 type
                mType = bundle.getInt(SELECTED_TYPE, TYPE_DEFAULT);
                mNextPageAction = bundle.getString(NEXT_PAGE_ACTION, "");
                if (mType == TYPE_DEFAULT) {
                    //默认类型需要模板路径 The default type requires a template path
                    mClipList = intent.getParcelableArrayListExtra(BUNDLE_CLIP_LIST);
                } else if (mType == TYPE_ONE_FINISH) {
                    //单选素材，需要片段 Radio selection material, need snippets
                    MeidaClip templateClip = bundle.getParcelable(BUNDLE_CLIP);
                    mClipList = new ArrayList<>(1);
                    mClipList.add(templateClip);
                }
            }
        }
        mTabTitleList.clear();
        mTabTitleList = Arrays.asList(getResources().getStringArray(R.array.select_media));
        mFragmentList.clear();
        long clipDuration = mClipList.get(0).getDuration();
        mFragmentList.clear();
        mFragmentList.add(MediaFragment.create(MediaData.TYPE_ALL, clipDuration, mMediaListener));
        mFragmentList.add(MediaFragment.create(MediaData.TYPE_VIDEO, clipDuration, mMediaListener));
        mFragmentList.add(MediaFragment.create(MediaData.TYPE_PHOTO, clipDuration, mMediaListener));
        mSelectedAdapter = new MediaSelectedFillAdapter();
    }

    @Override
    protected void initView() {
        mIvBack = findViewById(R.id.iv_back);
        SlidingTabLayout tabLayout = findViewById(R.id.tl_select_media);
        ViewPager viewPager = findViewById(R.id.vp_select_media);
        TextView tvMediaNum = findViewById(R.id.tv_selected_num);
        mTvNext = findViewById(R.id.tv_next);
        mRvMediaList = findViewById(R.id.rv_selected_list);
        viewPager.setOffscreenPageLimit(3);
        viewPager.setAdapter(new CommonFragmentAdapter(getSupportFragmentManager(), mFragmentList));
        tabLayout.setViewPager(viewPager, mTabTitleList);

        if (mType == TYPE_DEFAULT || mType == TYPE_ONE_FINISH) {
            // 选则列表 Choose the list
            mRvMediaList.setLayoutManager(new LinearLayoutManagerWrapper(this, RecyclerView.HORIZONTAL, false));
            mRvMediaList.addItemDecoration(new ItemDecoration(10, 10));
            mRvMediaList.setAdapter(mSelectedAdapter);
            mTvNext.setEnabled(false);
            if (mClipList == null) {
                LogUtils.e("mClipList == null");
                return;
            }
            if (mClipList.size() == 0) {
                return;
            }
            mSelectedAdapter.setNewData(mClipList);
            tvMediaNum.setText(String.format(getString(R.string.selected_material_num_hint), mClipList.size()));
        }
        initListener();
    }

    private void initListener() {
        mIvBack.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onBackPressed();
            }
        });
        mTvNext.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                final List<MeidaClip> list = mSelectedAdapter.getData();
                goNext(list);
            }
        });
        mSelectedAdapter.setOnItemChildClickListener(new BaseQuickAdapter.OnItemChildClickListener() {
            @Override
            public void onItemChildClick(
                    @NonNull BaseQuickAdapter adapter, @NonNull View view, int position) {
                if (view.getVisibility() != View.VISIBLE) {
                    return;
                }
                MeidaClip item = mSelectedAdapter.getItem(position);
                if (item != null) {
                    String filePath = item.getFilePath();
                    MeidaClip copy = item.copy();
                    copy.setTag(item.getTag());
                    mSelectedAdapter.deleteClip(position);
                    if (mSelectedAdapter.isDifferentMedia(filePath)) {
                        dealMediaUnSelect(copy);
                    }
                    if (mSelectedAdapter.getSelectedPosition() >= 0) {
                        mRvMediaList.scrollToPosition(mSelectedAdapter.getSelectedPosition());
                        dealNextDisplay(false);
                    }
                }
            }
        });
    }


    private void goNext(List<MeidaClip> list) {
        if (mType == TYPE_ONE_FINISH) {
            dealFinishResult(list.get(0));
        }
    }

    @Override
    protected void requestData() {

    }

    /**
     * 处理媒体选中
     * Handle media selection
     */
    private void dealMediaSelect(MeidaClip clip, MediaData mediaData) {
        MediaTag tag = (MediaTag) mediaData.getTag();
        //tag为空说明有问题。 A null tag indicates a problem.
        if (tag == null) {
            LogUtils.e("Tag is null!, You must fix it");
            return;
        }
        int type = tag.getType();
        if (clip.getTag() == null) {
            clip.setTag(new int[]{-1, -1, -1, -1});
        }
        int[] index = (int[]) clip.getTag();
        if (MediaData.TYPE_ALL == type) {
            index[0] = ((MediaFragment) mFragmentList.get(0)).dealSelected(mediaData.getThumbPath());
            index[1] = ((MediaFragment) mFragmentList.get(1)).dealSelected(mediaData.getThumbPath());
            index[2] = ((MediaFragment) mFragmentList.get(2)).dealSelected(mediaData.getThumbPath());
        } else if (MediaData.TYPE_PHOTO == type) {
            index[2] = ((MediaFragment) mFragmentList.get(2)).dealSelected(mediaData.getThumbPath());
            index[0] = ((MediaFragment) mFragmentList.get(0)).dealSelected(mediaData.getThumbPath());
        } else {
            index[1] = ((MediaFragment) mFragmentList.get(1)).dealSelected(mediaData.getThumbPath());
            index[0] = ((MediaFragment) mFragmentList.get(0)).dealSelected(mediaData.getThumbPath());
        }
        index[3] = type;
    }

    /**
     * 处理媒体选中
     * Handle media selection
     */
    private void dealMediaUnSelect(MeidaClip clip) {
        if (clip.getTag() == null) {
            return;
        }
        String filePath = clip.getFilePath();
        if (TextUtils.isEmpty(filePath)) {
            return;
        }
        int[] index = (int[]) clip.getTag();
        index[0] = -1;
        index[2] = -1;
        index[1] = -1;
        int mediaType = index[3];
        if (MediaData.TYPE_ALL == mediaType) {
            ((MediaFragment) mFragmentList.get(0)).dealUnSelected(filePath);
            ((MediaFragment) mFragmentList.get(1)).dealUnSelected(filePath);
            ((MediaFragment) mFragmentList.get(2)).dealUnSelected(filePath);
        } else if (MediaData.TYPE_PHOTO == mediaType) {
            ((MediaFragment) mFragmentList.get(0)).dealUnSelected(filePath);
            ((MediaFragment) mFragmentList.get(2)).dealUnSelected(filePath);
        } else {
            ((MediaFragment) mFragmentList.get(0)).dealUnSelected(filePath);
            ((MediaFragment) mFragmentList.get(1)).dealUnSelected(filePath);
        }
    }

    /**
     * 处理下一步的展示样式
     * Handle the next presentation style
     */
    private void dealNextDisplay(boolean enable) {
        if (mTvNext.isEnabled() == enable) {
            return;
        }
        if (enable) {
            mTvNext.setBackgroundResource(R.drawable.bg_rectangle_round_red365e);
            mTvNext.setTextColor(getResources().getColor(R.color.white));
        } else {
            mTvNext.setBackgroundResource(R.drawable.bg_rectangle_round_gray4b4b4b);
            mTvNext.setTextColor(getResources().getColor(R.color.color_ffa4a4a4));
        }
        mTvNext.setEnabled(enable);
    }

    @Override
    public void onRequestPermissionsResult(int requestCode,
                                           @NonNull String[] permissions,
                                           @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        //判断如果同意的情况下就去把权限请求设置给当前fragment的 Set the request to the current fragment if it is approved
        for (int i = 0; i < mFragmentList.size(); i++) {
            mFragmentList.get(i).onRequestPermissionsResult(requestCode, permissions, grantResults);
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == REQUEST_CLIP_REPLACE && data != null) {
            data.putExtra(BUNDLE_DATA, mClipList.get(0));
            setResult(RESULT_OK, data);
            finish();
        }
        for (Fragment fragment : mFragmentList) {
            fragment.onActivityResult(requestCode, resultCode, data);
        }
    }

    /**
     * 处理选择一个素材就finish的结果
     * Handle the result of selecting an ingredient on Finish
     */
    private void dealFinishResult(MeidaClip clip) {

        if (clip == null) {
            return;
        }
        Intent it = new Intent();
        it.putExtra(BUNDLE_DATA, clip);
        //替换页面跳转过来的 From replace page.
        if (clip.getMediaType() == MediaData.TYPE_PHOTO) {
            setResult(RESULT_OK, it);
            finish();
        } else if (!TextUtils.isEmpty(mNextPageAction)) {
            Bundle bundle = new Bundle();
            bundle.putString(ClipReplaceActivity.VIDEO_PATH, clip.getFilePath());
            bundle.putLong(ClipReplaceActivity.VIDEO_LIMIT, clip.getDuration());
            it.setAction(mNextPageAction);
            it.putExtras(bundle);
            startActivityForResult(it, REQUEST_CLIP_REPLACE, bundle);
        }
    }

    private final MediaFragment.MediaChangeListener mMediaListener = new MediaFragment.MediaChangeListener() {
        @Override
        public boolean onMediaChange(final MediaData mediaData) {
            final MeidaClip clip = mSelectedAdapter.getItem(0);
            if (clip == null) {
                return false;
            }
            if (mediaData.getType() == MediaData.TYPE_VIDEO && clip.getDuration() > mediaData.getDuration() * 1000) {
                ToastUtils.make()
                        .setBgResource(R.drawable.bg_toast_selected_material)
                        .setGravity(Gravity.CENTER, 0, 0)
                        .setDurationIsLong(false)
                        .show(R.string.error_video_duration_short);
                return false;
            }
            if (TextUtils.equals(clip.getFilePath(), mediaData.getThumbPath())) {
                setMediaUnSelected(clip);
            } else {
                setMediaSelected(clip, mediaData);
            }
            return true;
        }

        @Override
        public void onMediaPreView(MediaData mediaData) {
            if (Utils.isFastClick()) {
                return;
            }
            if (mediaData == null) {
                LogUtils.e("mediaData is null !");
                return;
            }
            final MeidaClip clip = mSelectedAdapter.getItem(0);
            if (clip == null) {
                return;
            }
            if (mediaData.getType() == MediaData.TYPE_VIDEO && clip.getDuration() > mediaData.getDuration() * 1000) {
                ToastUtils.make()
                        .setBgResource(R.drawable.bg_toast_selected_material)
                        .setGravity(Gravity.CENTER, 0, 0)
                        .setDurationIsLong(false)
                        .show(R.string.error_video_duration_short);
                return;
            }
            Bundle bundle = new Bundle();
            mediaData.setPosition(1);
            if (mediaData.isState()) {
                bundle.putInt(MEDIA_MAX_NUM, 1);
            } else {
                bundle.putInt(MEDIA_MAX_NUM, 0);
            }
            bundle.putParcelable(MEDIA_DATA, mediaData);
            bundle.putParcelable(MEDIA_TAG, ((MediaTag) mediaData.getTag()));

            //跳转预览页面Jump to preview page
            AppManager.getInstance().jumpActivityForResult(MaterialSelectFillActivity.this,
                    MaterialPreviewActivity.class, bundle, MEDIA_REQUEST_CODE_PREVIEW);
        }
    };

    public void setMediaSelected(MeidaClip templateClip, MediaData mediaData) {
        templateClip.setMediaType(mediaData.getType());
        dealMediaSelect(templateClip, mediaData);
        MeidaClip oldClip = templateClip.copy();
        oldClip.setTag(templateClip.getTag());
        String oldClipFilePath = oldClip.getFilePath();
        mSelectedAdapter.setSelected(mediaData, 0);
        if (!TextUtils.isEmpty(oldClipFilePath) && mSelectedAdapter.isDifferentMedia(oldClipFilePath)) {
            dealMediaUnSelect(oldClip);
        }
        updateMediaSelectState();
    }

    private void updateMediaSelectState() {
        if (mSelectedAdapter.getSelectedPosition() == -1) {
            dealNextDisplay(true);
            mRvMediaList.scrollToPosition(mSelectedAdapter.getData().size() - 1);
        } else {
            mRvMediaList.scrollToPosition(mSelectedAdapter.getSelectedPosition());
        }
    }

    public void setMediaUnSelected(MeidaClip templateClip) {
        MeidaClip oldClip = templateClip.copy();
        oldClip.setTag(templateClip.getTag());
        String oldClipFilePath = oldClip.getFilePath();
        mSelectedAdapter.deleteClip(0);
        if (!TextUtils.isEmpty(oldClipFilePath) && mSelectedAdapter.isDifferentMedia(oldClipFilePath)) {
            dealMediaUnSelect(oldClip);
        }
        updateMediaSelectState();
    }
}
