package com.meishe.myvideo.activity;

import android.animation.Animator;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.annotation.SuppressLint;
import android.app.Application;
import android.content.Intent;
import android.graphics.PointF;
import android.graphics.Rect;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.util.Log;
import android.util.Pair;
import android.view.Gravity;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewTreeObserver;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;

import com.czc.cutsame.ExportTemplateActivity;
import com.czc.cutsame.ExportTemplateSettingActivity;
import com.iflytek.cloud.SpeechError;
import com.meicam.sdk.NvsColor;
import com.meicam.sdk.NvsMediaFileConvertor;
import com.meicam.sdk.NvsRational;
import com.meicam.sdk.NvsStreamingContext;
import com.meicam.sdk.NvsTimeline;
import com.meicam.sdk.NvsTimelineCaption;
import com.meicam.sdk.NvsTrackCompoundCaption;
import com.meicam.sdk.NvsVideoResolution;
import com.meishe.base.bean.FloatPoint;
import com.meishe.base.bean.MediaData;
import com.meishe.base.bean.Navigation;
import com.meishe.base.constants.Constants;
import com.meishe.base.manager.AppManager;
import com.meishe.base.model.BaseApplication;
import com.meishe.base.model.BaseMvpActivity;
import com.meishe.base.observer.BackgroundObserver;
import com.meishe.base.utils.BarUtils;
import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.FileIOUtils;
import com.meishe.base.utils.FormatUtils;
import com.meishe.base.utils.LogUtils;
import com.meishe.base.utils.NetUtils;
import com.meishe.base.utils.PermissionUtils;
import com.meishe.base.utils.ResourceUtils;
import com.meishe.base.utils.ScreenUtils;
import com.meishe.base.utils.StringUtils;
import com.meishe.base.utils.ToastUtils;
import com.meishe.base.utils.Utils;
import com.meishe.base.view.NavigationBar;
import com.meishe.business.assets.view.MYMultiBottomView;
import com.meishe.draft.DraftManager;
import com.meishe.engine.EditorEngine;
import com.meishe.engine.EngineCallbackManager;
import com.meishe.engine.asset.AssetsManager;
import com.meishe.engine.asset.AssetsUserManager;
import com.meishe.engine.asset.BackgroundProxy;
import com.meishe.engine.asset.bean.AssetInfo;
import com.meishe.engine.asset.bean.AssetList;
import com.meishe.engine.asset.bean.AssetsConstants;
import com.meishe.engine.bean.AnimationData;
import com.meishe.engine.bean.BaseInfo;
import com.meishe.engine.bean.ClipInfo;
import com.meishe.engine.bean.CommonData;
import com.meishe.engine.bean.CurveAdjustData;
import com.meishe.engine.bean.CurveSpeed;
import com.meishe.engine.bean.KeyFrameProcessor;
import com.meishe.engine.bean.MaskInfoData;
import com.meishe.engine.bean.MeicamAudioClip;
import com.meishe.engine.bean.MeicamAudioTrack;
import com.meishe.engine.bean.MeicamCaptionClip;
import com.meishe.engine.bean.MeicamCompoundCaptionClip;
import com.meishe.engine.bean.MeicamFxParam;
import com.meishe.engine.bean.MeicamKeyFrame;
import com.meishe.engine.bean.MeicamPosition2D;
import com.meishe.engine.bean.MeicamStickerClip;
import com.meishe.engine.bean.MeicamTheme;
import com.meishe.engine.bean.MeicamTimeline;
import com.meishe.engine.bean.MeicamTimelineVideoFilterAndAdjustClip;
import com.meishe.engine.bean.MeicamTimelineVideoFxClip;
import com.meishe.engine.bean.MeicamTimelineVideoFxTrack;
import com.meishe.engine.bean.MeicamTransition;
import com.meishe.engine.bean.MeicamVideoClip;
import com.meishe.engine.bean.MeicamVideoFx;
import com.meishe.engine.bean.MeicamVideoTrack;
import com.meishe.engine.bean.MeicamWaterMark;
import com.meishe.engine.bean.NvsObject;
import com.meishe.engine.bean.Plug;
import com.meishe.engine.bean.PlugDetail;
import com.meishe.engine.bean.bridges.AtomicFxBridge;
import com.meishe.engine.command.CaptionCommand;
import com.meishe.engine.command.ClipCommand;
import com.meishe.engine.command.Command;
import com.meishe.engine.command.CommandManager;
import com.meishe.engine.command.KeyFrameHolderCommand;
import com.meishe.engine.command.TimelineFxCommand;
import com.meishe.engine.command.VideoClipCommand;
import com.meishe.engine.command.VideoFxCommand;
import com.meishe.engine.constant.NvsConstants;
import com.meishe.engine.db.TimelineEntity;
import com.meishe.engine.interf.IBaseInfo;
import com.meishe.engine.interf.IKeyFrameProcessor;
import com.meishe.engine.observer.ConvertFileObserver;
import com.meishe.engine.observer.EngineCallbackObserver;
import com.meishe.engine.util.ConvertFileManager;
import com.meishe.engine.util.ConvertManagerProxy;
import com.meishe.engine.util.IConvertManager;
import com.meishe.engine.util.PathUtils;
import com.meishe.engine.view.ConvertProgressPop;
import com.meishe.libplugin.PluginManager;
import com.meishe.libplugin.user.IUserPlugin;
import com.meishe.libplugin.user.UserAssetsInfo;
import com.meishe.libplugin.user.UserConstant;
import com.meishe.logic.constant.PagerConstants;
import com.meishe.myvideo.R;
import com.meishe.myvideo.activity.iview.DraftEditView;
import com.meishe.myvideo.activity.presenter.BottomViewHelper;
import com.meishe.myvideo.activity.presenter.BottomViewModel;
import com.meishe.myvideo.activity.presenter.DraftEditPresenter;
import com.meishe.myvideo.activity.presenter.MultiBottomHelper;
import com.meishe.myvideo.activity.presenter.TopViewHelper;
import com.meishe.myvideo.bean.ChangeSpeedCurveInfo;
import com.meishe.myvideo.bean.EditMixedModeInfo;
import com.meishe.myvideo.bean.MeidaClip;
import com.meishe.myvideo.bean.MusicInfo;
import com.meishe.myvideo.edit.manager.CommandOperateManager;
import com.meishe.myvideo.edit.manager.IEditOperateManager;
import com.meishe.myvideo.edit.observer.EditOperateObserver;
import com.meishe.myvideo.event.MessageEvent;
import com.meishe.myvideo.fragment.CaptionAnimationFragment;
import com.meishe.myvideo.fragment.CaptionBubbleFlowerFragment;
import com.meishe.myvideo.fragment.CaptionStyleFragment;
import com.meishe.myvideo.fragment.EffectFragment;
import com.meishe.myvideo.fragment.PropFragment;
import com.meishe.myvideo.fragment.StickerAllFragment;
import com.meishe.myvideo.fragment.StickerAnimationFragment;
import com.meishe.myvideo.fragment.TransitionFragment;
import com.meishe.myvideo.fragment.VideoClipAnimationFragment;
import com.meishe.myvideo.fragment.VolumeFragment;
import com.meishe.myvideo.fragment.WaterEffectFragment;
import com.meishe.myvideo.fragment.WaterFragment;
import com.meishe.myvideo.interfaces.BottomEventListener;
import com.meishe.myvideo.interfaces.OnMiddleOperationClickListener;
import com.meishe.myvideo.interfaces.PlugsEventListener;
import com.meishe.myvideo.manager.AudioRecordManager;
import com.meishe.myvideo.manager.MenuDataManager;
import com.meishe.myvideo.manager.observer.AudioRecordObserver;
import com.meishe.myvideo.ui.bean.AnimationInfo;
import com.meishe.myvideo.ui.bean.BaseTrackClip;
import com.meishe.myvideo.ui.bean.BaseUIClip;
import com.meishe.myvideo.ui.bean.BaseUIVideoClip;
import com.meishe.myvideo.ui.bean.ITrackClip;
import com.meishe.myvideo.ui.bean.KeyFrameInfo;
import com.meishe.myvideo.ui.bean.LineRegionClip;
import com.meishe.myvideo.ui.bean.SpeedInfo;
import com.meishe.myvideo.ui.bean.ThumbnailClip;
import com.meishe.myvideo.ui.trackview.BaseItemView;
import com.meishe.myvideo.ui.trackview.HandView;
import com.meishe.myvideo.ui.trackview.TrackViewLayout;
import com.meishe.myvideo.ui.trackview.bean.AudioClipProxy;
import com.meishe.myvideo.ui.trackview.bean.CaptionProxy;
import com.meishe.myvideo.ui.trackview.bean.StickerProxy;
import com.meishe.myvideo.ui.trackview.bean.TimelineVideoFxProxy;
import com.meishe.myvideo.ui.trackview.bean.VideoClipProxy;
import com.meishe.myvideo.ui.trackview.impl.OnThumbnailTrimListener;
import com.meishe.myvideo.ui.trackview.impl.OnTrackClickListener;
import com.meishe.myvideo.ui.trackview.impl.OperationListener;
import com.meishe.myvideo.util.ConfigUtil;
import com.meishe.myvideo.util.PixelPerMicrosecondUtil;
import com.meishe.myvideo.util.PixelPerMicrosecondUtil.PixelPerMicrosecondChangeListener;
import com.meishe.myvideo.util.TrackViewDataHelper;
import com.meishe.myvideo.view.AtomicEditMenuView;
import com.meishe.myvideo.view.BottomContainer;
import com.meishe.myvideo.view.MYCanvasBlur;
import com.meishe.myvideo.view.MYCanvasColor;
import com.meishe.myvideo.view.MYCanvasStyle;
import com.meishe.myvideo.view.MYColorPickMenuView;
import com.meishe.myvideo.view.MYCompoundCaptionEditView;
import com.meishe.myvideo.view.MYEditorParentLayout;
import com.meishe.myvideo.view.MYEditorTimeLine;
import com.meishe.myvideo.view.MYEditorTimelineTrackView;
import com.meishe.myvideo.view.MYEffectTargetMenuView;
import com.meishe.myvideo.view.MYFilterMenuView;
import com.meishe.myvideo.view.MYMiddleOperationView;
import com.meishe.myvideo.view.MYMixedModeMenuView;
import com.meishe.myvideo.view.MYRecordMenuView;
import com.meishe.myvideo.view.MYSpeedCurveMenu;
import com.meishe.myvideo.view.TopContainer;
import com.meishe.myvideo.view.editview.AdjustSeekBarView;
import com.meishe.myvideo.view.editview.CompileProgress;
import com.meishe.myvideo.view.editview.EditAtomicCurveView;
import com.meishe.myvideo.view.editview.EditChangeSpeedCurveView;
import com.meishe.myvideo.view.editview.EditChangeSpeedView;
import com.meishe.myvideo.view.editview.EditChangeTransitionView;
import com.meishe.myvideo.view.editview.EditChangeVoiceView;
import com.meishe.myvideo.view.editview.EditKeyFrameCurveView;
import com.meishe.myvideo.view.editview.EditMaskView;
import com.meishe.myvideo.view.pop.CancelSmartKeyerPop;
import com.meishe.myvideo.view.pop.CommonPop;
import com.meishe.myvideo.view.pop.IdentifyCaptionDlg;
import com.meishe.net.custom.BaseResponse;
import com.meishe.net.custom.RequestCallback;
import com.meishe.player.fragment.VideoFragment;
import com.meishe.player.view.mask.MaskZoomView;
import com.meishe.speaker.VoiceDictationHelperWrapper;
import com.meishe.speaker.bean.Speech;
import com.meishe.speaker.bean.VoiceParam;
import com.meishe.third.pop.XPopup;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Hashtable;
import java.util.List;
import java.util.Map;

import static android.view.View.GONE;
import static android.view.View.VISIBLE;
import static com.czc.cutsame.TailorActivity.INTENT_TRAM;
import static com.meicam.sdk.NvsStreamingContext.STREAMING_ENGINE_COMPILE_FLAG_BUDDY_HOST_VIDEO_FRAME;
import static com.meicam.sdk.NvsStreamingContext.STREAMING_ENGINE_SEEK_FLAG_BUDDY_HOST_VIDEO_FRAME;
import static com.meishe.base.constants.Constants.EDIT_MODE_CAPTION;
import static com.meishe.base.constants.Constants.EDIT_MODE_COMPOUND_CAPTION;
import static com.meishe.base.constants.Constants.EDIT_MODE_MASK;
import static com.meishe.base.constants.Constants.EDIT_MODE_STICKER;
import static com.meishe.base.constants.Constants.EDIT_MODE_VIDEO_CLIP;
import static com.meishe.base.constants.Constants.EDIT_MODE_WATERMARK;
import static com.meishe.base.constants.Constants.EDIT_MODE_WATERMARK_EFFECT;
import static com.meishe.base.constants.Constants.TRACK_INDEX_MAIN;
import static com.meishe.base.utils.PermissionConstants.MICROPHONE;
import static com.meishe.business.assets.view.MYMultiBottomView.TYPE_MENU_CAPTION;
import static com.meishe.business.assets.view.MYMultiBottomView.TYPE_MENU_EFFECT;
import static com.meishe.business.assets.view.MYMultiBottomView.TYPE_MENU_PROP;
import static com.meishe.business.assets.view.MYMultiBottomView.TYPE_MENU_STICKER;
import static com.meishe.business.assets.view.MYMultiBottomView.TYPE_MENU_TRANSITION;
import static com.meishe.business.assets.view.MYMultiBottomView.TYPE_MENU_VOLUME_FADE;
import static com.meishe.business.assets.view.MYMultiBottomView.TYPE_MENU_WATER_MARK;
import static com.meishe.engine.EditorEngine.ReturnCode.CODE_OK;
import static com.meishe.engine.bean.CommonData.CLIP_VIDEO;
import static com.meishe.engine.bean.CommonData.MAIN_TRACK_INDEX;
import static com.meishe.engine.bean.CommonData.ONE_FRAME;
import static com.meishe.engine.bean.CommonData.STORYBOARD_BACKGROUND_TYPE_BLUR;
import static com.meishe.engine.bean.CommonData.STORYBOARD_BACKGROUND_TYPE_COLOR;
import static com.meishe.engine.bean.CommonData.STORYBOARD_BACKGROUND_TYPE_IMAGE;
import static com.meishe.engine.bean.CommonData.TIMEBASE;
import static com.meishe.engine.bean.MeicamVideoFx.SubType.SUB_TYPE_ALPHA;
import static com.meishe.engine.command.VideoClipCommand.PARAM_REVERSE_FILE_PATH;
import static com.meishe.engine.command.VideoClipCommand.PARAM_VIDEO_CONVERT_SUCCESS;
import static com.meishe.engine.constant.NvsConstants.ALPHA_FILE;
import static com.meishe.engine.constant.NvsConstants.SEGMENTATION;
import static com.meishe.engine.constant.NvsConstants.SET_ALPHA;
import static com.meishe.engine.constant.NvsConstants.TYPE_BUILD_IN;
import static com.meishe.engine.constant.NvsConstants.TYPE_PACKAGE;
import static com.meishe.logic.constant.PagerConstants.BUNDLE_CLIP;
import static com.meishe.logic.constant.PagerConstants.BUNDLE_DATA;
import static com.meishe.logic.constant.PagerConstants.FROM_DRAFT_EDIT;
import static com.meishe.logic.constant.PagerConstants.FROM_MAIN_PAGE;
import static com.meishe.logic.constant.PagerConstants.FROM_MATERIAL_SELECTED;
import static com.meishe.logic.constant.PagerConstants.FROM_PAGE;
import static com.meishe.logic.constant.PagerConstants.MEDIA_FILTER;
import static com.meishe.logic.constant.PagerConstants.MEDIA_TYPE;
import static com.meishe.logic.constant.PagerConstants.NEXT_PAGE_ACTION;
import static com.meishe.logic.constant.PagerConstants.REQUEST_CODE_1;
import static com.meishe.logic.constant.PagerConstants.REQUEST_CODE_2;
import static com.meishe.logic.constant.PagerConstants.SELECTED_TYPE;
import static com.meishe.logic.constant.PagerConstants.START_TIME;
import static com.meishe.logic.constant.PagerConstants.TYPE_ADD_SOME;
import static com.meishe.logic.constant.PagerConstants.TYPE_ONE_FINISH;
import static com.meishe.myvideo.activity.ClipCuttingActivity.CLIP_INDEX;
import static com.meishe.myvideo.activity.ClipCuttingActivity.INTENT_KEY_TIMELINE_HEIGHT;
import static com.meishe.myvideo.activity.ClipCuttingActivity.INTENT_KEY_TIMELINE_WIDTH;
import static com.meishe.myvideo.activity.ClipCuttingActivity.INTENT_KEY_TRACK_INDEX;
import static com.meishe.myvideo.activity.presenter.DraftEditPresenter.ADD_HOLDER;
import static com.meishe.myvideo.activity.presenter.DraftEditPresenter.CHANGE_HOLDER;
import static com.meishe.myvideo.activity.presenter.DraftEditPresenter.DELETE_HOLDER;
import static com.meishe.myvideo.event.MessageEvent.MESSAGE_APPLY_ALL_FILTER;
import static com.meishe.myvideo.event.MessageEvent.MESSAGE_REMOVE_CLIP_FILTER;
import static com.meishe.myvideo.event.MessageEvent.MESSAGE_REMOVE_TIMELINE_FILTER;
import static com.meishe.myvideo.event.MessageEvent.MESSAGE_TYPE_ADD_ANIMATION_IN;
import static com.meishe.myvideo.event.MessageEvent.MESSAGE_TYPE_ADD_NORMAL_CAPTION;
import static com.meishe.myvideo.event.MessageEvent.MESSAGE_TYPE_ADJUST;
import static com.meishe.myvideo.event.MessageEvent.MESSAGE_TYPE_ADJUST_CLICK;
import static com.meishe.myvideo.event.MessageEvent.MESSAGE_TYPE_ANIMATION_CONFIRM_EFFECT;
import static com.meishe.myvideo.event.MessageEvent.MESSAGE_TYPE_APPLY_ADJUST_TO_ALL;
import static com.meishe.myvideo.event.MessageEvent.MESSAGE_TYPE_CHANGE_ADJUST_FINISH;
import static com.meishe.myvideo.event.MessageEvent.MESSAGE_TYPE_CHANGE_ANIMATION_IN_DURATION;
import static com.meishe.myvideo.event.MessageEvent.MESSAGE_TYPE_CHANGE_ANIMATION_OUT_DURATION;
import static com.meishe.myvideo.event.MessageEvent.MESSAGE_TYPE_CHANGE_CLIP_FILTER_FINISH;
import static com.meishe.myvideo.event.MessageEvent.MESSAGE_TYPE_CHANGE_CLIP_FILTER_PROGRESS;
import static com.meishe.myvideo.event.MessageEvent.MESSAGE_TYPE_CHANGE_SPEED_CURVE;
import static com.meishe.myvideo.event.MessageEvent.MESSAGE_TYPE_CHANGE_SPEED_CURVE_VIEV_UPDATE;
import static com.meishe.myvideo.event.MessageEvent.MESSAGE_TYPE_CHANGE_TIMELINE_FILTER_FINISH;
import static com.meishe.myvideo.event.MessageEvent.MESSAGE_TYPE_CHANGE_TIMELINE_FILTER_PROGRESS;
import static com.meishe.myvideo.event.MessageEvent.MESSAGE_TYPE_MIXED_MODE;
import static com.meishe.myvideo.event.MessageEvent.MESSAGE_TYPE_MIXED_MODE_PROGRESS;
import static com.meishe.myvideo.event.MessageEvent.MESSAGE_TYPE_MIXED_MODE_PROGRESS_FINISH;
import static com.meishe.myvideo.event.MessageEvent.MESSAGE_TYPE_PLUG_CLICK;
import static com.meishe.myvideo.event.MessageEvent.MESSAGE_TYPE_REFRESH_CAPTION_RANGE;
import static com.meishe.myvideo.event.MessageEvent.MESSAGE_TYPE_REMOVE_ANIMATION_GROUP_DURATION;
import static com.meishe.myvideo.event.MessageEvent.MESSAGE_TYPE_RESET_ADJUST_CLIP;
import static com.meishe.myvideo.event.MessageEvent.MESSAGE_TYPE_RESET_ADJUST_TIMELINE;
import static com.meishe.myvideo.event.MessageEvent.MESSAGE_TYPE_UPDATE_FILTER_PROGRESS;
import static com.meishe.myvideo.event.MessageEvent.MESSAGE_TYPE_WIDTH_CONFIRM_EFFECT;
import static com.meishe.player.fragment.presenter.VideoPresenter.EDIT_MODE_EDIT_TIMELINE;


/**
 * Created by MZ008064 on 2020-05-08.
 * 制图编辑类
 * Cartographic editor class
 *
 * <AUTHOR>
 * @CreateDate 2020 -05-08.
 */
public class DraftEditActivity extends BaseMvpActivity<DraftEditPresenter> implements OnMiddleOperationClickListener,
        TrackViewLayout.OnTrackViewScrollListener, TrackViewLayout.OnTrackViewDragListener, EditorEngine.OnTimelineChangeListener,
        HandView.OnHandChangeListener, PixelPerMicrosecondChangeListener, View.OnClickListener, DraftEditView, IUserPlugin.ILoginCallBack {
    private static final String TAG = "DraftEditActivity";
    private final int REQUEST_PICTURE_IN_PICTURE_CODE = 101;
    private final int REQUEST_SELECT_IMAGE_BACKGROUND_CODE = 102;
    private final int REQUEST_CROP_CLIP_CODE = 103;
    public static final int REQUEST_SELECT_WATERMARK = 104;
    public static final int REQUEST_PLUG_SELECT_PICTURE = 105;
    public static final int REQUEST_CLIP_REPLACE = 106;
    private final int REQUEST_SELECT_IMAGE_COVER_CODE = 107;

    /**
     * 视频播放
     * video playing
     */
    private static final int FLAG_PLAY = 1;
    /**
     * 滑动缩略图控件
     * Slide the thumbnail control
     */
    private static final int FLAG_SEQ = 11;
    /**
     * 滑动多轨
     * Slide rail more
     */
    private static final int FLAG_TRACK_VIEW = 111;
    public static final int REQUEST_ASSET_LIST_CODE = 111;
    private static final int SELECT_MUSIC = 112;
    private int mSeekFlag = FLAG_PLAY;
    private ImageView mIvBack;
    private TextView mTvExportVideo;
    /**
     * 操作台(播放，撤销，前进，全屏)
     * Console (Play, undo, forward, full screen)
     */
    private MYMiddleOperationView mEditOperationView;
    /**
     * 操作撤销、恢复管理
     * Operation undo, restore management
     */
    private IEditOperateManager mEditOperateManager;
    private MYEditorTimeLine mEditTimeline;
    private MYEditorTimelineTrackView mEditorTrackView;
    private MYMultiBottomView mMultiBottomView;
    private NavigationBar mNavigationBar;
    private BottomContainer mBottomViewContainer;
    /**
     * MYEditorTimeLine 的编辑对象
     * Edit objects for MYEditorTimeLine
     */
    private EditorEngine mEditorEngine;
    /**
     * 识别字幕弹窗
     * The identify caption dialog
     */
    private IdentifyCaptionDlg mIdentifyCaptionDlg;

    /**
     * 时间线，编辑场景的时间轴实体
     * Timeline. Edit the timeline entity of the scene
     */
    private MeicamTimeline mTimeline;
    private VideoFragment mVideoFragment;
    /**
     * 生成视频中遮层
     * Generate a mask in the video
     */
    private RelativeLayout mRlCompileProgress;
    private CompileProgress mEditCompileProgress;
    private TextView mTvCompileProgress;
    private View mBtCompileCancel;
    private MYEditorParentLayout mEditorParentView;
    //private MaterialTrackView mMaterialTrack;
    private CommonPop mCommonPop;
    private CancelSmartKeyerPop mCancelSmartKeyerPop;

    /**
     * 音频轨道集合
     * Audio track set
     */
    private HashMap<Integer, List<BaseUIClip>> mTrackListHashMap = new HashMap<>();
    /**
     * 当前选中的音乐片段
     * The currently selected piece of music
     */
    private MeicamAudioClip mCurrSelectedAudioClip = null;


    /**
     * 当前选中的视频特效
     * The currently selected video fx
     */
    private MeicamTimelineVideoFxClip mCurrSelectedVideoFx = null;

    /**
     * 当前选择的调节或者滤镜的clip特效
     * The currently selected filter and adjust clip
     */
    private MeicamTimelineVideoFilterAndAdjustClip mCurrSelectedFilterAndAdjustClip = null;
    /**
     * 当前选中的子轨片段
     */
    private ClipInfo<?> mCurrSelectedCaptionStickClip = null;
    /**
     * 当前选择的轨道数据
     * The currently selected track data
     */
    private BaseUIClip mSelectTrackData;

    /**
     * 当前选中的片段
     * The currently selected fragment
     */
    private MeicamVideoClip mCurSelectVideoClip;

    private AudioRecordManager mAudioRecordManager = null;
    private int mFromPage;
    public String mDraftDir;
    private ConvertManagerProxy mConvertFileManager;
    private TextView mTvProgressDesc;

    private long mCurrentInPoint;
    private int mCurrentTrackIndex = -1;

    private int mKeyboardHeight = 0;
    private boolean mIsVisibleForLast = false;
    private ViewTreeObserver.OnGlobalLayoutListener mOnGlobalLayoutListener = null;
    private View mDecorView;
    private boolean mFirstGetKeyboardHeight = true;
    private FrameLayout mFlSmartKeyer;
    private TextView mTvSmartKeyer;
    private ImageView mIvCancelSmartKeyer;
    private boolean mActivityStopped;
    /**
     * 导出模板view
     * export template
     */
    private TextView mTvExportTemplate;
    private View mLoginView;
    private long mBeforeJumpTimelinePosition = -1L;
    private int mInsertVideoClipIndex = -1;
    private boolean mIsAddTitleTheme;
    private BottomViewHelper mBottomViewHelper;
    private TopViewHelper mTopViewHelper;
    private MultiBottomHelper mMultiHelper;
    private final BackgroundObserver backgroundObserver = new BackgroundObserver() {
        @Override
        public void turnToBackground() {
            if (!(AppManager.getInstance().isTopActivity(ExportTemplateSettingActivity.class) ||
                    AppManager.getInstance().isTopActivity(ExportTemplateActivity.class))) {
                /*进入导出模板设置页面和导出模板页面之前就已经保存过草稿了，不要再保存草稿了，
                而且这两个页面可能会对timeline的数据进行更改，但是这个更改仅仅是临时的，不能保存到草稿里，
                而且回到编辑页面需要恢复过来。
                The draft has been saved before entering the export template setting page and the export template page.
                Don't save the draft again.And these two pages may change the timeline data, but this change is only
                temporary and cannot be saved in the draft.You need restored the timeline after back to the edit activity.
                */
                saveDraft();
                if (mBeforeJumpTimelinePosition != -1) {
                    mVideoFragment.seekTimeline(mBeforeJumpTimelinePosition, 0);
                }
            }
        }
    };
    private EditorEngine.OnTrackChangeListener mOnTrackChangedListener;
    private View mWarningLine;

    private void addOnSoftKeyBoardVisibleListener() {
        if (mKeyboardHeight > 0) {
            return;
        }
        mDecorView = getWindow().getDecorView();
        mOnGlobalLayoutListener = new ViewTreeObserver.OnGlobalLayoutListener() {
            @Override
            public void onGlobalLayout() {
                if (!mFirstGetKeyboardHeight && mKeyboardHeight > 0) {
                    return;
                }
                Rect rect = new Rect();
                mDecorView.getWindowVisibleDisplayFrame(rect);
                //计算出可见屏幕的高度
                //Calculate the height of the visible screen
                int displayHeight = rect.bottom - rect.top;
                //获得屏幕整体的高度
                //Get the overall height of the screen
                int hight = mDecorView.getHeight();
                boolean visible = (double) displayHeight / hight < 0.8;
                if (visible && !mIsVisibleForLast) {
                    mKeyboardHeight = hight - displayHeight - BarUtils.getStatusBarHeight() - BarUtils.getNavBarHeight(DraftEditActivity.this);
                    mFirstGetKeyboardHeight = false;
                    if (mMultiBottomView.isShow() && mMultiBottomView.getType() == TYPE_MENU_CAPTION) {
                        mMultiBottomView.setKeyboardHeight(mKeyboardHeight);
                    }
                    if (mBottomViewContainer.getShowView() instanceof MYCompoundCaptionEditView) {
                        ((MYCompoundCaptionEditView) mBottomViewContainer.getShowView()).setKeyboardHeight(mKeyboardHeight);
                    }
                }
                mIsVisibleForLast = visible;
            }
        };
        mDecorView.getViewTreeObserver().addOnGlobalLayoutListener(mOnGlobalLayoutListener);
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        PixelPerMicrosecondUtil.init(this);
        super.onCreate(savedInstanceState);
        EventBus.getDefault().register(this);
        Application application = getApplication();
        if (application instanceof BaseApplication) {
            ((BaseApplication) application).registerBackgroundObserver(backgroundObserver);
        }
    }

    @Override
    protected int bindLayout() {
        return R.layout.activity_draft_edit;
    }

    @Override
    protected void initData(Bundle savedInstanceState) {
        if (savedInstanceState != null) {
            String string = savedInstanceState.getString(PagerConstants.BUNDLE_KEY_TIMELINE_DATA);
            if (!TextUtils.isEmpty(string)) {
                LogUtils.d("timeline data is rebuild!!");
                MeicamTimeline meicamTimeline = EditorEngine.getInstance().recoverTimeline(string);
                if (meicamTimeline == null) {
                    LogUtils.e("timeline is null !!!");
                    backToMain();
                }
                EditorEngine.getInstance().setCurrentTimeline(meicamTimeline);
            }
            mFromPage = savedInstanceState.getInt(FROM_PAGE, FROM_MATERIAL_SELECTED);
            FragmentManager supportFragmentManager = getSupportFragmentManager();
            List<Fragment> fragments = supportFragmentManager.getFragments();
            if (!CommonUtils.isEmpty(fragments)) {
                for (Fragment fragment : fragments) {
                    supportFragmentManager.beginTransaction().remove(fragment).commitAllowingStateLoss();
                    LogUtils.d("remove fragment:" + fragment);
                }
            }
        }
        Intent intent = getIntent();
        if (intent != null) {
            mFromPage = intent.getIntExtra(FROM_PAGE, FROM_MATERIAL_SELECTED);
            if (mFromPage == FROM_MATERIAL_SELECTED) {//来自素材选择页面 from MaterialSelectActivity
                ArrayList<MediaData> mediaList = intent.getParcelableArrayListExtra(BUNDLE_DATA);
                mPresenter.initEngine(mediaList);
            }
        }
        AppManager.getInstance().setFromType(mFromPage);
        mBottomViewHelper = new BottomViewHelper(new BottomViewModel());
        mEditorEngine = mPresenter.getEngine();
        mTimeline = mPresenter.getTimeline();
        mConvertFileManager = new ConvertManagerProxy();
        mAudioRecordManager = AudioRecordManager.getInstance().init();

        mEditorEngine.checkBeautyShape();
        mEditOperateManager = CommandOperateManager.get();
        if (mTimeline == null) {
            LogUtils.e("timeline is null !!!");
            if (mFromPage == FROM_MAIN_PAGE) {
                ToastUtils.showShort(R.string.error_draft_data_is_error);
            }
            backToMain();
            return;
        }
        mDraftDir = mTimeline.getDraftDir();
        if (TextUtils.isEmpty(mDraftDir)) {
            mDraftDir = DraftManager.newDraftDir(String.valueOf(System.currentTimeMillis()));
            mTimeline.setDraftDir(mDraftDir);
        }
        DraftManager.getInstance().setCurrentDraftDir(mDraftDir);
        //saveOperation();
    }

    @Override
    protected void onSaveInstanceState(@NonNull Bundle outState) {
        super.onSaveInstanceState(outState);
        MeicamTimeline timeline = EditorEngine.getInstance().getCurrentTimeline();
        if (timeline != null) {
            //outState.putString(PagerConstants.BUNDLE_KEY_TIMELINE_DATA, timeline.toDraftJson());
            //outState.putString(DRAFT_PATH, mDraftDir);
            outState.putInt(FROM_PAGE, mFromPage);
        }
    }

    private void backToMain() {
      /*  Bundle bundle = new Bundle();
        bundle.putBoolean(BUNDLE_SAVE_DRAFT, false);
        Intent intent = new Intent("com.meishe.myvideo.action.MAIN");
        intent.putExtras(bundle);
        startActivity(intent);*/
        finish();
    }

    @Override
    protected void initView() {
        if (mTimeline == null) {
            return;
        }
        mWarningLine = findViewById(R.id.warning_line);
        mIvBack = findViewById(R.id.iv_back_pressed);
        mTvExportVideo = findViewById(R.id.tv_export_video);
        mEditOperationView = findViewById(R.id.edit_operation_view);
        mEditTimeline = findViewById(R.id.edit_timeline);
        mNavigationBar = findViewById(R.id.edit_navigation_bar);
        mEditCompileProgress = findViewById(R.id.edit_compile_progress);
        mTvCompileProgress = findViewById(R.id.tv_compile_progress);
        mRlCompileProgress = findViewById(R.id.rl_compile_progress);
        mTvProgressDesc = findViewById(R.id.tv_compile_info);
        mBtCompileCancel = findViewById(R.id.bt_compile_cancel);
        mMultiBottomView = findViewById(R.id.edit_add_sticker);
        mBottomViewContainer = findViewById(R.id.fl_bottom_container);
        TopContainer mTopViewContainer = findViewById(R.id.fl_top_container);
        mEditorTrackView = findViewById(R.id.editor_track_view);
        mEditorParentView = findViewById(R.id.editor_parent_view);
        LinearLayout llTopOperationBarView = findViewById(R.id.top_operation_bar_layout);
        mFlSmartKeyer = findViewById(R.id.fl_smart_keyer);
        mTvSmartKeyer = findViewById(R.id.tv_smart_keyer_hint);
        mIvCancelSmartKeyer = findViewById(R.id.iv_cancel_smart_keyer);
        mTvExportTemplate = findViewById(R.id.tv_export_template);
        mLoginView = findViewById(R.id.iv_login);
        mEditorTrackView.setTimeline(mTimeline);
        mBottomViewContainer.setFragmentManager(getSupportFragmentManager());
        mBottomViewHelper.attachView(mBottomViewContainer);
        mTopViewContainer.setFragmentManager(getSupportFragmentManager());
        mTopViewHelper = new TopViewHelper(mTopViewContainer);
        mMultiHelper = new MultiBottomHelper(mMultiBottomView);
        mMultiBottomView.setFragmentManager(getSupportFragmentManager());
        initVideoFragment();
        initEditorTimeline();
        if (mPresenter.checkThemeCaption()) {
            mEditorTrackView.initWidth(mTimeline.getDuration(), mEditTimeline.durationToLength(mPresenter.getTitleThemeDuration()));
        }
        int statusBarViewHeight = ScreenUtils.getStatusBarHeight();
        if (statusBarViewHeight > 0) {
            ConstraintLayout.LayoutParams layoutParams = (ConstraintLayout.LayoutParams) llTopOperationBarView.getLayoutParams();
            layoutParams.topMargin = statusBarViewHeight + 18;
            llTopOperationBarView.setLayoutParams(layoutParams);
        }
        initNavigationBar();
        initListener();
        if (ConfigUtil.isToC()) {
            mLoginView.setVisibility(GONE);
        } else {
            onLoginBack(mPresenter.isLogin());
        }
    }

    private void initVideoFragment() {
        FragmentManager fragmentManager = getSupportFragmentManager();
        mVideoFragment = VideoFragment.create(EDIT_MODE_EDIT_TIMELINE, NvsConstants.sHdrPreviewMode);
        fragmentManager.beginTransaction().add(R.id.edit_preview_view, mVideoFragment).commitAllowingStateLoss();
        fragmentManager.beginTransaction().show(mVideoFragment);
    }

    @SuppressLint("ClickableViewAccessibility")
    private void initListener() {
        mLoginView.setOnClickListener(this);
        mIvBack.setOnClickListener(this);
        mTvExportVideo.setOnClickListener(this);
        mIvCancelSmartKeyer.setOnClickListener(this);
        mTvExportTemplate.setOnClickListener(this);
        mBtCompileCancel.setOnClickListener(this);
        mEditOperationView.setOnMiddleOperationClickListener(this);
        mEditorParentView.setOnClickListener(this);
        mEditorTrackView.setOnHandChangeListener(this);
        mEditorTrackView.setOnTrackViewScrollListener(this);
        mEditorTrackView.setOnTrackViewDragListener(this);
        mEditorTrackView.setOnSystemUiVisibilityChangeListener(new View.OnSystemUiVisibilityChangeListener() {
            @Override
            public void onSystemUiVisibilityChange(int visibility) {
                Log.e(TAG, "onSystemUiVisibilityChange visibility = " + visibility);
            }
        });
        addOnSoftKeyBoardVisibleListener();
        mEditorEngine.setOnTimelineChangeListener(this);
        PixelPerMicrosecondUtil.addPixelPerMicrosecondChangeListener(this);
        EngineCallbackManager.get().registerCallbackObserver(mEngineCallbackObserver);
        mEditTimeline.setOnScrollListener(new MYEditorTimeLine.OnScrollListener() {
            @Override
            public void onScrollStopped() {
            }

            @Override
            public void onSeekingTimeline(boolean isSeekTimeline) {
                mSeekFlag = FLAG_SEQ;
                //mEditorEngine.seekTimeline(0);
            }
        });
        mEditTimeline.setThumbnailTrimListener(new OnThumbnailTrimListener() {
            long offset = 0;
            long originOutPoint = 0;
            long originInPoint = 0;

            @Override
            public void onThumbnailTrimStart(ITrackClip trackClip, boolean changeLeft) {
                originOutPoint = trackClip.getOutPoint();
                originInPoint = trackClip.getInPoint();
                offset = trackClip.getOutPoint() - trackClip.getInPoint();
                long trimPoint;
                if (changeLeft) {
                    trimPoint = BaseTrackClip.CLIP_VIDEO.equals(trackClip.getType()) ? 0 : TIMEBASE * 60;
                } else {
                    /*图片原始时长太大，如果设置可能会造成内存不足，这里暂时写1分钟
                     * The original duration of the picture is too long.
                     * If it is set, it may cause out of memory. Here, it is temporarily written for 1 minute
                     * */
                    trimPoint = BaseTrackClip.CLIP_VIDEO.equals(trackClip.getType()) ? trackClip.getOriginalDuration() : TIMEBASE * 60;
                }
                mEditorEngine.changeVideoClipTrim(trackClip.getIndexInTrack(), trimPoint, changeLeft);

            }

            @Override
            public void onThumbnailTrim(ITrackClip trackClip, boolean changeLeft) {
                if (mPresenter.getStreamingEngineState() != NvsStreamingContext.STREAMING_ENGINE_STATE_PLAYBACK) {
                    if (mTimeline != null) {
                        long timestamp;
                        if (changeLeft) {
                            timestamp = trackClip.getInPoint() + trackClip.getTrimIn();
                        } else {
                            timestamp = trackClip.getOutPoint() - 5;
                        }
                        mVideoFragment.seekTimeline(timestamp, 0);
                    }
                }
            }

            @Override
            public void onThumbnailTrimComplete(ITrackClip trackClip, boolean changeLeft) {
                if (mCurSelectVideoClip == null) {
                    LogUtils.ex("onThumbnailTrimComplete mCurSelectVideoClip is NULL!");
                    return;
                }
                offset = trackClip.getOutPoint() - trackClip.getInPoint() - offset;
                long trimPoint;
                if (changeLeft) {
                    /*图片只会更改trim out
                     * The picture only changes trim out
                     * */
                    trimPoint = BaseTrackClip.CLIP_VIDEO.equals(trackClip.getType()) ? trackClip.getTrimIn() : trackClip.getTrimOut();
                } else {
                    trimPoint = trackClip.getTrimOut();
                }
                mEditorEngine.timelineAddOrSubtract(originOutPoint, offset);

                mEditorEngine.changeVideoClipTrim(trackClip.getIndexInTrack(), trimPoint, changeLeft);
                mPresenter.checkTrackDuration();
                mEditorTrackView.initWidth(mTimeline.getDuration(), 0);
                if (mCurSelectVideoClip != null) {
                    /*长度减少，需要移除被剪裁掉的关键帧，因为更改trim底层不会删除关键帧
                     *The length is reduced. You need to remove the clipped key frame,
                     * because changing the trim bottom layer will not delete the key frame
                     * **/
                    mEditorEngine.checkKeyFrame(mCurSelectVideoClip, offset, changeLeft);
                    if (showAtomicEditMenuViewKeyFrame()) {
                        AtomicEditMenuView view = (AtomicEditMenuView) mBottomViewHelper.getView().getShowView();
                        if (mCurSelectVideoClip != null) {
                            Plug plug = view.getSelectedPlug();
                            if (plug != null) {
                                MeicamVideoFx meicamVideoFx = mCurSelectVideoClip.
                                        getVideoFx(MeicamVideoFx.SubType.SUB_TYPE_PLUG_FX, plug.plugName, plug.getClipIndex());
                                mPresenter.updateKeyFrameInfo(trackClip.getKeyFrameInfo(),
                                        meicamVideoFx, view.getSelectedParam().paramName);
                            }
                        }
                    } else {
                        mPresenter.updateKeyFrameInfo(trackClip.getKeyFrameInfo(), mCurSelectVideoClip.findPropertyVideoFx(), NvsConstants.KEY_CROPPER_TRANS_X);
                    }
                    mEditTimeline.updateThumbnail(trackClip, false);
                    mEditTimeline.checkKeyFrameSelected(mPresenter.getTimelineCurrentPosition());
                    mPresenter.updateTransitionByVideoClipDuration(trackClip.getIndexInTrack());
                    if (mNavigationBar.isShow(R.string.nb_video_edit1)
                            || mNavigationBar.isShow(R.string.nb_picture_edit1)) {
                        mPresenter.getMainTrackLineRegion();
                    }
                    mPresenter.updateTimelineFxTargetVideoClipInMain(mCurSelectVideoClip, offset);
                    if (mEditorTrackView.isShowTrackView()) {
                        updateStickerTrack();
                    }
                }
                saveOperation();
            }
        });
        mEditTimeline.setMainTrackClickListener(new OnTrackClickListener() {
            @Override
            public void onThumbnailTailClick(final int thumbnailIndex, final ThumbnailClip.TailInfo tailInfo) {

                if (!mEditorEngine.canAddTransition(thumbnailIndex)) {
                    ToastUtils.showShort(R.string.add_transition_tip);
                    return;
                }
                if (!TextUtils.isEmpty(tailInfo.getId()) && tailInfo.getType() == -1) {
                    tailInfo.setType(mPresenter.getTransitionTypeByDb(tailInfo.getId()));
                }
                int transitionType = tailInfo.getType();
                int selectTab = 0;
                if (!ConfigUtil.isNewAssets()) {
                    if (transitionType == AssetInfo.ASSET_VIDEO_TRANSITION_3D) {
                        selectTab = 1;
                    } else if (transitionType == AssetInfo.ASSET_VIDEO_TRANSITION_EFFECT) {
                        selectTab = 2;
                    }
                } else {
                    if (transitionType == AssetInfo.ASSET_VIDEO_TRANSITION_3D) {
                        selectTab = 1;
                    }/* else if (transitionType == AssetInfo.ASSET_VIDEO_TRANSITION_EFFECT) {
                        selectTab = 0;
                    }*/
                }
                //点击转场的时候隐藏下面的view
                //Hide the view below when you click the transition
                hideBottomView();
                mMultiHelper.showTransitionView(selectTab, thumbnailIndex,
                        new TransitionFragment.TransitionEventListener() {
                            @Override
                            public void onTransitionClick(IBaseInfo baseInfo, int transitionIndex, boolean applyAll) {
                                int coverId = 0;
                                if (baseInfo.getCoverId() != 0 || !TextUtils.isEmpty(baseInfo.getCoverPath())) {
                                    coverId = R.mipmap.ic_transition_added;
                                }
                                if (applyAll) {
                                    mEditTimeline.setAllThumbnailTailInfo(new ThumbnailClip.TailInfo()
                                            .setType(baseInfo.getType())
                                            .setId(baseInfo.getEffectMode() == BaseInfo.EFFECT_MODE_BUILTIN ?
                                                    baseInfo.getEffectId() : baseInfo.getPackageId())
                                            .setCoverId(coverId));
                                    //.setCoverPath(baseInfo.getCoverPath()));
                                } else {
                                    tailInfo.setType(baseInfo.getType())
                                            .setId(baseInfo.getEffectMode() == BaseInfo.EFFECT_MODE_BUILTIN ?
                                                    baseInfo.getEffectId() : baseInfo.getPackageId())
                                            .setCoverId(coverId);
                                    //.setCoverPath(baseInfo.getCoverPath());
                                    mEditTimeline.updateThumbnailTailInfo(transitionIndex);
                                }
                            }
                        });
            }

            @Override
            public void onThumbnailClick(ITrackClip trackClip, boolean selected, boolean fromUser) {
                if (!selected) {
                    mVideoFragment.openMaskZoomEditMode(null, false);
                    switchToMainMenu();
                    View showView = mBottomViewContainer.getShowView();
                    if (showView instanceof MYColorPickMenuView) {
                        mVideoFragment.releaseColorPicker();
                    }
                    mBottomViewContainer.dismissView();
                    mBottomViewContainer.dismissFragment();
                    mEditTimeline.enableThumbnailMove(true);
                    mEditOperationView.notShowKeyFrameView();
                    return;
                }
                if (mEditorEngine.isPlaying()) {
                    mEditorEngine.stop();
                }
                int selectIndex;
                if (mPresenter.isAddTitleTheme()) {
                    selectIndex = trackClip.getIndexInTrack() - 1;
                } else {
                    selectIndex = trackClip.getIndexInTrack();
                }
                MeicamVideoClip videoClip = mEditorEngine.getVideoClip(MAIN_TRACK_INDEX, selectIndex);
                if (videoClip != null) {
                    mCurSelectVideoClip = videoClip;
                    mCurrentInPoint = mCurSelectVideoClip.getInPoint();
                    mCurrentTrackIndex = 0;
                    LogUtils.d("onThumbnailClick mCurSelectVideoClip index===" + mCurSelectVideoClip.getIndex() + "===mCurSelectVideoClip inPoint===" + mCurSelectVideoClip.getInPoint());
                }
                mVideoFragment.updateTransformFx(mCurSelectVideoClip, true);
                //此时如果显示蒙版的view  切换片段就隐藏掉
                //At this point, if the view switching fragment showing the mask is hidden
                mVideoFragment.openMaskZoomEditMode(mCurSelectVideoClip, false);
                int showingName = mNavigationBar.getShowingNavigationName();
                if (showingName != R.string.nb_background1 && showingName != R.string.nb_animate2 &&
                        showingName != R.string.nb_ratio1) {
                    mEditTimeline.enableThumbnailAnimation(false);
                    showEditNavigation();
                    mEditTimeline.checkKeyFrameSelected(mPresenter.getTimelineCurrentPosition());
                    ITrackClip mainSelectedClip = mEditTimeline.getMainSelectedClip();
                    if (mainSelectedClip != null) {
                        mainSelectedClip.setInPoint(mCurSelectVideoClip.getInPoint());
                        mainSelectedClip.setOutPoint(mCurSelectVideoClip.getOutPoint());
                        SpeedInfo speedInfo = mainSelectedClip.getSpeedInfo();
                        speedInfo.setSpeed(mCurSelectVideoClip.getSpeed());
                        speedInfo.setSpeedName(mCurSelectVideoClip.getCurveSpeedName());
                        mainSelectedClip.setKeyFrameInfo(mPresenter.getKeyFrameInfo(mCurSelectVideoClip.findPropertyVideoFx(), null));
                        mEditTimeline.changeMainTrackClipSpeed(mainSelectedClip, mPresenter.getTimeline().getDuration());
                    }
                    updateKeyFrameStatus();
                    mVideoFragment.hideOperationBox();
                    hideBottomView();
                    mEditorTrackView.clickOutSide();
                } else {
                    if (showingName == R.string.nb_animate2) {
                        mEditorTrackView.clickOutSide();
                        mEditTimeline.enableThumbnailCoverEditMode(true);
                        mEditTimeline.enableThumbnailAnimation(true);
                        mEditTimeline.notShowSpanView();
                    }
                    if (mBottomViewContainer.getShowFragment() instanceof VideoClipAnimationFragment) {
                        mBottomViewHelper.updateAnimationView(mCurSelectVideoClip);
                    }
                    mEditorTrackView.toOtherMenu();
                }
            }

            @Override
            public void onPipIconClick(int trackIndex, long inPoint) {
                MeicamVideoClip pipVideoClip = mPresenter.findPipVideoClip(trackIndex, inPoint);
                if (pipVideoClip != null) {
                    toOtherMenu();
                    mEditorTrackView.showPipTrackView(mTimeline);
                    //注意这里的trackIndex是真实的 做选中的话需要 -1操作
                    // Note that the trackIndex here is real. If you want to select it, you need - 1 operation.
                    mEditorTrackView.setSelect(trackIndex - 1, inPoint);
                    mVideoFragment.updateTransformFx(pipVideoClip, true);
                    mCurrentInPoint = inPoint;
                    mCurrentTrackIndex = trackIndex;
                    mEditTimeline.showEffectRegion(false, false, true);
                }
            }

            @Override
            public void toggleOriginalVoice(boolean open) {
                mEditorEngine.toggleOriginalVoice(MAIN_TRACK_INDEX, open, true);
                mEditTimeline.setMainTrackList(mPresenter.getMainTrackThumbnailList());
                saveOperation();
            }

            @Override
            public void onAddThumbnailClick() {
                if (Utils.isFastClick()) {
                    return;
                }
                if (mTimeline == null) {
                    return;
                }
                if (mPresenter.checkVoiceDictation()) {
                    return;
                }
                mInsertVideoClipIndex = mPresenter.getInsertVideoClipIndex();
                Bundle bundle = new Bundle();
                bundle.putInt(FROM_PAGE, FROM_DRAFT_EDIT);
                bundle.putInt(SELECTED_TYPE, TYPE_ADD_SOME);
                AppManager.getInstance().jumpActivityForResult(DraftEditActivity.this, MaterialSelectActivity.class, bundle, REQUEST_CODE_1);
            }
        });
        mEditTimeline.setOperationListener(new OperationListener() {
            @Override
            public void onTimeScroll(long timestamp, boolean fromUser, int newX, int oldX) {
                /*if (!mNavigationBar.isShow(R.string.nb_background1) && !(mBottomViewContainer.getShowFragment() instanceof VideoClipAnimationFragment)
                        && !mNavigationBar.isShow(R.string.nb_ratio1)) {
                    *//*曲线变速，播放以后不用隐藏下面的选项页
                 * Curve speed change, do not hide the following option page after playing.
                 * **//*
                 *//*if (mBottomViewContainer.getShowView() instanceof EditMaskView) {
                        ((EditMaskView) mBottomViewContainer.getShowView()).getListener().onDismiss(false);
                    }*//*
                }*/
                if (mTimeline != null) {
                    if (timestamp >= mTimeline.getDuration()) {
                        timestamp = mTimeline.getDuration() - ONE_FRAME;
                    }
                }
                if (!mEditorEngine.isPlaying()) {
                    mVideoFragment.seekTimeline(timestamp, smartKeying() ? STREAMING_ENGINE_COMPILE_FLAG_BUDDY_HOST_VIDEO_FRAME : 0);
                } else if (fromUser) {
                    mVideoFragment.stopEngine();
                    mVideoFragment.seekTimeline(timestamp, smartKeying() ? STREAMING_ENGINE_COMPILE_FLAG_BUDDY_HOST_VIDEO_FRAME : 0);
                }
                if (mSeekFlag == FLAG_SEQ) {
                    /*必须要用smooth，因为滚动的时候调用scrollTo是无效的
                     * You must use smooth, because it is invalid to call scrollTo when scrolling.
                     * **/
                    mEditorTrackView.smoothScrollToByMainTrack(newX);
                    //mMaterialTrack.scrollTo(newX);
                } else if (mSeekFlag == FLAG_PLAY) {
                    /*播放的时候不要调用smooth，因为子轨道可能会抖动
                     * Don't call smooth when playing, because the sub-track may shake.
                     * **/
                    mEditorTrackView.scrollToByMainTrack(newX);
                } else {
                    /*缩放的时候可能会走这里，因为双指操作mSeekFlag不一定准确
                     * You may go here when zooming, because the double-finger operation mSeekFlag is not necessarily accurate.
                     * **/
                    if (newX != mEditorTrackView.getHorizontalScrollX()) {
                        mEditorTrackView.smoothScrollToByMainTrack(newX);
                    }
                }
                mEditOperationView.setDurationText(FormatUtils.microsecond2Time(timestamp) + "/" + FormatUtils.microsecond2Time(mTimeline.getDuration()));
                ITrackClip mainSelectedClip = mEditTimeline.getMainSelectedClip();
                boolean isCurveEdit = mBottomViewContainer.getShowView() instanceof EditKeyFrameCurveView;
                if (mainSelectedClip != null) {
                    boolean hasNoKeyframe = isNoKeyframe("");
                    if (!isCurveEdit && !hasNoKeyframe) {
                        updateKeyFrameStatus();
                    } else {
                        notShowKeyFrameView();
                    }
                } else if (mEditorTrackView.hasDragView()) {
                    BaseItemView dragView = mEditorTrackView.getDragView();
                    BaseUIClip baseUIClip = dragView.getBaseUIClip();
                    if (baseUIClip != null && timestamp >= baseUIClip.getInPoint() && timestamp <= baseUIClip.getOutPoint()) {
                        dragView.checkKeyFrame(timestamp);
                        /*
                         * 特效没有关键帧，不需要显示添加按钮
                         * Special effects have no keyframes and do not need to display the Add button.
                         * */
                        boolean hasNoKeyframe = isNoKeyframe(baseUIClip.getType());
                        if (!isCurveEdit && !hasNoKeyframe) {
                            updateKeyFrameStatus();
                        } else {
                            notShowKeyFrameView();
                        }
                    } else {
                        notShowKeyFrameView();
                    }
                    mVideoFragment.checkOperationBoxVisible();
                    mVideoFragment.checkVideoClipOperationBoxVisible();
                } else {
                    notShowKeyFrameView();
                }
                if (mPresenter.isAddTitleTheme()) {
                    mVideoFragment.hideOperationBox();
                }
                View showView = mBottomViewContainer.getShowView();
                if (showView instanceof EditMaskView) {
                    if (!CommonData.SUPPORT_MASK_KEY_FRAME_CURVE) {
                        mEditOperationView.notShowKeyFrameCurveView();
                    }
                }
            }


            @Override
            public boolean onThumbnailMove(int from, int to) {
                LogUtils.d("Move clip " + "from=" + from + ",to=" + to);
                boolean success = mPresenter.dealWithVideoClipMove(from, to);
                //交换相当于删除from，插入to,影响to的转场，to前面的转场和from前面的转场
                //Exchange is equivalent to deleting from, inserting to, affecting the transition of to,
                // the transition in front of to and the transition in front of from.
                if (from > to) {
                    //向前移动了
                    //Moving forward
                    mPresenter.updateTransitionByVideoClipDuration(from);
                } else {
                    mPresenter.updateTransitionByVideoClipDuration(from - 1);
                }
                mPresenter.updateTransitionByVideoClipDuration(to);
                mPresenter.updateTransitionByVideoClipDuration(to - 1);
                switchToMainMenu();
              /*  if (mNavigationBar.isShow(R.string.nb_main0)) {
                    mPresenter.getMainTrackLineRegion();
                }*/
                if (mEditorTrackView.isShowTrackView()) {
                    updateStickerTrack();
                }
                saveOperation();
                return success;
            }

            @Override
            public void onSelectedChanged(ITrackClip trackClip) {
                LogUtils.d("onSelectedChanged");
                int clipIndexByTime = 0;
                if (trackClip != null) {
                    clipIndexByTime = trackClip.getIndexInTrack();
                }
                MeicamVideoClip videoClip = mEditorEngine.getVideoClip(TRACK_INDEX_MAIN, clipIndexByTime);
                if (videoClip != null) {
                    mCurSelectVideoClip = videoClip;
                    mCurrentInPoint = mCurSelectVideoClip.getInPoint();
                    mCurrentTrackIndex = 0;
                }
                mBottomViewHelper.updateAnimationView(mCurSelectVideoClip);
                mVideoFragment.updateTransformFx(mCurSelectVideoClip, true);
            }
        });

        mEditTimeline.setCoverClickListener(new MYEditorTimeLine.OnCoverClickListener() {
            @Override
            public void onCoverClicked() {
                if (Utils.isFastClick()) {
                    return;
                }
                Bundle bundle = new Bundle();
                bundle.putInt(MEDIA_TYPE, MediaData.TYPE_PHOTO);
                ArrayList<String> filter = new ArrayList<>();
                filter.add(MediaData.TYPE_FILTER_GIF);
                bundle.putStringArrayList(MEDIA_FILTER, filter);
                AppManager.getInstance().jumpActivityForResult(DraftEditActivity.this,
                        CoverEditActivity.class, bundle, true, REQUEST_SELECT_IMAGE_COVER_CODE);
            }
        });

        //声音录制
        //Audio Recorder
        if (mAudioRecordManager != null) {
            mAudioRecordManager.registerConvertFileObserver(audioRecordObserver);
        }
        //字幕内容变化
        //Changes in Subtitle content
        mMultiBottomView.setMultiBottomEventListener(new MYMultiBottomView.MultiBottomEventListener() {
            @Override
            public void onEditTextChange(String text) {
                if (mVideoFragment.getEditFx() instanceof MeicamCaptionClip) {
                    mEditorEngine.changeCaptionText((MeicamCaptionClip) mVideoFragment.getEditFx(), text);
                } else {
                    mEditorEngine.changeCaptionText(null, text);
                }

            }

            @Override
            public void onConfirm(int type) {
                if (type == TYPE_MENU_WATER_MARK) {
                    mVideoFragment.resetFxEditMode();
                } else if (type == TYPE_MENU_CAPTION) {
                    if (mCurrSelectedCaptionStickClip instanceof MeicamCaptionClip) {
                        String text = ((MeicamCaptionClip) mCurrSelectedCaptionStickClip).getText();
                        if (TextUtils.isEmpty(text)) {
                            mEditorEngine.removeCaption((MeicamCaptionClip) mCurrSelectedCaptionStickClip);
                            mNavigationBar.show(R.string.nb_sticker1);
                        } else {
                            mNavigationBar.show(R.string.nb_caption2);
                        }
                        mVideoFragment.resetFxEditMode();
                        saveOperation();
                        updateStickerTrack();
                        if (mCurrSelectedCaptionStickClip != null) {
                            mEditorTrackView.setSelect(mCurrSelectedCaptionStickClip.getTrackIndex(), mCurrSelectedCaptionStickClip.getInPoint());
                        }
                        mEditTimeline.setCaptionRegion(mPresenter.getCaptionStickerLineRegion(CommonData.CLIP_CAPTION));
                    }
                } else if (type == TYPE_MENU_STICKER) {
                    mVideoFragment.resetFxEditMode();
                    updateStickerTrack();
                    mEditOperationView.updateCancelRecoverVisible(true);
                } else if (type == TYPE_MENU_TRANSITION) {
                    saveOperation();
                } else if (type == TYPE_MENU_EFFECT) {
                    if (mCurrSelectedVideoFx != null) {
                        seekViewOnPlay(mCurrSelectedVideoFx.getInPoint());
                        mVideoFragment.seekTimeline(mCurrSelectedVideoFx.getInPoint(), 0);
                        if (mNavigationBar.isShow(R.string.nb_effect2) || mNavigationBar.isShow(R.string.nb_effect1)) {
                            updateTimelineFxTrack();
                            mEditorTrackView.setSelect(mCurrSelectedVideoFx.getTrackIndex(), mCurrSelectedVideoFx.getInPoint());
                        }
                    }
                    mEditOperationView.updateCancelRecoverVisible(true);
                } else if (type == TYPE_MENU_PROP) {
                    saveOperation();
                } else if (type == TYPE_MENU_VOLUME_FADE) {
                    saveOperation();
                }
            }

            @Override
            public void onApplyToAll(Fragment fragment) {
                if (fragment instanceof TransitionFragment) {
                    TransitionFragment transitionFragment = (TransitionFragment) fragment;
                    if (transitionFragment.applyTransitionToAll()) {
                        ToastUtils.showShort(R.string.has_been_apply_to_all);
                    }
                }
            }

            @Override
            public void onErasure(int type) {
                //Remove the special effects
                if (type == TYPE_MENU_EFFECT) {
                    BaseUIClip baseUIClip = null;
                    if (mCurrSelectedVideoFx != null) {
                        baseUIClip = new BaseUIVideoClip(mCurrSelectedVideoFx.getIndex());
                        baseUIClip.setInPoint(mCurrSelectedVideoFx.getInPoint());
                    }
                    mEditorEngine.deleteEffect(baseUIClip);
                    mEditorTrackView.clickOutSide();
                    if (mNavigationBar.isShow(R.string.nb_sticker2)) {
                        mNavigationBar.show(R.string.nb_sticker1);
                    }
                    if (mMultiBottomView.getSelectedFragment() instanceof EffectFragment) {
                        ((EffectFragment) mMultiBottomView.getSelectedFragment()).setSelected(-1);
                    }
                    mCurrSelectedVideoFx = null;
                } else if (type == TYPE_MENU_PROP) {
                    EditorEngine.getInstance().deleteProp(mCurSelectVideoClip);
                    EditorEngine.getInstance().seekTimeline();
                    saveOperation();
                    if (mainTrackSelected()) {
                        ITrackClip mainSelectedClip = mEditTimeline.getMainSelectedClip();
                        if (mainSelectedClip != null) {
                            mainSelectedClip.setHasProp(false);
                            mEditTimeline.changeMainTrackClipProp();
                        }
                    } else {
                        mEditorTrackView.showPipTrackView(mTimeline);
                    }
                    Fragment selectedFragment = mMultiBottomView.getSelectedFragment();
                    if (selectedFragment instanceof PropFragment) {
                        ((PropFragment) selectedFragment).setSelected(-1);
                    }
                }
            }

            @Override
            public void onFragmentSelected(Fragment fragment, int type) {
                if (type == TYPE_MENU_TRANSITION) {
                    if (fragment instanceof TransitionFragment) {
                        TransitionFragment transitionFragment = (TransitionFragment) fragment;
                        ThumbnailClip.TailInfo tailInfo = mEditTimeline.findThumbnailTailInfo(transitionFragment.getTransitionIndex());
                        transitionFragment.selected(tailInfo.getId());
                    }
                } else if (type == TYPE_MENU_WATER_MARK) {
                    if (fragment instanceof WaterFragment) {
                        mVideoFragment.openFxEditMode(EDIT_MODE_WATERMARK, null, true);
                    } else if (fragment instanceof WaterEffectFragment) {
                        // updateViewAboutEffect();
                        mVideoFragment.openFxEditMode(EDIT_MODE_WATERMARK_EFFECT, null, true);
                    }
                } else if (type == TYPE_MENU_CAPTION) {
                    mMultiHelper.updateCaptionView(mCurrSelectedCaptionStickClip);
                } else if (type == TYPE_MENU_EFFECT) {
                    if (fragment instanceof EffectFragment) {
                        ((EffectFragment) fragment).updateSelected(mCurrSelectedVideoFx);
                    }
                }
            }
        });
        mVideoFragment.setTouchEventListener(new VideoFragment.TouchEventListener() {
            @Override
            public void onLiveWindowClick(int editMode) {
                clickOutSide();
            }

            @Override
            public void onClickBoxOutside(int editMode) {
                LogUtils.d("BarName = " + getString(mNavigationBar.getShowingNavigationName()) + ",editMode=" + editMode);
                if (mNavigationBar.isShow(R.string.nb_watermark1) || editMode == EDIT_MODE_CAPTION
                        || editMode == EDIT_MODE_COMPOUND_CAPTION || editMode == EDIT_MODE_STICKER) {
                    return;
                }
                mEditorTrackView.clickOutSide();
                clickOutSide();
            }

            @Override
            public void onClickBox(int index, int editMode) {
                if (editMode == EDIT_MODE_COMPOUND_CAPTION) {
                    if (!(mVideoFragment.getEditFx() instanceof MeicamCompoundCaptionClip)) {
                        LogUtils.e("the edit fx is not NvsTrackCompoundCaption");
                        return;
                    }
                    int captionCount = ((MeicamCompoundCaptionClip) mVideoFragment.getEditFx()).getCaptionItemCount();
                    if (index < 0 || index >= captionCount) {
                        LogUtils.e("the NvsTrackCompoundCaption is error! captionIndex: " + index + "  captionCount: " + captionCount);
                        return;
                    }
                    if (!(mCurrSelectedCaptionStickClip instanceof MeicamCompoundCaptionClip)) {
                        return;
                    }
                    final MeicamCompoundCaptionClip meicamCompoundCaptionClip = (MeicamCompoundCaptionClip) mCurrSelectedCaptionStickClip;
                    meicamCompoundCaptionClip.setItemSelectedIndex(index);
                    if (mBottomViewContainer.getShowView() instanceof MYCompoundCaptionEditView) {
                        //如果当前显示编辑view就直接更换内容，不用再消失显示了
                        //If the current display edit view directly change the content, no longer disappear display
                        ((MYCompoundCaptionEditView) mBottomViewContainer.getShowView()).setData(meicamCompoundCaptionClip, null, -1);
                        return;
                    }
                    mBottomViewHelper.showCompoundCaptionEdit(meicamCompoundCaptionClip, mKeyboardHeight,
                            new MYCompoundCaptionEditView.CompoundCaptionListener() {
                                @Override
                                public void onDismiss(boolean confirm) {
                                    if (confirm) {
                                        mVideoFragment.resetFxEditMode();
                                        HashMap<Integer, List<BaseUIClip>> hashMap = TrackViewDataHelper.getInstance().getTrackData(CommonData.CLIP_CAPTION);
                                        int trackIndex = meicamCompoundCaptionClip.getTrackIndex();
                                        List<BaseUIClip> list = hashMap.get(trackIndex);
                                        if (list != null) {
                                            for (int i = 0; i < list.size(); i++) {
                                                BaseUIClip baseUIClip = list.get(i);
                                                if (baseUIClip.getInPoint() == meicamCompoundCaptionClip.getInPoint()) {
                                                    baseUIClip.setDisplayName(meicamCompoundCaptionClip.getText(index));
                                                    break;
                                                }
                                            }
                                        }

                                        mEditorTrackView.setData(hashMap,
                                                mEditorEngine.getCurrentTimeline().getDuration(), CommonData.CLIP_CAPTION);
                                        mNavigationBar.show(R.string.nb_sticker1);
                                    }
                                    mBottomViewContainer.dismissView();
                                }
                            });
                }
            }

            @Override
            public void onDoubleClickBox(final int index, int editMode) {
                if (editMode == EDIT_MODE_CAPTION) {
                    if (mVideoFragment.operationBoxIsVisible()) {
                        hideBottomView();
                        showCaptionView();
                    }
                }
            }

            @Override
            public void onTouchBoxUp(PointF pointF, int editMode, boolean needSaveOperation) {
                super.onTouchBoxUp(pointF, editMode, needSaveOperation);
                if (needSaveOperation) {
                    saveOperation();
                }
            }
        });

        mVideoFragment.setColorPickerChangedListener(new VideoFragment.ColorPickerChangedListener() {

            @Override
            public void onColorChanged(int a, int r, int g, int b, FloatPoint colorPosition) {
                mPresenter.changeColorPickerParam(mCurSelectVideoClip, NvsConstants.MasterKeyer.KEY_COLOR, new NvsColor(r / 255F, g / 255F, b / 255F, a / 255F), colorPosition);
            }
        });

        mVideoFragment.setFxEditListener(new VideoFragment.FxEditListener() {

            @Override
            public void onDelete(int editMode) {
                if (editMode == EDIT_MODE_CAPTION || editMode == EDIT_MODE_STICKER || editMode == EDIT_MODE_COMPOUND_CAPTION) {
                    deleteCaptionSicker();
                } else if (editMode == EDIT_MODE_WATERMARK || editMode == EDIT_MODE_WATERMARK_EFFECT) {
                    mBottomViewHelper.unselectedAboutWatermark();
                }
            }


            @Override
            public void onCheckSelected(PointF pointF, boolean isVideoClip) {
                LogUtils.d("showNavigationName=" + getString(mNavigationBar.getShowingNavigationName()));
                if (isVideoClip) {
                    for (int i = mTimeline.videoTrackCount() - 1; i >= 0; i--) {
                        MeicamVideoTrack meicamVideoTrack = mTimeline.getVideoTrack(i);
                        if (meicamVideoTrack == null) {
                            continue;
                        }
                        MeicamVideoClip meicamVideoClip = meicamVideoTrack.getClipByTimelinePosition(mPresenter.getTimelineCurrentPosition());
                        if (meicamVideoClip == null) {
                            continue;
                        }
                        mVideoFragment.updateTransformFx(meicamVideoClip, true);
                        boolean b = mVideoFragment.insideVideoClipOperationBox((int) pointF.x, (int) pointF.y);
                        if (mEditorTrackView.isShowPipTrackView()) {
                            if (b) {
                                if (meicamVideoTrack.getIndex() == MAIN_TRACK_INDEX) {
                                    mEditTimeline.showSpanView(mPresenter.getTimelineCurrentPosition(), true);
                                } else {

                                    mEditorTrackView.setSelect(meicamVideoTrack.getIndex() - 1, meicamVideoClip.getInPoint());

                                }
                                break;
                            }
                        } else {

                            if (meicamVideoTrack.getIndex() == MAIN_TRACK_INDEX) {
                                if (mNavigationBar.getShowingNavigationName() == R.string.nb_animate2) {
                                    mEditorTrackView.clickOutSide();
                                    mEditTimeline.enableThumbnailCoverEditMode(true);
                                    mEditTimeline.enableThumbnailAnimation(true);
                                    mEditTimeline.notShowSpanView();
                                } else {
                                    mEditTimeline.showSpanView(mPresenter.getTimelineCurrentPosition(), true);
                                }
                                break;
                            }
                        }
                    }
                    return;
                }

                if (mNavigationBar.isShow(R.string.nb_sticker1) || mNavigationBar.isShow(R.string.nb_sticker2)
                        || mNavigationBar.isShow(R.string.nb_caption2)
                        || mNavigationBar.isShow(R.string.nb_combination_caption2)) {
                    //贴纸/字幕/组合字幕一级导航栏
                    //Sticker/subtitle/combined subtitle navigation bar
                    List<ClipInfo<?>> clipInfos = mEditorEngine.findAllCaptionStickByTimelinePosition(mPresenter.getTimelineCurrentPosition());
                    if (!CommonUtils.isEmpty(clipInfos)) {
                        for (ClipInfo<?> clipInfo : clipInfos) {
                            List<PointF> boundingRectangleVertices = null;
                            if (clipInfo instanceof MeicamCaptionClip) {
                                boundingRectangleVertices = ((MeicamCaptionClip) clipInfo).getBoundingRectangleVertices();
                            } else if (clipInfo instanceof MeicamStickerClip) {
                                boundingRectangleVertices = ((MeicamStickerClip) clipInfo).getBoundingRectangleVertices();
                            } else if (clipInfo instanceof MeicamCompoundCaptionClip) {
                                boundingRectangleVertices = ((MeicamCompoundCaptionClip) clipInfo).getCompoundBoundingVertices(NvsTrackCompoundCaption.BOUNDING_TYPE_TEXT_FRAME);
                            }
                            boolean b = mVideoFragment.insideOperationBox(boundingRectangleVertices, (int) pointF.x, (int) pointF.y);
                            if (b) {
                                mCurrSelectedCaptionStickClip = clipInfo;

                                if (mCurrSelectedCaptionStickClip != null && !mCurrSelectedCaptionStickClip.equals(mVideoFragment.getEditFx())) {
                                    updateStickerTrack();
                                    if (mCurrSelectedCaptionStickClip instanceof MeicamCaptionClip) {
                                        if (mMultiBottomView.isShow() && mMultiBottomView.getType() == TYPE_MENU_CAPTION) {
                                            mMultiHelper.updateCaptionView(mCurrSelectedCaptionStickClip);
                                            if (mMultiBottomView.getSelectedFragment() instanceof CaptionBubbleFlowerFragment) {
                                                CaptionBubbleFlowerFragment captionBubbleFlowerFragment = (CaptionBubbleFlowerFragment) mMultiBottomView.getSelectedFragment();
                                                captionBubbleFlowerFragment.setSelected();
                                            }
                                            if (mMultiBottomView.getSelectedFragment() instanceof CaptionAnimationFragment) {
                                                CaptionAnimationFragment captionAnimationFragment = (CaptionAnimationFragment) mMultiBottomView.getSelectedFragment();
                                                captionAnimationFragment.updateSelectedAnimation();
                                            }
                                        } else {
                                            if (mMultiBottomView.isShow()) {
                                                mMultiBottomView.hide();
                                            }
                                            mBottomViewContainer.dismissAll();
                                        }
                                        mVideoFragment.openFxEditMode(EDIT_MODE_CAPTION, mCurrSelectedCaptionStickClip, true);
                                    } else if (mCurrSelectedCaptionStickClip instanceof MeicamStickerClip) {
                                        mVideoFragment.openFxEditMode(EDIT_MODE_STICKER, mCurrSelectedCaptionStickClip, true);
                                        mBottomViewContainer.dismissAll();
                                        Fragment showFragment = mBottomViewHelper.getView().getShowFragment();
                                        if (showFragment instanceof StickerAnimationFragment) {
                                            ((StickerAnimationFragment) showFragment).updateClip((MeicamStickerClip) mCurrSelectedCaptionStickClip);
                                        }
                                        showFragment = mMultiBottomView.getSelectedFragment();
                                        if (showFragment instanceof StickerAllFragment) {
                                            ((StickerAllFragment) showFragment).updateSelectId(((MeicamStickerClip) mCurrSelectedCaptionStickClip).getPackageId());
                                        }

                                        if (mMultiBottomView.isShow() && mMultiBottomView.getType() == TYPE_MENU_STICKER) {
                                            return;
                                        }
                                        if (mMultiBottomView.isShow()) {
                                            mMultiBottomView.hide();
                                        }

                                    } else if (mCurrSelectedCaptionStickClip instanceof MeicamCompoundCaptionClip) {
                                        if (mMultiBottomView.isShow()) {
                                            mMultiBottomView.hide();
                                        }
                                        mBottomViewContainer.dismissAll();
                                        mVideoFragment.openFxEditMode(EDIT_MODE_COMPOUND_CAPTION, mCurrSelectedCaptionStickClip, true);
                                    }
                                    mEditorTrackView.setSelect(mCurrSelectedCaptionStickClip.getTrackIndex(), mCurrSelectedCaptionStickClip.getInPoint());
                                }
                                break;
                            }
                        }
                    }

                } else if (mNavigationBar.isShow(R.string.nb_main0)) {
                    //主菜单
                    //Main menu
                    long timelineCurrentPosition = mPresenter.getTimelineCurrentPosition();
                    boolean addTitleTheme = mPresenter.isAddTitleTheme();
                    long titleThemeDuration = mPresenter.getTitleThemeDuration();
                    if (addTitleTheme && timelineCurrentPosition < titleThemeDuration) {
                        List<ClipInfo<?>> captionsByTimelinePosition = mEditorEngine.getCaptionsByTimelinePosition(timelineCurrentPosition);
                        if (captionsByTimelinePosition != null && captionsByTimelinePosition.size() > 0) {
                            MeicamCaptionClip timelineCaption = (MeicamCaptionClip) captionsByTimelinePosition.get(0);
                            //对这个字幕进行判断
                            if (timelineCaption.getThemeType() == NvsTimelineCaption.THEME_CATEGORY) {
                                mVideoFragment.openFxEditMode(EDIT_MODE_CAPTION, timelineCaption, true);
                            }
                        }

                    }

                }
            }

            //平移
            //Translate
            @Override
            public void onTranslate(int editMode) {

            }

            //旋转和缩放
            //Rotation and scale
            @Override
            public void onRotationAndScale(int editMode) {
            }

            @Override
            public void onChanged(int quadrant, int editMode) {

            }

            @Override
            public void onFxEditEnd(int editMode) {
                if (editMode == EDIT_MODE_CAPTION || editMode == EDIT_MODE_STICKER) {
                    NvsObject<?> editFx = mVideoFragment.getEditFx();
                    if (editFx != null) {
                        ClipInfo<?> clipInfo = (ClipInfo<?>) editFx;
                        BaseItemView dragView = mEditorTrackView.getDragView();
                        if (dragView != null) {
                            KeyFrameInfo keyFrameInfo = dragView.getBaseUIClip().getKeyFrameInfo();
                            mPresenter.updateOrAddKeyFrame((IKeyFrameProcessor<?>) clipInfo, keyFrameInfo, mVideoFragment, false);
                            //saveOperation();
                        }
                    }
                } else if (editMode == EDIT_MODE_VIDEO_CLIP) {
                    if (mCurSelectVideoClip != null) {
                        MeicamVideoFx propertyVideoFx = mCurSelectVideoClip.findPropertyVideoFx();
                        if (propertyVideoFx != null && propertyVideoFx.keyFrameProcessor().getKeyFrameCount() > 0) {
                            /*如果有关键帧，自动添加或者修改
                             * If there are keys, add or modify them automatically
                             * */
                            KeyFrameInfo keyFrameInfo = null;
                            ITrackClip mainSelectedClip = mEditTimeline.getMainSelectedClip();
                            if (mainSelectedClip != null) {
                                keyFrameInfo = mainSelectedClip.getKeyFrameInfo();
                            } else if (mEditorTrackView.getDragView() != null) {
                                BaseUIClip baseUIClip = mEditorTrackView.getDragView().getBaseUIClip();
                                if (baseUIClip != null) {
                                    keyFrameInfo = baseUIClip.getKeyFrameInfo();
                                }
                            }
                            mPresenter.updateOrAddKeyFrame(propertyVideoFx, keyFrameInfo, mVideoFragment, mainSelectedClip != null);
                        }
                    }
                    saveOperation();
                } else if (editMode == EDIT_MODE_MASK) {
                    MeicamVideoFx maskTargetFx = EditorEngine.getInstance().getMaskTargetFx(mCurSelectVideoClip);
                    if (maskTargetFx != null && maskTargetFx.keyFrameProcessor().getKeyFrameCount(NvsConstants.KEY_PROPERTY_MASK_REGION_INFO) > 0) {
                        /*如果有关键帧，自动添加或者修改
                         * If there are keys, add or modify them automatically
                         * */
                        KeyFrameInfo keyFrameInfo = null;
                        ITrackClip mainSelectedClip = mEditTimeline.getMainSelectedClip();
                        if (mainSelectedClip != null) {
                            keyFrameInfo = mainSelectedClip.getKeyFrameInfo();
                        } else if (mEditorTrackView.getDragView() != null) {
                            BaseUIClip baseUIClip = mEditorTrackView.getDragView().getBaseUIClip();
                            if (baseUIClip != null) {
                                keyFrameInfo = baseUIClip.getKeyFrameInfo();
                            }
                        }
                        mPresenter.updateOrAddKeyFrame(maskTargetFx, keyFrameInfo, mVideoFragment, mainSelectedClip != null);
                    }
                    saveOperation();
                }
            }
        });

        mRlCompileProgress.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View view, MotionEvent motionEvent) {
                return true;
            }
        });
        mEditOperateManager.registerOperateObserver(mEditOperateObserver);
        mEditorEngine.setOnTrackChangeListener(mOnTrackChangedListener = new EditorEngine.OnTrackChangeListener() {
            @Override
            public void audioEditCutClip(MeicamAudioTrack nvsAudioTrack, long inPoint) {
                refreshAudioView();
                if (nvsAudioTrack != null) {
                    mEditorTrackView.setSelect(nvsAudioTrack.getIndex(), inPoint);
                }
                saveOperation();
            }

            @Override
            public void audioEditDeleteClip(boolean success) {
                if (success) {
                    refreshAudioView();
                    if (mTrackListHashMap.size() == 0) {
                        mTimeline.restoreThemeVolume();
                    }
                    saveOperation();
                }
                mPresenter.checkTrackDuration();
                if (mNavigationBar.isShow(R.string.nb_audio2)) {
                    mNavigationBar.show(R.string.nb_audio1);
                }
            }

            @Override
            public void audioEditCopyClip(long inPoint, MeicamAudioClip newAudioClip, MeicamAudioTrack nvsAudioTrack) {
                //MeicamAudioClip audioClip = mPresenter.createAudioClip(inPoint, mCurrSelectedAudioClip);
                if (newAudioClip != null) {
                    //TimelineDataUtil.addAudioClipInfoByTrackIndex(nvsAudioTrack, newAudioClip);
                    refreshAudioView();
                    if (nvsAudioTrack != null) {
                        mEditorTrackView.setSelect(nvsAudioTrack.getIndex(), inPoint);
                    }
                    mSeekFlag = FLAG_TRACK_VIEW;
                    saveOperation();
                }
                //onTimelineDurationChange(true);
                mPresenter.checkTrackDuration();
            }

        });
    }

    private void notShowKeyFrameView() {
        if (showAtomicEditMenuViewKeyFrame()) {
            mEditOperationView.notShowKeyFrameView();
            AtomicEditMenuView view = (AtomicEditMenuView) mBottomViewHelper.getView().getShowView();
            view.notShowKeyFrameView();
        } else {
            mEditOperationView.notShowKeyFrameView();
        }
    }

    /**
     * Whether is no keyframe.
     * 是否不显示关键帧按钮
     *
     * @param clipType clip类型 the clipType
     * @return Whether is no keyframe.true: yes; false:no
     */
    private boolean isNoKeyframe(String clipType) {
        return CommonData.CLIP_TIMELINE_FX.equals(clipType) && mCurrSelectedVideoFx != null && (!mCurrSelectedVideoFx.isBuildFx())
                || CommonData.CLIP_AUDIO.equals(clipType)
                || CommonData.CLIP_FILTER.equals(clipType)
                || CommonData.CLIP_ADJUST.equals(clipType)
                || CommonData.CLIP_COMPOUND_CAPTION.equals(clipType)
                || mBottomViewContainer.getShowView() instanceof MYSpeedCurveMenu
                || mBottomViewContainer.getShowView() instanceof EditChangeSpeedView
                || mBottomViewContainer.getShowView() instanceof EditChangeVoiceView
                || (mBottomViewContainer.getShowView() instanceof AdjustSeekBarView && !StringUtils.getString(R.string.sub_menu_name_edit_opacity).equals(((AdjustSeekBarView) mBottomViewContainer.getShowView()).getType()));
    }

    private final AudioRecordObserver audioRecordObserver = new AudioRecordObserver() {
        AudioClipProxy baseUIClip = null;

        @Override
        public void onRecordStart(Long id, String filePath) {
            if (!isActive()) {
                return;
            }
            long recordInPoint = mPresenter.getTimelineCurrentPosition();  //获取当前的位置
            int trackIndex = mPresenter.getNextAudioClipTrackIndex(recordInPoint);
            if (trackIndex >= CommonData.MAX_AUDIO_COUNT) {
                ToastUtils.showShort(String.format(StringUtils.getString(R.string.audio_max_track), CommonData.MAX_AUDIO_COUNT));
                return;
            }
            //对片头主题 进行特殊处理，字幕不能加在片头主题片段上
            //Special treatment to the opening theme, subtitle can not be added to the opening theme segment
            long titleThemeDuration = mPresenter.getTitleThemeDuration();
            if (titleThemeDuration > 0) {
                if (recordInPoint < titleThemeDuration) {
                    recordInPoint = titleThemeDuration;
                }
            }
            //ADD 增加录制View的添加以及音乐波形图
            //Add recording View and music waveform
            baseUIClip = new AudioClipProxy(null, trackIndex);
            baseUIClip.setFilePath(filePath);
            baseUIClip.setInPoint(recordInPoint);
            baseUIClip.setTrimIn(0);
            mTrackListHashMap = TrackViewDataHelper.getInstance().addAudioBaseUIClip(mTrackListHashMap, baseUIClip, mTimeline);
            mEditorTrackView.setTrackViewLayoutData(mTrackListHashMap, mTimeline.getDuration(), CommonData.CLIP_AUDIO);
            /* baseUIClip = TrackViewDataHelper.getInstance().getBaseUIClip(mTrackListHashMap, mRecordAudioInfo.getTrackIndex(), mRecordAudioInfo.getInPoint());*/
            mEditorTrackView.setSelect(baseUIClip);
            //按下的时候先补黑，补两分钟
            //When pressing, fill in the black for two minutes
            mPresenter.dealRecordTimelineDurationChanged();
            refreshEditorTimelineView(-1);
        }

        @Override
        public void onRecordProgress(float[] wave, int during, String path) {
            if (!isActive()) {
                return;
            }
            if (during != 0 && baseUIClip != null) {
                long moveTimeline = baseUIClip.getInPoint() + during * 1000L;
                if (moveTimeline > mTimeline.getDuration()) {
                    if (mBottomViewContainer.getShowView() instanceof MYRecordMenuView) {
                        ((MYRecordMenuView) mBottomViewContainer.getShowView()).stopRecord();
                    }
                    return;
                }
                int move = PixelPerMicrosecondUtil.durationToLength(moveTimeline);
                mSeekFlag = FLAG_TRACK_VIEW;
                mEditorTrackView.smoothScrollView(move);
                baseUIClip.setDuration(during);
                baseUIClip.setRecordArray(wave);
                baseUIClip.setTrimOut(mPresenter.getTimelineCurrentPosition() - baseUIClip.getInPoint());
                mEditorTrackView.refreshAudioSelectView(baseUIClip);
            }
        }

        @Override
        public void onRecordFail(String msg) {
            if (!isActive()) {
                return;
            }
            Log.e(TAG, "onRecordFail==" + msg);
            refreshAudioView();
        }

        @Override
        public void onRecordEnd() {
            if (!isActive()) {
                return;
            }
            //只有当正常录制完成才会进行绘制view，播放完成不进行绘制
            // Draw view only when normal recording is completed, and do not draw after playback.
            if (!PermissionUtils.isGranted(android.Manifest.permission.RECORD_AUDIO)) {
                return;
            }
            if (mBottomViewContainer.getShowView() instanceof MYRecordMenuView) {
                mBottomViewContainer.dismissView();
                mVideoFragment.stopEngine();
                if (baseUIClip != null) {
                    long curPoint = mPresenter.getTimelineCurrentPosition();
                    String disPlayName = getResources().getString(R.string.audio_num) + (TrackViewDataHelper.getInstance().getDrawText(mTrackListHashMap));
                    MeicamAudioClip meicamAudioClip = mPresenter.addAudioClipToTrack(baseUIClip.getFilePath(),
                            disPlayName, baseUIClip.getInPoint(), 0,
                            curPoint - baseUIClip.getInPoint(), MeicamAudioClip.AUDIO_RECORD_FILE, baseUIClip.getTrackIndex());
                    if (meicamAudioClip != null) {
                        baseUIClip.setAudioClip(meicamAudioClip);
                        baseUIClip.setDuration(meicamAudioClip.getTrimIn() - meicamAudioClip.getTrimOut());
                        mEditorTrackView.setTrackViewLayoutData(mTrackListHashMap, mTimeline.getDuration(), CommonData.CLIP_AUDIO);
                        mEditorTrackView.refreshAudioSelectView(baseUIClip);
                        mEditorTrackView.setSelect(baseUIClip);
                        saveOperation();
                    } else {
                        mTrackListHashMap = TrackViewDataHelper.getInstance().removeAudioBaseUIClip(mTrackListHashMap, baseUIClip);
                    }
                }
                mEditorTrackView.cancleAnimation();
                mPresenter.checkTrackDuration();
            }
        }
    };

    private final ConvertFileObserver convertFileObserver = new ConvertFileObserver() {

        @Override
        public void onConvertProgress(float progress) {
            if (!isActive()) {
                return;
            }
            setCenterProgress((int) progress);
        }

        @Override
        public void onConvertFinish(ConvertFileManager.ConvertParam convertParam, boolean convertSuccess) {
            if (!isActive()) {
                return;
            }
            if (mCurSelectVideoClip == null || convertParam == null) {
                LogUtils.e("onConvertFinish mCurSelectVideoClip  convertParam  == null");
                hideCenterProgress();
                return;
            }
            Map<String, ConvertFileManager.ConvertParam.Param> paramMap = convertParam.getParamMap();
            if (paramMap == null || paramMap.isEmpty()) {
                return;
            }
            if (convertSuccess) {
                ConvertFileManager.ConvertParam.Param param = paramMap.get(mCurSelectVideoClip.getFilePath());
                if (param != null) {
                    VideoClipCommand.setParam(mCurSelectVideoClip, PARAM_REVERSE_FILE_PATH, param.getDstFile());
                    //mCurSelectVideoClip.setReverseFilePath(param.getDstFile());
                }
                MeicamVideoFx videoFx = mCurSelectVideoClip.getVideoFx(SUB_TYPE_ALPHA, SET_ALPHA);
                if (videoFx != null) {
                    /*倒放alpha文件，即智能抠图的视频文件
                     * Invert the alpha file, that is, the intelligent matting video file
                     * */
                    String filePath = videoFx.getStringVal(ALPHA_FILE);
                    if (!TextUtils.isEmpty(filePath)) {
                        param = paramMap.get(filePath);
                        if (param != null) {
                            PathUtils.renameSmartKeyerFileFromTemp(param.getDstFile());
                        }
                    }
                }
                //视频编辑导航栏正在显示
                //Video editing navigation bar is showing
                mEditorEngine.setVideoConvert(mCurSelectVideoClip);
            } else {
                //mCurSelectVideoClip.setReverseFilePath("");
                VideoClipCommand.setParam(mCurSelectVideoClip, PARAM_REVERSE_FILE_PATH, null);
            }
            ToastUtils.showShort(mCurSelectVideoClip.getVideoReverse() ? R.string.revert_finish : R.string.revert_fail);
            //mCurSelectVideoClip.setConvertSuccess(convertSuccess);
            VideoClipCommand.setParam(mCurSelectVideoClip, PARAM_VIDEO_CONVERT_SUCCESS, true);
            hideCenterProgress();
        }

        @Override
        public void onConvertCancel() {
            if (!isActive()) {
                return;
            }
            hideCenterProgress();
        }
    };

    /**
     * 选中字幕，贴纸
     * Selected caption sicker.
     *
     * @param baseUIClip the base ui clip
     */
    public void selectedCaptionSicker(BaseUIClip baseUIClip) {
        if (baseUIClip == null) {
            return;
        }
        ClipInfo<?> clipInfo = mEditorEngine.getCaptionStickerData(baseUIClip.getTrackIndex(), baseUIClip.getInPoint());
        if (clipInfo != null) {
            mCurrSelectedCaptionStickClip = clipInfo;
            if (mCurrSelectedCaptionStickClip instanceof MeicamCaptionClip) {
                mVideoFragment.openFxEditMode(EDIT_MODE_CAPTION, mCurrSelectedCaptionStickClip, true);
            } else if (mCurrSelectedCaptionStickClip instanceof MeicamStickerClip) {
                mVideoFragment.openFxEditMode(EDIT_MODE_STICKER, mCurrSelectedCaptionStickClip, true);
            } else if (mCurrSelectedCaptionStickClip instanceof MeicamCompoundCaptionClip) {
                mVideoFragment.openFxEditMode(EDIT_MODE_COMPOUND_CAPTION, mCurrSelectedCaptionStickClip, true);
            }
        }
    }

    /**
     * 一键铺满baseItem
     * Change base item duration.
     *
     * @param baseUIClip the base ui clip
     */
    private void changeBaseItemDuration(BaseUIClip baseUIClip) {
        if (baseUIClip == null) {
            return;
        }
        String type = baseUIClip.getType();
        ClipInfo<?> clipInfo = null;
        BaseUIClip baseUIClipBefore = mEditorTrackView.getBeforeClip(baseUIClip);
        BaseUIClip baseUIClipNext = mEditorTrackView.getNextClip(baseUIClip);
        long offsetLeft;
        long inPoint, outPoint;
        if (baseUIClipBefore != null) {
            inPoint = baseUIClipBefore.getOutPoint();
        } else {
            inPoint = 0;
        }
        offsetLeft = baseUIClip.getInPoint() - inPoint;
        if (baseUIClipNext != null) {
            outPoint = baseUIClipNext.getInPoint();
        } else {
            outPoint = mEditorEngine.getCurrentTimeline().getDuration();
        }
        if (CommonData.CLIP_STICKER.equals(type) ||
                CommonData.CLIP_CAPTION.equals(type) ||
                CommonData.CLIP_COMPOUND_CAPTION.equals(type)) {
            mCurrSelectedCaptionStickClip = mEditorEngine.getCaptionStickerData(baseUIClip.getTrackIndex(), baseUIClip.getInPoint());
            if (mCurrSelectedCaptionStickClip == null) {
                return;
            }
            clipInfo = mCurrSelectedCaptionStickClip;
            ClipCommand.setInPoint(mCurrSelectedCaptionStickClip, inPoint, true);
            ClipCommand.setOutPoint(mCurrSelectedCaptionStickClip, outPoint, true);
            updateStickerTrack();
        } else if (CommonData.CLIP_TIMELINE_FX.equals(type)) {
            mCurrSelectedVideoFx = mEditorEngine.getTimelineVideoFxClip(baseUIClip.getTrackIndex(), baseUIClip.getInPoint());
            if (mCurrSelectedVideoFx == null) {
                return;
            }
            clipInfo = mCurrSelectedVideoFx;
            ClipCommand.setInPoint(mCurrSelectedVideoFx, inPoint, true);
            ClipCommand.setOutPoint(mCurrSelectedVideoFx, outPoint, true);
            updateTimelineFxTrack();
            mPresenter.updateTimelineFxTargetInAndOutPoint(mCurrSelectedVideoFx);
        } else if (CommonData.CLIP_FILTER.equals(type)) {
            mCurrSelectedFilterAndAdjustClip = mEditorEngine.getTimelineFilterAndAdjustClip(baseUIClip.getTrackIndex(), baseUIClip.getInPoint());
            if (mCurrSelectedFilterAndAdjustClip == null) {
                return;
            }
            mCurrSelectedVideoFx = mCurrSelectedFilterAndAdjustClip.getAdjustTimelineFx(MeicamTimelineVideoFxClip.ClipFxType.SUB_TYPE_TIMELINE_FILTER);
            if (mCurrSelectedVideoFx == null) {
                return;
            }
            clipInfo = mCurrSelectedFilterAndAdjustClip;
            ClipCommand.setInPoint(mCurrSelectedFilterAndAdjustClip, inPoint, true);
            ClipCommand.setOutPoint(mCurrSelectedFilterAndAdjustClip, outPoint, true);
            updateFilterAndAdjustTrack();
        } else if (CommonData.CLIP_ADJUST.equals(type)) {
            mCurrSelectedFilterAndAdjustClip = mEditorEngine.getTimelineFilterAndAdjustClip(baseUIClip.getTrackIndex(), baseUIClip.getInPoint());
            if (mCurrSelectedFilterAndAdjustClip == null) {
                return;
            }
            clipInfo = mCurrSelectedFilterAndAdjustClip;
            ClipCommand.setInPoint(mCurrSelectedFilterAndAdjustClip, inPoint, true);
            ClipCommand.setOutPoint(mCurrSelectedFilterAndAdjustClip, outPoint, true);
            updateFilterAndAdjustTrack();
        }
        baseUIClip.setInPoint(inPoint);
        baseUIClip.setOutPoint(outPoint);
        mEditorTrackView.setSelect(baseUIClip.getTrackIndex(), inPoint, false);
        mEditorEngine.checkKeyFrame(clipInfo, offsetLeft, true);
        BaseItemView dragView = mEditorTrackView.getDragView();
        if (dragView != null) {
            if (clipInfo instanceof IKeyFrameProcessor<?>) {
                if (!(clipInfo instanceof MeicamTimelineVideoFxClip)) {
                    long timestamp = mPresenter.getTimelineCurrentPosition();
                    dragView.updateKeyFrame(mPresenter.getKeyFrameInfo((IKeyFrameProcessor<?>) clipInfo, null), timestamp);
                    updateKeyFrameStatus();
                    mVideoFragment.updateOperationBoxWhenHadKeyFrame(timestamp);
                }
            }
        }
        saveOperation();
    }

    /**
     * 删除滤镜，调节clip
     * Delete filter and adjust clip.
     */
    public void deleteFilterAndAdjustClip() {
        if (mCurrSelectedFilterAndAdjustClip == null) {
            LogUtils.e("mCurrSelectedFilterAndAdjustClip is null;");
            return;
        }
        boolean isSuccess = mEditorEngine.removeFilterAndAdjustTimelineFx(mCurrSelectedFilterAndAdjustClip);
        if (!isSuccess) {
            LogUtils.e("deleteFilterAndAdjustClip fail");
            return;
        }
        mEditorEngine.seekTimeline(0);
        mPresenter.checkTrackDuration();
        mEditOperationView.notShowKeyFrameView();
        mNavigationBar.show(R.string.nb_filter1);
        updateFilterAndAdjustTrack();
        mCurrSelectedFilterAndAdjustClip = null;
        mCurrSelectedVideoFx = null;
        saveOperation();
    }

    /**
     * 删除字幕，贴纸
     * Delete caption sicker.
     */
    public void deleteCaptionSicker() {
        if (mCurrSelectedCaptionStickClip == null) {
            LogUtils.e("mCurrSelectedCaptionStickClip is nu;;");
            return;
        }

        boolean isSuccess = mEditorEngine.removeCaptionStickClip(mCurrSelectedCaptionStickClip);
        if (!isSuccess) {
            LogUtils.e("deleteCaptionSicker fail");
            return;
        }
        //删除尾部的空轨道
        //Delete the empty track at the end
        mEditorEngine.removeEmptyStickerCaptionTrackInTheEnd();
        mEditorEngine.seekTimeline();
        if (mCurrSelectedCaptionStickClip instanceof MeicamCaptionClip) {
            mEditTimeline.updateCaptionRegion(mPresenter.getLineRegionClip(mCurrSelectedCaptionStickClip,
                    mCurrSelectedCaptionStickClip.getTrackIndex(), false), true);
        } else if (mCurrSelectedCaptionStickClip instanceof MeicamStickerClip) {
            mEditTimeline.updateStickerRegion(mPresenter.getLineRegionClip(mCurrSelectedCaptionStickClip,
                    mCurrSelectedCaptionStickClip.getTrackIndex(), false), true);
        } else if (mCurrSelectedCaptionStickClip instanceof MeicamCompoundCaptionClip) {
            mEditTimeline.updateCompoundCaptionRegion(mPresenter.getLineRegionClip(mCurrSelectedCaptionStickClip,
                    mCurrSelectedCaptionStickClip.getTrackIndex(), false), true);
        }
        mPresenter.checkTrackDuration();
        mEditOperationView.notShowKeyFrameView();
        mCurrSelectedCaptionStickClip = null;
        mVideoFragment.resetFxEditMode();
        mNavigationBar.show(R.string.nb_sticker1);
        updateStickerTrack();
        mMultiBottomView.hide();
        mBottomViewContainer.dismissView();
        mBottomViewContainer.dismissFragment();
        mEditOperationView.updateCancelRecoverVisible(true);
        saveOperation();
    }

    /**
     * 复制字幕，贴纸
     * Copy caption sicker.
     */
    public void copyCaptionSicker() {
        mCurrSelectedCaptionStickClip = mEditorEngine.copyCaptionStick(mCurrSelectedCaptionStickClip);
        if (mCurrSelectedCaptionStickClip != null) {
            mEditTimeline.updateStickerRegion(mPresenter.getLineRegionClip(mCurrSelectedCaptionStickClip,
                    mCurrSelectedCaptionStickClip.getTrackIndex(), false), false);
            updateStickerTrack();
            mEditorTrackView.setSelect(mCurrSelectedCaptionStickClip.getTrackIndex(), mCurrSelectedCaptionStickClip.getInPoint());
            mEditorEngine.seekTimeline();
            saveOperation();
        }
    }

    /**
     * 初始化导航栏
     * Init navigation bar
     */
    private void initNavigationBar() {
        //目前最深层为2层级导航栏,如果层级添加，注意更换返回键。
        // At present, the deepest level is the 2-level navigation bar.
        // If the level is added, pay attention to replacing the return key.
        mNavigationBar.setBackButton(R.mipmap.main_menu_ic_back, R.mipmap.main_menu_ic_back)
                .addNavigationList(MenuDataManager.getNavigationList(this))
                //比例一级导航栏
                .addNavigation(R.string.nb_ratio1, R.string.nb_main0, 1)
                .setType(Navigation.TYPE_RATIO)
                .addItem(new Navigation.Item(R.string.ratio_original)
                        .setWidth((int) getResources().getDimension(R.dimen.dp_px_120))
                        .setHeight((int) getResources().getDimension(R.dimen.dp_px_140))
                        .setTag(BaseInfo.AspectRatio_NoFitRatio))
                .addItem(new Navigation.Item(R.string.ratio_9_16)
                        .setWidth((int) getResources().getDimension(R.dimen.dp_px_78))
                        .setHeight((int) getResources().getDimension(R.dimen.dp_px_140))
                        .setTag(BaseInfo.AspectRatio_9v16))
                .addItem(new Navigation.Item(R.string.ratio_3_4)
                        .setWidth((int) getResources().getDimension(R.dimen.dp_px_105))
                        .setHeight((int) getResources().getDimension(R.dimen.dp_px_140))
                        .setTag(BaseInfo.AspectRatio_3v4))
                .addItem(new Navigation.Item(R.string.ratio_1_1)
                        .setWidth((int) getResources().getDimension(R.dimen.dp_px_140))
                        .setHeight((int) getResources().getDimension(R.dimen.dp_px_140))
                        .setTag(BaseInfo.AspectRatio_1v1))
                .addItem(new Navigation.Item(R.string.ratio_4_3)
                        .setWidth((int) getResources().getDimension(R.dimen.dp_px_140))
                        .setHeight((int) getResources().getDimension(R.dimen.dp_px_105))
                        .setTag(BaseInfo.AspectRatio_4v3))
                .addItem(new Navigation.Item(R.string.ratio_16_9)
                        .setWidth((int) getResources().getDimension(R.dimen.dp_px_140))
                        .setHeight((int) getResources().getDimension(R.dimen.dp_px_78))
                        .setTag(BaseInfo.AspectRatio_16v9))
                .build()
                .show();
        //设置比例导航栏的默认选中项
        // Set the default selection of the scale navigation bar.
        setRatioNavigationItem(mEditorEngine.getMakeRatio());
        mNavigationBar.setNavigationListener(new NavigationBar.NavigationListener() {
            @Override
            protected boolean onInterceptItemClick(Navigation.Item item, int navigationName, int preNavigationName) {
                //拦截导航栏的点击事件，一般用于重新指定跳转目标。
                // Intercept the click event of the navigation bar, which is generally used to reassign the jump target.
                if (navigationName == R.string.nb_main0) {
                    if (item.getTitleId() == R.string.main_menu_name_edit) {
                        long timelineCurrentPosition = mPresenter.getTimelineCurrentPosition();
                        mEditTimeline.showSpanView(timelineCurrentPosition);
                        ITrackClip mainSelectedClip = mEditTimeline.getMainSelectedClip();
                        int clipIndexByTime = 0;

                        if (mainSelectedClip != null) {
                            clipIndexByTime = mainSelectedClip.getIndexInTrack();
                            mEditTimeline.checkKeyFrameSelected(timelineCurrentPosition);
                        }
                        MeicamVideoClip videoClip = mEditorEngine.getVideoClip(TRACK_INDEX_MAIN, clipIndexByTime);
                        if (videoClip != null) {
                            mCurSelectVideoClip = videoClip;
                            mCurrentInPoint = mCurSelectVideoClip.getInPoint();
                            mCurrentTrackIndex = 0;
                        }
                        showEditNavigation();
                        updateKeyFrameStatus();
                        mVideoFragment.updateTransformFx(mCurSelectVideoClip, true);
                        if (mCurSelectVideoClip != null && CommonData.CLIP_IMAGE.equals(mCurSelectVideoClip.getVideoType())) {
                            //默认是指向视频编辑导航栏，这里拦截，指向图片编辑导航栏
                            // The default is to point to the video editing navigation bar.
                            // Here, intercept and point to the picture editing navigation bar.
                            mVideoFragment.updateTransformFx(mCurSelectVideoClip, true);
                            mNavigationBar.show(R.string.nb_picture_edit1);
                            return true;
                        }

                    } else if (item.getTitleId() == R.string.main_menu_name_ratio) {
                        MeicamTheme theme = mTimeline.getMeicamTheme();
                        if (theme != null && !TextUtils.isEmpty(theme.getThemePackageId())) {
                            showCommonPop("", getString(R.string.cancel_theme_change_ratio), "", getString(R.string.got_it));
                            return true;
                        }
                    }
                }
                return false;
            }

            @Override
            protected Navigation.Item onNavigationItemClick(Navigation.Item item, int navigationName, int preNavigationName) {
                //返回值item可以重新指定（获取已有的导航栏item），用于导航栏的切换，但是不建议直接修改原来的item，因为可能会造成导航栏跳转逻辑混乱。
                //没有使用switch的原因是，如果用户以本页面作为library，资源id会找不到。
                //The return value item can be reassigned (get the existing navigation bar item) for the navigation bar switch,
                // but it is not recommended to modify the original item directly, because it may cause the navigation bar jump logic confusion.
                // The reason why switch is not used is that if the user uses this page as a library, the resource id will not be found.
                if (navigationName == R.string.nb_main0) {
                    //顶层编辑栏
                    //Top Edit Bar
                    return clickNavigationMain(item);
                } else if (navigationName == R.string.nb_video_edit1 || navigationName == R.string.nb_picture_edit1) {
                    //图片/视频编辑一级导航栏
                    //Picture/video editing navigation bar
                    clickNavigationEdit(item);
                } else if (navigationName == R.string.nb_change_speed2) {
                    //变速二级导航栏
                    //Variable speed secondary navigation bar
                    clickNavigationChangeSpeed(item);
                } else if (navigationName == R.string.nb_animate2) {
                    //动画二级导航栏
                    //Animation secondary navigation bar
                    clickNavigationAnimate(item);
                } else if (navigationName == R.string.nb_filter1) {
                    //滤镜一级导航栏
                    //Filter level navigation bar
                    clickNavigationFilter(item);
                } else if (navigationName == R.string.nb_effect1 || navigationName == R.string.nb_effect2) {
                    //特效一级/编辑特效二级导航栏
                    //Special effects level 1/editing special effects level 2 navigation bar
                    clickNavigationEffect(item);
                } else if (navigationName == R.string.nb_sticker1) {
                    //贴纸/字幕/组合字幕一级导航栏
                    //Sticker/caption/combined caption navigation bar
                    clickNavigationSticker(item);
                } else if (navigationName == R.string.nb_watermark1) {
                    //水印一级导航栏
                    //Watermark Level 1 Navigation Bar
                    clickNavigationWatermark(item);
                } else if (navigationName == R.string.nb_audio1) {
                    //音频一级导航栏
                    //Audio navigation bar
                    clickNavigationAudio(item);
                } else if (navigationName == R.string.nb_audio2) {
                    //音频编辑二级导航栏
                    //Audio editing secondary navigation bar
                    clickNavigationEditAudio(item);
                } else if (navigationName == R.string.nb_pip1) {
                    //画中画一级导航栏
                    //Picture-in-Picture navigation bar
                    if (R.string.sub_pic_in_menu_add_pic == item.getTitleId()) {
                        if (!Utils.isFastClick()) {
                            Bundle bundle = new Bundle();
                            bundle.putInt(SELECTED_TYPE, TYPE_ONE_FINISH);
                            AppManager.getInstance().jumpActivityForResult(DraftEditActivity.this, MaterialSelectActivity.class, bundle, REQUEST_PICTURE_IN_PICTURE_CODE);
                        }
                    }
                } else if (navigationName == R.string.nb_background1) {
                    //背景一级导航栏
                    //Background navigation bar
                    clickNavigationBackground(item);
                } else if (navigationName == R.string.nb_ratio1) {
                    //比例一级导航栏
                    //Ratio navigation bar
                    clickNavigationRatio(item);
                } else if (navigationName == R.string.nb_sticker2) {
                    //贴纸编辑二级导航栏
                    //Sticker secondary navigation bar
                    clickNavigationEditSticker(item);
                } else if (navigationName == R.string.nb_caption2) {
                    //字幕编辑二级导航栏
                    //Caption secondary navigation bar
                    clickNavigationEditCaption(item);
                } else if (navigationName == R.string.nb_combination_caption2) {
                    //组合字幕编辑二级导航栏
                    //Compound caption secondary navigation bar
                    clickNavigationEditCombinationCaption(item);
                } else if (navigationName == R.string.nb_filter2) {
                    //滤镜编辑二级导航栏
                    //Filter caption secondary navigation bar
                    clickNavigationEditFilter(item);
                } else if (navigationName == R.string.nb_adjust2) {
                    //调节编辑二级导航栏
                    //Adjust secondary navigation bar
                    clickNavigationEditAdjust(item);
                }
                return null;
            }

            @Override
            protected boolean onNavigationBack(int currentNavigationName, int preNavigationName) {
                mEditorTrackView.clickOutSide();
                if (mNavigationBar.isShow(R.string.nb_sticker2)
                        || mNavigationBar.isShow(R.string.nb_caption2)
                        || mNavigationBar.isShow(R.string.nb_combination_caption2)) {
                    mVideoFragment.resetFxEditMode();
                }
                if (currentNavigationName == R.string.nb_ratio1) {
                    //比例导航栏
                    //Ratio navigation bar
                    mEditTimeline.setTailViewVisibility(VISIBLE);
                } else if (currentNavigationName == R.string.nb_pip1) {
                    //画中画导航栏
                    //Picture-in-Picture navigation bar
                    mVideoFragment.setTransformViewVisible(GONE);
                } else if (currentNavigationName == R.string.nb_animate2) {
                    mEditTimeline.enableThumbnailAnimation(false);
                    mEditTimeline.enableThumbnailCoverEditMode(false);
                    mEditTimeline.showSpanView(mPresenter.getTimelineCurrentPosition());
                }

                if (preNavigationName == R.string.nb_main0) {
                    //这里拦截是为了统一处理回到主导航栏。
                    //The interception here is for unified processing and returning to the main navigation bar
                    if (currentNavigationName == R.string.nb_picture_edit1 || currentNavigationName == R.string.nb_video_edit1) {
                        //画中画正在显示
                        //Picture in picture is showing
                        if (mEditorTrackView.isShowPipTrackView()) {
                            mVideoFragment.resetVideoClipEditMode();
                            mNavigationBar.show(R.string.nb_pip1);
                        } else {
                            if (mainTrackSelected()) {
                                switchToMainMenu();

                            } else {
                                mVideoFragment.resetVideoClipEditMode();
                                mNavigationBar.show(R.string.nb_pip1);
                            }
                        }

                    } else {
                        switchToMainMenu();
                    }
                    return true;
                } else if (preNavigationName == R.string.nb_video_edit1) {
                    if (mCurSelectVideoClip != null && mCurSelectVideoClip.getVideoType().equals(CommonData.CLIP_IMAGE)
                            && currentNavigationName == R.string.nb_animate2) {
                        //默认返回到视频编辑导航栏，这里拦截返回到图片编辑导航栏
                        //Return to the video editing navigation bar by default,
                        // and intercept here to return to the picture editing navigation bar
                        mNavigationBar.show(R.string.nb_picture_edit1);
                        return true;
                    }
                }
                return false;
            }
        });
    }

    /**
     * 显示关键帧按钮
     * Show key frame view
     */
    private void showKeyFrameView(boolean addKeyFrame, KeyFrameProcessor<?> keyFrameHolder, long atTime) {
        if (showAtomicEditMenuView()) {
            mEditOperationView.notShowKeyFrameView();
        } else {
            if (!(mBottomViewContainer.getShowView() instanceof EditKeyFrameCurveView)) {
                mEditOperationView.showKeyFrameView(addKeyFrame);
                mEditOperationView.changeKeyFrameCurveState(keyFrameHolder != null &&
                        keyFrameHolder.getFramePair(getKeyOfKeyFrame(keyFrameHolder), atTime) != null);
            }
        }
        if (showAtomicEditMenuViewKeyFrame()) {
            AtomicEditMenuView view = (AtomicEditMenuView) mBottomViewHelper.getView().getShowView();
            if (view.getSelectedParam() != null && view.isShowKeyFrameView()) {
                String key = view.getSelectedParam().paramName;
                float value = mPresenter.getValueAtTime(keyFrameHolder, key, atTime);
                if (value != Float.MAX_VALUE) {
                    view.setNowIndex(value);
                }
                view.showKeyFrameView(addKeyFrame);
                view.changeKeyFrameCurveState(keyFrameHolder != null &&
                        keyFrameHolder.getFramePair(key, atTime) != null);
            }
        }

    }

    /**
     * 展示(视频/图片)编辑导航栏 ,如果是画中画存在“混合模式”
     * Show edit video or edit picture navigation bar
     */
    private void showEditNavigation() {
        if (mCurSelectVideoClip == null) {
            LogUtils.e("error ,mCurSelectVideoClip is null !");
            return;
        }
        Navigation.Item mixedMode = new Navigation.Item(R.string.sub_menu_name_edit_mixed_mode, R.mipmap.sub_menu_name_edit_mixed_mode);
        String videoType = mCurSelectVideoClip.getVideoType();
        if (CommonData.CLIP_VIDEO.equals(videoType)) {
            Navigation navigation = mNavigationBar.findNavigation(R.string.nb_video_edit1);
            if (navigation != null) {
                boolean isMainTrack = mCurSelectVideoClip.getTrackIndex() == 0;
                if (isMainTrack) {
                    navigation.removeItem(mixedMode);
                } else {
                    navigation.addItem(3, mixedMode);
                }
                addChangeTrackItem(navigation, isMainTrack);
                mNavigationBar.show(navigation);
            }
        } else if (CommonData.CLIP_IMAGE.equals(videoType)) {
            Navigation navigation = mNavigationBar.findNavigation(R.string.nb_picture_edit1);
            if (navigation != null) {
                boolean isMainTrack = mCurSelectVideoClip.getTrackIndex() == 0;
                if (isMainTrack) {
                    navigation.removeItem(mixedMode);
                } else {
                    navigation.addItem(1, mixedMode);
                }
                addChangeTrackItem(navigation, isMainTrack);
                mNavigationBar.show(navigation);
            }
        }
    }

    private void addChangeTrackItem(Navigation navigation, boolean isMainTrack) {
        if (isMainTrack) {
            Navigation.Item item = new Navigation.Item(R.string.sub_menu_name_edit_change_main_track, R.mipmap.sub_menu_name_edit_change_track);
            navigation.removeItem(item);
            item = new Navigation.Item(R.string.sub_menu_name_edit_change_sub_track, R.mipmap.sub_menu_name_edit_change_track);
            navigation.addItem(4, item);
        } else {
            Navigation.Item item = new Navigation.Item(R.string.sub_menu_name_edit_change_sub_track, R.mipmap.sub_menu_name_edit_change_track);
            navigation.removeItem(item);
            item = new Navigation.Item(R.string.sub_menu_name_edit_change_main_track, R.mipmap.sub_menu_name_edit_change_track);
            navigation.addItem(5, item);
        }
    }

    /**
     * 展示(贴纸)编辑导航栏
     * Show sticker navigation bar
     */
    private void showStickNavigation() {
        Navigation.Item stickerVoice = new Navigation.Item(R.string.sub_menu_sticker_edit_voice, R.mipmap.sub_menu_icon_edit_sticker_voice);
        Navigation navigation = mNavigationBar.findNavigation(R.string.nb_sticker2);
        if (navigation != null) {
            mNavigationBar.show(!mEditorEngine.currStickerHasVoice() ? navigation.removeItem(stickerVoice) : navigation.addItem(stickerVoice));
        }
    }

    /**
     * 主轨道是否被选中
     * Whether the main orbit is selected
     */
    private boolean mainTrackSelected() {
        return mCurrentTrackIndex == MAIN_TRACK_INDEX;
    }

    /**
     * 设置比例导航栏的选中的子项
     * Gets a ratio navigation's item
     */
    private void setRatioNavigationItem(int ratio) {
        Navigation navigation = mNavigationBar.findNavigation(R.string.nb_ratio1);
        if (navigation != null && navigation.getItems() != null) {
            for (Navigation.Item item : navigation.getItems()) {
                Object tag = item.getTag();
                if (tag != null && ratio == (int) tag) {
                    mNavigationBar.selectedNavigationItem(navigation, item);
                    break;
                }
            }
        }
    }

    /**
     * 点击主导航栏
     * clicked main navigation bar
     *
     * @param item The children of navigation bar
     */
    private Navigation.Item clickNavigationMain(Navigation.Item item) {
        if (item.getTitleId() == R.string.main_menu_name_theme) {
            //主题，目前被屏蔽了。
            //Theme, currently blocked
            if (mEditorEngine.getVideoTrackCount() > 1) {
                showCommonPop("", getString(R.string.delete_pip_add_theme), "", getString(R.string.got_it));
            }
            //mBottomViewHelper.showThemeMenu();
        } /*else if (item.getTitleId() == R.string.main_menu_name_edit) {
            //编辑 Edit
        } */ else if (item.getTitleId() == R.string.main_menu_name_fx) {
            //特效 Effect
            //mMultiHelper.showEffectView(null);
//            mEditOperationView.updateCancelRecoverVisible(false);
            toOtherMenu();
            updateTimelineFxTrack();
        } else if (item.getTitleId() == R.string.main_menu_name_sticker || item.getTitleId() == R.string.main_menu_name_caption
                || item.getTitleId() == R.string.main_menu_name_com_caption) {
            //贴纸/字幕/组合字幕
            //Sticker/caption/combined caption
            toOtherMenu();
            updateStickerTrack();
            mVideoFragment.resetFxEditMode();
            mEditTimeline.showEffectRegion(false, true, false);
        } else if (item.getTitleId() == R.string.main_menu_name_music || item.getTitleId() == R.string.main_menu_name_dubbing) {
            //音频/录制
            //Audio/Recording
            refreshAudioView();
            toOtherMenu();
        } else if (item.getTitleId() == R.string.main_menu_name_picture_in_picture) {
            //画中画
            //Picture-in-Picture
            MeicamTheme theme = mTimeline.getMeicamTheme();
            if (theme != null && !TextUtils.isEmpty(theme.getThemePackageId())) {
                showCommonPop("", getString(R.string.add_pip_cancel_theme), "", getString(R.string.got_it));
            }
            toOtherMenu();
            mVideoFragment.resetVideoClipEditMode();
            mEditTimeline.showEffectRegion(false, false, true);
            mEditorTrackView.showPipTrackView(mTimeline);
        } else if (item.getTitleId() == R.string.main_menu_name_background) {
            //背景
            //Background
            toOtherMenu();
            setCurrentMainTrackClip();
            mVideoFragment.updateTransformFx(mCurSelectVideoClip, true);
            mEditTimeline.enableThumbnailCoverEditMode(true);
        } else if (item.getTitleId() == R.string.main_menu_name_ratio) {
            //比例
            //Ratio
            toOtherMenu();
            setCurrentMainTrackClip();
            mVideoFragment.updateTransformFx(mCurSelectVideoClip, true);
            mEditTimeline.enableThumbnailCoverEditMode(true);
        } else if (item.getTitleId() == R.string.main_menu_name_filter || item.getTitleId() == R.string.main_menu_name_adjust) {
            toOtherMenu();
            updateFilterAndAdjustTrack();
        }
        return null;
    }

    /**
     * 点击编辑导航栏
     * clicked edit navigation bar
     *
     * @param item The children of navigation bar
     */
    private void clickNavigationEdit(Navigation.Item item) {
        if (item.getTitleId() == R.string.sub_menu_name_edit_divide) {
            //分割
            //Split
            int returnCode;
            if (checkSmartKeying() || mCurSelectVideoClip == null) {
                return;
            }
            if (mainTrackSelected()) {
                if (mPresenter.checkVoiceDictation()) {
                    return;
                }
                returnCode = mEditorEngine.cutClip(mCurSelectVideoClip, MAIN_TRACK_INDEX);
                if (returnCode == CODE_OK) {
                    //分割以后会影响当前videoClip前面那个转场和下一个的转场
                    //The split will affect the previous transition and the next transition of the current videoClip
                    if (mCurSelectVideoClip != null) {
                        mPresenter.updateTransitionByVideoClipDuration(mCurSelectVideoClip.getIndex());
                        mPresenter.updateTransitionByVideoClipDuration(mCurSelectVideoClip.getIndex() + 1);
                    }
                    refreshEditorTimelineView(EditorEngine.OnTimelineChangeListener.TYPE_CUT_CLIP);
                }
            } else {
                returnCode = mEditorEngine.cutClip(mCurSelectVideoClip, mCurSelectVideoClip.getTrackIndex());
                if (returnCode == CODE_OK) {
                    mEditorTrackView.showPipTrackView(mTimeline);
                    mCurSelectVideoClip = mEditorEngine.getVideoClip(mCurSelectVideoClip.getTrackIndex(), mCurSelectVideoClip.getIndex() + 1);
                    if (mCurSelectVideoClip != null) {
                        mEditorTrackView.setSelect(mCurSelectVideoClip.getTrackIndex() - 1, mCurSelectVideoClip.getInPoint());
                    }
                }
                mPresenter.getPipLineRegionAsync(new DraftEditPresenter.LineRegionDataCallBack() {
                    @Override
                    public void onResultCallback(List<LineRegionClip> data) {
                        mEditTimeline.setPipRegion(data);
                    }
                });
            }
            if (returnCode == EditorEngine.ReturnCode.CODE_CAN_NOT_OPERATE) {
                ToastUtils.showShort(R.string.current_position_not_allow_cut);
                return;
            }
            saveOperation();
        } else if (item.getTitleId() == R.string.sub_menu_name_edit_volume) {
            if (mCurSelectVideoClip == null) {
                return;
            }
            //音量
            //Volume
            if (mainTrackSelected()) {
                toOtherMenu();
            }
            mMultiHelper.showVolumeAndFade(mCurSelectVideoClip, new VolumeFragment.EventListener() {
                @Override
                public void onVolumeChange(int progress) {
                    VideoClipCommand.setVolume(mCurSelectVideoClip, progress *
                            Constants.maxNvVolume / Constants.maxVolumeProgress, true);
                    if (mainTrackSelected()) {
                        ITrackClip mainSelectedClip = mEditTimeline.getMainSelectedClip();
                        if (mainSelectedClip != null) {
                            mainSelectedClip.setVolume(mCurSelectVideoClip.getVolume());
                            mEditTimeline.changeMainTrackClipVolume();
                        }
                        if (progress > 0) {
                            mEditorEngine.toggleOriginalVoice(TRACK_INDEX_MAIN, true, false);
                        }
                        mEditTimeline.toggleOriginalVoice(mPresenter.originalVoiceIsOpen());
                    } else {
                        mEditorTrackView.changeVolumeState();
                    }
                }

                @Override
                public void onAudioFadeChange(long fadeIn, long fadeOut) {
                    mEditorEngine.videoEditAudioTransition(mCurSelectVideoClip, fadeIn, fadeOut);
                }
            });

        } else if (item.getTitleId() == R.string.sub_menu_name_edit_animation) {
            //动画
            //Animation
            if (mainTrackSelected()) {
                toOtherMenu();
                mEditTimeline.enableThumbnailCoverEditMode(true);
                mEditTimeline.enableThumbnailAnimation(true);
                mEditTimeline.notShowSpanView();
            }
        } else if (item.getTitleId() == R.string.sub_menu_name_edit_delete) {
            //删除
            //Delete
            int returnCode;
            if (checkSmartKeying() || mCurSelectVideoClip == null) {
                return;
            }
            mVideoFragment.hideVideoClipOperationBox();
            if (mainTrackSelected()) {
                if (mPresenter.checkVoiceDictation()) {
                    return;
                }
                mPresenter.deleteVideoClip(mEditTimeline.getMainSelectedClip());
            } else {
                int oldVideoTrackCount = mPresenter.getVideoTrackCount();
                LineRegionClip lineRegionClip = mPresenter.getLineRegionClip(mCurSelectVideoClip,
                        mCurSelectVideoClip.getTrackIndex(), false);
                if (mPresenter.deleteVideoClip(mCurSelectVideoClip)) {
                    if (oldVideoTrackCount != mPresenter.getVideoTrackCount()) {
                        // 删除片段的时候，轨道可能会被删除，需要重新设置。
                        // When deleting clips, the tracks may be deleted and need to be reset.
                        mPresenter.getPipLineRegionAsync(new DraftEditPresenter.LineRegionDataCallBack() {
                            @Override
                            public void onResultCallback(List<LineRegionClip> data) {
                                mEditTimeline.setPipRegion(data);
                            }
                        });
                    } else {
                        mEditTimeline.updatePipRegion(lineRegionClip, true);
                    }
                }
            }

        } else if (item.getTitleId() == R.string.sub_menu_name_edit_mask) {

            if (mCurSelectVideoClip == null) {
                LogUtils.e("mask mCurSelectVideoClip==null");
                return;
            }
            //如果有蒙版效果 要显示出来
            //If there is a mask effect, it should be displayed.
            int maskType = mCurSelectVideoClip.maskModel.maskType;
            mVideoFragment.openMaskZoomEditMode(mCurSelectVideoClip, true);
            if (!CommonData.SUPPORT_MASK_KEY_FRAME_CURVE) {
                mEditOperationView.notShowKeyFrameCurveView();
            }
            mEditTimeline.enableThumbnailMove(false);
            mBottomViewHelper.showMasking(maskType, new BottomEventListener() {
                @Override
                public void onDismiss(boolean confirm) {
                    mEditTimeline.enableThumbnailMove(true);
                    mBottomViewContainer.dismissView();
                    showKeyframeInTrack();
                    final MeicamVideoFx propertyVideoFx = mCurSelectVideoClip.findPropertyVideoFx();
                    if (CommonData.SUPPORT_MASK_KEY_FRAME_CURVE) {
                        mEditOperationView.changeKeyFrameCurveState(propertyVideoFx != null &&
                                propertyVideoFx.keyFrameProcessor().getFramePair(NvsConstants.KEY_CROPPER_TRANS_X, mEditorEngine.getCurrentTimelinePosition() - mCurSelectVideoClip.getInPoint()) != null);
                    }
                    updateVideoClipKeyFrame(mCurSelectVideoClip.findPropertyVideoFx(), NvsConstants.KEY_CROPPER_TRANS_X);
                    mVideoFragment.openMaskZoomEditMode(mCurSelectVideoClip, false);
                    saveOperation();
                }

                @Override
                public void onItemClick(IBaseInfo baseInfo, boolean reverse) {
                    //点击底部菜单选择蒙版类型
                    //Click the bottom menu to select the mask type
                    MaskInfoData maskData = (MaskInfoData) baseInfo;
                    if (mCurSelectVideoClip == null) {
                        return;
                    }
                    int newType = maskData.getMaskType();
                    if (!reverse) {
                        mCurSelectVideoClip.maskModel.reset();
                    }
                    int oldType = mCurSelectVideoClip.maskModel.maskType;
                    if (oldType != newType) {
                        mPresenter.removeMaskKeyFrame(mCurSelectVideoClip);
                        updateVideoClipKeyFrame(mEditorEngine.getMaskTargetFx(mCurSelectVideoClip), NvsConstants.KEY_PROPERTY_MASK_REGION_INFO);
                    }
                    mCurSelectVideoClip.maskModel.maskType = newType;
                    if (reverse) {
                        mCurSelectVideoClip.maskModel.inverseRegion = !mCurSelectVideoClip.maskModel.inverseRegion;
                    }
                    mVideoFragment.openMaskZoomEditMode(mCurSelectVideoClip, true);
                    if (newType == MaskZoomView.MaskType.NONE) {
                        mEditorEngine.removeMask(mCurSelectVideoClip);
                        mEditorEngine.seekTimeline();
                        /*
                         *  隐藏关键帧和关键帧曲线按钮
                         * Hide keyframe and keyframe curve button.
                         */
                        mEditOperationView.notShowKeyFrameView();
                    } else {
                        mEditOperationView.showKeyFrameView();
                    }
                }
            });

            if (maskType == 0) {
                hideKeyframeInTrack();
            } else {
                showKeyframeInTrack();
                updateVideoClipKeyFrame(EditorEngine.getInstance().getMaskTargetFx(mCurSelectVideoClip), NvsConstants.KEY_PROPERTY_MASK_REGION_INFO);
            }
        } else if (item.getTitleId() == R.string.sub_menu_name_edit_smart_keyer) {
            //智能抠像
            //smart extract
            if (mCurSelectVideoClip == null) {
                return;
            }
            if (mPresenter.sameSmartKeyerClip(mCurSelectVideoClip)) {
                /*是当前正在抠像的片段，则弹出取消弹窗
                 * If the clip is currently being extracted, the cancel pop-up window will pop up
                 * */
                showCancelSmartKeyerPop();
                return;
            }
            if (checkSmartKeying()) {
                /*正在抠像中则提示并返回
                 * Prompt and return when the image is being extracted
                 * */
                return;
            }

            MeicamVideoFx segmentVideoFx = mCurSelectVideoClip.getVideoFxById(SEGMENTATION);
            MeicamVideoFx alphaVideoFx = mCurSelectVideoClip.getVideoFx(SUB_TYPE_ALPHA, SET_ALPHA);
            if (segmentVideoFx == null && alphaVideoFx == null) {
                mPresenter.startSmartKeyer(mCurSelectVideoClip);
            } else {
                if (alphaVideoFx != null) {
                    VideoClipCommand.removeFx(mCurSelectVideoClip, alphaVideoFx);
                }
                if (segmentVideoFx != null) {
                    mCurSelectVideoClip.removeVideoFx(segmentVideoFx);
                    VideoClipCommand.removeFx(mCurSelectVideoClip, segmentVideoFx);
                }
                saveOperation();
                ToastUtils.showShort(R.string.cancel_smart_keyer);
                mEditorEngine.seekTimeline(STREAMING_ENGINE_SEEK_FLAG_BUDDY_HOST_VIDEO_FRAME);
            }
        } else if (item.getTitleId() == R.string.sub_menu_name_edit_color_picker) {
            //色度抠图
            //Color pick
            mEditOperationView.updateCancelRecoverVisible(false);
            mVideoFragment.showColorPicker();
            float[] param = mPresenter.getMasterKeyerParam(mCurSelectVideoClip);
            mBottomViewHelper.showColorPickMenu(param, new MYColorPickMenuView.OnEventChangedListener() {
                @Override
                public void onMenuClicked(int position) {
                    if (position == 0) {
                        mVideoFragment.showColorPicker();
                    } else {
                        mVideoFragment.hideColorPicker();
                    }
                }

                @Override
                public void onDataChanged(String key, int progress) {
                    mPresenter.changeColorPickerParam(mCurSelectVideoClip, key, progress, null);
                }

                @Override
                public void onConfirm() {
                    mVideoFragment.releaseColorPicker();
                    mEditOperationView.updateCancelRecoverVisible(true);
                    saveOperation();
                }

                @Override
                public void onReset() {
                    mPresenter.resetColorPickerParam(mCurSelectVideoClip);
                    mVideoFragment.showColorPicker();
                    mVideoFragment.resetColorPicker();
                }
            });
        } else if (item.getTitleId() == R.string.sub_menu_name_edit_filter || item.getTitleId() == R.string.sub_menu_name_edit_adjust) {
            //滤镜/调节
            //Filter/Adjust
            if (mainTrackSelected()) {
                toOtherMenu();
            }
            if (item.getTitleId() == R.string.sub_menu_name_edit_adjust) {
                mBottomViewHelper.showAdjustMenu(mCurSelectVideoClip, null, null);
            } else {
                mBottomViewHelper.showFilterMenu(mCurSelectVideoClip, null, null);
            }
        } else if (item.getTitleId() == R.string.sub_menu_name_edit_opacity) {
            if (mCurSelectVideoClip == null) {
                LogUtils.e("当前选中的videoClip是Null");
                return;
            }
            //透明度
            //Opacity
            toOtherMenu();
            showKeyframeInTrack();
            MeicamVideoFx propertyVideoFx = mCurSelectVideoClip.findPropertyVideoFx();
            updateVideoClipKeyFrame(propertyVideoFx, NvsConstants.KEY_MASK_OPACITY);
            final long atTime = mEditorEngine.getCurrentTimelinePosition() - mCurSelectVideoClip.getInPoint();
            mEditOperationView.showKeyFrameView(
                    propertyVideoFx.keyFrameProcessor().getKeyFrame(NvsConstants.KEY_MASK_OPACITY, atTime) == null);
            mEditOperationView.changeKeyFrameCurveState(
                    propertyVideoFx.keyFrameProcessor().getFramePair(NvsConstants.KEY_MASK_OPACITY, atTime) != null);
            final int maxProgress = 100;
            float opacity = mPresenter.getOpacityAtTime(mCurSelectVideoClip, atTime);
            mBottomViewHelper.showAdjustSeekBar(maxProgress, (int) (opacity * 100),
                    R.string.title_edit_not_opacity, getString(item.getTitleId()), new BottomEventListener() {
                        @Override
                        public void onDismiss(boolean confirm) {
                            mBottomViewContainer.dismissView();
                            showKeyframeInTrack();
                            updateVideoClipKeyFrame(propertyVideoFx, NvsConstants.KEY_CROPPER_TRANS_X);
                            mEditOperationView.changeKeyFrameCurveState(
                                    propertyVideoFx.keyFrameProcessor().
                                            getFramePair(NvsConstants.KEY_CROPPER_TRANS_X, atTime) != null);
                        }

                        @Override
                        public void onProgressChanged(int progress, boolean fromUser, int type) {
                            if (mNavigationBar.isShow(R.string.nb_video_edit1) || mNavigationBar.isShow(R.string.nb_picture_edit1)) {
                                //视频/图片编辑导航栏正在显示
                                //Video/picture editing navigation bar is showing
                                mEditorEngine.changeOpacity(mCurSelectVideoClip, progress * 1f / maxProgress);
                            }
                        }

                        @Override
                        public void onStopTrackingTouch(int type) {
                            MeicamVideoFx videoFx = mCurSelectVideoClip.findPropertyVideoFx();
                            if (videoFx != null && videoFx.keyFrameProcessor().getKeyFrameCount() > 0) {
                                /*如果有关键帧，自动添加或者修改
                                 * If there are keys, add or modify them automatically
                                 * */
                                KeyFrameInfo keyFrameInfo = null;
                                ITrackClip mainSelectedClip = mEditTimeline.getMainSelectedClip();
                                if (mainSelectedClip != null) {
                                    keyFrameInfo = mainSelectedClip.getKeyFrameInfo();
                                } else if (mEditorTrackView.getDragView() != null) {
                                    BaseUIClip baseUIClip = mEditorTrackView.getDragView().getBaseUIClip();
                                    if (baseUIClip != null) {
                                        keyFrameInfo = baseUIClip.getKeyFrameInfo();
                                    }
                                }
                                mPresenter.updateOrAddKeyFrame(videoFx, keyFrameInfo, mainSelectedClip != null);
                            }
                            saveOperation();
                        }

                        @Override
                        public void onStartTrackingTouch(int type) {
                            long atTime = mTimeline.getCurrentPosition() - mCurSelectVideoClip.getInPoint();
                            MeicamVideoTrack meicamVideoTrack = mTimeline.getVideoTrack(mCurSelectVideoClip.getTrackIndex());
                            long offSet = 0;
                            if (meicamVideoTrack != null) {
                                MeicamTransition transition = meicamVideoTrack.getTransition(mCurSelectVideoClip.getIndex() - 1);
                                if (transition != null) {
                                    offSet = (long) (transition.getDuration() / 2F);
                                }
                            }
                            List<MeicamFxParam<?>> data = new ArrayList<>();
                            data.add(new MeicamFxParam<>(MeicamFxParam.TYPE_FLOAT, NvsConstants.PROPERTY_OPACITY, mCurSelectVideoClip.getOpacity()));
                            mPresenter.tryToAddTempKeyFrame(mCurSelectVideoClip.findPropertyVideoFx(), data, NvsConstants.PROPERTY_OPACITY, atTime, offSet);
                        }
                    });
        } else if (item.getTitleId() == R.string.sub_menu_name_edit_rotation) {
            //角度
            //Rotation
            toOtherMenu();
            ITrackClip mainSelectedClip = mEditTimeline.getMainSelectedClip();
            long atTime = -1;
            if (mainSelectedClip != null) {
                atTime = mainSelectedClip.getKeyFrameInfo().getSelectedPoint();
            } else if (mEditorTrackView.getDragView() != null) {
                atTime = mEditorTrackView.getDragView().getKeyFrameSelectedPoint();
            }
            long resultTime = mEditorEngine.changeRotation(mCurSelectVideoClip, atTime);
            if (resultTime != -1 && atTime != resultTime) {
                /*视图需要新加关键帧了
                 * The view needs new keyframes
                 * */
                boolean success = true;
                if (mainSelectedClip != null) {
                    mEditTimeline.addKeyFrameTag(resultTime);
                } else if (mEditorTrackView.getDragView() != null) {
                    success = mEditorTrackView.getDragView().addKeyFrame(resultTime, true);
                }
                showKeyFrameView(!success, getKeyFrameProcessor(mCurSelectVideoClip), mEditorEngine.getCurrentTimelinePosition() - mCurSelectVideoClip.getInPoint());
            }
            mVideoFragment.updateTransformFx(mCurSelectVideoClip, true);
            saveOperation();
        } else if (item.getTitleId() == R.string.sub_menu_name_edit_cut) {
            if (Utils.isFastClick()) {
                return;
            }
            if (mCurSelectVideoClip == null) {
                return;
            }
            //剪裁
            //Cut
            toOtherMenu();
            Bundle bundle = new Bundle();
            MeicamTimeline currentTimeline = mEditorEngine.getCurrentTimeline();
            if (currentTimeline != null) {
                NvsVideoResolution videoRes = currentTimeline.getVideoResolution();
                bundle.putInt(INTENT_KEY_TIMELINE_HEIGHT, videoRes.imageHeight);
                bundle.putInt(INTENT_KEY_TIMELINE_WIDTH, videoRes.imageWidth);
            } else {
                LogUtils.e("timeline is null");
            }
            if (mainTrackSelected()) {
                mVideoFragment.updateTransformFx(mCurSelectVideoClip);
            }
            bundle.putInt(INTENT_KEY_TRACK_INDEX, mCurSelectVideoClip.getTrackIndex());
            bundle.putInt(CLIP_INDEX, mCurSelectVideoClip.getIndex());
            AppManager.getInstance().jumpActivityForResult(this, ClipCuttingActivity.class, bundle, REQUEST_CROP_CLIP_CODE);
        } else if (item.getTitleId() == R.string.sub_menu_name_edit_mirror) {
            //镜像
            //Mirror
            toOtherMenu();
            mEditorEngine.changeMirror(mCurSelectVideoClip);
        } else if (item.getTitleId() == R.string.sub_menu_name_edit_copy) {
            //复制
            //Copy
            if (mPresenter.checkVoiceDictation() || mCurSelectVideoClip == null) {
                return;
            }
            mPresenter.copyVideoClip(mCurSelectVideoClip, !mainTrackSelected());
        } else if (item.getTitleId() == R.string.sub_menu_name_edit_reverse) {
            if (checkSmartKeying()) {
                return;
            }
            //倒放
            //Reverse
            toOtherMenu();
            if (mCurSelectVideoClip != null) {
                if (mCurSelectVideoClip.isConvertSuccess()) {
                    mEditorEngine.setVideoConvert(mCurSelectVideoClip);
                    if (mCurSelectVideoClip.getVideoReverse()) {
                        ToastUtils.showShort(R.string.revert_finish);
                    } else {
                        ToastUtils.showShort(R.string.revert_cancel);
                    }
                } else {
                    showCenterProgress(getResources().getString(R.string.reverting));
                    String filePath = mCurSelectVideoClip.getFilePath();
                    mConvertFileManager.unregisterConvertFileObserver(convertFileObserver);
                    if (filePath != null && filePath.endsWith("m3u8")) {
                        IConvertManager convertManager = null;
                        try {
                            Class<?> aClass = Class.forName("com.meishe.user.manager.CloudReverseFileManager");
                            convertManager = (IConvertManager) aClass.newInstance();
                        } catch (Exception e) {
                            LogUtils.e(e);
                        }
                        if (convertManager != null) {
                            mConvertFileManager.setExecutor(convertManager);
                        }
                    } else {
                        mConvertFileManager.setExecutor(ConvertFileManager.getInstance());
                    }
                    mConvertFileManager.registerConvertFileObserver(convertFileObserver);
                    mPresenter.startReverseClip(mCurSelectVideoClip, mConvertFileManager);
                }
            }
        } else if (item.getTitleId() == R.string.sub_menu_name_edit_freeze_frame) {
            //定格
            //Freeze
            if (checkSmartKeying() || mCurSelectVideoClip == null) {
                return;
            }
            toOtherMenu();
            if (mainTrackSelected()) {
                mPresenter.freezeFrameClip(mCurSelectVideoClip, MAIN_TRACK_INDEX, false);
                mPresenter.getMainTrackLineRegion();
            } else {
                mPresenter.freezeFrameClip(mCurSelectVideoClip, mCurSelectVideoClip.getTrackIndex(), true);
            }
        } else if (item.getTitleId() == R.string.sub_menu_name_edit_beauty) {
            //美颜
            //Beauty
            toOtherMenu();
            hideKeyframeInTrack();
            mMultiHelper.showBeautyView(mCurSelectVideoClip, new BottomEventListener() {
                @Override
                public void onDismiss(boolean confirm) {
                    super.onDismiss(confirm);
                    showKeyframeInTrack();
                    if (confirm) {
                        saveOperation();
                    }
                }
            });
        } else if (item.getTitleId() == R.string.sub_menu_name_edit_mixed_mode) {
            //混合模式
            //Mixed mode
            hideKeyframeInTrack();
            mBottomViewHelper.showMixedModeMenu(mCurSelectVideoClip, new BottomEventListener() {
                @Override
                public void onDismiss(boolean confirm) {
                    super.onDismiss(confirm);
                    showKeyframeInTrack();
                }
            });
        } else if (item.getTitleId() == R.string.sub_menu_video_edit_change_voice) {
            //变声
            //Tone
            hideKeyframeInTrack();
            mBottomViewHelper.showVideoChangeVoiceView(mCurSelectVideoClip, new BottomEventListener() {
                @Override
                public void onDismiss(boolean confirm) {
                    showKeyframeInTrack();
                    mBottomViewContainer.dismissView();
                    if (confirm) {
                        saveOperation();
                    }
                }

                @Override
                public void onItemClick(IBaseInfo baseInfo, boolean applyAll) {
                    mEditorEngine.videoEditChangeVoice(mCurSelectVideoClip, baseInfo.getEffectId());
                }
            });
        } else if (item.getTitleId() == R.string.sub_menu_name_edit_prop) {
            EditorEngine.getInstance().setUseBeautyFaceShape(true);
            mMultiHelper.showPropMenu(mCurSelectVideoClip, baseInfo -> {
                EditorEngine.getInstance().addProp(mCurSelectVideoClip, baseInfo.getAssetPath(), baseInfo.getPackageId());
                EditorEngine.getInstance().seekTimeline();
                if (mainTrackSelected()) {
                    ITrackClip mainSelectedClip = mEditTimeline.getMainSelectedClip();
                    if (mainSelectedClip != null) {
                        mainSelectedClip.setHasProp(true);
                        mEditTimeline.changeMainTrackClipProp();
                    }
                } else {
                    mEditorTrackView.showPipTrackView(mTimeline);
                }
            });

        } else if (item.getTitleId() == R.string.effect_plug_add) {
            mCurrSelectedVideoFx = null;
            showPlusMenu();
        } else if (item.getTitleId() == R.string.sub_menu_name_edit_replace) {
            //替换
            //Replace
            if (mCurSelectVideoClip == null) {
                return;
            }
            long duration = mCurSelectVideoClip.getTrimOut() - mCurSelectVideoClip.getTrimIn();
            Bundle bundle = new Bundle();
            bundle.putInt(SELECTED_TYPE, MaterialSelectFillActivity.TYPE_ONE_FINISH);
            bundle.putString(NEXT_PAGE_ACTION, "com.meishe.myvideo.activity.ClipReplaceActivity");
            MeidaClip meidaClip = new MeidaClip();
            meidaClip.setDuration(duration);
            bundle.putParcelable(BUNDLE_CLIP, meidaClip);
            AppManager.getInstance().jumpActivityForResult(DraftEditActivity.this,
                    MaterialSelectFillActivity.class, bundle, REQUEST_CLIP_REPLACE);
        } else if (item.getTitleId() == R.string.sub_menu_name_edit_change_sub_track) {
            //切画中画
            //Change sub track
            if (checkSmartKeying() || mPresenter.checkVoiceDictation()) {
                return;
            }
            mPresenter.changeClipToSubTrack(mCurSelectVideoClip);
        } else if (item.getTitleId() == R.string.sub_menu_name_edit_change_main_track) {
            //切主轨
            //Change main track
            if (checkSmartKeying() || mPresenter.checkVoiceDictation()) {
                return;
            }
            mPresenter.changeClipToMainTrack(mCurSelectVideoClip);
        }

    }

    /**
     * 更新关键帧在view上的展示状态
     * Update key frame status
     */
    private void updateKeyFrameStatus() {
        long timelineCurrentPosition = mPresenter.getTimelineCurrentPosition();
        BaseItemView dragView = mEditorTrackView.getDragView();
        if (dragView != null) {
            BaseUIClip baseUIClip = dragView.getBaseUIClip();
            if (baseUIClip != null) {
                long atTime = mEditorEngine.getCurrentTimelinePosition() - baseUIClip.getInPoint();
                if (timelineCurrentPosition >= baseUIClip.getInPoint() && timelineCurrentPosition <= baseUIClip.getOutPoint()) {
                    KeyFrameInfo keyFrameInfo = baseUIClip.getKeyFrameInfo();
                    if (keyFrameInfo == null) {
                        showKeyFrameView(true, getKeyFrameProcessor(baseUIClip), atTime);
                    } else {
                        long selectedPoint = keyFrameInfo.getSelectedPoint();
                        if (selectedPoint >= 0) {
                            atTime = selectedPoint;
                        }
                        showKeyFrameView(selectedPoint < 0, getKeyFrameProcessor(baseUIClip), atTime);
                    }

                } else {

                    notShowKeyFrameView();
                }
            }
        } else {
            ITrackClip trackClip = mEditTimeline.getMainSelectedClip();
            if (trackClip == null) {
                return;
            }
            long currentTime = mPresenter.getTimelineCurrentPosition();
            mEditTimeline.checkKeyFrameSelected(mPresenter.getTimelineCurrentPosition());
            long selectedPoint = trackClip.getKeyFrameInfo().getSelectedPoint();

            long atTime = mCurSelectVideoClip != null ? currentTime - mCurSelectVideoClip.getInPoint() : -1;
            if (selectedPoint >= 0) {
                atTime = selectedPoint;
            }
            showKeyFrameView(selectedPoint < 0, getKeyFrameProcessor(mCurSelectVideoClip), atTime);
        }
    }

    private void updateVideoClipKeyFrame(IKeyFrameProcessor<?> keyFrameHolder, String key) {
        ITrackClip trackClip = mEditTimeline.getMainSelectedClip();
        if (trackClip == null) {
            BaseItemView dragView = mEditorTrackView.getDragView();
            if (dragView != null) {
                BaseUIClip baseUIClip = dragView.getBaseUIClip();
                if (baseUIClip != null) {
                    String type = baseUIClip.getType();
                    if (CommonData.CLIP_IMAGE.equals(type) || CommonData.CLIP_VIDEO.equals(type)
                            || baseUIClip instanceof TimelineVideoFxProxy) {
                        dragView.updateKeyFrame(mPresenter.getKeyFrameInfo(keyFrameHolder, key), mPresenter.getTimelineCurrentPosition());
                        mEditorTrackView.showKeyframe();
                    }
                }
            }
        } else {
            trackClip.setKeyFrameInfo(mPresenter.getKeyFrameInfo(keyFrameHolder, key));
            mEditTimeline.updateThumbnail(trackClip, true);
            mEditTimeline.showKeyFrame();
        }
    }

    /**
     * 点击变速导航栏
     * clicked change speed navigation bar
     *
     * @param item The children of navigation bar
     */
    private void clickNavigationChangeSpeed(Navigation.Item item) {
        if (mPresenter.checkVoiceDictation() || checkSmartKeying()) {
            return;
        }
        mEditOperationView.updateCancelRecoverVisible(false);
        toOtherMenu();
        if (R.string.menu_sub_tab_change_speed_practice == item.getTitleId()) {
            //常规变速
            //Normal speed
            if (mCurSelectVideoClip != null) {
                hideKeyframeInTrack();
                double speed = 1.0f;
                if (TextUtils.isEmpty(mCurSelectVideoClip.getCurveSpeed())) {
                    speed = mCurSelectVideoClip.getSpeed();
                }
                mBottomViewHelper.showNormalSpeed((float) speed, mCurSelectVideoClip.isKeepAudioPitch()
                        , new EditChangeSpeedView.ChangeSpeedListener() {
                            @Override
                            public void onSpeedChange(float speed, boolean changeVoice) {
                                if (mainTrackSelected()) {
                                    mPresenter.updateAiCaptionAndChaneSpeed(mCurSelectVideoClip, speed, changeVoice);
                                    mEditorEngine.seekTimeline(0);
                                    ITrackClip mainSelectedClip = mEditTimeline.getMainSelectedClip();
                                    if (mainSelectedClip != null) {
                                        mainSelectedClip.setInPoint(mCurSelectVideoClip.getInPoint());
                                        mainSelectedClip.setOutPoint(mCurSelectVideoClip.getOutPoint());
                                        SpeedInfo speedInfo = mainSelectedClip.getSpeedInfo();
                                        speedInfo.setSpeed(mCurSelectVideoClip.getSpeed());
                                        speedInfo.setSpeedName(mCurSelectVideoClip.getCurveSpeedName());
                                        mainSelectedClip.setKeyFrameInfo(mPresenter.getKeyFrameInfo(mCurSelectVideoClip.findPropertyVideoFx(), NvsConstants.KEY_CROPPER_TRANS_X));
                                        mEditTimeline.changeMainTrackClipSpeed(mainSelectedClip, mPresenter.getTimeline().getDuration());
                                        mEditorTrackView.initWidth(mTimeline.getDuration());
                                        mPresenter.updateTransitionByVideoClipDuration(mCurSelectVideoClip.getIndex());
                                    }
                                    //更新时间轴时长
                                    //Update duration of timeline
                                    mEditOperationView.setDurationText(FormatUtils.microsecond2Time(mPresenter.getTimelineCurrentPosition())
                                            + "/" + FormatUtils.microsecond2Time(mTimeline.getDuration()));
                                    MeicamVideoFx propertyVideoFx = mCurSelectVideoClip.findPropertyVideoFx();
                                    if (propertyVideoFx != null && propertyVideoFx.keyFrameProcessor().getKeyFrameCount() > 0 && mVideoFragment.transforViewVisible()) {
                                        mVideoFragment.updateTransformFx(mCurSelectVideoClip, true);
                                    }
                                    if (mEditorTrackView.isShowTrackView()) {
                                        updateStickerTrack();
                                    }
                                    mPresenter.getMainTrackLineRegion();
                                } else {
                                    mEditorEngine.changeNormalSpeed(mCurrentTrackIndex, mCurSelectVideoClip, speed, changeVoice);
                                    mEditorTrackView.showPipTrackView(mTimeline);
                                    mEditorTrackView.setSelect(mCurrentTrackIndex - 1, mCurSelectVideoClip.getInPoint());
                                    mEditorEngine.seekTimeline(0);
                                }
                                mPresenter.checkTrackDuration();
                                if (mainTrackSelected()) {
                                    ITrackClip mainSelectedClip = mEditTimeline.getMainSelectedClip();
                                    if (mainSelectedClip != null) {
                                        mEditTimeline.enableThumbnailSpeed(mainSelectedClip, false);
                                    }
                                }
                            }

                            @Override
                            public void onDismiss(boolean confirm) {
                                mBottomViewContainer.dismissView();
                                mEditorTrackView.setPipDuringVisiableStatus(true);
                                showKeyframeInTrack();
                                mEditTimeline.updateSpanViewContent(true);
                                mPresenter.getPipLineRegionAsync(new DraftEditPresenter.LineRegionDataCallBack() {
                                    @Override
                                    public void onResultCallback(List<LineRegionClip> data) {
                                        mEditTimeline.setPipRegion(data);
                                    }
                                });
                                mEditOperationView.updateCancelRecoverVisible(true);
                                if (confirm) {
                                    saveOperation();
                                }
                            }
                        });
                if (mainTrackSelected()) {
                    mEditTimeline.updateSpanViewContent(false);
                } else {
                    //画中画
                    //Picture-In-Picture
                    mEditorTrackView.setPipDuringVisiableStatus(false);
                }
            }

        } else if (R.string.menu_sub_tab_change_speed_curve == item.getTitleId()) {
            //曲线变速
            //Curve speed
            hideKeyframeInTrack();
            mBottomViewHelper.showSpeedCurveMenu(mCurSelectVideoClip, new BottomEventListener() {
                @Override
                public void onDismiss(boolean confirm) {
                    super.onDismiss(confirm);
                    showKeyframeInTrack();
                    if (mCurSelectVideoClip != null) {
                        List<CurveSpeed> speedList = mCurSelectVideoClip.getCurveSpeedList();
                        if (speedList != null) {
                            speedList.clear();
                        }
                    }
                    mBottomViewContainer.dismissPopView();
                    mEditOperationView.updateCancelRecoverVisible(true);
                }
            });
        }
    }

    /**
     * Hide keyframe in track
     * 隐藏轨道中的关键帧
     */
    private void hideKeyframeInTrack() {
        mEditTimeline.hideKeyFrame();
        mEditorTrackView.hideKeyframe();
        /*
         * 隐藏关键帧和关键帧曲线按钮
         * Hide keyframe and keyframe curve button.
         */
        mEditOperationView.notShowKeyFrameView();
    }

    /**
     * Show keyframe in track
     * 显示轨道只的关键帧
     */
    private void showKeyframeInTrack() {
        mEditorTrackView.showKeyframe();
        mEditTimeline.showKeyFrame();

        if (!showAtomicEditMenuViewKeyFrame()) {
            /*
             * 显示关键帧和关键帧曲线按钮
             * Show keyframe and keyframe curve button.
             */
            mEditOperationView.showKeyFrameView();
        }

    }

    /**
     * 点击动画导航栏
     * clicked animate navigation bar
     *
     * @param item The children of navigation bar
     */
    private void clickNavigationAnimate(Navigation.Item item) {
        if (mCurSelectVideoClip != null) {
            hideKeyframeInTrack();
            mBottomViewHelper.showAnimationView(getString(item.getTitleId()), mCurSelectVideoClip, new VideoClipAnimationFragment.OnEventListener() {
                @Override
                public void onConfirm() {
                    showKeyframeInTrack();
                    mEditOperationView.notShowKeyFrameView();
                    mBottomViewContainer.dismissView();
                    long timelineCurrentPosition = mPresenter.getTimelineCurrentPosition();
                    BaseItemView baseItemView = mEditorTrackView.getDragView();
                    if (baseItemView != null) {
                        baseItemView.addVideoAnimationView();
                        baseItemView.checkKeyFrame(timelineCurrentPosition);
                        updateKeyFrameStatus();
                    }
                    /*一般情况下，上边的子轨道和下边的主轨道互斥显示
                     * Generally, the upper sub-track and the lower main track are mutually exclusive
                     * */
//                    mEditTimeline.enableThumbnailCoverEditMode(false);
                    if (mNavigationBar.isShow(R.string.nb_animate2) && mainTrackSelected()) {
//                        mEditTimeline.showSpanView(timelineCurrentPosition);
                        ITrackClip mainSelectedClip = mEditTimeline.getMainSelectedClip();
                        if (mainSelectedClip != null) {
                            mEditTimeline.checkKeyFrameSelected(mPresenter.getTimelineCurrentPosition());
                            updateKeyFrameStatus();
                        }
                    }
                    mEditOperationView.updateCancelRecoverVisible(true);
                    saveOperation();
                }
            });
            //隐藏撤销，恢复
            //Hide Undo, Restore
            mEditOperationView.updateCancelRecoverVisible(false);
            mEditOperationView.notShowKeyFrameView();
        }
       /* if (item.getTitleId() == R.string.sub_menu_animation_in) {
            //入场动画
        } else if (item.getTitleId() == R.string.sub_menu_animation_out) {
            //出场动画
        } else if (item.getTitleId() == R.string.sub_menu_animation_group) {
            //组合动画
        }*/
    }

    /**
     * 点击滤镜导航栏
     * clicked filter navigation bar
     *
     * @param item The children of navigation bar
     */
    private void clickNavigationFilter(Navigation.Item item) {
        //滤镜/调节
        //Filter/Adjust
        toOtherMenu();
        if (item.getTitleId() == R.string.sub_menu_add_filter) {
            //新增滤镜
            //Add filter
            mCurrSelectedFilterAndAdjustClip = null;
            mCurrSelectedVideoFx = null;
            mBottomViewHelper.showFilterMenu(null, null, new BottomEventListener() {
                @Override
                public void onDismiss(boolean confirm) {
                    super.onDismiss(confirm);
                    updateFilterAndAdjustTrack();
                    if (mCurrSelectedFilterAndAdjustClip != null) {
                        mEditorTrackView.setSelect(mCurrSelectedFilterAndAdjustClip.getTrackIndex(), mCurrSelectedFilterAndAdjustClip.getInPoint());
                    }
                }
            });
        } else if (item.getTitleId() == R.string.sub_menu_add_adjust) {
            //新增调节
            //Add adjust
            mCurrSelectedFilterAndAdjustClip = null;
            mCurrSelectedVideoFx = null;
            mBottomViewHelper.showAdjustMenu(null, null, new BottomEventListener() {
                @Override
                public void onDismiss(boolean confirm) {
                    super.onDismiss(confirm);
                    updateFilterAndAdjustTrack();
                    if (mCurrSelectedFilterAndAdjustClip != null) {
                        mEditorTrackView.setSelect(mCurrSelectedFilterAndAdjustClip.getTrackIndex(), mCurrSelectedFilterAndAdjustClip.getInPoint());
                    }
                }
            });
        }
    }

    /**
     * 点击特效导航栏
     * clicked effect navigation bar
     *
     * @param item The children of navigation bar
     */
    private void clickNavigationEffect(Navigation.Item item) {
        if (item.getTitleId() == R.string.effect_add) {
            //特效一级导航栏
            //Effect Navigation Bar
            toOtherMenu();
            mCurrSelectedVideoFx = null;
            mMultiHelper.showEffectView(null);
            mEditOperationView.updateCancelRecoverVisible(false);
        }
        if (item.getTitleId() == R.string.effect_plug_add) {
            //特效一级导航栏，自定义插件特效
            //Effect Navigation Bar,Custom plug-in special effects
            toOtherMenu();
            mCurrSelectedVideoFx = null;
            mCurSelectVideoClip = null;
            showPlusMenu();

        } else if (item.getTitleId() == R.string.sub_menu_effect_edit_copy) {
            //复制特效
            //Copy effect
            if (mEditorEngine.copyEffect(mSelectTrackData, this) == EditorEngine.ReturnCode.CODE_CAN_NOT_OPERATE) {
                ToastUtils.showShort(R.string.unusable_space);
            }
        } else if (item.getTitleId() == R.string.sub_menu_effect_edit_target) {
            //作用对象
            //Effect target
            mEditOperationView.updateCancelRecoverVisible(false);
            mBottomViewHelper.showEffectTargetMenu(mCurrSelectedVideoFx, new MYEffectTargetMenuView.OnEventChangedListener() {
                @Override
                public void onMenuClicked(MYEffectTargetMenuView.TargetInfo targetInfo) {
                    boolean success = mPresenter.changeTimelineFxToTarget(mCurrSelectedVideoFx, targetInfo);
                    if (success) {
                        mEditorEngine.playVideo(mCurrSelectedVideoFx.getInPoint(), mCurrSelectedVideoFx.getOutPoint());
                    }
                    mEditorTrackView.updateTagView();
                    saveOperation();
                    mBottomViewHelper.getView().dismissView();
                    mEditOperationView.updateCancelRecoverVisible(true);
                }

                @Override
                public void onConfirm() {
                    mBottomViewHelper.getView().dismissView();
                    mEditOperationView.updateCancelRecoverVisible(true);
                }
            });
        } else if (item.getTitleId() == R.string.sub_menu_effect_edit_delete) {
            //删除特效
            //Delete effect
            mEditorEngine.deleteEffect(mSelectTrackData);
            mCurrSelectedVideoFx = null;
            mNavigationBar.show(R.string.nb_effect1);
            mPresenter.checkTrackDuration();
        } else if (item.getTitleId() == R.string.sub_menu_effect_edit_replace) {
            //替换特效
            //Replace effect
            mMultiHelper.showEffectView(mCurrSelectedVideoFx);
            mEditOperationView.updateCancelRecoverVisible(false);
        } else if (item.getTitleId() == R.string.sub_menu_edit_bedspread) {
            changeBaseItemDuration(mSelectTrackData);
        }

    }

    /**
     * 点击贴纸、字幕、组合字幕导航栏
     * clicked sticker or caption navigation bar
     *
     * @param item The children of navigation bar
     */
    private void clickNavigationSticker(Navigation.Item item) {
        if (item.getTitleId() == R.string.sub_menu_caption_sticker) {
            //贴纸
            //Sticker
            PointF liveWindrowSize = mEditorEngine.getLiveWindrowSize(mVideoFragment.getLiveWindow());
            mMultiHelper.showStickerView((int) liveWindrowSize.x, (int) liveWindrowSize.y);
            mEditOperationView.updateCancelRecoverVisible(false);
        } else if (item.getTitleId() == R.string.sub_menu_caption) {
            //字幕
            //Caption
            showCaptionView();
        } else if (item.getTitleId() == R.string.identification_caption) {
            //识别字幕
            //Identify cation
            showIdentifyDialog();
        } else if (item.getTitleId() == R.string.sub_menu_caption_mould) {
            //字幕模板
            //Caption mould
            mEditOperationView.updateCancelRecoverVisible(false);
            mCurrSelectedCaptionStickClip = null;
            mBottomViewHelper.showCaptionMouldView(new BottomEventListener() {
                @Override
                public void onDismiss(boolean confirm) {
                    mEditOperationView.updateCancelRecoverVisible(true);
                    saveOperation();
                    mVideoFragment.resetFxEditMode();
                    updateStickerTrack();
                    mBottomViewContainer.dismissFragment();
                    mCurrSelectedCaptionStickClip = null;
                }

                @Override
                public void onItemClick(IBaseInfo baseInfo, boolean applyAll) {
                    mEditorEngine.addCompoundCaption(mCurrSelectedCaptionStickClip, baseInfo.getPackageId());
                }
            });
        }
    }

    /**
     * 点击贴纸编辑导航栏
     * clicked edit sticker  navigation bar
     *
     * @param item The children of navigation bar
     */
    private void clickNavigationEditSticker(Navigation.Item item) {
        if (item.getTitleId() == R.string.sub_menu_sticker_edit_delete) {
            //删除
            //Delete
            deleteCaptionSicker();
        } else if (item.getTitleId() == R.string.sub_menu_sticker_edit_mirror) {
            //镜像
            //Mirror
            mEditorEngine.changeStickerMirror(mCurrSelectedCaptionStickClip);
            mVideoFragment.openFxEditMode(EDIT_MODE_STICKER, mCurrSelectedCaptionStickClip, true);
        } else if (item.getTitleId() == R.string.sub_menu_sticker_edit_copy) {
            //复制
            //Copy
            copyCaptionSicker();

        } else if (item.getTitleId() == R.string.sub_menu_sticker_edit_voice) {
            //音效
            //Voice
            mVideoFragment.changeStickerVolume();
        } else if (item.getTitleId() == R.string.sub_menu_name_edit_animation) {
            if (mCurrSelectedCaptionStickClip instanceof MeicamStickerClip) {
                mBottomViewHelper.showStickerAnimationView((MeicamStickerClip) mCurrSelectedCaptionStickClip, new StickerAnimationFragment.OnEventListener() {
                    @Override
                    public void onConfirm() {
                        if (mCurrSelectedCaptionStickClip != null) {
                            mEditorEngine.seekTimeline(mCurrSelectedCaptionStickClip.getInPoint(), 0);
                            seekViewOnPlay(mCurrSelectedCaptionStickClip.getInPoint());
                        }
                        saveOperation();
                    }
                });
            }
        } else if (item.getTitleId() == R.string.sub_menu_edit_bedspread) {
            changeBaseItemDuration(mSelectTrackData);
        }
    }

    /**
     * 点击字幕编辑导航栏
     * clicked edit caption navigation bar
     *
     * @param item The children of navigation bar
     */
    private void clickNavigationEditCaption(Navigation.Item item) {
        if (item.getTitleId() == R.string.sub_menu_caption_edit_delete) {
            //删除
            //Delete
            deleteCaptionSicker();
        } else if (item.getTitleId() == R.string.sub_menu_caption_edit_style) {
            //样式
            //Style
            showCaptionView();
        } else if (item.getTitleId() == R.string.sub_menu_caption_edit_copy) {
            //复制
            //Copy
            copyCaptionSicker();
        } else if (item.getTitleId() == R.string.sub_menu_edit_bedspread) {
            changeBaseItemDuration(mSelectTrackData);
        }
    }

    /**
     * 点击组合字幕编辑导航栏
     * clicked edit combination caption  navigation bar
     *
     * @param item The children of navigation bar
     */
    private void clickNavigationEditCombinationCaption(Navigation.Item item) {
        if (item.getTitleId() == R.string.sub_menu_compound_caption_edit_copy) {
            //复制
            //Copy
            copyCaptionSicker();
        } else if (item.getTitleId() == R.string.sub_menu_compound_caption_edit_delete) {
            //删除
            //Delete
            deleteCaptionSicker();
        } else if (item.getTitleId() == R.string.sub_menu_edit_bedspread) {
            changeBaseItemDuration(mSelectTrackData);
        }
    }

    /**
     * 点击滤镜编辑导航栏
     * clicked edit filter  navigation bar
     *
     * @param item The children of navigation bar
     */
    private void clickNavigationEditFilter(Navigation.Item item) {

        if (item.getTitleId() == R.string.sub_menu_replace_filter) {
            //替换滤镜
            //Replace filter
            if (mCurrSelectedFilterAndAdjustClip != null) {
                mBottomViewHelper.showFilterMenu(null, mCurrSelectedFilterAndAdjustClip, new BottomEventListener() {
                    @Override
                    public void onDismiss(boolean confirm) {
                        super.onDismiss(confirm);
                        updateFilterAndAdjustTrack();
                        if (mCurrSelectedFilterAndAdjustClip != null) {
                            mEditorTrackView.setSelect(mCurrSelectedFilterAndAdjustClip.getTrackIndex(), mCurrSelectedFilterAndAdjustClip.getInPoint());
                        }
                    }
                });
            }


        } else if (item.getTitleId() == R.string.sub_menu_name_edit_delete) {
            //删除
            //Delete
            deleteFilterAndAdjustClip();
        } else if (item.getTitleId() == R.string.sub_menu_edit_bedspread) {
            changeBaseItemDuration(mSelectTrackData);
        }
    }

    /**
     * 点击调节编辑导航栏
     * clicked edit adjust navigation bar
     *
     * @param item The children of navigation bar
     */
    private void clickNavigationEditAdjust(Navigation.Item item) {
        if (item.getTitleId() == R.string.sub_menu_replace_adjust) {
            //调节
            //Adjust
            if (mCurrSelectedFilterAndAdjustClip != null) {
                mBottomViewHelper.showAdjustMenu(null, mCurrSelectedFilterAndAdjustClip, new BottomEventListener() {
                    @Override
                    public void onDismiss(boolean confirm) {
                        super.onDismiss(confirm);
                        updateFilterAndAdjustTrack();
                        if (mCurrSelectedFilterAndAdjustClip != null) {
                            mEditorTrackView.setSelect(mCurrSelectedFilterAndAdjustClip.getTrackIndex(), mCurrSelectedFilterAndAdjustClip.getInPoint());
                        }
                    }
                });
            }
        } else if (item.getTitleId() == R.string.sub_menu_name_edit_delete) {
            //删除
            //Delete
            deleteFilterAndAdjustClip();
        } else if (item.getTitleId() == R.string.sub_menu_edit_bedspread) {
            changeBaseItemDuration(mSelectTrackData);
        }
    }


    /**
     * 点击水印导航栏
     * clicked watermark  navigation bar
     *
     * @param item The children of navigation bar
     */
    private void clickNavigationWatermark(Navigation.Item item) {
        if (item.getTitleId() == R.string.sub_menu_tab_watermark) {
            //水印
            //Water mark
            mBottomViewHelper.showWaterView(new BottomEventListener() {
                @Override
                public void onDismiss(boolean confirm) {
                    mBottomViewContainer.dismissFragment();
                    if (confirm) {
                        saveOperation();
                    }
                }

                @Override
                public void onItemClick(IBaseInfo baseInfo, boolean applyAll) {
                    mVideoFragment.openFxEditMode(EDIT_MODE_WATERMARK, null, true);
                    mVideoFragment.updateEditFx(baseInfo);
                }
            });
            mVideoFragment.openFxEditMode(EDIT_MODE_WATERMARK, null, true);
        } else if (item.getTitleId() == R.string.sub_menu_tab_watermark_effect) {
            //效果
            //Effect
            mBottomViewHelper.showWaterEffectView(new BottomEventListener() {
                @Override
                public void onDismiss(boolean confirm) {
                    mBottomViewContainer.dismissFragment();
                    if (confirm) {
                        saveOperation();
                    }
                }

                @Override
                public void onItemClick(IBaseInfo baseInfo, boolean applyAll) {
                    mVideoFragment.openFxEditMode(EDIT_MODE_WATERMARK_EFFECT, null, true);
                    mVideoFragment.updateEditFx(baseInfo);
                }

                @Override
                public void onProgressChanged(int progress, boolean fromUser, int type) {
                    if (fromUser) {
                        if (type == Constants.MOSAIC_DEGREE) {
                            //马赛克程度
                            //Mosaic degree
                            mVideoFragment.updateEditFxProperty(new MeicamFxParam<>(String.valueOf(type),
                                    null, progress / 100.0f));
                        } else if (type == Constants.MOSAIC_NUM) {
                            //马赛克数量
                            //Mosaic count
                            mVideoFragment.updateEditFxProperty(new MeicamFxParam<>(String.valueOf(type),
                                    null, progress / 1000.0f));
                        } else if (type == Constants.BLUR) {
                            //模糊程度
                            //Blur degree
                            mVideoFragment.updateEditFxProperty(new MeicamFxParam<>(String.valueOf(type),
                                    null, progress * 0.64f));
                        }
                    }
                }
            });
            mVideoFragment.openFxEditMode(EDIT_MODE_WATERMARK_EFFECT, null, true);
        }
    }

    /**
     * 点击音频导航栏
     * clicked audio  navigation bar
     *
     * @param item The children of navigation bar
     */
    private void clickNavigationAudio(Navigation.Item item) {
        if (item.getTitleId() == R.string.sub_add_voice_music) {
            //音乐
            //Music
            if (Utils.isFastClick()) {
                return;
            }
            AppManager.getInstance().jumpActivityForResult(this, SelectMusicActivity.class, null, SELECT_MUSIC);
            overridePendingTransition(R.anim.bottom_in, R.anim.bottom_silent);
        } else if (item.getTitleId() == R.string.sub_menu_name_record) {
            //录制
            //Record
            mBottomViewHelper.showRecordView(new MYRecordMenuView.OnRecordListener() {
                @Override
                public void onStartRecord() {
                    if (PermissionUtils.isGranted(android.Manifest.permission.RECORD_AUDIO)) {
                        if (mAudioRecordManager != null) {
                            mAudioRecordManager.startRecord(DraftEditActivity.this);
                        }
                    } else {
                        PermissionUtils.permission(MICROPHONE)
                                .callback(new PermissionUtils.FullCallback() {
                                    @Override
                                    public void onGranted(@NonNull List<String> granted) {
                                        if (mAudioRecordManager != null) {
                                            mAudioRecordManager.startRecord(DraftEditActivity.this);
                                        }
                                    }

                                    @Override
                                    public void onDenied(@NonNull List<String> deniedForever, @NonNull List<String> denied) {
                                        if (deniedForever.size() > 0) {
                                            ToastUtils.showLong(R.string.lack_of_record);
                                        }
                                    }
                                })
                                .request();
                    }
                }

                @Override
                public void onStopRecord() {
                    if (PermissionUtils.isGranted(android.Manifest.permission.RECORD_AUDIO)) {
                        if (mAudioRecordManager != null) {
                            mAudioRecordManager.stopRecord();
                        }
                    }
                }

                @Override
                public void onDismiss(boolean confirm) {
                    mBottomViewContainer.dismissView();
                }
            });
        } else if (item.getTitleId() == R.string.sub_menu_name_analysis) {
            //提取
            //Analysis
            if (Utils.isFastClick()) {
                return;
            }
            Bundle photoAlbumBundle = new Bundle();
            photoAlbumBundle.putInt(MEDIA_TYPE, MediaData.TYPE_VIDEO);
            AppManager.getInstance().jumpActivityForResult(this, MaterialSingleSelectActivity.class, photoAlbumBundle, REQUEST_CODE_2);
        }

    }

    /**
     * 点击音频编辑导航栏
     * clicked  edit audio  navigation bar
     *
     * @param item The children of navigation bar
     */
    private void clickNavigationEditAudio(Navigation.Item item) {
        if (item.getTitleId() == R.string.sub_menu_audio_edit_divide) {
            //分割
            //Split
            int returnCode = mEditorEngine.audioEditCutClip(this);
            if (returnCode == EditorEngine.ReturnCode.CODE_CAN_NOT_OPERATE) {
                ToastUtils.showShort(R.string.audio_unable_cut);
            }
        } else if (item.getTitleId() == R.string.sub_menu_audio_edit_speed) {
            //变速
            //Speed
            if (mCurrSelectedAudioClip != null) {
                mBottomViewHelper.showNormalSpeed((float) mCurrSelectedAudioClip.getSpeed(), mCurrSelectedAudioClip.isKeepAudioPitch(),
                        new EditChangeSpeedView.ChangeSpeedListener() {
                            @Override
                            public void onSpeedChange(float speed, boolean changeVoice) {
                                mEditorEngine.audioEditChangeClipSpeed(speed, changeVoice);
                                refreshAudioView();
                                mEditorTrackView.setSelect(mCurrSelectedAudioClip.getTrackIndex(), mCurrSelectedAudioClip.getInPoint());
                                mPresenter.checkTrackDuration();
                            }

                            @Override
                            public void onDismiss(boolean confirm) {
                                mBottomViewContainer.dismissView();
                                if (confirm) {
                                    saveOperation();
                                }
                            }
                        });
            }
        } else if (item.getTitleId() == R.string.sub_menu_audio_edit_volume) {
            //音量
            //Volume
            if (mCurrSelectedAudioClip != null) {
                mBottomViewHelper.showAdjustSeekBar(Constants.maxVolumeProgress, (int) (mCurrSelectedAudioClip.getVolume() * Constants.maxVolumeProgress / Constants.maxNvVolume),
                        R.string.sub_menu_audio_edit_volume, mCurrSelectedAudioClip.getType(), new BottomEventListener() {
                            @Override
                            public void onDismiss(boolean confirm) {
                                mBottomViewContainer.dismissView();
                                if (confirm) {
                                    saveOperation();
                                }
                            }

                            @Override
                            public void onProgressChanged(int progress, boolean fromUser, int type) {
                                mEditorEngine.audioEditChangeVolume(progress * Constants.maxNvVolume / Constants.maxVolumeProgress);
                            }
                        });
            }
        } else if (item.getTitleId() == R.string.sub_menu_audio_edit_copy) {
            //复制
            //Copy
            mEditorEngine.audioEditCopyClip(this);
        } else if (item.getTitleId() == R.string.sub_menu_audio_edit_delete) {
            //删除
            //Delete
            mEditorEngine.audioEditDeleteClip(mCurrSelectedAudioClip);
        } else if (item.getTitleId() == R.string.sub_menu_audio_transition) {
            //转场
            //Transition
            if (mCurrSelectedAudioClip != null) {
                mBottomViewHelper.showTransitionView(mCurrSelectedAudioClip, new EditChangeTransitionView.TransitionChangeListener() {
                    @Override
                    public void onProgressChange(long fadeInProgress, long fadeOutProgress) {
                        mEditorEngine.audioEditTransition(fadeInProgress, fadeOutProgress);
                        refreshAudioView();
                        mEditorTrackView.setSelect(mCurrSelectedAudioClip.getTrackIndex(), mCurrSelectedAudioClip.getInPoint());
                    }

                    @Override
                    public void onDismiss(boolean confirm) {
                        mBottomViewContainer.dismissView();
                        if (confirm) {
                            saveOperation();
                        }
                    }
                });
            }
        } else if (item.getTitleId() == R.string.sub_menu_audio_edit_change_voice) {
            //变声
            //Tone
            mBottomViewHelper.showChangeVoiceView(mCurrSelectedAudioClip, new BottomEventListener() {
                @Override
                public void onDismiss(boolean confirm) {
                    mBottomViewContainer.dismissView();
                    if (confirm) {
                        saveOperation();
                    }
                }

                @Override
                public void onItemClick(IBaseInfo baseInfo, boolean applyAll) {
                    mEditorEngine.audioEditChangeVoice(baseInfo.getEffectId());
                }
            });
        }

    }

    /**
     * 点击背景导航栏
     * clicked background navigation bar
     *
     * @param item The children of navigation bar
     */
    private void clickNavigationBackground(Navigation.Item item) {
        if (item.getTitleId() == R.string.sub_menu_canvas_color) {
            //画布颜色
            //Canvas color
            mBottomViewHelper.showCanvasColor(mCurSelectVideoClip, new BottomEventListener() {
                @Override
                public void onDismiss(boolean confirm) {
                    mBottomViewContainer.dismissView();
                }

                @Override
                public void onItemClick(IBaseInfo baseInfo, boolean applyAll) {
                    if (applyAll) {
                        mEditorEngine.applyAllBlurBackground(baseInfo.getCommonInfo(), STORYBOARD_BACKGROUND_TYPE_COLOR);
                        ToastUtils.showShort(getResources().getString(R.string.has_been_apply_to_all));
                    } else {
                        long timeStamp = EditorEngine.getInstance().getCurrentTimelinePosition();
                        MeicamVideoClip editVideoClip = EditorEngine.getInstance().getEditVideoClip(timeStamp, TRACK_INDEX_MAIN);
                        mEditorEngine.updateBlurAndColorBackground(editVideoClip, baseInfo.getCommonInfo(), STORYBOARD_BACKGROUND_TYPE_COLOR);
                    }
                    saveOperation();
                }
            });
        } else if (item.getTitleId() == R.string.sub_menu_canvas_style) {
            //画布样式
            //Canvas style
            AssetsManager assetsManager = AssetsManager.get();
            assetsManager.setAssetsProxy(new BackgroundProxy());
            mBottomViewHelper.showCanvasStyle(mCurSelectVideoClip, new BottomEventListener() {
                @Override
                public void onDismiss(boolean confirm) {
                    mBottomViewContainer.dismissView();
                    AssetsManager assetsManager = AssetsManager.get();
                    assetsManager.setAssetsProxy(new AssetsUserManager());
                }

                @Override
                public void onItemClick(IBaseInfo baseInfo, boolean applyAll) {
                    if (applyAll) {
                        mEditorEngine.applyAllImageBackground(baseInfo.getAssetPath(), STORYBOARD_BACKGROUND_TYPE_IMAGE);
                        ToastUtils.showShort(getResources().getString(R.string.has_been_apply_to_all));
                    } else {
                        long timeStamp = EditorEngine.getInstance().getCurrentTimelinePosition();
                        MeicamVideoClip editVideoClip = EditorEngine.getInstance().getEditVideoClip(timeStamp, TRACK_INDEX_MAIN);
                        if (TextUtils.isEmpty(baseInfo.getAssetPath())) {
                            if (baseInfo.getCoverPath().equals(ResourceUtils.getMipmapToUri(R.mipmap.ic_canvas_add_resource))) {
                                if (Utils.isFastClick()) {
                                    return;
                                }
                                Bundle bundle = new Bundle();
                                bundle.putInt(MEDIA_TYPE, MediaData.TYPE_PHOTO);
                                ArrayList<String> filter = new ArrayList<>();
                                filter.add(MediaData.TYPE_FILTER_GIF);
                                bundle.putStringArrayList(MEDIA_FILTER, filter);
                                AppManager.getInstance().jumpActivityForResult(DraftEditActivity.this,
                                        MaterialSingleSelectActivity.class, bundle, REQUEST_SELECT_IMAGE_BACKGROUND_CODE);
                            } else {
                                mEditorEngine.deleteBackground(editVideoClip, true);
                            }
                        } else {
                            mEditorEngine.updateBlurAndColorBackground(editVideoClip, baseInfo.getAssetPath(), STORYBOARD_BACKGROUND_TYPE_IMAGE);
                        }
                    }
                    saveOperation();
                }
            });
        } else if (item.getTitleId() == R.string.sub_menu_canvas_blur) {
            //画布模糊
            //Canvas blur
            mBottomViewHelper.showCanvasBlur(mCurSelectVideoClip, new BottomEventListener() {
                @Override
                public void onDismiss(boolean confirm) {
                    mBottomViewContainer.dismissView();
                }

                @Override
                public void onItemClick(IBaseInfo baseInfo, boolean applyAll) {
                    if (applyAll) {
                        mEditorEngine.applyAllBlurBackground(String.valueOf(baseInfo.getEffectStrength()), STORYBOARD_BACKGROUND_TYPE_BLUR);
                        ToastUtils.showShort(getResources().getString(R.string.has_been_apply_to_all));
                    } else {
                        long timeStamp = EditorEngine.getInstance().getCurrentTimelinePosition();
                        MeicamVideoClip editVideoClip = EditorEngine.getInstance().getEditVideoClip(timeStamp, TRACK_INDEX_MAIN);
                        mEditorEngine.updateBlurAndColorBackground(editVideoClip, String.valueOf(baseInfo.getEffectStrength()), STORYBOARD_BACKGROUND_TYPE_BLUR);
                    }
                    saveOperation();
                }
            });
        }
    }

    /**
     * 点击比例导航栏
     * clicked ratio navigation bar
     *
     * @param item The children of navigation bar
     */
    private void clickNavigationRatio(Navigation.Item item) {
        NvsVideoResolution originRes = mEditorEngine.getVideoResolution();
        int originWidth = originRes.imageWidth;
        int originHeight = originRes.imageHeight;
        mVideoFragment.restoreWaterList();
        mEditorEngine.changeRatio((int) item.getTag());
        NvsVideoResolution targetRes = mEditorEngine.getVideoResolution();
        int targetWidth = targetRes.imageWidth;
        int targetHeight = targetRes.imageHeight;
        float widthRatio = targetWidth * 1.0f / originWidth;
        float heightRatio = targetHeight * 1.0f / originHeight;
        PointF liveWindrowSize = EditorEngine.getInstance().getLiveWindrowSize(mVideoFragment.getLiveWindow());
        int width = (int) liveWindrowSize.x;
        int height = (int) liveWindrowSize.y;
        mVideoFragment.setLiveWindowRatio();
        mVideoFragment.changeRatioAddWaterToTimeline(width, height);
        mEditorEngine.changeCaptionPosition(widthRatio, heightRatio);
        mVideoFragment.updateTransformFx(mCurSelectVideoClip, true);
        mEditorEngine.seekTimeline(0);
        saveOperation();
    }

    /**
     * 显示公共弹窗
     * Show common dialog
     *
     * @param title        The title
     * @param tip          The tip
     * @param leftConfirm  The leftConfirm
     * @param rightConfirm The rightConfirm
     */
    private void showCommonPop(String title, String tip, String leftConfirm, String rightConfirm) {
        if (mCommonPop == null) {
            mCommonPop = (CommonPop) new XPopup.Builder(this)
                    .dismissOnBackPressed(false)
                    .dismissOnTouchOutside(false)
                    .asCustom(new CommonPop(this));
        }
        mCommonPop.show(title, tip, leftConfirm, rightConfirm);
    }

    /**
     * 显示取消智能抠像的弹窗
     * Show cancel smart keyer dialog
     */
    private void showCancelSmartKeyerPop() {
        if (mCancelSmartKeyerPop == null) {
            mCancelSmartKeyerPop = CancelSmartKeyerPop.create(new CancelSmartKeyerPop.EventListener() {
                @Override
                public void onResult(boolean confirm) {
                    if (confirm) {
                        mPresenter.cancelSmartKeyer();
                    }
                }
            }, this);
        }
        mCancelSmartKeyerPop.show();
    }

    /**
     * 展示识别字幕弹窗
     * Show identify dialog
     */
    private void showIdentifyDialog() {
        if (VoiceDictationHelperWrapper.get().isListening()) {
            ToastUtils.showShort(R.string.identifying_caption);
            return;
        }
        if (!NetUtils.isNetworkAvailable(this)) {
            ToastUtils.showShort(R.string.identify_net_not_available);
            return;
        }
        if (mIdentifyCaptionDlg == null) {
            mIdentifyCaptionDlg = new IdentifyCaptionDlg(this);
        }
        mIdentifyCaptionDlg.show(mPresenter.hasRecord(), new VoiceDictationHelperWrapper.SpeechWrapListener() {

            @Override
            public List<List<VoiceParam>> onPrepare(int type) {
                List<List<VoiceParam>> voiceParamList = mPresenter.getVoiceParamList(type);
                if (voiceParamList == null || voiceParamList.size() == 0) {
                    ToastUtils.showShort(R.string.not_identify_voices);
                }
                return voiceParamList;
            }

            @Override
            public void onSampleResult(List<Speech> resultList) {
                if (!isActive()) {
                    return;
                }
                if (resultList == null) {
                    mTopViewHelper.showIdentifyView(false, true);
                    ToastUtils.make()
                            .setGravity(Gravity.CENTER, 0, 0)
                            .setDurationIsLong(false)
                            .show(R.string.not_identify_voices);
                    return;
                }
                boolean isEmpty = true;
                for (Speech result : resultList) {
                    if (result != null) {
                        List<Speech.Text> textList = result.getText();
                        if (textList != null) {
                            for (Speech.Text text : textList) {
                                if (!TextUtils.isEmpty(text.getText())) {
                                    isEmpty = false;
                                    break;
                                }
                            }
                        }
                    }
                }
                if (isEmpty) {
                    mTopViewHelper.showIdentifyView(false, true);
                    ToastUtils.make()
                            .setGravity(Gravity.CENTER, 0, 0)
                            .setDurationIsLong(false)
                            .show(R.string.not_identify_voices);
                    return;
                }
                if (mIdentifyCaptionDlg.clearCaption()) {
                    mEditorEngine.removeAllAICaption();
                }

                for (int index = 0; index < resultList.size(); index++) {
                    Speech result = resultList.get(index);
                    if (result == null) {
                        continue;
                    }
                    List<Speech.Text> textList = result.getText();
                    if (textList != null) {
                        int aiTrackIndex = mEditorEngine.getAITrackIndex();
                        for (Speech.Text text : textList) {
                            if (result.isPunctuation(text.getText()) || TextUtils.isEmpty(text.getText())) {
                                continue;
                            }
                          /*  boolean needAddCaption = mPresenter.isNeedAddCaption(text.getBg() * 1000,
                                    text.getDuration() * 1000);
                            if (needAddCaption) {
                            }*/
                            //如果是需要startListener(VoiceParam,.)需要添加param.getInP();
                            //注意与startListener(List<VoiceParam>,.)参数获取结果的区别
                            //If startListener (VoiceParam,.) is required, param.getInP() needs to be added;
                            // Note the difference between the results obtained with the startListener (List<VoiceParam>,.)
                            MeicamCaptionClip captionClip = mEditorEngine.addCaption(text.getText(), text.getBg() * 1000,
                                    text.getDuration() * 1000, aiTrackIndex, false, Constants.TYPE_AI_CAPTION, false);
                            if (captionClip != null) {
                                mEditorEngine.changeAiCaptionPositionAndSize(captionClip, index);
                            }
                        }
                    }
                }
                saveOperation();
                mTopViewHelper.showIdentifyView(true, true);
                if (mNavigationBar.isShow(R.string.nb_sticker1)) {
                    mEditorEngine.seekTimeline(0, 0);
                    onTimelineChanged(mEditorEngine.getCurrentTimeline(), false);
                    mPresenter.checkTrackDuration();
                    // ToastUtils.showShort(R.string.identify_success_turn_to_caption);
                }
            }

            @Override
            public void onBeginOfSpeech() {
                if (!isActive()) {
                    return;
                }
                mTopViewHelper.showIdentifyView(false, false);
            }

            @Override
            public void onError(SpeechError speechError) {
                LogUtils.e("speechError=" + speechError);
                mTopViewHelper.showIdentifyView(false, true);
                ToastUtils.make()
                        .setGravity(Gravity.CENTER, 0, 0)
                        .setDurationIsLong(false)
                        .show(R.string.not_identify_voices);
            }
        });
    }

    private boolean isActive() {
        return !isFinishing();
    }

    private boolean isActivityStopped() {
        return mActivityStopped;
    }

    @Override
    public void onClick(View view) {
        int id = view.getId();
        if (id == R.id.editor_parent_view) {
            LogUtils.d("onClick: Click the blank area");
            if (mNavigationBar.isShow(R.string.nb_ratio1)) {
                return;
            }
            if (mNavigationBar.isShow(R.string.nb_video_edit1) || mNavigationBar.isShow(R.string.nb_picture_edit1)) {
                mEditTimeline.notShowSpanView();
                mNavigationBar.show(R.string.nb_main0);
            }
        } else if (id == R.id.tv_export_video) {
            goToExportVideoView();
        } else if (id == R.id.iv_back_pressed) {
            onBackPressed(true);
            String desc = mTvProgressDesc.getText().toString();
            if (getResources().getString(R.string.reverting).equals(desc)) {
                mConvertFileManager.cancelConvert();
            }
        } else if (id == R.id.bt_compile_cancel) {
            String desc2 = mTvProgressDesc.getText().toString();
            if (getResources().getString(R.string.reverting).equals(desc2)) {
                mConvertFileManager.cancelConvert();
            }
        } else if (id == R.id.tv_export_template) {
            hideBottomView();
            if (!ConfigUtil.isToC()) {
                if (!mPresenter.isLogin()) {
                    showLoginDialogEx();
                } else {
                    mPresenter.checkAssets(true);
                }
            } else {
                goToExportTemplateView();
            }
        } else if (id == R.id.iv_login) {
            if (mPresenter.isLogin()) {
                IUserPlugin userPlugin = PluginManager.get().getUserPlugin();
                if (userPlugin != null) {
                    userPlugin.showLoginOut(this, mLoginView, this);
                }
            } else {
                IUserPlugin userPlugin = PluginManager.get().getUserPlugin();
                if (userPlugin != null) {
                    userPlugin.showLoginInConfirmPop(this, mLoginView, this);
                }
            }
        } else if (id == R.id.iv_cancel_smart_keyer) {
            showCancelSmartKeyerPop();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
        EngineCallbackManager.get().unregisterCallbackObserver(mEngineCallbackObserver);
        Application application = getApplication();
        if (application instanceof BaseApplication) {
            ((BaseApplication) application).unregisterBackgroundObserver(backgroundObserver);
        }
        mBottomViewHelper.detachView();
        mEditOperateManager.unregisterOperateObserver(mEditOperateObserver);
        mEditOperateManager.destroy();
        mEditorEngine.removeOnTimelineChangeListener(this);
        mEditorEngine.removeOnTrackChangeListener(mOnTrackChangedListener);
        mEditorEngine.clear();
        PixelPerMicrosecondUtil.resetScale();
        PixelPerMicrosecondUtil.removeAllListener();
        if (mDecorView != null) {
            mDecorView.getViewTreeObserver().removeOnGlobalLayoutListener(mOnGlobalLayoutListener);
        }
        if (mConvertFileManager != null) {
            mConvertFileManager.unregisterConvertFileObserver(convertFileObserver);
        }
        if (mAudioRecordManager != null) {
            mAudioRecordManager.unregisterConvertFileObserver(audioRecordObserver);
        }
        AssetsManager.get().clearCache();
    }

    /**
     * 展示登录弹窗
     * Show login dialog
     */
    private void showLoginDialogEx() {
        IUserPlugin userPlugin = PluginManager.get().getUserPlugin();
        if (userPlugin != null) {
            userPlugin.showLoginPop(this, this);
        }
    }

    private void seekViewOnPlay(long stamp) {
        if (mTimeline == null) {
            return;
        }
        // int x = PixelPerMicrosecondUtil.durationToLength(stamp);
        // 滑动缩略图控件
        // Slide the thumbnail control
        mEditTimeline.scrollTo(stamp);
        //解决在缩略图超出基准线以后，交换视频片段，重新选中，选中位置错位问题
        //Solve the problem of exchanging video clips, re-selecting and displacing the selected position after the thumbnail exceeds the baseline
        // mEditorTrackView.scrollToXView(x);
        mEditOperationView.setDurationText(FormatUtils.microsecond2Time(stamp) + "/" + FormatUtils.microsecond2Time(mTimeline.getDuration()));
    }

    /**
     * 设置当前的主轨的clip
     * Set the clip of the current main track
     */
    private void setCurrentMainTrackClip() {
        MeicamVideoClip mainTrackVideoClip = mPresenter.getMainTrackVideoClip();
        if (mainTrackVideoClip != null) {
            mCurSelectVideoClip = mainTrackVideoClip;
        }
    }

    /**
     * On main track change.
     */
    public void onMainTrackChange() {
        //mEditTimeline.initTimeMarkingLineView();
        //复制，底层会默认添加转场，要先进行转场删除 Copy, the underlying layer will add the transition by default, and the transition must be deleted first
        //TimelineUtil.clearAllBuildInTransition(mTimeline);
        //TimelineUtil.setTransition(mTimeline, TimelineDataUtil.getMainTrack().getTransitionInfoList());
        //mEditorTrackView.initWidth(mTimeline.getDuration());
    }

    private void switchToMainMenu() {
        mNavigationBar.show(R.string.nb_main0);
        mVideoFragment.closeFxEditMode();
        mVideoFragment.setTransformViewVisible(GONE);
        mEditorTrackView.clear();
        mEditorTrackView.toMainMenu();
        mEditTimeline.toMainMenu();
        mEditTimeline.notShowSpanView();
        mEditOperationView.notShowKeyFrameView();
        mPresenter.getMainTrackLineRegion();
        mEditTimeline.showEffectRegion(true, true, true);
    }

    /**
     * 显示生成视频的滚动窗
     * Displays a scroll window for the generated video
     */
    private void showCenterProgress(String content) {
        mRlCompileProgress.setFocusable(true);
        mRlCompileProgress.requestFocus();
        mRlCompileProgress.setVisibility(View.VISIBLE);
        mTvProgressDesc.setText(content);
    }

    private void hideCenterProgress() {
        mRlCompileProgress.setVisibility(View.GONE);
        mRlCompileProgress.setFocusable(false);
        mEditCompileProgress.setProgress(0);
        mTvCompileProgress.setText("0%");
        mTvProgressDesc.setText("");
    }

    private void setCenterProgress(final int i) {
        mEditCompileProgress.setProgress(i);
        String text = i + "%";
        mTvCompileProgress.setText(text);
    }

    @Override
    protected void onPause() {
        //mBeforeJumpTimelinePosition = mTimeline.getCurrentPosition();
        super.onPause();
    }

    /**
     * 检测是否在智能抠像中
     * smart keying
     */
    private boolean checkSmartKeying() {
        if (smartKeying()) {
            ToastUtils.showShort(R.string.wait_when_smart_eyer);
            return true;
        }
        return false;
    }

    /**
     * 预览的时候检测是否正在抠像中，并给出提示。
     * Check smart keying when need preview
     */
    private boolean checkSmartKeyingWhenPreview() {
        if (smartKeying()) {
            ToastUtils.showShort(R.string.no_preview_in_keying);
            return true;
        }
        return false;
    }

    /**
     * 智能抠像中
     * smart keying
     */
    private boolean smartKeying() {
        return mFlSmartKeyer.getVisibility() == VISIBLE;
    }

    @Override
    public void onPlayEventCallback(boolean isPlaying) {
        LogUtils.d("----onPlayEventCallback" + "isPlaying: " + isPlaying + "  currentPosition :" +
                mEditorEngine.getCurrentTimelinePosition() + "  mTimeline.getDuration() :" + mTimeline.getDuration());
        if (mBottomViewContainer.getShowView() instanceof EditChangeSpeedCurveView) {
            ((EditChangeSpeedCurveView) mBottomViewContainer.getShowView()).setPlayState(!isPlaying);
        }
        if (checkSmartKeying()) {
            return;
        }
        if (isPlaying) {
            mVideoFragment.stopEngine();
        } else {
            if (mBottomViewContainer.getShowView() instanceof EditChangeSpeedCurveView) {
                if (mCurSelectVideoClip == null) {
                    return;
                }
                mVideoFragment.playVideo(mCurSelectVideoClip.getInPoint(), mCurSelectVideoClip.getOutPoint());
            } else {
                mVideoFragment.playVideo(mEditorEngine.getCurrentTimelinePosition(), mTimeline.getDuration(), smartKeying() ? STREAMING_ENGINE_COMPILE_FLAG_BUDDY_HOST_VIDEO_FRAME : 0);
            }

        }
    }

    @Override
    public void onCancelEventCallback() {
        if (mPresenter.checkVoiceDictation()) {
            return;
        }
        TimelineEntity timelineData = mEditOperateManager.cancelOperate();
        if (timelineData != null) {
            CommandManager.CommandHolder commandHolder = CommandManager.readObjectFromFile(timelineData.getJson());
            if (commandHolder != null) {
                List<Command> data = commandHolder.getAllCommand();
                if (!CommonUtils.isEmpty(data)) {
                    for (int index = data.size() - 1; index >= 0; index--) {
                        Command datum = data.get(index);
                        datum.undo();
                    }
                }
            }
            mEditorEngine.seekTimeline(0);
            mCurSelectVideoClip = mPresenter.getVideoClip(mCurrentTrackIndex, mCurrentInPoint);
            mVideoFragment.hideOperationBox();
            mVideoFragment.hideVideoClipOperationBox();
            updateUI();
        }
    }

    @Override
    public void onRecoverEventCallback() {
        if (mPresenter.checkVoiceDictation()) {
            return;
        }
        TimelineEntity timelineData = mEditOperateManager.recoverOperate();
        if (timelineData != null) {
            CommandManager.CommandHolder commandHolder = CommandManager.readObjectFromFile(timelineData.getJson());
            if (commandHolder != null) {
                List<Command> data = commandHolder.getAllCommand();
                if (!CommonUtils.isEmpty(data)) {
                    for (Command datum : data) {
                        datum.doIt();
                    }
                }
            }
            mEditorEngine.seekTimeline(0);
            //mEditorEngine.restoreTimeline(timelineData.getJson(), mTimeline);
            mCurSelectVideoClip = mPresenter.getVideoClip(mCurrentTrackIndex, mCurrentInPoint);
            // TimelineData.getInstance().setSelectedMeicamClipInfo(mCurSelectVideoClip);
            mVideoFragment.hideOperationBox();
            mVideoFragment.hideVideoClipOperationBox();
            updateUI();
        }

    }

    private void updateUI() {
        LogUtils.d("showNavigationName=" + getString(mNavigationBar.getShowingNavigationName()));
        if (smartKeying()) {
            mPresenter.cancelSmartKeyer();
        }
        //  mVideoFragment.openMaskZoomEditMode(null, false);
        mEditTimeline.notShowSpanView();
        initEditorTimeline();
        if (mEditorTrackView.isShowTrackView()) {
            updateStickerTrack();
        }
        if (mNavigationBar.isShow(R.string.nb_effect2) || mNavigationBar.isShow(R.string.nb_effect1)) {
            updateTimelineFxTrack();
            mNavigationBar.show(R.string.nb_effect1);
        } else if (mNavigationBar.isShow(R.string.nb_filter1) || mNavigationBar.isShow(R.string.nb_filter2)
                || mNavigationBar.isShow(R.string.nb_adjust2)) {
            updateFilterAndAdjustTrack();
            mBottomViewContainer.dismissView();
            mNavigationBar.show(R.string.nb_filter1);
        } else if (mNavigationBar.isShow(R.string.nb_sticker2) || mNavigationBar.isShow(R.string.nb_caption2)
                || mNavigationBar.isShow(R.string.nb_combination_caption2) || mNavigationBar.isShow(R.string.nb_sticker1)) {
            //由二级导航栏返回一级导航栏
            //Return from the secondary navigation bar to the primary navigation bar
            mNavigationBar.show(R.string.nb_sticker1);
            mCurrSelectedCaptionStickClip = null;
            mVideoFragment.closeFxEditMode();
            updateStickerTrack();
            mEditTimeline.showEffectRegion(false, true, false);
            mPresenter.getMainTrackLineRegion();
        } else if (mNavigationBar.isShow(R.string.nb_pip1)) {
            mEditorTrackView.setData(TrackViewDataHelper.getInstance().getTrackData(CommonData.CLIP_IMAGE),
                    mEditorEngine.getCurrentTimeline().getDuration(), CommonData.CLIP_IMAGE);
            mPresenter.getPipLineRegionAsync(new DraftEditPresenter.LineRegionDataCallBack() {
                @Override
                public void onResultCallback(List<LineRegionClip> data) {
                    mEditTimeline.setPipRegion(data);
                }
            });
        } else if (mNavigationBar.isShow(R.string.nb_animate2)) {
            if (mainTrackSelected()) {
                if (mCurSelectVideoClip == null) {
                    return;
                }
                mEditTimeline.post(new Runnable() {
                    @Override
                    public void run() {
                        mEditTimeline.showSpanView(mPresenter.getTimelineCurrentPosition(), true);
                        ITrackClip mainSelectedClip = mEditTimeline.getMainSelectedClip();
                        if (mainSelectedClip == null || mCurSelectVideoClip == null) {
                            return;
                        }
                        AnimationInfo animationInfo = ((ThumbnailClip) mainSelectedClip).getAnimationInfo();
                        AnimationData animationData = mEditorEngine.getVideoClipAnimation(mCurSelectVideoClip);
                        animationInfo = animationInfo.copy(animationData);
                        animationInfo.setTempType();
                        mEditTimeline.updateThumbnailAnimationInfo(mainSelectedClip);
                    }
                });

            } else {
                BaseItemView baseItemView = mEditorTrackView.getDragView();
                if (baseItemView != null) {
                    baseItemView.addVideoAnimationView();
                }
                mEditorTrackView.showPipTrackView(mTimeline);
            }

        } else if (mNavigationBar.isShow(R.string.nb_video_edit1) || mNavigationBar.isShow(R.string.nb_picture_edit1)) {
            if (!mainTrackSelected()) {
                mEditorTrackView.showPipTrackView(mTimeline);
                //画中画编辑
                //Picture-in-Picture Editing
                if (mSelectTrackData != null) {
                    int trackIndex = mSelectTrackData.getTrackIndex();
                    long inPoint = mSelectTrackData.getInPoint();
                    mCurSelectVideoClip = mEditorEngine.getVideoClip(trackIndex + 1, inPoint);
                    if (mCurSelectVideoClip != null) {
                        mEditorTrackView.setSelect(trackIndex, (int) inPoint);
                        mCurrentInPoint = inPoint;
                    } else {
                        mBottomViewHelper.hideIfNeed();
                        mNavigationBar.show(R.string.nb_pip1);
                        if (mBottomViewContainer.getVisibility() == VISIBLE) {
                            mBottomViewContainer.dismissView();
                        }
                    }
                }
            } else {
                mEditTimeline.showSpanView(mPresenter.getTimelineCurrentPosition(), true);
            }
        } else if (mNavigationBar.isShow(R.string.nb_audio2) || mNavigationBar.isShow(R.string.nb_audio1)) {
            refreshAudioView();
            mNavigationBar.show(R.string.nb_audio1);
            hideBottomView();
        } else if (mBottomViewHelper.updateWatermarkSelected()) {
            MeicamTimeline timeline = EditorEngine.getInstance().getCurrentTimeline();
            MeicamWaterMark waterMark = timeline.getMeicamWaterMark();
            if (waterMark != null && (!TextUtils.isEmpty(waterMark.getWatermarkFilePath()))) {
                mVideoFragment.openFxEditMode(EDIT_MODE_WATERMARK, null, true);
            } else {
                mVideoFragment.closeFxEditMode();
                mBottomViewHelper.updateWatermarkSelected();
            }

        } else if (mBottomViewHelper.updateWatermarkEffectSelected()) {
            MeicamTimeline timeline = EditorEngine.getInstance().getCurrentTimeline();
            MeicamTimelineVideoFxClip meicamTimelineVideoFxClip = null;
            if (timeline != null) {
                meicamTimelineVideoFxClip = timeline.getTimelineFxFromClipList(0);
            }
            mVideoFragment.openFxEditMode(Constants.EDIT_MODE_WATERMARK_EFFECT, meicamTimelineVideoFxClip, true);
        } else if (mNavigationBar.isShow(R.string.nb_ratio1)) {
            setRatioNavigationItem(mEditorEngine.getMakeRatio());
            mVideoFragment.updateTransformFx(mCurSelectVideoClip);
        } else if (mNavigationBar.isShow(R.string.nb_background1)) {
            mVideoFragment.updateTransformFx(mCurSelectVideoClip);
            if (mCurSelectVideoClip != null && mBottomViewContainer.getShowView() instanceof MYCanvasStyle) {
                mBottomViewHelper.updateCanvasStyle(mCurSelectVideoClip);
            }
            if (mCurSelectVideoClip != null && mBottomViewContainer.getShowView() instanceof MYCanvasColor) {
                mBottomViewHelper.updateCanvasColor(mCurSelectVideoClip);
            }
            if (mCurSelectVideoClip != null && mBottomViewContainer.getShowView() instanceof MYCanvasBlur) {
                mBottomViewHelper.updateCanvasBlur(mCurSelectVideoClip);
            }
        } else if (mNavigationBar.isShow(R.string.nb_sticker1)) {
            mEditTimeline.showEffectRegion(false, true, false);
            mPresenter.getMainTrackLineRegion();
        } else if (mNavigationBar.isShow(R.string.nb_pip1) || (!mainTrackSelected() && (mNavigationBar.isShow(R.string.nb_picture_edit1)
                || mNavigationBar.isShow(R.string.nb_video_edit1)))) {
            mEditTimeline.showEffectRegion(false, false, true);
        } else {
            mEditTimeline.showEffectRegion(true, true, true);
        }

        updateVideoClipUI();
        updateKeyFrameStatus();
    }

    /**
     * 刷新和videoClip操作相关的UI
     * update videoClip UI
     */
    private void updateVideoClipUI() {
        if (mCurrSelectedFilterAndAdjustClip != null) {
            mCurrSelectedFilterAndAdjustClip = mEditorEngine.getMeicamTimelineVideoFilterAndAdjustClip(mCurrSelectedFilterAndAdjustClip.getTrackIndex(), mCurrSelectedFilterAndAdjustClip.getIndex());
        }
        if (mBottomViewContainer.getShowView() instanceof MYFilterMenuView) {
            MYFilterMenuView filterMenuView = (MYFilterMenuView) mBottomViewContainer.getShowView();
            mBottomViewHelper.setTimelineFilterSelected(filterMenuView, mCurrSelectedFilterAndAdjustClip);
        }
        if (mCurSelectVideoClip == null) {
            return;
        }
        mBottomViewHelper.updateAnimationView(mCurSelectVideoClip);
        mBottomViewHelper.updateSeekBar(mCurSelectVideoClip, mCurrSelectedFilterAndAdjustClip);
        mBottomViewHelper.updateSelection(mCurSelectVideoClip);
        if (mCurSelectVideoClip != null && mBottomViewContainer.getShowView() instanceof MYMixedModeMenuView) {
            MYMixedModeMenuView mixedModeMenuView = (MYMixedModeMenuView) mBottomViewContainer.getShowView();
            mixedModeMenuView.setSelectedAndSeekBarProgress(mCurSelectVideoClip.getBlendingMode(), mCurSelectVideoClip.getOpacity());
        }
    }

    @Override
    public void onZoomEventCallback() {
        if (Utils.isFastClick()) {
            return;
        }
        mBeforeJumpTimelinePosition = mTimeline.getCurrentPosition();
        Bundle bundle = new Bundle();
        bundle.putLong(START_TIME, mEditorEngine.getCurrentTimelinePosition());
        AppManager.getInstance().jumpActivity(DraftEditActivity.this, FullScreenPreviewActivity.class, bundle);
    }

    @Override
    public boolean onKeyFrameClick(boolean add) {
        boolean success = false;
        boolean needSaveOperate = true;
        long timestamp = mPresenter.getTimelineCurrentPosition();
        MeicamKeyFrame keyFrame = null;
        BaseItemView dragView = mEditorTrackView.getDragView();
        boolean showAtomicKeyFrame = showAtomicEditMenuViewKeyFrame();
        Plug plug = null;
        PlugDetail.Param param = null;
        if (showAtomicKeyFrame) {
            AtomicEditMenuView view = (AtomicEditMenuView) mBottomViewHelper.getView().getShowView();
            plug = view.getSelectedPlug();
            param = view.getSelectedParam();
        }
        if (add) {
            showKeyframeInTrack();
            if (dragView != null) {
                /*子轨道
                 * Sub tack
                 * */
                BaseUIClip baseUIClip = dragView.getBaseUIClip();
                String key = null;
                if (baseUIClip instanceof VideoClipProxy) {
                    if (mCurSelectVideoClip == null) {
                        LogUtils.e("onKeyFrameClick  mCurSelectVideoClip==null");
                        return false;
                    }
                    /*画中画
                     * Picture-In-Picture
                     * */
                    View showView = mBottomViewContainer.getShowView();
                    if (showView instanceof EditMaskView) {
                        key = NvsConstants.KEY_CROPPER_REGION_INFO;
                        keyFrame = mEditorEngine.addMaskKeyFrame(mCurSelectVideoClip, timestamp);
                    } else if (showView instanceof AdjustSeekBarView) {
                        key = NvsConstants.KEY_MASK_OPACITY;
                        keyFrame = mEditorEngine.addOpacityFrame(mCurSelectVideoClip, timestamp);
                    } else if (showAtomicKeyFrame) {
                        if (mCurSelectVideoClip != null && param != null && plug != null) {
                            MeicamVideoFx meicamVideoFx = mCurSelectVideoClip.getVideoFx
                                    (MeicamVideoFx.SubType.SUB_TYPE_PLUG_FX, plug.plugName, plug.getClipIndex());
                            keyFrame = mEditorEngine.addClipKeyFrame(meicamVideoFx, param, timestamp);
                            key = param.paramName;
                            needSaveOperate = false;
                        }
                    } else {
                        keyFrame = mEditorEngine.addClipKeyFrame(mCurSelectVideoClip.findPropertyVideoFx(), timestamp);
                    }
                } else if (baseUIClip instanceof TimelineVideoFxProxy) {
                    if (mCurrSelectedVideoFx != null && param != null) {
                        keyFrame = mEditorEngine.addClipKeyFrame(mCurrSelectedVideoFx, param, timestamp);
                        key = param.paramName;
                    }
                } else {
                    if (baseUIClip instanceof CaptionProxy || baseUIClip instanceof StickerProxy) {
                        ClipInfo<?> clipInfo = mEditorEngine.getCaptionStickerData(baseUIClip.getTrackIndex(), baseUIClip.getInPoint());
                        keyFrame = mEditorEngine.addClipKeyFrame((IKeyFrameProcessor<?>) clipInfo, timestamp);
                    }
                }
                if (keyFrame != null) {
                    success = dragView.addKeyFrame(keyFrame.getAtTime(), true);
                }
                KeyFrameProcessor<?> keyFrameHolder = getKeyFrameProcessor(baseUIClip);
                changeKeyFrameCurveState(keyFrameHolder, key, timestamp - baseUIClip.getInPoint());

            } else if (mainTrackSelected()) {
                if (mCurSelectVideoClip == null) {
                    LogUtils.e("onKeyFrameClick  mCurSelectVideoClip==null");
                    return false;
                }
                /*主轨道视频片段的关键帧,要先判断子轨道，mCurrentTrackIndex仅指针对于视频片段
                 * The key frame of the main track video clip should be determined first.
                 * The mCurrentTrackIndex only refers to the video clip
                 * */
                View showView = mBottomViewContainer.getShowView();
                String key = null;
                if (showView instanceof EditMaskView) {
                    keyFrame = mEditorEngine.addMaskKeyFrame(mCurSelectVideoClip, timestamp);
                    key = NvsConstants.KEY_PROPERTY_MASK_REGION_INFO;
                } else if (showView instanceof AdjustSeekBarView) {
                    keyFrame = mEditorEngine.addOpacityFrame(mCurSelectVideoClip, timestamp);
                    key = NvsConstants.KEY_MASK_OPACITY;
                } else if (showAtomicKeyFrame) {
                    if (mCurSelectVideoClip != null && param != null && plug != null) {
                        MeicamVideoFx meicamVideoFx = mCurSelectVideoClip.getVideoFx
                                (MeicamVideoFx.SubType.SUB_TYPE_PLUG_FX, plug.plugName, plug.getClipIndex());
                        keyFrame = mEditorEngine.addClipKeyFrame(meicamVideoFx, param, timestamp);
                        key = param.paramName;
                        needSaveOperate = false;
                    }
                } else {
                    keyFrame = mEditorEngine.addClipKeyFrame(mCurSelectVideoClip.findPropertyVideoFx(), timestamp);
                }
                if (keyFrame != null) {
                    mEditTimeline.addKeyFrameTag(keyFrame.getAtTime());
                    success = true;
                }
                KeyFrameProcessor<?> keyFrameHolder = getKeyFrameProcessor(mCurSelectVideoClip);
                changeKeyFrameCurveState(keyFrameHolder, key, timestamp - mCurSelectVideoClip.getInPoint());
            }

            View showView = mBottomViewContainer.getShowView();
            if (showView instanceof AdjustSeekBarView) {
                float opacity = mPresenter.getOpacityAtTime(mCurSelectVideoClip, timestamp - mCurSelectVideoClip.getInPoint());
                ((AdjustSeekBarView) showView).setProgress((int) (opacity * 100));
            }
        } else {
            KeyFrameInfo keyFrameInfo;
            if (dragView != null) {
                /*子轨道
                 * Sub track
                 * */
                BaseUIClip baseUIClip = dragView.getBaseUIClip();
                if (baseUIClip != null) {
                    keyFrameInfo = dragView.getBaseUIClip().getKeyFrameInfo();
                    if (keyFrameInfo != null) {
                        IKeyFrameProcessor<?> keyFrameHolder = getKeyFrameHolder(baseUIClip);
                        if (keyFrameHolder != null) {
                            String keyOfKeyFrame = getKeyOfKeyFrame(keyFrameHolder.keyFrameProcessor());
                            if (KeyFrameHolderCommand.removeKeyFrame(keyFrameHolder,
                                    mPresenter.getRemoveParamKeys(keyOfKeyFrame), keyFrameInfo.getSelectedPoint())) {
                                dragView.deleteKeyFrame(keyFrameInfo.getSelectedPoint());
                                success = keyFrameInfo.getSelectedPoint() < 0;
                                changeKeyFrameCurveState(keyFrameHolder.keyFrameProcessor(), showAtomicKeyFrame ? param.paramName : keyOfKeyFrame
                                        , timestamp - baseUIClip.getInPoint());
                            }
                        }
                    }
                }
            } else if (mainTrackSelected()) {
                /*主轨道,要先判断子轨道，mCurrentTrackIndex仅指针对于视频片段
                 * The main track needs to be judged first. The mCurrentTrackIndex only refers to the video clip
                 * */
                ITrackClip mainSelectedClip = mEditTimeline.getMainSelectedClip();
                IKeyFrameProcessor<?> keyFrameHolder = getKeyFrameHolder(mCurSelectVideoClip);
                if (mainSelectedClip != null && keyFrameHolder != null) {
                    String keyOfKeyFrame = getKeyOfKeyFrame(keyFrameHolder.keyFrameProcessor());
                    keyFrameInfo = mainSelectedClip.getKeyFrameInfo();
                    if (keyFrameInfo != null && KeyFrameHolderCommand.removeKeyFrame(keyFrameHolder, mPresenter.getRemoveParamKeys(keyOfKeyFrame), keyFrameInfo.getSelectedPoint())) {
                        mEditTimeline.deleteKeyFrameTag(keyFrameInfo.getSelectedPoint());
                        success = keyFrameInfo.getSelectedPoint() < 0;
                        changeKeyFrameCurveState(keyFrameHolder.keyFrameProcessor(), showAtomicKeyFrame ? param.paramName : keyOfKeyFrame,
                                timestamp - mCurSelectVideoClip.getInPoint());
                    }
                }
            }
            mVideoFragment.updateOperationBoxWhenHadKeyFrame(timestamp);
            mVideoFragment.updateOperationVideoBounding(timestamp);
        }
        if (success && needSaveOperate) {
            mVideoFragment.seekTimeline(timestamp, 0);
            saveOperation();
        }
        return success;
    }

    private void changeKeyFrameCurveState(KeyFrameProcessor<?> keyFrameHolder, String key, long atTime) {
        if (showAtomicEditMenuViewKeyFrame()) {
            AtomicEditMenuView showView = (AtomicEditMenuView) mBottomViewHelper.getView().getShowView();
            showView.changeKeyFrameCurveState(keyFrameHolder != null &&
                    keyFrameHolder.getFramePair(key, atTime) != null);
        } else {
            mEditOperationView.changeKeyFrameCurveState(keyFrameHolder != null &&
                    keyFrameHolder.getFramePair(key, atTime) != null);
        }
    }

    @Override
    public void onPostKeyFrameClick() {
        View showView = mBottomViewContainer.getShowView();
        if (showView instanceof EditMaskView) {
            if (!CommonData.SUPPORT_MASK_KEY_FRAME_CURVE) {
                mEditOperationView.notShowKeyFrameCurveView();
            }
        }
    }

    @Override
    public void onKeyFrameCurveClick() {
        BaseItemView dragView = mEditorTrackView.getDragView();
        boolean isShowAtomicKeyFrame = showAtomicEditMenuViewKeyFrame();
        if (dragView != null) {
            /*子轨道
             * Sub tack
             * */
            BaseUIClip baseUIClip = dragView.getBaseUIClip();
            if (baseUIClip != null) {
                final ClipInfo<?> tempClip;
                if (baseUIClip instanceof VideoClipProxy) {
                    /*画中画
                     * Picture-In-Picture
                     * */
                    tempClip = mCurSelectVideoClip;
                } else if (baseUIClip instanceof TimelineVideoFxProxy) {
                    tempClip = mCurrSelectedVideoFx;
                } else {
                    tempClip = mEditorEngine.getCaptionStickerData(baseUIClip.getTrackIndex(), baseUIClip.getInPoint());
                }
                if (tempClip != null) {
                    long keyFrameSelectedPoint = dragView.getKeyFrameSelectedPoint();
                    long atTime;
                    if (keyFrameSelectedPoint >= 0) {
                        atTime = keyFrameSelectedPoint;
                    } else {
                        atTime = mEditorEngine.getCurrentTimelinePosition() - tempClip.getInPoint();
                    }
                    if (isShowAtomicKeyFrame) {
                        AtomicEditMenuView showView = (AtomicEditMenuView) mBottomViewHelper.getView().getShowView();
                        PlugDetail.Param param = showView.getSelectedParam();
                        if (param != null) {
                            editKeyFrameCurve(tempClip, param.paramName, atTime);
                        }
                    } else {
                        IKeyFrameProcessor<?> keyFrameHolder = getKeyFrameHolder(tempClip);
                        if (keyFrameHolder != null) {
                            editKeyFrameCurve(tempClip, getKeyOfKeyFrame(keyFrameHolder.keyFrameProcessor()), atTime);
                        }
                    }

                }
            }
        } else if (mainTrackSelected()) {
            ITrackClip mainSelectedClip = mEditTimeline.getMainSelectedClip();
            if (mainSelectedClip != null) {
                long selectedPoint = mainSelectedClip.getKeyFrameInfo().getSelectedPoint();
                long atTime;
                if (selectedPoint >= 0) {
                    atTime = selectedPoint;
                } else {
                    atTime = mCurSelectVideoClip != null ? mEditorEngine.getCurrentTimelinePosition()
                            - mCurSelectVideoClip.getInPoint() : -1;
                }
                if (mCurSelectVideoClip != null) {
                    if (isShowAtomicKeyFrame) {
                        AtomicEditMenuView showView = (AtomicEditMenuView) mBottomViewHelper.getView().getShowView();
                        PlugDetail.Param param = showView.getSelectedParam();
                        if (param != null) {
                            editKeyFrameCurve(mCurSelectVideoClip, param.paramName, atTime);
                        }
                    } else {
                        IKeyFrameProcessor<?> keyFrameHolder = getKeyFrameHolder(mCurSelectVideoClip);
                        if (keyFrameHolder != null) {
                            editKeyFrameCurve(mCurSelectVideoClip, getKeyOfKeyFrame(keyFrameHolder.keyFrameProcessor()), atTime);
                        }
                    }
                }
            }
        }
    }

    private void editKeyFrameCurve(final ClipInfo<?> tempClip, String key, long atTime) {
        IKeyFrameProcessor<?> keyFrameHolder = getKeyFrameHolder(tempClip);
        boolean isShowAtomicKeyFrame = showAtomicEditMenuViewKeyFrame();
        if (!isShowAtomicKeyFrame) {
            hideBottomView();
        }
        mEditOperationView.changeOperateViewState(GONE);
        final Pair<MeicamKeyFrame, MeicamKeyFrame> framePair = EditorEngine.getInstance().getFramePair(keyFrameHolder,
                key, atTime);
        mBottomViewHelper.showKeyFrameCurveView(framePair, new EditKeyFrameCurveView.OnEventListener() {
            @Override
            public void dismiss() {
                super.dismiss();
                mBottomViewHelper.getView().dismissPopView();
            }

            @Override
            public void onDismiss(boolean confirm) {
                if (isShowAtomicKeyFrame) {
                    AtomicEditMenuView showView = (AtomicEditMenuView) mBottomViewHelper.getView().getShowView();
                    Plug plug = showView.getSelectedPlug();
                    PlugDetail.Param param = showView.getSelectedParam();
                    if (mCurrSelectedVideoFx != null && plug != null && param != null) {
                        updateVideoClipKeyFrame(mCurrSelectedVideoFx, param.paramName);
                    } else if (mCurSelectVideoClip != null) {
                        if (plug != null && param != null) {
                            MeicamVideoFx meicamVideoFx = mCurSelectVideoClip.getVideoFx
                                    (MeicamVideoFx.SubType.SUB_TYPE_PLUG_FX, plug.plugName, plug.getClipIndex());
                            if (meicamVideoFx != null) {
                                updateVideoClipKeyFrame(meicamVideoFx, param.paramName);
                            }
                        }
                    }
                } else if (mCurSelectVideoClip != null) {
                    mEditOperationView.changeOperateViewState(VISIBLE);
                    updateVideoClipKeyFrame(mCurSelectVideoClip.findPropertyVideoFx(), NvsConstants.KEY_CROPPER_TRANS_X);
                }
                showKeyframeInTrack();
                mEditOperationView.updateCancelRecoverVisible(true);
                saveOperation();
            }

            @Override
            public void onItemClick(CurveAdjustData adjustData, int position) {
                try {
                    if (mEditorEngine.addKeyFrameCurve(keyFrameHolder,
                            key, atTime, adjustData.getFrontControlPointF(), adjustData.getBackControlPointF(), position)) {
                        mEditorEngine.playVideoRollBack(tempClip.getInPoint()
                                + framePair.first.getAtTime(), tempClip.getInPoint() + framePair.second.getAtTime());
                    }
                } catch (Exception e) {
                    LogUtils.e(e);
                }
            }
        });
    }

    private void hideBottomView() {
        LogUtils.d("showNavigationName=" + getString(mNavigationBar.getShowingNavigationName()));
        mBottomViewHelper.hideIfNeed();
        mEditTimeline.enableThumbnailMove(true);
        mVideoFragment.openMaskZoomEditMode(mEditTimeline.mainSpanIsShow() ? mCurSelectVideoClip : null, false);
        if (!(mBottomViewContainer.getShowView() instanceof EditChangeSpeedCurveView)) {
            //曲线变速view显示的时候，只有点击确定才会消失，其他操作不隐藏view
            //When the curved variable speed view is displayed, only click OK will disappear. Other operations do not hide the View
            if (mBottomViewContainer.getShowView() instanceof MYCompoundCaptionEditView) {
                saveOperation();
            }
            mBottomViewContainer.dismissView();
            mBottomViewContainer.dismissFragment();
        }
        if (mMultiBottomView != null && mMultiBottomView.isShow()) {
            //字幕，保存操作步骤
            //caption, save operation steps
            if (mMultiBottomView.getType() == TYPE_MENU_CAPTION) {
                saveOperation();
            }
            mMultiBottomView.hide();
        }
        mEditOperationView.updateCancelRecoverVisible(true);
    }

    private String getKeyOfKeyFrame(KeyFrameProcessor<?> keyFrameProcessor) {
        View showView = mBottomViewContainer.getShowView();
        if (showView instanceof EditMaskView) {
            return NvsConstants.KEY_PROPERTY_MASK_REGION_INFO;
        } else if (showView instanceof AdjustSeekBarView) {
            return NvsConstants.KEY_MASK_OPACITY;
        } else {
            return keyFrameProcessor.getKeyOfKeyFrame();
        }
    }

    private IKeyFrameProcessor<?> getKeyFrameHolder(Object clip) {
        if (clip instanceof MeicamVideoClip) {
            View showView = mBottomViewContainer.getShowView();
            if (showView instanceof EditMaskView) {
                MeicamVideoFx maskTargetFx = EditorEngine.getInstance().getMaskTargetFx((MeicamVideoClip) clip);
                if (maskTargetFx != null) {
                    return maskTargetFx;
                } else {
                    MeicamVideoFx propertyVideoFx = ((MeicamVideoClip) clip).findPropertyVideoFx();
                    if (propertyVideoFx != null) {
                        return propertyVideoFx;
                    }
                }
            } else if (showAtomicEditMenuView()) {
                AtomicEditMenuView view = (AtomicEditMenuView) mBottomViewHelper.getView().getShowView();
                Plug plug = view.getSelectedPlug();
                if (plug != null) {
                    MeicamVideoFx meicamVideoFx = ((MeicamVideoClip) clip).getVideoFx
                            (MeicamVideoFx.SubType.SUB_TYPE_PLUG_FX, plug.plugName, plug.getClipIndex());
                    if (meicamVideoFx != null) {
                        return meicamVideoFx;
                    }
                }
            }
            MeicamVideoFx propertyVideoFx = ((MeicamVideoClip) clip).findPropertyVideoFx();
            if (propertyVideoFx != null) {
                return propertyVideoFx;
            }
        } else if (clip instanceof BaseUIClip) {
            String type = ((BaseUIClip) clip).getType();
            if (CommonData.CLIP_VIDEO.equals(type) || CommonData.CLIP_IMAGE.equals(type)) {
                View showView = mBottomViewContainer.getShowView();
                if (showView instanceof EditMaskView) {
                    MeicamVideoFx maskTargetFx = EditorEngine.getInstance().getMaskTargetFx(mCurSelectVideoClip);
                    if (maskTargetFx != null) {
                        return maskTargetFx;
                    }
                } else if (showAtomicEditMenuView()) {
                    AtomicEditMenuView view = (AtomicEditMenuView) mBottomViewHelper.getView().getShowView();
                    Plug plug = view.getSelectedPlug();
                    if (plug != null) {
                        MeicamVideoFx meicamVideoFx = mCurSelectVideoClip.getVideoFx
                                (MeicamVideoFx.SubType.SUB_TYPE_PLUG_FX, plug.plugName, plug.getClipIndex());
                        if (meicamVideoFx != null) {
                            return meicamVideoFx;
                        }
                    }
                }
                if (mCurSelectVideoClip != null) {
                    MeicamVideoFx propertyVideoFx = mCurSelectVideoClip.findPropertyVideoFx();
                    if (propertyVideoFx != null) {
                        return propertyVideoFx;
                    }
                }
            }
        }
        if (clip instanceof IKeyFrameProcessor) {
            return ((IKeyFrameProcessor<?>) clip);
        }
        return null;
    }

    private KeyFrameProcessor<?> getKeyFrameProcessor(Object clip) {
        IKeyFrameProcessor<?> keyFrameHolder = getKeyFrameHolder(clip);
        if (keyFrameHolder != null) {
            return keyFrameHolder.keyFrameProcessor();
        }
        return null;
    }


    /**
     * 刷新Audio的view
     * Refresh Audio view
     */
    private void refreshAudioView() {
        if (mTimeline == null) {
            LogUtils.e("mTimeline == null");
            return;
        }
        if (mEditorTrackView == null) {
            LogUtils.e("mEditorTrackView == null");
            return;
        }
        mTrackListHashMap = TrackViewDataHelper.getInstance().getTrackData(CommonData.CLIP_AUDIO);
        mEditorTrackView.setTrackViewLayoutData(mTrackListHashMap, mTimeline.getDuration(), CommonData.CLIP_AUDIO);
    }

    /**
     * Refresh cover view now.
     * 现在刷新封面视图
     */
    public void refreshCoverViewNow(AnimationData animationData) {
        if ((mNavigationBar.isShow(R.string.nb_animate2)) && !mainTrackSelected()) {
            BaseItemView baseItemView = mEditorTrackView.getDragView();
            if (baseItemView != null) {
                baseItemView.addVideoAnimationView();
            }
        } else {
            ITrackClip trackClip = mEditTimeline.getSelectedThumbnailCover();
            if (trackClip != null) {
                AnimationInfo animationInfo = ((ThumbnailClip) trackClip).getAnimationInfo();
                if (animationData != null) {
                    animationInfo = animationInfo.copy(animationData);
                    animationInfo.setTempType();
                } else {
                    animationInfo.clear();
                }
                mEditTimeline.updateThumbnailAnimationInfo(trackClip);
            }

        }
    }

    /**
     * Refresh cover view.
     * 刷新封面视图
     */
    @Override
    public void refreshCoverView(AnimationData animationData) {
        ITrackClip trackClip = mEditTimeline.getMainSelectedClip();
        if (trackClip != null) {
            AnimationInfo animationInfo = ((ThumbnailClip) trackClip).getAnimationInfo();
            if (animationData != null) {
                animationInfo = animationInfo.copy(animationData);
                animationInfo.setTempType();
            } else {
                animationInfo.clear();
            }
            mEditTimeline.updateThumbnailAnimationInfo(trackClip);
        }
    }


    private void toOtherMenu() {
        mEditTimeline.toOtherMenu();
        mEditorTrackView.toOtherMenu();
    }

    private void showCaptionView() {
        String text = "";
        if (mVideoFragment.getEditFx() instanceof MeicamCaptionClip) {
            MeicamCaptionClip editFx = (MeicamCaptionClip) mVideoFragment.getEditFx();
            if (editFx != null) {
                text = (editFx).getText();
            }
        }
        long atTime = -1;
        if (mEditorTrackView.getDragView() != null) {
            atTime = mEditorTrackView.getDragView().getKeyFrameSelectedPoint();
        }
        mMultiHelper.showCaptionView(mCurrSelectedCaptionStickClip, mKeyboardHeight, text, atTime,
                new CaptionStyleFragment.CaptionStyleEventListener() {
                    @Override
                    public void onCaptionLocalChanged() {
                        if (mCurrSelectedCaptionStickClip == null) {
                            return;
                        }
                        mVideoFragment.openFxEditMode(EDIT_MODE_CAPTION, mCurrSelectedCaptionStickClip, true);
                    }
                });
    }

    /**
     * 更新滤镜，调节轨道的显示
     * Update the view track of filter or adjust
     */
    private void updateFilterAndAdjustTrack() {
        mEditorTrackView.setData(TrackViewDataHelper.getInstance().getTrackData(CommonData.CLIP_FILTER),
                mEditorEngine.getCurrentTimeline().getDuration(), CommonData.CLIP_FILTER);
    }

    /**
     * 更新timeline fx轨道的显示
     * Update the view track of timeline fx
     */
    private void updateTimelineFxTrack() {
        mEditorTrackView.setData(TrackViewDataHelper.getInstance().getTrackData(CommonData.CLIP_TIMELINE_FX),
                mEditorEngine.getCurrentTimeline().getDuration(), CommonData.CLIP_TIMELINE_FX);
    }

    /**
     * 更新贴纸/字幕/组合字幕轨道的显示
     * Update the view track of sticker or caption or combined caption
     */
    private void updateStickerTrack() {
        //贴纸/字幕/组合字幕共用
        //Sticker/subtitle/combined subtitle sharing
        mEditorTrackView.setData(TrackViewDataHelper.getInstance().getTrackData(CommonData.CLIP_CAPTION),
                mEditorEngine.getCurrentTimeline().getDuration(), CommonData.CLIP_CAPTION);
    }

    /**
     * 更新画中画轨道的显示
     * Update the view track of PIP
     */
    private void updatePipTrack() {
        mEditorTrackView.setData(TrackViewDataHelper.getInstance().getPipTrackData(),
                mEditorEngine.getCurrentTimeline().getDuration(), CommonData.CLIP_IMAGE);
    }

    @Override
    protected void onRestart() {
        super.onRestart();
        mActivityStopped = false;
        if (mPresenter != null) {
            mPresenter.restoreExportTemplateChange(!mEditTimeline.originalVoiceIsOpen());
        }
    }

    @Override
    protected void onStart() {
        super.onStart();
        mActivityStopped = false;
    }

    @Override
    protected void onResume() {
        super.onResume();
        mActivityStopped = false;
        try {
            //如果是跳转了全屏预览界面，需要重新连接livewindow
            //If you have switched to the full screen preview screen, you need to reconnect to LiveWindow
            if (mVideoFragment != null) {
                /*
                 * 如果有智能抠像，恢复
                 * If there is smart matting, restore
                 */
                mPresenter.restoreSomeThingFromTimeline();
                long position = mBeforeJumpTimelinePosition < 0 ? mEditorEngine.getCurrentTimelinePosition() : mBeforeJumpTimelinePosition;
                mVideoFragment.connectTimelineWithLiveWindow();
                mEditorEngine.seekTimeline(position, 0);
                //添加完视频片段后，这里重新执行，所有写个变量过滤一下
                //Now that I've added the video clip, I'm going to do it again, so I'm going to write all the variables and filter them
                if (mInsertVideoClipIndex == -1) {
                    seekViewOnPlay(position);
                } else {
                    mInsertVideoClipIndex = -1;
                }
            }
            String coverImagePath = mTimeline.getCoverImagePath();
            if (!TextUtils.isEmpty(coverImagePath)) {
                mEditTimeline.setCover(coverImagePath);
            }
        } catch (Exception e) {
            LogUtils.e(e);
        }
    }

    @Override
    protected void onStop() {
        super.onStop();
        mActivityStopped = true;
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == REQUEST_CODE_1) {
            if (data == null) {
                return;
            }
            ArrayList<MediaData> mediaList = data.getParcelableArrayListExtra(BUNDLE_DATA);
            if (mediaList == null || mediaList.size() == 0) {
                return;
            }
            mPresenter.addVideoClip(mInsertVideoClipIndex, mediaList);
            mAudioRecordManager = AudioRecordManager.getInstance();
        } else if (requestCode == REQUEST_PICTURE_IN_PICTURE_CODE) {
            if (data != null) {
                MediaData media = data.getParcelableExtra(BUNDLE_DATA);
                if (media == null) {
                    LogUtils.e("media is null");
                    return;
                }
                mPresenter.addPipVideoClip(media);
            }
        } else if (requestCode == REQUEST_SELECT_IMAGE_BACKGROUND_CODE) {
            if (data == null) {
                return;
            }
            MediaData media = data.getParcelableExtra(BUNDLE_DATA);
            if (media != null && !TextUtils.isEmpty(media.getPath())) {
                long timeStamp = EditorEngine.getInstance().getCurrentTimelinePosition();
                MeicamVideoClip editVideoClip = EditorEngine.getInstance().getEditVideoClip(timeStamp, TRACK_INDEX_MAIN);
                mEditorEngine.updateBlurAndColorBackground(editVideoClip, media.getPath(), STORYBOARD_BACKGROUND_TYPE_IMAGE);
            }
        } else if (requestCode == SELECT_MUSIC) {
            if (data == null) {
                return;
            }
            MusicInfo musicInfo = (MusicInfo) data.getSerializableExtra(BUNDLE_DATA);
            if (musicInfo != null) {
                long trimIn = musicInfo.getTrimIn();
                long trimOut = musicInfo.getTrimOut();
                addAudioTrack(musicInfo.getFilePath(), musicInfo.getTitle(), trimIn, trimOut, MeicamAudioClip.AUDIO_MUSIC);
            }

        } else if (requestCode == REQUEST_CODE_2) {
            if (data == null) {
                return;
            }
            MediaData mediaData = data.getParcelableExtra(BUNDLE_DATA);
            if (mediaData == null) {
                return;
            }
            ConvertFileManager.ConvertParam convertParam = new ConvertFileManager.ConvertParam();
            Hashtable<String, Object> config = new Hashtable<>();
            config.put(NvsMediaFileConvertor.CONVERTOR_NO_VIDEO, true);
            String mediaDataPath = mediaData.getPath();
            convertParam.appendParam(mediaDataPath, "", false, config);
            ConvertProgressPop.create(this, convertParam, new ConvertFileManager.EventListener() {
                @Override
                public void onConvertFinish(ConvertFileManager.ConvertParam convertParam, boolean convertSuccess) {
                    if (convertSuccess) {
                        if (convertParam != null) {
                            Map<String, ConvertFileManager.ConvertParam.Param> paramMap = convertParam.getParamMap();
                            if (paramMap != null) {
                                ConvertFileManager.ConvertParam.Param param = paramMap.get(mediaDataPath);
                                if (param != null) {
                                    long trimIn = 0;
                                    long trimOut = mediaData.getDuration() * 1000;
                                    addAudioTrack(param.getDstFile(), mediaData.getDisplayName(), trimIn, trimOut, MeicamAudioClip.VIDEO_MUSIC);
                                    return;
                                }
                            }
                        }
                    }
                    ToastUtils.make()
                            .setGravity(Gravity.CENTER, 0, 0)
                            .setDurationIsLong(false)
                            .show(R.string.convert_music_failed);

                }
            }).setHintText(R.string.video_clip_replace_progress).show();
        } else if (requestCode == REQUEST_SELECT_WATERMARK) {
            if (data == null) {
                return;
            }
            MediaData media = data.getParcelableExtra(BUNDLE_DATA);
            if (media != null && !TextUtils.isEmpty(media.getPath())) {
                mBottomViewHelper.addWatermark(media.getPath());
            }
        } else if (requestCode == REQUEST_CROP_CLIP_CODE) {
            mEditorEngine.seekTimeline(0);
            mVideoFragment.updateTransformFx(mCurSelectVideoClip, true);
            saveOperation();
        } else if (requestCode == REQUEST_PLUG_SELECT_PICTURE) {
            //插件添加图片
            //Plug-in add picture
            if (showAtomicEditMenuView()) {
                AtomicEditMenuView view = (AtomicEditMenuView) mBottomViewHelper.getView().getShowView();
                PlugDetail.Param param = view.getSelectedParam();
                if (param == null || data == null) {
                    return;
                }
                MediaData media = data.getParcelableExtra(BUNDLE_DATA);
                if (media != null && !TextUtils.isEmpty(media.getPath())) {
                    param.valueDefault = media.getPath();
                    if (mCurrSelectedVideoFx != null) {
                        TimelineFxCommand.setStringVal(mCurrSelectedVideoFx, param.paramName, param.valueDefault);
                    } else if (view.getMeicamVideoFx() != null) {
                        VideoFxCommand.setStringVal(view.getMeicamVideoFx(), param.paramName, param.valueDefault);
                    }
                }

            }
            mEditorEngine.seekTimeline();
        } else if (requestCode == REQUEST_CLIP_REPLACE) {
            if (mCurSelectVideoClip == null || data == null) {
                return;
            }
            MeidaClip media = data.getParcelableExtra(BUNDLE_DATA);
            if (media == null) {
                return;
            }
            String filePath = media.getFilePath();
            long newTrim = data.getLongExtra(INTENT_TRAM, 0);
            mPresenter.replaceClip(this, mCurSelectVideoClip, filePath, newTrim);
        } else if (requestCode == REQUEST_SELECT_IMAGE_COVER_CODE) {
            if (resultCode == RESULT_OK) {
                String draftDir = mTimeline.getDraftDir();
                if (TextUtils.isEmpty(draftDir)) {
                    draftDir = DraftManager.newDraftDir(String.valueOf(System.currentTimeMillis()));
                    mTimeline.setDraftDir(draftDir);
                }
                String coverConfigPath = DraftManager.getCoverConfigPath(draftDir);
                FileIOUtils.writeFileFromString(coverConfigPath, mTimeline.getCoverData().toJson());
                mTimeline.setCoverDataConfigPath(coverConfigPath);
            }
            mEditorEngine.seekTimeline(0, 0);
        }
    }

    /**
     * 设置添加音频轨道
     * Set to add audio track
     *
     * @param audioPath String The audio path
     * @param name      String The audio name
     * @param trimIn    long The trim in point
     * @param type      int  The audio type
     */
    private void addAudioTrack(String audioPath, String name, long trimIn, long trimOut, int type) {
        MeicamAudioClip audioClip = mPresenter.addAudioClipToTrack(audioPath, name, mEditorEngine.getCurrentTimelinePosition(), trimIn, trimOut, type, -1);
        if (audioClip != null) {
            refreshAudioView();
            mPresenter.checkTrackDuration();
            mEditorTrackView.setSelect(audioClip.getTrackIndex(), audioClip.getInPoint());
            saveOperation();
        }
    }

    @Override
    public void onBackPressed() {
        onBackPressed(false);
    }

    private void onBackPressed(boolean forceBack) {
        if (Utils.isFastClick() || checkSmartKeying()) {
            return;
        }
        if (mBottomViewHelper.getView().getShowFragment() != null) {
            mBottomViewHelper.getView().dismissFragment();
            if (!forceBack) {
                return;
            }
        }
        View showView = mBottomViewHelper.getView().getShowView();
        if (showView != null) {
            if (showView instanceof MYColorPickMenuView) {
                mVideoFragment.releaseColorPicker();
            }
            mBottomViewHelper.getView().dismissView();
            if (!forceBack) {
                return;
            }
        }
        if (mMultiBottomView.isShow()) {
            mMultiBottomView.hide();
            if (!forceBack) {
                return;
            }
        }
        saveDraft();
        backToMain();
    }

    private void saveDraft() {
        if (mFromPage == FROM_MAIN_PAGE) {
            //从草稿页面进入。
            //From draft page.
            if (mEditOperateManager.haveOperate()) {
                //有操作
                //With operation
                DraftManager.getInstance().updateDraft(mTimeline, mTimeline.getDuration(), null, true);
            } else {
                //无操作,这里也更新封面，因为从TimelineData转化成草稿json的时候少一些参数,这里先这么处理。
                // There is no operation. The cover page is also updated here, because there are fewer parameters when converting from TimelineData to draft json. This is how to deal with it first.
                DraftManager.getInstance().updateDraft(mTimeline, mTimeline.getDuration(), null, false);
            }
        } else if (AppManager.getInstance().getFromType() >= 0) {
            //从非草稿页面进入，即草稿尚未创建。
            // Enter from the non-draft page, that is, the draft has not been created.
            DraftManager.getInstance().saveDraft(mTimeline, mTimeline.getDuration(), null);
        }
        AppManager.getInstance().setFromType(-1);
    }

    public void showPlusMenu() {
        mEditOperationView.updateCancelRecoverVisible(false);
        mEditOperationView.notShowKeyFrameView();
        mBottomViewHelper.showPlugsMenu(this, mCurSelectVideoClip, mCurrSelectedVideoFx, new PlugsEventListener() {

            @Override
            public boolean onKeyFrameClick(Plug plug, PlugDetail.Param param, boolean add) {
                if (!showAtomicEditMenuView()) {
                    return false;
                }
                return DraftEditActivity.this.onKeyFrameClick(add);
            }

            @Override
            public void onKeyFrameCurveClick(Plug plug, PlugDetail.Param param) {
                DraftEditActivity.this.onKeyFrameCurveClick();
            }

            @Override
            public void onShowPlugList() {
                super.onShowPlugList();
                if (showAtomicEditMenuView()) {
                    AtomicEditMenuView view = (AtomicEditMenuView) mBottomViewHelper.getView().getShowView();
                    view.showTip(DraftEditActivity.this);
                }
            }

            @Override
            public void dismiss() {
                mBottomViewHelper.getView().dismissView();
            }

            @Override
            public void onDismiss(boolean confirm) {
                boolean isClipEdit = mNavigationBar.isShow(R.string.nb_video_edit1) || mNavigationBar.isShow(R.string.nb_picture_edit1);
                if (isClipEdit && mCurSelectVideoClip != null) {
                    //画中画选中的时候更新画中画关键帧显示状态
                    //Update picture-in-picture key frame display status when picture-in-picture is selected.
                    mEditOperationView.showKeyFrameView();
                    updateVideoClipKeyFrame(mCurSelectVideoClip.findPropertyVideoFx(), NvsConstants.KEY_CROPPER_TRANS_X);
                    updateKeyFrameStatus();
                }
                mCurrSelectedVideoFx = null;
                mEditOperationView.updateCancelRecoverVisible(true);
                saveOperation();
            }

            @Override
            public void onDisplayAddPlug() {
                super.onDisplayAddPlug();

                mCurrSelectedVideoFx = null;
                hideKeyframeInTrack();
                if (mCurSelectVideoClip == null) {
                    //画中画的时候不取消选中
                    //Don't uncheck when painting in picture
                    mEditorTrackView.clickOutSide();
                }
            }

            @Override
            public void onJumpPlugList() {
                super.onJumpPlugList();
                AppManager.getInstance().jumpActivity(DraftEditActivity.this, AtomicActivity.class);
            }

            @Override
            public void onPlugItemClick(Plug plug) {
                super.onPlugItemClick(plug);
                if (mCurSelectVideoClip == null) {
                    MeicamTimeline meicamTimeline = mPresenter.getTimeline();
                    MeicamTimelineVideoFxTrack track = meicamTimeline.getTimelineFxTrack(plug.getTrackIndex());
                    if (track != null) {
                        mCurrSelectedVideoFx = track.getClip(plug.getClipIndex());
                        if (mCurrSelectedVideoFx != null) {
                            mEditorTrackView.setSelect(mCurrSelectedVideoFx.getTrackIndex(), mCurrSelectedVideoFx.getInPoint(), false);
                        }
                    }
                    showPlusMenu();
                }
            }

            @Override
            public void onParamItemClick(Plug plug, PlugDetail.Param param) {
                super.onParamItemClick(plug, param);
                String valueDefault = param.valueDefault;
                String paramName = param.paramName;
                MeicamVideoFx meicamVideoFx = null;
                if (mCurSelectVideoClip != null && plug != null) {
                    meicamVideoFx = mCurSelectVideoClip.getVideoFx(MeicamVideoFx.SubType.SUB_TYPE_PLUG_FX, plug.plugName, plug.getClipIndex());
                }
                if (mCurrSelectedVideoFx == null && meicamVideoFx == null) {
                    return;
                }
                if (getString(R.string.plug_menu_delete).equals(paramName)) {
                    //删除
                    //Delete
                    if (mCurrSelectedVideoFx != null) {
                        //timeline
                        BaseUIClip trackData = new BaseUIVideoClip(mCurrSelectedVideoFx.getTrackIndex());
                        trackData.setInPoint(mCurrSelectedVideoFx.getInPoint());
                        boolean isSuccess = EditorEngine.getInstance().deleteEffect(trackData);
                        if (isSuccess) {
                            showPlusMenu();
                        }
                    } else if (meicamVideoFx != null) {
                        if (EditorEngine.getInstance().deleteClipEffect(mCurSelectVideoClip, meicamVideoFx)) {
                            showPlusMenu();
                        }
                    }
                    mCurrSelectedVideoFx = null;
                    //  mEditOperationView.updateCancelRecoverVisible(true);
                } else if (getString(R.string.plug_menu_copy).equals(paramName)) {
                    //复制
                    //Copy
                    if (mCurrSelectedVideoFx != null) {
                        //timeline
                        BaseUIClip trackData = new BaseUIVideoClip(mCurrSelectedVideoFx.getTrackIndex());
                        trackData.setInPoint(mCurrSelectedVideoFx.getInPoint());
                        int result = EditorEngine.getInstance().copyEffect(trackData, DraftEditActivity.this);
                        if (EditorEngine.ReturnCode.CODE_OK == result) {
                            showPlusMenu();
                        } else {
                            ToastUtils.showShort(R.string.unusable_space);
                        }
                    } else if (meicamVideoFx != null) {
                        if (EditorEngine.getInstance().copyClipEffect(mCurSelectVideoClip, meicamVideoFx)) {
                            showPlusMenu();
                        }
                    }
                } else if (getString(R.string.plug_menu_hide).equals(paramName)) {
                    //隐藏显示
                    //Hide
                    boolean defaultValue = Boolean.parseBoolean(valueDefault);
                    if (mCurrSelectedVideoFx != null) {
                        TimelineFxCommand.setIntensity(mCurrSelectedVideoFx, defaultValue ? 1 : 0);
                    } else if (meicamVideoFx != null) {
                        VideoFxCommand.setIntensity(meicamVideoFx, defaultValue ? 1 : 0);
                    }
                } else if (getString(R.string.plug_menu_bedspread).equals(paramName)) {
                    //铺满
                    //Spread out
                    changeBaseItemDuration(mSelectTrackData);

                } else if (Constants.PlugType.CURVE.equals(param.valueType)) {
                    //曲线调节 UI上需要调节曲线入口
                    //The curve entry needs to be adjusted on the curve adjustment UI
                    MeicamVideoFx finalMeicamVideoFx = meicamVideoFx;
                    String curve = meicamVideoFx == null ? mCurrSelectedVideoFx.getStringVal(paramName) : meicamVideoFx.getStringVal(paramName);
                    mBottomViewHelper.showAtomicCurve(curve, new EditAtomicCurveView.AtomicCurveListener() {
                        @Override
                        public void onCurveChanged(String curve) {
                            param.valueDefault = curve;
                            mEditorEngine.setPlugParam(finalMeicamVideoFx, mCurrSelectedVideoFx, param,
                                    finalMeicamVideoFx != null ? mCurSelectVideoClip.getInPoint() : mCurrSelectedVideoFx.getInPoint());
                            mEditorEngine.seekTimeline();
                        }
                    });
                } else if (Constants.PlugType.PATH.equals(param.valueType)) {
                    Bundle bundle = new Bundle();
                    bundle.putInt(MEDIA_TYPE, MediaData.TYPE_PHOTO);
                    AppManager.getInstance().jumpActivityForResult(DraftEditActivity.this,
                            MaterialSingleSelectActivity.class, bundle, REQUEST_PLUG_SELECT_PICTURE);
                }
                if (showAtomicEditMenuViewKeyFrame()) {
                    if (mCurrSelectedVideoFx != null) {
                        //timeline
                        updateVideoClipKeyFrame(mCurrSelectedVideoFx, param.paramName);
                    } else if (meicamVideoFx != null) {
                        updateVideoClipKeyFrame(meicamVideoFx, param.paramName);
                    }
                    updateKeyFrameStatus();
                }
                mEditorEngine.seekTimeline();
            }

            @Override
            public void onParamValueChangeStart(Plug plug, PlugDetail.Param param) {
                super.onParamValueChangeStart(plug, param);
                String valueType = param.valueType;
                String valueDefault = param.valueDefault;
                List<MeicamFxParam<?>> data = new ArrayList<>();
                if (Constants.PlugType.FLOAT.equals(valueType)) {
                    //float形式的，需要UI 轮盘
                    //In the form of float, the UI wheel is required
                    float defaultValue = Float.parseFloat(valueDefault);
                    data.add(new MeicamFxParam<>(MeicamFxParam.TYPE_FLOAT, param.paramName, defaultValue));
                }
                if (Constants.PlugType.POSITION2D_X.equals(valueType)) {
                    //POSITION2D_X形式的，需要UI 轮盘
                    //POSITION2D_X form, requiring UI wheel
                    float defaultValue = Float.parseFloat(valueDefault);
                    MeicamPosition2D position2D = null;
                    if (mCurrSelectedVideoFx != null) {
                        position2D = mCurrSelectedVideoFx.getPosition2DVal(param.paramName);
                    } else if (mCurSelectVideoClip != null) {
                        MeicamVideoFx videoFx = mCurSelectVideoClip.getVideoFx(MeicamVideoFx.SubType.SUB_TYPE_PLUG_FX,
                                plug.plugName, plug.getClipIndex());
                        position2D = videoFx.getPosition2DVal(param.paramName);
                    }
                    if (position2D == null) {
                        LogUtils.e("position2D==null");
                        return;
                    }
                    data.add(new MeicamFxParam<>(MeicamFxParam.TYPE_POSITION_2D, param.paramName, new MeicamPosition2D(defaultValue, position2D.y)));
                } else if (Constants.PlugType.POSITION2D_Y.equals(valueType)) {
                    //POSITION2D_Y形式的，需要UI 轮盘
                    //POSITION2D_Y form, requiring UI wheel
                    float defaultValue = Float.parseFloat(valueDefault);
                    MeicamPosition2D position2D = null;
                    if (mCurrSelectedVideoFx != null) {
                        position2D = mCurrSelectedVideoFx.getPosition2DVal(param.paramName);
                    } else if (mCurSelectVideoClip != null) {
                        MeicamVideoFx videoFx = mCurSelectVideoClip.getVideoFx(MeicamVideoFx.SubType.SUB_TYPE_PLUG_FX,
                                plug.plugName, plug.getClipIndex());
                        position2D = videoFx.getPosition2DVal(param.paramName);
                    }
                    if (position2D == null) {
                        LogUtils.e("position2D==null");
                        return;
                    }
                    data.add(new MeicamFxParam<>(MeicamFxParam.TYPE_POSITION_2D, param.paramName, new MeicamPosition2D(position2D.x, defaultValue)));
                } else if (Constants.PlugType.INT.equals(valueType)) {
                    int defaultValue = Integer.parseInt(valueDefault);
                    data.add(new MeicamFxParam<>(MeicamFxParam.TYPE_INT, param.paramName, defaultValue));
                } else if (Constants.PlugType.INT_CHOOSE.equals(valueType)) {
                    int defaultValue = Integer.parseInt(valueDefault);
                    data.add(new MeicamFxParam<>(MeicamFxParam.TYPE_INT, param.paramName, defaultValue));
                } else if (Constants.PlugType.COLOR.equals(valueType)) {
                    data.add(new MeicamFxParam<>(MeicamFxParam.TYPE_COLOR, param.paramName, valueDefault));
                }
                if (mCurrSelectedVideoFx != null) {
                    long atTime = mTimeline.getCurrentPosition() - mCurrSelectedVideoFx.getInPoint();
                    tryToAddTempKeyFrame(mCurrSelectedVideoFx, data, param.paramName, atTime);
                } else if (mCurSelectVideoClip != null) {
                    long atTime = mTimeline.getCurrentPosition() - mCurSelectVideoClip.getInPoint();
                    MeicamVideoFx videoFx = mCurSelectVideoClip.getVideoFx(MeicamVideoFx.SubType.SUB_TYPE_PLUG_FX,
                            plug.plugName, plug.getClipIndex());
                    tryToAddTempKeyFrame(videoFx, data, param.paramName, atTime);
                }

            }

            @Override
            public void onParamValueChangeFinish(Plug plug, PlugDetail.Param param) {
                super.onParamValueChangeFinish(plug, param);
                if (mCurrSelectedVideoFx != null) {
                    if (mCurrSelectedVideoFx.keyFrameProcessor().getKeyFrameCount() > 0) {
                        KeyFrameInfo keyFrameInfo = null;
                        BaseUIClip baseUIClip = mEditorTrackView.getDragView().getBaseUIClip();
                        if (baseUIClip != null) {
                            keyFrameInfo = baseUIClip.getKeyFrameInfo();
                        }
                        mPresenter.updateOrAddKeyFrame(mCurrSelectedVideoFx, keyFrameInfo, false);
                    }
                } else if (mCurSelectVideoClip != null) {
                    MeicamVideoFx videoFx = mCurSelectVideoClip.getVideoFx(MeicamVideoFx.SubType.SUB_TYPE_PLUG_FX, plug.plugName, plug.getClipIndex());
                    if (videoFx != null && videoFx.keyFrameProcessor().getKeyFrameCount() > 0) {
                        KeyFrameInfo keyFrameInfo = null;
                        ITrackClip mainSelectedClip = mEditTimeline.getMainSelectedClip();
                        if (mainSelectedClip != null) {
                            keyFrameInfo = mainSelectedClip.getKeyFrameInfo();
                        } else if (mEditorTrackView.getDragView() != null) {
                            BaseUIClip baseUIClip = mEditorTrackView.getDragView().getBaseUIClip();
                            if (baseUIClip != null) {
                                keyFrameInfo = baseUIClip.getKeyFrameInfo();
                            }
                        }
                        mPresenter.updateOrAddKeyFrame(videoFx, keyFrameInfo, mainSelectedClip != null);
                    }
                }
                updateKeyFrameStatus();
            }

            @Override
            public void onParamValueChange(Plug plug, PlugDetail.Param param) {
                super.onParamValueChange(plug, param);
                MeicamVideoFx meicamVideoFx = null;

                if (mCurSelectVideoClip != null) {
                    meicamVideoFx = mCurSelectVideoClip.getVideoFx(MeicamVideoFx.SubType.SUB_TYPE_PLUG_FX, plug.plugName, plug.getClipIndex());
                }
                if (meicamVideoFx == null && mCurrSelectedVideoFx == null) {
                    return;
                }
                mEditorEngine.setPlugParam(meicamVideoFx, mCurrSelectedVideoFx, param,
                        meicamVideoFx != null ? mCurSelectVideoClip.getInPoint() : mCurrSelectedVideoFx.getInPoint());
                mEditorEngine.seekTimeline();
            }
        });
    }

    private void tryToAddTempKeyFrame(IKeyFrameProcessor<?> keyFrameHolder, List<MeicamFxParam<?>> param, String keyFrameKey,
                                      long atTime) {
        long offSet = 0;
        if (mCurSelectVideoClip != null) {
            MeicamVideoTrack meicamVideoTrack = mTimeline.getVideoTrack(mCurSelectVideoClip.getTrackIndex());
            if (meicamVideoTrack != null) {
                MeicamTransition transition = meicamVideoTrack.getTransition(mCurSelectVideoClip.getIndex() - 1);
                if (transition != null) {
                    offSet = (long) (transition.getDuration() / 2F);
                }
            }
        }
        mPresenter.tryToAddTempKeyFrame(keyFrameHolder, param, keyFrameKey, atTime, offSet);
    }

    private boolean showAtomicEditMenuView() {
        View view = mBottomViewHelper.getView().getShowView();
        return view instanceof AtomicEditMenuView;
    }

    private boolean showAtomicEditMenuViewKeyFrame() {
        View view = mBottomViewHelper.getView().getShowView();
        if ((view instanceof AtomicEditMenuView)) {
            AtomicEditMenuView showView = (AtomicEditMenuView) view;
            return showView.isShowKeyFrameView();
        }
        return false;
    }

    /**
     * On message event.
     * 消息事件
     *
     * @param event the event
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onMessageEvent(MessageEvent event) {
        if (!isActive()) {
            return;
        }
        IBaseInfo baseInfo = event.getBaseInfo();
        boolean isClipEdit = mNavigationBar.isShow(R.string.nb_video_edit1) || mNavigationBar.isShow(R.string.nb_picture_edit1);
        if (event.getEventType() == MESSAGE_TYPE_PLUG_CLICK) {
            if (event.getObj() instanceof Plug) {
                Plug plug = (Plug) event.getObj();
                if (isClipEdit) {
                    if (mCurSelectVideoClip != null) {
                        AssetInfo assetInfo = new AssetInfo();
                        assetInfo.setEffectId(plug.plugName);
                        assetInfo.setEffectMode(BaseInfo.EFFECT_MODE_BUILTIN);
                        MeicamVideoFx meicamVideoFx = mEditorEngine.applyClipFx(assetInfo, mCurSelectVideoClip);
                        if (meicamVideoFx != null) {
                            PlugDetail detail = AtomicFxBridge.getPlugDetail(plug.effectPath);
                            for (PlugDetail.Param param : detail.paramList) {
                                mEditorEngine.setPlugParam(meicamVideoFx, null, param, mCurSelectVideoClip.getInPoint());
                            }
                        }
                        saveOperation();
                    }
                } else {
                    AssetInfo assetInfo = new AssetInfo();
                    assetInfo.setEffectId(plug.plugName);
                    assetInfo.setEffectMode(BaseInfo.EFFECT_MODE_BUILTIN);
                    assetInfo.setName(plug.getName());
                    mCurrSelectedVideoFx = mEditorEngine.addTimelineEffect(assetInfo, null, false);
                    PlugDetail detail = AtomicFxBridge.getPlugDetail(plug.effectPath);
                    for (PlugDetail.Param param : detail.paramList) {
                        mEditorEngine.setPlugParam(null, mCurrSelectedVideoFx, param, mCurrSelectedVideoFx.getInPoint());
                    }
                    saveOperation();
                }
            }
            showPlusMenu();
        } else if (event.getEventType() == MESSAGE_TYPE_WIDTH_CONFIRM_EFFECT) {
            if (baseInfo == null) {
                return;
            }
            if (baseInfo.getType() == AssetInfo.ASSET_THEME) {
                mEditorEngine.applyTheme(baseInfo.getPackageId());
            } else if (baseInfo.getType() == AssetInfo.ASSET_FILTER) {
                MeicamVideoFx videoClipFxInfo;
                if (isClipEdit) {
                    videoClipFxInfo = mEditorEngine.applyClipFilter(baseInfo, mCurSelectVideoClip);
                    if (videoClipFxInfo != null) {
                        MessageEvent.sendEvent(videoClipFxInfo.getIntensity(), MESSAGE_TYPE_UPDATE_FILTER_PROGRESS);
                    }
                } else {
                    //如果选中，展示的是编辑的导航栏，则替换，否则则添加
                    //If it is selected, it displays the edited navigation bar, replace it, otherwise add it.
                    if (mNavigationBar.isShow(R.string.nb_filter2) && mCurrSelectedFilterAndAdjustClip != null) {
                        boolean isBuildIn = baseInfo.getEffectMode() == BaseInfo.EFFECT_MODE_BUILTIN;
                        String type = isBuildIn ? TYPE_BUILD_IN : TYPE_PACKAGE;
                        String desc = isBuildIn ? baseInfo.getEffectId() : baseInfo.getPackageId();
                        mCurrSelectedVideoFx = mCurrSelectedFilterAndAdjustClip.getAdjustTimelineFx(MeicamTimelineVideoFxClip.ClipFxType.SUB_TYPE_TIMELINE_FILTER);
                        mCurrSelectedFilterAndAdjustClip = mEditorEngine.replaceFilterAndAdjustTimelineFx(mCurrSelectedFilterAndAdjustClip, type, MeicamTimelineVideoFxClip.ClipFxType.SUB_TYPE_TIMELINE_FILTER,
                                desc, baseInfo.getName());
                        if (mCurrSelectedFilterAndAdjustClip != null) {
                            if (mCurrSelectedVideoFx != null) {
                                MessageEvent.sendEvent(mCurrSelectedVideoFx.getIntensity(), MESSAGE_TYPE_UPDATE_FILTER_PROGRESS);
                            }
                        }

                    } else {
                        mCurrSelectedFilterAndAdjustClip = mEditorEngine.applyTimelineFilter(baseInfo);
                        if (mCurrSelectedFilterAndAdjustClip != null) {
                            mCurrSelectedVideoFx = mCurrSelectedFilterAndAdjustClip.getAdjustTimelineFx(MeicamTimelineVideoFxClip.ClipFxType.SUB_TYPE_TIMELINE_FILTER);
                        }
                    }
                    if (mCurrSelectedVideoFx != null && mCurrSelectedFilterAndAdjustClip != null) {
                        updateFilterAndAdjustTrack();
                        mEditorTrackView.setSelect(mCurrSelectedFilterAndAdjustClip.getTrackIndex(), mCurrSelectedFilterAndAdjustClip.getInPoint());
                        mEditorEngine.seekTimeline();
                        saveOperation();
                        MessageEvent.sendEvent(mCurrSelectedVideoFx.getIntensity(), MESSAGE_TYPE_UPDATE_FILTER_PROGRESS);
                    }
                }
            }
        } else if (event.getEventType() == MESSAGE_REMOVE_CLIP_FILTER) {
            mEditorEngine.removeClipFilter(mCurSelectVideoClip);
        } else if (event.getEventType() == MESSAGE_REMOVE_TIMELINE_FILTER) {
            deleteFilterAndAdjustClip();
        } else if (event.getEventType() == MESSAGE_TYPE_ADJUST) {
            if (mEditorEngine != null && mEditTimeline != null) {
                String effectName = event.getStrValue();
                if (isClipEdit) {
                    //视频/图片编辑导航栏正在显示
                    //Video/picture editing navigation bar is showing
                    mEditorEngine.setClipAdjustData(mCurSelectVideoClip, event.getProgress(), effectName);
                } else {
                    mEditorEngine.setTimelineAdjustData(mCurrSelectedVideoFx, event.getProgress(), effectName);
                }
            }
        } else if (event.getEventType() == MESSAGE_TYPE_ADJUST_CLICK) {
            if (mEditorEngine != null && mEditTimeline != null) {
                String effectName = event.getStrValue();
                if (!isClipEdit) {
                    //如果选中，展示的是编辑的导航栏，则替换，否则则添加
                    //If it is selected, it displays the edited navigation bar, replace it, otherwise add it.
                    if (mNavigationBar.isShow(R.string.nb_adjust2) && mCurrSelectedFilterAndAdjustClip != null) {
                        mCurrSelectedFilterAndAdjustClip = mEditorEngine.replaceFilterAndAdjustTimelineFx(mCurrSelectedFilterAndAdjustClip, TYPE_BUILD_IN, MeicamTimelineVideoFxClip.ClipFxType.SUB_TYPE_TIMELINE_ADJUST,
                                effectName, "");
                    } else {
                        String disPlayName = getResources().getString(R.string.adjust_num) + (TrackViewDataHelper.getInstance().getAdjustDrawText());
                        mCurrSelectedFilterAndAdjustClip = mEditorEngine.addFilterAndAdjustTimelineFx(TYPE_BUILD_IN, MeicamTimelineVideoFxClip.ClipFxType.SUB_TYPE_TIMELINE_ADJUST,
                                effectName, disPlayName);
                    }
                    if (mCurrSelectedFilterAndAdjustClip != null) {
                        mCurrSelectedVideoFx = mCurrSelectedFilterAndAdjustClip.getAdjustTimelineFx(mEditorEngine.getFxNameByEffectName(effectName));
                        updateFilterAndAdjustTrack();
                        mEditorTrackView.setSelect(mCurrSelectedFilterAndAdjustClip.getTrackIndex(), mCurrSelectedFilterAndAdjustClip.getInPoint());
                        mEditorEngine.seekTimeline();
                        saveOperation();
                    }
                }
            }
        } else if (event.getEventType() == MESSAGE_TYPE_CHANGE_CLIP_FILTER_PROGRESS) {
            mEditorEngine.changeClipFilter(mCurSelectVideoClip, event.getFloatValue());
        } else if (event.getEventType() == MESSAGE_TYPE_CHANGE_TIMELINE_FILTER_PROGRESS) {
            mEditorEngine.changeTimelineFilter(mCurrSelectedVideoFx, event.getFloatValue());
        } else if (event.getEventType() == MESSAGE_TYPE_CHANGE_ADJUST_FINISH ||
                event.getEventType() == MESSAGE_TYPE_CHANGE_CLIP_FILTER_FINISH ||
                event.getEventType() == MESSAGE_TYPE_CHANGE_TIMELINE_FILTER_FINISH ||
                event.getEventType() == MESSAGE_TYPE_MIXED_MODE_PROGRESS_FINISH) {
            saveOperation();
        } else if (event.getEventType() == MESSAGE_APPLY_ALL_FILTER) {
            mEditorEngine.applyAllFilter(mCurSelectVideoClip);
            ToastUtils.showShort(R.string.has_been_apply_to_all);
        } else if (event.getEventType() == MESSAGE_TYPE_ADD_ANIMATION_IN) {
            EditorEngine.getInstance().addVideoAnimation(mCurSelectVideoClip, (AssetInfo) event.getBaseInfo(), !checkSmartKeyingWhenPreview());
            AnimationData animationData = mEditorEngine.getVideoClipAnimation(mCurSelectVideoClip);
            refreshCoverViewNow(animationData);
        }
//        else if (event.getEventType() == MESSAGE_TYPE_ADD_ANIMATION_OUT) {
//            mEditorEngine.addAnimation((AnimationData) event.getObj(), mCurSelectVideoClip, AssetInfo.ASSET_ANIMATION_OUT, !checkSmartKeyingWhenPreview());
//            refreshCoverViewNow((AnimationData) event.getObj());
//        } else if (event.getEventType() == MESSAGE_TYPE_ADD_ANIMATION_GROUP) {
//            mEditorEngine.addAnimation((AnimationData) event.getObj(), mCurSelectVideoClip, AssetInfo.ASSET_ANIMATION_GROUP, !checkSmartKeyingWhenPreview());
//            refreshCoverViewNow((AnimationData) event.getObj());
//        }
        else if (event.getEventType() == MESSAGE_TYPE_CHANGE_ANIMATION_IN_DURATION) {
            mVideoFragment.stopEngine();
            mEditorEngine.changAnimationInAndComp((AnimationData) event.getObj(), mCurSelectVideoClip);
            AnimationData animationData = mEditorEngine.getVideoClipAnimation(mCurSelectVideoClip);
            refreshCoverViewNow(animationData);
        } else if (event.getEventType() == MESSAGE_TYPE_CHANGE_ANIMATION_OUT_DURATION) {
            mVideoFragment.stopEngine();
            mEditorEngine.changAnimationOut((AnimationData) event.getObj(), mCurSelectVideoClip);
            AnimationData animationData = mEditorEngine.getVideoClipAnimation(mCurSelectVideoClip);
            refreshCoverViewNow(animationData);
        }
//        else if (event.getEventType() == MESSAGE_TYPE_CHANGE_ANIMATION_GROUP_DURATION) {
//            mVideoFragment.stopEngine();
//            mEditorEngine.changAnimationInAndComp((AnimationData) event.getObj(), mCurSelectVideoClip);
//            refreshCoverViewNow((AnimationData) event.getObj());
//        }
        else if (event.getEventType() == MESSAGE_TYPE_REMOVE_ANIMATION_GROUP_DURATION) {
            mVideoFragment.stopEngine();
            mEditorEngine.removeAnimation(mCurSelectVideoClip, event.getIntValue());
            AnimationData animationData = mEditorEngine.getVideoClipAnimation(mCurSelectVideoClip);
            refreshCoverViewNow(animationData);
        } else if (event.getEventType() == MESSAGE_TYPE_CHANGE_SPEED_CURVE) {
            ChangeSpeedCurveInfo info = (ChangeSpeedCurveInfo) event.getBaseInfo();
            if (info == null) {
                return;
            }
            String curveSpeed = "";
            List<CurveSpeed> curveSpeedList = mCurSelectVideoClip.getCurveSpeedList();
            String originalSpeed = info.getSpeedOriginal();
            if (baseInfo != null && !getString(R.string.original).equals(baseInfo.getName())) {
                if (getCurveInfo(originalSpeed) == null) {
                    curveSpeedList.add(new CurveSpeed(originalSpeed, originalSpeed));
                }
                for (CurveSpeed curveSpeed1 : curveSpeedList) {
                    if (curveSpeed1.getSpeedOriginal().equals(originalSpeed)) {
                        curveSpeed = curveSpeed1.getSpeed();
                        break;
                    }
                }
            }
            if (mCurSelectVideoClip != null && (!TextUtils.isEmpty(mCurSelectVideoClip.getCurveSpeedName())) && mCurSelectVideoClip.getCurveSpeedName().equals(info.getName())) {
                curveSpeed = mCurSelectVideoClip.getCurveSpeed();
            }
            changeSpeedCurve(curveSpeed, info.getName());
        } else if (event.getEventType() == MESSAGE_TYPE_CHANGE_SPEED_CURVE_VIEV_UPDATE) {//进入曲线界面
            if (baseInfo != null && getString(R.string.original).equals(baseInfo.getName())) {
                //移除曲线变速
                //Remove curve speed change
                changeSpeedCurve("", "");
                saveOperation();
            } else {
                mVideoFragment.stopEngine();
                final ChangeSpeedCurveInfo info = (ChangeSpeedCurveInfo) event.getBaseInfo();
                mBottomViewHelper.showCurveSpeed(mCurSelectVideoClip, baseInfo, new EditChangeSpeedCurveView.SpeedCurveListener() {
                    @Override
                    public void onStartPlay(String originalSpeed, String speed) {
                        if (info == null || mCurSelectVideoClip == null) {
                            return;
                        }
                        List<CurveSpeed> curveSpeedList = mCurSelectVideoClip.getCurveSpeedList();
                        for (CurveSpeed curveSpeed1 : curveSpeedList) {
                            if (curveSpeed1.getSpeedOriginal().equals(originalSpeed)) {
                                curveSpeed1.setSpeed(speed);
                                break;
                            }
                        }
                        changeSpeedCurve(speed, info.getName());
                    }

                    @Override
                    public void onSeekPosition(long position) {
                        if (mCurSelectVideoClip != null) {
                            long timelinePosition;
                            if (!TextUtils.isEmpty(mCurSelectVideoClip.getClipVariableSpeedCurvesString())) {
                                timelinePosition = mCurSelectVideoClip.getTimelinePosByClipPosCurvesVariableSpeed(position + mCurSelectVideoClip.getTrimIn());
                            } else {
                                timelinePosition = mCurSelectVideoClip.getInPoint() + position;
                            }

                            if (timelinePosition >= mCurSelectVideoClip.getOutPoint()) {
                                NvsRational nvsRational = mEditorEngine.getVideoRational();
                                if (nvsRational != null) {
                                    //当前片段出点，减去，1帧毫秒数/2
                                    //Current clip output point, minus, 1 frame millisecond/2
                                    timelinePosition = mCurSelectVideoClip.getOutPoint() - TIMEBASE / nvsRational.num / 2;
                                } else {
                                    timelinePosition = mCurSelectVideoClip.getOutPoint();
                                }

                            }
                            mEditorEngine.seekTimeline(timelinePosition, 0);
                            seekViewOnPlay(timelinePosition);
                        }

                    }

                    @Override
                    public void onDismiss(boolean confirm) {
                        if (mEditorEngine.isPlaying()) {
                            mEditorEngine.stop();
                        }
                        mEditOperationView.updateImageVisible(true);
                        mPresenter.getPipLineRegionAsync(new DraftEditPresenter.LineRegionDataCallBack() {
                            @Override
                            public void onResultCallback(List<LineRegionClip> data) {
                                mEditTimeline.setPipRegion(data);
                            }
                        });
                        saveOperation();
                    }
                });
                mEditOperationView.updateImageVisible(false);
              /*  MYEditorTimelineSpanView timelineSpan = mEditTimeline.getTimelineSpan();
                if (timelineSpan != null) {
                    timelineSpan.changeDurationTextState(true);
                }*/
            }
        } else if (event.getEventType() == MESSAGE_TYPE_ANIMATION_CONFIRM_EFFECT) {
            //隐藏撤销，恢复
            //Hide Undo, Restore
            mEditOperationView.updateCancelRecoverVisible(true);
            saveOperation();
        } else if (event.getEventType() == MESSAGE_TYPE_RESET_ADJUST_CLIP) {
            EditorEngine.getInstance().resetClipAdjustData(mCurSelectVideoClip);
        } else if (event.getEventType() == MESSAGE_TYPE_RESET_ADJUST_TIMELINE) {
            EditorEngine.getInstance().resetTimelineAdjustData(mCurrSelectedFilterAndAdjustClip);
        } else if (event.getEventType() == MESSAGE_TYPE_APPLY_ADJUST_TO_ALL) {
            boolean success = EditorEngine.getInstance().applyClipAdjustToAll(mCurSelectVideoClip);
            if (success) {
                ToastUtils.showShort(R.string.has_been_apply_to_all);
            }
        } else if (event.getEventType() == MESSAGE_TYPE_MIXED_MODE) {
            //混合模式
            //Blend mode
            if (baseInfo != null && mCurSelectVideoClip != null) {
                EditMixedModeInfo info = (EditMixedModeInfo) baseInfo;
                mEditorEngine.setBlendingMode(mCurSelectVideoClip, info.getMixedMode());
                saveOperation();
            }
        } else if (event.getEventType() == MESSAGE_TYPE_MIXED_MODE_PROGRESS) {
            //混合模式，设置透明度
            //Blend mode, setting transparency
            mEditorEngine.changeOpacity(mCurSelectVideoClip, event.getFloatValue());
        } else if (event.getEventType() == MESSAGE_TYPE_REFRESH_CAPTION_RANGE) {
            if (!this.equals(AppManager.getInstance().currentActivity())) {
                return;
            }
            //刷新字幕框，范围
            //refresh caption range
            mVideoFragment.openFxEditMode(EDIT_MODE_CAPTION, mCurrSelectedCaptionStickClip, true);
        } else if (event.getEventType() == MESSAGE_TYPE_ADD_NORMAL_CAPTION) {
            if (!this.equals(AppManager.getInstance().currentActivity())) {
                return;
            }
            //新增普通字幕模板
            //Add normal caption
            long inPoint = EditorEngine.getInstance().getCurrentTimelinePosition();
            long captionDuration = 4 * Constants.NS_TIME_BASE;
            long outPoint = inPoint + captionDuration;
            IBaseInfo info = event.getBaseInfo();
            if (info instanceof AssetInfo) {
                AssetInfo assetInfo = (AssetInfo) info;
                if (mCurrSelectedCaptionStickClip instanceof MeicamCaptionClip) {
                    mEditorEngine.removeCaptionStickClip(mCurrSelectedCaptionStickClip);
                }
                MeicamCaptionClip meicamCaptionClip = mEditorEngine
                        .addCaption(assetInfo.getName(), inPoint, outPoint, true, CommonData.TYPE_COMMON_CAPTION, true);
                CaptionCommand.setParam(meicamCaptionClip, CaptionCommand.PARAM_CAPTION_STYLE, assetInfo.getPackageId());
                mVideoFragment.openFxEditMode(EDIT_MODE_CAPTION, mCurrSelectedCaptionStickClip, true);
                mEditorEngine.seekTimeline(NvsStreamingContext.STREAMING_ENGINE_SEEK_FLAG_SHOW_CAPTION_POSTER);
            }

        }

    }

    private CurveSpeed getCurveInfo(String originalSpeed) {
        if (mCurSelectVideoClip == null) {
            return null;
        }
        List<CurveSpeed> curveSpeedList = mCurSelectVideoClip.getCurveSpeedList();
        for (CurveSpeed curveSpeed : curveSpeedList) {
            if (curveSpeed.getSpeedOriginal().equals(originalSpeed)) {
                return curveSpeed;
            }
        }
        return null;
    }

    /**
     * 曲线变速
     * Curve of variable speed
     *
     * @param curveSpeed the curve speed
     * @param speedName  the speed name
     */
    public void changeSpeedCurve(String curveSpeed, String speedName) {
        if (mCurSelectVideoClip == null) {
            return;
        }
        mPresenter.dealWithCurveSpeed(mCurSelectVideoClip, mCurrentTrackIndex, curveSpeed, speedName);

        if (mBottomViewContainer.getShowView() instanceof EditChangeSpeedCurveView) {
            ((EditChangeSpeedCurveView) mBottomViewContainer.getShowView()).updateDuration(mCurSelectVideoClip.getOutPoint() - mCurSelectVideoClip.getInPoint());
        }
        if (mainTrackSelected()) {
            ITrackClip mainSelectedClip = mEditTimeline.getMainSelectedClip();
            if (mainSelectedClip != null) {
                mainSelectedClip.setInPoint(mCurSelectVideoClip.getInPoint());
                mainSelectedClip.setOutPoint(mCurSelectVideoClip.getOutPoint());
                SpeedInfo speedInfo = mainSelectedClip.getSpeedInfo();
                speedInfo.setSpeed(mCurSelectVideoClip.getSpeed());
                speedInfo.setSpeedName(mCurSelectVideoClip.getCurveSpeedName());
                mainSelectedClip.setKeyFrameInfo(mPresenter.getKeyFrameInfo(mCurSelectVideoClip.findPropertyVideoFx(), NvsConstants.KEY_CROPPER_TRANS_X));
                mEditTimeline.changeMainTrackClipSpeed(mainSelectedClip, mPresenter.getTimeline().getDuration());
            }
            //更新时间轴时长
            //Update duration of timeline
            mEditOperationView.setDurationText(FormatUtils.microsecond2Time(mPresenter.getTimelineCurrentPosition())
                    + "/" + FormatUtils.microsecond2Time(mTimeline.getDuration()));
            mEditorTrackView.initWidth(mTimeline.getDuration(), 0);
            mPresenter.updateTransitionByVideoClipDuration(mCurSelectVideoClip.getIndex());
            if (mEditorTrackView.isShowTrackView()) {
                updateStickerTrack();
            }
        } else {
            mEditorTrackView.showPipTrackView(mTimeline);
            mEditorTrackView.setSelect(mCurrentTrackIndex - 1, mCurSelectVideoClip.getInPoint());
        }
        mEditorEngine.seekTimeline(0);
        mPresenter.checkTrackDuration();
        saveOperation();
        // mEditorEngine.seekTimeline(mCurSelectVideoClip.getInPoint(), 0);
        int state = NvsStreamingContext.getInstance().getStreamingEngineState();
        if (state == NvsStreamingContext.STREAMING_ENGINE_STATE_PLAYBACK) {
            mVideoFragment.stopEngine();
        }
        mVideoFragment.playVideo(mCurSelectVideoClip.getInPoint(), mCurSelectVideoClip.getOutPoint());
    }

    private void saveOperation() {
        Log.d(TAG, "saveOperation() returned: ", new Throwable());
        mEditOperateManager.addOperate();
    }


    @Override
    public void scrollX(int dx, int oldDx, boolean fromMainTrack) {
        //LogUtils.d("ScrollX,fromMainTrack=" + fromMainTrack+",mSeekFlag="+mSeekFlag+",dx="+dx);
        if (!fromMainTrack && mSeekFlag == FLAG_TRACK_VIEW) {
            /*必须要用smooth，因为滚动的时候调用scrollTo是无效的
             *You must use smooth, because it is invalid to call scrollTo when scrolling
             **/
            mEditTimeline.smoothScrollToFromUser(dx);
        }
    }

    @Override
    public void startScroll() {
        mSeekFlag = FLAG_TRACK_VIEW;
        if (mEditorEngine.isPlaying()) {
            mEditorEngine.stop();
        }
    }

    @Override
    public void clickOutSide() {
        LogUtils.d("clickOutSide");
        if (mNavigationBar.isShow(R.string.nb_ratio1)) {
            //如果当前是比例，不做任何操作
            //If the current is scale, do nothing
            return;
        }
        mVideoFragment.updateTransformFx(null);
        mEditTimeline.notShowSpanView();
        mVideoFragment.hideOperationBox();
        hideBottomView();
        if (mNavigationBar.isShow(R.string.nb_video_edit1) || mNavigationBar.isShow(R.string.nb_picture_edit1)) {
            if (mainTrackSelected()) {
                switchToMainMenu();
            } else {
                mEditorTrackView.clickOutSide();
                mVideoFragment.setTransformViewVisible(GONE);
                mNavigationBar.show(R.string.nb_pip1);
            }
        } else if (mNavigationBar.isShow(R.string.nb_sticker2) || mNavigationBar.isShow(R.string.nb_caption2)
                || mNavigationBar.isShow(R.string.nb_combination_caption2)) {
            mNavigationBar.show(R.string.nb_sticker1);
        } else if (mNavigationBar.isShow(R.string.nb_effect2)) {
            mNavigationBar.show(R.string.nb_effect1);
            mEditorTrackView.clickOutSide();
        } else if (mNavigationBar.isShow(R.string.nb_audio2)) {
            mNavigationBar.show(R.string.nb_audio1);
        } else if (mNavigationBar.isShow(R.string.nb_filter2) || mNavigationBar.isShow(R.string.nb_adjust2)) {
            mEditorTrackView.clickOutSide();
            mNavigationBar.show(R.string.nb_filter1);
        }
        if (!(mNavigationBar.isShow(R.string.nb_animate2))) {
            mCurrentTrackIndex = -1;
        }
    }

    @Override
    public void clickToMusicMenu() {
        refreshAudioView();
        toOtherMenu();
        mNavigationBar.show(R.string.nb_audio1);
    }

    @Override
    public void onTrackViewLongClick(BaseUIClip baseUIClip) {
        if (baseUIClip == null) {
            return;
        }
        String type = baseUIClip.getType();
        if (TextUtils.isEmpty(type)) {
            return;
        }
        clickOutSide();
    }

    @Override
    public Object dragEnd(BaseUIClip oldUiClip, int newTrackIndex, long newInPoint, boolean isToEnd) {
        // 需要返回新的底层对象object
        //The new underlying object needs to be returned.
        if (oldUiClip == null) {
            return null;
        }
        LogUtils.d("dragEnd: beforeTrackIndex: " + oldUiClip.getTrackIndex() + " beforeInPoint: " + oldUiClip.getInPoint()
                + " newTrackIndex: " + newTrackIndex + " newInPoint: " + newInPoint);
        String type = oldUiClip.getType();

        int regionType = -1;
        int trackIndex = oldUiClip.getTrackIndex();
        if (CommonData.CLIP_CAPTION.equals(type)) {
            regionType = LineRegionClip.REGION_TYPE_CAPTION;
        } else if (CommonData.CLIP_COMPOUND_CAPTION.equals(type)) {
            regionType = LineRegionClip.REGION_TYPE_COMPOUND_CAPTION;
        } else if (CommonData.CLIP_STICKER.equals(type)) {
            regionType = LineRegionClip.REGION_TYPE_STICKER;
        } else if (CommonData.CLIP_VIDEO.equals(type) || CommonData.CLIP_IMAGE.equals(type)) {
            regionType = LineRegionClip.REGION_TYPE_PIP;
            trackIndex += 1;
        }
        LineRegionClip regionClip = mEditTimeline.findRegionClip(regionType, trackIndex, oldUiClip.getInPoint());
        if (regionClip != null) {
            regionClip.setTrackIndex(newTrackIndex + 1);
            regionClip.setInPoint(newInPoint);
            regionClip.setOutPoint(newInPoint + (oldUiClip.getTrimOut() - oldUiClip.getTrimIn()));
        }
        Object obj = mPresenter.dealWidthTrackDragEnd(type, oldUiClip, newInPoint, newTrackIndex, mTimeline);
        mPresenter.checkTrackDuration();
        if (CommonData.CLIP_AUDIO.equals(oldUiClip.getType())) {
            refreshAudioView();
        } else {
            boolean notNeedIncreaseTrackCount = CommonData.CLIP_VIDEO.equals(type)
                    || CommonData.CLIP_IMAGE.equals(type)
                    || CommonData.CLIP_AUDIO.equals(oldUiClip.getType());
            if (!notNeedIncreaseTrackCount) {
                mEditorTrackView.setCanIncreaseTrackCount(true);
                if (isToEnd) {
                    mEditorTrackView.increaseMaxTrackCount();
                }
            } else {
                mEditorTrackView.setCanIncreaseTrackCount(false);
            }
            mEditorTrackView.setData(TrackViewDataHelper.getInstance().getTrackData(oldUiClip.getType()),
                    mEditorEngine.getCurrentTimeline().getDuration(), oldUiClip.getType());
        }
        mEditorEngine.seekTimeline(0);
        saveOperation();
        return obj;
    }

    AnimatorSet animatorSet;

    @Override
    public void dragToEnd(boolean isToEnd) {
        if (isToEnd) {
            if (mWarningLine.getVisibility() == VISIBLE) {
                return;
            }
            mWarningLine.setVisibility(VISIBLE);
            ObjectAnimator alpha = ObjectAnimator.ofFloat(mWarningLine, "alpha", 0, 1F);
            if (animatorSet == null) {
                animatorSet = new AnimatorSet();
            }
            if (animatorSet.isRunning()) {
                return;
            }
            animatorSet.setDuration(200);
            animatorSet.playTogether(alpha);
            animatorSet.addListener(new Animator.AnimatorListener() {
                @Override
                public void onAnimationStart(Animator animation) {

                }

                @Override
                public void onAnimationEnd(Animator animation) {
                }

                @Override
                public void onAnimationCancel(Animator animation) {

                }

                @Override
                public void onAnimationRepeat(Animator animation) {

                }
            });
            animatorSet.start();
        } else {
            if (animatorSet != null) {
                animatorSet.cancel();
                mWarningLine.setVisibility(GONE);
            }
        }
    }

    @Override
    public void onSelectClip(BaseUIClip baseUIClip) {
        if (baseUIClip == null) {
            return;
        }
        //选择子轨道的时候先把clip置空
        //When selecting sub-tracks, set the clip to null first
        mCurSelectVideoClip = null;
        BaseItemView dragView = mEditorTrackView.getDragView();
        long timelineCurrentPosition = mPresenter.getTimelineCurrentPosition();
        if (dragView != null) {
            dragView.checkKeyFrame(timelineCurrentPosition);
            if (showAtomicEditMenuView()) {
                dragView.enableKeyFrame(false);
            }
        }
        boolean showKeyFrame = false;
        mEditTimeline.notShowSpanView();
        mEditTimeline.enableThumbnailCoverEditMode(false);
        mEditTimeline.enableThumbnailAnimation(false);
        mSelectTrackData = baseUIClip;
        mVideoFragment.setTransformViewVisible(GONE);
        if (CommonData.CLIP_VIDEO.equals(baseUIClip.getType()) || CommonData.CLIP_IMAGE.equals(baseUIClip.getType())) {
            //注意这里index 需要加 1 不能随便删除
            //Note that the index needs to be added by 1 and cannot be deleted randomly
            mCurSelectVideoClip = mEditorEngine.getVideoClip(baseUIClip.getTrackIndex() + 1,
                    baseUIClip.getInPoint());
            Log.e(TAG, "onSelectClip: " + baseUIClip.getTrackIndex() + " " + baseUIClip.getInPoint());
            if (null == mCurSelectVideoClip) {
                return;
            }
            if (mCurSelectVideoClip.getInPoint() > timelineCurrentPosition) {
                mEditTimeline.scrollToFromUser(mCurSelectVideoClip.getInPoint());
            } else if (timelineCurrentPosition > mCurSelectVideoClip.getOutPoint()) {
                mEditTimeline.scrollToFromUser(mCurSelectVideoClip.getOutPoint());
            }
            if (mEditorEngine.isPlaying()) {
                mEditorEngine.stop();
            }
            mCurrentTrackIndex = baseUIClip.getTrackIndex() + 1;
            mVideoFragment.updateTransformFx(mCurSelectVideoClip, true);
            updateVideoClipUI();
            showEditNavigation();
            showKeyFrame = true;
        } else if (CommonData.CLIP_AUDIO.equals(baseUIClip.getType())) {
            mCurrSelectedAudioClip = mEditorEngine.getAudioClip(baseUIClip.getTrackIndex(), baseUIClip.getClipIndexInTrack());
            if (mCurrSelectedAudioClip != null) {
                mEditorTrackView.audioScrollX(baseUIClip);
                EditorEngine.getInstance().setBaseUIClip(mCurrSelectedAudioClip);
                mNavigationBar.show(R.string.nb_audio2);
            } else {
                Log.e(TAG, "onSelectClip: mCurrSelectedAudioClip is null");
            }
        } else if (CommonData.CLIP_CAPTION.equals(baseUIClip.getType())) {
            selectedCaptionSicker(baseUIClip);
            mMultiHelper.updateCaptionView(mCurrSelectedCaptionStickClip);
            if (mMultiBottomView.isShow() && mMultiBottomView.getType() == TYPE_MENU_CAPTION) {
                if (mCurrSelectedCaptionStickClip instanceof MeicamCaptionClip) {
                    mMultiBottomView.setEditText(((MeicamCaptionClip) mCurrSelectedCaptionStickClip).getText());
                }
            }
            mNavigationBar.show(R.string.nb_caption2);
            showKeyFrame = true;
        } else if (CommonData.CLIP_STICKER.equals(baseUIClip.getType())) {
            selectedCaptionSicker(baseUIClip);
            showStickNavigation();
            showKeyFrame = true;
        } else if (CommonData.CLIP_COMPOUND_CAPTION.equals(baseUIClip.getType())) {
            selectedCaptionSicker(baseUIClip);
            mNavigationBar.show(R.string.nb_combination_caption2);
        } else if (CommonData.CLIP_TIMELINE_FX.equals(baseUIClip.getType())) {

            MeicamTimelineVideoFxClip currSelectedVideoFx = mEditorEngine.getTimelineVideoFxClip(baseUIClip.getTrackIndex(), baseUIClip.getInPoint());
            mCurrSelectedVideoFx = currSelectedVideoFx;
            if (currSelectedVideoFx != null) {
                if (currSelectedVideoFx.isBuildFx()) {
                    mNavigationBar.show(R.string.nb_effect1);
                    showPlusMenu();
                } else {
                    mNavigationBar.show(R.string.nb_effect2);
                    mBottomViewHelper.getView().dismissView();
                }
                if (currSelectedVideoFx.getInPoint() > timelineCurrentPosition) {
                    mEditTimeline.scrollToFromUser(currSelectedVideoFx.getInPoint());
                } else if (timelineCurrentPosition > currSelectedVideoFx.getOutPoint()) {
                    mEditTimeline.scrollToFromUser(currSelectedVideoFx.getOutPoint());
                }
            }
        } else if (CommonData.CLIP_FILTER.equals(baseUIClip.getType())) {
            mNavigationBar.show(R.string.nb_filter2);
            mCurrSelectedFilterAndAdjustClip = mEditorEngine.getTimelineFilterAndAdjustClip(baseUIClip.getTrackIndex(), baseUIClip.getInPoint());
            if (mCurrSelectedFilterAndAdjustClip != null) {
                mCurrSelectedVideoFx = mCurrSelectedFilterAndAdjustClip.getAdjustTimelineFx(MeicamTimelineVideoFxClip.ClipFxType.SUB_TYPE_TIMELINE_FILTER);
                if (mCurrSelectedFilterAndAdjustClip.getInPoint() > timelineCurrentPosition) {
                    mEditTimeline.scrollToFromUser(mCurrSelectedFilterAndAdjustClip.getInPoint());
                } else if (timelineCurrentPosition > mCurrSelectedFilterAndAdjustClip.getOutPoint()) {
                    mEditTimeline.scrollToFromUser(mCurrSelectedFilterAndAdjustClip.getOutPoint());
                }
            }

        } else if (CommonData.CLIP_ADJUST.equals(baseUIClip.getType())) {
            mNavigationBar.show(R.string.nb_adjust2);
            mCurrSelectedFilterAndAdjustClip = mEditorEngine.getTimelineFilterAndAdjustClip(baseUIClip.getTrackIndex(), baseUIClip.getInPoint());
            if (mCurrSelectedFilterAndAdjustClip != null) {
                if (mCurrSelectedFilterAndAdjustClip.getInPoint() > timelineCurrentPosition) {
                    mEditTimeline.scrollToFromUser(mCurrSelectedFilterAndAdjustClip.getInPoint());
                } else if (timelineCurrentPosition > mCurrSelectedFilterAndAdjustClip.getOutPoint()) {
                    mEditTimeline.scrollToFromUser(mCurrSelectedFilterAndAdjustClip.getOutPoint());
                }
            }
        }
        if (mCurSelectVideoClip != null) {
            updateVideoClipKeyFrame(mCurSelectVideoClip.findPropertyVideoFx(), NvsConstants.KEY_CROPPER_TRANS_X);
        }
        updateKeyFrameStatus();
        if (!showKeyFrame) {
            mEditOperationView.notShowKeyFrameView();
        }
    }

    @Override
    public void onUnselectedClip() {
        if (mEditTimeline.getMainSelectedClip() == null) {
            mEditOperationView.notShowKeyFrameView();
        }
        mCurrSelectedCaptionStickClip = null;
        mCurrSelectedVideoFx = null;
    }


    @Override
    public void onTimelineChanged(MeicamTimeline timeline, boolean needSave) {
        if (needSave) {
            saveOperation();
        }
        LogUtils.d("showNavigationName=" + getString(mNavigationBar.getShowingNavigationName()));
        if (mNavigationBar.isShow(R.string.nb_effect1) || mNavigationBar.isShow(R.string.nb_effect2)) {
           updateTimelineFxTrack();
        } else if (mNavigationBar.isShow(R.string.nb_sticker1)) {
            //字幕/贴纸/组合字幕共用同一个
            //Subtitles/stickers/combined captions share the same
            updateStickerTrack();
        } else if (mNavigationBar.isShow(R.string.nb_video_edit1) || mNavigationBar.isShow(R.string.nb_picture_edit1)) {
            if (!mainTrackSelected()) {
                mEditorTrackView.showPipTrackView(mTimeline);
                if (mSelectTrackData != null) {
                    int trackIndex = mSelectTrackData.getTrackIndex();
                    long inPoint = mSelectTrackData.getInPoint();
                    mEditorTrackView.setSelect(trackIndex, (int) inPoint);
                    mCurSelectVideoClip = mEditorEngine.getVideoClip(trackIndex + 1, inPoint);
                    mCurrentTrackIndex = trackIndex + 1;
                    mVideoFragment.updateTransformFx(mCurSelectVideoClip, true);
                }
            }
        } else if (mNavigationBar.isShow(R.string.nb_audio1)) {
            refreshAudioView();
        }
        //移动字幕 贴纸  组合字幕 ----------------------------
        //Mobile caption sticker combined caption---------
        if (mIsAddTitleTheme && !mPresenter.isAddTitleTheme()) {
            //当前这一次是片头主题，上一次不是片头主题  往后移动 字幕/贴纸
            mPresenter.moveCaptionOrSticker(mPresenter.getTitleThemeDuration());
        } else if (!mIsAddTitleTheme && mPresenter.isAddTitleTheme()) {
            mPresenter.moveCaptionOrSticker(-mPresenter.getTitleThemeDuration());
        }
        mPresenter.setAddTitleTheme(mIsAddTitleTheme);
        //移动字幕 贴纸 等标识线------------------------------------------
        // Mobile caption stickers and other identification lines-----
        //toOtherMenu();
    }


    @Override
    public void onNeedTrackSelectChanged(int trackIndex, long inPoint) {
        if (mEditorTrackView != null) {
            mEditorTrackView.setSelect(trackIndex, inPoint);
        }
    }

    @Override
    public void onTips(int code) {
        ToastUtils.showShort(R.string.unusable_space);
    }

    @Override
    public void refreshEditorTimelineView(int type) {
        mEditorTrackView.initWidth(mTimeline.getDuration(), 0);
        if (type == TYPE_DELETE_CLIP) {
            mEditTimeline.deleteMainTrackClip(mEditTimeline.getMainSelectedClip());
            mPresenter.checkTrackDuration();
        } else if (type == TYPE_CUT_CLIP) {
            if (mNavigationBar.isShow(R.string.nb_video_edit1) || mNavigationBar.isShow(R.string.nb_picture_edit1)) {
                //视频/图片编辑导航栏正在显示
                //Video/picture editing navigation bar is showing
                if (mainTrackSelected()) {
                    ITrackClip mainSelectedClip = mEditTimeline.getMainSelectedClip();
                    if (mainSelectedClip == null) {
                        return;
                    }
                    MeicamVideoClip videoClip = mEditorEngine.getVideoClip(TRACK_INDEX_MAIN, mainSelectedClip.getIndexInTrack() + 1);
                    if (videoClip != null) {
                        long outPoint = mCurSelectVideoClip.getOutPoint();
                        long trimOut = mCurSelectVideoClip.getTrimOut();
                        mCurSelectVideoClip = videoClip;
                        mVideoFragment.updateTransformFx(mCurSelectVideoClip, true);
                        mainSelectedClip.setKeyFrameInfo(mPresenter.getKeyFrameInfo(mCurSelectVideoClip.findPropertyVideoFx(), NvsConstants.KEY_CROPPER_TRANS_X));
                        ITrackClip iTrackClip = mEditTimeline.splitMainTrackClip(mainSelectedClip.getIndexInTrack(),
                                outPoint, trimOut, mCurSelectVideoClip.getInPoint(), mCurSelectVideoClip.getTrimIn());
                        if (iTrackClip != null) {
                            mEditTimeline.showSpanView(iTrackClip);
                        }
                    }
                } else {
                    if (mSelectTrackData != null) {

                        int trackIndex = mSelectTrackData.getTrackIndex();
                        long inPoint = mSelectTrackData.getInPoint();
                        mEditorTrackView.setSelect(trackIndex, (int) inPoint);
                        mCurSelectVideoClip = mEditorEngine.getVideoClip(trackIndex + 1, inPoint);
                        mCurrentInPoint = inPoint;
                        mCurrentTrackIndex = trackIndex + 1;
                        BaseItemView dragView = mEditorTrackView.getDragView();
                        if (dragView != null) {
                            /*重新设置关键帧*/
                            dragView.updateKeyFrame(mPresenter.getKeyFrameInfo(mCurSelectVideoClip.findPropertyVideoFx(),
                                    NvsConstants.KEY_CROPPER_TRANS_X), mPresenter.getTimelineCurrentPosition());
                        }
                    }
                }
            }
        } else if (type == TYPE_FREEZE_CLIP) {
            if (mNavigationBar.isShow(R.string.nb_video_edit1)) {
                //视频编辑导航栏正在显示
                //Video editing navigation bar is showing
                if (mainTrackSelected()) {
                    //mEditTimeline.setSelectSpan(mEditorEngine.getCurrentTimelinePosition());
                    ITrackClip mainSelectedClip = mEditTimeline.getMainSelectedClip();
                    if (mainSelectedClip != null) {
                        MeicamVideoClip videoClip = mEditorEngine.getVideoClip(0, mainSelectedClip.getInPoint());
                        if (videoClip != null) {
                            mCurSelectVideoClip = videoClip;
                            //TimelineData.getInstance().setSelectedMeicamClipInfo(mCurSelectVideoClip);
                        }
                    }
                } else {
                    if (mSelectTrackData != null) {
                        int trackIndex = mSelectTrackData.getTrackIndex();
                        long inPoint = mSelectTrackData.getInPoint();
                        mEditorTrackView.setSelect(trackIndex, (int) inPoint);
                        mCurSelectVideoClip = mEditorEngine.getVideoClip(trackIndex + 1, inPoint);
                        //TimelineData.getInstance().setSelectedMeicamClipInfo(mCurSelectVideoClip);
                        mCurrentTrackIndex = trackIndex + 1;
                    }
                }
            }
        } else if (type == TYPE_REVERT_CLIP) {
            if (mNavigationBar.isShow(R.string.nb_video_edit1)) {
                //视频编辑导航栏正在显示
                //Video editing navigation bar is showing
                KeyFrameInfo keyFrameInfo = mPresenter.getKeyFrameInfo(mCurSelectVideoClip.findPropertyVideoFx(), NvsConstants.KEY_CROPPER_TRANS_X);
                if (!mainTrackSelected()) {
                    if (mCurSelectVideoClip != null) {
                        mEditorTrackView.showPipTrackView(mTimeline);
                        int trackIndex = mCurSelectVideoClip.getTrackIndex();
                        long inPoint = mCurSelectVideoClip.getInPoint();
                        mEditorTrackView.setSelect(trackIndex, (int) inPoint);
                        BaseItemView dragView = mEditorTrackView.getDragView();
                        if (dragView != null) {
                            /*倒放后关键帧的点都会变，所以重新设置一下
                             *The key frame points will change after rewinding, so reset it
                             **/
                            dragView.updateKeyFrame(keyFrameInfo, mPresenter.getTimelineCurrentPosition());
                        }
                    }
                } else {
                    ITrackClip trackClip = mEditTimeline.getMainSelectedClip();
                    if (trackClip == null || mCurSelectVideoClip == null) {
                        return;
                    }
                    String filePath;
                    if (CommonData.CLIP_IMAGE.equals(mCurSelectVideoClip.getVideoType())) {
                        filePath = mCurSelectVideoClip.getFilePath();
                    } else {
                        filePath = mCurSelectVideoClip.getRemotePath();
                        if (TextUtils.isEmpty(filePath)) {
                            filePath = mCurSelectVideoClip.getFilePath();
                        }
                    }
                    trackClip.setAssetPath(mCurSelectVideoClip.getVideoReverse() ? mCurSelectVideoClip.getReverseFilePath() : filePath);
                    trackClip.setTrimIn(mCurSelectVideoClip.getTrimIn());
                    trackClip.setTrimOut(mCurSelectVideoClip.getTrimOut());
                    trackClip.setInPoint(mCurSelectVideoClip.getInPoint());
                    trackClip.setOutPoint(mCurSelectVideoClip.getOutPoint());
                    trackClip.setThumbNailInfo(null);
                    /*倒放后关键帧的点都会变，所以重新设置一下
                     * The key frame points will change after rewinding, so reset it
                     * */
                    trackClip.setKeyFrameInfo(keyFrameInfo);
                     mEditTimeline.updateThumbnail(trackClip, true);
                }
                if (keyFrameInfo != null) {
                    //倒放后，如果有关键帧，videoClip位置需要刷新一下
                    new Handler().post(new Runnable() {
                        @Override
                        public void run() {
                            mVideoFragment.openFxEditMode(EDIT_MODE_VIDEO_CLIP, mCurSelectVideoClip, true);
                        }
                    });
                }
                mPresenter.checkUpdateSmartKeyer(mCurSelectVideoClip);
                saveOperation();
            }

        }
    }

    private void initEditorTimeline() {
        mEditTimeline.setMainTrackList(mPresenter.getMainTrackThumbnailList());
        mPresenter.getCover();
        mEditTimeline.setOperationDuration(mTimeline.getDuration());
        mEditTimeline.toggleOriginalVoice(mPresenter.originalVoiceIsOpen());
        mPresenter.getMainTrackLineRegion();
        mEditorTrackView.initWidth(mTimeline.getDuration(), 0);
        // mMaterialTrack.setWidth(PixelPerMicrosecondUtil.durationToLength(mTimeline.getDuration()));
    }

    @Override
    public void onSaveOperation() {
        saveOperation();
    }

    /**
     * 新加字幕的时候设置选中
     * Select when adding subtitles
     */
    @Override
    public void onAddStickerCaptionPicFx(Object object, int type) {
        mPresenter.checkTrackDuration();
        if (object == null) {
            mCurrSelectedCaptionStickClip = null;
            mVideoFragment.resetFxEditMode();
        }
        if (object instanceof MeicamCaptionClip) {
            mCurrSelectedCaptionStickClip = (ClipInfo<?>) object;
            mEditTimeline.updateCaptionRegion(mPresenter.getLineRegionClip(mCurrSelectedCaptionStickClip,
                    mCurrSelectedCaptionStickClip.getTrackIndex(), false), false);
            mMultiHelper.updateCaptionView(mCurrSelectedCaptionStickClip);
            mVideoFragment.openFxEditMode(EDIT_MODE_CAPTION, mCurrSelectedCaptionStickClip, true);
        } else if (object instanceof MeicamCompoundCaptionClip) {
            mCurrSelectedCaptionStickClip = (ClipInfo<?>) object;
            mEditTimeline.updateCompoundCaptionRegion(mPresenter.getLineRegionClip(mCurrSelectedCaptionStickClip,
                    mCurrSelectedCaptionStickClip.getTrackIndex(), false), false);
            mVideoFragment.openFxEditMode(EDIT_MODE_COMPOUND_CAPTION, mCurrSelectedCaptionStickClip, true);
        } else if (object instanceof MeicamStickerClip) {
            mCurrSelectedCaptionStickClip = (ClipInfo<?>) object;
            mEditTimeline.updateStickerRegion(mPresenter.getLineRegionClip(mCurrSelectedCaptionStickClip,
                    mCurrSelectedCaptionStickClip.getTrackIndex(), false), false);
            mVideoFragment.openFxEditMode(EDIT_MODE_STICKER, mCurrSelectedCaptionStickClip, true);
        } else if (object instanceof MeicamTimelineVideoFxClip) {
            mCurrSelectedVideoFx = (MeicamTimelineVideoFxClip) object;
            mVideoFragment.playVideo(mCurrSelectedVideoFx.getInPoint(), mCurrSelectedVideoFx.getOutPoint());
            if (!mCurrSelectedVideoFx.isBuildFx()) {
                List<Fragment> fragments = mMultiBottomView.getFragmentList();
                if (fragments == null) {
                    return;
                }
                for (Fragment fragment : fragments) {
                    if (fragment instanceof EffectFragment) {
                        ((EffectFragment) fragment).updateSelected(mCurrSelectedVideoFx);
                    }
                }
            }
        }
    }

    @Override
    public void leftHandChange(int dx, long changeValue, BaseUIClip baseUIClip) {
        mEditorTrackView.refreshSelectView(baseUIClip, true);
    }

    @Override
    public void rightHandChange(int dx, long changeValue, BaseUIClip baseUIClip) {
        mEditorTrackView.refreshSelectView(baseUIClip, false);
    }

    @Override
    public void handUp(BaseUIClip baseUIClip, long changeDuration, boolean isLeft) {
        if (baseUIClip == null) {
            return;
        }
//        Log.e(TAG, "handUp: getInPoint: "+ baseUIClip.getInPoint()+"  getTrimIn: "+ baseUIClip.getTrimIn()
//                +"  getTrimOut: "+ baseUIClip.getTrimOut()+"  getTrackIndex: "+ baseUIClip.getTrackIndex() );
        mEditorEngine.refreshData(baseUIClip.getTrackIndex(), baseUIClip.getType());
        mPresenter.checkTrackDuration();
        mEditorEngine.seekTimeline(0);
        ClipInfo<?> clipInfo = null;
        boolean isVideo = false;
        if (CommonData.CLIP_CAPTION.equals(baseUIClip.getType())) {
            mEditTimeline.setCaptionRegion(mPresenter.getCaptionStickerLineRegion(CommonData.CLIP_CAPTION));
            clipInfo = mCurrSelectedCaptionStickClip;
        } else if (CommonData.CLIP_COMPOUND_CAPTION.equals(baseUIClip.getType())) {
            mEditTimeline.setCompoundCaptionRegion(mPresenter.getCaptionStickerLineRegion(CommonData.CLIP_COMPOUND_CAPTION));
        } else if (CommonData.CLIP_STICKER.equals(baseUIClip.getType())) {
            clipInfo = mCurrSelectedCaptionStickClip;
            mEditTimeline.setStickerRegion(mPresenter.getCaptionStickerLineRegion(CommonData.CLIP_STICKER));
        } else if (CommonData.CLIP_VIDEO.equals(baseUIClip.getType()) || CommonData.CLIP_IMAGE.equals(baseUIClip.getType())) {
            clipInfo = mCurSelectVideoClip;
            mPresenter.updateTimelineFxTargetVideoClipInPip(mCurSelectVideoClip);
            mPresenter.getPipLineRegionAsync(new DraftEditPresenter.LineRegionDataCallBack() {
                @Override
                public void onResultCallback(List<LineRegionClip> data) {
                    mEditTimeline.setPipRegion(data);
                }
            });
            isVideo = true;
        } else if (CommonData.CLIP_TIMELINE_FX.equals(baseUIClip.getType())) {
            if (mCurrSelectedVideoFx != null) {
                mPresenter.updateTimelineFxTargetInAndOutPoint(mCurrSelectedVideoFx);
            }
        }
        if (clipInfo != null) {
            mEditorEngine.checkKeyFrame(clipInfo, changeDuration, isLeft);
            long timestamp = mPresenter.getTimelineCurrentPosition();
            //clipInfo.testLog();
            BaseItemView dragView = mEditorTrackView.getDragView();
            if (dragView != null && baseUIClip.equals(dragView.getBaseUIClip())) {
                if (clipInfo instanceof MeicamVideoClip) {
                    if (showAtomicEditMenuViewKeyFrame()) {
                        AtomicEditMenuView view = (AtomicEditMenuView) mBottomViewHelper.getView().getShowView();
                        if (mCurSelectVideoClip != null) {
                            Plug plug = view.getSelectedPlug();
                            MeicamVideoFx meicamVideoFx = mCurSelectVideoClip.
                                    getVideoFx(MeicamVideoFx.SubType.SUB_TYPE_PLUG_FX, plug.plugName, plug.getClipIndex());
                            dragView.updateKeyFrame(mPresenter.getKeyFrameInfo(meicamVideoFx, null), timestamp);
                        }
                    } else {
                        dragView.updateKeyFrame(mPresenter.getKeyFrameInfo(((MeicamVideoClip) clipInfo).findPropertyVideoFx(), isVideo ? NvsConstants.KEY_CROPPER_TRANS_X : null), timestamp);
                    }
                } else {
                    dragView.updateKeyFrame(mPresenter.getKeyFrameInfo((IKeyFrameProcessor<?>) clipInfo, null), timestamp);
                }
                updateKeyFrameStatus();
            } else {
                notShowKeyFrameView();
            }
            mVideoFragment.updateOperationBoxWhenHadKeyFrame(timestamp);

        }
        long seekPosition = isLeft ? baseUIClip.getInPoint() : baseUIClip.getOutPoint();
        int scrollX = mEditTimeline.durationToLength(seekPosition);
        //滚动的时候计算距离时间转化会存在误差，所以做此操作
        //There is an error in calculating the distance to time conversion while scrolling, so do this
        mEditTimeline.scrollTo(isLeft ? scrollX + 2 : scrollX - 2);
        saveOperation();
    }

    @Override
    public void onWindowFocusChanged(boolean hasFocus) {
        super.onWindowFocusChanged(hasFocus);
    }

    @Override
    public void onPixelPerMicrosecondChange(double pixelPerMicrosecond, float scale) {
        mEditorEngine.seekTimeline(0);
        seekViewOnPlay(mEditorEngine.getCurrentTimelinePosition());
    }

    private void onLoginBack(boolean isLogin) {
        if (mLoginView != null) {
            mLoginView.setSelected(isLogin);
        }
        hideBottomView();
    }

    @Override
    public void onLoginSuccess(String token) {
        onLoginBack(true);
    }

    @Override
    public void onLoginFailed(int code) {
        if (code == UserConstant.ResultCode.LOGIN_IN_CONFIRM) {
            showLoginDialogEx();
        } else {
            onLoginBack(false);
        }
    }

    @Override
    public void showUnavailablePop(final AssetList data, final boolean isForTemplate) {
        IUserPlugin userPlugin = PluginManager.get().getUserPlugin();
        if (data.realAssetList == null || data.realAssetList.isEmpty() || userPlugin == null) {
            return;
        }
        List<UserAssetsInfo> assetsData = new ArrayList<>();
        for (AssetInfo assetInfo : data.realAssetList) {
            UserAssetsInfo userAssetsInfo = new UserAssetsInfo();
            userAssetsInfo.setPossessor(assetInfo.getPossessor());
            userAssetsInfo.setName(assetInfo.getName());
            userAssetsInfo.setTag(AssetsConstants.getAssetsTypeName(this, assetInfo.getType()));
            userAssetsInfo.setContent(assetInfo.getPackageId());
            assetsData.add(userAssetsInfo);
        }
        userPlugin.showUnAvailablePop(this, assetsData, new IUserPlugin.OnEventListener<List<String>>() {
            @Override
            public void onConfirm(List<String> strings) {
                AssetsManager.get().commitUnavailableAssets(userPlugin.getToken(), strings, new RequestCallback<Object>() {
                    @Override
                    public void onSuccess(BaseResponse<Object> response) {
                        if (response != null) {
                            LogUtils.d("commitAssets success :" + response.getCode());
                            ToastUtils.showShort(userPlugin.getMsg(DraftEditActivity.this, IUserPlugin.MSG_COMMIT_SUCCESS));
                        }
                    }

                    @Override
                    public void onError(BaseResponse<Object> response) {
                        if (response != null) {
                            LogUtils.e("commitAssets error :" + response.getCode());
                            ToastUtils.showShort(userPlugin.getMsg(DraftEditActivity.this, IUserPlugin.MSG_COMMIT_FAILED));
                        }
                    }
                });
            }

            @Override
            public void onCancel() {

            }
        });

    }

    @Override
    public void goToExportTemplateView() {
        if (Utils.isFastClick() || checkSmartKeying()) {
            return;
        }
        mBeforeJumpTimelinePosition = mTimeline.getCurrentPosition();
        saveDraft();
        /*
         * 导出模板需要删除智能抠像
         * Smart matting needs to be deleted when exporting templates
         */
        mPresenter.removeSomeThingFromTimeline();
        AppManager.getInstance().jumpActivity(DraftEditActivity.this, ExportTemplateSettingActivity.class);
    }

    @Override
    public void goToExportVideoView() {
        if (Utils.isFastClick() || checkSmartKeying()) {
            return;
        }
        AppManager.getInstance().jumpActivity(DraftEditActivity.this, CompileActivity.class, null);
    }

    @Override
    public void onAddVideoClip(List<MeicamVideoClip> videoClipList, int trackIndex, long seekTime) {
        if (trackIndex == MAIN_TRACK_INDEX && videoClipList != null && videoClipList.size() > 0) {
            List<ITrackClip> addTrackList = new ArrayList<>(videoClipList.size());
            for (MeicamVideoClip videoClip : videoClipList) {
                addTrackList.add(mPresenter.getThumbnailClip(videoClip));
            }
            mEditTimeline.addMainTrackClip(mInsertVideoClipIndex, addTrackList);
            ITrackClip iTrackClip = addTrackList.get(0);
            if (iTrackClip != null) {
                if (iTrackClip.getType().equals(CommonData.CLIP_IMAGE)) {
                    mNavigationBar.show(R.string.nb_picture_edit1);
                } else {
                    mNavigationBar.show(R.string.nb_video_edit1);
                }
                mEditTimeline.showSpanView(iTrackClip);
                mCurSelectVideoClip = videoClipList.get(0);
                mVideoFragment.updateTransformFx(mCurSelectVideoClip, true);
            }
            if (mNavigationBar.isShow(R.string.nb_video_edit1)
                    || mNavigationBar.isShow(R.string.nb_picture_edit1)) {
                mPresenter.getMainTrackLineRegion();
            }
            mEditorTrackView.initWidth(mTimeline.getDuration(), 0);
            if (mEditorTrackView.isShowTrackView()) {
                updateStickerTrack();
            }
            //插入影响插入的前面的转场
            //Insertion affects the previous transition of insertion
            mPresenter.updateTransitionByVideoClipDuration(mInsertVideoClipIndex - 1);
            seekViewOnPlay(seekTime);
            saveOperation();
            showEditNavigation();
        }
    }

    @Override
    public void onAddVideoClip(MeicamVideoClip videoClip) {
        if (videoClip != null) {
            int trackIndex = videoClip.getTrackIndex();
            if (trackIndex > 0) {
                mEditorTrackView.showPipTrackView(mTimeline);
                mEditorTrackView.setSelect(trackIndex - 1, videoClip.getInPoint());
                if (videoClip.getVideoType().equals(CommonData.CLIP_IMAGE)) {
                    mNavigationBar.show(R.string.nb_picture_edit1);
                } else {
                    mNavigationBar.show(R.string.nb_video_edit1);
                }
                mEditTimeline.updatePipRegion(mPresenter.getLineRegionClip(videoClip, trackIndex, true), false);
                mEditorTrackView.initWidth(mTimeline.getDuration(), 0);
                mCurSelectVideoClip = videoClip;
                mCurrentTrackIndex = trackIndex;
                mVideoFragment.updateTransformFx(mCurSelectVideoClip, true);
                mPresenter.checkTrackDuration();
                saveOperation();
            } else {
                toOtherMenu();
                ITrackClip mainSelectedClip = mEditTimeline.getMainSelectedClip();
                if (mainSelectedClip != null) {
                    ITrackClip trackClip = mPresenter.getThumbnailClip(videoClip);
                    mEditTimeline.addMainTrackClip(mainSelectedClip.getIndexInTrack() + 1, trackClip);
                    mEditTimeline.showSpanView(trackClip);
                }
                mCurSelectVideoClip = videoClip;
                mVideoFragment.updateTransformFx(mCurSelectVideoClip, true);
                mEditorTrackView.initWidth(mTimeline.getDuration(), 0);
                mPresenter.checkTrackDuration();
                //当前为复制的videoClip,影响复制的前面的那个转场
                //Currently, it is a replicated videoClip, which affects the previous transition of the replication.
                mPresenter.updateTransitionByVideoClipDuration(videoClip.getIndex() - 1);
                seekViewOnPlay(videoClip.getInPoint());
                if (mEditorTrackView.isShowTrackView()) {
                    updateStickerTrack();
                }
                mPresenter.getMainTrackLineRegion();
                mEditorEngine.seekTimeline(videoClip.getInPoint() + 1, 0);
                saveOperation();
            }
        }
    }

    @Override
    public void onClipChangedToSubTrack(MeicamVideoClip removedClip, MeicamVideoClip addedClip) {
        if (removedClip == null) {
            ToastUtils.showShort(R.string.toast_least_one_material);
            return;
        }
        if (addedClip == null) {
            return;
        }
        mEditTimeline.deleteMainTrackClip(mEditTimeline.getMainSelectedClip());
        //删除影响前一个转场,也就是当前clip的转场
        //Deletion affects the previous transition, that is, the transition of the current clip
        mPresenter.updateTransitionByVideoClipDuration(removedClip.getIndex());
        Navigation.Item item = new Navigation.Item();
        clickNavigationMain(item.setTitleId(R.string.main_menu_name_picture_in_picture));
        onAddVideoClip(addedClip);
        mEditorEngine.seekTimeline(0);
    }

    @Override
    public void onClipChangedToMainTrack(MeicamVideoClip removedClip, MeicamVideoClip addedClip) {
        mEditTimeline.addMainTrackClip(addedClip.getIndex(), mPresenter.getThumbnailClip(addedClip));
        mPresenter.checkTrackDuration();
        mEditTimeline.showSpanView(addedClip.getInPoint());
        updatePipTrack();
        mCurSelectVideoClip = addedClip;
        mCurrentTrackIndex = addedClip.getTrackIndex();
        mPresenter.getPipLineRegionAsync(new DraftEditPresenter.LineRegionDataCallBack() {
            @Override
            public void onResultCallback(List<LineRegionClip> data) {
                mEditTimeline.setPipRegion(data);
            }
        });
        showEditNavigation();
        mEditorEngine.seekTimeline(0);
        saveOperation();
    }

    @Override
    public void onCheckTrackResult(MeicamVideoClip videoClip, int state) {
        ITrackClip lastMainTrackClip = mEditTimeline.findLastMainTrackClip();
        if (lastMainTrackClip != null) {
            if (state == DELETE_HOLDER) {
                if (BaseTrackClip.CLIP_HOLDER.equals(lastMainTrackClip.getType())) {
                    mEditTimeline.deleteMainTrackClip(lastMainTrackClip);
                }
            } else if (state == ADD_HOLDER) {
                if (!BaseTrackClip.CLIP_HOLDER.equals(lastMainTrackClip.getType())) {
                    mEditTimeline.addMainTrackClip(mPresenter.getThumbnailClip(videoClip));
                }
            } else if (state == CHANGE_HOLDER) {
                if (BaseTrackClip.CLIP_HOLDER.equals(lastMainTrackClip.getType())) {
                    lastMainTrackClip.setInPoint(videoClip.getInPoint());
                    lastMainTrackClip.setOutPoint(videoClip.getOutPoint());
                    lastMainTrackClip.setTrimOut(videoClip.getTrimOut());
                    mEditTimeline.changeMainTrackClipInAndOut(lastMainTrackClip, mPresenter.getTimeline().getDuration());
                }
            }
        }
        mEditorEngine.alignmentTimelineFxDuration();
        mEditorTrackView.initWidth(mTimeline.getDuration(), 0);
    }

    @Override
    public void onMainTrackLineRegionChange(List<LineRegionClip> captionRegion, List<LineRegionClip> compoundCaptionRegion,
                                            List<LineRegionClip> stickerRegion, List<LineRegionClip> pipRegion) {
        mEditTimeline.setCaptionRegion(captionRegion);
        mEditTimeline.setCompoundCaptionRegion(compoundCaptionRegion);
        mEditTimeline.setStickerRegion(stickerRegion);
        mEditTimeline.setPipRegion(pipRegion);
        mEditTimeline.updateEffectRegion();
    }

    @Override
    public void onFreezeFrameComplete(boolean isPip, int trackIndex, MeicamVideoClip preVideoClip, MeicamVideoClip postVideoClip, final MeicamVideoClip insertVideoClip) {
        if (insertVideoClip != null) {
            mCurSelectVideoClip = mEditorEngine.getVideoClip(trackIndex, insertVideoClip.getInPoint());
            if (isPip) {
                mPresenter.checkTrackDuration();
                mEditorTrackView.showPipTrackView(mTimeline);
                mCurrentTrackIndex = trackIndex;
                onNeedTrackSelectChanged(trackIndex - 1, insertVideoClip.getInPoint());
                mVideoFragment.updateTransformFx(mCurSelectVideoClip, true);
                saveOperation();
            } else {
                /*定格的时候postVideoClip已经调整完毕了，但是view并没有，所以这里需要还原一下分割后片段的入点
                 * PostVideoClip has been adjusted at the time of freeze frame, but the view does not,
                 * so here we need to restore the entry point of the split segment.
                 * */
                long duration = insertVideoClip.getOutPoint() - insertVideoClip.getInPoint();
                if (postVideoClip != null) {
                    mEditTimeline.splitMainTrackClip(preVideoClip.getIndex(), preVideoClip.getOutPoint(), preVideoClip.getTrimOut(),
                            postVideoClip.getInPoint() - duration, postVideoClip.getTrimIn());

                    //定格以后会影响被分割开的片段
                    //Freeze frame will affect the split segment
                    if (mCurSelectVideoClip != null) {
                        mPresenter.updateTransitionByVideoClipDuration(preVideoClip.getIndex());
                        mPresenter.updateTransitionByVideoClipDuration(postVideoClip.getIndex());
                    }
                }
                mEditTimeline.addMainTrackClip(insertVideoClip.getIndex(), mPresenter.getThumbnailClip(insertVideoClip));
                mEditTimeline.showSpanView(insertVideoClip.getInPoint());
                refreshEditorTimelineView(TYPE_FREEZE_CLIP);
                if (mEditorTrackView.isShowTrackView()) {
                    updateStickerTrack();
                }
                saveOperation();
            }

            mVideoFragment.updateTransformFx(mCurSelectVideoClip, true);
            /*
             * 跳转到图片编辑菜单
             * Jump to picture edit menu
             */
            mNavigationBar.show(R.string.nb_picture_edit1);
        }
    }

    @Override
    public void onAddKeyFrame(MeicamKeyFrame keyFrame, boolean isMainTrack) {
        boolean show;
        if (isMainTrack) {
            mEditTimeline.addKeyFrameTag(keyFrame.getAtTime());
            showKeyFrameView(false, getKeyFrameProcessor(mCurSelectVideoClip), mEditorEngine.getCurrentTimelinePosition() - mCurSelectVideoClip.getInPoint());
        } else {
            BaseItemView dragView = mEditorTrackView.getDragView();
            if (dragView != null) {
                show = dragView.addKeyFrame(keyFrame.getAtTime(), true);
                BaseUIClip baseUIClip = dragView.getBaseUIClip();
                showKeyFrameView(show, getKeyFrameProcessor(baseUIClip), mEditorEngine.getCurrentTimelinePosition() - baseUIClip.getInPoint());
            }
        }

    }

    @Override
    public void onSmartKeyerProgress(int progress) {
        mTvSmartKeyer.setText(String.format(getString(R.string.smart_keyer_progress), progress));
        if (mFlSmartKeyer.getVisibility() != VISIBLE) {
            mFlSmartKeyer.setVisibility(VISIBLE);
        }
    }

    @Override
    public void onSmartKeyerComplete(boolean success) {
        mFlSmartKeyer.setVisibility(GONE);
        if (success) {
            saveOperation();
        }
    }

    @Override
    public void updateTransitionByVideoClipDuration(int clipIndex) {
        ThumbnailClip.TailInfo tailInfo = mEditTimeline.findThumbnailTailInfo(clipIndex);
        if (tailInfo != null) {
            tailInfo.setId("");
            mEditTimeline.updateThumbnailTailInfo(clipIndex);
        }
        ThumbnailClip.TailInfo tailInfoBefore = mEditTimeline.findThumbnailTailInfo(clipIndex - 1);
        if (tailInfoBefore != null) {
            tailInfoBefore.setId("");
            mEditTimeline.updateThumbnailTailInfo(clipIndex - 1);
        }
    }

    @Override
    public void onDeleteTransition(int index) {
        ThumbnailClip.TailInfo tailInfo = mEditTimeline.findThumbnailTailInfo(index);
        if (tailInfo != null) {
            tailInfo.setId("");
            mEditTimeline.updateThumbnailTailInfo(index);
        }
    }

    @Override
    public void onDeleteVideoClip(MeicamVideoClip videoClip) {
        mEditorTrackView.initWidth(mTimeline.getDuration(), 0);
        if (videoClip.getTrackIndex() == MAIN_TRACK_INDEX) {
            mEditTimeline.deleteMainTrackClip(mEditTimeline.getMainSelectedClip());
            switchToMainMenu();
        } else {
            if (mNavigationBar.isShow(R.string.nb_video_edit1) || mNavigationBar.isShow(R.string.nb_picture_edit1)) {
                if (videoClip.getTrackIndex() > MAIN_TRACK_INDEX) {
                    mVideoFragment.setTransformViewVisible(GONE);
                    mEditorTrackView.showPipTrackView(mTimeline);
                    // updatePipTrack();
                }
            }
            mNavigationBar.show(R.string.nb_pip1);
        }
        mPresenter.checkTrackDuration();
        saveOperation();
    }

    @Override
    public void onReplaceVideoClipFinish(MeicamVideoClip replacedVideoClip) {
        mCurSelectVideoClip = replacedVideoClip;
        if (replacedVideoClip == null) {
            ToastUtils.make()
                    .setGravity(Gravity.CENTER, 0, 0)
                    .setDurationIsLong(false)
                    .show(R.string.video_clip_replace_failed);
            return;
        }
        int trackIndex = replacedVideoClip.getTrackIndex();
        mEditorEngine.seekTimeline(STREAMING_ENGINE_SEEK_FLAG_BUDDY_HOST_VIDEO_FRAME);
        ITrackClip trackClip = mEditTimeline.getMainSelectedClip();
        if (trackClip == null && trackIndex == MAIN_TRACK_INDEX) {
            // 页面切换的情况，mEditTimeline.getMainSelectedClip()可能为空
            trackClip = mEditTimeline.findMainTrackClip(replacedVideoClip.getIndex());
        }
      //  LogUtils.d("isShow=" + mEditTimeline.mainSpanIsShow() + ",trackClip=" + trackClip + ",\n replacedVideoClip=" + replacedVideoClip);
        if (trackClip == null) {
            BaseItemView dragView = mEditorTrackView.getDragView();
            if (dragView != null) {
                BaseUIClip baseUIClip = dragView.getBaseUIClip();
                if (baseUIClip != null) {
                    String type = baseUIClip.getType();
                    if (CommonData.CLIP_IMAGE.equals(type) || CommonData.CLIP_VIDEO.equals(type)) {
                        updatePipTrack();
                        mEditorTrackView.setSelect(baseUIClip, false);
                    }
                }
            }
        } else {
//                trackClip = mPresenter.getThumbnailClip(mCurSelectVideoClip);
            ThumbnailClip thumbnailClip = (ThumbnailClip) trackClip;
            if (CommonData.CLIP_VIDEO.equals(mCurSelectVideoClip.getVideoType())) {
                thumbnailClip.setType(BaseTrackClip.CLIP_VIDEO);
            } else if (CommonData.CLIP_IMAGE.equals(mCurSelectVideoClip.getVideoType())) {
                thumbnailClip.setType(BaseTrackClip.CLIP_IMAGE);
            } else {
                thumbnailClip.setType(BaseTrackClip.CLIP_HOLDER);
            }
            float volume = CLIP_VIDEO.equals(mCurSelectVideoClip.getVideoType()) ? mCurSelectVideoClip.getVolume() : 1;
            thumbnailClip.setVolume(volume);
            thumbnailClip.setAssetPath(mCurSelectVideoClip.getFilePath());
            thumbnailClip.setTrimIn(mCurSelectVideoClip.getTrimIn());
            thumbnailClip.setTrimOut(mCurSelectVideoClip.getTrimOut());
            mEditTimeline.updateThumbnail(thumbnailClip, true);
        }
        mVideoFragment.updateTransformFx(mCurSelectVideoClip, true);
        showEditNavigation();
        seekViewOnPlay(mCurSelectVideoClip.getInPoint() + ONE_FRAME);
        saveOperation();
    }

    @Override
    public void setCover(String imagePath) {
        mEditTimeline.setCover(imagePath);
    }

    private final EditOperateObserver mEditOperateObserver = new EditOperateObserver() {
        @Override
        public void onUndo(boolean isEmpty) {
            mEditOperationView.updateCancelState(!isEmpty);
        }

        @Override
        public void onRecover(boolean isEmpty) {
            mEditOperationView.updateRecoverState(!isEmpty);
        }
    };

    private final EngineCallbackObserver mEngineCallbackObserver = new EngineCallbackObserver() {
        @Override
        public boolean isActive() {
            /*可根据实际情况可自行设置本方法返回值，返回false则不会收到Engine回调。
            其他页面不建议这么写，因为编辑页面的启动模式是SingleTask,且是重要activity。
            当然也可以可以通过EngineCallbackManager.setCurrentCallbackTag方法和本类的getCallbackFromTag配合判断，
            注意反注销的时候要调用unregisterCallbackObserverRemoveTag

            You can set the return value of this method according to the actual situation. If you return false,
            you will not receive the Engine callback.This is not recommended for other pages because the starting
            mode of the editing page is SingleTask and it is an important activity.Of course, it can also be judged
            by the EngineCallbackManager.setCurrentCallbackTag method and the getCallbackFromTag of this class,
            Note that unregisterCallbackObserverRemoveTag should be called when canceling logout*/
           // return DraftEditActivity.this.isActive();
            return !isActivityStopped();
        }


        @Override
        public void onPlaybackEOF(NvsTimeline timeline) {
            if (mBottomViewContainer.getShowView() instanceof EditChangeSpeedCurveView) {
                mEditorEngine.seekTimeline(mCurSelectVideoClip.getInPoint(), 0);
                seekViewOnPlay(mCurSelectVideoClip.getInPoint());
                ((EditChangeSpeedCurveView) mBottomViewContainer.getShowView()).updateBaseLine(
                        mCurSelectVideoClip.getClipPosByTimelinePosCurvesVariableSpeed(mCurSelectVideoClip.getInPoint())
                                - mCurSelectVideoClip.getTrimIn());
            }

            Object attachment = timeline.getAttachment(NvsConstants.KEY_ATTACHMENT_RESET_PLAY_POINT);
            if (attachment instanceof Long) {
                long playPoint = (long) attachment;
                mEditorEngine.seekTimeline(playPoint, 0);
                seekViewOnPlay(playPoint);
            }

            attachment = timeline.getAttachment(NvsConstants.KEY_ATTACHMENT_LOOP_PLAY_POINT_START);
            Object attachmentEnd = timeline.getAttachment(NvsConstants.KEY_ATTACHMENT_LOOP_PLAY_POINT_END);
            if (attachment instanceof Long && attachmentEnd instanceof Long) {
                long playPoint = (long) attachment;
                long playEndPoint = (long) attachmentEnd;
                mEditorEngine.playVideo(playPoint, playEndPoint);
            }
        }

        @Override
        public void onPlaybackStopped(NvsTimeline timeline) {
            if (mNavigationBar.isShow(R.string.nb_background1)) {
                setCurrentMainTrackClip();
                mVideoFragment.updateTransformFx(mCurSelectVideoClip);
            }
        }

        @Override
        public void onPlaybackTimelinePosition(NvsTimeline timeline, long stamp) {
            mEditOperationView.setDurationText(FormatUtils.microsecond2Time(stamp) + "/" + FormatUtils.microsecond2Time(mTimeline.getDuration()));
            if (mEditTimeline == null) {
                return;
            }
            if (mTimeline == null) {
                return;
            }
            seekViewOnPlay(stamp);
            // mVideoFragment.checkOperationBoxVisible();
            if (mBottomViewContainer.getShowView() instanceof EditChangeSpeedCurveView) {
                ((EditChangeSpeedCurveView) mBottomViewContainer.getShowView()).updateBaseLine(
                        mCurSelectVideoClip.getClipPosByTimelinePosCurvesVariableSpeed(stamp)
                                - mCurSelectVideoClip.getTrimIn());
            }
        }

        @Override
        public void onStreamingEngineStateChanged(int state) {
            boolean isPlaying = state == NvsStreamingContext.STREAMING_ENGINE_STATE_PLAYBACK;
            if (isPlaying) {
                //水印页面弹出时点击播放不消失操作框
                //When the watermark page pops up, click the play and do not disappear operation box
               /* if (!(mMultiBottomView.getSelectedFragment() instanceof WaterFragment)) {
                    mVideoFragment.hideOperationBox();
                }*/
                mSeekFlag = FLAG_PLAY;
            } else {
                if (mEditorTrackView.hasDragView()) {
                    mVideoFragment.checkOperationBoxVisible();
                    mVideoFragment.checkVideoClipOperationBoxVisible();
                }
            }
            mEditOperationView.updateViewState(isPlaying);
        }
    };
}
