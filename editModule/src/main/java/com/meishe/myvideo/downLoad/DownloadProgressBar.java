package com.meishe.myvideo.downLoad;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Rect;
import android.util.AttributeSet;
import android.widget.ProgressBar;

import com.meishe.base.utils.SizeUtils;


/**
 * Created by czl on 2018/6/25.
 * 素材下载进度条
 * Material download progress bar
 */
public class DownloadProgressBar extends ProgressBar {
    private Paint mPaint;
    private String mProgress;
    private Rect mRect;

    /**
     * Instantiates a new Download progress bar.
     * 实例化一个新的下载进度条
     * @param context the context
     */
    public DownloadProgressBar(Context context) {
        super(context);
        initText();
    }

    /**
     * Instantiates a new Download progress bar.
     * 实例化一个新的下载进度条
     * @param context  the context
     * @param attrs    the attrs
     * @param defStyle the def style
     */
    public DownloadProgressBar(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        initText();
    }


    /**
     * Instantiates a new Download progress bar.
     * 实例化一个新的下载进度条
     * @param context the context
     * @param attrs   the attrs
     */
    public DownloadProgressBar(Context context, AttributeSet attrs) {
        super(context, attrs);
        initText();
    }

    @Override
    public synchronized void setProgress(int progress) {
        super.setProgress(progress);
        setText(progress);
    }

    @Override
    protected synchronized void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        if (mRect == null) {
            mRect = new Rect();
        }
        this.mPaint.getTextBounds(mProgress, 0, mProgress.length(), mRect);
        float textSize = SizeUtils.sp2px(12);
        mPaint.setTextSize(textSize);
        int x = getWidth() / 2 - mRect.centerX();
        int y = getHeight() / 2 - mRect.centerY();
        canvas.drawText(mProgress, x, y, mPaint);
    }

    private void initText(){
        this.mPaint = new Paint();
        this.mPaint.setColor(Color.WHITE);
        mProgress = "";
    }

    /**
    * 设置文字进度值
    * Set text progress value
    * */
    private void setText(int progress){
        int i = (progress * 100)/this.getMax();
        mProgress = i + "%";
    }
}
