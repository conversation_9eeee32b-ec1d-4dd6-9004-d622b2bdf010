package com.meishe.myvideo.fragment;

import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.facebook.drawee.backends.pipeline.Fresco;
import com.facebook.drawee.backends.pipeline.PipelineDraweeControllerBuilder;
import com.facebook.drawee.controller.AbstractDraweeController;
import com.facebook.drawee.view.SimpleDraweeView;
import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.ImageLoader;
import com.meishe.base.utils.SizeUtils;
import com.meishe.business.assets.AssetUtils;
import com.meishe.business.assets.fragment.FlowFragment;
import com.meishe.business.assets.fragment.adapter.CommonAdapter;
import com.meishe.business.assets.iview.AssetsView;
import com.meishe.business.assets.presenter.FlowPresenter;
import com.meishe.engine.asset.bean.AssetInfo;
import com.meishe.engine.asset.bean.RequestParam;
import com.meishe.engine.bean.MeicamVideoClip;
import com.meishe.logic.constant.PagerConstants;
import com.meishe.myvideo.R;
import com.meishe.myvideo.interfaces.OnAssetsClickedListener;
import com.meishe.third.adpater.BaseViewHolder;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2021/11/29 10:26
 * @Description :道具fragment the prop fragment
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class PropFragment extends FlowFragment<FlowPresenter> implements AssetsView {
    private OnAssetsClickedListener mListener;
    private MeicamVideoClip mVideoClip;

    public static PropFragment create(MeicamVideoClip videoClip, RequestParam param, OnAssetsClickedListener listener) {
        PropFragment fragment = new PropFragment();
        fragment.setOnAssetsClickedListener(listener);
        fragment.mVideoClip = videoClip;
        Bundle bundle = new Bundle();
        if (param != null) {
            bundle.putInt(PagerConstants.BUNDLE_KEY_ASSET_TYPE, param.type);
            bundle.putInt(PagerConstants.BUNDLE_KEY_ASSET_TYPE_NEW, param.type);
            bundle.putInt(PagerConstants.BUNDLE_KEY_ASSET_TYPE_CATEGORY, param.categoryId);
            bundle.putInt(PagerConstants.BUNDLE_KEY_ASSET_TYPE_KIND, param.kind);
        }
        fragment.setArguments(bundle);
        return fragment;
    }

    public PropFragment() {
        leftItemDecoration = SizeUtils.dp2px(4.5F);
        topItemDecoration = SizeUtils.dp2px(15);
    }

    private void setOnAssetsClickedListener(OnAssetsClickedListener listener) {
        this.mListener = listener;
    }

    @Override
    protected int getItemLayoutResId() {
        return 0;
    }

    @Override
    protected void onAdapterItemClick(int position) {
        if (mListener != null) {
            mListener.onItemClicked(mAdapter.getItem(position));
        }
    }

    @Override
    protected CommonAdapter getAdapter() {
        return new PropAdapter(getContext());
    }

    @Override
    protected String getAssetId() {
        if(mVideoClip != null){
           return mVideoClip.getPropId();
        }
        return "";
    }

    private static class PropAdapter extends CommonAdapter {
        private final ImageLoader.Options mOptions;

        private final PipelineDraweeControllerBuilder mPipelineBuilder;

        private PropAdapter(Context context) {
            super(R.layout.prop_item);
            mContext = context;
            mPipelineBuilder = Fresco.newDraweeControllerBuilder();
            mOptions = new ImageLoader.Options()
                    .centerCrop()
                    .roundedCorners(4)
                    .dontAnimate();
        }

        @Override
        protected void convert(@NonNull BaseViewHolder helper, AssetInfo item) {
            SimpleDraweeView ivCover = helper.getView(R.id.iv_cover);
            TextView tvAssetPurchased = helper.getView(R.id.tv_asset_purchased);
            if (AssetUtils.assetPurchasedVisible(tvAssetPurchased, mAssetSubType, item.isAuthorized())) {
                tvAssetPurchased.setVisibility(View.VISIBLE);
            } else {
                if (tvAssetPurchased != null) {
                    tvAssetPurchased.setVisibility(View.GONE);
                }
            }
            String coverPath = item.getCoverPath();
            if (!TextUtils.isEmpty(coverPath) && coverPath.startsWith("http") && coverPath.endsWith("webp")) {
                AbstractDraweeController controller = mPipelineBuilder
                        .setUri(coverPath)
                        .setOldController(ivCover.getController())
                        .setAutoPlayAnimations(true)
                        .build();
                ivCover.setController(controller);
            } else {
                ImageLoader.loadUrl(mContext, item.getCoverPath(), ivCover, mOptions);
            }
            if (helper.getAdapterPosition() == mSelectedPosition) {
                ivCover.setBackground(CommonUtils.getRadiusDrawable(3, mContext.getResources().getColor(R.color.color_fffc2b55), 6, -1));
            } else {
                ivCover.setBackgroundResource(0);
            }
            helper.setText(R.id.tv_name, item.getName());
            ImageView ivDownload = helper.getView(R.id.iv_downloading);
            if (!item.isHadDownloaded() || item.needUpdate()) {
                ImageLoader.loadUrl(mContext, R.mipmap.downloading, ivDownload);
                int progress = item.getDownloadProgress();
                if (progress >= 0 && progress < 100) {
                    ivDownload.setVisibility(View.VISIBLE);
                    ivCover.setVisibility(View.GONE);
                } else {
                    ivDownload.setVisibility(View.GONE);
                    ivCover.setVisibility(View.VISIBLE);
                }
            } else {
                ivDownload.setVisibility(View.GONE);
                ivCover.setVisibility(View.VISIBLE);
            }
        }
    }

    @Override
    public void onDetach() {
        super.onDetach();
        mListener = null;
    }
}
