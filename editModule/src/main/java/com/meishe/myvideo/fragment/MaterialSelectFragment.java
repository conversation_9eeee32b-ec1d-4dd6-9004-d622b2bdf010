package com.meishe.myvideo.fragment;

import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.deep.foresight.libspi.BusinessDataProviderMgr;
import com.deep.foresight.libspi.IDataChangeListener;
import com.deep.foresight.libspi.ILocalMediaProvider;
import com.meishe.base.bean.MediaData;
import com.meishe.base.bean.MediaSection;
import com.meishe.base.bean.MediaTag;
import com.meishe.base.model.BaseMvpFragment;
import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.ScreenUtils;
import com.meishe.base.utils.SizeUtils;
import com.meishe.base.utils.ToastUtils;
import com.meishe.engine.util.WhiteList;
import com.meishe.myvideo.R;
import com.meishe.myvideo.fragment.adapter.BaseSelectAdapter;
import com.meishe.myvideo.fragment.adapter.MaterialSelectAdapter;
import com.meishe.myvideo.fragment.adapter.MaterialSelectPreviewAdapter;
import com.meishe.myvideo.fragment.iview.MediaView;
import com.meishe.myvideo.fragment.presenter.MediaPresenter;
import com.meishe.third.adpater.BaseQuickAdapter;
import com.meishe.third.adpater.decoration.GridSectionAverageGapItemDecoration;

import java.util.ArrayList;
import java.util.List;

import static com.meishe.logic.constant.PagerConstants.MEDIA_FILTER;
import static com.meishe.logic.constant.PagerConstants.MEDIA_TAG;
import static com.meishe.logic.constant.PagerConstants.MEDIA_TYPE;
import static com.meishe.logic.constant.PagerConstants.SELECTED_NEED_PREVIEW;
import static com.meishe.logic.constant.PagerConstants.SELECTED_TYPE;
import static com.meishe.logic.constant.PagerConstants.TYPE_ONE_FINISH;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> LiHangZhou
 * @CreateDate :2020/12/10 19:40
 * @Description :素材选择页面 Material selection page * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class MaterialSelectFragment extends BaseMvpFragment<MediaPresenter> implements MediaView {
    private RecyclerView mRvMediaList;
    private int mSelectedType;
    private BaseSelectAdapter mAdapter;
    private MaterialSelectFragment.MediaChangeListener mListener;
    private int mMediaTagType;
    private boolean mNeedPreview;
    private ArrayList<String> mMediaFilter;
    private String[] mMediaFilterTemp;
    private static final int DATA_SOURCE_LOCAL = 0;  // 本地数据源
    private static final int DATA_SOURCE_EXTERNAL = 1;  // 外部数据源
    private int mDataSourceType = DATA_SOURCE_LOCAL;  // 默认使用本地数据源
    private List<MediaSection> mExternalDataList = new ArrayList<>();  // 外部数据集

    /**
     * Instantiates a new Material select fragment.
     */
    public MaterialSelectFragment() {
    }

    /**
     * Create material select fragment.
     *
     * @param mediaType    the media type
     * @param selectedType the selected type
     * @param needPreview  the need preview
     * @param listener     the listener
     * @return the material select fragment
     */
    public static MaterialSelectFragment create(int mediaType,int selectedType, boolean needPreview,
                                                MaterialSelectFragment.MediaChangeListener listener) {
        return create(mediaType, null, selectedType, needPreview, listener);
    }


    /**
     * Create material select fragment.
     *
     * @param mediaType    the media type
     * @param mediaFilter  the media filter
     * @param selectedType the selected type
     * @param needPreview  the need preview
     * @param listener     the listener
     * @return the material select fragment
     */
    public static MaterialSelectFragment create(int mediaType,ArrayList<String> mediaFilter, int selectedType, boolean needPreview,
                                                MaterialSelectFragment.MediaChangeListener listener) {
        MaterialSelectFragment fragment = new MaterialSelectFragment();
        Bundle bundle = new Bundle();
        bundle.putInt(MEDIA_TYPE, mediaType);
        bundle.putStringArrayList(MEDIA_FILTER, mediaFilter);
        bundle.putInt(SELECTED_TYPE, selectedType);
        bundle.putBoolean(SELECTED_NEED_PREVIEW, needPreview);
        bundle.putBoolean(SELECTED_NEED_PREVIEW, needPreview);
        fragment.setArguments(bundle);
        fragment.setOnMediaChangeListener(listener);
        return fragment;
    }

    /**
     * 创建使用外部数据集的MaterialSelectFragment
     *
     * @param mediaType    媒体类型
     * @param externalData 外部数据集
     * @param selectedType 选择类型
     * @param needPreview  是否需要预览
     * @param listener     媒体变化监听器
     * @return MaterialSelectFragment实例
     */
    public static MaterialSelectFragment createWithExternalData(int mediaType, List<MediaSection> externalData,
                                                int selectedType, boolean needPreview,
                                                MaterialSelectFragment.MediaChangeListener listener) {
        MaterialSelectFragment fragment = new MaterialSelectFragment();
        Bundle bundle = new Bundle();
        bundle.putInt(MEDIA_TYPE, mediaType);
        bundle.putInt(SELECTED_TYPE, selectedType);
        bundle.putBoolean(SELECTED_NEED_PREVIEW, needPreview);
        bundle.putInt("data_source_type", DATA_SOURCE_EXTERNAL);
        fragment.setArguments(bundle);
        fragment.setExternalDataList(externalData);
        fragment.setOnMediaChangeListener(listener);
        return fragment;
    }

    /**
     * 设置外部数据集
     *
     * @param externalDataList 外部数据集
     */
    public void setExternalDataList(List<MediaSection> externalDataList) {
        this.mExternalDataList = externalDataList;
    }

    private void setOnMediaChangeListener(MaterialSelectFragment.MediaChangeListener listener) {
        mListener = listener;
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (data != null) {
            MediaTag mediaTag = data.getParcelableExtra(MEDIA_TAG);
            if (mediaTag.getType() == mMediaTagType) {
                dealSelectedState(mediaTag.getIndex(), true);
            }
        }
    }

    @Override
    protected int bindLayout() {
        return R.layout.fragment_media;
    }

    @Override
    protected void onLazyLoad() {
    }

    @Override
    protected void initView(View view) {
        mRvMediaList = view.findViewById(R.id.rv_media_list);
        //每个item的间距
        //Spacing of each item
        int w = SizeUtils.dp2px(6);
        FrameLayout.LayoutParams layoutParams = null;
        if (mRvMediaList.getLayoutParams() != null) {
            layoutParams = (FrameLayout.LayoutParams) mRvMediaList.getLayoutParams();
        }
        if (layoutParams == null) {
            layoutParams = new FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
        }
        //两边是两倍的item间距
        //Double item spacing on both sides
        layoutParams.width = ScreenUtils.getScreenWidth() - 4 * w;
        layoutParams.leftMargin = 2 * w;
        layoutParams.rightMargin = 2 * w;
        int SPAN_COUNT = 4;
        //item大小（宽高）
        //Item size (width and height)
        int itemSize = (int) ((layoutParams.width - (SPAN_COUNT - 1) * w) / SPAN_COUNT * 1f);
        mRvMediaList.setLayoutParams(layoutParams);
        mRvMediaList.addItemDecoration(new GridSectionAverageGapItemDecoration(6, 6, 6));
        mRvMediaList.setLayoutManager(new GridLayoutManager(getContext(), SPAN_COUNT));
        if (getArguments() != null) {
            mNeedPreview = getArguments().getBoolean(SELECTED_NEED_PREVIEW);
        }
        if (mNeedPreview) {
            mAdapter = new MaterialSelectPreviewAdapter(itemSize);
        } else {
            mAdapter = new MaterialSelectAdapter(itemSize);
        }
        mRvMediaList.setAdapter(mAdapter);
        initListener();
    }

    @Override
    protected void initData() {
        mMediaTagType = MediaData.TYPE_VIDEO;
        if (getArguments() != null) {
            mMediaTagType = getArguments().getInt(MEDIA_TYPE);
            mMediaFilter = getArguments().getStringArrayList(MEDIA_FILTER);
            mSelectedType = getArguments().getInt(SELECTED_TYPE);
            mDataSourceType = getArguments().getInt("data_source_type", DATA_SOURCE_LOCAL);
        }

        if (mDataSourceType == DATA_SOURCE_EXTERNAL) {
            ILocalMediaProvider localmedia = (ILocalMediaProvider) BusinessDataProviderMgr.getInstance().getProvider("localmedia");

            BusinessDataProviderMgr.getInstance().registerDataListener("localmedia", new IDataChangeListener() {
                @Override
                public void onDataChanged(Object o) {
                    if(o instanceof List){
                        if (localmedia != null) {
                            List<MediaData> localMedias = (List<MediaData>) o;
                            for (int i = 0; i < localMedias.size(); i++) {
                                mExternalDataList.add(new MediaSection(localMedias.get(i)));
                            }
                        }
                    }
                }
            });
            // 使用外部数据集
            if (mExternalDataList != null && !mExternalDataList.isEmpty()) {
                onMediaBack(mExternalDataList);
            }
        } else {
            // 使用本地数据源
            String[] filter = null;
            if (!CommonUtils.isEmpty(mMediaFilter)) {
                filter = new String[mMediaFilter.size()];
                for (int index = 0; index < mMediaFilter.size(); index++) {
                    filter[index] = mMediaFilter.get(index);
                }
            }
            mPresenter.getMediaList(mMediaTagType, filter);
        }
    }

    private void initListener() {
        mAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                MediaSection item = mAdapter.getItem(position);
                if (item != null) {
                    if (mListener != null && !item.isHeader) {
                        mListener.onMediaPreView(item.t);
                    }
                }
            }
        });
        mAdapter.setOnItemChildClickListener(new BaseQuickAdapter.OnItemChildClickListener() {
            @Override
            public void onItemChildClick(BaseQuickAdapter adapter, View view, int position) {
                dealSelectedState(position, true);
            }
        });
    }

    /**
     * 更新选中的数字
     *
     * @param position The index of list
     * @param number   The selected number
     */
    public void updateSelectedNumber(int position, int number) {
        if (mSelectedType != TYPE_ONE_FINISH) {
            MediaSection section = mAdapter.getItem(position);
            if (section != null && !section.isHeader) {
                section.t.setPosition(number);
                mAdapter.notifyItemChanged(position);
            }
        }
    }

    /**
     * 处理选中状态，再次点击可以取消选中
     * Deal with selected status,Click again to deselect it.
     *
     * @param position    The index of list
     * @param mediaChange true call back interface ,false not
     */
    public void dealSelectedState(int position, boolean mediaChange) {
        if (mAdapter != null) {
            MediaSection section = mAdapter.getItem(position);
            if (section != null && section.t != null && !WhiteList.isInTimelineWhiteList(section.t.getPath())) {
                ToastUtils.showShort(R.string.error_clip_file_is_invalid);
                return;
            }
            if (section != null && !section.isHeader) {
                if(mSelectedType == TYPE_ONE_FINISH){
                    mAdapter.select(position,section.t);
                }else{
                    mPresenter.dealSelected(section.t, position, false);
                }
                if (mediaChange && mListener != null) {
                    mListener.onMediaChange(section.t);
                }
            }
        }
    }

    /**
     * 处理选中，已选中的不做处理
     * Deal width selected ,Selected ones are not processed
     *
     * @param mediaPath String The path of media
     * @return the media tag
     */
    public MediaTag dealSelected(String mediaPath) {
        if (mSelectedType != TYPE_ONE_FINISH && !TextUtils.isEmpty(mediaPath)) {
            for (int i = 0; i < mAdapter.getData().size(); i++) {
                MediaSection section = mAdapter.getItem(i);
                if (section != null && !section.isHeader && mediaPath.equals(section.t.getThumbPath())) {
                    if (!section.t.isState()) {
                        mPresenter.dealSelected(section.t, i, false);
                    }
                    return (MediaTag) section.t.getTag();
                }
            }
        }
        return new MediaTag();
    }

    @Override
    public void onMediaBack(List<MediaSection> mediaData) {
        if (mediaData.size() > 0) {
            mAdapter.setNewData(null);
        }
        mAdapter.setNewData(mediaData);
        if (mAdapter.getData().size() > 0) {
            mRvMediaList.setVisibility(View.VISIBLE);
        }
    }

    @Override
    public void onItemChange(int position) {
        mAdapter.notifyItemChanged(position);
    }


    /**
     * The interface Media change listener.
     */
    public interface MediaChangeListener {
        /**
         * 选择的素材有变化
         * The selection of material varies
         *
         * @param mediaData 素材的信息
         */
        void onMediaChange(MediaData mediaData);

        /**
         * 预览素材
         * On media pre view.
         *
         * @param mediaData the media data
         */
        void onMediaPreView(MediaData mediaData);
    }
}
