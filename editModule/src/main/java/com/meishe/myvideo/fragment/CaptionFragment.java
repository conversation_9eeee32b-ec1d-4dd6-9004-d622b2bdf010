package com.meishe.myvideo.fragment;

import static com.meishe.myvideo.event.MessageEvent.MESSAGE_TYPE_ADD_NORMAL_CAPTION;

import android.os.Bundle;

import com.meishe.business.assets.fragment.FlowFragment;
import com.meishe.business.assets.iview.AssetsView;
import com.meishe.engine.asset.bean.AssetInfo;
import com.meishe.logic.constant.PagerConstants;
import com.meishe.myvideo.R;
import com.meishe.myvideo.event.MessageEvent;
import com.meishe.myvideo.fragment.presenter.CaptionMouldPresenter;

/**
 * All rights Reserved, Designed By www.meishesdk.com
 *
 * <AUTHOR> ChuChenGuang
 * @CreateDate : 2022/08/30 16:29
 * @Description : 普通字幕模板 The caption fragment
 * @Copyright : www.meishesdk.com Inc. All rights reserved.
 */
public class CaptionFragment extends FlowFragment<CaptionMouldPresenter> implements AssetsView {

    public CaptionFragment() {
        needDisplayName = false;
        spanCount = 4;
    }

    public static CaptionFragment create(int assetType) {
        CaptionFragment flowerFragment = new CaptionFragment();
        Bundle bundle = new Bundle();
        bundle.putInt(PagerConstants.BUNDLE_KEY_ASSET_TYPE, assetType);
        flowerFragment.setArguments(bundle);
        return flowerFragment;
    }


    @Override
    protected int getItemLayoutResId() {
        return R.layout.item_flower_bubble;
    }

    @Override
    protected void onAdapterItemClick(int position) {
        AssetInfo info = mAdapter.getItem(position);
        if (info != null) {
            MessageEvent.sendEvent(info,MESSAGE_TYPE_ADD_NORMAL_CAPTION);
        }
    }

    @Override
    protected String getAssetId() {

        return null;
    }
}
