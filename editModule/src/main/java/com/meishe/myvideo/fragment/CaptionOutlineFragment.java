package com.meishe.myvideo.fragment;

import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.SeekBar;

import com.meishe.base.model.BaseMvpFragment;
import com.meishe.engine.bean.MeicamCaptionClip;
import com.meishe.myvideo.R;
import com.meishe.myvideo.bean.ColorInfo;
import com.meishe.myvideo.fragment.iview.CaptionStyleView;
import com.meishe.myvideo.fragment.presenter.CaptionStylePresenter;
import com.meishe.myvideo.view.MYMultiColorView;
import com.meishe.base.view.SeekBarTextView;


/**
 * 字幕描边
 * The caption outline fragment
 */
public class CaptionOutlineFragment extends BaseMvpFragment<CaptionStylePresenter> implements CaptionStyleView {

    private MYMultiColorView mMultiColorView;
    /**
     * 宽度
     * width
     */
    private SeekBarTextView mSbWidth;
    /**
     * 不透明度
     * Opacity
     */
    private SeekBarTextView mSbOpacity;

    private ImageView mIvClear;
    private LinearLayout linearWidth, linearOpacity;

    public CaptionOutlineFragment() {
        mPresenter = new CaptionStylePresenter(null);
    }

    public static CaptionOutlineFragment create(MeicamCaptionClip captionClip) {
        CaptionOutlineFragment fragment = new CaptionOutlineFragment();
        fragment.updateCaptionClip(captionClip);
        return fragment;
    }

    /**
     * 这里重写，不使用模板自动生成的Presenter了.
     * Rewrite here, do not use the Presenter generated automatically by the template
     *
     * @return The presenter
     */
    @Override
    protected CaptionStylePresenter createPresenter() {
        return mPresenter;
    }

    @Override
    protected int bindLayout() {
        return R.layout.fragment_caption_style_outline;
    }

    @Override
    protected void onLazyLoad() {

    }

    @Override
    protected void initView(View rootView) {
        mMultiColorView = rootView.findViewById(R.id.multi_color_view);
        linearWidth = rootView.findViewById(R.id.linear_outline_sb_width);
        linearOpacity = rootView.findViewById(R.id.linear_outline_sb_opacity);
        mSbWidth = rootView.findViewById(R.id.outline_sb_width);
        mSbWidth = rootView.findViewById(R.id.outline_sb_width);
        mSbOpacity = rootView.findViewById(R.id.outline_sb_opacity);
        mIvClear = rootView.findViewById(R.id.iv_clear_color);
        initListener();
    }

    private void initListener() {
        mMultiColorView.setColorClickListener(new MYMultiColorView.OnColorClickListener() {
            @Override
            public void onClick(ColorInfo colorInfo) {
                linearWidth.setVisibility(View.VISIBLE);
                linearOpacity.setVisibility(View.VISIBLE);
                if (mPresenter.hasCaptionOutlineColor()) {
                    mPresenter.setCaptionOutLineColor(colorInfo.getCommonInfo());
                    mPresenter.setCaptionOutLineOpacity((int) mSbOpacity.getProgress());
                } else {
                    mSbOpacity.setProgress(100);
                    mPresenter.setCaptionOutLineColor(colorInfo.getCommonInfo());
                }

            }
        });
        mSbWidth.setListener(new SeekBarTextView.OnSeekBarListener() {
            @Override
            public void onStopTrackingTouch(int progress) {

            }

            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                if (fromUser) {
                    mPresenter.setCaptionOutLineWidth(progress);
                }
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {

            }


        });

        mSbOpacity.setListener(new SeekBarTextView.OnSeekBarListener() {
            @Override
            public void onStopTrackingTouch(int progress) {

            }

            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                if (fromUser) {
                    mPresenter.setCaptionOutLineOpacity(progress);
                }
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {

            }


        });

        mIvClear.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mMultiColorView.selected(-1);
                linearWidth.setVisibility(View.INVISIBLE);
                linearOpacity.setVisibility(View.INVISIBLE);
                mPresenter.clearCaptionOutLine();
            }
        });
    }

    @Override
    protected void initData() {
        if (!mPresenter.hasCaptionOutlineColor()) {
            linearWidth.setVisibility(View.INVISIBLE);
            linearOpacity.setVisibility(View.INVISIBLE);
        }
        mMultiColorView.selected(mPresenter.getCaptionOutlineColor());
        mSbOpacity.setProgress((int) (mPresenter.getCaptionOutLineOpacity() * 100));
        mSbWidth.setProgress((int) (mPresenter.getCaptionOutLineWidth() * 10));
    }

    /**
     * 更新字幕片段
     * Update caption clip
     *
     * @param captionClip the new caption clip
     */
    public void updateCaptionClip(MeicamCaptionClip captionClip) {
        mPresenter.updateCaptionClip(captionClip);
        if (isInitView) {
            if (!mPresenter.hasCaptionOutlineColor()) {
                linearWidth.setVisibility(View.INVISIBLE);
                linearOpacity.setVisibility(View.INVISIBLE);
            }
            mMultiColorView.selected(mPresenter.getCaptionOutlineColor());
            mSbOpacity.setProgress((int) (mPresenter.getCaptionOutLineOpacity() * 100));
            mSbWidth.setProgress((int) (mPresenter.getCaptionOutLineWidth() * 10));
        }
    }

}
