package com.meishe.myvideo.fragment;

import android.content.res.ColorStateList;
import android.view.View;
import android.widget.TextView;

import androidx.fragment.app.FragmentPagerAdapter;

import com.google.android.material.tabs.TabLayout;
import com.meishe.base.adapter.CommonFragmentAdapter;
import com.meishe.base.model.BaseMvpFragment;
import com.meishe.base.view.CustomViewPager;
import com.meishe.base.view.HorizontalSeekBar;
import com.meishe.business.assets.view.AssetsTypeTabView;
import com.meishe.engine.asset.bean.AssetInfo;
import com.meishe.engine.bean.MeicamStickerClip;
import com.meishe.engine.bean.StickerAnimation;
import com.meishe.engine.interf.IBaseInfo;
import com.meishe.myvideo.R;
import com.meishe.myvideo.fragment.iview.StickerAnimationView;
import com.meishe.myvideo.fragment.presenter.StickerAnimationPresenter;

import java.util.ArrayList;
import java.util.List;

import static com.meishe.myvideo.fragment.presenter.StickerAnimationPresenter.ANIMATION_TYPE_COMP;
import static com.meishe.myvideo.fragment.presenter.StickerAnimationPresenter.ANIMATION_TYPE_IN;
import static com.meishe.myvideo.fragment.presenter.StickerAnimationPresenter.ANIMATION_TYPE_OUT;

/**
 * All rights Reserved, Designed By www.meishesdk.com
 *
 * @Author: yangtailin
 * @CreateDate: 2020/6/17 10:30
 * @Description: 贴纸动画Fragment The sticker animation fragment.
 * @Copyright: www.meishesdk.com Inc. All rights reserved.
 */
public class StickerAnimationFragment extends BaseMvpFragment<StickerAnimationPresenter> implements StickerAnimationView {

    private static final int COMP_TAB_POSITION = 2;
    private TabLayout mTabLayout;
    private CustomViewPager mViewPager;
    private List<StickerAnimationStyleFragment> mFragmentList;
    private HorizontalSeekBar mSeekBar;
    private MeicamStickerClip mStickerClip;
    private AssetsTypeTabView mTabTypeView;
    private View mSeekBarLayout;
    private TextView mTvFast, mTvSlow;
    private OnEventListener mOnEventListener;
    private View mConfirmLayout;

    public void setOnEventListener(OnEventListener listener) {
        this.mOnEventListener = listener;
    }

    public StickerAnimationFragment() {
    }

    public static StickerAnimationFragment create(MeicamStickerClip stickerClip) {
        StickerAnimationFragment fragment = new StickerAnimationFragment();
        fragment.mStickerClip = stickerClip;
        return fragment;
    }

    @Override
    protected int bindLayout() {
        return R.layout.fragment_sticker_animation;
    }

    @Override
    protected void onLazyLoad() {

    }

    @Override
    protected void initView(View rootView) {
        mTabLayout = rootView.findViewById(R.id.tab_layout);
        mTabLayout.setTabRippleColor(ColorStateList.valueOf(getResources().getColor(R.color.color_ff181818)));
        mViewPager = rootView.findViewById(R.id.viewPager);
        mSeekBar = rootView.findViewById(R.id.seek_bar);
        mTvFast = rootView.findViewById(R.id.tv_animation_group_fast);
        mTvSlow = rootView.findViewById(R.id.tv_animation_group_slow);
        mSeekBarLayout = rootView.findViewById(R.id.seek_bar_layout);
        mTabTypeView = rootView.findViewById(R.id.ttv_tab_type);
        mConfirmLayout = rootView.findViewById(R.id.fl_confirm);
        mViewPager.setScanScroll(false);
        mViewPager.setOffscreenPageLimit(3);
        mSeekBar.setTransformText(1000, 1);
        initListener();
    }

    private void initFragment() {
        if (mFragmentList != null) {
            mFragmentList.clear();
        } else {
            mFragmentList = new ArrayList<>();
        }
        StickerAnimationStyleFragment.AnimationEventListener listener = new StickerAnimationStyleFragment.AnimationEventListener() {
            @Override
            public void onAnimationClick(IBaseInfo info, int type) {
                mPresenter.handleStickerAnimation(info, type);
                updateAnimation();
            }

            @Override
            public void onDataBack(List<AssetInfo> info, int subType) {
                if (info == null || info.size() <= 2) {
                    mSeekBarLayout.setVisibility(View.INVISIBLE);
                }
            }
        };
        mPresenter.getAnimation(ANIMATION_TYPE_IN);
        mFragmentList.add(new StickerAnimationStyleFragment(ANIMATION_TYPE_IN, mPresenter.getAnimation(ANIMATION_TYPE_IN), listener));
        mFragmentList.add(new StickerAnimationStyleFragment(ANIMATION_TYPE_OUT, mPresenter.getAnimation(ANIMATION_TYPE_OUT), listener));
        mFragmentList.add(new StickerAnimationStyleFragment(ANIMATION_TYPE_COMP, mPresenter.getAnimation(ANIMATION_TYPE_COMP), listener));
        setAdapter();
        final String[] tabs = getResources().getStringArray(R.array.menu_tab_sub_caption_animation);
        mTabLayout.removeAllTabs();
        for (String tab : tabs) {
            mTabLayout.addTab(mTabLayout.newTab().setText(tab));
        }

    }

    private void setAdapter() {
        FragmentPagerAdapter adapter = new CommonFragmentAdapter(getChildFragmentManager(), mFragmentList);
        mViewPager.setAdapter(adapter);
        mTabLayout.setupWithViewPager(mViewPager);
    }

    private void initListener() {
        mSeekBar.setOnRangeListener(new HorizontalSeekBar.onRangeListener() {
            @Override
            public void onRange(float left, float right) {
                if (mStickerClip == null) {
                    return;
                }
                int leftValue = (int) (left * 1000);
                //组合动画与出入动画互斥(出入动画不互斥)。前者默认时长0.5s后者0.6s
                // Composite animation and access animation are mutually exclusive (access animation is not mutually exclusive).
                // The default duration of the former is 0.5s and the latter is 0.6s.
                StickerAnimation animation = mPresenter.getAnimation(ANIMATION_TYPE_COMP);
                if (animation != null) {
                    if (leftValue <= 100) {
                        leftValue = 100;
                        mSeekBar.setLeftProgress(leftValue, true);
                        //组合动画最小值可设置成100ms
                        //The minimum value of composite animation can be set to 100ms
                    }
                }
            }

            @Override
            public void onLeftScrollEnd(float value) {
                int leftValue = (int) (value * 1000);
                StickerAnimation animation = mPresenter.getAnimation(ANIMATION_TYPE_COMP);
                if (animation != null) {
                    mPresenter.handleStickerAnimationDuration(leftValue, ANIMATION_TYPE_COMP);
                } else {
                    animation = mPresenter.getAnimation(ANIMATION_TYPE_IN);
                    if (animation != null) {
                        mPresenter.handleStickerAnimationDuration(leftValue, ANIMATION_TYPE_IN);
                    }
                }
            }

            @Override
            public void onRightScrollEnd(float value) {
                StickerAnimation animation = mPresenter.getAnimation(ANIMATION_TYPE_OUT);
                if (animation != null) {
                    mPresenter.handleStickerAnimationDuration((long) (value * 1000), ANIMATION_TYPE_OUT);
                }
            }
        });
        mTabLayout.addOnTabSelectedListener(new TabLayout.OnTabSelectedListener() {
            @Override
            public void onTabSelected(TabLayout.Tab tab) {
                int position = tab.getPosition();
                mTabTypeView.setSelected(0);
                if (mStickerClip == null) {
                    return;
                }
                StickerAnimationStyleFragment fragment = mFragmentList.get(position);
                fragment.onSubTypeClicked(0);
                if (position == 0) {
                    StickerAnimation animation = mPresenter.getAnimation(ANIMATION_TYPE_IN);
                    if (animation != null) {
                        fragment.selectedAnimation(animation.getPackageId());
                        mPresenter.play(ANIMATION_TYPE_IN);
                    } else {
                        displayAnimationProgress();
                        mPresenter.stop(ANIMATION_TYPE_IN);
                    }
                } else if (position == 1) {
                    displayCombineAnimationView(false);
                    StickerAnimation animation = mPresenter.getAnimation(ANIMATION_TYPE_OUT);
                    if (animation != null) {
                        fragment.selectedAnimation(animation.getPackageId());
                        mPresenter.play(ANIMATION_TYPE_OUT);
                    } else {
                        displayAnimationProgress();
                        mPresenter.stop(ANIMATION_TYPE_OUT);
                    }
                } else if (position == 2) {
                    StickerAnimation animation = mPresenter.getAnimation(ANIMATION_TYPE_COMP);
                    if (animation != null) {
                        fragment.selectedAnimation(animation.getPackageId());
                        mPresenter.play(ANIMATION_TYPE_COMP);
                    } else {
                        displayAnimationProgress();
                        mPresenter.stop(ANIMATION_TYPE_COMP);
                    }
                }
            }

            @Override
            public void onTabUnselected(TabLayout.Tab tab) {

            }

            @Override
            public void onTabReselected(TabLayout.Tab tab) {

            }
        });


        mTabTypeView.setItemClickedListener(new AssetsTypeTabView.ItemClickedListener() {
            @Override
            public void onItemClicked(int position) {
                StickerAnimationStyleFragment fragment = mFragmentList.get(mViewPager.getCurrentItem());
                fragment.onSubTypeClicked(position);
            }
        });

        mConfirmLayout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mOnEventListener != null) {
                    mOnEventListener.onConfirm();
                }
            }
        });
    }

    @Override
    protected void initData() {
        if (mStickerClip != null) {
            mSeekBar.setMaxProgress((int) (mStickerClip.getOutPoint() - mStickerClip.getInPoint()) / 1000);
        }
        mPresenter.setStickerClip(mStickerClip);
        initFragment();
        showAnimationProgress();
    }

    /**
     * 展示动画进度
     * Show animation progress
     */
    private void showAnimationProgress() {
        if (mStickerClip == null) {
            return;
        }
        displayAnimationProgress();
    }

    /**
     * 更新字幕片段
     * Update caption clip
     *
     * @param captionClip the caption clip
     */
    public void updateClip(MeicamStickerClip captionClip) {
        if (captionClip != null) {
            mPresenter.setStickerClip(captionClip);
            updateAnimation();
        }
        if (mSeekBar != null) {
            mSeekBar.setMaxProgress(mPresenter.getMaxProgress());
        }
    }

    private void updateAnimation() {
        for (int index = 0; index < mFragmentList.size(); index++) {
            StickerAnimationStyleFragment fragment = mFragmentList.get(index);
            fragment.updateCaptionClip(mPresenter.getAnimation((index + 1)));
        }
        displayAnimationProgress();
    }

    /**
     * Display animation progress.
     * 显示动画时长进度
     */
    private void displayAnimationProgress() {
        StickerAnimation animationIn = mPresenter.getAnimation(ANIMATION_TYPE_IN);
        StickerAnimation animationOut = mPresenter.getAnimation(ANIMATION_TYPE_OUT);
        StickerAnimation animationComp = mPresenter.getAnimation(ANIMATION_TYPE_COMP);
        mSeekBar.reset();
        int tabPosition = mTabLayout.getSelectedTabPosition();
        if (animationIn == null && animationOut == null) {
            mSeekBar.setLeftMoveIcon(-1);
            mSeekBar.setRightMoveIcon(-1);
            if (animationComp != null && tabPosition == COMP_TAB_POSITION) {
                mSeekBarLayout.setVisibility(View.VISIBLE);
                displayCombineAnimationView(true);
                if (mSeekBar.getLastLeftIconId() != R.mipmap.round_white) {
                    //已经重置过状态了没有必要重置了
                    //The status has been reset. There is no need to reset
                    mSeekBar.reset();
                    mSeekBar.setMoveIconSize(15, 15);
                    mSeekBar.setLeftMoveIcon(R.mipmap.round_white);
                }
                mSeekBar.setLeftProgress((int) animationComp.getDuration(), true);
            } else {
                mSeekBarLayout.setVisibility(View.INVISIBLE);
            }
        } else {
            if (tabPosition == COMP_TAB_POSITION) {
                mSeekBarLayout.setVisibility(View.INVISIBLE);
            } else {
                mSeekBarLayout.setVisibility(View.VISIBLE);
            }
            displayCombineAnimationView(false);
            mSeekBar.setMoveIconSize(23, 37);
            if (animationIn != null) {
                mSeekBar.setMoveIconLowPadding(7);
                mSeekBar.setLeftMoveIcon(R.mipmap.icon_animation_in);
                mSeekBar.setLeftProgress((int) animationIn.getDuration(), false);
            }
            if (animationOut != null) {
                mSeekBar.setMoveIconLowPadding(7);
                mSeekBar.setRightMoveIcon(R.mipmap.icon_animation_out);
                mSeekBar.setRightProgress((int) animationOut.getDuration());
            }
        }
    }

    /**
     * 展示组合动画相关的视图
     *
     * @param display true display ,false not
     */
    private void displayCombineAnimationView(boolean display) {
        int visibility = display ? View.VISIBLE : View.INVISIBLE;
        mTvFast.setVisibility(visibility);
        mTvSlow.setVisibility(visibility);
    }

    public interface OnEventListener {
        /**
         * 确认回调
         * On confirm.
         */
        void onConfirm();
    }
}
