package com.meishe.myvideo.fragment;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.meishe.base.bean.MediaData;
import com.meishe.base.constants.Constants;
import com.meishe.base.manager.AppManager;
import com.meishe.base.model.BaseFragment;
import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.ImageLoader;
import com.meishe.base.utils.SizeUtils;
import com.meishe.base.utils.Utils;
import com.meishe.base.view.decoration.ItemDecoration;
import com.meishe.engine.EditorEngine;
import com.meishe.engine.asset.AssetsManager;
import com.meishe.engine.asset.bean.AssetInfo;
import com.meishe.engine.bean.MeicamTimeline;
import com.meishe.engine.bean.MeicamWaterMark;
import com.meishe.engine.interf.IBaseInfo;
import com.meishe.myvideo.R;
import com.meishe.myvideo.activity.MaterialSingleSelectActivity;
import com.meishe.myvideo.interfaces.BottomEventListener;
import com.meishe.third.adpater.BaseQuickAdapter;
import com.meishe.third.adpater.BaseViewHolder;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import static com.meishe.logic.constant.PagerConstants.MEDIA_TYPE;
import static com.meishe.myvideo.activity.DraftEditActivity.REQUEST_SELECT_WATERMARK;

/**
 * The type Water fragment.
 * 水印
 */
public class WaterFragment extends BaseFragment {
    private WaterAdapter mAdapter;
    private BottomEventListener mEventListener;
    private View confirmView;

    public WaterFragment() {
    }

    public static WaterFragment getInstance(BottomEventListener listener) {
        WaterFragment fragment = new WaterFragment();
        fragment.mEventListener = listener;
        return fragment;
    }


    @Override
    protected int bindLayout() {
        return R.layout.fragment_water_mark;
    }

    @Override
    protected void onLazyLoad() {

    }

    @Override
    protected void initView(View rootView) {
        RecyclerView rvWaterMarkList = rootView.findViewById(R.id.recyclerView);
        confirmView = rootView.findViewById(R.id.iv_confirm);
        TextView tvContent = rootView.findViewById(R.id.tv_content);
        tvContent.setText(R.string.fragment_menu_water);
        LinearLayoutManager layoutManager = new LinearLayoutManager(getContext(), LinearLayoutManager.HORIZONTAL, false);
        rvWaterMarkList.setLayoutManager(layoutManager);
        mAdapter = new WaterAdapter();
        rvWaterMarkList.setAdapter(mAdapter);
        rvWaterMarkList.addItemDecoration(new ItemDecoration(SizeUtils.dp2px(5), SizeUtils.dp2px(12), SizeUtils.dp2px(5), 0));
        initListener();
    }

    private void initListener() {
        mAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                if (position != 0) {
                    mAdapter.selected(position);
                }
                IBaseInfo baseInfo = mAdapter.getItem(position);
                if (baseInfo != null) {
                    if (getResources().getString(R.string.add).equals(baseInfo.getName())) {
                        if (Utils.isFastClick()){
                            return;
                        }
                        Bundle bundle = new Bundle();
                        bundle.putInt(MEDIA_TYPE, MediaData.TYPE_PHOTO);
                        AppManager.getInstance().jumpActivityForResult(getActivity(),
                                MaterialSingleSelectActivity.class, bundle, REQUEST_SELECT_WATERMARK);
                    } else {
                        if (TextUtils.isEmpty(baseInfo.getAssetPath()) && (baseInfo.getType() == Constants.WATER)) {
                            return;
                        }
                        onWaterMarkItemClick(baseInfo);
                    }
                }

            }
        });
        confirmView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mEventListener != null) {
                    mEventListener.onDismiss(true);
                }
            }
        });
    }

    @Override
    protected void initData() {
        final List<IBaseInfo> waterMarkInfoList = new ArrayList<>();
        IBaseInfo waterMarkInfo = new AssetInfo();
        waterMarkInfo.setName(getString(R.string.add));
        waterMarkInfoList.add(waterMarkInfo);
        waterMarkInfo = new AssetInfo();
        waterMarkInfo.setCoverPath("file:///android_asset/water_mark/pic_meiying.png");
        waterMarkInfo.setAssetPath("assets:/water_mark/water_mark_meiying.png");
        waterMarkInfo.setType(AssetInfo.ASSET_WATER);
        waterMarkInfoList.add(waterMarkInfo);
        AssetsManager.get().getDatabaseAssetList(AssetInfo.ASSET_WATER, new AssetsManager.AssetCallback() {
            @Override
            public void onSuccess(List<AssetInfo> assetInfoList) {
                if (assetInfoList != null && assetInfoList.size() > 0) {
                    waterMarkInfoList.addAll(assetInfoList);
                }
                mAdapter.setNewData(waterMarkInfoList);
                updateSelected();
            }

            @Override
            public void onFailure() {

            }
        });
    }


    /**
     * 添加新的水印
     * Add new watermark
     *
     * @param picturePath the watermark path
     */
    public void addWaterMark(String picturePath) {
        if (mAdapter != null && !isAdd(picturePath)) {
            AssetInfo assetInfo = new AssetInfo();
            assetInfo.setId(UUID.randomUUID().toString());
            assetInfo.setType(AssetInfo.ASSET_WATER);
            assetInfo.setCoverPath(picturePath);
            assetInfo.setAssetPath(picturePath);
            AssetsManager.get().updateDatabase(assetInfo, false);
            mAdapter.addData(assetInfo);
            List<IBaseInfo> list = mAdapter.getData();
            for (int i = 0; i < list.size(); i++) {
                AssetInfo waterMarkInfo = (AssetInfo) list.get(i);
                if (picturePath.equals(waterMarkInfo.getAssetPath())) {
                    mAdapter.selected(i);
                    onWaterMarkItemClick(assetInfo);
                    break;
                }
            }
        }
    }

    /**
     * 水印列表点击
     *
     * @param assetInfo The asset information 水印数据
     */
    private void onWaterMarkItemClick(IBaseInfo assetInfo) {
        MeicamTimeline meicamTimeline = EditorEngine.getInstance().getCurrentTimeline();
        if (meicamTimeline != null) {
            meicamTimeline.getMeicamWaterMark().setWatermarkFilePath(assetInfo.getAssetPath());
        }
        if (mEventListener != null) {
            mEventListener.onItemClick(assetInfo, false);
        }
    }

    private boolean isAdd(String path) {
        for (IBaseInfo waterMarkInfo : mAdapter.getData()) {
            if (path.equals(waterMarkInfo.getAssetPath())) {
                return true;
            }
        }
        return false;
    }


    /**
     * Cancel selected item.
     * 取消选中项
     */
    public void unselected() {
        if (mAdapter != null) {
            mAdapter.selected(-1);
        }
    }


    /**
     * Update selected.
     * 更新选中
     */
    public void updateSelected() {
        MeicamTimeline meicamTimeline = EditorEngine.getInstance().getCurrentTimeline();
        if (meicamTimeline == null) {
            return;
        }
        MeicamWaterMark meicamWaterMark = meicamTimeline.getMeicamWaterMark();
        if (meicamWaterMark != null && mAdapter != null) {
            String path = meicamWaterMark.getWatermarkFilePath();
            if (TextUtils.isEmpty(path)) {
                mAdapter.selected(-1);
                return;
            }
            for (int i = 1; i < mAdapter.getData().size(); i++) {
                String waterPath = mAdapter.getData().get(i).getAssetPath();
                if (i == 1 && path.contains(mAdapter.getData().get(i).getAssetPath())) {
                    mAdapter.selected(1);
                    break;
                }
                if (path.equals(waterPath)) {
                    mAdapter.selected(i);
                    break;
                }
            }
        }

    }

    private static class WaterAdapter extends BaseQuickAdapter<IBaseInfo, BaseViewHolder> {
        private int mSelectedPosition = -1;

        private WaterAdapter() {
            super(R.layout.item_water_mark_);
        }

        /**
         * 选中某一项
         * Selected item .
         *
         * @param position The index of list
         */
        public void selected(int position) {
            //注意：0不允许被选中
            //Note: 0 is not allowed to be selected
            if (mSelectedPosition > 0) {
                notifyItemChanged(mSelectedPosition);
            }
            mSelectedPosition = position;
            if (position > 0 && position < getData().size()) {
                notifyItemChanged(position);
            }
        }

        @Override
        protected void convert(@NonNull BaseViewHolder helper, IBaseInfo item) {
            FrameLayout moreParent = helper.getView(R.id.fl_add);
            ImageView ivCover = helper.getView(R.id.iv_cover);
            View vMask = helper.getView(R.id.v_mask);
            if (mContext.getResources().getString(R.string.add).equals(item.getName())) {
                moreParent.setVisibility(View.VISIBLE);
                ivCover.setVisibility(View.GONE);
            } else {
                if (ivCover.getVisibility() != View.VISIBLE) {
                    moreParent.setVisibility(View.GONE);
                    ivCover.setVisibility(View.VISIBLE);
                }
                ImageLoader.loadUrl(mContext, item.getCoverPath(), ivCover);
            }
            if (helper.getAdapterPosition() == mSelectedPosition) {
                vMask.setBackground(CommonUtils.getRadiusDrawable(SizeUtils.dp2px(2), mContext.getResources().getColor(R.color.color_fffc2b55), SizeUtils.dp2px(4), -1));
            } else {
                vMask.setBackgroundResource(0);
            }
        }
    }
}
