package com.meishe.myvideo.fragment;

import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.facebook.drawee.backends.pipeline.Fresco;
import com.facebook.drawee.interfaces.DraweeController;
import com.facebook.drawee.view.SimpleDraweeView;
import com.meishe.base.manager.LinearLayoutManagerWrapper;
import com.meishe.base.model.BaseMvpFragment;
import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.ImageLoader;
import com.meishe.base.utils.NetUtils;
import com.meishe.base.utils.SizeUtils;
import com.meishe.base.utils.ToastUtils;
import com.meishe.base.utils.Utils;
import com.meishe.base.view.decoration.ItemDecoration;
import com.meishe.business.assets.AssetUtils;
import com.meishe.business.assets.iview.AssetsView;
import com.meishe.engine.asset.AssetsManager;
import com.meishe.engine.asset.bean.AssetInfo;
import com.meishe.engine.asset.bean.AssetsConstants;
import com.meishe.engine.bean.StickerAnimation;
import com.meishe.engine.interf.IBaseInfo;
import com.meishe.myvideo.R;
import com.meishe.myvideo.fragment.presenter.StickerAnimationStylePresenter;
import com.meishe.third.adpater.BaseQuickAdapter;
import com.meishe.third.adpater.BaseViewHolder;

import java.util.ArrayList;
import java.util.List;

/**
 * 字幕动画样式碎片
 * Caption animation style fragments
 */
public class StickerAnimationStyleFragment extends BaseMvpFragment<StickerAnimationStylePresenter> implements AssetsView {
    private RecyclerView mRvAnimationList;
    private AnimationStyleAdapter mAdapter;
    private int mAssetSubType = 0;
    private int mAssetKind = -1;
    private String mAnimationId;
    private StickerAnimation mStickerAnimation;
    private AnimationEventListener mEventListener;
    private TextView mHintText;
    private int animationType;
    private String mDownloadTag;

    public StickerAnimationStyleFragment() {
    }

    /**
     * Instantiates a new Caption animation style fragment.
     * 实例化一个新的标题动画样式片段
     *
     * @param animationType the animation type
     */
    StickerAnimationStyleFragment(int animationType, StickerAnimation captionClip, AnimationEventListener listener) {
        mStickerAnimation = captionClip;
        mEventListener = listener;
        mAssetKind = animationType;
        changeAnimationId();
    }

    /**
     * Change animation id
     * 改变动画id
     */
    private void changeAnimationId() {
        mAnimationId = mStickerAnimation == null ? "" : mStickerAnimation.getPackageId();
        if (mAdapter != null) {
            mAdapter.selected(mAnimationId);
        }
    }

    @Override
    protected int bindLayout() {
        return R.layout.fragment_sticker_common_list;
    }

    @Override
    protected void onLazyLoad() {

    }


    @Override
    protected void initView(View rootView) {
        mRvAnimationList = rootView.findViewById(R.id.recyclerView);
        LinearLayoutManager layoutManager = new LinearLayoutManagerWrapper(getContext(), LinearLayoutManager.HORIZONTAL, false);
        mRvAnimationList.setLayoutManager(layoutManager);
        mAdapter = new AnimationStyleAdapter();
        mRvAnimationList.setAdapter(mAdapter);
        mRvAnimationList.addItemDecoration(new ItemDecoration(SizeUtils.dp2px(5), SizeUtils.dp2px(0), SizeUtils.dp2px(5), 12));
        mHintText = rootView.findViewById(R.id.tv_hint);
        initListener();
    }

    @Override
    protected void initData() {
        updateData(true);
    }

    private void updateData(final boolean needUpdate) {
        if (mHintText == null) {
            return;
        }
        mHintText.setCompoundDrawables(null, null, null, null);
        mPresenter.loadData(AssetsConstants.AssetsTypeData.STICKER_ANIMATION.type, mAssetSubType, AssetsConstants.AssetsTypeData.STICKER_ANIMATION.category, mAssetKind, needUpdate);
    }

    private boolean loadMore() {
        mHintText.setCompoundDrawables(null, null, null, null);
        return mPresenter.loadMoreData(AssetsConstants.AssetsTypeData.STICKER_ANIMATION.type, mAssetSubType, AssetsConstants.AssetsTypeData.STICKER_ANIMATION.category, mAssetKind, false);
    }


    private void initListener() {
        mAdapter.setOnLoadMoreListener(new BaseQuickAdapter.RequestLoadMoreListener() {
            @Override
            public void onLoadMoreRequested() {
                if (!loadMore()) {
                    mAdapter.loadMoreEnd(true);
                }
            }
        }, mRvAnimationList);
        mAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                AssetInfo info = mAdapter.getItem(position);
                if (info != null) {
                    if (position == 0) {
                        applyCaptionAnimation(info);
                        mAdapter.selected(position);
                    } else {
                        if ((!info.isHadDownloaded() || info.needUpdate())) {
                            downloadAsset(info, position);
                        } else {
                            applyCaptionAnimation(info);
                            mPresenter.clickAssetItem(info);
                            mAdapter.selected(position);
                        }
                    }
                    mAnimationId = info.getPackageId();
                }
            }
        });
    }


    void onSubTypeClicked(int position) {
        mAssetSubType = position;
        if (mAdapter != null) {
            mAdapter.setAssetSubType(position);
        }
        updateData(false);
    }

    /**
     * 下载特效资源包
     * Download the effects resource pack
     *
     * @param assetInfo asset info
     * @param position  the index of asset info int the list
     */
    private void downloadAsset(final AssetInfo assetInfo, final int position) {
        assetInfo.setDownloadProgress(0);
        mAdapter.notifyItemChanged(position);
        mDownloadTag = assetInfo.getPackageId();
        mPresenter.downloadAsset(assetInfo, position);
    }

    /**
     * 应用字幕动画
     * Apply the caption animation
     */
    private void applyCaptionAnimation(AssetInfo info) {
        if (mEventListener != null) {
            mEventListener.onAnimationClick(info, mAssetKind);
        }
    }

    /**
     * Sets item selected.
     * 设置选中动画
     *
     * @param animationId the animation id
     */
    void selectedAnimation(String animationId) {
        if (mAdapter != null) {
            mAnimationId = animationId;
            mAdapter.selected(animationId);
        }
    }

    private void onAnimationSelected() {
        if (CommonUtils.isIndexAvailable(mAdapter.mSelectedPosition, mAdapter.getData())) {
            AssetInfo item = mAdapter.getItem(mAdapter.mSelectedPosition);
            if (mEventListener != null) {
                mEventListener.onAnimationClick(item, animationType);
            }
        }
    }

    /**
     * 更新字幕片段
     * Update caption clip
     *
     * @param captionClip the caption clip
     */
    public void updateCaptionClip(StickerAnimation captionClip) {
        mStickerAnimation = captionClip;
        changeAnimationId();
    }

    @Override
    public boolean isActive() {
        return isAdded();
    }

    @Override
    public void onNewDataBack(List<AssetInfo> list, int subType, final boolean needUpdate) {
        if (!CommonUtils.isEmpty(list)) {
            mAdapter.setNewData(list);
        }
        updateViewState(subType, needUpdate);
    }

    private void updateViewState(int subType, final boolean needUpdate) {
        if (getItemCount() <= 0) {
            mHintText.setVisibility(View.VISIBLE);
            mRvAnimationList.setVisibility(View.GONE);

            if (!NetUtils.isNetworkAvailable(getContext())) {
                mHintText.setText(R.string.user_hint_assets_net_error_refresh);
                Drawable drawable = getResources().getDrawable(R.mipmap.ic_assets_data_update);
                drawable.setBounds(new Rect(0, 4, drawable.getIntrinsicHeight(), drawable.getIntrinsicHeight() + 4));
                mHintText.setCompoundDrawables(null, null, drawable, null);
                mHintText.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        mHintText.setCompoundDrawables(null, null, null, null);
                        updateData(needUpdate);
                    }
                });
            } else {
                mHintText.setText(AssetsManager.get().getErrorMsg(getContext(), subType));
            }
        } else {
            mHintText.setVisibility(View.GONE);
            mRvAnimationList.setVisibility(View.VISIBLE);
            if (!NetUtils.isNetworkAvailable(getContext()) && needUpdate) {
                ToastUtils.showShort(Utils.getApp().getResources().getString(R.string.user_hint_assets_net_error));
            }
        }
        if (mEventListener != null) {
            mEventListener.onDataBack(mAdapter.getData(), mAssetSubType);
        }
        //网络数据排序，所以每次都要检查选中。
        //Network data is sorted, so check and select each time.
        selectedAnimation(mAnimationId);
        onAnimationSelected();
    }

    @Override
    public void onMoreDataBack(List<AssetInfo> list, int subType, boolean needUpdate) {
        if (!CommonUtils.isEmpty(list)) {
            mAdapter.addData(list);
        }
        mAdapter.loadMoreComplete();
        updateViewState(subType, needUpdate);
    }

    @Override
    public void onDataError(int subType, boolean needUpdate) {
        mAdapter.setNewData(new ArrayList<>());
        mAdapter.loadMoreComplete();
        updateViewState(subType, needUpdate);
    }

    @Override
    public int getItemCount() {
        //最少数量是2，包括null和加载更多item
        //The minimum number is 2, including null and loading more items.
        return mAdapter == null ? 0 : mAdapter.getItemCount() - 2;
    }

    @Override
    public void onDownloadProgress(int position) {
        mAdapter.notifyItemChanged(position);
    }

    @Override
    public void onDownloadFinish(int position, AssetInfo assetInfo) {
        if (TextUtils.equals(mDownloadTag, assetInfo.getPackageId())) {
            mAdapter.selected(position);
            applyCaptionAnimation(assetInfo);
        } else {
            mAdapter.notifyItemChanged(position);
        }
    }

    @Override
    public void onDownloadError(int position) {
        mAdapter.notifyItemChanged(position);
    }


    public interface AnimationEventListener {
        void onAnimationClick(IBaseInfo info, int type);

        void onDataBack(List<AssetInfo> info, int subType);
    }

    private static class AnimationStyleAdapter extends BaseQuickAdapter<AssetInfo, BaseViewHolder> {
        private int mSelectedPosition = 0;
        private int mAssetSubType = 0;

        private AnimationStyleAdapter() {
            super(R.layout.item_caption_animation);
        }

        void setAssetSubType(int mAssetSubType) {
            this.mAssetSubType = mAssetSubType;
        }

        /**
         * 选中某一项
         * Selected item .
         *
         * @param animationId The item id
         */
        public void selected(String animationId) {
            if (TextUtils.isEmpty(animationId)) {
                selected(0);
            } else {
                int index = 0;
                for (int i = 0; i < getData().size(); i++) {
                    if (animationId.equals(getData().get(i).getPackageId())) {
                        index = i;
                        break;
                    }
                }
                selected(index);
            }
        }

        /**
         * 选中某一项
         * Selected item .
         *
         * @param position The index of list
         */
        public void selected(int position) {
            if (position == mSelectedPosition) {
                return;
            }
            if (mSelectedPosition >= 0) {
                notifyItemChanged(mSelectedPosition);
            }
            mSelectedPosition = position;
            if (position >= 0 && position < getData().size()) {
                notifyItemChanged(position);
            }
        }

        //有WebP图片所以用了Fresco
        // There are WebP images, so Fresco is used
        @Override
        protected void convert(@NonNull BaseViewHolder helper, AssetInfo item) {
            helper.setText(R.id.tv_animation_name, item.getName());
            SimpleDraweeView ivCover = helper.getView(R.id.iv_cover);
            TextView tvAssetPurchased = helper.getView(R.id.tv_asset_purchased);
            if (AssetUtils.assetPurchasedVisible(tvAssetPurchased, mAssetSubType, item.isAuthorized())) {
                tvAssetPurchased.setVisibility(View.VISIBLE);
            } else {
                if (tvAssetPurchased != null) {
                    tvAssetPurchased.setVisibility(View.GONE);
                }
            }
            int paddingNo = (int) mContext.getResources().getDimension(R.dimen.dp_px_36);
            //更改封面大小
            //Change cover size
            if (mContext.getResources().getString(R.string.no).equals(item.getName())) {
                if (tvAssetPurchased != null) {
                    tvAssetPurchased.setVisibility(View.GONE);
                }
                ivCover.setPadding(paddingNo, paddingNo, paddingNo, paddingNo);
            } else if (ivCover.getPaddingLeft() == paddingNo) {
                int otherPadding = (int) mContext.getResources().getDimension(R.dimen.dp_px_2);
                ivCover.setPadding(otherPadding, otherPadding, otherPadding, otherPadding);
            }
            String imageUrl = item.getCoverPath();
            if (!TextUtils.isEmpty(imageUrl)) {
                if (imageUrl.startsWith("file")) {
                    imageUrl = imageUrl.replace("file:/", "asset:");
                }
            }
            DraweeController controller = Fresco.newDraweeControllerBuilder()
                    .setUri(imageUrl)
                    .setAutoPlayAnimations(true)
                    .setOldController(ivCover.getController())
                    .build();
            ivCover.setController(controller);
            if (helper.getAdapterPosition() == mSelectedPosition) {
                ivCover.setBackground(CommonUtils.getRadiusDrawable(2, mContext.getResources().getColor(R.color.color_fffc2b55), 10, -1));
            } else {
                ivCover.setBackground(CommonUtils.getRadiusDrawable(10, mContext.getResources().getColor(R.color.color_ff242424)));
            }

            ImageView ivDownload = helper.getView(R.id.iv_downloading);
            if (!item.isHadDownloaded() || item.needUpdate()) {
                ImageLoader.loadUrl(mContext, R.mipmap.downloading, ivDownload);
                int progress = item.getDownloadProgress();
                if (progress >= 0 && progress < 100) {
                    ivDownload.setVisibility(View.VISIBLE);
                    ivCover.setVisibility(View.GONE);
                } else {
                    ivDownload.setVisibility(View.GONE);
                    ivCover.setVisibility(View.VISIBLE);
                }
            } else {
                ivDownload.setVisibility(View.GONE);
                ivCover.setVisibility(View.VISIBLE);
            }
        }
    }
}
