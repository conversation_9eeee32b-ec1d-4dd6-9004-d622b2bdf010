package com.meishe.myvideo.fragment.presenter;

import com.meishe.base.model.Presenter;
import com.meishe.base.utils.ToastUtils;
import com.meishe.engine.EditorEngine;
import com.meishe.engine.bean.MeicamFxParam;
import com.meishe.engine.bean.MeicamVideoClip;
import com.meishe.engine.bean.MeicamVideoFx;
import com.meishe.engine.bean.MeicamVideoTrack;
import com.meishe.engine.command.VideoClipCommand;
import com.meishe.engine.constant.NvsConstants;
import com.meishe.myvideo.R;
import com.meishe.myvideo.fragment.iview.BeautyView;

import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> LiHang<PERSON>hou
 * @CreateDate :2021/1/8 17:38
 * @Description :美颜、美型逻辑处理类 The beauty presenter
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class BeautyPresenter extends Presenter<BeautyView> {
    public static final double INVALID_VALUE = -1000;
    private MeicamVideoClip mVideoClip;

    /**
     * 初始化视频特效
     * Init video fx
     *
     * @param meicamVideoClip the video clip
     */
    public void initVideoFx(MeicamVideoClip meicamVideoClip) {
        mVideoClip = meicamVideoClip;
        //美型编辑的时候，默认打开美型flag开关，需要在确认的时候再次检查flag
        //When editing beauty and shape, the beauty and shape flag switch is turned on by default.
        //You need to check the flag again when you confirm.
        EditorEngine.getInstance().setUseBeautyFaceShape(true);
    }

    /**
     * 应用美颜视频特效
     * Apply the beauty video fx
     *
     * @param fxName The video fx name
     * @param value  The video fx strength
     */
    public void applyBeautyVideoFx(String fxName, double value) {
        if (mVideoClip != null) {
            if (VideoClipCommand.applyBeautyVideoFx(mVideoClip, fxName, (float) value)) {
                EditorEngine.getInstance().seekTimeline();
            }
        }
    }

    /**
     * 应用美型视频特效
     * Apply the video fx
     *
     * @param faceID 美型ID
     * @param packageID 包ID
     * @param degreeName 调节名称
     * @param value  The video fx strength
     */
    public void applyShapeVideoFx(String faceID,String packageID,String degreeName, double value) {
        if (mVideoClip != null) {
            if ( VideoClipCommand.applyShapeVideoFx(mVideoClip, faceID,packageID,degreeName, (float) value)) {
                EditorEngine.getInstance().seekTimeline();
            }
        }
    }

    /**
     * 打开美型特效
     * Open the shape video fx
     */
    public void openShapeVideoFx() {
        if (mVideoClip != null && VideoClipCommand.openShapeFx(mVideoClip)) {
            EditorEngine.getInstance().seekTimeline();
        }
    }

    /**
     * 关闭特效
     * Close the shape video fx
     */
    public void closeShapeVideoFx() {
        if (mVideoClip != null && VideoClipCommand.closeShapeFx(mVideoClip)) {
            EditorEngine.getInstance().seekTimeline();
        }
    }

    /**
     * 重置视频美肤特效
     * Reset the video fx
     */
    public void resetBeautyFx() {
        if (mVideoClip != null) {
            VideoClipCommand.resetBeautyOrShapeFx(mVideoClip, false);
            EditorEngine.getInstance().seekTimeline();
        }
    }

    /**
     * 重置视频美型特效
     * Reset the video fx
     */
    public void resetShapeFx() {
        if (mVideoClip != null) {
            VideoClipCommand.resetBeautyOrShapeFx(mVideoClip, true);
            EditorEngine.getInstance().seekTimeline();
        }
    }

    /**
     * 检测美肤类型特效的状态
     * Check beauty skin status
     *
     * @return true is default ,false is not
     */
    public boolean checkBeautySkinStatus() {
        return mVideoClip != null && mVideoClip.isDefaultBeauty();
    }

    /**
     * 检测美型类型特效的状态
     * Check beauty shape status
     *
     * @return true is default ,false is not
     */
    public boolean checkBeautyShapeStatus() {
        return mVideoClip != null && mVideoClip.isDefaultBeautyShape();
    }

    /**
     * 检测美型特效的状态
     * Check shape status
     *
     * @return true is default ,false is not
     */
    public boolean checkShapeStatus() {
        return mVideoClip != null && mVideoClip.hasBeautyShape();
    }

    /**
     * 获取视频特效的强度
     * Get the video fx strength
     *
     * @param fxName the video fx name
     */
    public double getVideoFxStrength(String fxName) {
        if (mVideoClip != null) {
            Float fxValue = mVideoClip.getBeautyOrShapeFxValue(fxName);
            if (fxValue != null) {
                return fxValue;
            }
        }
        return INVALID_VALUE;
    }

    /**
     * 应用到所有片段
     * Apply to all clip
     *
     * @param isShape true is beauty shape ,false is beauty skin
     */
    public void applyAllClip(boolean isShape) {
        if (mVideoClip == null) {
            return;
        }
        MeicamVideoFx arSceneFxEx = mVideoClip.getArSceneFxEx();
        if (arSceneFxEx == null) {
            return;
        }
        List<String[]> fxParams;
        if (isShape) {
            fxParams = NvsConstants.getShapeParam();
        } else {
            fxParams = NvsConstants.getBeautyParam();
        }
        int count = EditorEngine.getInstance().getVideoTrackCount();
        for (int i = 0; i < count; i++) {
            MeicamVideoTrack videoTrack = EditorEngine.getInstance().getVideoTrack(i);
            if (videoTrack != null) {
                for (int j = 0; j < videoTrack.getClipCount(); j++) {
                    MeicamVideoClip videoClip = videoTrack.getVideoClip(j);
                    if (videoClip == this.mVideoClip) {
                        continue;
                    }
                    MeicamVideoFx newEffect = videoClip.getArSceneFxEx();
                    for (String[] fxParam : fxParams) {
                        String key = fxParam[1];
                        if (MeicamFxParam.TYPE_STRING.equals(fxParam[0])) {
                            newEffect.setStringVal(key,arSceneFxEx.getStringVal(key));
                        } else if (MeicamFxParam.TYPE_FLOAT.equals(fxParam[0])){
                            float floatVal = arSceneFxEx.getFloatVal(key);
                            if (floatVal != INVALID_VALUE) {
                                newEffect.setFloatVal(key, floatVal);
                            }
                        } else if (MeicamFxParam.TYPE_BOOLEAN.equals(fxParam[0])) {
                            newEffect.setBooleanVal(key, arSceneFxEx.getBooleanVal(key));
                        }
                    }
                }
            }
        }
        ToastUtils.showShort(R.string.has_been_apply_to_all);
    }
}
