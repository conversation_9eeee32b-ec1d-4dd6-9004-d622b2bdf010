package com.meishe.myvideo.fragment;

import android.os.Bundle;

import com.meishe.business.assets.fragment.FlowFragment;
import com.meishe.business.assets.iview.AssetsView;
import com.meishe.engine.asset.bean.AssetInfo;
import com.meishe.engine.bean.MeicamCaptionClip;
import com.meishe.logic.constant.PagerConstants;
import com.meishe.myvideo.R;
import com.meishe.myvideo.fragment.presenter.CaptionBubbleFlowerPresenter;

import static com.meishe.engine.asset.bean.AssetInfo.ASSET_CUSTOM_CAPTION_BUBBLE;


/**
 * 目前用于花字、气泡
 * Flower caption 、Bubble fragment
 */
public class CaptionBubbleFlowerFragment extends FlowFragment<CaptionBubbleFlowerPresenter> implements AssetsView {


    private MeicamCaptionClip mCaptionClip;

    public CaptionBubbleFlowerFragment() {
        needDisplayName = false;
        spanCount = 4;
    }

    public static CaptionBubbleFlowerFragment create(int assetType, MeicamCaptionClip captionClip) {
        CaptionBubbleFlowerFragment flowerFragment = new CaptionBubbleFlowerFragment();
        flowerFragment.updateCaptionClip(captionClip);
        Bundle bundle = new Bundle();
        bundle.putInt(PagerConstants.BUNDLE_KEY_ASSET_TYPE, assetType);
        flowerFragment.setArguments(bundle);
        return flowerFragment;
    }

    @Override
    protected void initData() {
        super.initData();
        mPresenter.initData(mAssetType, mCaptionClip);
    }


    public void setSelected() {
        String uuid = mPresenter.getCurrentPackageId();
        if (mAdapter != null) {
            mAdapter.selected(uuid);
        }
    }

    /**
     * 更新字幕片段
     * Update caption clip
     *
     * @param captionClip the new caption clip
     */
    public void updateCaptionClip(MeicamCaptionClip captionClip) {
        mCaptionClip = captionClip;
        if (mPresenter != null) {
            mPresenter.updateCaptionClip(captionClip);
        }
        if (isInitView) {
            setSelected();
        }
    }


    @Override
    protected int getItemLayoutResId() {
        return R.layout.item_flower_bubble;
    }

    @Override
    protected void onAdapterItemClick(int position) {
        AssetInfo info = mAdapter.getItem(position);
        if (info != null) {
            mPresenter.applyBubbleOrFlower(info);
        }
    }

    @Override
    protected String getAssetId() {
        String uuid = null;
        if (mCaptionClip != null) {
            if (mAssetType == ASSET_CUSTOM_CAPTION_BUBBLE) {
                uuid = mCaptionClip.getBubbleUuid();
            } else {
                uuid = mCaptionClip.getRichWordUuid();
            }
        }
        return uuid;
    }
}
