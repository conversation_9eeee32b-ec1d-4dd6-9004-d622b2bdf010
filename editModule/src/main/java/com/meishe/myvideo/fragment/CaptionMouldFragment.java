package com.meishe.myvideo.fragment;

import android.view.View;
import android.widget.ImageView;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentPagerAdapter;

import com.meishe.base.adapter.CommonFragmentAdapter;
import com.meishe.base.model.BaseFragment;
import com.meishe.base.view.CustomViewPager;
import com.meishe.engine.asset.AssetsManager;
import com.meishe.engine.asset.bean.AssetInfo;
import com.meishe.engine.asset.bean.AssetsConstants;
import com.meishe.engine.asset.bean.RequestParam;
import com.meishe.myvideo.R;
import com.meishe.myvideo.interfaces.BottomEventListener;
import com.meishe.myvideo.util.ConfigUtil;
import com.meishe.third.tablayout.SlidingTabLayout;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * The type Caption mould Fragment
 * 此类的类型为 字幕模板
 *
 * <AUTHOR>
 * @Description 字幕模板
 */
public class CaptionMouldFragment extends BaseFragment {
    private BottomEventListener mEventListener;
    private ImageView mIvConfirm;
    private List<Fragment> mCaptionMouldFragmentList;
    private SlidingTabLayout mTabLayout;
    private CustomViewPager mViewPager;

    public CaptionMouldFragment() {
    }

    public static CaptionMouldFragment getInstance() {
        return new CaptionMouldFragment();
    }

    public void setEventListener(BottomEventListener mEventListener) {
        this.mEventListener = mEventListener;
    }


    @Override
    protected int bindLayout() {
        return R.layout.view_caption_mould;
    }

    @Override
    protected void onLazyLoad() {

    }

    @Override
    protected void initView(View rootView) {
        mIvConfirm = rootView.findViewById(R.id.iv_confirm);
        mTabLayout = rootView.findViewById(R.id.tab_layout);
        mViewPager = rootView.findViewById(R.id.viewPager);
        initFragment();
        initListener();
    }

    private void initFragment() {
        if (mCaptionMouldFragmentList != null) {
            mCaptionMouldFragmentList.clear();
        } else {
            mCaptionMouldFragmentList = new ArrayList<>();
        }
        boolean newAssets = ConfigUtil.isNewAssets();
        int assetsType = !newAssets ? AssetInfo.ASSET_COMPOUND_CAPTION : AssetsConstants.AssetsTypeData.CAPTION_COMP.type;
        int category = !newAssets ? 1 : AssetsConstants.AssetsTypeData.CAPTION_COMP.category;
        int kind = !newAssets ? -1 : AssetsConstants.AssetsTypeData.CAPTION_COMP.kind;
        CompoundCaptionFragment compoundCaptionFragment = CompoundCaptionFragment.getInstance(new RequestParam(assetsType, -1, category, kind));
        compoundCaptionFragment.setEventListener(mEventListener);
        mCaptionMouldFragmentList.add(compoundCaptionFragment);
        mCaptionMouldFragmentList.add(CaptionFragment.create(AssetInfo.ASSET_CAPTION_STYLE));
        setAdapter();
    }

    private void setAdapter() {
        final String[] tabs = getResources().getStringArray(R.array.menu_tab_sub_caption_mould);
        FragmentPagerAdapter adapter = new CommonFragmentAdapter(getChildFragmentManager(), mCaptionMouldFragmentList, Arrays.asList(tabs));
        mViewPager.setAdapter(adapter);
        mTabLayout.setViewPager(mViewPager);
    }

    @Override
    protected void initData() {

    }

    private void initListener() {
        mIvConfirm.setOnClickListener(v -> {
            if (mEventListener != null) {
                mEventListener.onDismiss(true);
            }
        });
    }


    @Override
    public void onDestroy() {
        super.onDestroy();
        AssetsManager.get().clearCache();
    }
}
