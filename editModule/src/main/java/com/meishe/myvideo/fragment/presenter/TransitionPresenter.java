package com.meishe.myvideo.fragment.presenter;

import com.meishe.base.utils.Utils;
import com.meishe.business.assets.presenter.AssetsPresenter;
import com.meishe.engine.EditorEngine;
import com.meishe.engine.asset.bean.AssetInfo;
import com.meishe.engine.asset.bean.RequestParam;
import com.meishe.engine.bean.BaseInfo;
import com.meishe.engine.bean.MeicamTransition;
import com.meishe.engine.command.TransitionCommand;
import com.meishe.engine.interf.IBaseInfo;
import com.meishe.myvideo.R;
import com.meishe.myvideo.fragment.iview.TransitionView;
import com.meishe.myvideo.manager.MenuDataManager;

import java.util.ArrayList;
import java.util.List;

import static com.meishe.base.constants.Constants.TRACK_INDEX_MAIN;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2021/8/4 17:38
 * @Description :转场逻辑处理类 The transition presenter
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class TransitionPresenter extends AssetsPresenter<TransitionView> {

    public static final int TRANS_TYPE_3D = 1;
    public static final int TRANS_TYPE_EFFECT = 2;

    @Override
    protected boolean interceptBefore(RequestParam requestParam, int pageNumber, boolean needUpdate) {
        int type = requestParam.categoryId;
        if (type != TRANS_TYPE_3D && type != TRANS_TYPE_EFFECT) {
            List<AssetInfo> list = new ArrayList<>();
            if (pageNumber == 0) {
                AssetInfo effectInfo = new AssetInfo();
                effectInfo.setType(AssetInfo.ASSET_VIDEO_TRANSITION);
                effectInfo.setName(Utils.getApp().getString(R.string.no));
                effectInfo.setCoverId(R.mipmap.ic_no);
                effectInfo.setHadDownloaded(true);
                effectInfo.setEffectMode(BaseInfo.EFFECT_MODE_BUILTIN);
                list.add(effectInfo);
            }
            list.addAll(MenuDataManager.getTransition(Utils.getApp().getApplicationContext()));
            getView().onNewDataBack(list, requestParam.subType, needUpdate);
            return true;
        }
        return false;
    }

    @Override
    protected List<AssetInfo> handleDataInFirstPage(List<AssetInfo> list) {
        AssetInfo effectInfo = new AssetInfo();
        effectInfo.setType(AssetInfo.ASSET_VIDEO_TRANSITION);
        effectInfo.setName(Utils.getApp().getString(R.string.no));
        effectInfo.setCoverId(R.mipmap.ic_no);
        effectInfo.setHadDownloaded(true);
        effectInfo.setEffectMode(BaseInfo.EFFECT_MODE_BUILTIN);
        list.add(0, effectInfo);
        return list;
    }

    public void applyTransition(IBaseInfo baseInfo, int transitionIndex, long duration) {
        EditorEngine.getInstance().applyTransition(baseInfo, transitionIndex, duration);
    }

    public MeicamTransition getTransition(int index) {
        return EditorEngine.getInstance().getTransition(TRACK_INDEX_MAIN, index);
    }

    public void updateTransitionDuration(MeicamTransition transition, long duration) {
        if (transition != null) {
            TransitionCommand.setParam(transition, TransitionCommand.PARAM_DURATION, duration);
            EditorEngine.getInstance().playTransition(TRACK_INDEX_MAIN, transition.getIndex());
        }
    }

    public boolean applyTransitionToAll(int index) {
        MeicamTransition meicamTransition = EditorEngine.getInstance().getTransition(TRACK_INDEX_MAIN, index);
        if (meicamTransition != null) {
            return EditorEngine.getInstance().applyTransitionToVideoTrack(meicamTransition, TRACK_INDEX_MAIN);
        }
        return false;
    }
}
