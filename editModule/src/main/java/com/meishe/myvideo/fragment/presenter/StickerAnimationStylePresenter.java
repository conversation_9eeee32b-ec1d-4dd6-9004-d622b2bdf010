package com.meishe.myvideo.fragment.presenter;

import com.meishe.base.utils.ResourceUtils;
import com.meishe.base.utils.StringUtils;
import com.meishe.business.assets.iview.AssetsView;
import com.meishe.business.assets.presenter.AssetsPresenter;
import com.meishe.engine.asset.bean.AssetInfo;
import com.meishe.engine.bean.BaseInfo;
import com.meishe.myvideo.R;

import java.util.List;

import static com.meishe.engine.asset.bean.AssetInfo.ASSET_CUSTOM_CAPTION_ANIMATION_IN;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2021/10/08 11:40
 * @Description :贴纸动画Style presenter The sticker animation style presenter.
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class StickerAnimationStylePresenter extends AssetsPresenter<AssetsView> {
    @Override
    protected List<AssetInfo> handleDataInFirstPage(List<AssetInfo> list) {
        AssetInfo nullInfo = new AssetInfo();
        nullInfo.setType(ASSET_CUSTOM_CAPTION_ANIMATION_IN);
        nullInfo.setName(StringUtils.getString(R.string.no));
        nullInfo.setEffectMode(BaseInfo.EFFECT_MODE_PACKAGE);
        nullInfo.setCoverPath(ResourceUtils.getMipmapToUri(R.mipmap.icon_none));
        nullInfo.setHadDownloaded(true);
        list.add(0, nullInfo);
        return list;
    }
}
