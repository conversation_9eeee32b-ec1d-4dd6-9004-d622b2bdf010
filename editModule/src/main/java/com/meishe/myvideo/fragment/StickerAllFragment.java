package com.meishe.myvideo.fragment;

import android.os.Bundle;

import com.meishe.base.utils.SizeUtils;
import com.meishe.business.assets.fragment.FlowFragment;
import com.meishe.business.assets.iview.AssetsView;
import com.meishe.engine.asset.bean.AssetInfo;
import com.meishe.engine.asset.bean.RequestParam;
import com.meishe.logic.constant.PagerConstants;
import com.meishe.myvideo.R;
import com.meishe.myvideo.fragment.presenter.StickerAllPresenter;
import com.meishe.myvideo.interfaces.OnAssetsClickedListener;

/**
 * The type Sticker all fragment.
 * 此类的类型为 贴纸
 *
 * <AUTHOR>
 * @Description 贴纸
 */
public class StickerAllFragment extends FlowFragment<StickerAllPresenter> implements AssetsView {

    private OnAssetsClickedListener mOnItemClickedListener;
    private String mPackageId;

    public StickerAllFragment() {
        needSelected = true;
        spanCount = 4;
        leftItemDecoration = SizeUtils.dp2px(7);
    }

    @Override
    protected StickerAllPresenter createPresenter() {
        return new StickerAllPresenter();
    }

    public StickerAllFragment setOnItemClickedListener(OnAssetsClickedListener listener) {
        this.mOnItemClickedListener = listener;
        return this;
    }

    public static StickerAllFragment create(RequestParam param, OnAssetsClickedListener listener) {
        StickerAllFragment fragment = new StickerAllFragment().setOnItemClickedListener(listener);
        Bundle bundle = new Bundle();
        bundle.putInt(PagerConstants.BUNDLE_KEY_ASSET_TYPE, AssetInfo.ASSET_ANIMATED_STICKER);
        if (param != null) {
            bundle.putInt(PagerConstants.BUNDLE_KEY_ASSET_TYPE_NEW, param.type);
            bundle.putInt(PagerConstants.BUNDLE_KEY_ASSET_TYPE_CATEGORY, param.categoryId);
            bundle.putInt(PagerConstants.BUNDLE_KEY_ASSET_TYPE_KIND, param.kind);
        }
        fragment.setArguments(bundle);
        return fragment;
    }


    @Override
    protected int getItemLayoutResId() {
        return R.layout.item_sticker_all;
    }

    @Override
    protected void onAdapterItemClick(int position) {
        if (mOnItemClickedListener != null) {
            AssetInfo item = mAdapter.getItem(position);
            mOnItemClickedListener.onItemClicked(item);
        }
    }

    public void updateSelectId(String packageId){
        mPackageId = packageId;
        mAdapter.selected(packageId);
    }

    @Override
    protected String getAssetId() {
        return mPackageId;
    }
}
