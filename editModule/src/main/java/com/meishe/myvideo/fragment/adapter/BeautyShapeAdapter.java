package com.meishe.myvideo.fragment.adapter;

import com.meishe.base.utils.CommonUtils;
import com.meishe.engine.interf.IBaseInfo;
import com.meishe.myvideo.R;
import com.meishe.third.adpater.BaseQuickAdapter;
import com.meishe.third.adpater.BaseViewHolder;


import androidx.annotation.NonNull;

/**
 * author：yangtailin on 2020/8/31 18:35
 * 美颜适配器
 * Beauty adapter
 */
public class BeautyShapeAdapter extends BaseQuickAdapter<IBaseInfo, BaseViewHolder> {
    private int mSelectedPosition = -1;

    public BeautyShapeAdapter() {
        super(R.layout.item_beauty_shape);
    }


    /**
     * 选中某一项
     * Selected item .
     *
     * @param position The index of list
     */
    public void selected(int position) {
        if (mSelectedPosition >= 0) {
            notifyItemChanged(mSelectedPosition);
        }
        mSelectedPosition = position;
        if (position >= 0 && position < getData().size()) {
            notifyItemChanged(position);
        }
    }

    @Override
    protected void convert(@NonNull BaseViewHolder helper, IBaseInfo item) {
        helper.setText(R.id.tv_name, item.getName());
        helper.setImageResource(R.id.iv_cover, item.getCoverId());
        if (helper.getAdapterPosition() == mSelectedPosition) {
            helper.itemView.setBackground(CommonUtils.getRadiusDrawable(12, mContext.getResources().getColor(R.color.color_80fc2b55)));
        } else {
            helper.itemView.setBackground(CommonUtils.getRadiusDrawable(12, mContext.getResources().getColor(R.color.color_ff242424)));
        }
    }
}
