package com.meishe.myvideo.fragment;

import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.facebook.drawee.backends.pipeline.Fresco;
import com.facebook.drawee.interfaces.DraweeController;
import com.facebook.drawee.view.SimpleDraweeView;
import com.meishe.base.manager.LinearLayoutManagerWrapper;
import com.meishe.base.model.BaseMvpFragment;
import com.meishe.base.utils.SizeUtils;
import com.meishe.base.view.decoration.ItemDecoration;
import com.meishe.engine.bean.Plug;
import com.meishe.myvideo.R;
import com.meishe.myvideo.activity.iview.PlugsView;
import com.meishe.myvideo.activity.presenter.PlugsPresenter;
import com.meishe.third.adpater.BaseQuickAdapter;
import com.meishe.third.adpater.BaseViewHolder;

import java.util.List;

/**
 * All rights Reserved, Designed By www.meishesdk.com
 *
 * @Author: Chu<PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2022/6/17 15:57
 * @Description: 特效插件Fragment The plugs fragment
 * @Copyright: www.meishesdk.com Inc. All rights reserved.
 */
public class PlugsFragment extends BaseMvpFragment<PlugsPresenter> implements PlugsView {

    private List<Plug> mPlugList;
    private EventListener mEventListener;

    public PlugsFragment() {
    }

    public static PlugsFragment create(List<Plug> plugList, EventListener eventListener) {
        PlugsFragment fragment = new PlugsFragment();
        fragment.setEventListener(eventListener);
        fragment.mPlugList = plugList;
        return fragment;
    }

    public void setEventListener(EventListener mEventListener) {
        this.mEventListener = mEventListener;
    }

    public interface EventListener {
        /**
         * 点击事件回调
         * On item click.
         *
         * @param plug the plug
         */
        void onItemClick(Plug plug);
    }

    @Override
    protected int bindLayout() {
        return R.layout.fragment_plugs;
    }

    @Override
    protected void onLazyLoad() {

    }

    @Override
    protected void initView(View rootView) {
        RecyclerView mRecyclerView = rootView.findViewById(R.id.recyclerView);
        LinearLayoutManager layoutManager = new LinearLayoutManagerWrapper(getContext(), LinearLayoutManager.VERTICAL, false);
        mRecyclerView.setLayoutManager(layoutManager);
        PlusAdapter mAdapter = new PlusAdapter(R.layout.item_plug_frament, mPlugList);
        mRecyclerView.setAdapter(mAdapter);
        mRecyclerView.addItemDecoration(new ItemDecoration(0, SizeUtils.dp2px(5), 0, SizeUtils.dp2px(5)));
        mAdapter.setOnItemClickListener((adapter, view, position) -> {
            Plug plug = mPlugList.get(position);
            if (mEventListener != null) {
                mEventListener.onItemClick(plug);
            }
        });
    }

    @Override
    protected void initData() {
        for (Plug plug : mPlugList) {
            plug.coverPath = plug.coverPath.replace("png", "webp").replace("file:/", "asset:");
        }
    }

    private static class PlusAdapter extends BaseQuickAdapter<Plug, BaseViewHolder> {

        public PlusAdapter(int layoutResId, @Nullable List<Plug> data) {
            super(layoutResId, data);
        }

        @Override
        protected void convert(@NonNull BaseViewHolder helper, Plug item) {
            helper.setText(R.id.title, item.getName());
            helper.setText(R.id.content, item.getPlugDesc());
            SimpleDraweeView imageView = helper.getView(R.id.image);
            DraweeController controller = Fresco.newDraweeControllerBuilder()
                    .setUri(item.coverPath)
                    .setAutoPlayAnimations(true)
                    .setOldController(imageView.getController())
                    .build();
            imageView.setController(controller);

        }
    }
}
