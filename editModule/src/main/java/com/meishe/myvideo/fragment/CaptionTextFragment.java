package com.meishe.myvideo.fragment;

import android.view.View;
import android.widget.SeekBar;

import com.meishe.base.model.BaseMvpFragment;
import com.meishe.engine.bean.MeicamCaptionClip;
import com.meishe.myvideo.R;
import com.meishe.myvideo.bean.ColorInfo;
import com.meishe.myvideo.fragment.iview.CaptionStyleView;
import com.meishe.myvideo.fragment.presenter.CaptionStylePresenter;
import com.meishe.myvideo.view.MYMultiColorView;
import com.meishe.myvideo.view.MYSeekBarTextView;

/**
 * The type caption text fragment.
 * 字幕颜色fragment
 */
public class CaptionTextFragment extends BaseMvpFragment<CaptionStylePresenter> implements CaptionStyleView {

    private MYMultiColorView mMultiColorView;
    private MYSeekBarTextView mSbOpacity;

    public CaptionTextFragment() {
        mPresenter = new CaptionStylePresenter(null);
    }

    public static CaptionTextFragment create(<PERSON><PERSON><PERSON><PERSON><PERSON>Clip captionClip) {
        CaptionTextFragment fragment = new CaptionTextFragment();
        fragment.updateCaptionClip(captionClip);
        return fragment;
    }


    /**
     * 这里重写，不使用模板自动生成的Presenter了.
     * Rewrite here, do not use the Presenter generated automatically by the template
     *
     * @return The presenter
     */
    @Override
    protected CaptionStylePresenter createPresenter() {
        return mPresenter;
    }

    @Override
    protected int bindLayout() {
        return R.layout.fragment_caption_style_text;
    }

    @Override
    protected void onLazyLoad() {

    }

    @Override
    protected void initView(View rootView) {
        mMultiColorView = rootView.findViewById(R.id.multi_color_view);
        mSbOpacity = rootView.findViewById(R.id.sb_opacity);
        initListener();
    }

    private void initListener() {
        mMultiColorView.setColorClickListener(new MYMultiColorView.OnColorClickListener() {
            @Override
            public void onClick(ColorInfo colorInfo) {
                mPresenter.setCaptionColor(colorInfo.getCommonInfo());
                mPresenter.setCaptionOpacity((int) mSbOpacity.getProgress() / 100f);
            }
        });
        mSbOpacity.setOnSeekBarChangeListener(new MYSeekBarTextView.OnSeekBarListener() {
            @Override
            public void onStopTrackingTouch(int progress, String name) {

            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {

            }

            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                if (fromUser) {
                    mPresenter.setCaptionOpacity(progress / 100f);
                }
            }
        });
    }

    @Override
    protected void initData() {
        mMultiColorView.selected(mPresenter.getCaptionTextColor(true));
        mSbOpacity.setProgress((int) (mPresenter.getCaptionOpacity() * 100));
    }

    /**
     * 更新字幕片段
     * Update caption clip
     *
     * @param captionClip the new caption clip
     */
    public void updateCaptionClip(MeicamCaptionClip captionClip) {
        mPresenter.updateCaptionClip(captionClip);
        if (isInitView) {
            mSbOpacity.setProgress((int) (mPresenter.getCaptionOpacity() * 100));
            mMultiColorView.selected(mPresenter.getCaptionTextColor(true));
        }
    }

}
