package com.meishe.myvideo.fragment.adapter;

import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import androidx.annotation.NonNull;

import com.meishe.base.utils.ImageLoader;
import com.meishe.draft.data.DraftData;
import com.meishe.myvideo.R;
import com.meishe.third.adpater.BaseQuickAdapter;
import com.meishe.third.adpater.BaseViewHolder;

import java.util.ArrayList;
import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> LiHangZhou
 * @CreateDate :2020/12/3 13:35
 * @Description :剪辑草稿列表适配器 The Adapter of the editing page's list
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class EditingDraftAdapter extends BaseQuickAdapter<DraftData, BaseViewHolder> {
    private boolean mEditEnable;

    public EditingDraftAdapter() {
        super(R.layout.item_editing_draft);
    }

    @NonNull
    @Override
    public BaseViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        BaseViewHolder holder = super.onCreateViewHolder(parent, viewType);
        holder.addOnClickListener(R.id.tv_upload);
        holder.addOnClickListener(R.id.iv_draft_manager);
        holder.addOnClickListener(R.id.iv_select_state);
        return holder;
    }

    /**
     * 设置编辑状态是否可用
     * Sets whether editing is available
     *
     * @param enable boolean true is available ,false is unavailable.
     */
    public void setEditEnable(boolean enable) {
        mEditEnable = enable;
        for (DraftData data : getData()) {
            data.setTag(false);
        }
        notifyDataSetChanged();
    }

    /**
     * 处理编辑状态的选中。如果已经被选中，再次调用本方法则取消选中。
     * Handles the selection of edit status.If already selected, call this method again to deselect
     *
     * @param position int the index in collection.
     */
    public void dealSelected(int position) {
        if (editEnable() && position >= 0 && position < getData().size()) {
            DraftData data = getData().get(position);
            data.setTag(data.getTag() == null || !(boolean) data.getTag());
            notifyItemChanged(position);
        }
    }

    /**
     * 删除草稿
     * delete draft
     *
     * @param position int the index of list
     */
    public void deleteDraft(int position) {
        if (position >= 0 && position < getData().size()) {
            remove(position);
        }
    }

    /**
     * 编辑状态是否可用
     * whether editing is available
     *
     * @return true is available ,false is unavailable.
     */
    public boolean editEnable() {
        return mEditEnable;
    }

    /**
     * 获取编辑状态下，选中的item 集合
     * Gets the items selected in the edit state
     */
    public List<DraftData> getSelectedList() {
        List<DraftData> selectedList = new ArrayList<>();
        for (DraftData data : getData()) {
            if (data.getTag() != null && (boolean) data.getTag()) {
                selectedList.add(data);
            }
        }
        return selectedList;
    }

    @Override
    protected void convert(@NonNull BaseViewHolder helper, DraftData item) {
        ImageLoader.loadUrl(mContext, item.getCoverPath(), helper.getView(R.id.iv_cover));
        helper.setText(R.id.tv_draft_name, item.getFileName());
        String updateTime = mContext.getString(R.string.draft_update_time) + item.getLastModifyTime();
        helper.setText(R.id.tv_update_time, updateTime);
        helper.setText(R.id.tv_draft_size, item.getFileSize());
        helper.setText(R.id.tv_draft_duration, item.getDuration());
        helper.getView(R.id.fl_cloud).setVisibility(item.isCloud() && (!item.isModified()) ? View.VISIBLE:View.GONE);

        // 显示草稿类型标识
        if (item.isTemplate()) {
            // 如果是模板类型，可以在这里添加模板标识的显示逻辑
            // 例如：显示模板图标或者改变背景色等
        }
        ImageView ivDraftManager = helper.getView(R.id.iv_draft_manager);
        ImageView ivSelected = helper.getView(R.id.iv_select_state);
        if (mEditEnable) {
            if (ivDraftManager.getVisibility() == View.VISIBLE) {
                ivDraftManager.setVisibility(View.INVISIBLE);
                ivSelected.setVisibility(View.VISIBLE);
                helper.itemView.setBackgroundColor(mContext.getResources().getColor(R.color.colorTranslucent));
            }
        } else {
            if (ivSelected.getVisibility() == View.VISIBLE) {
                ivDraftManager.setVisibility(View.VISIBLE);
                ivSelected.setVisibility(View.INVISIBLE);
                helper.itemView.setBackgroundColor(mContext.getResources().getColor(R.color.colorTranslucent));
            }
        }
        if (ivSelected.getVisibility() == View.VISIBLE) {
            if (item.getTag() != null && (Boolean) item.getTag()) {
                ivSelected.setBackgroundResource(R.mipmap.home_ck_selected);
                helper.itemView.setBackgroundColor(mContext.getResources().getColor(R.color.draft_clicked_bg));
            } else {
                ivSelected.setBackgroundResource(R.mipmap.home_ck_unselected);
                helper.itemView.setBackgroundColor(mContext.getResources().getColor(R.color.colorTranslucent));
            }
        }
    }
}
