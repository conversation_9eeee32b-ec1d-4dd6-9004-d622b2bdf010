package com.meishe.myvideo.fragment;

import android.app.Activity;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentTransaction;
import androidx.recyclerview.widget.RecyclerView;

import com.meishe.base.manager.AppManager;
import com.meishe.base.manager.LinearLayoutManagerWrapper;
import com.meishe.base.model.BaseMvpFragment;
import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.LogUtils;
import com.meishe.base.utils.NetUtils;
import com.meishe.base.utils.PermissionConstants;
import com.meishe.base.utils.PermissionUtils;
import com.meishe.base.utils.ToastUtils;
import com.meishe.base.utils.Utils;
import com.meishe.draft.DraftManager;
import com.meishe.draft.DraftTimelineHelper;
import com.meishe.draft.data.DraftData;
import com.meishe.draft.observer.DraftObserver;
import com.meishe.engine.EditorEngine;
import com.meishe.engine.bean.MeicamAudioClip;
import com.meishe.engine.bean.MeicamAudioTrack;
import com.meishe.engine.bean.MeicamTimeline;
import com.meishe.engine.bean.MeicamVideoClip;
import com.meishe.engine.bean.MeicamVideoTrack;
import com.meishe.libplugin.PluginManager;
import com.meishe.libplugin.user.IUserPlugin;
import com.meishe.myvideo.R;
import com.meishe.myvideo.activity.DraftEditActivity;
import com.meishe.myvideo.activity.MaterialSelectActivity;
import com.meishe.myvideo.event.MessageEvent;
import com.meishe.myvideo.fragment.adapter.EditingDraftAdapter;
import com.meishe.myvideo.fragment.iview.DraftView;
import com.meishe.myvideo.fragment.presenter.DraftPresenter;
import com.meishe.myvideo.view.pop.DeleteDraftPop;
import com.meishe.myvideo.view.pop.ManageDraftPop;
import com.meishe.myvideo.view.pop.RenameDraftPop;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.lang.reflect.InvocationTargetException;
import java.util.List;
import java.util.UUID;

import static com.meishe.base.utils.PermissionConstants.LOCATION;
import static com.meishe.base.utils.PermissionConstants.STORAGE;
import static com.meishe.logic.constant.PagerConstants.FROM_MAIN_PAGE;
import static com.meishe.logic.constant.PagerConstants.FROM_PAGE;
import static com.meishe.myvideo.event.MessageEvent.MESSAGE_REQUEST_PERMISSION;
import static com.meishe.myvideo.fragment.EditingFragment.MESSAGE_UPDATE_DRAFT_MANAGER;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/3/8 17:15
 * @Description :草稿fragment The draft fragment
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class DraftFragment extends BaseMvpFragment<DraftPresenter> implements DraftView {
    private EditingDraftAdapter mEditingDraftAdapter;
    private int mCurrentClickedPosition;
    private ManageDraftPop mManageDraftPop;
    private RenameDraftPop renameDraftPop;
    private boolean isEditState;
    private BottomDeleteFragment mBottomDeleteView;
    private DraftObserver mDraftObserver;
    private RecyclerView mDraftListView;
    private View mNoDraftHint;

    public static Fragment create() {
        return new DraftFragment();
    }

    @Override
    protected int bindLayout() {
        return R.layout.fragment_draft_layout;
    }

    @Override
    protected void onLazyLoad() {

    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        DraftManager.getInstance().unregisterDraftObserver(mDraftObserver);
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EventBus.getDefault().register(this);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
    }

    @Override
    protected void initView(View rootView) {
        mDraftListView = rootView.findViewById(R.id.rv_draft_list);
        mNoDraftHint = rootView.findViewById(R.id.ll_no_draft_hint);
        mEditingDraftAdapter = new EditingDraftAdapter();
        mDraftListView.setLayoutManager(new LinearLayoutManagerWrapper(getContext()));
        mDraftListView.setAdapter(mEditingDraftAdapter);
        initListener();
    }

    @Override
    public void onResume() {
        super.onResume();
        exitManagerState();
    }

    /**
     * Refresh data.
     * 刷新数据
     */
    public void refreshData() {
        if (mPresenter != null) {
            mPresenter.getDraftList();
        }
    }

    private void initListener() {
        DraftManager.getInstance().registerDraftObserver(mDraftObserver = new DraftObserver() {
            @Override
            public void onDraftChanged() {
                mPresenter.getDraftList();
            }
        });
        mEditingDraftAdapter.setOnItemClickListener((adapter, view, position) -> {
            if (Utils.isFastClick()) {
                return;
            }
            if (!PermissionUtils.isGroupGranted(STORAGE, LOCATION)) {
                ToastUtils.make()
                        .setGravity(Gravity.CENTER, 0, 0)
                        .setDurationIsLong(false)
                        .show(R.string.lack_of_authority);
                return;
            }
            if (!mEditingDraftAdapter.editEnable()) {
                DraftData data = mEditingDraftAdapter.getData().get(position);
                DraftManager.getInstance().setCurrentDraft(data);

                // 如果是模板类型，需要先跳转到视频选择页面
                if (data.isTemplate()) {
                    handleTemplateSelection(data);
                } else {
                    // 普通草稿，直接进入编辑页面
                    handleUserDraftSelection(data);
                }
            }
        });
        mEditingDraftAdapter.setOnItemChildClickListener((adapter, view, position) -> {
            mCurrentClickedPosition = position;
            if (view.getId() == R.id.iv_draft_manager) {
                if (!mEditingDraftAdapter.editEnable()) {
                    showManageDraftDialog();
                }
            } else if (view.getId() == R.id.iv_select_state) {
                mEditingDraftAdapter.dealSelected(position);
            } else if (view.getId() == R.id.tv_upload) {
                if (mEditingDraftAdapter.editEnable()) {

                }
            }
        });
        mNoDraftHint.setOnClickListener(v -> {
            if (!PermissionUtils.isGroupGranted(STORAGE, LOCATION)) {
                requestPermission(new PermissionUtils.FullCallback() {
                    @Override
                    public void onGranted(@NonNull List<String> granted) {
                        if (PermissionUtils.isGroupGranted(STORAGE, LOCATION)) {
                            EventBus.getDefault().post(MESSAGE_REQUEST_PERMISSION);
                            Bundle bundle = new Bundle();
                            bundle.putInt(FROM_PAGE, FROM_MAIN_PAGE);
                            AppManager.getInstance().jumpActivity(getContext(), MaterialSelectActivity.class, bundle);
                        } else {
                            ToastUtils.make()
                                    .setGravity(Gravity.CENTER, 0, 0)
                                    .setDurationIsLong(false)
                                    .show(R.string.lack_of_storage);
                        }
                    }

                    @Override
                    public void onDenied(@NonNull List<String> deniedForever, @NonNull List<String> denied) {
                        if (deniedForever.size() > 0) {
                            ToastUtils.make()
                                    .setGravity(Gravity.CENTER, 0, 0)
                                    .setDurationIsLong(false)
                                    .show(R.string.lack_of_storage);
                        }
                    }
                }, STORAGE, LOCATION);
            } else {
                Bundle bundle = new Bundle();
                bundle.putInt(FROM_PAGE, FROM_MAIN_PAGE);
                AppManager.getInstance().jumpActivity(getContext(), MaterialSelectActivity.class, bundle);
            }
        });
    }

    private void requestPermission(final PermissionUtils.FullCallback callback,
                                   @PermissionConstants.Permission final String... permissions) {
        if (PermissionUtils.isGroupGranted(permissions)) {
            EventBus.getDefault().post(MESSAGE_REQUEST_PERMISSION);
        } else {
            PermissionUtils.permission(permissions).callback(callback).request();
        }
    }

    private boolean checkRemoteClip(MeicamTimeline meicamTimeline) {
        if (meicamTimeline == null) {
            return false;
        }
        int audioTrackCount = meicamTimeline.getAudioTrackCount();
        for (int audioTrackIndex = 0; audioTrackIndex < audioTrackCount; audioTrackIndex++) {
            MeicamAudioTrack audioTrack = meicamTimeline.getAudioTrack(audioTrackIndex);
            if (audioTrack != null) {
                int clipCount = audioTrack.getClipCount();
                for (int clipIndex = 0; clipIndex < clipCount; clipIndex++) {
                    MeicamAudioClip audioClip = audioTrack.getAudioClip(clipIndex);
                    if (audioClip != null) {
                        String filePath = audioClip.getFilePath();
                        if (filePath != null && filePath.endsWith("m3u8")){
                            return false;
                        }
                    }
                }
            }
        }
        int count = meicamTimeline.videoTrackCount();
        for (int trackIndex = 0; trackIndex < count; trackIndex++) {
            MeicamVideoTrack videoTrack = meicamTimeline.getVideoTrack(trackIndex);
            if (videoTrack != null) {
                int clipCount = videoTrack.getClipCount();
                for (int clipIndex = 0; clipIndex < clipCount; clipIndex++) {
                    MeicamVideoClip videoClip = videoTrack.getVideoClip(clipIndex);
                    if (videoClip != null) {
                        String filePath = videoClip.getFilePath();
                        if (filePath != null && filePath.endsWith("m3u8")) {
                            return false;
                        }
                        filePath = videoClip.getReverseFilePath();
                        if (filePath != null && filePath.endsWith("m3u8")) {
                            return false;
                        }
                    }
                }
            }
        }
        return true;
    }

    @Override
    protected void initData() {
        /*
         *   无权限，不设置数据
         *   No permission, no data set
         */
        if (!PermissionUtils.isGroupGranted(STORAGE, LOCATION)) {
            return;
        }
        refreshData();
    }

    @Override
    public void onEditingDataBack(List<DraftData> dataList) {
        if (CommonUtils.isEmpty(dataList)) {
            mNoDraftHint.setVisibility(View.VISIBLE);
            mDraftListView.setVisibility(View.GONE);
            return;
        }
        mNoDraftHint.setVisibility(View.GONE);
        mDraftListView.setVisibility(View.VISIBLE);
        for (DraftData draftData : dataList) {
            draftData.setFileSize(getPrintSize(draftData.getFileSizeLong()));
        }
        mEditingDraftAdapter.setNewData(dataList);
    }

    @Override
    public void onLoginBack(boolean isLogin) {

    }

    /**
     * 展示删除视图
     * Show the delete view
     */
    public void showDeleteView(int layoutId) {
        if (getContext() == null) {
            return;
        }

        if (mBottomDeleteView == null) {
            mBottomDeleteView = BottomDeleteFragment.create(new BottomDeleteFragment.OnEventListener() {
                DeleteDraftPop deleteDraftPop;

                @Override
                public void onDelete() {
                    if (CommonUtils.isEmpty(mEditingDraftAdapter.getSelectedList())) {
                        return;
                    }
                    if (deleteDraftPop == null && getContext() != null) {
                        deleteDraftPop = DeleteDraftPop.create(getContext(), new DeleteDraftPop.EventListener() {
                            @Override
                            public void onDelete() {
                                for (DraftData data : mEditingDraftAdapter.getSelectedList()) {
                                    mPresenter.deleteDraft(data);
                                }
                                exitManagerState();
                            }

                            @Override
                            public void onCancel() {

                            }
                        });
                    }
                    deleteDraftPop.show();
                }
            });
        }
        FragmentActivity activity = getActivity();
        if (activity == null) {
            return;
        }
        View view = activity.findViewById(layoutId);
        if (view.getVisibility() == View.GONE) {
            view.setVisibility(View.VISIBLE);
        }
        FragmentTransaction transaction = activity.getSupportFragmentManager().beginTransaction();
        transaction.replace(layoutId, mBottomDeleteView);
        transaction.commitAllowingStateLoss();
    }

    /**
     * 不显示删除视图
     * Not display the delete view
     */
    private void closeDeleteView() {
        FragmentActivity activity = getActivity();
        if (activity == null) {
            return;
        }
        MessageEvent.sendEvent(MESSAGE_UPDATE_DRAFT_MANAGER);
        View view = activity.findViewById(R.id.fl_bottom_container);
        if (view.getVisibility() == View.VISIBLE) {
            view.setVisibility(View.GONE);
        }
        FragmentTransaction transaction = activity.getSupportFragmentManager().beginTransaction();
        transaction.remove(mBottomDeleteView);
        transaction.commitAllowingStateLoss();
    }


    /**
     * 显示管理草稿的弹窗
     * Displays the dialog for the administration draft
     */
    private void showManageDraftDialog() {
        if (getContext() == null) {
            return;
        }
        if (mManageDraftPop == null) {
            mManageDraftPop = ManageDraftPop.create(getContext(), new ManageDraftPop.ManageListener() {
                @Override
                public void onRename() {
                    mManageDraftPop.dismiss();
                    showRenameDialog();
                }

                @Override
                public void onCopy() {
                    if (mCurrentClickedPosition >= 0 && mCurrentClickedPosition < mEditingDraftAdapter.getData().size()) {
                        mPresenter.copyDraft(mEditingDraftAdapter.getItem(mCurrentClickedPosition));
                    }
                    mManageDraftPop.dismiss();
                }

                @Override
                public void onDelete() {
                    DraftData item = mEditingDraftAdapter.getItem(mCurrentClickedPosition);
                    if (item != null) {
                        mPresenter.deleteDraft(item);
                    }
                    mManageDraftPop.dismiss();
                }

                @Override
                public void onUpload() {
                    IUserPlugin userPlugin = PluginManager.get().getUserPlugin();
                    if (userPlugin != null) {
                        if (userPlugin.isLogin()) {
                            DraftData item = mEditingDraftAdapter.getItem(mCurrentClickedPosition);
                            if (item != null) {
                                if (item.isCloud()) {
                                    userPlugin.showCloudUploadConfirmPop(getActivity(), new IUserPlugin.OnEventListener() {
                                        @Override
                                        public void onConfirm(Object o) {
                                            upload(userPlugin, item, true);
                                        }

                                        @Override
                                        public void onCancel() {
                                            upload(userPlugin, item, false);
                                        }
                                    });
                                } else {
                                    upload(userPlugin, item, true);
                                }
                            }
                        } else {
                            showLoginDialog(new IUserPlugin.ILoginCallBack() {
                                @Override
                                public void onLoginSuccess(String token) {
                                    mPresenter.getDraftList();
                                    DraftData draftData = mEditingDraftAdapter.getItem(mCurrentClickedPosition);
                                    upload(userPlugin, draftData, false);
                                    onLoginBackInActivity(true);
                                }

                                @Override
                                public void onLoginFailed(int code) {
                                    onLoginBackInActivity(false);
                                }
                            });
                        }
                    }
                }

                @Override
                public void onCompile() {
                    IUserPlugin userPlugin = PluginManager.get().getUserPlugin();
                    if (userPlugin != null) {
                        if (userPlugin.isLogin()) {
                            goToCloudCompileActivity();
                        } else {
                            showLoginDialog(new IUserPlugin.ILoginCallBack() {
                                @Override
                                public void onLoginSuccess(String token) {
                                    goToCloudCompileActivity();
                                    onLoginBackInActivity(true);
                                }

                                @Override
                                public void onLoginFailed(int code) {
                                    onLoginBackInActivity(false);
                                }
                            });
                        }
                    }
                }

                private void upload(IUserPlugin userPlugin, DraftData item, boolean isUpdate) {
                    DraftData.CloudInfo cloudInfo = item.getCloudInfo();
                    if (TextUtils.isEmpty(cloudInfo.uuid)) {
                        cloudInfo.uuid = UUID.randomUUID().toString().toUpperCase();
                    }
                    userPlugin.upload(item, isUpdate);
                }

            });
        }
        mManageDraftPop.show();
    }

    private void onLoginBackInActivity(boolean isLogin) {
        FragmentActivity activity = getActivity();
        if (activity == null) {
            return;
        }
        try {
            activity.getClass().getMethod("onLoginBack", boolean.class).invoke(activity, isLogin);
        } catch (IllegalAccessException | InvocationTargetException | NoSuchMethodException e) {
            LogUtils.e(e);
        }
    }

    private void goToCloudCompileActivity() {
        DraftManager.getInstance().setCurrentDraft(mEditingDraftAdapter.getItem(mCurrentClickedPosition));
        try {
            Class<?> aClass = Class.forName("com.meishe.user.activity.CloudCompileActivity");
            AppManager.getInstance().jumpActivityForResult(getActivity(), (Class<? extends Activity>) aClass, null, 1000);
        } catch (ClassNotFoundException e) {
            LogUtils.e(e);
        }
    }

    /**
     * 显示草稿重命名的弹窗
     * Displays the dialog for the rename draft
     */
    private void showRenameDialog() {
        if (getContext() == null || mCurrentClickedPosition < 0 ||
                mCurrentClickedPosition >= mEditingDraftAdapter.getData().size()) {
            return;
        }
        if (renameDraftPop == null) {
            renameDraftPop = RenameDraftPop.create(getContext(), new RenameDraftPop.EventListener() {
                @Override
                public void onConfirm(String name) {
                    //注意：需要再次获取item
                    // Note: you need to obtain item again
                    DraftData item = mEditingDraftAdapter.getItem(mCurrentClickedPosition);
                    if (item != null && !TextUtils.isEmpty(name) && !name.equals(item.getFileName())) {
                        mPresenter.renameDraft(item, name);
                    }
                    renameDraftPop.dismiss();
                }
            });
        }
        //注意：如果使用参数传递过来position是有问题的
        // Note: There is a problem if you use the parameter to pass the position
        DraftData item = mEditingDraftAdapter.getItem(mCurrentClickedPosition);
        if (item != null) {
            renameDraftPop.show(item.getFileName());
        }
    }

    /**
     * 展示登录弹窗
     * Show login dialog
     */
    private void showLoginDialog(IUserPlugin.ILoginCallBack callBack) {
        if (getContext() == null) {
            return;
        }
        final IUserPlugin userPlugin = PluginManager.get().getUserPlugin();
        if (userPlugin != null) {
            userPlugin.showLoginPop(getContext(), callBack);
        }

    }

    private String getPrintSize(long size) {
        if (size < 0) {
            return "";
        }
        /*
         *如果字节数少于1024，则直接以B为单位，否则先除于1024，后3位因太少无意义
         *If the number of bytes is less than 1024, it is directly in B.
         *otherwise, divide by 1024 first, and the last three bits are meaningless because they are too few.
         */
        if (size < 1024) {
            return size + "B";
        } else {
            size = size / 1024;
        }
        /*
         * 如果原字节数除于1024之后，少于1024，则可以直接以KB作为单位
         * 因为还没有到达要使用另一个单位的时候 接下去以此类推
         * If the original number of bytes is less than 1024 divided by 1024, KB can be used as the unit directly.
         * Because it's not time to use another unit.
         * And so on.
         */
        if (size < 1024) {
            return size + "KB";
        } else {
            size = size / 1024;
        }
        if (size < 1024) {
            /*
             *  因为如果以MB为单位的话，要保留最后1位小数，
             *  因此，把此数乘以100之后再取余
             *  Because if the unit is MB, the last decimal place shall be reserved,
             *  and the remainder shall be taken after multiplying this number by 100.
             */
            size = size * 100;
            return (size / 100) + "." + (size % 100) + "M";
        } else {
            /*
             *  否则如果要以GB为单位的，先除于1024再作同样的处理
             * Otherwise, if the unit is GB, divide it by 1024 and then do the same
             */
            size = size * 100 / 1024;
            return (size / 100) + "." + (size % 100) + "G";
        }
    }

    public void exitManagerState() {
        if (!isEditState) {
            return;
        }
        isEditState = false;
        closeDeleteView();
        mEditingDraftAdapter.setEditEnable(false);
    }

    public void goManagerState(int bottomLayoutId) {
        if (isEditState) {
            return;
        }
        isEditState = true;
        showDeleteView(bottomLayoutId);
        mEditingDraftAdapter.setEditEnable(true);
    }

    /**
     * 处理模板选择
     * Handle template selection
     * @param templateData 模板数据
     */
    private void handleTemplateSelection(DraftData templateData) {
        // 对于模板类型，先跳转到视频选择页面
        Bundle bundle = new Bundle();
        bundle.putInt(FROM_PAGE, FROM_MAIN_PAGE);
        bundle.putSerializable("template_data", templateData);
        AppManager.getInstance().jumpActivity(getActivity(), MaterialSelectActivity.class, bundle);
    }

    /**
     * 处理用户草稿选择
     * Handle user draft selection
     * @param draftData 草稿数据
     */
    private void handleUserDraftSelection(DraftData draftData) {
        DraftTimelineHelper.recoverTimelineFromDraft(draftData, meicamTimeline -> {
            if (meicamTimeline == null) {
                LogUtils.e("timeline is null !!!");
                ToastUtils.showShort(R.string.error_draft_data_is_error);
                return;
            }

            meicamTimeline.setCoverDataConfigPath(DraftManager.getCoverConfigPath(draftData.getDirPath()));
            meicamTimeline.setDraftDir(draftData.getDirPath());
            //检查是否有远程路径，如果有,且无网络，则不编辑
            //Check whether there is a remote path. If there is, and there is no network, do not edit.
            if (!NetUtils.isNetworkAvailable(getContext())) {
                if (!checkRemoteClip(meicamTimeline)){
                    ToastUtils.showShort(R.string.tv_tip_no_net);
                    return;
                }
            }
            EditorEngine.getInstance().setCurrentTimeline(meicamTimeline);
            Bundle bundle = new Bundle();
            bundle.putInt(FROM_PAGE, FROM_MAIN_PAGE);
            AppManager.getInstance().jumpActivity(getActivity(), DraftEditActivity.class, bundle);
        });
    }

    /**
     * On message event.
     * 消息事件
     *
     * @param event the event
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onMessageEvent(MessageEvent event) {

    }
}
