package com.meishe.myvideo.fragment;

import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.SeekBar;
import android.widget.TextView;

import com.meishe.base.model.BaseMvpFragment;
import com.meishe.engine.bean.MeicamVideoClip;
import com.meishe.engine.constant.NvsConstants;
import com.meishe.myvideo.R;
import com.meishe.myvideo.fragment.iview.BeautyView;
import com.meishe.myvideo.fragment.presenter.BeautyPresenter;
import com.meishe.myvideo.view.MYSeekBarTextView;

import static com.meishe.myvideo.fragment.BeautyShapeFragment.VIDEO_CLIP;
import static com.meishe.myvideo.fragment.presenter.BeautyPresenter.INVALID_VALUE;

/**
 * The type Beauty fragment.
 * 美颜fragment
 *
 * <AUTHOR>
 * @Description 美颜 The beauty fragment
 * @CreateDate 2020 /6/30 20:17
 */
public class BeautyFragment extends BaseMvpFragment<BeautyPresenter> implements BeautyView, View.OnClickListener {
    /**
     * 磨皮
     * buffing
     */
    private static final int TYPE_BUFFING = 1;
    /**
     * whitening
     * 美白
     */
    private static final int TYPE_WHITENING = 2;
    /**
     * ruddy
     * 甜美
     */
    private static final int TYPE_RUDDY = 3;

    private ImageView mIvApplyAll;
    private TextView mTvApplyAll;
    private MYSeekBarTextView mSeekBar;

    private LinearLayout mLlBuffing;
    private ImageView mIvBuffing;
    private TextView mTvBuffing;

    private LinearLayout mLlWhitening;
    private ImageView mIvWhitening;
    private TextView mTvWhitening;

    private LinearLayout mLlRuddy;
    private ImageView mIvRuddy;
    private TextView mTvRuddy;
    private ImageView mIvConfirm;
    private int mType;
    private TextView mTvReset;
    private BeautyEventListener mEventListener;

    public BeautyFragment() {
    }

    public BeautyFragment(BeautyEventListener listener) {
        mEventListener = listener;
    }
    @Override
    protected int bindLayout() {
        return R.layout.fragment_skin_beauty;
    }

    @Override
    protected void onLazyLoad() {

    }

    @Override
    protected void initView(View rootView) {
        mIvApplyAll = rootView.findViewById(R.id.iv_apply_all);
        mTvApplyAll = rootView.findViewById(R.id.tv_apply_all);
        mSeekBar = rootView.findViewById(R.id.seek_bar);

        mLlBuffing = rootView.findViewById(R.id.ll_buffing);
        mIvBuffing = rootView.findViewById(R.id.iv_buffing);
        mTvBuffing = rootView.findViewById(R.id.tv_buffing);

        mLlWhitening = rootView.findViewById(R.id.ll_whitening);
        mIvWhitening = rootView.findViewById(R.id.iv_whitening);
        mTvWhitening = rootView.findViewById(R.id.tv_whitening);

        mLlRuddy = rootView.findViewById(R.id.ll_ruddy);
        mIvRuddy = rootView.findViewById(R.id.iv_ruddy);
        mTvRuddy = rootView.findViewById(R.id.tv_ruddy);

        mTvReset = rootView.findViewById(R.id.tv_reset);
        mIvConfirm = rootView.findViewById(R.id.iv_confirm);
        initListener();
    }

    private void initListener() {
        mIvApplyAll.setOnClickListener(this);
        mTvApplyAll.setOnClickListener(this);
        mIvConfirm.setOnClickListener(this);
        mLlBuffing.setOnClickListener(this);
        mLlWhitening.setOnClickListener(this);
        mLlRuddy.setOnClickListener(this);
        mTvReset.setOnClickListener(this);
        mSeekBar.setOnSeekBarChangeListener(new MYSeekBarTextView.OnSeekBarListener() {
            @Override
            public void onStopTrackingTouch(int progress, String name) {

            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {

            }

            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                float value = seekBar.getProgress() / 100f;
                if (mType == TYPE_BUFFING) {
                    mPresenter.applyBeautyVideoFx(NvsConstants.Beauty.VIDEO_FX_BEAUTY_STRENGTH, value);
                } else if (mType == TYPE_WHITENING) {
                    mPresenter.applyBeautyVideoFx(NvsConstants.Beauty.VIDEO_FX_BEAUTY_WHITENING, seekBar.getProgress() / 100f);
                } else if (mType == TYPE_RUDDY) {
                    mPresenter.applyBeautyVideoFx(NvsConstants.Beauty.VIDEO_FX_BEAUTY_REDDENING, seekBar.getProgress() / 100f);
                }
                if (Math.abs(value) > 0.0001) {
                    mTvReset.setSelected(false);
                }
            }
        });
    }

    @Override
    protected void initData() {
        Bundle bundle = getArguments();
        if (bundle != null) {
            MeicamVideoClip meicamVideoClip = (MeicamVideoClip) bundle.getSerializable(VIDEO_CLIP);
            if (meicamVideoClip != null) {
                mPresenter.initVideoFx(meicamVideoClip);
                mTvReset.setSelected(mPresenter.checkBeautySkinStatus());
            }
        }
        selectBuffing();
    }


    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.iv_apply_all || id == R.id.tv_apply_all) {
            mPresenter.applyAllClip(false);
        } else if (id == R.id.ll_buffing) {
            selectBuffing();
        } else if (id == R.id.ll_whitening) {
            selectWhitening();
        } else if (id == R.id.ll_ruddy) {
            selectRuddy();
        } else if (id == R.id.tv_reset) {
            resetVideoFx();
        } else if (id == R.id.iv_confirm) {
            if(mEventListener != null){
                mEventListener.onConfirm();
            }
        }
    }

    /**
     * 重置特效
     * Reset the videoFx
     */
    private void resetVideoFx() {
        mSeekBar.setProgress(0);
        mTvReset.setSelected(true);
        mPresenter.resetBeautyFx();
    }

    private void selectRuddy() {
        mType = TYPE_RUDDY;
        mIvBuffing.setSelected(false);
        mTvBuffing.setTextColor(getResources().getColor(R.color.white_8));
        mIvWhitening.setSelected(false);
        mTvWhitening.setTextColor(getResources().getColor(R.color.white_8));
        mIvRuddy.setSelected(true);
        mTvRuddy.setTextColor(getResources().getColor(R.color.adjust_selected_bg));
        double videoFxStrength = mPresenter.getVideoFxStrength(NvsConstants.Beauty.VIDEO_FX_BEAUTY_REDDENING);
        if (videoFxStrength == INVALID_VALUE) {
            videoFxStrength = 0;
        }
        mSeekBar.setProgress((int) (videoFxStrength * 100));
    }

    private void selectWhitening() {
        mType = TYPE_WHITENING;
        mIvBuffing.setSelected(false);
        mTvBuffing.setTextColor(getResources().getColor(R.color.white_8));
        mIvWhitening.setSelected(true);
        mTvWhitening.setTextColor(getResources().getColor(R.color.adjust_selected_bg));
        mIvRuddy.setSelected(false);
        mTvRuddy.setTextColor(getResources().getColor(R.color.white_8));
        double videoFxStrength = mPresenter.getVideoFxStrength(NvsConstants.Beauty.VIDEO_FX_BEAUTY_WHITENING);
        if (videoFxStrength == INVALID_VALUE) {
            videoFxStrength = 0;
        }
        mSeekBar.setProgress((int) (videoFxStrength * 100));
    }

    private void selectBuffing() {
        mType = TYPE_BUFFING;
        mIvBuffing.setSelected(true);
        mTvBuffing.setTextColor(getResources().getColor(R.color.adjust_selected_bg));
        mIvWhitening.setSelected(false);
        mTvWhitening.setTextColor(getResources().getColor(R.color.white_8));
        mIvRuddy.setSelected(false);
        mTvRuddy.setTextColor(getResources().getColor(R.color.white_8));
        double videoFxStrength = mPresenter.getVideoFxStrength(NvsConstants.Beauty.VIDEO_FX_BEAUTY_STRENGTH);
        if (videoFxStrength == INVALID_VALUE) {
            videoFxStrength = 0;
        }
        mSeekBar.setProgress((int) (videoFxStrength * 100));
    }
    public interface BeautyEventListener {
        void onConfirm();
    }
}
