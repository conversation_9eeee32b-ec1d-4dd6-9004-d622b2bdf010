package com.meishe.myvideo.fragment;

import android.os.Bundle;
import android.view.View;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.meishe.base.manager.LinearLayoutManagerWrapper;
import com.meishe.base.model.BaseFragment;
import com.meishe.myvideo.R;
import com.meishe.myvideo.activity.adapter.MusicListAdapter;
import com.meishe.myvideo.bean.MusicInfo;
import com.meishe.myvideo.util.AudioUtil;
import com.meishe.third.adpater.BaseQuickAdapter;

import java.util.List;

/**
 * 媒体音乐页面
 * The music page
 */
public class MusicFragment extends BaseFragment {
    private static final String TYPE = "type";
    private RecyclerView mRvMusicList;
    private MusicListAdapter mAdapter;
    private TextView mTvNothing;
    private boolean mIsLocalMusic;
    private EventListener mEventListener;

    public MusicFragment() {

    }

    public static MusicFragment create(boolean isLocalMusic, EventListener listener) {
        MusicFragment fragment = new MusicFragment();
        Bundle bundle = new Bundle();
        bundle.putBoolean(TYPE, isLocalMusic);
        fragment.setArguments(bundle);
        fragment.setEventListener(listener);
        return fragment;
    }

    private void setEventListener(EventListener listener) {
        mEventListener = listener;
    }

    @Override
    protected int bindLayout() {
        return R.layout.fragment_music;
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        if(!isVisibleToUser){
            unselected();
            if(mEventListener != null){
                mEventListener.onMusicUnselected(mIsLocalMusic);
            }
        }
    }

    @Override
    protected void onLazyLoad() {

    }

    @Override
    protected void initView(View rootView) {
        mRvMusicList = rootView.findViewById(R.id.rv_music_list);
        mTvNothing = rootView.findViewById(R.id.tv_nothing);

        mRvMusicList.setLayoutManager(new LinearLayoutManagerWrapper(getActivity(), LinearLayoutManager.VERTICAL, false));
        mAdapter = new MusicListAdapter();
        mRvMusicList.setAdapter(mAdapter);
        mAdapter.setOnItemChildClickListener(new BaseQuickAdapter.OnItemChildClickListener() {
            @Override
            public void onItemChildClick(BaseQuickAdapter adapter, View view, int position) {
                if (view.getId() == R.id.ibt_play) {
                    mAdapter.selected(position);
                    if (mEventListener != null) {
                        if (mAdapter.getSelectedPosition() >= 0) {
                            mEventListener.onMusicSelected(mAdapter.getItem(position), mIsLocalMusic);
                        } else {
                            mEventListener.onMusicUnselected(mIsLocalMusic);
                        }
                    }
                }
            }
        });
    }

    @Override
    protected void initData() {
        Bundle arguments = getArguments();
        if (arguments != null) {
            mIsLocalMusic = arguments.getBoolean(TYPE);
        }
        AudioUtil audioUtil = new AudioUtil(getActivity());
        audioUtil.getAudioList(mIsLocalMusic, new AudioUtil.AudioLoadListener() {
            @Override
            public void loadComplete(List<MusicInfo> audioList) {
                if (audioList != null && audioList.size() > 0) {
                    mTvNothing.setVisibility(View.INVISIBLE);
                    mRvMusicList.setVisibility(View.VISIBLE);
                    mAdapter.setNewData(audioList);
                }
            }
        });
    }

    /**
     * 不选中
     * unselected
     */
    private void unselected() {
        if (mAdapter != null && mAdapter.getSelectedPosition() >= 0) {
            mAdapter.selected(mAdapter.getSelectedPosition());
        }
    }

    public interface EventListener {
        /**
         * 选择音乐
         * Choose music
         * @param info 音乐数据。Music infomation
         * @param isLocalMusic 是否是本地音乐。is music in phone
         */
        void onMusicSelected(MusicInfo info, boolean isLocalMusic);

        /**
         * 音乐被取消选中
         * Music is unchecked
         * @param isLocalMusic 是否是本地音乐。is music in phone
         */
        void onMusicUnselected(boolean isLocalMusic);
    }
}
