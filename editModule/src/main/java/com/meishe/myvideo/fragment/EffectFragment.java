package com.meishe.myvideo.fragment;


import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.facebook.drawee.backends.pipeline.Fresco;
import com.facebook.drawee.backends.pipeline.PipelineDraweeControllerBuilder;
import com.facebook.drawee.controller.AbstractDraweeController;
import com.facebook.drawee.view.SimpleDraweeView;
import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.ImageLoader;
import com.meishe.base.utils.SizeUtils;
import com.meishe.business.assets.AssetUtils;
import com.meishe.business.assets.fragment.FlowFragment;
import com.meishe.business.assets.fragment.adapter.CommonAdapter;
import com.meishe.business.assets.iview.AssetsView;
import com.meishe.business.assets.presenter.FlowPresenter;
import com.meishe.engine.EditorEngine;
import com.meishe.engine.asset.bean.AssetInfo;
import com.meishe.engine.asset.bean.RequestParam;
import com.meishe.engine.bean.MeicamTimelineVideoFxClip;
import com.meishe.engine.interf.IBaseInfo;
import com.meishe.logic.constant.PagerConstants;
import com.meishe.myvideo.R;
import com.meishe.third.adpater.BaseViewHolder;

/**
 * 特效fragment
 * EffectFragment
 */
public class EffectFragment extends FlowFragment<FlowPresenter> implements AssetsView {


    private MeicamTimelineVideoFxClip meicamTimelineVideoFxClip;

    public EffectFragment() {
        spanCount = 4;
        leftItemDecoration = SizeUtils.dp2px(7);
    }


    public static EffectFragment create(RequestParam param, MeicamTimelineVideoFxClip meicamTimelineVideoFxClip) {
        EffectFragment fragment = new EffectFragment();
        fragment.meicamTimelineVideoFxClip = meicamTimelineVideoFxClip;
        Bundle bundle = new Bundle();
        if (param != null) {
            bundle.putInt(PagerConstants.BUNDLE_KEY_ASSET_TYPE, param.type);
            bundle.putInt(PagerConstants.BUNDLE_KEY_ASSET_TYPE_NEW, param.type);
            bundle.putInt(PagerConstants.BUNDLE_KEY_ASSET_TYPE_CATEGORY, param.categoryId);
            bundle.putInt(PagerConstants.BUNDLE_KEY_ASSET_TYPE_KIND, param.kind);
        }
        fragment.setArguments(bundle);
        return fragment;
    }


    @Override
    protected int getItemLayoutResId() {
        return 0;
    }

    @Override
    protected void onAdapterItemClick(int position) {
        AssetInfo info = mAdapter.getItem(position);
        if (info != null) {
            meicamTimelineVideoFxClip = applyEffect(info, meicamTimelineVideoFxClip);
        }
    }

    @Override
    protected String getAssetId() {
        if(meicamTimelineVideoFxClip != null){
           return meicamTimelineVideoFxClip.getDesc();
        }
        return "";
    }

    @Override
    protected CommonAdapter getAdapter() {
        return new EffectAdapter(getContext());
    }

    /**
     * 应用特效
     * Apply effect
     *
     * @param info the info
     */
    private MeicamTimelineVideoFxClip applyEffect(IBaseInfo info, MeicamTimelineVideoFxClip meicamTimelineVideoFxClip) {
        return EditorEngine.getInstance().addTimelineEffect(info, meicamTimelineVideoFxClip,true);
    }


    /**
     * Sets item selected.
     * 设置项目选择
     *
     * @param position the position
     */
    @Override
    public void setSelected(int position) {
        if (position == -1) {
            meicamTimelineVideoFxClip = null;
        }
        super.setSelected(position);
    }


    /**
     * 切换页面更新选中状态
     * Update selected.
     *
     * @param effect the effect
     */
    public void updateSelected(MeicamTimelineVideoFxClip effect) {
        meicamTimelineVideoFxClip = effect;
        if (effect == null || mAdapter == null) {
            return;
        }
        setSelected(effect.getDesc());
    }

    private static class EffectAdapter extends CommonAdapter {
        private final ImageLoader.Options mOptions;

        private final PipelineDraweeControllerBuilder mPipelineBuilder;

        private EffectAdapter(Context context) {
            super(R.layout.effect_item);
            mContext  = context;
            mPipelineBuilder = Fresco.newDraweeControllerBuilder();
            mOptions = new ImageLoader.Options()
                    .centerCrop()
                    .dontAnimate();
        }

        @Override
        protected void convert(@NonNull BaseViewHolder helper, AssetInfo item) {
            SimpleDraweeView ivCover = helper.getView(R.id.iv_cover);
            TextView tvAssetPurchased = helper.getView(R.id.tv_asset_purchased);
            if (AssetUtils.assetPurchasedVisible(tvAssetPurchased, mAssetSubType, item.isAuthorized())) {
                tvAssetPurchased.setVisibility(View.VISIBLE);
            } else {
                if (tvAssetPurchased != null) {
                    tvAssetPurchased.setVisibility(View.GONE);
                }
            }
            String coverPath = item.getCoverPath();
            if (!TextUtils.isEmpty(coverPath) && coverPath.startsWith("http")) {
                AbstractDraweeController controller = mPipelineBuilder
                        .setUri(coverPath)
                        .setOldController(ivCover.getController())
                        .setAutoPlayAnimations(true)
                        .build();
                ivCover.setController(controller);
            } else {
                ImageLoader.loadUrl(mContext, item.getCoverPath(), ivCover, mOptions);
            }

            View coverRound = helper.getView(R.id.iv_cover_round);
            if (helper.getAdapterPosition() == mSelectedPosition) {
                coverRound.setBackground(CommonUtils.getRadiusDrawable(3, mContext.getResources().getColor(R.color.color_fffc2b55), 6, -1));
            } else {
                coverRound.setBackgroundResource(0);
            }
            helper.setText(R.id.tv_name, item.getName());
            ImageView ivDownload = helper.getView(R.id.iv_downloading);
            if (!item.isHadDownloaded() || item.needUpdate()) {
                ImageLoader.loadUrl(mContext, R.mipmap.downloading, ivDownload);
                int progress = item.getDownloadProgress();
                if (progress >= 0 && progress < 100) {
                    ivDownload.setVisibility(View.VISIBLE);
                    ivCover.setVisibility(View.GONE);
                } else {
                    ivDownload.setVisibility(View.GONE);
                    ivCover.setVisibility(View.VISIBLE);
                }
            } else {
                ivDownload.setVisibility(View.GONE);
                ivCover.setVisibility(View.VISIBLE);
            }
        }
    }
}
