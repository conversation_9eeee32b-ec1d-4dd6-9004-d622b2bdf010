package com.meishe.myvideo.fragment;

import android.graphics.Color;
import android.graphics.PointF;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.facebook.drawee.backends.pipeline.Fresco;
import com.facebook.drawee.drawable.ScalingUtils;
import com.facebook.drawee.generic.GenericDraweeHierarchy;
import com.facebook.drawee.generic.GenericDraweeHierarchyBuilder;
import com.facebook.drawee.interfaces.DraweeController;
import com.facebook.drawee.view.SimpleDraweeView;
import com.meishe.base.model.BaseMvpFragment;
import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.ImageLoader;
import com.meishe.base.utils.NetUtils;
import com.meishe.base.utils.SizeUtils;
import com.meishe.base.utils.ToastUtils;
import com.meishe.base.view.PullToRefreshAndPushToLoadView;
import com.meishe.base.view.decoration.ItemDecoration;
import com.meishe.business.assets.AssetUtils;
import com.meishe.business.assets.fragment.adapter.CommonAdapter;
import com.meishe.business.assets.view.AssetsTypeTabView;
import com.meishe.engine.asset.AssetsManager;
import com.meishe.engine.asset.bean.AssetInfo;
import com.meishe.engine.asset.bean.RequestParam;
import com.meishe.logic.constant.PagerConstants;
import com.meishe.myvideo.R;
import com.meishe.myvideo.fragment.iview.CompoundCaptionView;
import com.meishe.myvideo.fragment.presenter.CompoundCaptionPresenter;
import com.meishe.myvideo.interfaces.BottomEventListener;
import com.meishe.third.adpater.BaseQuickAdapter;
import com.meishe.third.adpater.BaseViewHolder;

import java.util.ArrayList;
import java.util.List;

/**
 * The type Compound Caption Fragment
 * 此类的类型为 组合字幕
 *
 * <AUTHOR>
 * @Description 组合字幕
 */
public class CompoundCaptionFragment extends BaseMvpFragment<CompoundCaptionPresenter> implements CompoundCaptionView {

    private BottomEventListener mEventListener;
    private CompoundCaptionAdapter mAdapter;
    private TextView mHintText;
    private PullToRefreshAndPushToLoadView mRefreshLayout;
    private AssetsTypeTabView mTypeTab;
    private RecyclerView mRvAssetList;
    private int mAssetSubType;
    private int mAssetType;
    private int mCategory;
    private int mKind;
    private String mDownloadTag;

    public CompoundCaptionFragment() {
        mPresenter = new CompoundCaptionPresenter();
    }

    public static CompoundCaptionFragment getInstance(RequestParam param) {
        CompoundCaptionFragment fragment = new CompoundCaptionFragment();
        Bundle bundle = new Bundle();
        if (param != null) {
            bundle.putInt(PagerConstants.BUNDLE_KEY_ASSET_TYPE_NEW, param.type);
            bundle.putInt(PagerConstants.BUNDLE_KEY_ASSET_TYPE_CATEGORY, param.categoryId);
            bundle.putInt(PagerConstants.BUNDLE_KEY_ASSET_TYPE_KIND, param.kind);
        }
        fragment.setArguments(bundle);
        return fragment;
    }

    public void setEventListener(BottomEventListener mEventListener) {
        this.mEventListener = mEventListener;
    }

    @Override
    protected CompoundCaptionPresenter createPresenter() {
        return mPresenter;
    }

    @Override
    protected int bindLayout() {
        return R.layout.view_compound_caption;
    }

    @Override
    protected void onLazyLoad() {

    }

    @Override
    protected void initView(View rootView) {
        mRvAssetList = rootView.findViewById(R.id.recyclerView);
        mTypeTab = rootView.findViewById(R.id.ttv_tab_type);
        mHintText = rootView.findViewById(R.id.tv_hint);
        GridLayoutManager gridLayoutManager = new GridLayoutManager(getContext(), 4);
        mRvAssetList.setLayoutManager(gridLayoutManager);
        mAdapter = new CompoundCaptionAdapter();
        mRvAssetList.setAdapter(mAdapter);
        mRvAssetList.addItemDecoration(new ItemDecoration(SizeUtils.dp2px(7), SizeUtils.dp2px(12), SizeUtils.dp2px(7), 0));
        mRefreshLayout = rootView.findViewById(R.id.ptl_recyclerView);
        mRefreshLayout.setCanLoadMore(true);
        mRefreshLayout.setCanRefresh(true);
        mRefreshLayout.finishRefreshing();

        initListener();
    }

    @Override
    protected void initData() {
        Bundle arguments = getArguments();
        if (arguments != null) {
            mAssetType = arguments.getInt(PagerConstants.BUNDLE_KEY_ASSET_TYPE_NEW, 0);
            mCategory = arguments.getInt(PagerConstants.BUNDLE_KEY_ASSET_TYPE_CATEGORY, 0);
            mKind = arguments.getInt(PagerConstants.BUNDLE_KEY_ASSET_TYPE_KIND, 0);
        }
        updateData(true);
    }

    private void initListener() {
        mRefreshLayout.setOnRefreshAndLoadMoreListener(new PullToRefreshAndPushToLoadView.PullToRefreshAndPushToLoadMoreListener() {
            @Override
            public void onRefresh() {
                initData();
            }

            @Override
            public void onLoadMore() {
                if (!loadMore()) {
                    finishLoading();
                }
            }
        });

        mAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                AssetInfo item = mAdapter.getItem(position);
                if (item != null) {
                    if ((!item.isHadDownloaded() || item.needUpdate())) {
                        downloadAssets(position, item);
                    } else {
                        if (mEventListener != null) {
                            mEventListener.onItemClick(item, false);
                        }
                        mPresenter.clickAssetItem(item);
                        mAdapter.selected(position);
                    }
                }
            }
        });
        mTypeTab.setItemClickedListener(new AssetsTypeTabView.ItemClickedListener() {
            @Override
            public void onItemClicked(int position) {
                mAssetSubType = position;
                mAdapter.setAssetSubType(position);
                updateData(false);
            }
        });
    }

    private void downloadAssets(int position, AssetInfo item) {
        item.setDownloadProgress(0);
        mAdapter.notifyItemChanged(position);
        mDownloadTag = item.getPackageId();
        mPresenter.downloadAsset(item, position);
    }

    private void updateData(final boolean needUpdate) {
        mHintText.setCompoundDrawables(null, null, null, null);
        mPresenter.setPageSize(16);
        mPresenter.loadData(mAssetType, mAssetSubType, mCategory, mKind, needUpdate);
    }

    private boolean loadMore() {
        mHintText.setCompoundDrawables(null, null, null, null);
        return mPresenter.loadMoreData(mAssetType, mAssetSubType, mCategory, mKind, false);
    }

    private void finishLoading() {
        if (mRefreshLayout.isLoading()) {
            mRefreshLayout.finishLoading();
        }
    }

    private void finishRefresh() {
        if (mRefreshLayout.isRefreshing()) {
            mRefreshLayout.finishRefreshing();
        }
    }


    @Override
    public void onDestroy() {
        super.onDestroy();
        AssetsManager.get().clearCache();
    }

    @Override
    public boolean isActive() {
        return isAdded();
    }

    @Override
    public void onNewDataBack(List<AssetInfo> list, int subType, boolean needUpdate) {
        if (!CommonUtils.isEmpty(list)) {
            mAdapter.setNewData(list);
        }
        updateViewState(subType, needUpdate);
    }

    private void updateViewState(int subType, final boolean needUpdate) {
        finishRefresh();
        finishLoading();
        if (getItemCount() <= 0) {
            mHintText.setVisibility(View.VISIBLE);
            mRvAssetList.setVisibility(View.GONE);
            if (!NetUtils.isNetworkAvailable(getContext())) {
                mHintText.setText(R.string.user_hint_assets_net_error_refresh);
                Drawable drawable = getResources().getDrawable(R.mipmap.ic_assets_data_update);
                drawable.setBounds(new Rect(0, 4, drawable.getIntrinsicHeight(), drawable.getIntrinsicHeight() + 4));
                mHintText.setCompoundDrawables(null, null, drawable, null);
                mHintText.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        mHintText.setCompoundDrawables(null, null, null, null);
                        updateData(needUpdate);
                    }
                });
            } else {
                mHintText.setText(AssetsManager.get().getErrorMsg(getContext(), subType));
            }
        } else {
            mHintText.setVisibility(View.GONE);
            mRvAssetList.setVisibility(View.VISIBLE);
            if (!NetUtils.isNetworkAvailable(getContext())) {
                if (mRefreshLayout.isRefreshing() || mRefreshLayout.isLoading()) {
                    ToastUtils.showShort(getResources().getString(R.string.user_hint_assets_net_error));
                }
            }
        }
    }

    @Override
    public void onMoreDataBack(List<AssetInfo> list, int subType, boolean needUpdate) {
        if (!CommonUtils.isEmpty(list)) {
            mAdapter.addData(list);
            mAdapter.loadMoreComplete();
        }
        updateViewState(subType, needUpdate);
    }

    @Override
    public void onDataError(int subType, boolean needUpdate) {
        mAdapter.setNewData(new ArrayList<>());
        mAdapter.loadMoreComplete();
        updateViewState(subType, needUpdate);
    }

    @Override
    public int getItemCount() {
        return mAdapter == null ? 0 : mAdapter.getItemCount();
    }

    @Override
    public void onDownloadProgress(int position) {
        if (!isAdded()) {
            return;
        }
        mAdapter.notifyItemChanged(position);
    }

    @Override
    public void onDownloadFinish(int position, AssetInfo assetInfo) {
        if (!isAdded()) {
            return;
        }
        if (TextUtils.equals(mDownloadTag, assetInfo.getPackageId())) {
            mAdapter.selected(position);
            if (mEventListener != null) {
                mEventListener.onItemClick(assetInfo, false);
            }
        } else {
            mAdapter.notifyItemChanged(position);
        }
    }

    @Override
    public void onDownloadError(int position) {
        if (!isAdded()) {
            return;
        }
        mAdapter.notifyItemChanged(position);
    }

    private static class CompoundCaptionAdapter extends CommonAdapter {
        ImageLoader.Options mOptions = new ImageLoader.Options().centerCrop().roundedCorners(8);

        private CompoundCaptionAdapter() {
            super(R.layout.compound_caption_item);
        }


        @Override
        protected void convert(@NonNull BaseViewHolder helper, AssetInfo item) {
            ImageView ivCover = helper.getView(R.id.iv_cover);
            TextView tvAssetPurchased = helper.getView(R.id.tv_asset_purchased);
            if (AssetUtils.assetPurchasedVisible(tvAssetPurchased, mAssetSubType, item.isAuthorized())) {
                tvAssetPurchased.setVisibility(View.VISIBLE);
            } else {
                if (tvAssetPurchased != null) {
                    tvAssetPurchased.setVisibility(View.GONE);
                }
            }
            String coverPath = item.getCoverPath();
            if (!TextUtils.isEmpty(coverPath) && coverPath.startsWith("http") && ivCover instanceof SimpleDraweeView) {
                GenericDraweeHierarchyBuilder builder =
                        new GenericDraweeHierarchyBuilder(ivCover.getContext().getResources());
                builder.setActualImageScaleType(ScalingUtils.ScaleType.CENTER_CROP);

                PointF pf = new PointF(0.5f, 0.5f);
                builder.setActualImageFocusPoint(pf);

                GenericDraweeHierarchy hierachy = builder.build();
                ((SimpleDraweeView) ivCover).setHierarchy(hierachy);
                DraweeController controller = Fresco.newDraweeControllerBuilder()
                        .setUri(coverPath)
                        .setAutoPlayAnimations(true)
                        .setOldController(((SimpleDraweeView) ivCover).getController())
                        .build();
                ((SimpleDraweeView) ivCover).setController(controller);

            } else {
                ImageLoader.loadUrl(mContext, item.getCoverPath(), ivCover, mOptions);
            }

            if (helper.getAdapterPosition() == mSelectedPosition) {
                helper.itemView.setBackground(CommonUtils.getRadiusDrawable(mContext.getResources().getDimensionPixelOffset(R.dimen.dp_px_3),
                        mContext.getResources().getColor(R.color.color_fffc2b55), mContext.getResources().getDimensionPixelOffset(R.dimen.dp_px_12), Color.TRANSPARENT));
            } else {
                helper.itemView.setBackground(CommonUtils.getRadiusDrawable(mContext.getResources().getDimensionPixelOffset(R.dimen.dp_px_3),
                        mContext.getResources().getColor(R.color.animate_sticker_bottom_bg),
                        mContext.getResources().getDimensionPixelOffset(R.dimen.dp_px_12), Color.TRANSPARENT));
            }

            ImageView ivDownload = helper.getView(R.id.iv_downloading);
            if (!item.isHadDownloaded() || item.needUpdate()) {
                ImageLoader.loadUrl(mContext, R.mipmap.downloading, ivDownload);
                int progress = item.getDownloadProgress();
                if (progress >= 0 && progress < 100) {
                    ivDownload.setVisibility(View.VISIBLE);
                    ivCover.setVisibility(View.GONE);
                } else {
                    ivDownload.setVisibility(View.GONE);
                    ivCover.setVisibility(View.VISIBLE);
                }
            } else {
                ivDownload.setVisibility(View.GONE);
                ivCover.setVisibility(View.VISIBLE);
            }
        }
    }
}
