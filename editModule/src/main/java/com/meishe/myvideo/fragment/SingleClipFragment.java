package com.meishe.myvideo.fragment;

import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.Nullable;

import com.meicam.sdk.NvsLiveWindow;
import com.meicam.sdk.NvsStreamingContext;
import com.meicam.sdk.NvsTimeline;
import com.meishe.base.manager.AppManager;
import com.meishe.base.model.BaseFragment;
import com.meishe.base.utils.LogUtils;
import com.meishe.base.utils.ScreenUtils;
import com.meishe.engine.EditorEngine;
import com.meishe.engine.EngineCallbackManager;
import com.meishe.engine.asset.bean.AssetInfo;
import com.meishe.engine.bean.MeicamTimeline;
import com.meishe.engine.observer.EngineCallbackObserver;
import com.meishe.myvideo.R;


/**
 * Created by czl on 2018/5/29 0029.
 * 单片段编辑SingleClipFragment，封装liveWindow,供多个页面使用，避免代码重复
 * Single fragment editing SingleClipFragment, encapsulate liveWindow for multiple pages, avoid code duplication
 */
public class SingleClipFragment extends BaseFragment {
    private NvsLiveWindow mLiveWindow;
    private NvsStreamingContext mStreamingContext;
    private MeicamTimeline mTimeline;
    private PlayEventListener mPlayEventListener;
    private onFragmentEvent mFragmentEvent;
    private EngineCallbackObserver mCallbackObserver;

    public SingleClipFragment() {
    }

    @Override
    protected int bindLayout() {
        return R.layout.fragment_single_clip;
    }

    @Override
    protected void onLazyLoad() {

    }

    @Override
    protected void initView(View rootView) {
        mLiveWindow = rootView.findViewById(R.id.liveWindow);
    }

    @Override
    protected void initData() {
        mStreamingContext = NvsStreamingContext.getInstance();
        if (mFragmentEvent != null) {
            mFragmentEvent.onInitData();
        }
        connectTimelineWithLiveWindow();
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EngineCallbackManager.get().registerCallbackObserver(mCallbackObserver = new EngineCallbackObserver() {
            @Override
            public boolean isActive() {
                return isVisible() && getActivity() != null && getActivity().equals(AppManager.getInstance().currentActivity());
            }

            @Override
            public void onPlaybackStopped(NvsTimeline nvsTimeline) {
                if (mPlayEventListener != null) {
                    mPlayEventListener.playStopped(nvsTimeline);
                }
            }

            @Override
            public void onPlaybackEOF(NvsTimeline nvsTimeline) {
                if (mPlayEventListener != null) {
                    mPlayEventListener.playBackEOF(nvsTimeline);
                }
            }

            @Override
            public void onPlaybackTimelinePosition(NvsTimeline nvsTimeline, long l) {
                if (mPlayEventListener != null) {
                    mPlayEventListener.playbackTimelinePosition(nvsTimeline, l);
                }
            }

            @Override
            public void onStreamingEngineStateChanged(int i) {
                if (mPlayEventListener != null) {
                    mPlayEventListener.streamingEngineStateChanged(i);
                }
            }
        });
    }

    /**
     * 设置播放事件监听
     * Set play event listening
     *
     * @param playEventListener the play event listener
     */
    public void setPlayEventListener(PlayEventListener playEventListener) {
        this.mPlayEventListener = playEventListener;
    }

    /**
     * 页面加载事件监听
     * Page load event listening
     *
     * @param onFragmentEvent The fragment load listener
     */
    public void setFragmentEventListener(onFragmentEvent onFragmentEvent) {
        this.mFragmentEvent = onFragmentEvent;
    }

    /**
     * 设置livewindow的宽高
     * set livewindow width and height
     *
     * @param titleHeight  标题栏的高度。 The height of title bar
     * @param bottomHeight 底部控件的高度。The height of bottom view height
     */
    public void setLiveWindowRatio(int titleHeight, int bottomHeight) {
        MeicamTimeline meicamTimeline = EditorEngine.getInstance().getCurrentTimeline();
        if (meicamTimeline == null) {
            return;
        }
        setLiveWindowRatio(meicamTimeline.getMakeRatio(), titleHeight, bottomHeight);
    }

    private void setLiveWindowRatio(int ratio, int titleHeight, int bottomHeight) {
        ViewGroup.LayoutParams layoutParams = mLiveWindow.getLayoutParams();
        int statusHeight = ScreenUtils.getStatusBarHeight();
        int screenW = ScreenUtils.getScreenWidth();
        int screenH = ScreenUtils.getScreenHeight();
        int newH = screenH - titleHeight - bottomHeight - statusHeight;
        // 16:9
        if (ratio == AssetInfo.AspectRatio_16v9) {
            layoutParams.width = screenW;
            layoutParams.height = (int) (screenW * 9.0 / 16);
        } else if (ratio == AssetInfo.AspectRatio_1v1) {
            if (newH < screenW) {
                layoutParams.width = newH;
                layoutParams.height = newH;
            } else {
                layoutParams.width = screenW;
                layoutParams.height = screenW;
            }
        } else if (ratio == AssetInfo.AspectRatio_9v16) {
            layoutParams.width = (int) (newH * 9.0 / 16);
            layoutParams.height = newH;
        } else if (ratio == AssetInfo.AspectRatio_3v4) {
            layoutParams.width = (int) (newH * 3.0 / 4);
            layoutParams.height = newH;
        } else if (ratio == AssetInfo.AspectRatio_4v3) {
            layoutParams.width = screenW;
            layoutParams.height = (int) (screenW * 3.0 / 4);
        } else {
            layoutParams.width = screenW;
            layoutParams.height = (int) (screenW * 9.0 / 16);
        }
        mLiveWindow.setLayoutParams(layoutParams);
        mLiveWindow.setFillMode(NvsLiveWindow.FILLMODE_PRESERVEASPECTFIT);
    }

    /**
     * Sets timeline.
     * 设置时间
     *
     * @param timeline the timeline
     */
    public void setTimeline(MeicamTimeline timeline) {
        this.mTimeline = timeline;
    }

    /**
     * Play video.
     * 播放视频
     *
     * @param startTime the start time
     * @param endTime   the end time
     */
    public void playVideo(long startTime, long endTime) {
        mTimeline.playBack(mStreamingContext, startTime, endTime);
    }

    /**
     * Connect timeline with live window.
     * 连接时间线与活动窗口
     */
    public void connectTimelineWithLiveWindow() {
        if (mStreamingContext == null || mTimeline == null || mLiveWindow == null) {
            return;
        }
        mTimeline.connectToLiveWindow(mStreamingContext, mLiveWindow);
    }


    /**
     * Seek timeline.
     * 寻求时间表
     *
     * @param timestamp    the timestamp 当前时间线；Current timeline
     * @param seekShowMode the seek show mode 图像预览模式preview mode
     */
    public void seekTimeline(long timestamp, int seekShowMode) {
        mTimeline.seekTimeline(mStreamingContext, timestamp, seekShowMode);
    }


    /**
     * Stop engine.
     * 停止引擎
     */
    public void stopEngine() {
        if (mStreamingContext != null) {
            mStreamingContext.stop();
        }
    }


    @Override
    public void onDestroy() {
        super.onDestroy();
        LogUtils.e("onDestroy");
        stopEngine();
        EngineCallbackManager.get().unregisterCallbackObserver(mCallbackObserver);
    }

    /**
     * The interface Video fragment listener.
     * 接口视频片段监听器
     */

    public static abstract class PlayEventListener {
        /**
         * Play back eof.
         * 回放eof
         *
         * @param timeline the timeline
         */
        protected void playBackEOF(NvsTimeline timeline) {
        }

        /**
         * Play stopped.
         * 停止播放
         *
         * @param timeline the timeline
         */
        protected void playStopped(NvsTimeline timeline) {
        }

        /**
         * Playback timeline position.
         * 播放时间轴的位置
         *
         * @param timeline the timeline
         * @param stamp    the stamp
         */
        protected void playbackTimelinePosition(NvsTimeline timeline, long stamp) {
        }

        /**
         * Streaming engine state changed.
         * 流媒体引擎状态改变
         *
         * @param state the state
         */
        protected void streamingEngineStateChanged(int state) {
        }
    }

    public interface onFragmentEvent {
        /**
         * 初始化
         * On init data.
         */
        void onInitData();
    }
}
