package com.meishe.myvideo.fragment;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.TextView;

import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewpager.widget.ViewPager;

import com.meishe.base.adapter.CommonFragmentAdapter;
import com.meishe.base.bean.MediaData;
import com.meishe.base.bean.MediaSection;
import com.meishe.base.bean.MediaTag;
import com.meishe.base.model.BaseFragment;
import com.meishe.base.utils.ScreenUtils;
import com.meishe.base.utils.SizeUtils;
import com.meishe.base.utils.ToastUtils;
import com.meishe.base.utils.Utils;
import com.meishe.engine.util.ConvertFileManager;
import com.meishe.engine.util.IConvertManager;
import com.meishe.engine.util.WhiteList;
import com.meishe.engine.view.ConvertProgressPop;
import com.meishe.myvideo.R;
import com.meishe.myvideo.activity.DraftEditActivity;
import com.meishe.myvideo.activity.MaterialSelectActivity;
import com.meishe.myvideo.fragment.adapter.BaseSelectAdapter;
import com.meishe.myvideo.fragment.adapter.MaterialSelectAdapter;
import com.meishe.myvideo.fragment.adapter.MaterialSelectPreviewAdapter;
import com.meishe.myvideo.util.ConfigUtil;
import com.meishe.third.adpater.BaseQuickAdapter;
import com.meishe.third.adpater.decoration.GridSectionAverageGapItemDecoration;
import com.meishe.third.tablayout.SlidingTabLayout;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static com.meishe.logic.constant.PagerConstants.BUNDLE_DATA;
import static com.meishe.logic.constant.PagerConstants.FROM_DRAFT_EDIT;
import static com.meishe.logic.constant.PagerConstants.FROM_MATERIAL_SELECTED;
import static com.meishe.logic.constant.PagerConstants.FROM_PAGE;
import static com.meishe.logic.constant.PagerConstants.MEDIA_TYPE;
import static com.meishe.logic.constant.PagerConstants.SELECTED_NEED_PREVIEW;
import static com.meishe.logic.constant.PagerConstants.SELECTED_TYPE;
import static com.meishe.logic.constant.PagerConstants.TYPE_ADD_SOME;
import static com.meishe.logic.constant.PagerConstants.TYPE_DEFAULT;
import static com.meishe.logic.constant.PagerConstants.TYPE_ONE_FINISH;

/**
 * Media selection fragment
 */
public class MediaSelectFragment extends BaseFragment {
    private int mSelectedType;
    private int mMediaTagType;
    private boolean mNeedPreview;
    private List<Fragment> mFragmentList = new ArrayList<>(3);
    private List<String> mTabTitleList;
    private SlidingTabLayout mTabLayout;
    private ViewPager mViewPager;
    private ArrayList<MediaData> mSelectedMaterialList;
    private TextView mTvNext;

    public interface MediaChangeListener {
        void onMediaChange(MediaData mediaData);
        void onMediaPreView(MediaData mediaData);
    }

    public static MediaSelectFragment create(int mediaType, int selectedType, boolean needPreview,
                                          MediaChangeListener listener) {
        MediaSelectFragment fragment = new MediaSelectFragment();
        Bundle bundle = new Bundle();
        bundle.putInt(MEDIA_TYPE, mediaType);
        bundle.putInt(SELECTED_TYPE, selectedType);
        bundle.putBoolean(SELECTED_NEED_PREVIEW, needPreview);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int bindLayout() {
        return R.layout.fragment_media_select;
    }

    @Override
    protected void onLazyLoad() {

    }

    @Override
    protected void initView(View view) {
        mTabLayout = view.findViewById(R.id.tl_select_media);
        mViewPager = view.findViewById(R.id.vp_select_media);
        mTvNext = view.findViewById(R.id.tv_start_edit);
        // Initialize tab layout
        mTabTitleList = Arrays.asList(getResources().getStringArray(R.array.select_media));
        mFragmentList.clear();
        mFragmentList.add(MaterialSelectFragment.create(MediaData.TYPE_ALL, mSelectedType, true, mMediaListener));
        mFragmentList.add(MaterialSelectFragment.create(MediaData.TYPE_VIDEO, mSelectedType, true, mMediaListener));
        mFragmentList.add(MaterialSelectFragment.create(MediaData.TYPE_PHOTO, mSelectedType, true, mMediaListener));

        mViewPager.setOffscreenPageLimit(3);
        mViewPager.setAdapter(new CommonFragmentAdapter(getChildFragmentManager(), mFragmentList));
        mTabLayout.setViewPager(mViewPager, mTabTitleList);

        // Initialize media list
        int w = SizeUtils.dp2px(6);
//        FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(
//                ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
//        layoutParams.width = ScreenUtils.getScreenWidth() - 4 * w;
//        layoutParams.leftMargin = 2 * w;
//        layoutParams.rightMargin = 2 * w;
//
        int SPAN_COUNT = 4;
        int itemSize = (int) ((ScreenUtils.getScreenWidth() - 4 * w - (SPAN_COUNT - 1) * w) / SPAN_COUNT * 1f);
        
//        mRvMediaList.setLayoutParams(layoutParams);
        initListener();
    }

    @Override
    protected void initData() {
        mMediaTagType = MediaData.TYPE_VIDEO;
        if (getArguments() != null) {
            mMediaTagType = getArguments().getInt(MEDIA_TYPE);
            mSelectedType = getArguments().getInt(SELECTED_TYPE);
        }
        loadMediaList();
        mSelectedMaterialList = new ArrayList<>();
    }

    private void loadMediaList() {
        // TODO: Implement media list loading logic
        // This should load media files based on mMediaTagType
    }

    private void initListener() {
        mTvNext.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (Utils.isFastClick()) {
                    return;
                }
                if (!ConfigUtil.needConvert()) {
                    gotoNext();
                    return;
                }
                boolean needConvert = false;
                ConvertFileManager.ConvertParam convertParam = new ConvertFileManager.ConvertParam();
                for (MediaData mediaData : mSelectedMaterialList) {
                    String path = mediaData.getPath();
                    if (WhiteList.isCovert4KFileWhiteList(path)) {
                        needConvert = true;
                        convertParam.appendParam(path, "", false);
                    }
                }
                if (needConvert) {
                    convert(requireContext(), convertParam, new ConvertFileManager.EventListener() {
                        @Override
                        public void onConvertFinish(ConvertFileManager.ConvertParam convertParam, boolean convertSuccess) {
                            if (convertSuccess) {
                                Map<String, ConvertFileManager.ConvertParam.Param> paramMap =
                                        convertParam.getParamMap();
                                if (paramMap != null && !paramMap.isEmpty()) {
                                    for (MediaData mediaData : mSelectedMaterialList) {
                                        ConvertFileManager.ConvertParam.Param param = paramMap.get(mediaData.getPath());
                                        if (param != null) {
                                            String dstFile = param.getDstFile();
                                            if (!TextUtils.isEmpty(dstFile)) {
                                                mediaData.setPath(dstFile);
                                            }
                                        }
                                    }
                                }
                                gotoNext();
                            } else {
                                ToastUtils.make()
                                        .setGravity(Gravity.CENTER, 0, 0)
                                        .setDurationIsLong(false)
                                        .show(R.string.convert_failed);
                            }
                        }
                    });
                } else {
                    gotoNext();
                }
            }
        });
    }
    private void gotoNext() {
        if (mSelectedType == TYPE_DEFAULT) {
            Intent it = new Intent(requireActivity(), DraftEditActivity.class);
            it.putExtra(FROM_PAGE, FROM_MATERIAL_SELECTED);
            it.putParcelableArrayListExtra(BUNDLE_DATA, mSelectedMaterialList);
            startActivity(it);
        }
        requireActivity().finish();
    }

    private final MaterialSelectFragment.MediaChangeListener mMediaListener = new MaterialSelectFragment.MediaChangeListener() {
        @Override
        public void onMediaChange(MediaData mediaData) {
            dealMaterialSelected(mediaData);
            if (mSelectedMaterialList.size() > 0) {
                if (mTvNext.getVisibility() != View.VISIBLE) {
                    mTvNext.setVisibility(View.VISIBLE);
                }
            } else if (mTvNext.getVisibility() == View.VISIBLE) {
                mTvNext.setVisibility(View.GONE);
            }
        }

        @Override
        public void onMediaPreView(MediaData mediaData) {
        }
    };

    private void dealMaterialSelected(MediaData mediaData) {
        MediaData selectedMedia;
        if (mSelectedType == TYPE_ONE_FINISH || mSelectedMaterialList.size() == 0) {
            mSelectedMaterialList.clear();
            mSelectedMaterialList.add(selectedMedia = getMaterialWidthTag(mediaData));
            updateSelectedNumber(0, selectedMedia, false);
        } else {
            int currentType = ((MediaTag) mediaData.getTag()).getType();
            for (int i = 0; i < mSelectedMaterialList.size(); i++) {
                selectedMedia = mSelectedMaterialList.get(i);
                if (selectedMedia.getId() == mediaData.getId()) {
                    selectedMedia.setState(mediaData.isState());
                    if (!selectedMedia.isState()) {
                        MediaTag[] mediaTags = (MediaTag[]) selectedMedia.getTag();
                        MediaTag tag;
                        for (int j = 0; j < mFragmentList.size(); j++) {
                            tag = mediaTags[j];
                            if (tag.getType() == currentType) {
                                /*
                                 * 当前页面已经取消过了
                                 * The current page has been cancelled
                                 * */
                                continue;
                            }
                            /*
                             * 通知其他子页面，取消选中
                             * Notify other subpages to deselect them
                             * */
                            ((MaterialSelectFragment) mFragmentList.get(j)).dealSelectedState(tag.getIndex(), false);
                        }
                        /*
                         * 未选中，则在选中集合中删除
                         * Unchecked, the selected collection is deleted
                         * */
                        mSelectedMaterialList.remove(i);
                        updateSelectedNumber(i, selectedMedia, true);
                        return;
                    }
                }
            }
            /*
             * 没有选中，则添加选中集合。
             * If not selected, the selected collection is added.
             * */
            mSelectedMaterialList.add(selectedMedia = getMaterialWidthTag(mediaData));
            updateSelectedNumber(mSelectedMaterialList.size() - 1, selectedMedia, false);
        }
    }
    private MediaData getMaterialWidthTag(MediaData mediaData) {
        MediaTag[] mediaTags = new MediaTag[mFragmentList.size()];
        MediaTag tag = (MediaTag) mediaData.getTag();
        //如果为空，肯定有问题检查之 If it is empty, there must be a problem checking it.
        //获取mediaData中的tag，并获取其他子页面的tag Gets the tags in the mediaData and the tags for the other child pages.
        if (tag.getType() == MediaData.TYPE_ALL) {
            mediaTags[0] = tag;
            mediaTags[1] = ((MaterialSelectFragment) mFragmentList.get(1)).dealSelected(mediaData.getThumbPath());
            mediaTags[2] = ((MaterialSelectFragment) mFragmentList.get(2)).dealSelected(mediaData.getThumbPath());
        } else if (tag.getType() == MediaData.TYPE_VIDEO) {
            mediaTags[1] = tag;
            mediaTags[0] = ((MaterialSelectFragment) mFragmentList.get(0)).dealSelected(mediaData.getThumbPath());
            mediaTags[2] = ((MaterialSelectFragment) mFragmentList.get(2)).dealSelected(mediaData.getThumbPath());
        } else if (tag.getType() == MediaData.TYPE_PHOTO) {
            mediaTags[2] = tag;
            mediaTags[0] = ((MaterialSelectFragment) mFragmentList.get(0)).dealSelected(mediaData.getThumbPath());
            mediaTags[1] = ((MaterialSelectFragment) mFragmentList.get(1)).dealSelected(mediaData.getThumbPath());
        }
        MediaData newData = mediaData.copy();
        newData.setTag(mediaTags);
        return newData;
    }
    private void updateSelectedNumber(int startIndex, MediaData data, boolean useSelectedListTag) {
        MediaTag[] mediaTags = (MediaTag[]) data.getTag();
        MediaTag tag;
        for (int i = startIndex; i < mSelectedMaterialList.size(); i++) {
            if (useSelectedListTag) {
                mediaTags = (MediaTag[]) mSelectedMaterialList.get(i).getTag();
            }
            for (int j = 0; j < mFragmentList.size(); j++) {
                tag = mediaTags[j];
                /*
                 * 更新选中索引
                 * Update selected index
                 * */
                ((MaterialSelectFragment) mFragmentList.get(j)).updateSelectedNumber(tag.getIndex(), i + 1);
            }
        }
    }
    private void convert(Context context, ConvertFileManager.ConvertParam convertParam, ConvertFileManager.EventListener listener) {
        ConvertProgressPop.create(context, convertParam, listener).show();
    }
} 