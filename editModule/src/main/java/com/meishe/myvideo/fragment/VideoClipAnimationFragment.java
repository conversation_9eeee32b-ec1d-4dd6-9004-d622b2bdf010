package com.meishe.myvideo.fragment;

import android.content.res.ColorStateList;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import androidx.fragment.app.FragmentPagerAdapter;

import com.google.android.material.tabs.TabLayout;
import com.meishe.base.adapter.CommonFragmentAdapter;
import com.meishe.base.model.BaseMvpFragment;
import com.meishe.base.utils.LogUtils;
import com.meishe.base.view.CustomViewPager;
import com.meishe.base.view.HorizontalSeekBar;
import com.meishe.business.assets.view.AssetsTypeTabView;
import com.meishe.engine.asset.bean.AssetInfo;
import com.meishe.engine.bean.AnimationData;
import com.meishe.engine.bean.CommonData;
import com.meishe.engine.bean.MeicamVideoClip;
import com.meishe.myvideo.R;
import com.meishe.myvideo.event.MessageEvent;
import com.meishe.myvideo.fragment.iview.StickerAnimationView;
import com.meishe.myvideo.fragment.presenter.VideoClipAnimationPresenter;

import java.util.ArrayList;
import java.util.List;

import static com.meishe.engine.asset.bean.AssetInfo.ASSET_ANIMATION_GROUP;
import static com.meishe.engine.asset.bean.AssetInfo.ASSET_ANIMATION_IN;
import static com.meishe.engine.asset.bean.AssetInfo.ASSET_ANIMATION_OUT;
import static com.meishe.myvideo.event.MessageEvent.MESSAGE_TYPE_ADD_ANIMATION_IN;
import static com.meishe.myvideo.event.MessageEvent.MESSAGE_TYPE_CHANGE_ANIMATION_IN_DURATION;
import static com.meishe.myvideo.event.MessageEvent.MESSAGE_TYPE_CHANGE_ANIMATION_OUT_DURATION;

/**
 * All rights Reserved, Designed By www.meishesdk.com
 *
 * @Author: yangtailin
 * @CreateDate: 2020/6/17 10:30
 * @Description: 贴纸动画Fragment The sticker animation fragment.
 * @Copyright: www.meishesdk.com Inc. All rights reserved.
 */
public class VideoClipAnimationFragment extends BaseMvpFragment<VideoClipAnimationPresenter> implements StickerAnimationView {

    private static final int COMP_TAB_POSITION = 2;
    private TabLayout mTabLayout;
    private CustomViewPager mViewPager;
    private List<VideoClipAnimationStyleFragment> mFragmentList;
    private HorizontalSeekBar mSeekBar;
    private MeicamVideoClip mVideoClip;
    private AssetsTypeTabView mTabTypeView;
    private View mSeekBarLayout;
    private TextView mTvFast, mTvSlow;
    private OnEventListener mOnEventListener;
    private View mConfirmLayout;

    public void setOnEventListener(OnEventListener listener) {
        this.mOnEventListener = listener;
    }

    public VideoClipAnimationFragment() {
    }

    public static VideoClipAnimationFragment create(MeicamVideoClip videoClip) {
        VideoClipAnimationFragment fragment = new VideoClipAnimationFragment();
        fragment.mVideoClip = videoClip;
        return fragment;
    }

    @Override
    protected int bindLayout() {
        return R.layout.fragment_sticker_animation;
    }

    @Override
    protected void onLazyLoad() {

    }

    @Override
    protected void initView(View rootView) {
        mTabLayout = rootView.findViewById(R.id.tab_layout);
        TextView tvContent = rootView.findViewById(R.id.tv_content);
        mTabLayout.setTabRippleColor(ColorStateList.valueOf(getResources().getColor(R.color.color_ff181818)));
        mViewPager = rootView.findViewById(R.id.viewPager);
        mSeekBar = rootView.findViewById(R.id.seek_bar);
        mTvFast = rootView.findViewById(R.id.tv_animation_group_fast);
        mTvSlow = rootView.findViewById(R.id.tv_animation_group_slow);
        mSeekBarLayout = rootView.findViewById(R.id.seek_bar_layout);
        mTabTypeView = rootView.findViewById(R.id.ttv_tab_type);
        mConfirmLayout = rootView.findViewById(R.id.fl_confirm);
        tvContent.setText(getResources().getString(R.string.tab_name_video_animation));
        mViewPager.setScanScroll(false);
        mViewPager.setOffscreenPageLimit(3);
        mSeekBar.setTransformText(1000000, 1);
        initListener();
    }

    private void initFragment() {
        if (mFragmentList != null) {
            mFragmentList.clear();
        } else {
            mFragmentList = new ArrayList<>();
        }
        AnimationEventListener listener = new AnimationEventListener() {
            @Override
            public void onAnimationClick(AssetInfo info, int type) {
                MessageEvent.sendEvent(info, MESSAGE_TYPE_ADD_ANIMATION_IN);
                updateAnimation();
            }

            @Override
            public void updateSeekBar() {
                updateAnimation();
            }

            @Override
            public void onDataBack(List<AssetInfo> info, int subType) {
                if (info == null || info.size() <= 2) {
                    mSeekBarLayout.setVisibility(View.INVISIBLE);
                }
            }
        };
        mFragmentList.add(new VideoClipAnimationStyleFragment(ASSET_ANIMATION_IN, mVideoClip, listener));
        mFragmentList.add(new VideoClipAnimationStyleFragment(ASSET_ANIMATION_OUT, mVideoClip, listener));
        mFragmentList.add(new VideoClipAnimationStyleFragment(ASSET_ANIMATION_GROUP, mVideoClip, listener));
        setAdapter();
        final String[] tabs = getResources().getStringArray(R.array.menu_tab_sub_video_animation);
        mTabLayout.removeAllTabs();
        for (String tab : tabs) {
            mTabLayout.addTab(mTabLayout.newTab().setText(tab));
        }

    }

    private void setAdapter() {
        FragmentPagerAdapter adapter = new CommonFragmentAdapter(getChildFragmentManager(), mFragmentList);
        mViewPager.setAdapter(adapter);
        mTabLayout.setupWithViewPager(mViewPager);
    }

    private void initListener() {
        mSeekBar.setOnRangeListener(new HorizontalSeekBar.onRangeListener() {

            @Override
            public void onRange(float left, float right) {
                if (mVideoClip == null) {
                    return;
                }
                int leftValue = (int) (left * CommonData.TIMEBASE);
                //组合动画与出入动画互斥(出入动画不互斥)。前者默认时长0.5s后者0.6s
                // Composite animation and access animation are mutually exclusive (access animation is not mutually exclusive).
                // The default duration of the former is 0.5s and the latter is 0.6s
                AnimationData animationDate = mPresenter.getVideoClipAnimation();
                if (animationDate != null && (!animationDate.getIsAnimationIn() && (!TextUtils.isEmpty(animationDate.getPackageID())))) {
                    //组合动画最小值可设置成100ms
                    //The minimum value of composite animation can be set to 100ms
                    if (leftValue <= 0.1 * CommonData.TIMEBASE) {
                        leftValue = (int) (0.1 * CommonData.TIMEBASE);
                        mSeekBar.setLeftProgress(leftValue, true);
                    }
                }
            }

            @Override
            public void onLeftScrollEnd(float value) {
                int leftValue = (int) (value * CommonData.TIMEBASE);
                AnimationData animationDate = mPresenter.getVideoClipAnimation();
                if (animationDate != null && (!TextUtils.isEmpty(animationDate.getPackageID()))) {
                    AnimationData animationData = new AnimationData();
                    animationData.setInPoint(0);
                    animationData.setOutPoint(leftValue);
                    if (!animationDate.getIsAnimationIn()) {
                        animationData.setIsAnimationIn(false);
                    } else {
                        animationData.setIsAnimationIn(true);
                    }
                    MessageEvent.sendEvent(animationData, MESSAGE_TYPE_CHANGE_ANIMATION_IN_DURATION);
                }
            }

            @Override
            public void onRightScrollEnd(float value) {
                MeicamVideoClip meicamVideoClip = mPresenter.getVideoClip();
                if (meicamVideoClip == null) {
                    return;
                }
                long clipDuration = meicamVideoClip.getOutPoint() - meicamVideoClip.getInPoint();
                AnimationData animationDate = mPresenter.getVideoClipAnimation();
                AnimationData animationData = new AnimationData();
                animationData.setInPoint2(clipDuration - (long) (value * CommonData.TIMEBASE));
                animationData.setOutPoint2(clipDuration);
                if (animationDate != null) {
                    MessageEvent.sendEvent(animationData, MESSAGE_TYPE_CHANGE_ANIMATION_OUT_DURATION);
                }
            }
        });
        mTabLayout.addOnTabSelectedListener(new TabLayout.OnTabSelectedListener() {
            @Override
            public void onTabSelected(TabLayout.Tab tab) {
                int position = tab.getPosition();
                mTabTypeView.setSelected(0);
                if (mVideoClip == null) {
                    return;
                }
                VideoClipAnimationStyleFragment fragment = mFragmentList.get(position);
                fragment.onSubTypeClicked(0);
                AnimationData animation = mPresenter.getVideoClipAnimation();
                if (position == 0) {
                    if (!TextUtils.isEmpty(animation.getPackageID()) && animation.getIsAnimationIn()) {
                        fragment.selectedAnimation(animation.getPackageID());
                        mPresenter.play(ASSET_ANIMATION_IN);
                    } else {
                        displayAnimationProgress();
                    }
                    changeSeekBarView(true);
                } else if (position == 1) {
                    displayCombineAnimationView(false);
                    if (!TextUtils.isEmpty(animation.getPackageID2())) {
                        fragment.selectedAnimation(animation.getPackageID2());
                        mPresenter.play(ASSET_ANIMATION_OUT);
                    } else {
                        displayAnimationProgress();
                    }
                    changeSeekBarView(false);
                } else if (position == 2) {
                    if (!TextUtils.isEmpty(animation.getPackageID()) && (!animation.getIsAnimationIn())) {
                        fragment.selectedAnimation(animation.getPackageID());
                        mPresenter.play(ASSET_ANIMATION_GROUP);
                    } else {
                        displayAnimationProgress();
                    }
                }
            }

            @Override
            public void onTabUnselected(TabLayout.Tab tab) {

            }

            @Override
            public void onTabReselected(TabLayout.Tab tab) {

            }
        });


        mTabTypeView.setItemClickedListener(new AssetsTypeTabView.ItemClickedListener() {
            @Override
            public void onItemClicked(int position) {
                VideoClipAnimationStyleFragment fragment = mFragmentList.get(mViewPager.getCurrentItem());
                fragment.onSubTypeClicked(position);
            }
        });

        mConfirmLayout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mOnEventListener != null) {
                    mOnEventListener.onConfirm();
                }
            }
        });
    }

    /**
     * 修改seek bar 视图。切换到 哪个tab，哪个view在上面
     * Change Seek Bar View. Which tab to switch to and which view is on.
     *
     * @param leftLastMove Is the last left tab moved 是否最后移动的是左tab
     */
    private void changeSeekBarView(boolean leftLastMove) {
        if (mSeekBar == null) {
            return;
        }
        mSeekBar.changeSeekBarView(leftLastMove);
    }

    @Override
    protected void initData() {
        if (mVideoClip != null) {
            mSeekBar.setMaxProgress((int) (mVideoClip.getOutPoint() - mVideoClip.getInPoint()));
        }
        mPresenter.setVideoClip(mVideoClip);
        initFragment();
        showAnimationProgress();
    }

    /**
     * 展示动画进度
     * Show animation progress
     */
    private void showAnimationProgress() {
        if (mVideoClip == null) {
            return;
        }
        displayAnimationProgress();
    }

    /**
     * 更新片段
     * Update  clip
     *
     * @param videoClip the  clip
     */
    public void updateClip(MeicamVideoClip videoClip) {
        try {
            if (videoClip != null) {
                mPresenter.setVideoClip(videoClip);
                updateAnimation();
            }
            if (mSeekBar != null) {
                mSeekBar.setMaxProgress(mPresenter.getMaxProgress());
            }
        } catch (Exception e) {
            LogUtils.e(e);
        }
    }

    private void updateAnimation() {
        for (int index = 0; index < mFragmentList.size(); index++) {
            VideoClipAnimationStyleFragment fragment = mFragmentList.get(index);
            fragment.updateVideoClip(mVideoClip);
        }
        displayAnimationProgress();
    }

    /**
     * Display animation progress.
     * 显示动画时长进度
     */
    private void displayAnimationProgress() {
        AnimationData animationDate = mPresenter.getVideoClipAnimation();
        mSeekBar.reset();
        int tabPosition = mTabLayout.getSelectedTabPosition();

        if (animationDate != null) {

            boolean hasInAnimation = (!TextUtils.isEmpty(animationDate.getPackageID())) && animationDate.getIsAnimationIn();
            boolean hasOutAnimation = !TextUtils.isEmpty(animationDate.getPackageID2());
            boolean hasCompAnimation = (!TextUtils.isEmpty(animationDate.getPackageID())) && (!animationDate.getIsAnimationIn());

            if (!hasInAnimation && (!hasOutAnimation)) {
                //组合动画
                //Compound animation
                mSeekBar.setLeftMoveIcon(-1);
                mSeekBar.setRightMoveIcon(-1);
                if (hasCompAnimation && tabPosition == COMP_TAB_POSITION) {
                    mSeekBarLayout.setVisibility(View.VISIBLE);
                    displayCombineAnimationView(true);
                    if (mSeekBar.getLastLeftIconId() != R.mipmap.round_white) {
                        //已经重置过状态了没有必要重置了
                        //The status has been reset. There is no need to reset.
                        mSeekBar.reset();
                        mSeekBar.setMoveIconSize(15, 15);
                        mSeekBar.setLeftMoveIcon(R.mipmap.round_white);
                    }
                    mSeekBar.setLeftProgress((int) (animationDate.getOutPoint() - animationDate.getInPoint()), true);
                } else {
                    mSeekBarLayout.setVisibility(View.INVISIBLE);
                }
            } else {
                if (tabPosition == COMP_TAB_POSITION) {
                    mSeekBarLayout.setVisibility(View.INVISIBLE);
                } else {
                    mSeekBarLayout.setVisibility(View.VISIBLE);
                }
                displayCombineAnimationView(false);
                mSeekBar.setMoveIconSize(23, 37);
                if (!TextUtils.isEmpty(animationDate.getPackageID())) {
                    mSeekBar.setMoveIconLowPadding(7);
                    mSeekBar.setLeftMoveIcon(R.mipmap.icon_animation_in);
                    mSeekBar.setLeftProgress((int) (animationDate.getOutPoint() - animationDate.getInPoint()), false);
                }

                if (!TextUtils.isEmpty(animationDate.getPackageID2())) {
                    mSeekBar.setMoveIconLowPadding(7);
                    mSeekBar.setRightMoveIcon(R.mipmap.icon_animation_out);
                    mSeekBar.setRightProgress((int) (animationDate.getOutPoint2() - animationDate.getInPoint2()));
                }

                if (tabPosition == 0) {
                    changeSeekBarView(true);
                } else if (tabPosition == 1) {
                    changeSeekBarView(false);
                }
            }
        }
    }


    /**
     * 展示组合动画相关的视图
     *
     * @param display true display ,false not
     */
    private void displayCombineAnimationView(boolean display) {
        int visibility = display ? View.VISIBLE : View.INVISIBLE;
        mTvFast.setVisibility(visibility);
        mTvSlow.setVisibility(visibility);
    }

    public void setCurrPage(int currPage) {
        if (mViewPager != null) {
            mViewPager.setCurrentItem(currPage);
        }
    }

    public interface OnEventListener {
        /**
         * 确认
         * On confirm.
         */
        void onConfirm();
    }

    public interface AnimationEventListener {
        /**
         * 动画点击回调
         * On animation click.
         *
         * @param info the info item项
         * @param type the type 动画类型
         */
        void onAnimationClick(AssetInfo info, int type);

        /**
         * 更新进度条
         * Update seek bar.
         */
        void updateSeekBar();

        /**
         * 数据获取到的回调
         * On data back.
         *
         * @param info    the info
         * @param subType the sub type
         */
        void onDataBack(List<AssetInfo> info, int subType);
    }
}
