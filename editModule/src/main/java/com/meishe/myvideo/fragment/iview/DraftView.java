package com.meishe.myvideo.fragment.iview;

import com.meishe.base.model.IBaseView;
import com.meishe.draft.data.DraftData;

import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> LiHangZ<PERSON>
 * @CreateDate :2020/12/3 10:58
 * @Description :草稿View The draft view
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public interface DraftView extends IBaseView {
    /**
     * 草稿数据列表完成
     * Draft data list complete
     * @param dataList 草稿列表
     */
    void onEditingDataBack(List<DraftData> dataList);

    /**
     * Callback of login
     * <p>
     * 登录返回callback
     * @param isLogin 是否登录成功 Whether is login successful.
     */
    void onLoginBack(boolean isLogin);
}
