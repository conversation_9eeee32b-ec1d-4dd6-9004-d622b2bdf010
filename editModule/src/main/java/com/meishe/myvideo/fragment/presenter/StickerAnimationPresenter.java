package com.meishe.myvideo.fragment.presenter;

import com.meishe.base.model.Presenter;
import com.meishe.engine.EditorEngine;
import com.meishe.engine.asset.bean.AssetsConstants;
import com.meishe.engine.bean.CommonData;
import com.meishe.engine.bean.MeicamStickerClip;
import com.meishe.engine.bean.StickerAnimation;
import com.meishe.engine.command.StickerCommand;
import com.meishe.engine.interf.IBaseInfo;
import com.meishe.myvideo.fragment.iview.StickerAnimationView;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2021/10/08 11:40
 * @Description :贴纸动画presenter The sticker animation presenter.
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class StickerAnimationPresenter extends Presenter<StickerAnimationView> {
    /**
     * The constant ANIMATION_TYPE_IN.
     */
    public final static int ANIMATION_TYPE_IN = AssetsConstants.AssetsTypeData.STICKER_ANIMATION_IN.kind;
    /**
     * The constant ANIMATION_TYPE_OUT.
     */
    public final static int ANIMATION_TYPE_OUT = AssetsConstants.AssetsTypeData.STICKER_ANIMATION_OUT.kind;
    /**
     * The constant ANIMATION_TYPE_COMP.
     */
    public final static int ANIMATION_TYPE_COMP = AssetsConstants.AssetsTypeData.STICKER_ANIMATION_COMP.kind;

    private MeicamStickerClip mStickerClip;

    /**
     * Sets sticker clip.
     *
     * @param stickerClip the sticker clip
     */
    public void setStickerClip(MeicamStickerClip stickerClip) {
        this.mStickerClip = stickerClip;
    }

    /**
     * Handle sticker animation.
     *
     * @param info the info
     * @param type the type
     */
    public void handleStickerAnimation(IBaseInfo info, int type) {
        if (mStickerClip != null) {
            String stringType = getStringType(type);
            long duration = getAnimationDuration(type);
            long playPoint = getPlayPoint(type, duration);
            StickerCommand.setAnimation(mStickerClip, info.getPackageId(), stringType, duration / 1000);
            if (type == ANIMATION_TYPE_COMP) {
                EditorEngine.getInstance().playVideoLoop(playPoint, mStickerClip.getOutPoint());
            } else if (type == ANIMATION_TYPE_IN) {
                EditorEngine.getInstance().playVideo(playPoint, playPoint + duration);
            } else if (type == ANIMATION_TYPE_OUT) {
                EditorEngine.getInstance().playVideoRollBack(playPoint, playPoint + duration);
            }
        }
    }

    /**
     * Handle sticker animation duration.
     *
     * @param duration the duration
     * @param type     the type
     */
    public void handleStickerAnimationDuration(long duration, int type) {
        if (mStickerClip != null) {
            StickerCommand.changeAnimationDuration(mStickerClip, getStringType(type), duration);
            duration *= 1000;
            long playPoint = getPlayPoint(type, duration);
            if (type == ANIMATION_TYPE_COMP) {
                EditorEngine.getInstance().playVideoLoop(playPoint, mStickerClip.getOutPoint());
            } else if (type == ANIMATION_TYPE_IN) {
                EditorEngine.getInstance().playVideo(playPoint, playPoint + duration);
            } else if (type == ANIMATION_TYPE_OUT) {
                EditorEngine.getInstance().playVideoRollBack(playPoint, playPoint + duration);
            }
        }
    }

    private long getPlayPoint(int type, long animationDuration) {
        if (type == ANIMATION_TYPE_OUT) {
            return mStickerClip.getOutPoint() - animationDuration - 1;
        }
        return mStickerClip.getInPoint();
    }

    private long getAnimationDuration(int type) {
        if (type == ANIMATION_TYPE_IN) {
            return CommonData.STICKER_ANIMATION_IN_DEFAULT_DURATION;
        } else if (type == ANIMATION_TYPE_OUT) {
            return CommonData.STICKER_ANIMATION_OUT_DEFAULT_DURATION;
        } else if (type == ANIMATION_TYPE_COMP) {
            return CommonData.STICKER_ANIMATION_COMP_DEFAULT_DURATION;
        }
        return 0;
    }

    private String getStringType(int type) {
        if (type == ANIMATION_TYPE_IN) {
            return StickerAnimation.TYPE_ANIMATION_IN;
        } else if (type == ANIMATION_TYPE_OUT) {
            return StickerAnimation.TYPE_ANIMATION_OUT;
        } else if (type == ANIMATION_TYPE_COMP) {
            return StickerAnimation.TYPE_ANIMATION_COMP;
        }
        return "";
    }

    /**
     * Gets animation.
     *
     * @param type the type
     * @return the animation
     */
    public StickerAnimation getAnimation(int type) {
        return mStickerClip != null ? mStickerClip.getAnimation(getStringType(type)) : null;
    }

    /**
     * Gets max progress.
     *
     * @return the max progress
     */
    public int getMaxProgress() {
        return mStickerClip == null ? 0 : (int) (mStickerClip.getOutPoint() - mStickerClip.getInPoint()) / 1000;
    }

    /**
     * Play.
     *
     * @param type the type
     */
    public void play(int type) {
        if (type == ANIMATION_TYPE_COMP) {
            StickerAnimation animationComp = getAnimation(ANIMATION_TYPE_COMP);
            if (animationComp != null) {
                long playPoint = getPlayPoint(ANIMATION_TYPE_COMP, animationComp.getDuration() * 1000);
                EditorEngine.getInstance().playVideoLoop(playPoint, mStickerClip.getOutPoint());
            }
        } else if (type == ANIMATION_TYPE_IN) {
            StickerAnimation animationIn = getAnimation(ANIMATION_TYPE_IN);
            if (animationIn != null) {
                long duration = animationIn.getDuration() * 1000;
                long playPoint = getPlayPoint(ANIMATION_TYPE_IN, duration);
                EditorEngine.getInstance().playVideo(playPoint, playPoint + duration);
            }
        } else if (type == ANIMATION_TYPE_OUT) {
            StickerAnimation animationOut = getAnimation(ANIMATION_TYPE_OUT);
            if (animationOut != null) {
                long duration = animationOut.getDuration() * 1000;
                long playPoint = getPlayPoint(ANIMATION_TYPE_OUT, duration);
                EditorEngine.getInstance().playVideoRollBack(playPoint, playPoint + duration);
            }
        }
    }

    public void stop(int type) {
        long playPoint = mStickerClip.getInPoint();
        if (type == ANIMATION_TYPE_OUT) {
            StickerAnimation animationOut = getAnimation(ANIMATION_TYPE_OUT);
            long duration = 0;
            if (animationOut != null) {
                duration = animationOut.getDuration() * 1000;
            }
            playPoint = getPlayPoint(ANIMATION_TYPE_OUT, duration);
        }
        if (playPoint >= 0) {
            EditorEngine.getInstance().seekTimeline(playPoint, 0);
        } else {
            EditorEngine.getInstance().stop();
        }
    }
}
