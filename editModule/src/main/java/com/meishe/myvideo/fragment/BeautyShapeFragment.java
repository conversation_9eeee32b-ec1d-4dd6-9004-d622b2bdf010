package com.meishe.myvideo.fragment;

import static com.meishe.myvideo.fragment.presenter.BeautyPresenter.INVALID_VALUE;

import android.os.Bundle;
import android.view.View;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.SeekBar;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.meishe.base.manager.LinearLayoutManagerWrapper;
import com.meishe.base.model.BaseMvpFragment;
import com.meishe.base.utils.SizeUtils;
import com.meishe.base.view.decoration.ItemDecoration;
import com.meishe.engine.bean.MeicamVideoClip;
import com.meishe.engine.interf.IBaseInfo;
import com.meishe.myvideo.R;
import com.meishe.myvideo.fragment.adapter.BeautyShapeAdapter;
import com.meishe.myvideo.fragment.iview.BeautyView;
import com.meishe.myvideo.fragment.presenter.BeautyPresenter;
import com.meishe.myvideo.interfaces.BottomEventListener;
import com.meishe.myvideo.manager.MenuDataManager;
import com.meishe.myvideo.view.MYSeekBarTextView;
import com.meishe.third.adpater.BaseQuickAdapter;

/**
 * The type Beauty shape fragment.
 * 美型类
 *
 * <AUTHOR>
 * @Description 美型 The beauty shape fragment
 * @CreateDate 2020 /6/30 20:18
 */
public class BeautyShapeFragment extends BaseMvpFragment<BeautyPresenter> implements BeautyView, View.OnClickListener {
    private RecyclerView mRvShapeList;
    private MYSeekBarTextView mSeekBar;
    private IBaseInfo mBeautyItem;
    private TextView mTvEndText;
    private TextView mTvReset;
    private ImageView mIvApplyAll;
    private TextView mTvApplyAll;
    public static final String VIDEO_CLIP = "videoClip";
    private ImageView mIvConfirm;
    private CheckBox mCbBeautySwitch;
    private LinearLayout mLlSeekBarContainer;
    private BeautyShapeAdapter mAdapter;
    private BottomEventListener mEventListener;

    public BeautyShapeFragment() {
    }

    public BeautyShapeFragment(BottomEventListener listener) {
        mEventListener = listener;
    }

    @Override
    protected int bindLayout() {
        return R.layout.fragment_beauty_shape;
    }

    @Override
    protected void onLazyLoad() {

    }

    @Override
    protected void initView(View rootView) {
        mIvApplyAll = rootView.findViewById(R.id.iv_apply_all);
        mTvApplyAll = rootView.findViewById(R.id.tv_apply_all);
        mRvShapeList = rootView.findViewById(R.id.recyclerView);
        mSeekBar = rootView.findViewById(R.id.seek_bar);
        mTvEndText = rootView.findViewById(R.id.tv_end_text);
        mIvConfirm = rootView.findViewById(R.id.iv_confirm);
        mCbBeautySwitch = rootView.findViewById(R.id.cb_beauty_switch);
        mLlSeekBarContainer = rootView.findViewById(R.id.ll_seek_bar_parent);
        mTvReset = rootView.findViewById(R.id.tv_reset);
        LinearLayoutManager layoutManager = new LinearLayoutManagerWrapper(getContext(), LinearLayoutManager.HORIZONTAL, false);
        mRvShapeList.setLayoutManager(layoutManager);
        mAdapter = new BeautyShapeAdapter();
        mRvShapeList.setAdapter(mAdapter);
        mRvShapeList.setAlpha(0.5f);
        mRvShapeList.addItemDecoration(new ItemDecoration(SizeUtils.dp2px(3), SizeUtils.dp2px(3)));
        initListener();
    }

    private void initListener() {
        mTvReset.setOnClickListener(this);
        mIvApplyAll.setOnClickListener(this);
        mTvApplyAll.setOnClickListener(this);
        mIvConfirm.setOnClickListener(this);
        mCbBeautySwitch.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                if (isChecked) {
                    mCbBeautySwitch.setText(getResources().getString(R.string.fragment_beauty_shape_close));
                    mRvShapeList.setAlpha(1);
                    mLlSeekBarContainer.setVisibility(View.VISIBLE);
                    mPresenter.openShapeVideoFx();
                    mAdapter.selected(0);
                    selectItem(mAdapter.getItem(0));
                } else {
                    mAdapter.selected(-1);
                    mBeautyItem = null;
                    mSeekBar.setProgress(0);
                    mCbBeautySwitch.setText(getResources().getString(R.string.fragment_beauty_shape_open));
                    mRvShapeList.setAlpha(0.5f);
                    mLlSeekBarContainer.setVisibility(View.INVISIBLE);
                    mPresenter.closeShapeVideoFx();
                }
            }
        });
        mAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                if (!mCbBeautySwitch.isChecked()) {
                    return;
                }
                mAdapter.selected(position);
                selectItem(mAdapter.getItem(position));

            }
        });
        mSeekBar.setOnSeekBarChangeListener(new MYSeekBarTextView.OnSeekBarListener() {
            @Override
            public void onStopTrackingTouch(int progress, String name) {

            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {

            }

            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                if (!fromUser) {
                    return;
                }
                if (mBeautyItem == null) {
                    return;
                }
                float value = seekBar.getProgress() / 50f - 1;
                mPresenter.applyShapeVideoFx(mBeautyItem.getId(), mBeautyItem.getPackageId(), mBeautyItem.getEffectId(), value);
                String text = progress + "";
                mTvEndText.setText(text);
                mTvReset.setSelected(mPresenter.checkBeautyShapeStatus());
            }
        });
    }

    private void selectItem(IBaseInfo item) {
        if (item != null) {
            mBeautyItem = item;
            double videoFxStrength = mPresenter.getVideoFxStrength(mBeautyItem.getEffectId());
            if (videoFxStrength == INVALID_VALUE) {
                videoFxStrength = 0;
            }
            mPresenter.applyShapeVideoFx(mBeautyItem.getId(), mBeautyItem.getPackageId(), mBeautyItem.getEffectId(), videoFxStrength);
            mSeekBar.setProgress((int) ((videoFxStrength + 1) * 50));
            String text = String.valueOf(mSeekBar.getProgress());
            mTvEndText.setText(text);
        }
    }

    @Override
    protected void initData() {
        Bundle bundle = getArguments();
        if (bundle != null) {
            MeicamVideoClip meicamVideoClip = (MeicamVideoClip) bundle.getSerializable(VIDEO_CLIP);
            if (meicamVideoClip != null) {
                mPresenter.initVideoFx(meicamVideoClip);
            }
        }
        if (getContext() != null) {
            mAdapter.setNewData(MenuDataManager.getBeautyShapeDataList(getContext()));
        }
        mTvReset.setSelected(mPresenter.checkBeautyShapeStatus());
        mCbBeautySwitch.setChecked(mPresenter.checkShapeStatus());
        if (mCbBeautySwitch.isChecked()) {
            mAdapter.selected(0);
            selectItem(mAdapter.getItem(0));
        }
    }


    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.tv_reset) {
            mPresenter.resetShapeFx();
            mSeekBar.setProgress(50);
            mTvReset.setSelected(true);
        } else if (id == R.id.iv_apply_all || id == R.id.tv_apply_all) {
            mPresenter.applyAllClip(true);
        } else if (id == R.id.iv_confirm) {
            if (mEventListener != null) {
                mEventListener.onDismiss(true);
            }
        }
    }

}
