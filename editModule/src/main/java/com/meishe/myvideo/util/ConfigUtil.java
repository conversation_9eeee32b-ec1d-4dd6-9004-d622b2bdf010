package com.meishe.myvideo.util;

import com.meishe.base.utils.LogUtils;
import com.meishe.base.utils.Utils;
import com.meishe.engine.asset.AssetsManager;

import java.lang.reflect.Field;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2021/3/22 10:17
 * @Description :配置工具 The config util
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class ConfigUtil {
    public static final String BUILD_TYPE_TOB = "2B";
    public static final String BUILD_TYPE_TOC = "2C";
    /**
     * 是否要开启云互通功能
     */
    public static final boolean IS_NEED_CLOUD = false;

    public static boolean isToC() {
        String className = Utils.getApp().getPackageName() + ".BuildConfig";
        try {
            Class<?> aClass = Class.forName(className);
            Field flavor = aClass.getField("FLAVOR");
            Object obj = flavor.get(null);
            return (obj instanceof String && ((String)obj).contains(BUILD_TYPE_TOC));
        } catch (Exception e) {
            LogUtils.e(e);
        }
        return true;
    }

    public static boolean isNewAssets() {
        return AssetsManager.IS_NEW_ASSETS;
    }

    /**
     * Need convert boolean.
     * 是否需要转码的开关
     * @return the boolean
     */
    public static boolean needConvert(){
        return true;
    }
}
