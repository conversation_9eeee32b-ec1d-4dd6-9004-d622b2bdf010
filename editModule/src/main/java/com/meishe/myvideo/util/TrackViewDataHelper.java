package com.meishe.myvideo.util;

import android.text.TextUtils;

import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.LogUtils;
import com.meishe.engine.EditorEngine;
import com.meishe.engine.bean.ClipInfo;
import com.meishe.engine.bean.CommonData;
import com.meishe.engine.bean.MeicamAudioClip;
import com.meishe.engine.bean.MeicamAudioTrack;
import com.meishe.engine.bean.MeicamCaptionClip;
import com.meishe.engine.bean.MeicamCompoundCaptionClip;
import com.meishe.engine.bean.MeicamKeyFrame;
import com.meishe.engine.bean.MeicamStickerCaptionTrack;
import com.meishe.engine.bean.MeicamStickerClip;
import com.meishe.engine.bean.MeicamTimeline;
import com.meishe.engine.bean.MeicamTimelineVideoFilterAndAdjustClip;
import com.meishe.engine.bean.MeicamTimelineVideoFxClip;
import com.meishe.engine.bean.MeicamTimelineVideoFxTrack;
import com.meishe.engine.bean.MeicamVideoClip;
import com.meishe.engine.bean.MeicamVideoTrack;
import com.meishe.engine.constant.NvsConstants;
import com.meishe.engine.interf.IKeyFrameProcessor;
import com.meishe.myvideo.ui.bean.BaseUIClip;
import com.meishe.myvideo.ui.bean.KeyFrameInfo;
import com.meishe.myvideo.ui.trackview.bean.AudioClipProxy;
import com.meishe.myvideo.ui.trackview.bean.CaptionProxy;
import com.meishe.myvideo.ui.trackview.bean.CompoundCaptionProxy;
import com.meishe.myvideo.ui.trackview.bean.StickerProxy;
import com.meishe.myvideo.ui.trackview.bean.TimelineAdjustProxy;
import com.meishe.myvideo.ui.trackview.bean.TimelineFilterProxy;
import com.meishe.myvideo.ui.trackview.bean.TimelineVideoFxProxy;
import com.meishe.myvideo.ui.trackview.bean.VideoClipProxy;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * author：yangtailin on 2020/7/1 11:56
 */
public class TrackViewDataHelper {
    private static TrackViewDataHelper sTrackViewDataHelper;

    private TrackViewDataHelper() {
    }

    public static TrackViewDataHelper getInstance() {
        if (sTrackViewDataHelper == null) {
            synchronized (TrackViewDataHelper.class) {
                if (sTrackViewDataHelper == null) {
                    sTrackViewDataHelper = new TrackViewDataHelper();
                }
            }
        }
        return sTrackViewDataHelper;
    }

    public HashMap<Integer, List<BaseUIClip>> getTrackData(String type) {
        HashMap<Integer, List<BaseUIClip>> integerListHashMap = new HashMap<>();
        if (CommonData.CLIP_STICKER.equals(type)
                || CommonData.CLIP_CAPTION.equals(type)
                || CommonData.CLIP_COMPOUND_CAPTION.equals(type)) {
            return getCaptionAndStickerTrackData();
        } else if (CommonData.CLIP_TIMELINE_FX.equals(type)) {
            return getTimelineFxTrackData();
        } else if (CommonData.CLIP_AUDIO.equals(type)) {
            return getAudioTrackData();
        } else if (CommonData.CLIP_IMAGE.equals(type) || CommonData.CLIP_VIDEO.equals(type)) {
            return getPipTrackData();
        } else if (CommonData.CLIP_FILTER.equals(type) || CommonData.CLIP_ADJUST.equals(type)) {
            return getFilterAndAdjustData();
        }
        return integerListHashMap;
    }

    private HashMap<Integer, List<BaseUIClip>> getFilterAndAdjustData() {
        HashMap<Integer, List<BaseUIClip>> integerListHashMap = new HashMap<>();
        MeicamTimeline meicamTimeline = EditorEngine.getInstance().getCurrentTimeline();

        if (meicamTimeline != null) {
            int count = meicamTimeline.getFilterAndAdjustTimelineTracksCount();
            for (int i = 0; i < count; i++) {
                MeicamTimelineVideoFxTrack track = meicamTimeline.getFilterAndAdjustTimelineTrack(i);
                if (track == null) {
                    continue;
                }
                int trackIndex = track.getIndex();
                for (int j = 0; j < track.getFilterAndAdjustCount(); j++) {
                    MeicamTimelineVideoFilterAndAdjustClip clip = track.getFilterAndAdjustClip(j);
                    BaseUIClip proxy;
                    if (MeicamTimelineVideoFxClip.ClipFxType.SUB_TYPE_TIMELINE_FILTER.equals(clip.getType())) {
                        proxy = new TimelineFilterProxy(clip, trackIndex);
                    } else {
                        proxy = new TimelineAdjustProxy(clip, trackIndex);
                    }
                    proxy.setTrimIn(CommonData.DEFAULT_TRIM_IN);
                    proxy.setTrimOut(CommonData.DEFAULT_TRIM_IN + clip.getOutPoint() - clip.getInPoint());
                    if (integerListHashMap.containsKey(trackIndex)) {
                        List<BaseUIClip> baseUIClips = integerListHashMap.get(trackIndex);
                        if (baseUIClips != null) {
                            baseUIClips.add(proxy);
                        }
                    } else {
                        List<BaseUIClip> uiClips = new ArrayList<>();
                        uiClips.add(proxy);
                        integerListHashMap.put(trackIndex, uiClips);
                    }
                }
            }
        }
        return integerListHashMap;
    }

    private HashMap<Integer, List<BaseUIClip>> getAudioTrackData() {
        HashMap<Integer, List<BaseUIClip>> integerListHashMap = new HashMap<>();
        int count = EditorEngine.getInstance().getAudioTrackCount();
        for (int i = 0; i < count; i++) {
            MeicamAudioTrack audioTrack = EditorEngine.getInstance().getAudioTrack(i);
            if (audioTrack != null) {
                for (int j = 0; j < audioTrack.getClipCount(); j++) {
                    MeicamAudioClip recordAudioInfo = audioTrack.getAudioClip(j);
                    setTrackViewData(integerListHashMap, recordAudioInfo);
                }
            }
        }
        return integerListHashMap;
    }

    /**
     * 新增一个音乐片段view
     * Add audio BaseUIClip
     *
     * @param listHashMap the list hashmap
     * @param audioInfo the audio information
     * @param timeline the time林额
     * @return The hashmap
     */
    public HashMap<Integer, List<BaseUIClip>> addAudioBaseUIClip(HashMap<Integer, List<BaseUIClip>> listHashMap, BaseUIClip audioInfo, MeicamTimeline timeline) {
        MeicamAudioTrack nvsAudioTrack;
        boolean isAppend = false;
        if (audioInfo == null) {
            return listHashMap;
        }
        if (timeline == null) {
            return listHashMap;
        }
        /*
         * 循环遍历所有的音乐轨道
         * Cycle through all music tracks
         */
        int audioTrackCount = timeline.getAudioTrackCount();
        for (int i = 0; i < audioTrackCount; i++) {
            nvsAudioTrack = timeline.getAudioTrack(i);
            int clipCount = nvsAudioTrack.getClipCount();
            //最后一个音频片段 Last audio clip
            MeicamAudioClip audioClip = nvsAudioTrack.getAudioClip(clipCount - 1);
            //如果新录入的时间点大于等于最后一个音频最后时间点，就直接插入到该音频轨道内
            // If the newly entered time point is greater than or equal to the last audio time point,
            // it will be directly inserted into the audio track
            if (audioClip == null || audioInfo.getInPoint() >= audioClip.getOutPoint()) {
                isAppend = true;
                audioInfo.setTrackIndex(i);
                break;
            }
        }
        //如果所有的轨道都不能添加，就新建轨道添加，这里面包含音频轨道为0的时候
        // If all tracks cannot be added, create a new track to add.
        // This includes the time when the audio track is 0
        if (!isAppend) {
            audioInfo.setTrackIndex(audioTrackCount);
        }

        return setTrackViewData(listHashMap, audioInfo);
    }

    /**
     * 移除一个音乐片段view
     * Remove Audio BaseUIClip
     *
     * @param listHashMap The list hashmap
     * @param meicamAudioClip The audio clip
     * @return The data
     */
    public HashMap<Integer, List<BaseUIClip>> removeAudioBaseUIClip(HashMap<Integer, List<BaseUIClip>> listHashMap, BaseUIClip meicamAudioClip) {
        for (int i = 0; i < listHashMap.size(); i++) {
            List<BaseUIClip> baseUIClipList = listHashMap.get(i);
            if (!CommonUtils.isEmpty(baseUIClipList)) {
                for (int j = 0; j < baseUIClipList.size(); j++) {
                    BaseUIClip baseUIClip = baseUIClipList.get(j);
                    if (baseUIClip.getTrackIndex() == meicamAudioClip.getTrackIndex() && baseUIClip.getInPoint() == meicamAudioClip.getInPoint()) {
                        baseUIClipList.remove(baseUIClip);
                        return listHashMap;
                    }
                }
            }
        }
        return listHashMap;
    }

    /**
     * 赋值view属性
     * Set track view data
     *
     * @param listHashMap The list hashmap
     * @param mBaseUIClip the UI clip
     * @return The data
     */
    public HashMap<Integer, List<BaseUIClip>> setTrackViewData(HashMap<Integer, List<BaseUIClip>> listHashMap, BaseUIClip mBaseUIClip) {

        List<BaseUIClip> baseUIClipList;
        int trackIndex = mBaseUIClip.getTrackIndex();
        if (listHashMap.get(trackIndex) == null) {
            baseUIClipList = new ArrayList<>();
        } else {
            baseUIClipList = listHashMap.get(trackIndex);
        }
        if (baseUIClipList != null) {
            baseUIClipList.add(mBaseUIClip);
            listHashMap.put(trackIndex, baseUIClipList);
        }
        return listHashMap;
    }

    /**
     * 赋值view属性
     *
     * @param listHashMap The list hashmap
     * @param recordAudioInfo The Record audio Info
     * @return The data
     */
    public HashMap<Integer, List<BaseUIClip>> setTrackViewData(HashMap<Integer, List<BaseUIClip>> listHashMap, MeicamAudioClip recordAudioInfo) {

        List<BaseUIClip> baseUIClipList;
        int trackIndex = recordAudioInfo.getTrackIndex();
        if (listHashMap.get(trackIndex) == null) {
            baseUIClipList = new ArrayList<>();
        } else {
            baseUIClipList = listHashMap.get(trackIndex);
        }
        BaseUIClip mBaseUIClip = new AudioClipProxy(recordAudioInfo, trackIndex);
        if (baseUIClipList != null) {
            baseUIClipList.add(mBaseUIClip);
            listHashMap.put(trackIndex, baseUIClipList);
        }
        return listHashMap;
    }


    /**
     * 获取音频顺序view
     * Get Draw Text
     *
     * @param listHashMap the list hash map
     * @return the draw text
     */
    public int getDrawText(HashMap<Integer, List<BaseUIClip>> listHashMap) {
        int max = 0;
        for (int i = 0; i < listHashMap.size(); i++) {
            List<BaseUIClip> baseUIClipList = listHashMap.get(i);
            if (baseUIClipList != null) {
                for (int j = 0; j < baseUIClipList.size(); j++) {
                    BaseUIClip baseUIClip = baseUIClipList.get(j);
                    if (baseUIClip.getSubType() == MeicamAudioClip.AUDIO_RECORD_FILE) {
                        String text = baseUIClip.getDisplayName();
                        if (!TextUtils.isEmpty(text)) {
                            text.replaceAll(" ", "");
                            String split = text.substring(2);
                            if (!TextUtils.isEmpty(split)) {
                                try {
                                    int num = Integer.parseInt(split);
                                    if (num > max) {
                                        max = num;
                                    }
                                } catch (Exception e) {
                                    LogUtils.e(e.toString());
                                }

                            }
                        }
                    }
                }
            }
        }
        return max + 1;
    }


    /**
     * 获取调节顺序view
     * Get Adjust Draw Text
     *
     * @return the adjust draw text
     */
    public int getAdjustDrawText() {
        HashMap<Integer, List<BaseUIClip>> listHashMap = TrackViewDataHelper.getInstance().getTrackData(CommonData.CLIP_FILTER);
        int max = 0;
        for (int i = 0; i < listHashMap.size(); i++) {
            List<BaseUIClip> baseUIClipList = listHashMap.get(i);
            if (baseUIClipList != null) {
                for (int j = 0; j < baseUIClipList.size(); j++) {
                    BaseUIClip baseUIClip = baseUIClipList.get(j);
                    if (CommonData.CLIP_ADJUST.equals(baseUIClip.getType())) {
                        String text = baseUIClip.getDisplayName();
                        if (!TextUtils.isEmpty(text)) {
                            text.replaceAll(" ", "");
                            String split = text.substring(2);
                            if (!TextUtils.isEmpty(split)) {
                                try {
                                    int num = Integer.parseInt(split);
                                    if (num > max) {
                                        max = num;
                                    }
                                } catch (Exception e) {
                                    LogUtils.e(e.toString());
                                }

                            }
                        }
                    }

                }
            }
        }
        return max + 1;
    }

    public HashMap<Integer, List<BaseUIClip>> getPipTrackData() {
        MeicamTimeline timeline = EditorEngine.getInstance().getCurrentTimeline();
        HashMap<Integer, List<BaseUIClip>> integerListHashMap = new HashMap<>();
        if (timeline == null) {
            LogUtils.e("timeline is null");
            return integerListHashMap;
        }
        int count = timeline.videoTrackCount();
        if (count < 1) {
            return integerListHashMap;
        }

        for (int i = 1; i < count; i++) {
            MeicamVideoTrack meicamVideoTrack = timeline.getVideoTrack(i);
            if (meicamVideoTrack.getIndex() == 0) {
                continue;
            }
            int clipCount = meicamVideoTrack.getClipCount();
            if (clipCount <= 0) {
                continue;
            }
            int trackIndex = meicamVideoTrack.getIndex() - 1;
            for (int j = 0; j < clipCount; j++) {
                MeicamVideoClip meicamVideoClip = meicamVideoTrack.getVideoClip(j);
                BaseUIClip baseUIVideoClip = new VideoClipProxy(meicamVideoClip, trackIndex);
                baseUIVideoClip.setKeyFrameInfo(getKeyFrameInfo(meicamVideoClip.findPropertyVideoFx(), NvsConstants.KEY_CROPPER_TRANS_X));
                if (integerListHashMap.containsKey(trackIndex)) {
                    List<BaseUIClip> baseUIClips = integerListHashMap.get(trackIndex);
                    if (baseUIClips != null) {
                        baseUIClips.add(baseUIVideoClip);
                    }
                } else {
                    List<BaseUIClip> uiClips = new ArrayList<>();
                    uiClips.add(baseUIVideoClip);
                    integerListHashMap.put(trackIndex, uiClips);
                }
            }
        }
        return integerListHashMap;
    }

    public HashMap<Integer, List<BaseUIClip>> getTimelineFxTrackData() {
        MeicamTimeline timeline = EditorEngine.getInstance().getCurrentTimeline();
        HashMap<Integer, List<BaseUIClip>> integerListHashMap = new HashMap<>();
        if (timeline == null) {
            LogUtils.e("timeline is null");
            return integerListHashMap;
        }
        int count = timeline.getTimelineFxTrackCount();
        for (int i = 0; i < count; i++) {
            MeicamTimelineVideoFxTrack fxTrack = timeline.getTimelineFxTrack(i);
            if (fxTrack != null) {
                int clipCount = fxTrack.getClipCount();
                List<BaseUIClip> baseUIClipList = new ArrayList<>();
                if (clipCount > 0) {
                    for (int index = 0; index < clipCount; index++) {
                        MeicamTimelineVideoFxClip clip = fxTrack.getClip(index);
                        BaseUIClip baseUIVideoClip = new TimelineVideoFxProxy(clip, i);
                        baseUIVideoClip.setTrimIn(CommonData.DEFAULT_TRIM_IN);
                        baseUIVideoClip.setTrimOut(CommonData.DEFAULT_TRIM_IN + clip.getOutPoint() - clip.getInPoint());
                        baseUIClipList.add(baseUIVideoClip);
                    }
                }
                integerListHashMap.put(i, baseUIClipList);
            }
        }
        return integerListHashMap;
    }

    public HashMap<Integer, List<BaseUIClip>> getCaptionAndStickerTrackData() {
        MeicamTimeline timeline = EditorEngine.getInstance().getCurrentTimeline();
        HashMap<Integer, List<BaseUIClip>> integerListHashMap = new HashMap<>();
        if (timeline == null) {
            LogUtils.e("timeline is null");
            return integerListHashMap;
        }
        int count = timeline.getStickerCaptionTrackCount();
        for (int index = 0; index < count; index++) {
            MeicamStickerCaptionTrack meicamStickerCaptionTrack = timeline.findStickCaptionTrack(index);
            int clipCount = meicamStickerCaptionTrack.getClipCount();
            int trackIndex = meicamStickerCaptionTrack.getIndex();
            for (int i = 0; i < clipCount; i++) {
                ClipInfo<?> clipInfo = meicamStickerCaptionTrack.getCaptionStickerClip(i);
                BaseUIClip baseUIVideoClip = null;
                if (clipInfo instanceof MeicamStickerClip) {
                    baseUIVideoClip = new StickerProxy(((MeicamStickerClip) clipInfo), trackIndex);
                    baseUIVideoClip.setTrimIn(CommonData.DEFAULT_TRIM_IN);
                    baseUIVideoClip.setTrimOut(CommonData.DEFAULT_TRIM_IN + clipInfo.getOutPoint() - clipInfo.getInPoint());
                    baseUIVideoClip.setKeyFrameInfo(getKeyFrameInfo(baseUIVideoClip, null));
                } else if (clipInfo instanceof MeicamCaptionClip) {
                    baseUIVideoClip = new CaptionProxy(((MeicamCaptionClip) clipInfo), trackIndex);
                    baseUIVideoClip.setTrimIn(CommonData.DEFAULT_TRIM_IN);
                    baseUIVideoClip.setTrimOut(CommonData.DEFAULT_TRIM_IN + clipInfo.getOutPoint() - clipInfo.getInPoint());
                    baseUIVideoClip.setKeyFrameInfo(getKeyFrameInfo(baseUIVideoClip, null));
                } else if (clipInfo instanceof MeicamCompoundCaptionClip) {
                    baseUIVideoClip = new CompoundCaptionProxy(((MeicamCompoundCaptionClip) clipInfo), trackIndex);
                    baseUIVideoClip.setTrimIn(CommonData.DEFAULT_TRIM_IN);
                    baseUIVideoClip.setTrimOut(CommonData.DEFAULT_TRIM_IN + clipInfo.getOutPoint() - clipInfo.getInPoint());
                }

                if (integerListHashMap.containsKey(trackIndex)) {
                    List<BaseUIClip> baseUIClips = integerListHashMap.get(trackIndex);
                    if (baseUIClips != null) {
                        baseUIClips.add(baseUIVideoClip);
                    }
                } else {
                    List<BaseUIClip> uiClips = new ArrayList<>();
                    uiClips.add(baseUIVideoClip);
                    integerListHashMap.put(trackIndex, uiClips);
                }
            }
        }
        return integerListHashMap;
    }

    /**
     * 获取关键帧信息
     * Get key frame info
     *
     * @param clipInfo the clip info 片段信息
     * @param key the key 关键帧的关键字
     * @return KeyFrameInfo the key frame info
     */
    public KeyFrameInfo getKeyFrameInfo(IKeyFrameProcessor<?> clipInfo, String key) {
        KeyFrameInfo keyFrameInfo = new KeyFrameInfo();
        if (clipInfo != null) {
            Map<Long, MeicamKeyFrame> keyFrameMap = clipInfo.keyFrameProcessor().getKeyFrameMap(key);
            if (keyFrameMap != null) {
                for (Map.Entry<Long, MeicamKeyFrame> item : keyFrameMap.entrySet()) {
                    MeicamKeyFrame keyFrame = item.getValue();
                    keyFrameInfo.addKeyFrame(keyFrame.getAtTime());
                }
            }
        }
        return keyFrameInfo;
    }

  /*  public BaseUIClip getBaseUIClip(int trackIndex, ClipInfo clipInfo) {
        if (clipInfo != null) {
            BaseUIVideoClip baseUIVideoClip = new BaseUIVideoClip();
            baseUIVideoClip.setInPoint(clipInfo.getInPoint());
            baseUIVideoClip.setTrimIn(CommonData.DEFAULT_TRIM_IN);
            baseUIVideoClip.setTrimOut(CommonData.DEFAULT_TRIM_IN + clipInfo.getOutPoint() - clipInfo.getInPoint());
            baseUIVideoClip.setTrackIndex(trackIndex);
            baseUIVideoClip.setNvsObject(clipInfo.getObject());
            if (clipInfo instanceof MeicamStickerClip) {
                baseUIVideoClip.setType(CommonData.CLIP_STICKER);
            } else if (clipInfo instanceof MeicamCaptionClip) {
                baseUIVideoClip.setType(CommonData.CLIP_CAPTION);
                baseUIVideoClip.setText(((MeicamCaptionClip) clipInfo).getText());
            } else if (clipInfo instanceof MeicamCompoundCaptionClip) {
                baseUIVideoClip.setType(CommonData.CLIP_COMPOUND_CAPTION);
                baseUIVideoClip.setText(((MeicamCompoundCaptionClip) clipInfo).getCompoundCaptionItems().get(0).getText());
            }
            return baseUIVideoClip;
        }
        return null;
    }*/
}
