package com.meishe.myvideo.util;

import android.content.res.AssetFileDescriptor;
import android.database.Cursor;
import android.media.MediaMetadataRetriever;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.provider.MediaStore;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentActivity;
import androidx.loader.app.LoaderManager;
import androidx.loader.content.CursorLoader;
import androidx.loader.content.Loader;

import com.meishe.base.utils.AndroidVersionUtils;
import com.meishe.base.utils.LogUtils;
import com.meishe.base.utils.ThreadUtils;
import com.meishe.base.utils.Utils;
import com.meishe.myvideo.bean.MusicInfo;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

import static com.meishe.base.constants.Constants.MEDIA_TYPE_AUDIO;

/**
 * 1、获取媒体库所有音频 2、获取asset目录下的音频文件。获取到的时长是毫秒，设置到结构中会转化成微妙
 * 1. Get all audio files in the asset directory. The amount of time you get is milliseconds, which translates into subtlety when you set it in the structure
 */

public class AudioUtil {
    private static final String TAG = "AudioUtil";
    private final FragmentActivity mContext;
    private long videoMaxS = 0;
    private long videoMinS = 0;

    private static final Uri QUERY_URI = MediaStore.Files.getContentUri("external");
    /**
     * 过滤掉小于500毫秒的录音
     *
     * Filter out recordings smaller than 500 milliseconds
     */
    private static final int AUDIO_DURATION = 500;
    private static final String ORDER_BY = MediaStore.Files.FileColumns._ID + " DESC";
    private static final String DURATION = "duration";

    /**
     * 媒体文件数据库字段
     * Media file database field
     */
    private static String[] PROJECTION = {
            MediaStore.Files.FileColumns._ID,
            MediaStore.MediaColumns.DATA,
            MediaStore.MediaColumns.MIME_TYPE,
            MediaStore.MediaColumns.WIDTH,
            MediaStore.MediaColumns.HEIGHT,
            DURATION,
            MediaStore.MediaColumns.SIZE,
            MediaStore.Audio.Media.TITLE,
            MediaStore.Audio.Media.ARTIST};


    public AudioUtil(FragmentActivity context) {
        this.mContext = context;
    }

    /**
     * The interface Audio load listener.
     * 加载音频监听
     */
    public interface AudioLoadListener {
        /**
         * Load complete.
         * 完成加载
         *
         * @param audioList the audio list
         */
        void loadComplete(List<MusicInfo> audioList);
    }

    /**
     * 获取音乐列表
     * Get a list of  music
     *
     * @param loadAssets        boolean true load assets music ,false load local music
     * @param mediaLoadListener The listener
     */
    public void getAudioList(boolean loadAssets, final AudioLoadListener mediaLoadListener) {
        if (loadAssets) {
            getAssetsAudio(mediaLoadListener);
        } else {
            getLocalAudio(mediaLoadListener);
        }
    }

    /**
     * 获取本地音乐列表
     * Get a list of local music
     *
     * @param mediaLoadListener The listener
     */
    private void getLocalAudio(final AudioLoadListener mediaLoadListener) {
        //适配android10 以上音乐数据 Adapt to music data above android10
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            PROJECTION = new String[]{
                    MediaStore.Files.FileColumns._ID,
                    MediaStore.MediaColumns.DATA,
                    MediaStore.MediaColumns.MIME_TYPE,
                    MediaStore.MediaColumns.WIDTH,
                    MediaStore.MediaColumns.HEIGHT,
                    DURATION,
                    MediaStore.MediaColumns.SIZE,
                    MediaStore.Audio.Media.TITLE};
        }

        LoaderManager.getInstance(mContext).initLoader(MEDIA_TYPE_AUDIO, new Bundle(), new LoaderManager.LoaderCallbacks<Cursor>() {

            @NonNull
            @Override
            public Loader<Cursor> onCreateLoader(int id, @Nullable Bundle args) {
                String audioCondition = getSelectionArgsForSingleMediaCondition(getDurationCondition(0, AUDIO_DURATION));
                String[] MEDIA_TYPE_AUDIO = new String[]{String.valueOf(MediaStore.Files.FileColumns.MEDIA_TYPE_AUDIO)};
                return new CursorLoader(
                        mContext, QUERY_URI, PROJECTION, audioCondition, MEDIA_TYPE_AUDIO
                        , ORDER_BY);
            }

            @Override
            public void onLoadFinished(@NonNull Loader<Cursor> loader, final Cursor data) {
                ThreadUtils.getCachedPool().execute(new Runnable() {
                    @Override
                    public void run() {
                        final List<MusicInfo> medias = new ArrayList<>();
                        if (data != null) {
                            int count = data.getCount();
                            if (count > 0) {
                                data.moveToFirst();
                                MediaMetadataRetriever mmr = new MediaMetadataRetriever();
                                do {
                                    String absolutePath = data.getString(data.getColumnIndexOrThrow(PROJECTION[1]));
                                    int fileId = data.getInt(data.getColumnIndexOrThrow(PROJECTION[0]));
                                    String path = AndroidVersionUtils.isAboveAndroid_Q() ? AndroidVersionUtils.getPath(fileId) : absolutePath;
                                    if (!isSupport(absolutePath)) {
                                        continue;
                                    }
                                    int duration = data.getInt(data.getColumnIndexOrThrow(PROJECTION[5]));
                                    //String pictureType = data.getString(data.getColumnIndexOrThrow(PROJECTION[2]));
                                    //int size = data.getInt(data.getColumnIndexOrThrow(PROJECTION[6]));
                                    String title = data.getString(data.getColumnIndexOrThrow(PROJECTION[7]));
                                    String author = "";
                                    if (Build.VERSION.SDK_INT < Build.VERSION_CODES.Q) {
                                        author = data.getString(data.getColumnIndexOrThrow(PROJECTION[8]));
                                    }
                                    try {
                                        mmr.setDataSource(path);
                                        author = mmr.extractMetadata(MediaMetadataRetriever.METADATA_KEY_ARTIST);
                                        if (author == null || author.isEmpty()) {
                                            author = "";
                                        }
                                    } catch (Exception e) {
                                        e.printStackTrace();
                                    }
                                    MusicInfo oneMedia = new MusicInfo();
                                    oneMedia.setFilePath(path);
                                    oneMedia.setExoplayerPath(path);
                                    oneMedia.setDuration(duration * 1000L);
                                    oneMedia.setTitle(title);
                                    oneMedia.setArtist(author);
                                    oneMedia.setMimeType(MEDIA_TYPE_AUDIO);
                                    oneMedia.setTrimIn(0);
                                    oneMedia.setTrimOut(oneMedia.getDuration());
                                    medias.add(oneMedia);
                                    //LogUtils.d( "path=" + audioInfo.getFilePath()+",duration="+audioInfo.getDuration());
                                } while (data.moveToNext());
                                ThreadUtils.runOnUiThread(new Runnable() {
                                    @Override
                                    public void run() {
                                        if (mediaLoadListener != null) {
                                            mediaLoadListener.loadComplete(medias);
                                        }
                                    }
                                });
                            }
                            data.close();
                        }
                    }
                });

            }

            @Override
            public void onLoaderReset(@NonNull Loader<Cursor> loader) {
            }
        });
    }

    /**
     * 获取assets目录下的音乐文件列表
     * Get the list of music files in the assets directory
     *
     * @param mediaLoadListener The listener
     */
    private void getAssetsAudio(final AudioLoadListener mediaLoadListener) {
        ThreadUtils.getCachedPool().execute(new Runnable() {
            @Override
            public void run() {
                final List<MusicInfo> fileList = new ArrayList<>();
                String[] pathList;
                try {
                    pathList = Utils.getApp().getAssets().list("music");
                    if (pathList != null) {
                        for (String path : pathList) {
                            if (!isSupport(path)) {
                                continue;
                            }
                            MusicInfo audioInfo = new MusicInfo();
                            audioInfo.setIsAsset(true);
                            audioInfo.setFilePath("assets:/music/" + path);
                            audioInfo.setExoplayerPath("asset:/music/" + path);
                            String tmp = path.substring(0, path.lastIndexOf("."));
                            audioInfo.setLrcPath("assets:/music/" + tmp + ".lrc");
                            audioInfo.setAssetPath("music/" + path);
                            String name = path.substring(0, path.lastIndexOf("."));
                            MediaMetadataRetriever mmr = new MediaMetadataRetriever();
                            try {
                                AssetFileDescriptor afd = Utils.getApp().getAssets().openFd(audioInfo.getAssetPath());
                                mmr.setDataSource(afd.getFileDescriptor(), afd.getStartOffset(), afd.getLength());
                                String artist = mmr.extractMetadata(MediaMetadataRetriever.METADATA_KEY_ARTIST);
                                if (artist == null || artist.isEmpty()) {
                                    artist = "";
                                }
                                String duration = mmr.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION);
                                audioInfo.setTitle(name);
                                audioInfo.setArtist(artist);
                                audioInfo.setDuration(Integer.parseInt(duration) * 1000L);
                                audioInfo.setTrimOut(audioInfo.getDuration());
                                audioInfo.setTrimIn(0);
                                fileList.add(audioInfo);
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                            //LogUtils.d( "path=" + audioInfo.getFilePath()+",duration="+audioInfo.getDuration());
                        }
                    }
                } catch (IOException e) {
                    LogUtils.e("Exception=" + e);
                }
                ThreadUtils.runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        if (mediaLoadListener != null) {
                            mediaLoadListener.loadComplete(fileList);
                        }
                    }
                });
            }
        });
    }

    /**
     * 获取视频(最长或最小时间)
     *
     * @param exMaxLimit long
     * @param exMinLimit long
     * @return the duration
     */
    private String getDurationCondition(long exMaxLimit, long exMinLimit) {
        long maxS = videoMaxS == 0 ? Long.MAX_VALUE : videoMaxS;
        if (exMaxLimit != 0) {
            maxS = Math.min(maxS, exMaxLimit);
        }

        return String.format(Locale.CHINA, "%d <%s duration and duration <= %d",
                Math.max(exMinLimit, videoMinS),
                Math.max(exMinLimit, videoMinS) == 0 ? "" : "=",
                maxS);
    }

    /**
     * 查询条件(音视频)
     * Get selection args for single media condition
     *
     * @param timeCondition 查询条件
     * @return The selection args
     */
    private String getSelectionArgsForSingleMediaCondition(String timeCondition) {
        return MediaStore.Files.FileColumns.MEDIA_TYPE + "=?"
                + " AND " + MediaStore.MediaColumns.SIZE + ">0"
                + " AND " + timeCondition;
    }

    private Boolean isSupport(String path) {
        if (path == null || path.isEmpty()) {
            return false;
        }
        return (path.toLowerCase().endsWith(".mp3") ||
                path.toLowerCase().endsWith(".m4a") ||
                path.toLowerCase().endsWith(".aac") ||
                path.toLowerCase().endsWith(".flac"));
    }

}
