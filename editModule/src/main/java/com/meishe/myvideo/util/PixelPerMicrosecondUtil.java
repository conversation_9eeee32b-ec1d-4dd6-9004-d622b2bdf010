package com.meishe.myvideo.util;

import android.content.Context;

import com.meishe.engine.bean.CommonData;
import com.meishe.base.utils.ScreenUtils;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2020/6/17 16:40
 * 每微秒像素工具类
 * Pixel per microsecond tool class
 */
public class PixelPerMicrosecondUtil {
    private static final String TAG = "PixelPerMicrosecondUtil";
    private static double mPixelPerMicrosecond = 0d;
    private static double mDefaultPerMicrosecond = 0d;
    private static float mScale = 1f;
    private static final List<PixelPerMicrosecondChangeListener> mPixelPerMicrosecondChangeListener = new ArrayList<>();

    /**
     * Init.
     * 初始化
     * @param context the context 上下文
     */
    public static void init(Context context) {
        int screenWidth = ScreenUtils.getScreenWidth();
        mPixelPerMicrosecond = screenWidth / 10D / CommonData.TIMEBASE;
        mDefaultPerMicrosecond = mPixelPerMicrosecond;
    }

    /**
     * Gets pixel per microsecond.
     * 获得像素每微秒
     * @param context the context 上下文
     * @return the pixel per microsecond  像素每微秒
     */
    public static double getPixelPerMicrosecond(Context context) {
        if (mPixelPerMicrosecond == 0) {
            int screenWidth = ScreenUtils.getScreenWidth();
            mPixelPerMicrosecond = screenWidth / 10F / CommonData.TIMEBASE;
        }
        return mPixelPerMicrosecond;
    }

    /**
     * Sets scale.
     * 设置缩放
     * @param scale the scale 缩放
     */
    public static void setScale(float scale) {
        mScale = scale;
        mPixelPerMicrosecond = mDefaultPerMicrosecond * scale;
        for (PixelPerMicrosecondChangeListener pixelPerMicrosecondChangeListener : mPixelPerMicrosecondChangeListener) {
            pixelPerMicrosecondChangeListener.onPixelPerMicrosecondChange(mPixelPerMicrosecond, scale);
        }
    }

    /**
     * 取消科学计数法表示
     * Eliminate scientific representation
     * @param d
     * @return
     */
    private static String formatDouble(double d) {
        NumberFormat nf = NumberFormat.getInstance();
        nf.setMaximumFractionDigits(20);
        nf.setGroupingUsed(false);
        return nf.format(d);
    }

    /**
     * Add pixel per microsecond change listener.
     *每微秒添加像素更改监听器
     * @param pixelPerMicrosecondChangeListener the pixel per microsecond change listener 每微秒像素变更监听器
     */
    public static void addPixelPerMicrosecondChangeListener(PixelPerMicrosecondChangeListener pixelPerMicrosecondChangeListener) {
        mPixelPerMicrosecondChangeListener.add(pixelPerMicrosecondChangeListener);
    }

    /**
     * Remove all listener.
     * 删除所有监听器
     */
    public static void removeAllListener() {
        mPixelPerMicrosecondChangeListener.clear();
    }

    /**
     * Duration to length int.
     * 持续时间到长度
     * @param duration the duration  持续时间
     * @return the int
     */
    public static int durationToLength(long duration) {
        return (int) Math.floor(duration * mPixelPerMicrosecond + 0.5D);
    }

    /**
     * Length to duration long.
     * 长度到持续时间长
     * @param dx the dx
     * @return the long
     */
    public static long lengthToDuration(int dx) {
        return (long) Math.floor(dx / mPixelPerMicrosecond + 0.5D);
    }

    /**
     * Reset scale.
     * 复位缩放
     */
    public static void resetScale() {
        mScale = 1f;
    }

    /**
     * Gets scale.
     * 获得缩放
     * @return the scale 缩放
     */
    public static float getScale() {
        return mScale;
    }


    /**
     * The interface Pixel per microsecond change listener.
     * 每微秒像素变更监听器的接口
     */
    public interface PixelPerMicrosecondChangeListener {
        /**
         * On pixel per microsecond change.
         * 每微秒像素的变化
         * @param pixelPerMicrosecond the pixel per microsecond 像素每微秒
         * @param scale               the scale 缩放
         */
        void onPixelPerMicrosecondChange(double pixelPerMicrosecond, float scale);
    }
}
