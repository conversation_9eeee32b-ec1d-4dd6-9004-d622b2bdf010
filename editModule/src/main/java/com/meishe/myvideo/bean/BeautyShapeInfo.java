package com.meishe.myvideo.bean;

import com.meishe.engine.bean.BaseInfo;

/**
 * 美型信息实体类
 * Beauty type information entity class
 */
public class BeautyShapeInfo extends BaseInfo {
    private String effectId;
    private float effectStrength;
    private String id;
    private String packageId;

    @Override
    public String getEffectId() {
        return effectId;
    }

    @Override
    public void setEffectId(String effectId) {
        this.effectId = effectId;
    }

    @Override
    public float getEffectStrength() {
        return effectStrength;
    }

    @Override
    public void setEffectStrength(float effectStrength) {
        this.effectStrength = effectStrength;
    }

    @Override
    public String getId() {
        return id;
    }

    @Override
    public void setId(String id) {
        this.id = id;
    }

    @Override
    public String getPackageId() {
        return packageId;
    }

    @Override
    public void setPackageId(String packageId) {
        this.packageId = packageId;
    }
}
