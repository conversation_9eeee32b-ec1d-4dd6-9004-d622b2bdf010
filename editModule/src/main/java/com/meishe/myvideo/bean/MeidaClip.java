package com.meishe.myvideo.bean;

import android.os.Parcel;
import android.os.Parcelable;

import com.meishe.base.utils.GsonUtils;


/**
 * All rights Reserved, Designed By www.meishesdk.com
 *
 * @Author: Chu<PERSON><PERSON><PERSON>uang
 * @CreateDate: 2022/10/31 14:18
 * @Description: clip 素材选择
 * @Copyright: www.meishesdk.com Inc. All rights reserved.
 */
public class MeidaClip implements Parcelable {

    /**
     * 所需文件路径
     * The file path
     */
    private String filePath;
    /**
     * 片段时长
     *  clip length
     */
    private long duration = -1;

    /**
     * 辅助类/属性
     * Helper classes/attributes int[]
     */
    private Object tag;

    /**
     * 填入素材类型
     * media type
     */
    private int mediaType;

    public MeidaClip() {
    }


    public String getFilePath() {
        return filePath;
    }

    public MeidaClip setFilePath(String filePath) {
        this.filePath = filePath;
        return this;
    }

    public long getDuration() {
        return duration;
    }

    public MeidaClip setDuration(long duration) {
        this.duration = duration;
        return this;
    }


    public int getMediaType() {
        return mediaType;
    }

    public void setMediaType(int mediaType) {
        this.mediaType = mediaType;
    }

    public Object getTag() {
        return tag;
    }

    public MeidaClip setTag(Object tag) {
        this.tag = tag;
        return this;
    }

    protected MeidaClip(Parcel in) {
        filePath = in.readString();
        duration = in.readLong();
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(filePath);
        dest.writeLong(duration);
    }

    public MeidaClip copy() {
        return GsonUtils.fromJson(GsonUtils.toJson(this), getClass());
    }

    public void update(MeidaClip clip) {
        setFilePath(clip.getFilePath());
        setDuration(clip.getDuration());
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<MeidaClip> CREATOR = new Creator<MeidaClip>() {
        @Override
        public MeidaClip createFromParcel(Parcel in) {
            return new MeidaClip(in);
        }

        @Override
        public MeidaClip[] newArray(int size) {
            return new MeidaClip[size];
        }
    };


}
