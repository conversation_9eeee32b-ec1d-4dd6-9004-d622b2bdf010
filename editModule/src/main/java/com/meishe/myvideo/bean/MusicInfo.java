package com.meishe.myvideo.bean;


import android.graphics.Bitmap;
import android.os.Parcel;
import android.os.Parcelable;

import androidx.annotation.NonNull;

import com.meishe.engine.asset.bean.AssetInfo;

import java.io.Serializable;

/**
 * The type Music info.
 * 音乐信息类
 */
public class MusicInfo implements Serializable{
    private String m_title;
    private String m_artist;
    private String m_imagePath;
    private String m_filePath;
    private String m_fileUrl;
    private String m_assetPath;
    private Bitmap m_image;
    private long m_duration;
    private long m_inPoint;
    private long m_outPoint;
    private long m_trimIn;
    private long m_trimOut;
    private int m_mimeType;
    private boolean m_prepare;
    private boolean m_isHttpMusic;
    private boolean m_isAsset;
    private float m_volume;
    private long m_originalInPoint;
    private long m_originalOutPoint;
    private long m_originalTrimIn;
    private long m_originalTrimOut;
    private int m_extraMusic;
    private long m_extraMusicLeft;
    private long m_fadeDuration;

    private String m_lrcPath;

    /**
     *  Path for exoplayer
     *  给exoplayer使用的path
     */
    private String m_exoPlayerPath;

    private transient AssetInfo mAssetsInfo;

    /**
     * Instantiates a new Music info.
     * 实例化一个新的音乐信息
     */
    public MusicInfo() {
        m_title = null;
        m_artist = null;
        m_imagePath = null;
        m_filePath = null;
        m_fileUrl = null;
        m_assetPath = null;
        m_image = null;
        m_duration = 0;
        m_inPoint = 0;
        m_outPoint = 0;
        m_trimIn = 0;
        m_trimOut = 0;
        m_originalInPoint = 0;
        m_originalOutPoint = 0;
        m_originalTrimIn = 0;
        m_originalTrimOut = 0;
        m_mimeType = 0;
        m_prepare = false;
        m_isHttpMusic = false;
        m_isAsset = false;
        m_volume = 1.0f;
        m_extraMusic = 0;
        m_extraMusicLeft = 0;
        m_fadeDuration = 0;
        m_lrcPath = "";
    }

    @Override
    @NonNull
    public MusicInfo clone() {
        try {
            return (MusicInfo) super.clone();
        } catch (CloneNotSupportedException e) {
        }
        return new MusicInfo();
    }

    /**
     * Sets title.
     * 设置标题
     * @param title the title
     */
    public void setTitle(String title) {
        m_title = title;
    }

    /**
     * Gets title.
     * 获取标题
     * @return the title
     */
    public String getTitle() {
        return m_title;
    }

    /**
     * Sets artist.
     * 设置艺术家
     * @param artist the artist
     */
    public void setArtist(String artist) {
        m_artist = artist;
    }

    /**
     * Gets artist.
     * 获取艺术家
     * @return the artist
     */
    public String getArtist() {
        return m_artist;
    }

    /**
     * Sets image path.
     * 设置图像路径
     * @param filePath the file path
     */
    public void setImagePath(String filePath) {
        m_imagePath = filePath;
    }

    /**
     * Gets image path.
     * 获取图像路径
     * @return the image path
     */
    public String getImagePath() {
        return m_imagePath;
    }

    /**
     * Sets file path.
     * 设置文件路径
     * @param filePath the file path
     */
    public void setFilePath(String filePath) {
        m_filePath = filePath;
    }

    /**
     * Gets file path.
     * 获取文件路径
     * @return the file path
     */
    public String getFilePath() {
        if (mAssetsInfo != null) {
            m_filePath = mAssetsInfo.getAssetPath();
        }
        return m_filePath;
    }

    /**
     * Sets file url.
     * 设置文件地址
     * @param url the url
     */
    public void setFileUrl(String url) {
        m_fileUrl = url;
    }

    /**
     * Gets file url.
     * 获取文件地址
     * @return the file url
     */
    public String getFileUrl() {
        return m_fileUrl;
    }

    /**
     * Gets asset path.
     * 获取资产的路径
     * @return the asset path
     */
    public String getAssetPath() {
        return m_assetPath;
    }

    /**
     * Sets asset path.
     * 设置资产的路径
     * @param m_assetPath the m asset path
     */
    public void setAssetPath(String m_assetPath) {
        this.m_assetPath = m_assetPath;
    }

    /**
     * Sets image.
     * 设置图像
     * @param bitmap the bitmap
     */
    public void setImage(Bitmap bitmap) {
        m_image = bitmap;
    }

    /**
     * Gets image.
     * 获取图像
     * @return the image
     */
    public Bitmap getImage() {
        return m_image;
    }

    /**
     * Sets in point.
     * 设置点
     * @param time the time
     */
    public void setInPoint(long time) {
        m_inPoint = time;
    }

    /**
     * Gets in point.
     * 获取点
     * @return the in point
     */
    public long getInPoint() {
        return m_inPoint;
    }

    /**
     * Sets out point.
     * 设置结束点
     * @param time the time
     */
    public void setOutPoint(long time) {
        m_outPoint = time;
    }

    /**
     * Gets out point.
     * 获取结束点
     * @return the out point
     */
    public long getOutPoint() {
        return m_outPoint;
    }

    /**
     * Sets duration.
     * 设置时长
     * @param time the time
     */
    public void setDuration(long time) {
        m_duration = time;
    }

    /**
     * Gets duration.
     * 获取时长
     * @return the duration
     */
    public long getDuration() {
        return m_duration;
    }

    /**
     * Sets trim in.
     * 设置嵌入
     * @param time the time
     */
    public void setTrimIn(long time) {
        m_trimIn = time;
    }

    /**
     * Gets trim in.
     * 获取嵌入
     * @return the trim in
     */
    public long getTrimIn() {
        return m_trimIn;
    }

    /**
     * Sets trim out.
     * 设置配平
     * @param time the time
     */
    public void setTrimOut(long time) {
        m_trimOut = time;
    }

    /**
     * Gets trim out.
     * 获取配平
     * @return the trim out
     */
    public long getTrimOut() {
        return m_trimOut;
    }

    /**
     * Gets mime type.
     * 获取文档类型
     * @return the mime type
     */
    public int getMimeType() {
        return m_mimeType;
    }

    /**
     * Sets mime type.
     * 设置文档类型
     * @param m_mimeType the m mime type
     */
    public void setMimeType(int m_mimeType) {
        this.m_mimeType = m_mimeType;
    }

    /**
     * Is prepare boolean.
     * 是准备布尔
     * @return the boolean
     */
    public boolean isPrepare() {
        return m_prepare;
    }

    /**
     * Sets prepare.
     * 设置准备
     * @param m_prepare the m prepare
     */
    public void setPrepare(boolean m_prepare) {
        this.m_prepare = m_prepare;
    }

    /**
     * Is http music boolean.
     * 超文本传输协议音乐布尔值
     * @return the boolean
     */
    public boolean isHttpMusic() {
        return m_isHttpMusic;
    }

    /**
     * Sets is http music.
     * 设置超文本传输协议音乐布尔值
     * @param m_isHttpMusic the m is http music
     */
    public void setIsHttpMusic(boolean m_isHttpMusic) {
        this.m_isHttpMusic = m_isHttpMusic;
    }

    /**
     * Is asset boolean.
     * 资源
     * @return the boolean
     */
    public boolean isAsset() {
        return m_isAsset;
    }

    /**
     * Sets is asset.
     * 设置资产
     * @param m_isAsset the m is asset
     */
    public void setIsAsset(boolean m_isAsset) {
        this.m_isAsset = m_isAsset;
    }

    /**
     * Gets volume.
     * 获取体积
     * @return the volume
     */
    public float getVolume() {
        return m_volume;
    }

    /**
     * Sets volume.
     * 设置体积
     * @param volume the volume
     */
    public void setVolume(float volume) {
        this.m_volume = volume;
    }

    /**
     * Gets original in point.
     * 得到原始的点
     * @return the original in point
     */
    public long getOriginalInPoint() {
        return m_originalInPoint;
    }

    /**
     * Sets original in point.
     * 设置原始的点
     * @param m_originalTrimIn the m original trim in
     */
    public void setOriginalInPoint(long m_originalTrimIn) {
        this.m_originalInPoint = m_originalTrimIn;
    }

    /**
     * Gets original out point.
     * 得到原始的输出点
     * @return the original out point
     */
    public long getOriginalOutPoint() {
        return m_originalOutPoint;
    }

    /**
     * Sets original out point.
     * 设置原始的输出点
     * @param m_originalTrimOut the m original trim out
     */
    public void setOriginalOutPoint(long m_originalTrimOut) {
        this.m_originalOutPoint = m_originalTrimOut;
    }

    /**
     * Gets original trim in.
     * 获取原始修剪
     * @return the original trim in
     */
    public long getOriginalTrimIn() {
        return m_originalTrimIn;
    }

    /**
     * Sets original trim in.
     * 设置原始修剪
     * @param m_originalTrimIn the m original trim in
     */
    public void setOriginalTrimIn(long m_originalTrimIn) {
        this.m_originalTrimIn = m_originalTrimIn;
    }

    /**
     * Gets original trim out.
     * 获取原始配平
     * @return the original trim out
     */
    public long getOriginalTrimOut() {
        return m_originalTrimOut;
    }

    /**
     * Sets original trim out.
     * 设置原始配平
     * @param m_originalTrimOut the m original trim out
     */
    public void setOriginalTrimOut(long m_originalTrimOut) {
        this.m_originalTrimOut = m_originalTrimOut;
    }

    /**
     * Gets extra music.
     * 获取附加音乐
     * @return the extra music
     */
    public int getExtraMusic() {
        return m_extraMusic;
    }

    /**
     * Sets extra music.
     * 设置附加音乐
     * @param m_extraMusic the m extra music
     */
    public void setExtraMusic(int m_extraMusic) {
        this.m_extraMusic = m_extraMusic;
    }

    /**
     * Gets extra music left.
     * 获取附加音乐
     * @return the extra music left
     */
    public long getExtraMusicLeft() {
        return m_extraMusicLeft;
    }

    /**
     * Sets extra music left.
     * 设置附加音乐
     * @param m_extraMusicLeft the m extra music left
     */
    public void setExtraMusicLeft(long m_extraMusicLeft) {
        this.m_extraMusicLeft = m_extraMusicLeft;
    }

    /**
     * Gets fade duration.
     * 获取淡入淡出持续时间
     * @return the fade duration
     */
    public long getFadeDuration() {
        return m_fadeDuration;
    }

    /**
     * Sets fade duration.
     * 设置淡入淡出持续时间
     * @param m_fadeDuration the m fade duration
     */
    public void setFadeDuration(long m_fadeDuration) {
        this.m_fadeDuration = m_fadeDuration;
    }

    /**
     * Set exoplayer path.
     * 设置exoplayer的路径
     * @param path the path
     */
    public void setExoplayerPath(String path){
        m_exoPlayerPath = path;
    }

    /**
     * Get exo player path string.
     * 获取exo播放器路径字符串
     * @return the string
     */
    public String getExoPlayerPath(){
        return m_exoPlayerPath;
    }

    /**
     * Gets lrc path.
     * 获得荣誉奖的路径
     * @return the lrc path
     */
    public String getLrcPath() {
        return m_lrcPath;
    }

    /**
     * Sets lrc path.
     * 设置荣誉奖的路径
     * @param m_lrcPath the m lrc path
     */
    public void setLrcPath(String m_lrcPath) {
        this.m_lrcPath = m_lrcPath;
    }

    public void setAssetsInfo(AssetInfo assetInfo) {
        this.mAssetsInfo = assetInfo;
        if (assetInfo != null) {
            m_duration = assetInfo.getDuration() * 1000000;
            m_artist = assetInfo.getDescription();
            m_title = assetInfo.getName();
            m_trimOut = m_duration;
            m_assetPath = assetInfo.getAssetPath();
            m_fileUrl = m_assetPath;
            m_filePath = m_assetPath;
            m_isHttpMusic = false;
        }
    }

    public AssetInfo getAssetsInfo() {
        return mAssetsInfo;
    }

    public SampleMusicInfo createSampleInfo(){
        return new SampleMusicInfo(getTitle(), getTrimIn(), getTrimOut(), getFilePath());
    }

    public static class SampleMusicInfo implements Parcelable, Serializable {
        public String title;
        public long trimIn;
        public long trimOut;
        public String filePath;

        public SampleMusicInfo(String title, long trimIn, long trimOut, String filePath) {
            this.title = title;
            this.trimIn = trimIn;
            this.trimOut = trimOut;
            this.filePath = filePath;
        }

        protected SampleMusicInfo(Parcel in) {
            title = in.readString();
            trimIn = in.readLong();
            trimOut = in.readLong();
            filePath = in.readString();
        }

        public static final Creator<SampleMusicInfo> CREATOR = new Creator<SampleMusicInfo>() {
            @Override
            public SampleMusicInfo createFromParcel(Parcel in) {
                return new SampleMusicInfo(in);
            }

            @Override
            public SampleMusicInfo[] newArray(int size) {
                return new SampleMusicInfo[size];
            }
        };

        @Override
        public int describeContents() {
            return 0;
        }

        @Override
        public void writeToParcel(Parcel dest, int flags) {
            dest.writeString(title);
            dest.writeLong(trimIn);
            dest.writeLong(trimOut);
            dest.writeString(filePath);
        }
    }
}
