package com.meishe.myvideo.bean;

import com.meishe.third.tablayout.listener.CustomTabEntity;



/**
 * * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> <PERSON><PERSON><PERSON>Z<PERSON>
 * @CreateDate : 2020/11/11
 * @Description :主页标签实体类。Home tag entity class
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class MainTabEntity implements CustomTabEntity {
    private String title;
    private int selectedIconId;
    private int unselectedIconId;

    public MainTabEntity(String title, int selectedIconId, int unselectedIconId) {
        this.title = title;
        this.selectedIconId = selectedIconId;
        this.unselectedIconId = unselectedIconId;
    }

    @Override
    public String getTabTitle() {
        return title;
    }

    @Override
    public int getTabSelectedIcon() {
        return selectedIconId;
    }

    @Override
    public int getTabUnselectedIcon() {
        return unselectedIconId;
    }
}
