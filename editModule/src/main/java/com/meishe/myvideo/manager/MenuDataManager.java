package com.meishe.myvideo.manager;

import android.content.Context;
import android.content.res.TypedArray;
import android.text.TextUtils;

import com.google.gson.reflect.TypeToken;
import com.meicam.sdk.NvsStreamingContext;
import com.meishe.base.bean.Navigation;
import com.meishe.base.utils.FileUtils;
import com.meishe.base.utils.GsonUtils;
import com.meishe.base.utils.ResourceUtils;
import com.meishe.base.utils.Utils;
import com.meishe.engine.asset.bean.AssetInfo;
import com.meishe.engine.bean.BaseInfo;
import com.meishe.engine.constant.NvsConstants;
import com.meishe.engine.interf.IBaseInfo;
import com.meishe.engine.util.PathUtils;
import com.meishe.myvideo.R;
import com.meishe.myvideo.bean.BeautyShapeInfo;
import com.meishe.myvideo.bean.CaptionFontInfo;
import com.meishe.myvideo.bean.ChangeSpeedCurveInfo;
import com.meishe.myvideo.bean.EditAdjustInfo;
import com.meishe.myvideo.bean.EditMixedModeInfo;

import java.io.File;
import java.util.ArrayList;
import java.util.List;


/**
 * The type Menu data manager.
 * 此类为 菜单数据管理器
 */
public class MenuDataManager {

    public static List<AssetInfo> getTransition(Context context) {
        context = context.getApplicationContext();
        List<AssetInfo> transitionInfoList = new ArrayList<>();
        int[] resImages = {
                R.mipmap.ic_transition_fade, R.mipmap.ic_transition_turning, R.mipmap.ic_transition_swap, R.mipmap.ic_transition_stretch_in,
                R.mipmap.ic_transition_page_curl, R.mipmap.ic_transition_lens_flare, R.mipmap.ic_transition_star, R.mipmap.ic_transition_dip_to_black,
                R.mipmap.ic_transition_dip_to_white, R.mipmap.ic_transition_push_to_right, R.mipmap.ic_transition_push_to_left, R.mipmap.ic_transition_upper_left_into
        };


        String[] resNames = {
                context.getResources().getString(R.string.trans_fade), context.getResources().getString(R.string.trans_turning), context.getResources().getString(R.string.trans_swap),
                context.getResources().getString(R.string.trans_stretch_in),
                context.getResources().getString(R.string.trans_page_curl), context.getResources().getString(R.string.trans_lens_flare),
                context.getResources().getString(R.string.trans_star), context.getResources().getString(R.string.trans_dip_to_black),
                context.getResources().getString(R.string.trans_dip_to_white), context.getResources().getString(R.string.trans_push_to_right),
                context.getResources().getString(R.string.trans_push_to_left), context.getResources().getString(R.string.trans_upper_left_into)
        };
        String[] resNamesEn = {"Fade", "Turning", "Swap", "Stretch In",
                "Page Curl", "Lens Flare", "Star", "Dip To Black", "Dip To White", "Push To Right",
                "Push To Top", "Upper Left Into"
        };
        AssetInfo transitionInfo;
        for (int i = 0; i < resNames.length; i++) {
            String transitionName = resNames[i];
            transitionInfo = new AssetInfo();
            transitionInfo.setName(transitionName);
            transitionInfo.setCoverId(resImages[i]);
            transitionInfo.setEffectMode(BaseInfo.EFFECT_MODE_BUILTIN);
            transitionInfo.setType(AssetInfo.ASSET_VIDEO_TRANSITION);
            transitionInfo.setEffectId(resNamesEn[i]);
            transitionInfo.setHadDownloaded(true);
            transitionInfoList.add(transitionInfo);
        }
        return transitionInfoList;
    }

    /**
     * Gets adjust menu data.
     * 获取调整菜单数据
     *
     * @param context the context
     * @return the adjust menu data
     */
    public static List<IBaseInfo> getAdjustMenuData(Context context) {
        TypedArray editIconArray = context.getResources().obtainTypedArray(R.array.sub_adjust_menu_icon);
        String[] nameArray = context.getResources().getStringArray(R.array.sub_adjust_menu_name);
        String[] fxNameArray = new String[]{"Brightness", "Contrast", "Saturation", "Highlight", "Shadow",
                "Temperature", "Tint", "Blackpoint", "Degree", "Amount"};
        List<IBaseInfo> adjustMenuList = new ArrayList<>();
        IBaseInfo editMenuInfo;
        for (int i = 0; i < nameArray.length; i++) {
            editMenuInfo = new EditAdjustInfo();
            editMenuInfo.setName(nameArray[i]);
            editMenuInfo.setCoverId(editIconArray.getResourceId(i, -1));
            editMenuInfo.setEffectId(fxNameArray[i]);
            adjustMenuList.add(editMenuInfo);
        }
        editIconArray.recycle();
        return adjustMenuList;
    }


    /**
     * Gets mixed mode menu data.
     * 获取混合模式数据
     *
     * @param context the context
     * @return the mixed mode menu data
     */
    public static List<IBaseInfo> getMixedModeMenuData(Context context) {
        TypedArray editIconArray = context.getResources().obtainTypedArray(R.array.sub_mixed_mode_menu_icon);
        String[] nameArray = context.getResources().getStringArray(R.array.sub_mixed_mode_menu_name);
        int[] nvsIdArray = {0, 6, 3, 11, 1, 9, 13, 12, 8, 7, 10, 2, 5, 4, 14, 15, 16, 17, 18};
        List<IBaseInfo> adjustMenuList = new ArrayList<>();
        EditMixedModeInfo editMenuInfo;
        for (int i = 0; i < nameArray.length; i++) {
            editMenuInfo = new EditMixedModeInfo();
            editMenuInfo.setName(nameArray[i]);
            editMenuInfo.setCoverId(editIconArray.getResourceId(i, -1));
            editMenuInfo.setMixedMode(nvsIdArray[i]);
            adjustMenuList.add(editMenuInfo);
        }
        editIconArray.recycle();
        return adjustMenuList;
    }

    /**
     * 获取本地资源目录下的字幕字体列表
     * Gets Assets caption font data
     *
     * @param context the context
     * @return the caption font data
     */
    public static List<IBaseInfo> getCaptionFontList(Context context) {
        //注册asset里的字体
        //Register fonts in asset
        List<IBaseInfo> captionFontList = new ArrayList<>();
        String path = "font/font.json";
        boolean isZh = Utils.isZh();
        List<CaptionFontInfo> fontInfos = GsonUtils.fromJson(ResourceUtils.readAssets2String(path, "UTF-8"),
                new TypeToken<List<CaptionFontInfo>>() {
                }.getType());
        if (fontInfos != null) {
            for (CaptionFontInfo info : fontInfos) {
                String fontAssetPath = "assets:/" + info.getAssetPath();
                info.setAssetPath(fontAssetPath);
                String fontFamily = NvsStreamingContext.getInstance().registerFontByFilePath(fontAssetPath);
                if (!isZh) {
                    info.setName(info.getEnName());
                }
                if (!TextUtils.isEmpty(fontFamily)) {
                    info.setFontFamily(fontFamily);
                }
                captionFontList.add(info);
            }

        }
        //注册导入的字体
        //Register imported fonts
        List<File> files = FileUtils.listFilesInDir(PathUtils.getFontFilePath());
        int i = 0;
        for (File file : files) {
            CaptionFontInfo info = new CaptionFontInfo();
            info.setName(context.getResources().getString(R.string.font) + (i++));
            String fontPathLocal = file.getAbsolutePath();
            info.setAssetPath(fontPathLocal);
            String fontFamily = NvsStreamingContext.getInstance().registerFontByFilePath(fontPathLocal);
            if (!TextUtils.isEmpty(fontFamily)) {
                info.setFontFamily(fontFamily);
                captionFontList.add(info);
            }
            captionFontList.add(info);
        }
        return captionFontList;
    }

    /**
     * 获取导航栏集合
     * Gets the navigation bar collection
     */
    public static List<Navigation> getNavigationList(Context context) {
        List<Navigation> list = new ArrayList<>();
        //第0级导航栏
        // Level 0 navigation bar
        list.add(createNavigation(context, R.array.navigation_main0_icon, R.array.navigation_main0_text));
        //视频编辑一级导航栏
        // Video editing navigation bar
        list.add(createNavigation(context, R.array.navigation_video_edit1_icon, R.array.navigation_video_edit1_text));
        //图片编辑一级导航栏
        // Picture editing navigation bar
        list.add(createNavigation(context, R.array.navigation_picture_edit1_icon, R.array.navigation_picture_edit1_text));
        //变速二级导航栏
        // Variable speed secondary navigation bar
        list.add(createNavigation(context, R.array.navigation_speed1_icon, R.array.navigation_speed1_text));
        //动画二级导航栏
        // Animation secondary navigation bar
        list.add(createNavigation(context, R.array.navigation_animate2_icon, R.array.navigation_animate2_text));
        //滤镜一级导航栏
        // Filter navigation bar
        list.add(createNavigation(context, R.array.navigation_filter1_icon, R.array.navigation_filter1_text));
        //特效一级导航栏
        // Effect navigation bar
        list.add(createNavigation(context, R.array.navigation_effect1_icon, R.array.navigation_effect1_text));
        //特效二级导航栏
        // Effect secondary navigation bar
        list.add(createNavigation(context, R.array.navigation_effect_edit2_icon, R.array.navigation_effect_edit2_text));
        //字幕、贴纸、组合字幕一级导航栏
        // Sticker,caption,compound caption navigation bar
        list.add(createNavigation(context, R.array.navigation_sticker1_icon, R.array.navigation_sticker1_text));
        //贴纸编辑二级导航栏
        //Sticker editing secondary navigation bar
        list.add(createNavigation(context, R.array.navigation_sticker_edit2_icon, R.array.navigation_sticker_edit2_text));
        //字幕编辑二级导航栏
        //Caption editing secondary navigation bar
        list.add(createNavigation(context, R.array.navigation_caption_edit2_icon, R.array.navigation_caption_edit2_text));
        //组合字幕编辑二级导航栏
        //Compound caption editing secondary navigation bar
        list.add(createNavigation(context, R.array.navigation_combined_caption_edit2_icon, R.array.navigation_combined_caption_edit2_text));
        //水印一级导航栏
        // Water mark navigation bar
        /*if (ConfigUtil.isToC()) {
            list.add(createNavigation(context, R.array.navigation_water_mark1_icon, R.array.navigation_water_mark1_text));
        } else {
            list.add(createNavigation(context, R.array.navigation_water_mark1_icon_to_b, R.array.navigation_water_mark1_text_to_b));
        }*/
        list.add(createNavigation(context, R.array.navigation_water_mark1_icon_to_b, R.array.navigation_water_mark1_text_to_b));
        //音频一级导航栏
        // Audio navigation bar
        list.add(createNavigation(context, R.array.navigation_audio1_icon, R.array.navigation_audio1_text));
        //音频编辑二级导航栏
        // Audio editing secondary navigation bar
        list.add(createNavigation(context, R.array.navigation_audio_edit2_icon, R.array.navigation_audio_edit2_text));
        //画中画一级导航栏
        // Picture-in-picture navigation bar
        list.add(createNavigation(context, R.array.navigation_pip1_icon, R.array.navigation_pip1_text));
        //背景一级导航栏
        // Background navigation bar
        list.add(createNavigation(context, R.array.navigation_background1_icon, R.array.navigation_background1_text));
        //滤镜二级导航栏
        // Filter secondary navigation bar
        list.add(createNavigation(context, R.array.navigation_filter2_icon, R.array.navigation_filter2_text));
        //调节二级导航栏
        // Adjust secondary navigation bar
        list.add(createNavigation(context, R.array.navigation_adjust2_icon, R.array.navigation_adjust2_text));
        return list;
    }

    /**
     * 创建某个导航栏
     * Create a navigation bar
     */
    private static Navigation createNavigation(Context context, int iconArray, int titleArray) {
        Navigation navigation = new Navigation();
        TypedArray typedArray = context.getResources().obtainTypedArray(iconArray);
        TypedArray nameArray = context.getResources().obtainTypedArray(titleArray);
        List<Navigation.Item> itemList = new ArrayList<>(typedArray.length());
        //前三个是固定的
        // The first three are fixed
        int titleIndex = 0;
        //第一个是本级名称The first is the current level name
        //第二个是上级名称The second is the name of the superior
        //第三个是层级The third is hierarchy
        //title的下一个是指向下一层级的名称The next name of the title is the name pointing to the next level
        navigation.setName(nameArray.getResourceId(titleIndex++, -1))
                .setPreName(nameArray.getResourceId(titleIndex++, -1))
                .setLevel(nameArray.getInt(titleIndex++, 1))
                .setItems(itemList);
        for (int index = 0; index < typedArray.length(); index++) {
            itemList.add(new Navigation.Item()
                    .setIconId(typedArray.getResourceId(index, -1))
                    .setTitleId(nameArray.getResourceId(index + titleIndex, -1))
                    .setNextName(nameArray.getResourceId(index + (++titleIndex), -1)));
        }
        typedArray.recycle();
        nameArray.recycle();
        return navigation;
    }

    /**
     * 曲线变速
     * Curve of variable speed
     *
     * @param context 上下文
     * @return The curve speed data曲线变速数据
     */
    public static List<IBaseInfo> getChangeSpeedCurve(Context context) {
        List<IBaseInfo> list = new ArrayList<>();
        IBaseInfo curveInfo = new ChangeSpeedCurveInfo();
        curveInfo.setName(context.getResources().getString(R.string.original));
        curveInfo.setCoverId(R.mipmap.icon_original);
        list.add(curveInfo);
        String path = "curve_speed/speed.json";
        boolean isZh = Utils.isZh();
        List<ChangeSpeedCurveInfo> speedList = GsonUtils.fromJson(ResourceUtils.
                        readAssets2String(path, "UTF-8"),
                new TypeToken<List<ChangeSpeedCurveInfo>>() {
                }.getType());
        for (ChangeSpeedCurveInfo info : speedList) {
            if (!isZh) {
                info.setName(info.getEnName());
            }
        }
        list.addAll(speedList);
        return list;
    }

    /**
     * Gets beauty shape data list.
     * 获取美型数据列表
     *
     * @param context 上下文 The context
     * @return 美型数据集合 ；Beauty data collection
     */
    public static List<IBaseInfo> getBeautyShapeDataList(Context context) {
       /* 美型瘦脸60，大眼70，瘦鼻50  BeautyShapeDataKindItem.Type.NEW_BUILD  瘦脸（瘦）60，眼部（大眼）50，眼角（拉长）20，瘦鼻（变窄）50 额头（抬高）25
       * BeautyShapeDataKindItem.Type.NEW_ BUILD thin face (thin) 60, eyes (big eyes) 50, corners of eyes (elongated) 20, thin nose (narrow) 50 forehead (raised) 25
       * */
        ArrayList<IBaseInfo> list = new ArrayList<>();
        /*
         * 瘦脸
         * Thin face
         * */
        IBaseInfo cheekThinning = new BeautyShapeInfo();
        cheekThinning.setName(context.getResources().getString(R.string.cheek_thinning));
        cheekThinning.setCoverId(R.mipmap.ic_cheek_thinning);
        cheekThinning.setEffectId(NvsConstants.BeautyShapeDegree.Mesh_Face_Size);
        cheekThinning.setPackageId(NvsConstants.BeautyShapePackageID.Mesh_Face_Size);
        cheekThinning.setId(NvsConstants.BeautyShapeID.Mesh_Face_Size);
        cheekThinning.setEffectStrength(-0.6f);
        list.add(cheekThinning);
        /*
         * 大眼
         * Eye enlarging
         * */
        IBaseInfo eyeEnlarging = new BeautyShapeInfo();
        eyeEnlarging.setName(context.getResources().getString(R.string.eye_enlarging));
        eyeEnlarging.setCoverId(R.mipmap.ic_eye_enlarging);
        eyeEnlarging.setEffectId(NvsConstants.BeautyShapeDegree.Mesh_Eye_Size);
        eyeEnlarging.setPackageId(NvsConstants.BeautyShapePackageID.Mesh_Eye_Size);
        eyeEnlarging.setId(NvsConstants.BeautyShapeID.Mesh_Eye_Size);
        eyeEnlarging.setEffectStrength(0.5f);
        list.add(eyeEnlarging);

        /*
         * 下巴
         * Chin
         * */
        IBaseInfo intensityChin = new BeautyShapeInfo();
        intensityChin.setName(context.getResources().getString(R.string.intensity_chin));
        intensityChin.setCoverId(R.mipmap.ic_intensity_chin);
        intensityChin.setEffectId(NvsConstants.BeautyShapeDegree.Mesh_Chin_Length);
        intensityChin.setPackageId(NvsConstants.BeautyShapePackageID.Mesh_Chin_Length);
        intensityChin.setId(NvsConstants.BeautyShapeID.Mesh_Chin_Length);
        list.add(intensityChin);

        /*
         * 额头
         * forehead
         * */
        IBaseInfo intensityForehead = new BeautyShapeInfo();
        intensityForehead.setName(context.getResources().getString(R.string.intensity_forehead));
        intensityForehead.setCoverId(R.mipmap.ic_intensity_forehead);
        intensityForehead.setEffectId(NvsConstants.BeautyShapeDegree.Warp_Forehead_Height);
        intensityForehead.setPackageId(NvsConstants.BeautyShapePackageID.Warp_Forehead_Height);
        intensityForehead.setId(NvsConstants.BeautyShapeID.Warp_Forehead_Height);
        intensityForehead.setEffectStrength(0.25f);
        list.add(intensityForehead);
        /*
         * 瘦鼻
         * Nose width
         * */
        IBaseInfo intensityNose = new BeautyShapeInfo();
        intensityNose.setName(context.getResources().getString(R.string.intensity_nose));
        intensityNose.setCoverId(R.mipmap.ic_intensity_nose);
        intensityNose.setEffectId(NvsConstants.BeautyShapeDegree.Mesh_Nose_Width);
        intensityNose.setPackageId(NvsConstants.BeautyShapePackageID.Mesh_Nose_Width);
        intensityNose.setId(NvsConstants.BeautyShapeID.Mesh_Nose_Width);
        intensityNose.setEffectStrength(-0.5f);
        list.add(intensityNose);

        /*
         * 嘴形
         * Mouth size
         * */
        IBaseInfo intensityMouth = new BeautyShapeInfo();
        intensityMouth.setName(context.getResources().getString(R.string.intensity_mouth));
        intensityMouth.setCoverId(R.mipmap.intensity_mouth);
        intensityMouth.setEffectId(NvsConstants.BeautyShapeDegree.Mesh_Mouth_Size);
        intensityMouth.setPackageId(NvsConstants.BeautyShapePackageID.Mesh_Mouth_Size);
        intensityMouth.setId(NvsConstants.BeautyShapeID.Mesh_Mouth_Size);
        list.add(intensityMouth);

        return list;
    }

}
