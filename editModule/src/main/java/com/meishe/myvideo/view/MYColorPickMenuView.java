package com.meishe.myvideo.view;

import android.content.Context;
import android.content.res.Resources;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.CheckBox;
import android.widget.LinearLayout;
import android.widget.SeekBar;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.meishe.base.manager.LinearLayoutManagerWrapper;
import com.meishe.base.view.decoration.ItemDecoration;
import com.meishe.engine.bean.BaseInfo;
import com.meishe.engine.constant.NvsConstants;
import com.meishe.engine.interf.IBaseInfo;
import com.meishe.myvideo.R;
import com.meishe.myvideo.adapter.BaseSelectAdapter;
import com.meishe.third.adpater.BaseQuickAdapter;
import com.meishe.third.adpater.BaseViewHolder;

import java.util.ArrayList;
import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/12/29 10:50
 * @Description :色度抠图菜单 The color pick menu view
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class MYColorPickMenuView extends LinearLayout implements View.OnClickListener {
    private RecyclerView mRecyclerView;
    private OnEventChangedListener mOnEventChangedListener;
    private MYSeekBarTopTextView mSeekBar;
    private ColorPickAdapter mColorAdapter;
    private int mCurrentPosition;
    private float[] mInitParam;

    public void setOnEventChangedListener(OnEventChangedListener listener) {
        this.mOnEventChangedListener = listener;
    }

    public MYColorPickMenuView(Context context, float[] param) {
        super(context);
        mInitParam = param;
        initView(context);
    }

    private void initView(Context context) {
        View view = LayoutInflater.from(getContext()).inflate(R.layout.view_menu_color_pick, this);
        mRecyclerView = view.findViewById(R.id.rv_list);
        mSeekBar = view.findViewById(R.id.view_seek_bar);
        view.findViewById(R.id.tv_reset).setOnClickListener(this);
        view.findViewById(R.id.iv_confirm).setOnClickListener(this);
        mSeekBar.setNeedHideText(true);
        initRecyclerView();
        initListener();
    }

    private void initListener() {
        mColorAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                mColorAdapter.setSelectPosition(position);
                mCurrentPosition = position;
                if (position == 0) {
                    mSeekBar.setVisibility(INVISIBLE);
                } else {
                    mSeekBar.setVisibility(View.VISIBLE);
                    IBaseInfo item = mColorAdapter.getItem(position);
                    if (item != null) {
                        float effectStrength = ((ItemInfo) item).getEffectStrength();
                        mSeekBar.setProgress((int) effectStrength);
                    }
                }
                if (mOnEventChangedListener != null) {
                    mOnEventChangedListener.onMenuClicked(position);
                }
            }
        });
        mSeekBar.setOnSeekBarChangeListener(new MYSeekBarTopTextView.OnSeekBarListener() {
            @Override
            public void onStopTrackingTouch(int progress, String name) {

            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {

            }

            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                if (!fromUser) {
                    return;
                }
                ItemInfo item = (ItemInfo) mColorAdapter.getItem(mCurrentPosition);
                if (item != null) {
                    item.setEffectStrength(progress);
                    if (mOnEventChangedListener != null) {
                        mOnEventChangedListener.onDataChanged(item.getEffectId(), progress);
                    }
                }
            }
        });
    }

    private void initRecyclerView() {
        LinearLayoutManager layoutManager = new LinearLayoutManagerWrapper(getContext(), LinearLayoutManager.HORIZONTAL, false);
        mRecyclerView.setLayoutManager(layoutManager);
        mColorAdapter = new ColorPickAdapter();
        mRecyclerView.setAdapter(mColorAdapter);
        mColorAdapter.setNewData(getMenuData());
        mColorAdapter.setSelectPosition(0);
        mSeekBar.setVisibility(INVISIBLE);
        int padding = getResources().getDimensionPixelSize(R.dimen.dp_px_9);
        mRecyclerView.addItemDecoration(new ItemDecoration(padding, padding));
    }

    private List<IBaseInfo> getMenuData() {
        int[] iconList = new int[]{R.drawable.color_pick_select_color_bg,
                R.drawable.color_pick_remove_color_bg,
                R.drawable.color_pick_edge_shrinkage_bg,
                R.drawable.color_pick_strength_bg};
        int[] iconDesc = new int[]{R.string.menu_color_pick_color_picker,
                R.string.menu_color_pick_overflow_removal,
                R.string.menu_color_pick_edge_shrinkage,
                R.string.menu_color_strength};
        String[] packageId = new String[]{NvsConstants.MasterKeyer.KEY_COLOR,
                NvsConstants.MasterKeyer.SPILL_REMOVAL_INTENSITY,
                NvsConstants.MasterKeyer.SHRINK_INTENSITY,
                NvsConstants.MasterKeyer.KEY_APERTURE};
        float[] initParam;
        if (mInitParam != null) {
            initParam = new float[]{0, mInitParam[0], mInitParam[1], mInitParam[2]};
        } else {
            initParam = new float[4];
        }
        Resources resources = getResources();
        List<IBaseInfo> data = new ArrayList<>();
        for (int index = 0; index < iconList.length; index++) {
            ItemInfo itemInfo = new ItemInfo();
            itemInfo.setCoverId(iconList[index]);
            itemInfo.setName(resources.getString(iconDesc[index]));
            itemInfo.setEffectId(packageId[index]);
            itemInfo.setEffectStrength(initParam[index] * 100);
            data.add(itemInfo);
        }
        return data;
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.tv_reset) {
            mColorAdapter.setSelectPosition(0);
            List<IBaseInfo> data = mColorAdapter.getData();
            for (IBaseInfo datum : data) {
                if (NvsConstants.MasterKeyer.KEY_APERTURE.equals(datum.getEffectId())) {
                    datum.setEffectStrength(100);
                } else {
                    datum.setEffectStrength(0);
                }
            }
            mCurrentPosition = 0;
            mSeekBar.setVisibility(INVISIBLE);
            if (mOnEventChangedListener != null) {
                mOnEventChangedListener.onReset();
            }
        } else if (id == R.id.iv_confirm) {
            if (mOnEventChangedListener != null) {
                mOnEventChangedListener.onConfirm();
            }
        }

    }

    public static class ColorPickAdapter extends BaseSelectAdapter<IBaseInfo> {
        public ColorPickAdapter() {
            super(R.layout.view_color_pick_item);
        }

        @Override
        protected void convert(@NonNull BaseViewHolder helper, IBaseInfo item) {
            CheckBox icon = helper.getView(R.id.icon);
            TextView name = helper.getView(R.id.name);
            icon.setBackgroundResource(item.getCoverId());
            icon.setClickable(false);
            name.setText(item.getName());
            if (getSelectPosition() == helper.getAdapterPosition()) {
                icon.setChecked(true);
                name.setTextColor(mContext.getResources().getColor(R.color.adjust_selected_bg));
            } else {
                icon.setChecked(false);
                name.setTextColor(mContext.getResources().getColor(R.color.white_8));
            }
        }
    }

    /**
     * The type Edit adjust info.
     * 编辑调整信息
     */
    public static class ItemInfo extends BaseInfo {
        private String effectId;
        private float effectStrength;

        @Override
        public String getEffectId() {
            return effectId;
        }

        @Override
        public void setEffectId(String effectId) {
            this.effectId = effectId;
        }

        @Override
        public float getEffectStrength() {
            return effectStrength;
        }

        @Override
        public void setEffectStrength(float effectStrength) {
            this.effectStrength = effectStrength;
        }
    }

    public interface OnEventChangedListener {
        /**
         * On menu clicked.
         *
         * @param position the position
         */
        void onMenuClicked(int position);

        /**
         * On data changed.
         *
         * @param key      the key
         * @param progress the progress
         */
        void onDataChanged(String key, int progress);

        /**
         * On confirm.
         */
        void onConfirm();

        /**
         * On reset.
         */
        void onReset();
    }
}
