package com.meishe.myvideo.view;

import android.content.Context;
import android.util.AttributeSet;
import android.view.SurfaceView;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;

import com.meicam.sdk.NvsMultiThumbnailSequenceView;
import com.meishe.myvideo.R;
import com.meishe.myvideo.bean.RecordAreaInfo;
import java.util.HashMap;
import java.util.Map;

/**
 * 录制的轨道 View
 * Recorded track View
 */
public class MYRecordTrackView extends RelativeLayout {

    private Context mContext;
    private Map<Long, RecordAreaInfo> mAreasInfo;
    private LinearLayout mLlAllView;
    private RelativeLayout mRlRecordAreasView;


    public MYRecordTrackView(Context context) {
        super(context);
        init(context);
    }

    public MYRecordTrackView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    private void init(Context context) {
        mContext = context;
        mAreasInfo = new HashMap<>();
    }

    /**
     * Init record view.
     * 初始化记录视图
     * @param sequenceView  the sequence view 顺序图
     * @param sequenceWidth the sequence width 序列的宽度
     */
    public void initRecordView(NvsMultiThumbnailSequenceView sequenceView, int sequenceWidth) {
        mLlAllView = new LinearLayout(mContext);
        LinearLayout.LayoutParams allViewsParams = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
        addView(mLlAllView, allViewsParams);
        mRlRecordAreasView = new RelativeLayout(mContext);
        LinearLayout.LayoutParams recordAreaParams = new LinearLayout.LayoutParams(sequenceWidth, ViewGroup.LayoutParams.MATCH_PARENT);
        View leftMarginView = new View(mContext);
        LinearLayout.LayoutParams leftMarginViewParams = new LinearLayout.LayoutParams(sequenceView.getStartPadding(), LayoutParams.MATCH_PARENT);
        View rightMarginView = new View(mContext);
        LinearLayout.LayoutParams rightMarginViewParams = new LinearLayout.LayoutParams(sequenceView.getEndPadding(), LayoutParams.MATCH_PARENT);
        mLlAllView.addView(leftMarginView, leftMarginViewParams);
        mLlAllView.addView(mRlRecordAreasView, recordAreaParams);
        mLlAllView.addView(rightMarginView, rightMarginViewParams);
    }

    /**
     * Add  record view.
     * 添加记录视图
     * @param inPoint             the in point 点
     * @param outPoint            the out point 出点
     * @param filePath            the file path 文件路径
     * @param pixelPerMicrosecond the pixel per microsecond 像素每微秒
     */
    public void addRecordView(long inPoint, long outPoint, String filePath,double pixelPerMicrosecond) {
        int begin_point = (int) (inPoint * pixelPerMicrosecond);
        View recordView = new SurfaceView(mContext);
        recordView.setBackgroundColor(mContext.getResources().getColor(R.color.white));


        int view_width = 0;
        if (outPoint > 0) {
            view_width = (int) ((outPoint - inPoint) * pixelPerMicrosecond);
        }

        RelativeLayout.LayoutParams layoutParams = new RelativeLayout.LayoutParams(view_width, ViewGroup.LayoutParams.MATCH_PARENT);
        layoutParams.setMargins(begin_point, 0, 0, 0);
        mRlRecordAreasView.addView(recordView, layoutParams);

        RecordAreaInfo areaInfo = new RecordAreaInfo();
        areaInfo.setInPoint(inPoint);
        areaInfo.setOutPoint(outPoint);
        areaInfo.setInPosition(begin_point);
        areaInfo.setAreaView(recordView);
        recordView.setTag(areaInfo);
        mAreasInfo.put(inPoint, areaInfo);

    }

    /**
     * Update record view.
     * 更新记录视图
     * @param inPoint             the in point  点
     * @param outPoint            the out point出点
     * @param pixelPerMicrosecond the pixel per microsecond 像素每微秒
     */
    public void updateRecordView(long inPoint, long outPoint, double pixelPerMicrosecond) {
        RecordAreaInfo areaInfo = mAreasInfo.get(inPoint);
        if (areaInfo == null || areaInfo.getAreaView() == null) {
            return;
        }
        RelativeLayout.LayoutParams layoutParams = (RelativeLayout.LayoutParams) areaInfo.getAreaView().getLayoutParams();
        if (layoutParams != null) {
            int begin_point = areaInfo.getInPosition();
            int view_width = (int) ((outPoint - inPoint) * pixelPerMicrosecond);
            layoutParams.setMargins(begin_point, 0, 0, 0);
            layoutParams.width = view_width;
            areaInfo.getAreaView().setLayoutParams(layoutParams);
            areaInfo.setOutPoint(outPoint);
            mRlRecordAreasView.requestLayout();
        }
    }

    /**
     * Get record container view view.
     * 获得记录容器视图
     * @return the view
     */
    public View getRecordContainerView(){
        return mLlAllView;
    }

}
