package com.meishe.myvideo.view;

import android.content.Context;
import android.widget.TextView;

import com.meishe.engine.interf.IBaseInfo;
import com.meishe.myvideo.R;
import com.meishe.myvideo.adapter.BaseSelectAdapter;
import com.meishe.myvideo.adapter.SpeedCurveAdapter;
import com.meishe.myvideo.view.base.BaseConfirmMenuView;
import com.meishe.myvideo.view.presenter.BaseConfirmPresenter;
import com.meishe.myvideo.view.presenter.SpeedCurvePresenter;

import java.util.List;


/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2020/12/30 15:35
 * @Description :曲线变速菜单 The speed curve menu.
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class MYSpeedCurveMenu extends BaseConfirmMenuView {
    public MYSpeedCurveMenu(Context context) {
        super(context);
        mAssetsTypeTab.setVisibility(GONE);
    }

    @Override
    public BaseSelectAdapter<IBaseInfo> getAdapter() {
        if (mAdapter == null) {
            mAdapter = new SpeedCurveAdapter();
        }
        return mAdapter;
    }

    @Override
    protected void onItemClicked(IBaseInfo baseInfo, boolean isSelected) {
        mPresenter.onItemClicked(baseInfo, isSelected);
    }


    @Override
    protected void setContentText(TextView textView) {
        textView.setText(R.string.menu_sub_tab_change_speed_curve);

    }

    @Override
    protected BaseConfirmPresenter<? extends BaseConfirmMenuView> getPresenter() {
        SpeedCurvePresenter presenter = new SpeedCurvePresenter();
        presenter.attachView(this);
        return presenter;
    }

    /**
     * Update view.
     * 更新视图
     *
     * @param data the data
     */
    public void updateView(List<IBaseInfo> data) {
        mAdapter.setNewData(data);
    }
}
