package com.meishe.myvideo.view.pop;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.meishe.myvideo.R;
import com.meishe.third.pop.core.CenterPopupView;

/**
 * All rights reserved,Designed by www.meishesdk.com
 * 常见的弹出式窗口
 * A common pop-up window
 * <AUTHOR> LiHangZhou
 * @CreateDate :2020/12/23 15:27
 * @Description :
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class CommonPop extends CenterPopupView {
    private TextView mTvTitle;
    private TextView mTvFirstTip;
    // private TextView mTvSecondTip;
    private TextView mTvLeftConfirm, mTvRightConfirm;
    private View mTitleLine;
    private EventListener mListener;
    private String mTitle;
    private String mFirstTip;
    private String mLeftConfirm;
    private String mRightConfirm;

    public CommonPop(@NonNull Context context) {
        super(context);
    }

    @Override
    protected int getImplLayoutId() {
        return R.layout.common_dialog;
    }

    @Override
    protected void onCreate() {
        super.onCreate();
        initView();
    }

    private void initView() {
        mTvTitle = findViewById(R.id.tv_title);
        mTvFirstTip = findViewById(R.id.tv_first_tip);
        //mTvSecondTip = findViewById(R.id.tv_second_tip);
        mTvLeftConfirm = findViewById(R.id.tv_confirm_left);
        mTvRightConfirm = findViewById(R.id.tv_confirm_right);
        mTitleLine = findViewById(R.id.v_title_line);
        mTvLeftConfirm.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
                if (mListener != null) {
                    mListener.onConfirm(true);
                }
            }
        });
        mTvRightConfirm.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
                if (mListener != null) {
                    mListener.onConfirm(false);
                }
            }
        });
    }

    /**
     * 显示弹窗
     * Show dialog
     *
     * @param title        The title
     * @param tip          The tip
     * @param leftConfirm  The leftConfirm
     * @param rightConfirm The rightConfirm
     */
    public void show(String title, String tip, String leftConfirm, String rightConfirm) {
        mTitle = title;
        mFirstTip = tip;
        mLeftConfirm = leftConfirm;
        mRightConfirm = rightConfirm;
        show();
    }

    /**
     * 设置事件监听
     * Set event listener
     *
     * @param listener The listener
     * @return the event listener
     */
    public CommonPop setEventListener(EventListener listener) {
        mListener = listener;
        return this;
    }

    @Override
    protected void onShow() {
        super.onShow();
        if (TextUtils.isEmpty(mTitle)) {
            mTvTitle.setVisibility(View.GONE);
            mTitleLine.setVisibility(View.GONE);
        } else {
            mTitleLine.setVisibility(View.VISIBLE);
            mTvTitle.setVisibility(View.VISIBLE);
            mTvTitle.setText(mTitle);
        }
        if (TextUtils.isEmpty(mFirstTip)) {
            mTvFirstTip.setVisibility(View.GONE);
        } else {
            mTvFirstTip.setText(mFirstTip);
            mTvFirstTip.setVisibility(View.VISIBLE);
        }
        if (TextUtils.isEmpty(mLeftConfirm)) {
            mTvLeftConfirm.setVisibility(GONE);
        } else {
            mTvLeftConfirm.setVisibility(VISIBLE);
            mTvLeftConfirm.setText(mLeftConfirm);
        }
        if (TextUtils.isEmpty(mRightConfirm)) {
            mTvRightConfirm.setVisibility(GONE);
        } else {
            mTvRightConfirm.setVisibility(VISIBLE);
            mTvRightConfirm.setText(mRightConfirm);
        }
    }

    /**
     * The interface Event listener.
     * 事件监听接口
     */
    public interface EventListener {
        /**
         * On confirm.
         * 确认
         * @param isLeft the is left
         */
        void onConfirm(boolean isLeft);
    }
}
