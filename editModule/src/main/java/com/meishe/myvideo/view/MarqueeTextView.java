package com.meishe.myvideo.view;

import android.content.Context;
import android.graphics.Rect;
import android.text.TextUtils;
import android.util.AttributeSet;

/**
 * All rights Reserved, Designed By www.meishesdk.com
 * 跑马灯
 *
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2021/4/12 15:37
 * @Description:
 * @Copyright: www.meishesdk.com Inc. All rights reserved.
 */
public class MarqueeTextView extends androidx.appcompat.widget.AppCompatTextView {
    public MarqueeTextView(Context context) {
        this(context, null);
    }

    public MarqueeTextView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public MarqueeTextView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init() {
        //设置单行 Set single line.
        setSingleLine();
        //设置Ellipsize  Set ellipsize.
        setEllipsize(TextUtils.TruncateAt.MARQUEE);
        //获取焦点 Set focusable.
        setFocusable(true);
        //走马灯的重复次数，-1代表无限重复
        //Set marquee repeat limit,-1 represents infinite repetition.
        setMarqueeRepeatLimit(-1);
        //强制获得焦点 Force focus.
        setFocusableInTouchMode(true);
    }

    @Override
    public boolean isFocused() {
        return true;
    }

    /**
     * 用于EditText抢注焦点的问题
     * Issues used for EditText preemption focus.
     * */
    @Override
    protected void onFocusChanged(boolean focused, int direction, Rect previouslyFocusedRect) {
        if (focused) {
            super.onFocusChanged(focused, direction, previouslyFocusedRect);
        }
    }

    /**
     * Window与Window间焦点发生改变时的回调
     * Callback when the focus changes between Windows.
     * */
    @Override
    public void onWindowFocusChanged(boolean hasWindowFocus) {
        if (hasWindowFocus)
            super.onWindowFocusChanged(hasWindowFocus);
    }

}
