package com.meishe.myvideo.view;

import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.app.hubert.guide.NewbieGuide;
import com.app.hubert.guide.model.GuidePage;
import com.google.android.material.tabs.TabLayout;
import com.meishe.base.constants.Constants;
import com.meishe.base.manager.LinearLayoutManagerWrapper;
import com.meishe.base.utils.BarUtils;
import com.meishe.base.utils.ScreenUtils;
import com.meishe.base.view.ScaleView;
import com.meishe.base.view.decoration.ItemDecoration;
import com.meishe.engine.EditorEngine;
import com.meishe.engine.bean.MeicamTimeline;
import com.meishe.engine.bean.MeicamTimelineVideoFxClip;
import com.meishe.engine.bean.MeicamTimelineVideoFxTrack;
import com.meishe.engine.bean.MeicamVideoClip;
import com.meishe.engine.bean.MeicamVideoFx;
import com.meishe.engine.bean.Plug;
import com.meishe.engine.bean.PlugDetail;
import com.meishe.engine.bean.bridges.AtomicFxBridge;
import com.meishe.engine.util.ColorUtil;
import com.meishe.myvideo.R;
import com.meishe.myvideo.activity.DraftEditActivity;
import com.meishe.myvideo.adapter.BaseSelectAdapter;
import com.meishe.myvideo.adapter.PlugAdapter;
import com.meishe.myvideo.adapter.PlugMenuAdapter;
import com.meishe.myvideo.interfaces.BottomEventListener;
import com.meishe.myvideo.interfaces.PlugsEventListener;
import com.meishe.myvideo.view.interf.IBottomView;
import com.meishe.myvideo.view.pop.PlugMenuDialog;
import com.meishe.third.adpater.BaseQuickAdapter;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;

/**
 * All rights Reserved, Designed By www.meishesdk.com
 *
 * @Author: ChuChenGuang
 * @CreateDate: 2022/6/20 14:18
 * @Description:
 * @Copyright: www.meishesdk.com Inc. All rights reserved.
 */
public class AtomicEditMenuView extends FrameLayout implements IBottomView {

    protected BaseSelectAdapter<PlugDetail.Param> mMenuAdapter;
    protected BaseSelectAdapter<Plug> mPlugAdapter;
    private final Context mContext;
    private ImageView mIvBack;
    private TextView mTvAddPlugDesc;
    private ImageView mImageAddPlug;
    private TextView mTvCorona;
    private ColorSeekBar mColorSeekBar;
    private RelativeLayout mCoronaLayout;
    private RelativeLayout mAdjustLayout;
    private ImageView mIvAddKeyFrame;
    private ImageView mIvAddKeyFrameCurve;
    private RecyclerView mMenuRecyclerview;
    private RecyclerView mPlugRecyclerview;
    private ScaleView mScaleView;
    private TabLayout mTabLayout;
    private List<Plug> plugs = new ArrayList<>();
    private MeicamTimelineVideoFxClip meicamTimelineVideoFxClip;
    private MeicamVideoClip meicamVideoClip;
    private Plug mSelectedPlug;
    private boolean clipPlugsVisible;
    private MeicamVideoFx meicamVideoFx;
    private PlugMenuDialog mMenuDialog;
    /**
     * 是否展示关键帧
     * Whether the key frame view is showed.
     */
    private boolean isShowKeyFrameView = false;
    private Activity mActivity;
    private PlugDetail.Param selectedParam = null;
    private final BaseQuickAdapter.OnItemLongClickListener onItemLongClickListener = new BaseQuickAdapter.OnItemLongClickListener() {
        @Override
        public boolean onItemLongClick(BaseQuickAdapter adapter, View view, int position) {
            if (!clipPlugsVisible) {
                return true;
            }
            mSelectedPlug = plugs.get(position);
            if (meicamVideoClip != null && mSelectedPlug != null) {
                meicamVideoFx = meicamVideoClip.getVideoFx(MeicamVideoFx.SubType.SUB_TYPE_PLUG_FX, mSelectedPlug.plugName, mSelectedPlug.getClipIndex());
            }
            if (mMenuDialog == null) {
                int offX = (int) ((ScreenUtils.getScreenWidth() - mContext.getResources().getDimension(R.dimen.dp_px_630)) / 2);
                boolean isNavBarVisible = BarUtils.isNavBarVisible(mActivity);
                int offY;
                if (isNavBarVisible) {
                    offY = (int) (ScreenUtils.getScreenHeight() - BarUtils.getNavBarHeight(AtomicEditMenuView.this.getContext()) -
                            ScreenUtils.getStatusBarHeight() - mContext.getResources().getDimension(R.dimen.dp_px_354));
                } else {
                    offY = (int) (ScreenUtils.getScreenHeight() -
                            ScreenUtils.getStatusBarHeight() - mContext.getResources().getDimension(R.dimen.dp_px_354));
                }

                mMenuDialog = PlugMenuDialog.create(mContext, offX, offY, param -> {
                    if (mContext.getString(R.string.plug_menu_hide).equals(param.paramName)) {
                        AtomicEditMenuView.this.changePlugVisible(param);
                    }
                    if (mEventListener != null) {
                        mEventListener.onParamItemClick(mSelectedPlug, param);
                    }
                });
            }
            mMenuDialog.setNewData(AtomicEditMenuView.this.getEditMenu());
            mMenuDialog.show();
            return true;
        }
    };

    public AtomicEditMenuView(Context context) {
        this(context, null);
    }

    public AtomicEditMenuView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public AtomicEditMenuView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        mContext = context;
        initView();
    }

    private void initView() {
        View view = LayoutInflater.from(getContext()).inflate(R.layout.view_plug_edit, this);
        mCoronaLayout = view.findViewById(R.id.corona_layout);
        mTvCorona = view.findViewById(R.id.tv_corona);
        mAdjustLayout = view.findViewById(R.id.root_adjust);
        mIvAddKeyFrameCurve = view.findViewById(R.id.iv_add_key_frame_curve);
        mIvAddKeyFrame = view.findViewById(R.id.iv_add_key_frame);
        mColorSeekBar = view.findViewById(R.id.corlor_seekbar);
        mIvBack = view.findViewById(R.id.iv_back);
        mTvAddPlugDesc = view.findViewById(R.id.tv_add_plug_desc);
        mImageAddPlug = view.findViewById(R.id.image_add_plug_menu);
        mMenuRecyclerview = view.findViewById(R.id.recyclerview);
        mScaleView = view.findViewById(R.id.scaleview);
        mTabLayout = view.findViewById(R.id.tab_layout);
        mPlugRecyclerview = view.findViewById(R.id.plug_recyclerview);
        mMenuAdapter = new PlugMenuAdapter(mContext);
        LinearLayoutManager layoutManager = new LinearLayoutManagerWrapper(getContext(), LinearLayoutManager.HORIZONTAL, false);
        mMenuRecyclerview.setLayoutManager(layoutManager);
        mMenuRecyclerview.setAdapter(mMenuAdapter);
        mMenuRecyclerview.addItemDecoration(new ItemDecoration((int) getResources().getDimension(R.dimen.dp18), (int) getResources().getDimension(R.dimen.dp18)));

        mPlugAdapter = new PlugAdapter(mContext);
        LinearLayoutManager layoutPlug = new LinearLayoutManagerWrapper(getContext(), LinearLayoutManager.HORIZONTAL, false);
        mPlugRecyclerview.setLayoutManager(layoutPlug);
        mPlugRecyclerview.setAdapter(mPlugAdapter);
        mPlugRecyclerview.addItemDecoration(new ItemDecoration((int) getResources().getDimension(R.dimen.dp10), (int) getResources().getDimension(R.dimen.dp10)));
        initListener();
    }


    private void initListener() {
        mPlugAdapter.setOnItemClickListener((adapter, view, position) -> {
            Plug plug = plugs.get(position);
            showMenu(plug);
            if (meicamVideoClip != null && mSelectedPlug != null) {
                meicamVideoFx = meicamVideoClip.getVideoFx(MeicamVideoFx.SubType.SUB_TYPE_PLUG_FX, mSelectedPlug.plugName, mSelectedPlug.getClipIndex());
            }
            if (mEventListener != null) {
                mEventListener.onPlugItemClick(plug);
            }
        });


        mMenuAdapter.setOnItemClickListener((adapter, view, position) -> {
            mMenuAdapter.setSelectPosition(position);
            PlugDetail.Param opParam = mMenuAdapter.getData().get(position);
            if (mContext.getString(R.string.plug_menu_delete).equals(opParam.paramName)) {
                //删除 Delete
                if (mEventListener != null) {
                    mEventListener.onParamItemClick(mSelectedPlug, opParam);
                }
                mIvBack.performClick();
            } else if (mContext.getString(R.string.plug_menu_copy).equals(opParam.paramName)
                    || mContext.getString(R.string.plug_menu_bedspread).equals(opParam.paramName)) {
                if (mEventListener != null) {
                    mEventListener.onParamItemClick(mSelectedPlug, opParam);
                }
            } else if (mContext.getString(R.string.plug_menu_hide).equals(opParam.paramName)) {
                //隐藏显示 Hide
                showView(-1);
                changePlugVisible(opParam);
                if (mEventListener != null) {
                    mEventListener.onParamItemClick(mSelectedPlug, opParam);
                }
            } else {
                selectedParam = mMenuAdapter.getData().get(position);
                String valueType = selectedParam.valueType;
                String valueDefault = selectedParam.valueDefault;
                if (selectedParam.modeList != null && selectedParam.modeList.size() != 0) {
                    showView(0);
                    int tabPosition = 0;
                    List<PlugDetail.Param.Type> types = selectedParam.modeList;
                    for (int i = 0; i < types.size(); i++) {
                        if (selectedParam.valueDefault.equals(types.get(i).paramValue)) {
                            tabPosition = i;
                            break;
                        }
                    }
                    mTabLayout.removeAllTabs();
                    for (PlugDetail.Param.Type type : types) {
                        addTab(type.getName());
                    }
                    mTabLayout.selectTab(mTabLayout.getTabAt(tabPosition));
                } else {
                    if (Constants.PlugType.BOOL.equals(valueType)) {
                        //bool形式的，需要UI checkBox Bool format, requires UI checkBox.
                        showView(-1);
                        boolean defaultValue = Boolean.parseBoolean(valueDefault);
                        selectedParam.valueDefault = String.valueOf(!defaultValue);
                        if (mEventListener != null) {
                            mEventListener.onParamValueChange(mSelectedPlug, selectedParam);
                        }
                    } else if (Constants.PlugType.FLOAT.equals(valueType)
                            || Constants.PlugType.POSITION2D_X.equals(valueType)
                            || Constants.PlugType.POSITION2D_Y.equals(valueType)) {
                        //float形式的，需要UI 轮盘 Float format, requires UI wheel.
                        showView(1);
                        initScaleViewData(valueDefault);
                    } else if (Constants.PlugType.INT_CHOOSE.equals(valueType)) {
                        //int形式的 选项式  分屏特技 水平数量 可以添加关键帧
                        // The number of optional split screen stunt levels in the form of int can be added with keyframes.
                        showView(0);
                        int tabPosition = 0;
                        for (int i = (int) selectedParam.valueMin; i <= selectedParam.valueMax; i++) {
                            if (selectedParam.valueDefault.equals(String.valueOf(i))) {
                                tabPosition = i;
                                break;
                            }
                        }
                        mTabLayout.removeAllTabs();
                        for (int i = (int) selectedParam.valueMin; i <= selectedParam.valueMax; i++) {
                            addTab(String.valueOf(i));
                        }
                        mTabLayout.selectTab(mTabLayout.getTabAt((int) (tabPosition - selectedParam.valueMin)));
                    } else if (Constants.PlugType.COLOR.equals(valueType)) {
                        //CO 颜色 形式的 边缘增强 Edge enhancement in CO color form.
                        showView(2);
//                        NvsColor nvsColor;
//                        if (valueDefault.contains("#")) {
////                            mColorSeekBar.setCurrentColor(Color.parseColor(valueDefault));
//                        } else {
//                            valueDefault = valueDefault.substring(1, valueDefault.length() - 1);
//                            String[] strings = valueDefault.split(",");
//                            float[] defaultFloat = new float[strings.length];
//                            for (int i = 0; i < strings.length; i++) {
//                                defaultFloat[i] = Integer.parseInt(strings[i]);
//                            }
//                            nvsColor = ColorUtil.colorFloatToNvsColor(defaultFloat);
////                            mColorSeekBar.setCurrentColor(Color.argb(nvsColor.a, nvsColor.r, nvsColor.g, nvsColor.b));
//                        }

                    } else if (Constants.PlugType.CURVE.equals(valueType)) {
                        showView(-1);
                    }
                }
                if (mEventListener != null) {
                    mEventListener.onParamItemClick(mSelectedPlug, selectedParam);
                }
            }

            adapter.notifyDataSetChanged();

        });

        mTabLayout.addOnTabSelectedListener(new TabLayout.OnTabSelectedListener() {
            @Override
            public void onTabSelected(TabLayout.Tab tab) {
                if (mEventListener != null) {
                    if (selectedParam.modeList != null && selectedParam.modeList.size() != 0) {
                        selectedParam.valueDefault = selectedParam.modeList.get(tab.getPosition()).paramValue;
                    } else {
                        selectedParam.valueDefault = Objects.requireNonNull(tab.getText()).toString();
                    }
                    mEventListener.onParamValueChange(mSelectedPlug, selectedParam);
                }
            }

            @Override
            public void onTabUnselected(TabLayout.Tab tab) {

            }

            @Override
            public void onTabReselected(TabLayout.Tab tab) {

            }
        });

        mIvBack.setOnClickListener(view -> {
            if (mImageAddPlug.getVisibility() == VISIBLE) {
                //添加显示的时候dismiss Dismiss when adding a display.
                if (mEventListener != null) {
                    mEventListener.dismiss();
                }
            } else {
                selectedParam = null;
                mSelectedPlug = null;
                showView(-1);
                if (mEventListener != null) {
                    mEventListener.onDisplayAddPlug();
                }
                //展示添加 Display
                displayMenuLayout(false);
            }
        });

        mImageAddPlug.setOnClickListener(view -> {
            if (mEventListener != null) {
                mEventListener.onJumpPlugList();
            }
        });

        mScaleView.setOnScaleChangeListener(new ScaleView.OnScaleChangeListener() {
            @Override
            public void OnChange(double index, boolean isFromUser) {
                if (selectedParam == null) {
                    return;
                }
                String value = String.valueOf(Math.round(index));
                mTvCorona.setText(value);
                if (mEventListener != null && isFromUser) {
                    double sdkValue = (index / (mScaleView.getMaxIndex() - mScaleView.getMinIndex()))
                            * (selectedParam.valueMax - selectedParam.valueMin);
                    selectedParam.valueDefault = String.valueOf(selectedParam.valueMin > 0 ? sdkValue + selectedParam.valueMin : sdkValue);
                    mEventListener.onParamValueChange(mSelectedPlug, selectedParam);
                }
            }

            @Override
            public void OnChangeFinish() {
                if (mEventListener != null) {
                    mEventListener.onParamValueChangeFinish(mSelectedPlug, selectedParam);
                }
            }

            @Override
            public void OnChangeStart() {
                if (mEventListener != null) {
                    mEventListener.onParamValueChangeStart(mSelectedPlug, selectedParam);
                }
            }
        });
        mColorSeekBar.setOnColorChangedListener(new ColorSeekBar.OnColorChangedListener() {
            @Override
            public void onColorChanged(int color) {
                selectedParam.valueDefault = ColorUtil.intColorToHexString(color);
                if (mEventListener != null) {
                    mEventListener.onParamValueChange(mSelectedPlug, selectedParam);
                }
            }

            @Override
            public void onColorChangeStart(int color) {
                selectedParam.valueDefault = ColorUtil.intColorToHexString(color);
                if (mEventListener != null) {
                    mEventListener.onParamValueChangeStart(mSelectedPlug, selectedParam);
                }
            }

            @Override
            public void onColorChangeFinish() {
                if (mEventListener != null) {
                    mEventListener.onParamValueChangeFinish(mSelectedPlug, selectedParam);
                }
            }
        });

        mIvAddKeyFrame.setOnClickListener(view -> {
            if (mEventListener.onKeyFrameClick(mSelectedPlug, selectedParam, addKeyFrame)) {
                addKeyFrame = !addKeyFrame;
                showKeyFrameView(addKeyFrame);
            }
        });
        mIvAddKeyFrameCurve.setOnClickListener(view -> {
            if (mIvAddKeyFrameCurve.isEnabled()) {
                mEventListener.onKeyFrameCurveClick(mSelectedPlug, selectedParam);
            }
        });
    }

    private int scaleViewDuration = 100;

    public void initScaleViewData(String value) {
        if (selectedParam == null) {
            return;
        }
        float defaultValue = Float.parseFloat(value);
        int max, min;
        if (selectedParam.isAngel()) {
            //角度 UI上展示 取值范围 最大为 -360 到 360
            // The maximum displayed value range on the angle UI is -360 to 360.
            max = 360;
            min = selectedParam.valueMin < 0 ? -360 : 0;
        } else {
            // float 数值 取值范围 最大 为 -100 到 100
            // The maximum range of float values is -100 to 100.
            max = 100;
            min = selectedParam.valueMin < 0 ? -100 : 0;
        }
        mScaleView.setMaxIndex(max);
        mScaleView.setMinIndex(min);
        scaleViewDuration = max - min;
        float nowIndex = (defaultValue / (selectedParam.valueMax - selectedParam.valueMin)) * scaleViewDuration;
        mScaleView.setNowIndex(Math.round(nowIndex));
    }

    /**
     * Sets now index.
     *
     * @param value the value 特效参数值
     */
    public void setNowIndex(float value) {
        if (mScaleView != null && mScaleView.getVisibility() == VISIBLE) {
            float index = (value / (selectedParam.valueMax - selectedParam.valueMin)) * scaleViewDuration;
            mScaleView.setNowIndex(Math.round(index));
        }
    }

    private void changePlugVisible(PlugDetail.Param param) {
        boolean defaultValue = Boolean.parseBoolean(param.valueDefault);
        param.valueDefault = String.valueOf(!defaultValue);
        mSelectedPlug.setDisplay(!defaultValue);
        param.setName(mSelectedPlug.isDisplay() ? getContext().getString(R.string.plug_visible)
                : getContext().getString(R.string.plug_hide));
        mPlugAdapter.notifyDataSetChanged();
    }


    private void addTab(String title) {
        TabLayout.Tab tab = mTabLayout.newTab();
        tab.setText(title);
        mTabLayout.addTab(tab);
    }

    /**
     * Update view.
     * 更新视图
     *
     * @param videoClip the videoClip
     */

    public void updateView(Activity activity, MeicamVideoClip videoClip, MeicamTimelineVideoFxClip clip) {
        mActivity = activity;
        if (videoClip != null && clip == null) {
            //片段特效P Fragment Effect P
            meicamVideoClip = videoClip;
            meicamTimelineVideoFxClip = null;
            plugs = getPlugsByMeicamVideoClip(videoClip);
            displayMenuLayout(false);
        } else if (clip == null) {
            plugs = getPlugsByMeicamTimeline();
            displayMenuLayout(false);
        } else {
            //timeline特效 The timeline effect
            meicamTimelineVideoFxClip = clip;
            meicamVideoClip = null;
            plugs = getPlugsByMeicamTimeline();
            for (Plug plug : plugs) {
                if (plug.getTrackIndex() == clip.getTrackIndex() && plug.getClipIndex() == clip.getIndex()) {
                    showMenu(plug);
                    break;
                }
            }
            displayMenuLayout(true);
        }
        if (plugs != null && mPlugAdapter != null) {
            mPlugAdapter.setNewData(plugs);
        }
        if (mMenuRecyclerview != null) {
            mMenuRecyclerview.scrollToPosition(0);
        }
        if (plugs != null && plugs.size() != 0 && clipPlugsVisible) {
            if (mEventListener != null) {
                mEventListener.onShowPlugList();
            }
        }
    }


    private PlugsEventListener mEventListener;

    public void showView(int viewPosition) {
        if (viewPosition == 0) {
            //展示选项 display menu
            mAdjustLayout.setVisibility(VISIBLE);
            mTabLayout.setVisibility(VISIBLE);
            mCoronaLayout.setVisibility(INVISIBLE);
            mColorSeekBar.setVisibility(INVISIBLE);
            if (Constants.PlugType.INT_CHOOSE.equals(selectedParam.valueType)) {
                isShowKeyFrameView = true;
                showKeyFrameView();
            } else {
                isShowKeyFrameView = false;
                notShowKeyFrameView();
            }
        } else if (viewPosition == 1) {
            //展示轮盘 Display Wheel
            mAdjustLayout.setVisibility(VISIBLE);
            mTabLayout.setVisibility(INVISIBLE);
            mCoronaLayout.setVisibility(VISIBLE);
            mColorSeekBar.setVisibility(INVISIBLE);
            isShowKeyFrameView = true;
            showKeyFrameView();
        } else if (viewPosition == 2) {
            //展示颜色 Display color
            mAdjustLayout.setVisibility(VISIBLE);
            mTabLayout.setVisibility(INVISIBLE);
            mCoronaLayout.setVisibility(INVISIBLE);
            mColorSeekBar.setVisibility(VISIBLE);
            isShowKeyFrameView = false;
            notShowKeyFrameView();
        } else {
            mAdjustLayout.setVisibility(GONE);
            mTabLayout.setVisibility(INVISIBLE);
            mCoronaLayout.setVisibility(INVISIBLE);
            mColorSeekBar.setVisibility(INVISIBLE);
            isShowKeyFrameView = false;
            notShowKeyFrameView();
        }
    }


    public void displayMenuLayout(boolean showMenu) {
        clipPlugsVisible = meicamVideoClip != null && plugs != null && plugs.size() != 0;
        boolean timelinePlugsVisible = plugs != null && plugs.size() != 0;
        mAdjustLayout.setVisibility(GONE);
        if (showMenu) {
            //展示菜单 Display menu
            mPlugRecyclerview.setVisibility(INVISIBLE);
            mMenuRecyclerview.setVisibility(View.VISIBLE);
            mImageAddPlug.setVisibility(View.INVISIBLE);
            mTvAddPlugDesc.setVisibility(View.INVISIBLE);
        } else {
            //展示插件描述 Display plugin description.
            if (clipPlugsVisible) {
                mPlugAdapter.setOnItemLongClickListener(onItemLongClickListener);
            } else {
                mPlugAdapter.setOnItemLongClickListener(null);
            }
            mPlugRecyclerview.setVisibility((clipPlugsVisible || timelinePlugsVisible) ? VISIBLE : INVISIBLE);
            mTvAddPlugDesc.setVisibility((clipPlugsVisible || timelinePlugsVisible) ? INVISIBLE : VISIBLE);
            mMenuRecyclerview.setVisibility(View.INVISIBLE);
            mImageAddPlug.setVisibility(View.VISIBLE);
        }

    }


    private void showMenu(Plug plug) {
        this.mSelectedPlug = plug;
        PlugDetail plugDetail = getPlugDetailByPlug(plug);
        if (plugDetail != null) {
            List<PlugDetail.Param> params = plugDetail.paramList;
            if (params == null) {
                params = new ArrayList<>();
            }
            Iterator<PlugDetail.Param> iterable = params.iterator();
            while (iterable.hasNext()) {
                PlugDetail.Param param = iterable.next();
                //删除需要设置的默认值，且不需要在view上展示的
                // Delete the default values that need to be set and do not need to be displayed in the view.
                if (TextUtils.isEmpty(param.coverPath)) {
                    iterable.remove();
                }
            }
            if (meicamVideoClip == null) {
                params.addAll(getEditMenu());
            }
            mMenuAdapter.setSelectPosition(-1);
            mMenuAdapter.setNewData(params);
            displayMenuLayout(true);
        }
    }

    private List<PlugDetail.Param> getEditMenu() {
        List<PlugDetail.Param> params = new ArrayList<>();
        PlugDetail.Param param = new PlugDetail.Param("plug_hide",
                getContext().getString(R.string.plug_menu_hide));
        if (mSelectedPlug != null) {
            if (mSelectedPlug.isDisplay()) {
                param.setName(getContext().getString(R.string.plug_visible));
            } else {
                param.setName(getContext().getString(R.string.plug_hide));
            }
            param.valueDefault = String.valueOf(mSelectedPlug.isDisplay());
            param.valueType = Constants.PlugType.BOOL;
            params.add(param);
        }

        params.add(new PlugDetail.Param("复制", "copy",
                "plug_copy", mContext.getString(R.string.plug_menu_copy)));
        if (meicamTimelineVideoFxClip != null) {
            PlugDetail.Param paramBedspread = new PlugDetail.Param(mContext.getString(R.string.sub_menu_edit_bedspread),
                    mContext.getString(R.string.sub_menu_edit_bedspread),
                    "sub_menu_icon_edit_bedspread", mContext.getString(R.string.plug_menu_bedspread));
            paramBedspread.valueType = Constants.PlugType.BOOL;
            params.add(paramBedspread);
        }
        params.add(new PlugDetail.Param("删除", "delete",
                "plug_delete", mContext.getString(R.string.plug_menu_delete)));
        return params;
    }

    private PlugDetail getPlugDetailByPlug(Plug plug) {
        if (plug == null) {
            return null;
        }
        PlugDetail plugDetail = AtomicFxBridge.getPlugDetail(plug.effectPath);
        if (plugDetail == null) {
            return null;
        }
        if (meicamVideoClip != null) {
            MeicamVideoFx meicamVideoFx = meicamVideoClip.getVideoFx(MeicamVideoFx.SubType.SUB_TYPE_PLUG_FX, plug.plugName, plug.getClipIndex());
            plugDetail.setPlugDetail(meicamVideoFx);
        }
        if (meicamTimelineVideoFxClip != null) {
            plugDetail.setPlugDetail(meicamTimelineVideoFxClip);
        }
        return plugDetail;
    }

    private List<Plug> getPlugsByMeicamTimeline() {
        MeicamTimeline timeline = EditorEngine.getInstance().getCurrentTimeline();
        List<Plug> plugs = new ArrayList<>();
        for (int i = 0; i < timeline.getTimelineFxTrackCount(); i++) {
            MeicamTimelineVideoFxTrack meicamTimelineVideoFxTrack = timeline.getTimelineFxTrack(i);
            for (int j = 0; j < meicamTimelineVideoFxTrack.getClipCount(); j++) {
                MeicamTimelineVideoFxClip clip = meicamTimelineVideoFxTrack.getClip(j);
                if (!clip.isBuildFx()) {
                    continue;
                }
                Plug plug = AtomicFxBridge.getPlugByEffectID(clip.getDesc());
                if (plug != null) {
                    plug.setTrackIndex(clip.getTrackIndex());
                    plug.setClipIndex(clip.getIndex());
                    plug.setDisplay(clip.getIntensity() == 1);
                    plugs.add(plug);
                }
            }
        }
        return plugs;
    }

    public List<Plug> getPlugsByMeicamVideoClip(MeicamVideoClip videoClip) {
        List<Plug> plugs = new ArrayList<>();
        for (int i = 0; i < videoClip.getVideoFxCount(); i++) {
            MeicamVideoFx videoFx = videoClip.getVideoFx(i);
            if (MeicamVideoFx.SubType.SUB_TYPE_PLUG_FX.equals(videoFx.getSubType())) {
                Plug plug = AtomicFxBridge.getPlugByEffectID(videoFx.getDesc());
                if (plug != null) {
                    plug.setClipIndex(videoFx.getIndex());
                    plug.setDisplay(videoFx.getIntensity() == 1);
                    plugs.add(plug);
                }
            }
        }
        return plugs;
    }


    public MeicamVideoFx getMeicamVideoFx() {
        return meicamVideoFx;
    }

    public Plug getSelectedPlug() {
        return mSelectedPlug;
    }

    public PlugDetail.Param getSelectedParam() {
        return selectedParam;
    }

    private boolean addKeyFrame;

    public void showKeyFrameView(boolean add) {
        addKeyFrame = add;
        showKeyFrameView();
        mIvAddKeyFrame.setImageResource(add ? R.mipmap.icon_add_key_frame : R.mipmap.icon_delete_key_frame);
    }

    public void changeKeyFrameCurveState(boolean select) {
        mIvAddKeyFrameCurve.setImageResource(select ? R.mipmap.icon_add_key_frame_curve_select : R.mipmap.icon_add_key_frame_curve_unselect);
        mIvAddKeyFrameCurve.setEnabled(select);
    }

    public void notShowKeyFrameView() {

        mIvAddKeyFrame.setVisibility(GONE);
        mIvAddKeyFrameCurve.setVisibility(GONE);
    }


    public void showKeyFrameView() {
        mIvAddKeyFrame.setVisibility(VISIBLE);
        mIvAddKeyFrameCurve.setVisibility(VISIBLE);
    }


    public boolean isShowKeyFrameView() {
        return isShowKeyFrameView;
    }

    public void showTip(DraftEditActivity draftEditActivity) {
        mPlugRecyclerview.post(() -> NewbieGuide.with(draftEditActivity).setLabel("plug_guide")
                .addGuidePage(GuidePage.newInstance().addHighLight(mPlugRecyclerview
                ).setLayoutRes(R.layout.guide_view_plug)).show());

    }

    @Override
    public void setListener(BottomEventListener listener) {
        mEventListener  = (PlugsEventListener) listener;
    }

    @NonNull
    @Override
    public BottomEventListener getListener() {
        return mEventListener;
    }
}
