package com.meishe.myvideo.view;

import android.content.Context;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.SeekBar;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.meishe.base.manager.LinearLayoutManagerWrapper;
import com.meishe.base.model.BaseMvpView;
import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.NetUtils;
import com.meishe.base.utils.SizeUtils;
import com.meishe.base.utils.ToastUtils;
import com.meishe.base.utils.Utils;
import com.meishe.base.view.decoration.ItemDecoration;
import com.meishe.business.assets.view.AssetsTypeTabView;
import com.meishe.engine.asset.AssetsManager;
import com.meishe.engine.asset.bean.AssetInfo;
import com.meishe.engine.bean.BaseInfo;
import com.meishe.engine.interf.IBaseInfo;
import com.meishe.myvideo.R;
import com.meishe.myvideo.adapter.BaseSelectAdapter;
import com.meishe.myvideo.adapter.FilterAdapter;
import com.meishe.myvideo.interfaces.BottomEventListener;
import com.meishe.myvideo.view.interf.FilterView;
import com.meishe.myvideo.view.presenter.FilterPresenter;
import com.meishe.third.adpater.BaseQuickAdapter;
import com.meishe.third.pop.util.ViewUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2020/12/25 14:04
 * @Description :滤镜菜单, Filter menu
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 * 滤镜菜单视图类
 * Filter menu view class
 */
public class MYFilterMenuView extends BaseMvpView<FilterPresenter> implements FilterView, View.OnClickListener {
    private String mFilterId = "";
    private int mEffectMode;

    protected RecyclerView mRecyclerView;
    private ImageView mIvConfirm;
    protected FilterAdapter mAdapter;
    private MYSeekBarTextView mSeekBarView;
    private IBaseInfo mBaseInfo;
    private View mTopView;
    private BottomEventListener mEventListener;
    private View mResetLeftLine;
    private View mIvApplyToAllView;
    private View mTvApplyToAllView;
    protected boolean mNeedShowApply = true;
    protected boolean needShowSeekBar = true;
    protected AssetsTypeTabView mAssetsTypeTab;
    protected TextView mHintTextView;
    protected TextView mTvSeekBarDesc;
    protected int mCurrentSubType;
    private String mDownloadTag;
    private float mProgress;

    public MYFilterMenuView(Context context) {
        super(context);
    }

    @Override
    protected FilterPresenter createPresenter() {
        return new FilterPresenter();
    }

    @Override
    public void initView() {
        View view = LayoutInflater.from(getContext()).inflate(R.layout.view_menu_base_confirm, this);
        mRecyclerView = view.findViewById(R.id.width_confirm_menu_recycleView);
        mTvSeekBarDesc = view.findViewById(R.id.tv_seekbar_desc);
        TextView content = view.findViewById(R.id.tv_content);
        content.setText(R.string.sub_menu_name_edit_filter);
        mIvConfirm = view.findViewById(R.id.iv_confirm);
        mSeekBarView = view.findViewById(R.id.view_seek_bar);
        mTopView = view.findViewById(R.id.rl_top_view);
        mSeekBarView.setStartTextVisible(false);
        mSeekBarView.setEndTextVisible(false);
        mResetLeftLine = view.findViewById(R.id.reset_left_line);
        mIvApplyToAllView = view.findViewById(R.id.iv_apply_all);
        mTvApplyToAllView = view.findViewById(R.id.tv_apply_all);
        mAssetsTypeTab = view.findViewById(R.id.ttv_tab_type);
        mHintTextView = view.findViewById(R.id.tv_hint);
        ViewUtil.expandTouchArea(mTvApplyToAllView, SizeUtils.dp2px(10F));
        hideSeekBar();
        initRecyclerView();
        setApplyAble();
        initListener();

    }

    @Override
    protected void initData() {
        mPresenter.loadData(0, true);
    }

    public void initRecyclerView() {
        mAdapter = new FilterAdapter();
        LinearLayoutManager layoutManager = new LinearLayoutManagerWrapper(getContext(), LinearLayoutManager.HORIZONTAL, false);
        mRecyclerView.setLayoutManager(layoutManager);
        mRecyclerView.setAdapter(mAdapter);
        mRecyclerView.addItemDecoration(new ItemDecoration(7, 7));
    }

    private void setApplyAble() {
        int visible = mNeedShowApply ? View.VISIBLE : View.GONE;
        if (mResetLeftLine != null) {
            mResetLeftLine.setVisibility(visible);
        }
        if (mIvApplyToAllView != null) {
            mIvApplyToAllView.setVisibility(visible);
        }
        if (mTvApplyToAllView != null) {
            mTvApplyToAllView.setVisibility(visible);
        }
    }


    private void updateViewState(int subType) {
        if (mAdapter.getItemCount() <= 2) {
            mHintTextView.setVisibility(VISIBLE);
            mRecyclerView.setVisibility(INVISIBLE);
            if (!NetUtils.isNetworkAvailable(getContext())) {
                Drawable drawable = getResources().getDrawable(R.mipmap.ic_assets_data_update);
                drawable.setBounds(new Rect(0, 4, drawable.getIntrinsicHeight(), drawable.getIntrinsicHeight() + 4));
                mHintTextView.setCompoundDrawables(null, null, drawable, null);
                mHintTextView.setText(R.string.user_hint_assets_net_error_refresh);
                mHintTextView.setOnClickListener(new OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        mPresenter.loadData(mCurrentSubType, true);
                    }
                });
            } else {
                mHintTextView.setCompoundDrawables(null, null, null, null);
                mHintTextView.setText(AssetsManager.get().getErrorMsg(getContext(), subType));
            }
            hideSeekBar();
        } else {
            mHintTextView.setVisibility(GONE);
            mRecyclerView.setVisibility(VISIBLE);
            if (mAdapter.getSelectPosition() == 0) {
                hideSeekBar();
            } else {
                showSeekBar();
            }
            if (!NetUtils.isNetworkAvailable(getContext())) {
                /*
                 * 防止移动后最后面后，自动加载更多，造成加载重复数据
                 * Prevent automatic loading of more data after moving the rear end, resulting in loading duplicate data
                 */
                mAdapter.setEnableLoadMore(false);
                ToastUtils.showShort(Utils.getApp().getResources().getString(R.string.user_hint_assets_net_error));
            } else {
                /*
                 * 网络正常后，开启自动加载
                 * After the network is normal, turn on automatic loading
                 */
                mAdapter.setEnableLoadMore(true);
            }
        }
    }


    protected void onItemClicked(IBaseInfo baseInfo, int position) {
        AssetInfo assetInfo = (AssetInfo) baseInfo;
        if (assetInfo != null) {
            mEffectMode = assetInfo.getEffectMode();
            mFilterId = mEffectMode == BaseInfo.EFFECT_MODE_BUILTIN ? assetInfo.getEffectId() : assetInfo.getPackageId();
            if ((!assetInfo.isHadDownloaded() || assetInfo.needUpdate())) {
                downloadAssets(assetInfo, position);
            } else {
                setSelection(position);
                if (getResources().getString(R.string.top_menu_no).equals(baseInfo.getName())) {
                    hideSeekBar();
                } else {
                    showSeekBar();
                }
                mPresenter.onItemClicked(baseInfo);
                mPresenter.clickAssetItem(baseInfo);
            }
        }
    }

    private void downloadAssets(AssetInfo assetInfo, int position) {
        assetInfo.setDownloadProgress(0);
        mAdapter.notifyItemChanged(position);
        mDownloadTag = assetInfo.getPackageId();
        mPresenter.downloadAsset(assetInfo, position);
    }

    @Override
    public void setProgress(float progress) {
        mProgress = progress;
        if (mAdapter != null && mAdapter.getSelectPosition() > 0) {
            showSeekBar();
        }
    }

    @Override
    public void updateSelectPosition(int selection) {
        if (!isShown()) {
            return;
        }
        mBaseInfo = mAdapter.getItem(selection);
        mAdapter.setSelectPosition(selection);
        if (selection == 0) {
            //滤镜选无隐藏滑竿 Filter selection without hidden sliding rod.
            hideSeekBar();
            mRecyclerView.scrollToPosition(0);
        } else {
            mRecyclerView.scrollToPosition(selection);
        }
    }

    @Override
    public boolean needShowApplyAll() {
        return mNeedShowApply;
    }

    /**
     * 设置选中
     * Set selected
     *
     * @param packageId  package id
     * @param effectMode effect mode
     */
    public void selected(String packageId, int effectMode) {
        selected(packageId, effectMode, true);
    }

    /**
     * 设置选中
     * Set selected
     *
     * @param packageId  package id
     * @param effectMode effect mode
     * @param needUpdate Whether need update? true:yes;false:no
     */
    public void selected(String packageId, int effectMode, boolean needUpdate) {
        mFilterId = packageId;
        mEffectMode = effectMode;
        if (mAdapter != null) {
            if (mAdapter.getData().size() > 0) {
                mAdapter.setSelected(mFilterId, mEffectMode);
                if (needUpdate) {
                    updateSelectPosition(mAdapter.getSelectPosition());
                }
            }
        }
    }

    @Override
    public boolean isActive() {
        return isShown();
    }

    @Override
    public void onNewDataBack(List<AssetInfo> list, int subType, boolean needUpdate) {
        mAdapter.loadMoreComplete();
        mAdapter.setAssetSubType(subType);
        if (!CommonUtils.isEmpty(list)) {
            mAdapter.setNewData(new ArrayList<IBaseInfo>(list));
            selected(mFilterId, mEffectMode);
        }
        updateViewState(subType);
    }

    @Override
    public void onMoreDataBack(List<AssetInfo> list, final int subType, boolean needUpdate) {
        mAdapter.loadMoreComplete();
        if (!CommonUtils.isEmpty(list)) {
            mAdapter.addData(new ArrayList<IBaseInfo>(list));
            selected(mFilterId, mEffectMode, false);
            updateViewState(subType);
        }
    }

    @Override
    public void onDataError(int subType, boolean needUpdate) {
        mAdapter.setNewData(new ArrayList<IBaseInfo>());
        selected(mFilterId, mEffectMode);
        updateViewState(subType);
        mAdapter.loadMoreEnd(true);
    }

    @Override
    public int getItemCount() {
        return mAdapter == null ? 0 : mAdapter.getItemCount() - 2;
    }

    @Override
    public void onDownloadProgress(int position) {
        mAdapter.notifyItemChanged(position);
    }

    @Override
    public void onDownloadFinish(int position, AssetInfo assetInfo) {
        if (getResources().getString(R.string.top_menu_no).equals(assetInfo.getName())) {
            hideSeekBar();
        } else {
            showSeekBar();
        }
        if (TextUtils.equals(mDownloadTag, assetInfo.getPackageId())) {
            mAdapter.setSelectPosition(position);
            mPresenter.onItemClicked(assetInfo);
        } else {
            mAdapter.notifyItemChanged(position);
        }
    }

    @Override
    public void onDownloadError(int position) {
        mAdapter.notifyItemChanged(position);
    }


    /**
     * Sets need show seekbar.
     * <p>
     * 需要展示seekBar
     *
     * @param needShowSeekBar the need show seekbar
     */
    public void setNeedShowSeekBar(boolean needShowSeekBar) {
        this.needShowSeekBar = needShowSeekBar;
        if (!this.needShowSeekBar) {
            mTopView.setVisibility(INVISIBLE);
        }
    }

    public void setEventListener(BottomEventListener listener) {
        this.mEventListener = listener;
    }

    public void initListener() {
        mIvConfirm.setOnClickListener(this);
        if (mIvApplyToAllView != null) {
            mIvApplyToAllView.setOnClickListener(this);
        }
        mTvApplyToAllView.setOnClickListener(this);
        mAdapter.setOnLoadMoreListener(new BaseQuickAdapter.RequestLoadMoreListener() {
            @Override
            public void onLoadMoreRequested() {
                if (!mPresenter.loadMoreData(mCurrentSubType, false)) {
                    mAdapter.loadMoreEnd(true);
                }
            }
        }, mRecyclerView);
        mAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                mBaseInfo = mAdapter.getItem(position);
                if (mBaseInfo != null) {
                    mSeekBarView.setName(mBaseInfo.getName());
                    onItemClicked(mBaseInfo, position);
                }
            }
        });

        mSeekBarView.setOnSeekBarChangeListener(new MYSeekBarTextView.OnSeekBarListener() {
            @Override
            public void onStopTrackingTouch(int progress, String name) {
                if (mBaseInfo != null) {
                    mPresenter.onStopTrackingTouch();
                    mBaseInfo.setEffectStrength(progress);
                }
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {

            }

            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                if (mBaseInfo == null && mAdapter != null && mAdapter.getSelectPosition() != -1) {
                    mBaseInfo = mAdapter.getItem(mAdapter.getSelectPosition());
                }
                if (mBaseInfo != null) {
                    mPresenter.onProgressChanged(progress, mBaseInfo.getEffectId(), fromUser);
                }
            }
        });
        mAssetsTypeTab.setItemClickedListener(new AssetsTypeTabView.ItemClickedListener() {
            @Override
            public void onItemClicked(int position) {
                mCurrentSubType = position;
                mPresenter.loadData(position, false);
            }
        });
    }

    private void hide() {
        if (mEventListener != null) {
            mEventListener.onDismiss(true);
        }
    }

    protected void showSeekBar() {
        mTopView.setVisibility(VISIBLE);
        mSeekBarView.setProgress((int) (mProgress * (mSeekBarView.getMaxProgress() - mSeekBarView.getMinProgress())));
    }

    protected void hideSeekBar() {
        mTopView.setVisibility(INVISIBLE);
    }

    public void setSelection(int selection) {
        mAdapter.setSelectPosition(selection);
    }


    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.iv_confirm) {
            hide();
            mPresenter.confirm();
        } else if (id == R.id.iv_apply_all || id == R.id.tv_apply_all) {
            mPresenter.applyToAll();
        }
    }

    public void setNeedShowApply(boolean needShowApply) {
        this.mNeedShowApply = needShowApply;
        setApplyAble();
    }

    public BaseSelectAdapter<IBaseInfo> getAdapter() {
        return mAdapter;
    }
}
