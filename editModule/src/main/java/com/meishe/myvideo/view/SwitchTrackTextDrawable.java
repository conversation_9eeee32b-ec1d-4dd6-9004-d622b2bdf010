package com.meishe.myvideo.view;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2021/3/25 18:48
 * @Description :带左右文字的drawable, 用于switch开关的背景
 * <p></>The Drawable with left and right text used in switch background.
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.ColorFilter;
import android.graphics.Paint;
import android.graphics.PixelFormat;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.text.TextUtils;

import androidx.annotation.Nullable;

import com.meishe.myvideo.R;

/**
 * Drawable that generates the two pieces of text in the track of the switch, one of each
 * side of the positions of the thumb.
 */
public class SwitchTrackTextDrawable extends Drawable {

    private final Context mContext;
    private String mLeftText;

    private String mRightText;

    private Paint mRightPaint;
    private Paint mLeftPaint;

    public SwitchTrackTextDrawable(Context context,
                                   int leftTextId,
                                   int rightTextId) {
        this(context, context.getString(leftTextId, ""),
                context.getString(rightTextId, ""));
    }

    public SwitchTrackTextDrawable(Context context,
                                   String leftText,
                                   String rightText) {
        mContext = context;

        // Left text
        mLeftText = leftText;
        mLeftPaint = createTextPaint();

        // Right text
        mRightText = rightText;
        mRightPaint = createTextPaint();
    }

    private Paint createTextPaint() {
        Paint textPaint = new Paint();
        //noinspection deprecation
        textPaint.setColor(mContext.getResources().getColor(R.color.color_ffb1b1b1));
        textPaint.setTextSize(mContext.getResources().getDimensionPixelSize(R.dimen.sp_px_36));
        textPaint.setAntiAlias(true);
        textPaint.setStyle(Paint.Style.FILL);
        textPaint.setTextAlign(Paint.Align.CENTER);
        // Set textSize, typeface, etc, as you wish
        return textPaint;
    }

    @Override
    public void draw(Canvas canvas) {

        if (TextUtils.isEmpty(mLeftText)) {
            mLeftText = mRightText;
            mLeftPaint.setColor(Color.TRANSPARENT);
        }
        if (TextUtils.isEmpty(mRightText)) {
            mRightText = mLeftText;
            mRightPaint.setColor(Color.TRANSPARENT);
        }

        // The baseline for the text: centered, including the height of the text itself
        Rect rectF = canvas.getClipBounds();
        Paint.FontMetrics fontMetrics = mRightPaint.getFontMetrics();
        float distance = (fontMetrics.bottom - fontMetrics.top) / 2 - fontMetrics.bottom;
        float baseline = rectF.centerY() + distance;

        // This is one quarter of the full width, to measure the centers of the texts
        final int widthQuarter = canvas.getClipBounds().width() / 4;

        canvas.drawText(mLeftText, 0, mLeftText.length(),
                widthQuarter, baseline,
                mLeftPaint);

        canvas.drawText(mRightText, 0, mRightText.length(),
                widthQuarter * 3, baseline,
                mRightPaint);

    }


    @Override
    public void setAlpha(int alpha) {
    }

    @Override
    public void setColorFilter(@Nullable ColorFilter colorFilter) {

    }


    @Override
    public int getOpacity() {
        return PixelFormat.TRANSLUCENT;
    }
}