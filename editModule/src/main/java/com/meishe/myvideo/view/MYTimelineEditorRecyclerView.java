package com.meishe.myvideo.view;

import android.content.Context;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import android.util.AttributeSet;
import android.view.MotionEvent;

/**
 * The type My timeline editor recycler view.
 * 时间线编辑器类
 */
public class MYTimelineEditorRecyclerView extends RecyclerView {

    public MYTimelineEditorRecyclerView(@NonNull Context context) {
        super(context);
    }

    public MYTimelineEditorRecyclerView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent e) {
        return false;
    }

    @Override
    public boolean onTouchEvent(MotionEvent e) {
        return false;
    }
}
