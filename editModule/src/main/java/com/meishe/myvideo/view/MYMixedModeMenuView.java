package com.meishe.myvideo.view;

import android.content.Context;
import android.widget.TextView;

import com.meishe.engine.interf.IBaseInfo;
import com.meishe.myvideo.R;
import com.meishe.myvideo.adapter.BaseSelectAdapter;
import com.meishe.myvideo.adapter.MixedModeAdapter;
import com.meishe.myvideo.bean.EditMixedModeInfo;
import com.meishe.myvideo.view.base.BaseConfirmMenuView;
import com.meishe.myvideo.view.presenter.BaseConfirmPresenter;
import com.meishe.myvideo.view.presenter.MixedModePresenter;

import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> ChuChenGuang
 * @CreateDate :2021/06/29 14:04
 * @Description :混合模式 The mixed mode menu view
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class MYMixedModeMenuView extends BaseConfirmMenuView {

    @Override
    public void initView() {
        super.initView();
        setResetAble(false);
        showSeekBar();
        setSeekBarDesc(R.string.title_edit_not_opacity);
    }

    public MYMixedModeMenuView(Context context) {
        super(context);
        mAssetsTypeTab.setVisibility(GONE);
    }

    @Override
    public BaseSelectAdapter<IBaseInfo> getAdapter() {
        if (mAdapter == null) {
            mAdapter = new MixedModeAdapter();
        }
        return mAdapter;
    }

    @Override
    protected void onItemClicked(IBaseInfo baseInfo, boolean isSelected) {
        if (baseInfo != null) {
            mPresenter.onItemClicked(baseInfo, isSelected);
        }
    }

    /**
     * 设置选中状态和不透明度
     * Sets selected and seek bar progress.
     *
     * @param mixedModeDesc the mixed mode desc
     * @param opacity       the opacity
     */
    public void setSelectedAndSeekBarProgress(int mixedModeDesc, float opacity) {
        if (mAdapter == null) {
            return;
        }
        List<IBaseInfo> list = mAdapter.getData();
        for (int i = 0; i < list.size(); i++) {
            EditMixedModeInfo info = (EditMixedModeInfo) list.get(i);
            if (mixedModeDesc == info.getMixedMode()) {
                mAdapter.setSelectPosition(i);
                mRecyclerView.scrollToPosition(i);
                break;
            }
        }
        setProgress(opacity);
    }

    @Override
    protected void setContentText(TextView textView) {
        textView.setText(R.string.sub_menu_name_edit_mixed_mode);
    }

    @Override
    protected BaseConfirmPresenter<? extends BaseConfirmMenuView> getPresenter() {
        MixedModePresenter presenter = new MixedModePresenter();
        presenter.attachView(this);
        return presenter;
    }

    /**
     * Update view.
     * 更新视图
     *
     * @param data the data
     */
    public void updateView(List<IBaseInfo> data) {
        mAdapter.setNewData(data);
    }

}
