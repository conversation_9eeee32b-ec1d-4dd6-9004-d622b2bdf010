package com.meishe.myvideo.view.editview;

import android.content.Context;
import android.os.Build;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.SeekBar;
import android.widget.TextView;

import androidx.annotation.RequiresApi;

import com.meishe.myvideo.R;
import com.meishe.myvideo.interfaces.BottomEventListener;
import com.meishe.myvideo.view.MYSeekBarTextView;

/**
 * 进度调节视图
 * Progress adjustment view
 */
public class AdjustSeekBarView extends RelativeLayout {
    public MYSeekBarTextView mSeekBar;
    public TextView mTvEnd;
    private ImageView mIvConfirm;
    public TextView mTvContent;
    private TextView mTvStart;
    private String mType;
    private int mResourceId;
    private BottomEventListener mEventListener;

    public AdjustSeekBarView(Context context) {
        this(context, null);
    }

    public AdjustSeekBarView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public AdjustSeekBarView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView();
        initListener();
    }

    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    public AdjustSeekBarView(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        initView();
        initListener();
    }

    protected void initView() {
        View rootView = LayoutInflater.from(getContext()).inflate(R.layout.view_adjust_seek_bar, this);
        mSeekBar = rootView.findViewById(R.id.seek_bar);
        mTvStart = rootView.findViewById(R.id.tv_start);
        mTvEnd = rootView.findViewById(R.id.tv_end);
        mIvConfirm = rootView.findViewById(R.id.iv_confirm);
        mTvContent = rootView.findViewById(R.id.tv_content);
        mTvStart.setVisibility(GONE);

        initListener();
    }

    /**
     * 设置类型
     * Set the type
     *
     * @param type the type
     */
    public void setType(String type) {
        mType = type;
    }

    /**
     * 获取类型
     * Procurement type
     *
     * @return the type
     */
    public String getType() {
        return mType;
    }

    /**
     * 设置最大进度值
     * Set the maximum progress value
     *
     * @param max the max 最大值
     */
    public void setSeekBarMax(int max) {
        mSeekBar.setMax(max);
        String maxText = max + "";
        mTvEnd.setText(maxText);
    }

    /**
     * 进度调节标题
     * Progress adjustment heading
     *
     * @param resourceId the resource id 资源识别
     */

    public void setContentText(int resourceId) {
        mResourceId = resourceId;
        mTvContent.setText(resourceId);
    }

    public int getResourceId() {
        return mResourceId;
    }

    /**
     * 设置调节进度
     * Set Adjustment Schedule
     *
     * @param progress the progress 进度
     */
    public void setProgress(int progress) {
        mSeekBar.setProgress(progress);
    }

    private void changeProgressText(String progress) {
        mTvEnd.setText(progress);
    }

    /**
     * Init listener.
     * 初始化监听
     */
    protected void initListener() {
        mSeekBar.setOnSeekBarChangeListener(new MYSeekBarTextView.OnSeekBarListener() {
            @Override
            public void onStopTrackingTouch(int progress, String name) {
                if (mEventListener != null) {
                    mEventListener.onStopTrackingTouch(0);
                }
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                if (mEventListener != null) {
                    mEventListener.onStartTrackingTouch(0);
                }
            }

            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                changeProgressText(progress + "");
                if (mEventListener != null) {
                    mEventListener.onProgressChanged(progress, fromUser, 0);
                }
            }
        });

        mIvConfirm.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mEventListener != null) {
                    mEventListener.onDismiss(true);
                }
            }
        });
    }

    /**
     * 设置事件监听
     * Sets the listener
     *
     * @param listener the listener
     */
    public void setListener(BottomEventListener listener) {
        mEventListener = listener;
    }
}
