package com.meishe.myvideo.view;

import android.content.Context;
import android.os.Handler;
import android.os.Message;
import android.os.Vibrator;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewParent;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.meicam.sdk.NvsVideoClip;
import com.meishe.base.constants.Constants;
import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.FormatUtils;
import com.meishe.base.utils.ScreenUtils;
import com.meishe.engine.EditorEngine;
import com.meishe.engine.bean.CommonData;
import com.meishe.myvideo.R;
import com.meishe.myvideo.ui.bean.BaseUIClip;
import com.meishe.myvideo.ui.bean.BaseUIVideoClip;
import com.meishe.myvideo.util.PixelPerMicrosecondUtil;

import java.text.DecimalFormat;

/**
 * The type My editor timeline span view.
 * 编辑器时间线跨度视图类
 */
public class MYEditorTimelineSpanView extends RelativeLayout {

    private static final String TAG = MYEditorTimelineSpanView.class.getSimpleName();
    private static final int HAND_LEFT = 0x220;
    private static final int HAND_RIGHT = 0x221;
    private static final int HAND_SCREEN_BORDER = 10;
    private static final int TIME_INTERVAL = 50;
    public static final int LEFT = 0x16;
    public static final int CENTER = 0x17;
    public static final int RIGHT = 0x18;
    private static final int HAND_LIMIT_BORDER = 10;
    private Context mContexgt;
    private int mLimitLengthOfHand;
    private ImageView mLeftHandView;
    private ImageView mRightHandView;
    private int mHandWidth;
    private int mMaxLeftToRight = 0;
    private int mMaxLeftToLeft = 0;
    private int mMaxRightToLeft = 0;
    private int mMaxRightToRight = 0;
    private OnHandChangeListener mOnHandChangeListener;
    private boolean mOnMiddleState;
    private boolean mCanMoveHandle;
    private float mPreviewRawX;
    private int mDragDirection;
    private TextView mDurationText;
    private LinearLayout mLayoutEditorTimelineSpeed;
    private TextView mTvEditorTimelineSpeedText;
    private BaseUIClip mBaseUIClip;
    private long mCurrTimelinePosition = -1;
    private int totalMoveX = -1;
    /**
     * 入点和基准线的差的距离
     * The difference between the entry point and the datum
     */
    private int lengthLeft = -1;
    private int lengthRight = -1;
    private static final int MIN_MARGIN_BASE_LINR = 50;
    /**
     * 震动器
     * vibrator
     */
    private Vibrator mVibrator;
    private float mStartMarginLeft;
    private boolean hasNearByLeftBaseLine = false;
    private boolean hasNearByRightBaseLine = false;
    Handler mHandler = new Handler() {
        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            if (msg.what == HAND_LEFT) {
                onLeftHandToScreenBorder(-HAND_SCREEN_BORDER, true);
                sendEmptyMessageDelayed(HAND_LEFT, TIME_INTERVAL);
            } else if (msg.what == HAND_RIGHT) {
                onRightHandToScreenBorder(HAND_SCREEN_BORDER, true);
                sendEmptyMessageDelayed(HAND_RIGHT, TIME_INTERVAL);
            }
        }
    };


    public MYEditorTimelineSpanView(Context context, BaseUIClip uiClip) {
        super(context);
        this.mBaseUIClip = uiClip;
        init(context);
    }


    public MYEditorTimelineSpanView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }


    private void init(Context context) {
        this.mContexgt = context;
        LayoutInflater layoutInflater = LayoutInflater.from(context);
        View view = layoutInflater.inflate(R.layout.editor_timeline_span_view_layout, this);
        mVibrator = (Vibrator) context.getSystemService(Context.VIBRATOR_SERVICE);
        mLimitLengthOfHand = ScreenUtils.getScreenWidth() / 2;
        mLeftHandView = view.findViewById(R.id.editor_timeline_view_left_handle);
        mRightHandView = view.findViewById(R.id.editor_timeline_view_right_handle);
        mDurationText = view.findViewById(R.id.tv_editor_timeline_time_text);
        mTvEditorTimelineSpeedText = view.findViewById(R.id.tv_editor_timeline_speed_text);
        mDurationText.setBackground(CommonUtils.getRadiusDrawable(-1, -1,
                getResources().getDimensionPixelOffset(R.dimen.dp2), getResources().getColor(R.color.black_half)));
        mLayoutEditorTimelineSpeed = view.findViewById(R.id.layout_editor_timeline_speed);
        mLayoutEditorTimelineSpeed.setBackground(CommonUtils.getRadiusDrawable(-1, -1,
                getResources().getDimensionPixelOffset(R.dimen.dp2), getResources().getColor(R.color.black_half)));
        changeDurationText(getClipDurationText());
        initSpeedText();
        if (mBaseUIClip != null && CommonData.CLIP_IMAGE.equals(mBaseUIClip.getType())) {
            mLayoutEditorTimelineSpeed.setVisibility(GONE);
        }
    }


    @Override
    public boolean onTouchEvent(MotionEvent event) {
        int action = event.getAction();
        if (action == MotionEvent.ACTION_DOWN) {
            mCurrTimelinePosition = EditorEngine.getInstance().getCurrentTimelinePosition();
            lengthLeft = PixelPerMicrosecondUtil.durationToLength(mCurrTimelinePosition - mBaseUIClip.getInPoint());
            lengthRight = PixelPerMicrosecondUtil.durationToLength(mCurrTimelinePosition - (mBaseUIClip.getInPoint() + mBaseUIClip.getTrimOut() - mBaseUIClip.getTrimIn()));
            mCanMoveHandle = false;
            mPreviewRawX = (int) event.getRawX();
            mStartMarginLeft = getWidth();
            mDragDirection = getDirection((int) event.getRawX(), (int) event.getRawY());
            if (mDragDirection != CENTER) {
                mCanMoveHandle = true;
                requestDisallowIntecept(true);
            }
            if (mOnHandChangeListener != null && mDragDirection == LEFT) {
                int[] local = new int[2];
                mLeftHandView.getLocationOnScreen(local);
                if (local[0] < HAND_LIMIT_BORDER) {
                    mOnHandChangeListener.onLeftHandDown(HAND_LIMIT_BORDER - local[0]);
                } else {
                    mOnHandChangeListener.onLeftHandDown(0);
                }
            }

            if (mOnHandChangeListener != null && mDragDirection == RIGHT) {
                mOnHandChangeListener.onRightHandDown();
            }
        } else if (action == MotionEvent.ACTION_MOVE) {
            float tempRawX = event.getRawX();

            int dx = (int) (tempRawX - mPreviewRawX);
            if ((hasNearByLeftBaseLine || hasNearByRightBaseLine) && Math.abs(dx) < Constants.SLOPX) {
                return false;
            }
            if (mDragDirection == LEFT) {
                if (checkLeftHandCanMove(dx)) {
                    totalMoveX = (int) (mStartMarginLeft - getWidth());
                    dx = leftViewAdsorb(dx);
                    onLeftHandMove(dx);
                }
            } else if (mDragDirection == RIGHT) {
                if (checkRightHandCanMove(dx)) {
                    totalMoveX = (int) (getWidth() - mStartMarginLeft);
                    dx = rightViewAdsorb(dx);
                    onRightHandMove(dx);
                }
            }
            //屏幕左角为原点 The left corner of the screen is the origin
            mPreviewRawX = event.getRawX();
        } else if (action == MotionEvent.ACTION_UP) {
            onTouchUp();
        }
        return mCanMoveHandle;
    }

    /**
     * 右边的view吸附
     * right View Adsorb
     *
     * @param dx 移动距离
     * @return
     */
    private int rightViewAdsorb(int dx) {
        if (dx == 0) {
            return dx;
        }
        if (totalMoveX == 0) {
            return dx;
        }
        int rightMoveLeftLength = lengthRight - totalMoveX;
        if (Constants.END_ADSORB < rightMoveLeftLength && rightMoveLeftLength < Constants.START_ADSORB) {
            if (!hasNearByRightBaseLine) {
                mVibrator.vibrate(30);
                hasNearByRightBaseLine = true;
                if (dx < 0) {
                    return -Math.abs(rightMoveLeftLength);
                } else {
                    return Math.abs(rightMoveLeftLength);
                }
            }
        } else {
            hasNearByRightBaseLine = false;
        }
        return dx;
    }

    /**
     * 左边边的view吸附
     * left View Adsorb
     *
     * @param dx 移动距离
     * @return
     */
    private int leftViewAdsorb(int dx) {
        if (dx == 0) {
            return dx;
        }
        if (totalMoveX == 0) {
            return dx;
        }
        int leftMoveLeftLength = lengthLeft - totalMoveX;
        //判断吸附基准线 Determine the adsorption baseline
        if (Constants.END_ADSORB < leftMoveLeftLength && leftMoveLeftLength < Constants.START_ADSORB) {
            if (!hasNearByLeftBaseLine) {
                mVibrator.vibrate(30);
                hasNearByLeftBaseLine = true;
                if (dx < 0) {
                    return -Math.abs(leftMoveLeftLength);
                } else {
                    return Math.abs(leftMoveLeftLength);
                }
            }
        } else {
            hasNearByLeftBaseLine = false;
        }
        return dx;
    }

    private void onTouchUp() {
        mHandler.removeCallbacksAndMessages(null);
        requestDisallowIntecept(false);
        if (mOnHandChangeListener != null) {
            if (mDragDirection == LEFT) {
                mOnHandChangeListener.onLeftHandUp();
            } else if (mDragDirection == RIGHT) {
                mOnHandChangeListener.onRightHandUp();
            }
        }
        mDragDirection = CENTER;
    }

    private void onRightHandMove(int dx) {
        dx = getRealDxForRight(dx);
        int[] local = new int[2];
        mOnMiddleState = false;
        mRightHandView.getLocationOnScreen(local);
        LayoutParams layoutParams = (LayoutParams) getLayoutParams();
        if (dx < 0) {
            mHandler.removeCallbacksAndMessages(null);
        } else if (dx > 0 && mHandler.hasMessages(HAND_RIGHT)) {
            return;
        }
        if (mCurrTimelinePosition != -1 && local[0] + dx <= mLimitLengthOfHand) {
            mOnMiddleState = true;
        }
        if (local[0] + HAND_LIMIT_BORDER + mHandWidth + dx >= ScreenUtils.getScreenWidth()) {
            if (dx < 0) {
                layoutParams.width = layoutParams.width + dx;
                setLayoutParams(layoutParams);
                if (mOnHandChangeListener != null) {
                    mOnHandChangeListener.onRightHandChange(false, false, dx,
                            mOnMiddleState, false, false);
                }
            } else {
                int newDx = ScreenUtils.getScreenWidth() - local[0] - HAND_LIMIT_BORDER - mHandWidth;
                onRightHandToScreenBorder(dx, false);
                if (!mHandler.hasMessages(HAND_RIGHT)) {
                    mHandler.sendEmptyMessage(HAND_RIGHT);
                }
            }
        } else {
            layoutParams.width = layoutParams.width + dx;
            setLayoutParams(layoutParams);
            if (mOnHandChangeListener != null) {
                mOnHandChangeListener.onRightHandChange(false, false, dx, mOnMiddleState, false, false);
            }
        }
    }

    private void onLeftHandMove(int dx) {
        dx = getRealDxForLeft(dx);
        mOnMiddleState = false;
        if (dx > 0) {
            mHandler.removeCallbacksAndMessages(null);
        } else if (dx < 0 && mHandler.hasMessages(HAND_LEFT)) {
            return;
        }

        LayoutParams layoutParams = (LayoutParams) getLayoutParams();
        int[] local = new int[2];
        mLeftHandView.getLocationOnScreen(local);
        if (mCurrTimelinePosition != -1 && local[0] + dx + mHandWidth >= mLimitLengthOfHand) {
            mOnMiddleState = true;
        }
        if (dx > 0) {
            layoutParams.width = layoutParams.width - dx;
            setLayoutParams(layoutParams);
            if (mOnHandChangeListener != null) {
                mOnHandChangeListener.onLeftHandChange(false, false, dx, false, mOnMiddleState,
                        0, false);
            }
        } else if (dx < 0) {
            if (local[0] - HAND_LIMIT_BORDER + dx < 0) {
                if (local[0] < HAND_LIMIT_BORDER) {
                    // 已经在边缘以外了 It's already over the edge
                    if (!mHandler.hasMessages(HAND_LEFT)) {
                        mHandler.sendEmptyMessage(HAND_LEFT);
                    }
                } else {
                    // 校准一次 然后开始自动平移 Calibrate once and then start automatic translation
                    int newDx = -local[0] + HAND_LIMIT_BORDER;
                    if (mOnHandChangeListener != null) {
                        mOnHandChangeListener.onLeftHandChange(false, false, newDx, false,
                                mOnMiddleState, 0, false);
                    }
                    if (!mHandler.hasMessages(HAND_LEFT)) {
                        mHandler.sendEmptyMessage(HAND_LEFT);
                    }
                }
            } else {
                if (mOnMiddleState) {
                    layoutParams.width = layoutParams.width - dx;
                    setLayoutParams(layoutParams);
                }
                if (mOnHandChangeListener != null) {
                    mOnHandChangeListener.onLeftHandChange(false, false, dx, false, mOnMiddleState, 0, false);
                }
            }
        }

    }


    /**
     * Sets clip position.
     * 设置裁剪位置
     *
     * @param start the start  开始
     * @param end   the end 结束
     */
    public void setClipPosition(int start, int end) {
        int spanWidth = end - start + mHandWidth * 2;
        LayoutParams layoutParams = new LayoutParams(spanWidth, LayoutParams.MATCH_PARENT);
        layoutParams.setMargins(start, 0, 0, 0);
        setLayoutParams(layoutParams);
        // set duration text
        long during = -1L;
        if (!TextUtils.isEmpty(mBaseUIClip.getCurveSpeedName())) {
            //NvsVideoClip nvsVideoClip = (NvsVideoClip) mBaseUIClip.getNvsObject();
            NvsVideoClip nvsVideoClip = new NvsVideoClip();
            during = nvsVideoClip.getOutPoint() - nvsVideoClip.getInPoint();
        } else {
            during = (long) ((mBaseUIClip.getTrimOut() - mBaseUIClip.getTrimIn()) / mBaseUIClip.getSpeed());
        }
        changeDurationText(during);
    }

    /**
     * Change duration text state.
     * 更改时长文本状态
     *
     * @param state the state
     */
    public void changeDurationTextState(boolean state) {
        mDurationText.setVisibility(state ? VISIBLE : GONE);
    }

    public void changeDurationText(String text) {
        mDurationText.setText(text);
    }

    public void changeDurationText(long duration) {
        mDurationText.setText(FormatUtils.duration2Text(duration));
    }

    /**
     * Init speed text.
     * 初始化文本速度
     */
    public void initSpeedText() {

        if (TextUtils.isEmpty(mBaseUIClip.getCurveSpeedName()) || mBaseUIClip.getCurveSpeedName().equals(mContexgt.getString(R.string.original))) {
            if (mBaseUIClip != null && (mBaseUIClip.getSpeed() != 1)) {
                mLayoutEditorTimelineSpeed.setVisibility(VISIBLE);
                mTvEditorTimelineSpeedText.setText(getClipSpeedText());
            } else {
                mLayoutEditorTimelineSpeed.setVisibility(GONE);
            }
        } else {
            mLayoutEditorTimelineSpeed.setVisibility(VISIBLE);
            mTvEditorTimelineSpeedText.setText(mBaseUIClip.getCurveSpeedName());
        }
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
    }

    private void requestDisallowIntecept(boolean intercepter) {
        ViewParent viewParent = getParent();
        if (viewParent == null) {
            return;
        }
        /*
         * 是否让父布局禁用事件拦截功能
         * Whether to have the parent layout disable event interception
         */
        viewParent.requestDisallowInterceptTouchEvent(intercepter);
    }

    public void setHandWidth(int handWidth) {
        this.mHandWidth = handWidth;
    }

    private void onLeftHandToScreenBorder(int dx, boolean fromHandler) {
        if (fromHandler) {
            if (!checkLeftHandCanMove(dx)) {
                mHandler.removeCallbacksAndMessages(null);
                return;
            }
        }

        dx = getRealDxForLeft(dx);
        LayoutParams layoutParams = (LayoutParams) getLayoutParams();
        layoutParams.width = layoutParams.width - dx;
        setLayoutParams(layoutParams);
        if (mOnHandChangeListener != null) {
            mOnHandChangeListener.onLeftHandChange(false, false, dx, true,
                    false, 0, false);
        }
    }

    private int getDirection(int x, int y) {
        int[] leftLocal = new int[2];
        mLeftHandView.getLocationOnScreen(leftLocal);
        int[] rightLocal = new int[2];
        mRightHandView.getLocationOnScreen(rightLocal);
        if ((x - leftLocal[0]) < mHandWidth) {
            return LEFT;
        }
        if ((rightLocal[0] - x) < mHandWidth) {
            return RIGHT;
        }
        return CENTER;
    }

    private int getRealDxForLeft(int dx) {
        if (dx > 0 && dx > mMaxLeftToRight) {
            dx = mMaxLeftToRight;
        } else if (dx < 0 && -dx > mMaxLeftToLeft) {
            dx = -mMaxLeftToLeft;
        }
        return dx;
    }

    private boolean checkLeftHandCanMove(int dx) {
        if (dx > 0) {
            return mMaxLeftToRight > 0;
        } else if (dx < 0) {
            return mMaxLeftToLeft > 0;
        }
        return false;
    }

    private void onRightHandToScreenBorder(int dx, boolean fromHandler) {
        if (fromHandler) {
            if (!checkRightHandCanMove(dx)) {
                mHandler.removeCallbacksAndMessages(null);
                return;
            }
        }
        dx = getRealDxForRight(dx);
        LayoutParams layoutParams = (LayoutParams) getLayoutParams();
        layoutParams.width = layoutParams.width + dx;
        if (mOnHandChangeListener != null) {
            mOnHandChangeListener.onNeedScrollParentLinearLayout(dx);
        }
        setLayoutParams(layoutParams);
        if (mOnHandChangeListener != null) {
            mOnHandChangeListener.onRightHandChange(false, false, dx, mOnMiddleState,
                    true, false);
        }
    }

    private int getRealDxForRight(int dx) {
        if (dx > 0 && dx > mMaxRightToRight) {
            dx = mMaxRightToRight;
        } else if (dx < 0 && -dx > mMaxRightToLeft) {
            dx = -mMaxRightToLeft;
        }
        return dx;
    }

    private boolean checkRightHandCanMove(int dx) {
        if (dx > 0) {
            return mMaxRightToRight > 0;
        } else if (dx < 0) {
            return mMaxRightToLeft > 0;
        }
        return false;
    }

    /**
     * Sets on hand change listener.
     * 设置手部改变的监听
     *
     * @param onHandChangeListener the on hand change listener
     */
    public void setOnHandChangeListener(OnHandChangeListener onHandChangeListener) {
        this.mOnHandChangeListener = onHandChangeListener;
    }

    /**
     * Sets max left to left.
     * 设置最大值从左到左
     *
     * @param maxLeftToLeft the max left to left
     */
    public void setMaxLeftToLeft(int maxLeftToLeft) {
        this.mMaxLeftToLeft = maxLeftToLeft;
    }

    public void setMaxLeftToRight(int maxLeftToRight) {
        this.mMaxLeftToRight = maxLeftToRight;
    }

    /**
     * Sets max right to left.
     * 设置最大值从右到左
     *
     * @param maxRightToLeft the max right to left
     */
    public void setMaxRightToLeft(int maxRightToLeft) {
        this.mMaxRightToLeft = maxRightToLeft;
    }

    /**
     * Sets max right to right.
     * 设置最大值从右到右
     *
     * @param maxRightToRight the max right to right
     */
    public void setMaxRightToRight(int maxRightToRight) {
        this.mMaxRightToRight = maxRightToRight;
    }

    /**
     * Left on dx change.
     * 左边是dx的变化
     *
     * @param dx           the dx
     * @param viewLeftEdge the view left edge
     */
    public void leftOnDxChange(int dx, boolean viewLeftEdge) {
        LayoutParams layoutParams = (LayoutParams) getLayoutParams();
        if (viewLeftEdge) {
            if (mOnHandChangeListener != null) {
                mOnHandChangeListener.onNeedScrollParentLinearLayout(-dx);
            }
        }

        layoutParams.width = layoutParams.width - dx;
        setLayoutParams(layoutParams);
    }

    /**
     * Gets right hand x.
     * 得到右手
     *
     * @return the right hand x
     */
    public int getRightHandX() {
        int[] local = new int[2];
        mRightHandView.getLocationOnScreen(local);
        return local[0];
    }

    /**
     * Gets drag direction.
     * 获得拖动的方向
     *
     * @return the drag direction
     */
    public int getDragDirection() {
        return mDragDirection;
    }

    /**
     * Is on middle state boolean.
     * 是否是中间状态
     *
     * @return the boolean
     */
    public boolean isOnMiddleState() {
        return mOnMiddleState;
    }

    /**
     * The interface On hand change listener.
     * 手部改变的监听的接口
     */
    public interface OnHandChangeListener {

        /**
         * On left hand down.
         * 左手按下
         *
         * @param addParentLength the add parent length 增加父的长度
         */
        void onLeftHandDown(int addParentLength);

        /**
         * On left hand change.
         * 左手改变
         *
         * @param isDown          the is down 下
         * @param isDragEnd       the is drag end 结束
         * @param dx              the dx dx
         * @param isLeftBorder    the is left border  左侧边框
         * @param center          the center 中心
         * @param addParentLength the add parent length 增加父的长度
         * @param inMinDuration   the in min duration 最小时长
         */
        void onLeftHandChange(boolean isDown, boolean isDragEnd, int dx, boolean isLeftBorder, boolean center, int addParentLength,
                              boolean inMinDuration);

        /**
         * On left hand up.
         * 左手抬起
         */
        void onLeftHandUp();


        /**
         * On right hand down.
         * 右手按下
         */
        void onRightHandDown();

        /**
         * On right hand change.
         * 右手的变化
         *
         * @param isDown        the is down 按下
         * @param isDragEnd     the is drag end 拖拽结束
         * @param dx            the dx dx
         * @param inMiddle      the in middle 中间
         * @param isRightBorder the is right border 右边界
         * @param inMinDuration the in min duration 时长最小值
         */
        void onRightHandChange(boolean isDown, boolean isDragEnd, int dx, boolean inMiddle, boolean isRightBorder, boolean inMinDuration);

        /**
         * On right hand up.
         * 右手抬起
         */
        void onRightHandUp();

        /**
         * On need scroll parent linear layout.
         * 需要滚动父线性布局
         *
         * @param dx the dx
         */
        void onNeedScrollParentLinearLayout(int dx);
    }

    private String getClipDurationText() {
        if (mBaseUIClip == null) {
            Log.e(TAG, "getClipDurationText: mBaseUIClip is null!");
            return "";
        }
        if (!TextUtils.isEmpty(mBaseUIClip.getCurveSpeedName())) {
           // NvsVideoClip nvsVideoClip = (NvsVideoClip) mBaseUIClip.getNvsObject();
            NvsVideoClip nvsVideoClip = new NvsVideoClip();
            long duration = nvsVideoClip.getOutPoint() - nvsVideoClip.getInPoint();
            return FormatUtils.duration2Text(duration);
        }

        long duration = mBaseUIClip.getTrimOut() - mBaseUIClip.getTrimIn();
        if (mBaseUIClip instanceof BaseUIVideoClip) {
            duration = (long) (duration / ((BaseUIVideoClip) mBaseUIClip).getSpeed());
        }
        return FormatUtils.duration2Text(duration);
    }

    private String getClipSpeedText() {
        if (mBaseUIClip == null) {
            Log.e(TAG, "getClipDurationText: mBaseUIClip is null!");
            return "";
        }
        DecimalFormat decimalFormat = new DecimalFormat("#0.0");
        return decimalFormat.format(mBaseUIClip.getSpeed()) + "x";
    }

    /**
     * Sets base ui clip.
     * 设置基础ui裁剪
     *
     * @param baseUIClip the base ui clip
     */
    public void setBaseUIClip(BaseUIClip baseUIClip) {
        mBaseUIClip = baseUIClip;
    }

    /**
     * Gets base ui clip.
     * 获取基础ui裁剪
     *
     * @return the base ui clip
     */
    public BaseUIClip getBaseUIClip() {
        return mBaseUIClip;
    }
}
