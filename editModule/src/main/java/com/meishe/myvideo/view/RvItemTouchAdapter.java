package com.meishe.myvideo.view;

import android.content.Context;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import com.meishe.base.view.dragview.CustomItemTouchHandler;
import com.meishe.myvideo.R;
import com.meishe.base.utils.ImageLoader;
import com.meishe.engine.view.MultiThumbnailSequenceView;

import java.util.ArrayList;
import java.util.Collections;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

/**
 * The type Rv item touch adapter.
 * 触摸条目的适配器
 */
public class RvItemTouchAdapter extends CustomItemTouchHandler.ItemTouchAdapterImpl {
    private final LayoutInflater mLayoutInflater;
    private Context mContext;
    private int mLeftPadding = 0;
    private static final int TYPE_HEAD = 0;
    private static final int TYPE_MID = 1;
    private ArrayList<MultiThumbnailSequenceView.ThumbnailSequenceDesc> mData = null;
    private ImageLoader.Options mOptions;

    public RvItemTouchAdapter(Context context, ArrayList<MultiThumbnailSequenceView.ThumbnailSequenceDesc> list) {
        this.mContext = context;
        this.mData = list;
        mLayoutInflater = LayoutInflater.from(context);
        mOptions = new ImageLoader.Options()
                .centerCrop()
                .placeholder(R.mipmap.icon_feed_back_pic)
                .dontAnimate();
    }


    @Override
    public void onItemMove(int fromPosition, int toPosition) {
        if (onItemDragListener != null) {
            onItemDragListener.onItemDragMoving(fromPosition - 1, toPosition - 1);
        }
        Log.e("DRAG", "onItemMove 1111111111111 "+ (fromPosition-1) + "---- " + (toPosition-1));
        Collections.swap(mData, fromPosition, toPosition);
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        ViewGroup.LayoutParams layoutParams;
        View view = new View(mContext);
        if (viewType == TYPE_HEAD) {
            layoutParams = new ViewGroup.LayoutParams(mLeftPadding, ViewGroup.LayoutParams.MATCH_PARENT);
            view.setLayoutParams(layoutParams);
            return new HeadHolder(view);
        } else if (viewType == TYPE_MID) {
            return new MidHolder(mLayoutInflater.inflate(R.layout.item_rv_linear2, parent, false));
        }
        return null;
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder viewHolder, int position) {
        if (viewHolder instanceof HeadHolder) {
            ViewGroup.LayoutParams layoutParams = new ViewGroup.LayoutParams(mLeftPadding, ViewGroup.LayoutParams.MATCH_PARENT);
            viewHolder.itemView.setLayoutParams(layoutParams);
        } else if (viewHolder instanceof MidHolder) {
            String path = mData.get(position).mediaFilePath;
            ImageLoader.loadUrl(mContext,path,((MidHolder) viewHolder).imageView,mOptions);
        }

    }

    @Override
    public int getItemViewType(int position) {
        if (position == 0) {
            return TYPE_HEAD;
        } else {
            return TYPE_MID;
        }
    }

    @Override
    public int getItemCount() {
        return mData.size();
    }

    class HeadHolder extends RecyclerView.ViewHolder {

        public HeadHolder(@NonNull View itemView) {
            super(itemView);
        }
    }

    class MidHolder extends RecyclerView.ViewHolder {
        ImageView imageView;

        public MidHolder(View itemView) {
            super(itemView);
            imageView = itemView.findViewById(R.id.image_item_rv_linear);
        }
    }

    private OnItemDragListener onItemDragListener;

    /**
     * Sets on item drag listener.
     * 设置拖动条目的监听
     * @param listener the listener
     */
    public void setOnItemDragListener(OnItemDragListener listener) {
        this.onItemDragListener = listener;
    }

    /**
     * The interface On item drag listener.
     * 拖动条目监听的接口
     */
    public interface OnItemDragListener {
        /**
         * On item drag moving.
         * 离开拖动条目
         * @param from the from
         * @param to   the to
         */
        void onItemDragMoving(int from, int to);
    }

    /**
     * Refresh data.
     * 刷新数据
     * @param list the list
     */
    public void refreshData(ArrayList<MultiThumbnailSequenceView.ThumbnailSequenceDesc> list) {
        this.mData = list;
        notifyDataSetChanged();
    }


    /**
     * Sets left padding.
     * 设置左边距
     * @param mLeftPadding the m left padding
     */
    public void setLeftPadding(int mLeftPadding) {
        this.mLeftPadding = mLeftPadding;
        notifyItemChanged(0);
    }
}
