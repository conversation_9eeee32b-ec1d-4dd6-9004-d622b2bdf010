package com.meishe.myvideo.view;

import android.content.Context;
import android.graphics.Canvas;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.meishe.base.utils.FormatUtils;
import com.meishe.myvideo.R;

/**
 * 此控件中的所有时间，均是微妙
 * All times in this control are subtle
 */
public class CutMusicView extends RelativeLayout {
    private final String TAG = "CutMusicView";
    private Context mContext;
    private RelativeLayout mMainLayout, mHandleLayout, realFollowTime;
    private ImageView mLeftHandle, mRightHandle;
    private View mIndicatorView;
    private OnSeekBarChanged mListener;
    private boolean mCanTouchCenter = true;

    private int mCurrentTouch = -1;
    private final int TOUCH_LEFT = 1, TOUCH_CENTER = 2, TOUCH_RIGHT = 3;

    private int prevRawX = 0;
    private int mTotalWidth = 0, mHandleWidth = 0, mIndicatorWidth, mLeftHandleWidth, mRightHandleWidth;
    private int originLeft = 0, originRight = 0, leftToRight = 0;

    private long mMinDuration = 0, mMaxDuration = 0;
    private int mMinSpan = 0, mDurationWidth = 0, mPerSecondWidth = 0, m_touchWidth = 30;
    private long mInPoint = 0, mOutPoint = 0;
    private View mBaseLineView;
    private View mBaseLineViewTop;
    private TextView mTvMusicTimeFllow;
    private TextView mTvSelectMusicTime;
    private TextView tvFollowLeft, tvFollowRight;
    private TextView rightTime, leftTime;

    public CutMusicView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    public CutMusicView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public CutMusicView(Context context) {
        super(context);
        init(context);
    }

    private void init(Context context) {
        mContext = context;
        LayoutInflater.from(context).inflate(R.layout.cut_music_view, this);
        mMainLayout = (RelativeLayout) findViewById(R.id.main_layout);
        mHandleLayout = (RelativeLayout) findViewById(R.id.handle_layout);
        realFollowTime = (RelativeLayout) findViewById(R.id.real_follow_time);
        mLeftHandle = (ImageView) findViewById(R.id.leftHandle);
        mRightHandle = (ImageView) findViewById(R.id.rightHandle);
        leftTime = (TextView) findViewById(R.id.leftTime);
        rightTime = (TextView) findViewById(R.id.rightTime);
        mIndicatorView = findViewById(R.id.indicator_view);
        mIndicatorWidth = mIndicatorView.getLayoutParams().width;
        mBaseLineView = findViewById(R.id.view_base_line);
        mBaseLineViewTop = findViewById(R.id.view_base_line_top);
        mTvMusicTimeFllow = findViewById(R.id.tv_music_time_follow);
        mTvSelectMusicTime = findViewById(R.id.select_music_time);
        tvFollowLeft = findViewById(R.id.tv_follow_left);
        tvFollowRight = findViewById(R.id.tv_follow_right);
        mLeftHandle.post(new Runnable() {
            @Override
            public void run() {
                mLeftHandleWidth = mLeftHandle.getWidth();
                mRightHandleWidth = mRightHandle.getWidth();
                mHandleWidth = mLeftHandleWidth + mRightHandleWidth;
            }
        });
    }

    /**
     * Sets cut layout width.
     * 设置减少宽度布局
     * @param width the width
     */
    public void setCutLayoutWidth(int width) {
        mTotalWidth = width;
        mDurationWidth = mTotalWidth - mHandleWidth;
    }

    public void setMinDuration(long min_duration) {
        mMinDuration = min_duration;
    }

    public long getMinDuration() {
        return mMinDuration;
    }

    public void setMaxDuration(long max_duration) {
        mMaxDuration = max_duration;
        mOutPoint = mMaxDuration;

        double per_second_w = 1000000 / mMaxDuration * mDurationWidth;
        mPerSecondWidth = (int) per_second_w;
    }

    public long getInPoint() {
        return mInPoint;
    }

    public void setInPoint(long inPoint) {
        this.mInPoint = inPoint;
    }

    public long getOutPoint() {
        return mOutPoint;
    }

    public void setOutPoint(long outPoint) {
        this.mOutPoint = outPoint;
    }

    /**
     * Re layout.
     * 重新布局
     */
    public void reLayout() {
        tvFollowRight.setText(FormatUtils.microsecond2Time(getOutPoint()));
        originRight = mTotalWidth;
        originLeft = 0;
        LayoutParams lp = (LayoutParams) mHandleLayout.getLayoutParams();
        lp.width = originRight - originLeft;
        lp.setMargins(originLeft, 0, mTotalWidth - originRight, 0);

       /* LayoutParams tvFollowLeftLayoutParams = (LayoutParams) realFollowTime.getLayoutParams();
        tvFollowLeftLayoutParams.width = originRight - originLeft;
        tvFollowLeftLayoutParams.setMargins(originLeft, 0, mTotalWidth - originRight, 0);
        realFollowTime.setLayoutParams(tvFollowLeftLayoutParams);*/
        tvFollowLeft.setText(FormatUtils.microsecond2Time(mInPoint));
        tvFollowRight.setText(FormatUtils.microsecond2Time(mOutPoint));

        mHandleLayout.setLayoutParams(lp);

        mLeftHandle.post(new Runnable() {
            @Override
            public void run() {
                mLeftHandleWidth = mLeftHandle.getWidth();
                mRightHandleWidth = mRightHandle.getWidth();
                mHandleWidth = mLeftHandleWidth + mRightHandleWidth;
            }
        });
    }

    /**
     * Sets indicator.
     * 设置指标
     * @param inPoint the in point 点
     */
    public void setIndicator(long inPoint) {
        double per_second_w = (double) inPoint / mMaxDuration * mDurationWidth;
        LayoutParams lp = (LayoutParams) mIndicatorView.getLayoutParams();
        lp.width = mIndicatorWidth;
        lp.setMargins((int) per_second_w + mLeftHandleWidth, 0, 0, 0);
        mIndicatorView.setLayoutParams(lp);
        ViewGroup.LayoutParams layoutParams = mBaseLineViewTop.getLayoutParams();
        layoutParams.width = (int) per_second_w + mLeftHandleWidth;
        mBaseLineViewTop.setLayoutParams(layoutParams);

        RelativeLayout.LayoutParams mTvMusicTimeLayoutParams = (LayoutParams) mTvSelectMusicTime.getLayoutParams();
        mTvMusicTimeLayoutParams.setMargins((int) per_second_w + mLeftHandleWidth, 0, 0, 0);
        mTvSelectMusicTime.setLayoutParams(mTvMusicTimeLayoutParams);
        mTvSelectMusicTime.setText(FormatUtils.microsecond2Time(inPoint));
    }

    /**
     * Sets can touch center move.
     * 设置可以触摸中心移动
     * @param can_move the can move 能动
     */
    public void setCanTouchCenterMove(boolean can_move) {
        mCanTouchCenter = can_move;
    }

    /**
     * Sets on seek bar changed listener.
     * 设置在搜索栏改变监听器
     * @param mListener the m listener  监听
     */
    public void setOnSeekBarChangedListener(OnSeekBarChanged mListener) {
        this.mListener = mListener;
    }

    /**
     * Sets right handle visiable.
     * 设置正确的handle可见
     * @param visiable the visiable
     */
    public void setRightHandleVisiable(boolean visiable) {
        if (visiable) {
            mRightHandle.setVisibility(VISIBLE);
            mRightHandleWidth = mRightHandle.getLayoutParams().width;
        } else {
            mRightHandle.setVisibility(INVISIBLE);
            mRightHandleWidth = 0;
        }
        mHandleWidth = mLeftHandleWidth + mRightHandleWidth;
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        float cur_x = event.getX();
        float cur_y = event.getY();
//        Log.e("===>", "x: " + cur_x + " " + " y: " + cur_y);

        // 避免误差 Avoid error
        String min_duration_text = FormatUtils.microsecond2Time(mMinDuration);
        String max_duration_text = FormatUtils.microsecond2Time(mMaxDuration);
        if (min_duration_text.equals(max_duration_text) || mMinDuration >= mMaxDuration) {
            return true;
        }
        int action = event.getAction();
        if (action == MotionEvent.ACTION_DOWN) {
            originLeft = mHandleLayout.getLeft();
            originRight = mHandleLayout.getRight();
            prevRawX = (int) event.getRawX();
            mCurrentTouch = getTouchMode(cur_x, cur_y);
        } else if (action == MotionEvent.ACTION_MOVE) {
            int tempRawX = (int) event.getRawX();
            int dx = tempRawX - prevRawX;
            prevRawX = tempRawX;

            if (mCurrentTouch == TOUCH_LEFT) {
                tvFollowLeft.setVisibility(VISIBLE);
                left(dx);
                LayoutParams lp = (LayoutParams) mHandleLayout.getLayoutParams();
                lp.width = originRight - originLeft;
                lp.setMargins(originLeft, 0, mTotalWidth - originRight, 0);
                mHandleLayout.setLayoutParams(lp);

                mInPoint = (long) Math.floor((float) originLeft / mDurationWidth * mMaxDuration + 0.5D);
                if (mListener != null) {
                   /* LayoutParams tvFollowLeftLayoutParams = (LayoutParams) realFollowTime.getLayoutParams();
                    tvFollowLeftLayoutParams.width = originRight - originLeft;
                    tvFollowLeftLayoutParams.setMargins(originLeft, 0, mTotalWidth - originRight, 0);
                    realFollowTime.setLayoutParams(tvFollowLeftLayoutParams);*/
                    tvFollowLeft.setText(FormatUtils.microsecond2Time(mInPoint));
                    mListener.onLeftValueChange(mInPoint);
                }

            } else if (mCurrentTouch == TOUCH_RIGHT) {
                tvFollowRight.setVisibility(VISIBLE);
                right(dx);
                LayoutParams lp = (LayoutParams) mHandleLayout.getLayoutParams();
                lp.width = originRight - originLeft;
                lp.setMargins(originLeft, 0, mTotalWidth - originRight, 0);
                mHandleLayout.setLayoutParams(lp);

                mOutPoint = (long) Math.floor((float) (originRight - mHandleWidth) / mDurationWidth * mMaxDuration + 0.5D);
                if (mListener != null) {
                  /*  LayoutParams tvFollowLeftLayoutParams = (LayoutParams) realFollowTime.getLayoutParams();
                    tvFollowLeftLayoutParams.width = originRight - originLeft;
                    tvFollowLeftLayoutParams.setMargins(originLeft, 0, mTotalWidth - originRight, 0);
                    realFollowTime.setLayoutParams(tvFollowLeftLayoutParams);*/
                    tvFollowRight.setText(FormatUtils.microsecond2Time(mOutPoint));
                    mListener.onRightValueChange(mOutPoint);
                }

            } else if (mCurrentTouch == TOUCH_CENTER) {
                if (!mCanTouchCenter) {
                    return true;
                }
                leftToRight = mMainLayout.getWidth();

                center(dx);

                LayoutParams lp = (LayoutParams) mHandleLayout.getLayoutParams();
                lp.setMargins(originLeft, 0, mTotalWidth - originRight, 0);
                mHandleLayout.setLayoutParams(lp);

                mInPoint = (long) Math.floor((float) originLeft / mDurationWidth * mMaxDuration + 0.5D);
                mOutPoint = (long) Math.floor((float) (originRight - mHandleWidth) / mDurationWidth * mMaxDuration + 0.5D);
                if (mListener != null)
                    mListener.onCenterTouched(mInPoint, mOutPoint);
            }
        } else if (action == MotionEvent.ACTION_UP) {
            if (mListener != null) {
                if (mCurrentTouch == TOUCH_LEFT) {
                    mListener.onUpTouched(true, mInPoint, mOutPoint);
                } else {
                    mListener.onUpTouched(false, mInPoint, mOutPoint);
                }
            }
        }
        return true;
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
    }

    private int getTouchMode(float x, float y) {
        int left = mHandleLayout.getLeft();
        int right = mHandleLayout.getRight();

//        Log.e("===>", "left: " + left + " right: " + right + " x: " + x + " handle_width: " + mHandleWidth);
        // 此处m_touchWidth是为了增加touch区间，使触摸更加敏感 Here m_touchWidth is used to increase the touch range and make the touch more sensitive
        if (x - left < mLeftHandleWidth + m_touchWidth && x - left >  -m_touchWidth) {
            return TOUCH_LEFT;
        }
        if (right - x < mRightHandleWidth + m_touchWidth && right - x > -m_touchWidth) {
            return TOUCH_RIGHT;
        }
        return TOUCH_CENTER;
    }

    /**
     * 触摸点为右边缘
     * The touch point is the right edge
     */
    private void right(int dx) {
        mMinSpan = (int) Math.floor((float) mMinDuration / mMaxDuration * mDurationWidth + 0.5D);

        originRight += dx;
        if (originRight > mTotalWidth) {
            originRight = mTotalWidth;
        }
        if (originRight - originLeft - mHandleWidth < mMinSpan) {
            originRight = originLeft + mMinSpan + mHandleWidth;
        }
    }

    /**
     * 触摸点为左边缘
     * The touch point is the left edge
     */
    private void left(int dx) {
        mMinSpan = (int) Math.floor((float) mMinDuration / mMaxDuration * mDurationWidth + 0.5D);

        originLeft += dx;
        if (originLeft < 0) {
            originLeft = 0;
        }
        if (originRight - originLeft - mHandleWidth < mMinSpan) {
            originLeft = originRight - mHandleWidth - mMinSpan;
        }
    }

    /**
     * 触摸点为中心
     * Touch points are centered
     */
    private void center(int dx) {
        originLeft += dx;
        originRight += dx;
        if (originLeft <= 0) {
            originLeft = 0;
            originRight = originLeft + leftToRight;
        }
        if (originRight > mTotalWidth) {
            originRight = mTotalWidth;
            originLeft = originRight - leftToRight;
        }
    }

    /**
     * The interface On seek bar changed.
     * 拖动条改变的接口
     */
    public interface OnSeekBarChanged {
        /**
         * On left value change.
         * 左值变化
         * @param var the var
         */
        void onLeftValueChange(long var);

        /**
         * On right value change.
         * 右值变化
         * @param var the var
         */
        void onRightValueChange(long var);

        /**
         * On center touched.
         * 中心触摸
         * @param left  the left 左
         * @param right the right 右
         */
        void onCenterTouched(long left, long right);

        /**
         * On up touched.
         * 向上触摸
         * @param touch_left the touch left 触摸左
         * @param left       the left 左
         * @param right      the right 右
         */
        void onUpTouched(boolean touch_left, long left, long right);
    }
}
