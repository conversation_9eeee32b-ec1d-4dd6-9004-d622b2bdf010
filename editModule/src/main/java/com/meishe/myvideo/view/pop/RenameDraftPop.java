package com.meishe.myvideo.view.pop;

import android.content.Context;
import android.text.Editable;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;

import androidx.annotation.NonNull;

import com.meishe.base.utils.KeyboardUtils;
import com.meishe.myvideo.R;
import com.meishe.third.pop.XPopup;
import com.meishe.third.pop.core.CenterPopupView;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> LiHangZhou
 * @CreateDate :2020/12/3 17:58
 * @Description :重命名草稿的弹窗 Rename the draft dialog
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class RenameDraftPop extends CenterPopupView {
    private EditText mEtDraftName;
    private EventListener mListener;
    private String mDraftName;

    public static RenameDraftPop create(Context context, EventListener listener) {
        return (RenameDraftPop) new XPopup.Builder(context)
                .asCustom(new RenameDraftPop(context)
                        .setEventListener(listener));
    }

    public RenameDraftPop(@NonNull Context context) {
        super(context);
    }

    @Override
    protected int getImplLayoutId() {
        return R.layout.dialog_rename_draft;
    }

    @Override
    protected int getPopupWidth() {
        return (int) getResources().getDimension(R.dimen.dp_px_660);
    }

    @Override
    protected void onCreate() {
        super.onCreate();
        mEtDraftName = findViewById(R.id.et_draft_name);
        ImageView ivCancel = findViewById(R.id.iv_cancel);
        ivCancel.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });
        Button btConfirm = findViewById(R.id.bt_confirm);
        btConfirm.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mListener != null) {
                    Editable text = mEtDraftName.getText();
                    if (text != null) {
                        mDraftName = text.toString();
                    }
                    mListener.onConfirm(mDraftName);
                }
            }
        });
    }

    /**
     * 展示重命名弹窗
     * Display the rename draft dialog
     *
     * @param name String The draft name
     */
    public void show(String name) {
        mDraftName = name;
        show();
    }

    @Override
    protected void onShow() {
        super.onShow();
        mEtDraftName.setText(mDraftName);
        KeyboardUtils.showSoftInput(this);
    }

    @Override
    protected void onDismiss() {
        super.onDismiss();
        KeyboardUtils.hideSoftInput(this);
    }

    /**
     * 设置事件监听
     * Set event listener
     *
     * @param listener EventListener the listener
     */
    public RenameDraftPop setEventListener(EventListener listener) {
        mListener = listener;
        return this;
    }

    public interface EventListener {
        /**
         * 确定
         * Confirm
         *
         * @param name the name
         */
        void onConfirm(String name);
    }
}
