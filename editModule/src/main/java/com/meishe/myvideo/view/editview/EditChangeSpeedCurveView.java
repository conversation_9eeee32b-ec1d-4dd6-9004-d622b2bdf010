package com.meishe.myvideo.view.editview;

import android.content.Context;
import android.os.Build;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.RequiresApi;

import com.meishe.base.utils.FormatUtils;
import com.meishe.base.utils.Utils;
import com.meishe.base.view.NvBezierSpeedView;
import com.meishe.myvideo.R;
import com.meishe.myvideo.bean.ChangeSpeedCurveInfo;
import com.meishe.myvideo.interfaces.BottomEventListener;

import java.util.List;

/**
 * The type Edit change speed curve view.
 * 编辑改变速度曲线视图类
 */
public class EditChangeSpeedCurveView extends RelativeLayout {
    /**
     * 页面使用到的view
     * used view in page
     */
    private TextView mTvChangePoint, mTvReset, mTvFromTime, mTvToTime;
    private View mVPointMask;
    private ImageView mIvChangePoint, mIvConfirm;
    private NvBezierSpeedView mBezierView;
    private int currSelected = -1;

    private TextView mTvCurveName;
    private boolean mIsPlaying = true;
    private SpeedCurveListener mEventListener;
    private ChangeSpeedCurveInfo speedCurveInfo;

    public EditChangeSpeedCurveView(Context context) {
        this(context, null);
    }

    public EditChangeSpeedCurveView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public EditChangeSpeedCurveView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView();
        initListener();
    }

    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    public EditChangeSpeedCurveView(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        initView();
        initListener();
    }

    /**
     * Init view.
     * 初始化视图
     */
    protected void initView() {
        setClickable(true);
        setBackgroundColor(getResources().getColor(R.color.black));
        View rootView = LayoutInflater.from(getContext()).inflate(R.layout.view_edit_chang_speed_curve, this);
        mTvFromTime = rootView.findViewById(R.id.tv_from_time);
        mTvToTime = rootView.findViewById(R.id.tv_to_time);
        mBezierView = rootView.findViewById(R.id.speed_view);
        mTvChangePoint = rootView.findViewById(R.id.tv_point);
        mIvChangePoint = rootView.findViewById(R.id.iv_point);
        mVPointMask = rootView.findViewById(R.id.v_point_mask);
        mTvCurveName = rootView.findViewById(R.id.tv_curve_name);
        mTvReset = rootView.findViewById(R.id.tv_reset);
        mIvConfirm = rootView.findViewById(R.id.iv_confirm);

    }


    /**
     * Init listener.
     * 初始化监听
     */
    protected void initListener() {
        mTvChangePoint.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (Utils.isFastClick()){
                    return;
                }
                mBezierView.addOrDeletePoint(currSelected);
            }
        });

        mIvConfirm.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mEventListener != null) {
                    mEventListener.onDismiss(true);
                }
            }
        });

        mTvReset.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                mBezierView.reset();
            }
        });

        mBezierView.setOnBezierListener(new NvBezierSpeedView.OnBezierListener() {
            @Override
            public void startPlay(String speed) {
                if (!mIsPlaying || mEventListener == null) {
                    return;
                }
                mEventListener.onStartPlay(speedCurveInfo.getSpeedOriginal(), speed);
            }

            @Override
            public void onSelectedPoint(int position) {
                currSelected = position;
                List<NvBezierSpeedView.BzPoint> list = mBezierView.getList();
                if (position != -1) {
                    if (position == 0 || position == list.size() - 1) {
                        mVPointMask.setVisibility(VISIBLE);
                        mTvChangePoint.setClickable(false);
                    } else {
                        mVPointMask.setVisibility(GONE);
                        mTvChangePoint.setClickable(true);
                    }
                    mTvChangePoint.setText(getResources().getString(R.string.tv_point_remove));
                    mIvChangePoint.setImageResource(R.mipmap.icon_remove_point);
                } else {
                    mTvChangePoint.setClickable(true);
                    mVPointMask.setVisibility(GONE);
                    mTvChangePoint.setText(getResources().getString(R.string.tv_point_add));
                    mIvChangePoint.setImageResource(R.mipmap.icon_add_point);
                }
            }

            @Override
            public void seekPosition(long position) {
                if (mEventListener != null) {
                    mEventListener.onSeekPosition(position);
                }
            }
        });
    }

    /**
     * 设置事件监听
     * Sets the listener
     *
     * @param listener the listener 监听
     */
    public void setListener(SpeedCurveListener listener) {
        mEventListener = listener;
    }

    /**
     * 更新视图view
     * Update the view
     *
     * @param duration         the clip duration 剪辑时间
     * @param originalDuration the clip original duration 原始剪辑时间
     * @param trimDuration     the clip trim duration 修剪时间
     * @param curveName        the speed cure view name 视图名称
     * @param curveSpeed       the speed cure view speed 观察速度
     * @param baseInfo         the speed cure view info 速度查看信息
     */
    public void updateView(long duration, long originalDuration, long trimDuration, String curveName, String curveSpeed, ChangeSpeedCurveInfo baseInfo) {
        mTvFromTime.setText(String.format(getResources().getString(R.string.tv_from_time_format), FormatUtils.microsecond2Time(originalDuration)));
        mBezierView.setDuring(trimDuration);
        updateDuration(duration);
        speedCurveInfo = baseInfo;
        if ((!TextUtils.isEmpty(curveName))) {
            mBezierView.setSpeedPoint(curveSpeed);
        } else {
            mBezierView.setSpeedPoint(baseInfo.getSpeedOriginal());
        }
        mBezierView.setSpeedOriginal(speedCurveInfo.getSpeedOriginal());
        mTvCurveName.setText(speedCurveInfo.getName());
    }


    /**
     * 更新基准线
     * Update the baseline
     *
     * @param stamp 当前时间节点
     */
    public void updateBaseLine(long stamp) {
        mBezierView.setUpdeteBaseLine(stamp);
    }

    /**
     * 根据视频片段长度更新时间
     * Update time according to video clip length
     *
     * @param duration the duration
     */
    public void updateDuration(long duration) {
        String text = FormatUtils.microsecond2Time(duration) + "s";
        mTvToTime.setText(text);
    }


    /**
     * 是否按了暂停
     * Pause or not
     *
     * @param isPlaying the is playing
     */
    public void setPlayState(boolean isPlaying) {
        mIsPlaying = isPlaying;
    }

    /**
     * The type Speed curve listener.
     * 速度曲线监听器
     */
    public static abstract class SpeedCurveListener extends BottomEventListener {
        /**
         * On start play.
         * 开始播放
         *
         * @param speed the speed 速度
         */
        public abstract void onStartPlay(String originalSpeed, String speed);

        /**
         * On seek position.
         * 寻找位置
         *
         * @param position the position 位置
         */
        public abstract void onSeekPosition(long position);
    }
}
