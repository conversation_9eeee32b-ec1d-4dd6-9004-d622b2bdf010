package com.meishe.myvideo.view.pop;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.view.MotionEvent;
import android.view.View;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.RadioButton;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.meishe.myvideo.R;
import com.meishe.speaker.VoiceDictationHelperWrapper;
import com.meishe.speaker.bean.VoiceParam;

import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> LiHangZhou
 * @CreateDate :2021/3/1 15:35
 * @Description :识别字幕弹窗 Identify caption dialog
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class IdentifyCaptionDlg extends Dialog implements View.OnClickListener {
    private static final int TYPE_VIDEO = 0;
    private static final int TYPE_RECORD = 1;
    private static final int TYPE_ALL = 2;
    private RadioButton mRbtClearCaption;
    private Button mBtStartIdentify;
    private VoiceDictationHelperWrapper.SpeechWrapListener mSpeechListener;
    private ImageButton mBtnOnlyVideo, mBtnOnlyRecord, mBtnAll;
    private TextView mTvOnlyVideo, mTvOnlyRecord, mTvAll;
    private int mType;
    private boolean mHasAudio = true;

    public IdentifyCaptionDlg(@NonNull Context context) {
        super(context, R.style.dialog);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.dialog_identify_caption);
        setCanceledOnTouchOutside(false);
        initView();
    }

    @Override
    protected void onStart() {
        super.onStart();
        selectType(TYPE_ALL);
        updateItemEnableState();
    }

    @SuppressLint("ClickableViewAccessibility")
    private void initView() {
        ImageView ivClose = findViewById(R.id.iv_close);
        mRbtClearCaption = findViewById(R.id.rb_clear_caption);
        mBtStartIdentify = findViewById(R.id.bt_start_identify);
        mBtnOnlyVideo = findViewById(R.id.btn_only_video);
        mBtnOnlyRecord = findViewById(R.id.btn_only_record);
        mBtnAll = findViewById(R.id.btn_all);
        mTvOnlyVideo = findViewById(R.id.tv_only_video);
        mTvOnlyRecord = findViewById(R.id.tv_only_record);
        mTvAll = findViewById(R.id.tv_all);
        mBtnOnlyVideo.setOnClickListener(this);
        mBtnOnlyRecord.setOnClickListener(this);
        mBtnAll.setOnClickListener(this);
        ivClose.setOnClickListener(this);
        mBtStartIdentify.setOnClickListener(this);
        mRbtClearCaption.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                if (event.getAction() == MotionEvent.ACTION_DOWN) {
                    mRbtClearCaption.setChecked(!mRbtClearCaption.isChecked());
                }
                return true;
            }
        });

        updateItemEnableState();
    }

    private void updateItemEnableState() {
        int color;
        if (mHasAudio) {
            color = getContext().getResources().getColor(R.color.color_ff101010);
        } else {
            color = getContext().getResources().getColor(R.color.color_ffc1c1c1);
        }
        mTvOnlyRecord.setTextColor(color);
    }

    public boolean clearCaption() {
        return mRbtClearCaption.isChecked();
    }

    public void show(VoiceDictationHelperWrapper.SpeechWrapListener listener) {
        mSpeechListener = listener;
        show();
    }

    public void show(boolean hasAudio, VoiceDictationHelperWrapper.SpeechWrapListener listener) {
        mHasAudio = hasAudio;
        show(listener);
    }

    @Override
    public void dismiss() {
        mBtStartIdentify.setText(R.string.start_identify);
        mSpeechListener = null;
        super.dismiss();
    }

    @SuppressLint("NonConstantResourceId")
    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.bt_start_identify) {
            if (mSpeechListener != null) {
                if (VoiceDictationHelperWrapper.get().isListening()) {
                    return;
                }
                List<List<VoiceParam>> voiceParams = mSpeechListener.onPrepare(mType);
                VoiceDictationHelperWrapper.get().startDictation(voiceParams, mSpeechListener);
                dismiss();
            }
        } else if (id == R.id.iv_close) {
            if (mSpeechListener != null) {
                dismiss();
            }
        } else if (id == R.id.btn_only_video) {
            selectType(TYPE_VIDEO);
        } else if (id == R.id.btn_only_record) {
            if (mHasAudio) {
                selectType(TYPE_RECORD);
            }
        } else if (id == R.id.btn_all) {
            selectType(TYPE_ALL);
        }
    }

    private void selectType(int type) {
        mType = type;
        selectOnlyVideo(type == TYPE_VIDEO);
        selectOnlyRecord(type == TYPE_RECORD);
        selectAll(type == TYPE_ALL);
    }

    private void selectOnlyVideo(boolean isSelected) {
        if (isSelected) {
            mBtnOnlyVideo.setImageResource(R.mipmap.icon_only_video_selected);
        } else {
            mBtnOnlyVideo.setImageResource(R.mipmap.icon_only_video_unselected);
        }
    }

    private void selectOnlyRecord(boolean isSelected) {
        if (isSelected) {
            mBtnOnlyRecord.setImageResource(R.mipmap.icon_only_record_selected);
        } else {
            mBtnOnlyRecord.setImageResource(R.mipmap.icon_only_record_unselected);
        }
    }

    private void selectAll(boolean isSelected) {
        if (isSelected) {
            mBtnAll.setImageResource(R.mipmap.icon_all_selected);
        } else {
            mBtnAll.setImageResource(R.mipmap.icon_all_unselected);
        }

    }
}
