package com.meishe.myvideo.view.pop;

import android.content.Context;
import android.view.View;

import androidx.annotation.NonNull;

import com.meishe.myvideo.R;
import com.meishe.myvideo.util.ConfigUtil;
import com.meishe.third.pop.XPopup;
import com.meishe.third.pop.core.BottomPopupView;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> LiHangZhou
 * @CreateDate :2020/12/3 17:33
 * @Description :管理草稿的弹窗 Manage the dialog for drafts
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class ManageDraftPop extends BottomPopupView implements View.OnClickListener {
    private ManageListener mListener;

    public static ManageDraftPop create(Context context, ManageListener listener) {
        return (ManageDraftPop) new XPopup.Builder(context)
                .asCustom(new ManageDraftPop(context).setManageListener(listener));

    }

    public ManageDraftPop(@NonNull Context context) {
        super(context);
    }

    @Override
    protected int getImplLayoutId() {
        return R.layout.dialog_manage_draft;
    }

    @Override
    protected void onCreate() {
        super.onCreate();
        findViewById(R.id.tv_rename).setOnClickListener(this);
        findViewById(R.id.tv_copy).setOnClickListener(this);
        findViewById(R.id.tv_delete).setOnClickListener(this);
        View uploadTextView = findViewById(R.id.tv_upload);
        uploadTextView.setOnClickListener(this);
        View uploadHintView = findViewById(R.id.tv_upload_hint);
        uploadHintView.setOnClickListener(this);
        View compileTextView = findViewById(R.id.tv_cloud_compile);
        View compileImageView = findViewById(R.id.iv_cloud_compile);

        View uploadImageView = findViewById(R.id.iv_upload);
        if (ConfigUtil.isToC() || !ConfigUtil.IS_NEED_CLOUD) {
            compileTextView.setVisibility(GONE);
            compileImageView.setVisibility(GONE);
            uploadImageView.setVisibility(GONE);
            uploadHintView.setVisibility(GONE);
            uploadTextView.setVisibility(GONE);
        }
        compileTextView.setOnClickListener(this);
    }

    /**
     * 设置事件监听
     * Set event listener
     *
     * @param listener ManageListener the listener
     */
    public ManageDraftPop setManageListener(ManageListener listener) {
        mListener = listener;
        return this;
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.tv_rename) {
            if (mListener != null) {
                mListener.onRename();
            }
        } else if (id == R.id.tv_copy) {
            if (mListener != null) {
                mListener.onCopy();
            }
        } else if (id == R.id.tv_delete) {
            if (mListener != null) {
                mListener.onDelete();
            }
        } else if (id == R.id.tv_upload || id == R.id.tv_upload_hint) {
            if (mListener != null) {
                mListener.onUpload();
            }
            dismiss();
        } else if (id == R.id.tv_cloud_compile) {
            if (mListener != null) {
                mListener.onCompile();
            }
            dismiss();
        }
    }

    public interface ManageListener {
        /**
         * 重命名
         * Rename
         */
        void onRename();

        /**
         * 复制
         * Copy
         */
        void onCopy();

        /**
         * 删除
         * Delete
         */
        void onDelete();

        /**
         * 上传
         * On upload.
         */
        void onUpload();

        /**
         * 合成
         * On compile.
         */
        void onCompile();
    }
}
