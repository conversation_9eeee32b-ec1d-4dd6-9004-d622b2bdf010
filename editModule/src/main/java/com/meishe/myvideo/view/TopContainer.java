package com.meishe.myvideo.view;

import android.annotation.SuppressLint;
import android.content.Context;
import android.os.Build;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;

import com.meishe.base.model.IBaseView;
import com.meishe.base.model.NvsError;


/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> LiHangZhou
 * @CreateDate :2021/03/02 16:08
 * @Description :顶部视图容器 Top view container
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class TopContainer extends FrameLayout implements IBaseView {
    private Fragment mShowFragment;
    private View mShowView;
    private FragmentManager mFragmentManager;
    public TopContainer(@NonNull Context context) {
        this(context, null);
    }

    public TopContainer(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public TopContainer(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    public TopContainer(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        init();
    }

    private void init() {
        setOnTouchListener(new OnTouchListener() {
            @SuppressLint("ClickableViewAccessibility")
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                //拦截点击事件 Intercept click events.
                return v.getId() == getId();
            }
        });
    }

    /**
     * Set fragment manager
     *
     * @param fragmentManager the fragmentManager
     */
    public void setFragmentManager(FragmentManager fragmentManager) {
        mFragmentManager = fragmentManager;
    }
    /**
     * 显示视图view
     * Show the view
     *
     * @param view  the view
     */
    public void showView(View view) {
       // Animation animation = AnimationUtils.loadAnimation(getContext(), R.anim.view_enter);
       // setAnimation(animation);
        setVisibility(View.VISIBLE);
        mShowView = view;
        addView(view);
    }

    /**
     * 销毁视图view
     * Dismiss the view
     */
    public void dismissView() {
       // Animation animation = AnimationUtils.loadAnimation(getContext(), R.anim.view_exit);
       // setAnimation(animation);
        setVisibility(View.INVISIBLE);
        removeAllViews();
        mShowView = null;
    }

    /**
     * 获取正在显示的视图view
     * Gets the show view
     */
    public View getShowView() {
        return mShowView;
    }

    /**
     * 替换显示目标fragment页面
     * Show and replace the fragment
     *
     * @param fragment the fragment
     * @param fragmentManager the fragmentManager
     */
    public void showReplaceFragment(Fragment fragment, FragmentManager fragmentManager) {
        setVisibility(VISIBLE);
        mShowFragment = fragment;
        FragmentTransaction transaction = fragmentManager.beginTransaction();
        transaction.replace(getId(), fragment);
        transaction.show(fragment);
        transaction.commitAllowingStateLoss();
    }

    /**
     * 显示目标fragment页面
     * Show the fragment
     *
     *@param fragment the fragment
     *@param fragmentManager the fragmentManager
     */
    public void showFragment(Fragment fragment, FragmentManager fragmentManager) {
        setVisibility(VISIBLE);
        mShowFragment = fragment;
        FragmentTransaction transaction = fragmentManager.beginTransaction();
        if (!fragment.isAdded()) {
            transaction.add(getId(), fragment);
        }
        transaction.show(fragment);
        transaction.commitAllowingStateLoss();
    }

    /**
     * 不显示fragment页面
     * Dismiss the fragment
     */
    public void dismissFragment() {
        setVisibility(INVISIBLE);
        if (mShowFragment != null) {
            FragmentTransaction transaction = mFragmentManager.beginTransaction();
            transaction.remove(mShowFragment);
            transaction.commitAllowingStateLoss();
        }
        mShowFragment = null;
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        if (mShowFragment != null) {
            dismissFragment();
        }
    }


    /**
     * 获取正在显示的fragment
     * Gets the show fragment
     */
    public Fragment getShowFragment() {
        return mShowFragment;
    }

    @Override
    public void onShowDialog() {

    }

    @Override
    public void onDismissDialog() {

    }

    @Override
    public void onError(NvsError error) {

    }
}
