package com.meishe.myvideo.view;

import android.annotation.SuppressLint;
import android.content.Context;
import android.text.Editable;
import android.text.TextWatcher;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;

import androidx.recyclerview.widget.RecyclerView;

import com.meishe.base.manager.LinearLayoutManagerWrapper;
import com.meishe.base.utils.KeyboardUtils;
import com.meishe.base.utils.SizeUtils;
import com.meishe.base.view.decoration.ItemDecoration;
import com.meishe.engine.bean.MeicamCompoundCaptionClip;
import com.meishe.engine.interf.IBaseInfo;
import com.meishe.myvideo.R;
import com.meishe.myvideo.bean.CaptionFontInfo;
import com.meishe.myvideo.bean.ColorInfo;
import com.meishe.myvideo.fragment.adapter.CaptionFontAdapter;
import com.meishe.myvideo.interfaces.BottomEventListener;
import com.meishe.third.adpater.BaseQuickAdapter;

import java.util.List;


/**
 * The type My compound caption edit view.
 * Composite subtitle editing
 *
 * <AUTHOR>
 * @Description : 复合字幕编辑 The compound caption edit view
 * @CreateDate :2020/6/18 11:10
 */
public class MYCompoundCaptionEditView extends RelativeLayout implements View.OnClickListener {

    private ImageView mIvConfirm;
    private EditText mEtCaptionInput;
    private ImageView mIvCancel;
    private MYMultiColorView mMultiColorView;
    private LinearLayout mLlColorAndFontContainer;
    private CompoundCaptionListener mEventListener;
    private CaptionFontAdapter mAdapter;

    public MYCompoundCaptionEditView(Context context) {
        this(context, null);
    }

    public MYCompoundCaptionEditView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public MYCompoundCaptionEditView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView();
        initListener();
    }

    private void initView() {
        setBackgroundColor(getResources().getColor(R.color.black));
        View view = LayoutInflater.from(getContext()).inflate(R.layout.compound_caption_edit_view, this);
        mMultiColorView = view.findViewById(R.id.multi_color_view);
        mEtCaptionInput = view.findViewById(R.id.et_caption_input);
        mIvConfirm = view.findViewById(R.id.iv_confirm);
        mIvCancel = view.findViewById(R.id.iv_cancel);
        RecyclerView rvFontList = view.findViewById(R.id.rv_font_list);
        mLlColorAndFontContainer = view.findViewById(R.id.ll_font_and_color_container);

        mAdapter = new CaptionFontAdapter(getResources().getDimension(R.dimen.sp_px_27), (int) getResources().getDimension(R.dimen.dp_px_183));
        rvFontList.setAdapter(mAdapter);
        rvFontList.setLayoutManager(new LinearLayoutManagerWrapper(getContext(), RecyclerView.HORIZONTAL, false));
        rvFontList.addItemDecoration(new ItemDecoration((int) getResources().getDimension(R.dimen.dp_px_15), (int) getResources().getDimension(R.dimen.dp_px_15)));
    }

    private void initListener() {
        setOnTouchListener(new OnTouchListener() {
            @SuppressLint("ClickableViewAccessibility")
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                //拦截焦点 Intercept the focus
                return v.getId() == getId();
            }
        });
        mIvConfirm.setOnClickListener(this);
        mIvCancel.setOnClickListener(this);
        mAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                mAdapter.selected(position);
                if (mEventListener != null) {
                    mEventListener.onItemClick(mAdapter.getItem(position), false);
                }
            }
        });
        mEtCaptionInput.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                if (s != null && s.toString().length() > 0) {
                    if (mEventListener != null) {
                        mEventListener.onCaptionTextChange(s.toString());
                    }
                }
            }
        });
        mMultiColorView.setColorClickListener(new MYMultiColorView.OnColorClickListener() {
            @Override
            public void onClick(ColorInfo colorInfo) {
                if (colorInfo != null && mEventListener != null) {
                    mEventListener.onColorChange(colorInfo.getCommonInfo());
                }
            }
        });

    }


    /**
     * 设置初始数据
     * Sets the original data
     *
     * @param captionClip    the caption  组合字幕
     * @param fontList       the caption font list 标题字体列表
     * @param keyboardHeight the keyboard height 键盘高度
     */
    public void setData(MeicamCompoundCaptionClip captionClip,
                        List<IBaseInfo> fontList, int keyboardHeight) {
        String captionText = captionClip.getText(captionClip.getItemSelectedIndex());
        String captionFontFamily = captionClip.getFontFamily(captionClip.getItemSelectedIndex());
        String captionTextColor = captionClip.getTextColor(captionClip.getItemSelectedIndex());
        if (keyboardHeight != -1) {
            setKeyboardHeight(keyboardHeight);
        }
        mMultiColorView.selected(captionTextColor);
        List<IBaseInfo> fonts = null;
        if (fontList != null) {
            mAdapter.setNewData(fontList);
            fonts = fontList;
        } else {
            fonts = mAdapter.getData();
        }
        for (int i = 0; i < fonts.size(); i++) {
            CaptionFontInfo fontInfo = (CaptionFontInfo) fonts.get(i);
            if (fontInfo.getFontFamily().equals(captionFontFamily)) {
                mAdapter.selected(i);
            }
        }
        if (mEtCaptionInput != null) {
            mEtCaptionInput.setText(captionText);
            mEtCaptionInput.setSelection(mEtCaptionInput.getText().length());
            post(new Runnable() {
                @Override
                public void run() {
                    KeyboardUtils.showSoftInput(mEtCaptionInput, InputMethodManager.SHOW_IMPLICIT);
                }
            });
        }
    }

    /**
     * Sets the keyboard height
     * 设置键盘高度
     *
     * @param keyboardHeight the keyboard height
     */
    public void setKeyboardHeight(final int keyboardHeight) {
        post(new Runnable() {
            @Override
            public void run() {
                if (keyboardHeight <= 0) {
                    return;
                }
                ViewGroup.LayoutParams layoutParams = getLayoutParams();
                layoutParams.height = SizeUtils.dp2px(94) + keyboardHeight;
                setLayoutParams(layoutParams);
                ViewGroup.LayoutParams layoutParams1 = mLlColorAndFontContainer.getLayoutParams();
                layoutParams1.height = keyboardHeight;
                mLlColorAndFontContainer.setLayoutParams(layoutParams1);
            }
        });

    }

    private void hide() {
        if (mEtCaptionInput != null) {
            KeyboardUtils.hideSoftInput(mEtCaptionInput);
        }
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.iv_cancel) {
            hide();
            if (mEventListener != null) {
                mEventListener.onDismiss(false);
            }
        } else if (id == R.id.iv_confirm) {
            hide();
            if (mEventListener != null) {
                mEventListener.onDismiss(true);
            }
        }
    }


    /**
     * 设置事件监听
     * Sets the listener
     *
     * @param listener the listener
     */
    public void setListener(CompoundCaptionListener listener) {
        mEventListener = listener;
    }

    /**
     * The type Compound caption listener.
     * 复合标题监听类
     */
    public static abstract class CompoundCaptionListener extends BottomEventListener {
        /**
         * On color change.
         * 改变颜色
         *
         * @param colorValue the color value 颜色值
         */
        public void onColorChange(String colorValue) {
        }

        /**
         * On caption text change.
         * 标题文本的变化
         *
         * @param text the text 文本
         */
        public void onCaptionTextChange(String text) {
        }
    }
}
