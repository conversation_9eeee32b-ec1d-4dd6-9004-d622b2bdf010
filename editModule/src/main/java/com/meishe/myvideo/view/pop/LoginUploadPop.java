package com.meishe.myvideo.view.pop;

import android.content.Context;
import android.telephony.PhoneNumberUtils;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.EditText;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.meishe.base.utils.KeyboardUtils;
import com.meishe.base.utils.ScreenUtils;
import com.meishe.base.utils.ToastUtils;
import com.meishe.logic.utils.AppNetAPi;
import com.meishe.myvideo.R;
import com.meishe.myvideo.bean.LoginResponse;
import com.meishe.myvideo.bean.LoginParams;
import com.meishe.net.custom.BaseResponse;
import com.meishe.net.custom.RequestCallback;
import com.meishe.third.pop.XPopup;
import com.meishe.third.pop.core.BottomPopupView;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> LiHang<PERSON>hou
 * @CreateDate :2020/12/4 15:38
 * @Description :登录上传弹窗 Login upload pop
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class LoginUploadPop extends BottomPopupView {
    private EditText mEtAccount;
    private EditText mEtPassword;
    private TextView mTvUpload;
    private EventListener mListener;

    public static LoginUploadPop create(Context context, EventListener listener) {
        return (LoginUploadPop) new XPopup
                .Builder(context)
                .asCustom(new LoginUploadPop(context)
                        .setEventListener(listener));
    }

    public LoginUploadPop(@NonNull Context context) {
        super(context);
    }

    @Override
    protected int getImplLayoutId() {
        return R.layout.dialog_login_ali;
    }

    @Override
    protected int getPopupWidth() {
        return (int) (ScreenUtils.getScreenWidth() * 19 / 20f);
    }

    /*
        @Override
        protected void onShow() {
            super.onShow();
            KeyboardUtils.showSoftInput(this);
        }
    */
    @Override
    protected void onDismiss() {
        super.onDismiss();
        KeyboardUtils.hideSoftInput(this);
    }


    @Override
    protected void onCreate() {
        super.onCreate();
        mEtAccount = findViewById(R.id.et_account);
        mEtPassword = findViewById(R.id.et_password);
        mTvUpload = findViewById(R.id.tv_upload);
        mTvUpload.setEnabled(false);
        initListener();
    }

    private void initListener() {
        mEtAccount.addTextChangedListener(mTextWatcher);
        mEtPassword.addTextChangedListener(mTextWatcher);
        mTvUpload.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                Editable account = mEtAccount.getText();
                Editable password = mEtPassword.getText();
                if (account == null || password == null) {
                    return;
                }
                LoginParams loginParams = new LoginParams();
                loginParams.loginName = account.toString();
                loginParams.password = password.toString();
                AppNetAPi.loginCloudClip(null, loginParams, new RequestCallback<LoginResponse>() {

                    @Override
                    public void onSuccess(BaseResponse<LoginResponse> response) {
                        dismiss();
                        if (mListener != null) {
                            if (response.getCode() == 0) {
                                LoginResponse data = response.getData();
                                if (data != null) {
                                    mListener.onLoginSuccess(data);
                                }
                            } else {
                                ToastUtils.make().setDurationIsLong(false).showCustomView(R.layout.toast_upload_fail);
                                mListener.onLoginFailed(response.getMessage());
                            }
                        }

                    }

                    @Override
                    public void onError(BaseResponse<LoginResponse> response) {
                        dismiss();
                        ToastUtils.make().setDurationIsLong(false).showCustomView(R.layout.toast_upload_fail);
                        if (mListener != null) {
                            mListener.onLoginFailed(response.getMessage());
                        }

                    }
                });

            }
        });
    }

    /**
     * 判断手机号是否符合规范
     * Whether the phone number conforms to the specification
     *
     * @param phone String 输入的手机号 The phone number
     */
    private boolean isPhoneNumber(String phone) {
        if (TextUtils.isEmpty(phone)) {
            return false;
        }
        if (phone.length() == 11) {
            for (int i = 0; i < 11; i++) {
                if (!PhoneNumberUtils.isISODigit(phone.charAt(i))) {
                    return false;
                }
            }
            return true;
        }
        return false;
    }

    /**
     * 改变上传view的状态
     * Change the status of the uploaded view
     */
    private void changeUploadView() {
        Editable account = mEtAccount.getText();
        Editable password = mEtPassword.getText();
        if (account != null && password != null && isPhoneNumber(account.toString()) && !TextUtils.isEmpty(password.toString())) {
            if (!mTvUpload.isEnabled()) {
                mTvUpload.setBackgroundResource(R.drawable.bg_upload_clickable);
                mTvUpload.setEnabled(true);
            }
        } else {
            if (mTvUpload.isEnabled()) {
                mTvUpload.setBackgroundResource(R.drawable.bg_upload_default);
                mTvUpload.setEnabled(false);
            }
        }
    }

    /**
     * 设置事件监听
     * Set event listener
     *
     * @param listener EventListener the listener
     */
    public LoginUploadPop setEventListener(EventListener listener) {
        mListener = listener;
        return this;
    }

    private TextWatcher mTextWatcher = new TextWatcher() {
        @Override
        public void beforeTextChanged(CharSequence s, int start, int count, int after) {

        }

        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {

        }

        @Override
        public void afterTextChanged(Editable s) {
            changeUploadView();
        }
    };

    public interface EventListener {
        /**
         * 登录成功
         * login successfully
         *
         * @param result the login result
         */
        void onLoginSuccess(LoginResponse result);

        /**
         * 登录失败
         * login failure
         *
         * @param error the  error info
         */
        void onLoginFailed(String error);
    }
}
