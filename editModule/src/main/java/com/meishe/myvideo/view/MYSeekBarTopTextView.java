package com.meishe.myvideo.view;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.text.TextPaint;
import android.util.AttributeSet;
import android.widget.SeekBar;

import androidx.appcompat.widget.AppCompatSeekBar;

import com.meishe.base.utils.ThreadUtils;
import com.meishe.myvideo.R;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2021/10/12 15:10
 * @Description :支持动态显示与隐藏进度值的SeekBar，进度值显示在上面 Seekbar that supports dynamic display and hiding，the value is located top.
 * of progress values
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class MYSeekBarTopTextView extends AppCompatSeekBar {
    private static final int DALEY_TIME = 1000;
    private OnSeekBarListener mListener;
    private String mName;
    private long mSeekingTime;
    private boolean mNeedHideText = false;
    private boolean mIsHideText = false;
    private IntToTextFunction mIntToTextFunction;
    private TextPaint mPaint;
    private String mProgressText;
    private Rect mProgressTextRect = new Rect();
    /**
     * 字体距离seekbar把手的距离
     * The distance between the font and the seekbar handle.
     */
    private float mTextMargin;

    public void setIntToTextFunction(IntToTextFunction function) {
        this.mIntToTextFunction = function;
    }

    public MYSeekBarTopTextView(Context context) {
        super(context);
    }


    public MYSeekBarTopTextView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public MYSeekBarTopTextView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    @Override
    protected synchronized void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        Drawable thumb = getThumb();
        setMeasuredDimension(getMeasuredWidth(), (int) (getMeasuredHeight() + thumb.getIntrinsicWidth() + mTextMargin * 2F));
    }

    @Override
    protected synchronized void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        if (mIsHideText || mPaint == null) {
            return;
        }
        Drawable thumb = getThumb();
        Rect bounds = thumb.getBounds();
        mPaint.getTextBounds(mProgressText, 0, mProgressText.length(), mProgressTextRect);
        float thumbX = bounds.left + bounds.width();
        float thumbY = (getHeight() - bounds.height()) / 2F - mTextMargin;
        canvas.drawText(mProgressText, thumbX, thumbY, mPaint);
    }

    private void init() {
        mPaint = new TextPaint();
        mPaint.setAntiAlias(true);
        mPaint.setColor(Color.WHITE);
        mPaint.setTextAlign(Paint.Align.CENTER);
        mPaint.setTextSize(getResources().getDimensionPixelSize(R.dimen.sp_px_30));
        mTextMargin = getResources().getDimensionPixelSize(R.dimen.dp_px_15);
        if (mNeedHideText) {
            mSeekingTime = System.currentTimeMillis();
            ThreadUtils.runOnUiThreadDelayed(() -> {
                if (System.currentTimeMillis() - mSeekingTime >= DALEY_TIME ) {
                    mIsHideText = true;
                    invalidate();
                }
            }, DALEY_TIME);
        }
        initListener();
    }


    public void setNeedHideText(boolean needHideText) {
        this.mNeedHideText = needHideText;
    }

    private void initListener() {
        setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {

                mProgressText = mIntToTextFunction != null? mIntToTextFunction.parseIntToText(progress) : progress + "";
                mIsHideText = false;
                invalidate();
                if (mListener != null) {
                    mListener.onProgressChanged(seekBar, progress, fromUser);
                }
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                if (mListener != null) {
                    mListener.onStartTrackingTouch(seekBar);
                }
                mSeekingTime = System.currentTimeMillis();
                mIsHideText = false;
                invalidate();
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                if (mListener != null) {
                    mListener.onStopTrackingTouch(seekBar.getProgress(), mName);
                }
                if (mNeedHideText) {
                    mSeekingTime = System.currentTimeMillis();
                    ThreadUtils.runOnUiThreadDelayed(() -> {
                        if (System.currentTimeMillis() - mSeekingTime >= DALEY_TIME) {
                            mIsHideText = true;
                            invalidate();
                        }
                    }, DALEY_TIME);
                }
            }
        });
    }

    public void setOnSeekBarChangeListener(OnSeekBarListener listener) {
        this.mListener = listener;
    }

    /**
     * Sets name.
     * 设置名字
     *
     * @param name the name
     */
    public void setName(String name) {
        this.mName = name;
    }

    /**
     * The interface On seek bar listener.
     * 拖动条监听的接口
     */
    public interface OnSeekBarListener {

        /**
         * On stop tracking touch.
         * 停止触摸追踪
         *
         * @param progress the progress 进度
         * @param name     the name 名字
         */
        void onStopTrackingTouch(int progress, String name);

        /**
         * On start tracking touch.
         * 停止开始追踪
         *
         * @param seekBar the seek bar 拖动条
         */
        void onStartTrackingTouch(SeekBar seekBar);

        /**
         * On progress changed.
         * 改变进度
         *
         * @param seekBar  the seek bar 拖动条
         * @param progress the progress 进度
         * @param fromUser the from user 来自用户
         */
        void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser);

    }


    /**
     * Sets seek progress.
     * 设置拖动条进度
     *
     * @param progress the progress 进度
     */
    @Override
    public void setProgress(int progress) {
        super.setProgress(progress);
        mProgressText = mIntToTextFunction != null? mIntToTextFunction.parseIntToText(getProgress()) : getProgress() + "";
        mIsHideText = false;
        invalidate();
        if (mNeedHideText) {
            mSeekingTime = System.currentTimeMillis();
            ThreadUtils.runOnUiThreadDelayed(() -> {
                if (System.currentTimeMillis() - mSeekingTime >= DALEY_TIME ) {
                    mIsHideText = true;
                    invalidate();
                }
            }, DALEY_TIME);
        }
    }

    public interface IntToTextFunction{
        /**
         * Parse int to text string.
         *
         * @param value the value
         * @return the string
         */
        String parseIntToText(int value);
    }
}
