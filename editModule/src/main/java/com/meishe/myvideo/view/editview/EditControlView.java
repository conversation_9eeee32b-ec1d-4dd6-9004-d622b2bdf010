package com.meishe.myvideo.view.editview;

import android.app.AlertDialog;
import android.content.Context;
import androidx.core.content.ContextCompat;
import android.util.AttributeSet;
import android.view.Gravity;
import android.view.View;
import android.view.WindowManager;
import android.widget.RelativeLayout;

import com.meishe.myvideo.R;

/**
 * The type Edit control view.
 * 编辑控件视图类
 */
public abstract class EditControlView extends RelativeLayout {
    protected Context mContext;
    public EditControlView(Context context) {
        super(context);
    }

    public EditControlView(Context context, AttributeSet attrs) {
        super(context, attrs);
        mContext = context;
        initView();
    }

    /**
     * Init view.
     * 初始化视图
     */
    protected abstract void initView();

    /**
     * Dismiss.
     * 消失
     */
    public abstract void dismiss();


    /**
     * 显示对话框窗口
     * Show dialog window
     *
     * @param dialog the dialog 对话框
     * @param view   the view
     */
    public void showDialogView(AlertDialog dialog, View view) {
        dialog.show();
        dialog.setContentView(view);
        dialog.setCanceledOnTouchOutside(true);
        WindowManager.LayoutParams params = dialog.getWindow().getAttributes();
        params.width = WindowManager.LayoutParams.MATCH_PARENT;
        params.height = WindowManager.LayoutParams.WRAP_CONTENT;
        params.flags = WindowManager.LayoutParams.FLAG_DIM_BEHIND;
        dialog.getWindow().setGravity(Gravity.BOTTOM);
        params.dimAmount = 0.0f;
        dialog.getWindow().setAttributes(params);
        dialog.getWindow().setBackgroundDrawable(ContextCompat.getDrawable(getContext(), R.color.colorTranslucent));
        dialog.getWindow().setWindowAnimations(R.style.edit_dlg_style);
    }

}
