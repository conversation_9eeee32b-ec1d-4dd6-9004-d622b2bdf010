package com.meishe.myvideo.view.editview;

import android.annotation.SuppressLint;
import android.content.Context;
import android.util.AttributeSet;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.meishe.base.utils.SizeUtils;
import com.meishe.myvideo.R;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;


/**
 * The type Edit change speed scroll view.
 * 进度拖拽，计算当数据，取消吸附效果
 *
 * <AUTHOR>
 * @date :2020/7/27 14:42
 * @des : 进度拖拽，计算当数据，取消吸附效果
 */
public class EditChangeSpeedScrollView extends LinearLayout {
    private static final String TAG = "EditChangeSpeedScrollView";
    private OnSpeedChangedListener onSpeedChangedListener;
    private LinearLayout ll_data;
    private View view_shadow;
    private LinearLayout view_mask;
    private View view_background;
    /**
     * onTouch 事件使用变量
     * OnTouch Event Usage Variables
     */
    private float downX, downY;
    private float currentX;
    /**
     * The Do click.
     * 判断为去响应点击事件
     */
    boolean doClick = false;
    /**
     * The Do scroll by on touch.
     * shadow是否跟随手指移动
     */
    boolean doScrollByOnTouch = false;

    private final List<SpeedParam> itemDataList = new ArrayList<>();
    /**
     * 节点对应x坐标集合
     * The nodes correspond to the set of x coordinates
     */
    private List<Integer> itemPointX = new ArrayList<>();
    /**
     * 每个节点之间的距离
     * The distance between each node
     */
    private int itemWidth;
    private int marginLeft;
    /**
     * 当前的倍速 The current double speed
     */
    private float currentSpeed;

    private TextView tvSpeed;

    public EditChangeSpeedScrollView(Context context) {
        this(context, null);

    }

    public EditChangeSpeedScrollView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);

    }

    public EditChangeSpeedScrollView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView(context);
        setListener();
    }

    private void initView(Context context) {
        View rootView = LayoutInflater.from(context).inflate(R.layout.layout_custom_speed_params, this);
        setOrientation(VERTICAL);
        ll_data = rootView.findViewById(R.id.data);
        view_shadow = rootView.findViewById(R.id.view_shadow);
        view_mask = rootView.findViewById(R.id.view_mask);
        view_background = rootView.findViewById(R.id.view_background);
        marginLeft = SizeUtils.dp2px(20);
        tvSpeed = rootView.findViewById(R.id.tv_speed);
    }

    @SuppressLint("ClickableViewAccessibility")
    private void setListener() {
    }

    @Override
    public void requestDisallowInterceptTouchEvent(boolean disallowIntercept) {
        super.requestDisallowInterceptTouchEvent(disallowIntercept);
    }
    private float speed;
    @SuppressLint({"LongLogTag", "ClickableViewAccessibility"})
    @Override
    public boolean onTouchEvent(MotionEvent event) {
        int action = event.getAction();
        if (action == MotionEvent.ACTION_DOWN) {
            downX = event.getRawX();
            downY = event.getRawY();
            currentX = downX;
            float shadowX = view_shadow.getX() + marginLeft;
            int width = view_shadow.getWidth();
            //起始点落在shadowView上，随着移动去执行滑动 The starting point lands on the ShadowView, and the slide is performed as it moves
            if (downX > shadowX - 50 && downX < shadowX + width + 50) {
                doScrollByOnTouch = true;
                doClick = false;
            }/*else{
                            //落在其他选项上，随手指抬起去响应点击事件
                            doClick = true;
                            doScrollByOnTouch = false;
                        }*/
//            Log.e(TAG, "onTouch ACTION_DOWN");
        } else if (action == MotionEvent.ACTION_MOVE) {
            //如果手指落下时点击的是滑块范围，需要随手指移动 If the finger falls on the slider range, you need to move the finger
            if (doScrollByOnTouch) {
                float currentMoveX = event.getRawX();
                //滑动shadowView Sliding shadowView
                float shadowCurrentX = view_shadow.getX();
                 speed = itemDataList.get(0).value;
                //计算需要滑动的距离 Calculate the distance to slide
                float targetPositionX = shadowCurrentX + currentMoveX - currentX;
                //最左变得距离是经典按钮左侧位置 The leftmost becomes the distance to the left of the classic button
                if (targetPositionX <= view_background.getLeft()) {
                    targetPositionX = view_background.getLeft();
                    //计算当前变速滑动的位置对应的值 Calculates the value corresponding to the position of the current variable speed slip
                    speed = itemDataList.get(0).value;
//                    Log.e(TAG, "getCurrentSpeedByPosition == " + speed);

                }
                //最右侧的位置 The far right
                else if (targetPositionX > view_background.getRight()) {
                    targetPositionX = view_background.getRight() - view_shadow.getWidth() / 2F;
                    //计算当前变速滑动的位置对应的值 Calculates the value corresponding to the position of the current variable speed slip
                    speed = itemDataList.get(itemDataList.size() - 1).value;
//                    Log.e(TAG, "getCurrentSpeedByPosition == " + speed);

                } else {
                    //计算当前变速滑动的位置对应的值 Calculates the value corresponding to the position of the current variable speed slip
                    speed = getCurrentSpeedByPosition(targetPositionX + view_shadow.getWidth() / 2F);
//                    Log.e(TAG, "getCurrentSpeedByPosition == " + speed);
                }
                BigDecimal b = new BigDecimal(speed);
                float f1 = b.setScale(1, BigDecimal.ROUND_HALF_UP).floatValue();
                tvSpeed.setText(f1 + "X");
                doMoveToTargetPosition((int) targetPositionX);

                currentX = currentMoveX;
            }
//            Log.e(TAG, "onTouch ACTION_MOVE");
        } else if (action == MotionEvent.ACTION_UP) {
            //滑动事件，去操作，手指滑动出当前控件已然响应 Slide event, to operate, finger slide out of the current control has responded
            if (doScrollByOnTouch) {
                //手指抬起时 shadowView 的 x坐标 X coordinate of ShadowView when the finger is raised
                float currentUpX = view_shadow.getX();
                BigDecimal b = new BigDecimal(speed);
                float f1 = b.setScale(1, BigDecimal.ROUND_HALF_UP).floatValue();
                if (null != onSpeedChangedListener) {
                    onSpeedChangedListener.onSpeedChanged(f1);
                }
            }
//            Log.e(TAG, "onTouch ACTION_UP");
            doScrollByOnTouch = false;
        } else if (action == MotionEvent.ACTION_CANCEL) {
//            Log.e(TAG, "onTouch ACTION_CANCEL");
        }
        return true;
    }

    /**
     * 计算当前的变速值
     * Calculate the current speed change
     *
     * @param targetPositionX
     * @return
     */
    private float getCurrentSpeedByPosition(float targetPositionX) {
        //当前位置左侧的节点是第几个节点 The node to the left of the current position is the node
        int leftIndex = (int) (targetPositionX / itemWidth);
        //如果当前节点是最后一个节点 If the current node is the last node
        if (leftIndex == itemDataList.size() - 1) {
            return itemDataList.get(leftIndex).value;
        }
        //获取这个节点对应的速度值 Gets the speed value for this node
        float baseSpeedValue = itemDataList.get(leftIndex).value;
        //当前所在位置相对左节点的距离 The distance between the current position and the left node
        float leftIndexOffsetValue = targetPositionX - itemWidth * leftIndex;
        //获取当前位置相对左节点的数据 Gets the data of the current position relative to the left node
        float rightIndexValue = itemDataList.get(leftIndex + 1).value;
        float valueToLeftIndex = (rightIndexValue - baseSpeedValue) * leftIndexOffsetValue / itemWidth;
        return valueToLeftIndex + baseSpeedValue;
    }


    /**
     * shadow移动到目标位置
     * Shadow moves to the target position
     *
     * @param targetPosition 移动的目标位置x坐标
     */
    private void doMoveToTargetPosition(int targetPosition) {
        if (targetPosition > view_background.getRight() - view_shadow.getWidth() / 2) {
            targetPosition = view_background.getRight() - view_shadow.getWidth() / 2;
        }
        //绘制进度 Draw the progress
        view_shadow.setX(targetPosition);
        //绘制显示当前速度的textview Draws a TextView that shows the current speed
        //最左侧位置向右移动一点，避免显示不全 Move the leftmost position a little to the right to avoid an incomplete display
        if (targetPosition <= view_background.getLeft() + view_shadow.getWidth()) {
            targetPosition = targetPosition + view_shadow.getWidth();
        }
        tvSpeed.setX(targetPosition - view_shadow.getWidth());
    }

    /**
     * Sets selected data.
     * 设置选择数据
     *
     * @param selectedDataList the selected data list
     */
    public void setSelectedData(List<SpeedParam> selectedDataList) {
        if (null != selectedDataList && selectedDataList.size() > 0) {
            this.itemDataList.addAll(selectedDataList);
            itemPointX = new ArrayList<>(itemDataList.size());
            itemWidth = (view_background.getRight() - view_background.getLeft()) / (itemDataList.size() - 1);
            for (int i = 0; i < itemDataList.size(); i++) {
                SpeedParam speedParam = itemDataList.get(i);
                //添加对应的节点 Add the corresponding node
                itemPointX.add(i * itemWidth);

                //添加view显示数据 Add a view to display data
                TextView textView = new TextView(getContext());
                textView.setTextColor(getResources().getColor(R.color.white));
                textView.setTextSize(10);
                textView.setText(speedParam.showValue);

                //添加节点对应的小图标 Add a small icon for the node
                View view = new View(getContext());
                int viewWidth = SizeUtils.dp2px(8);
                view.setLayoutParams(new LinearLayout.LayoutParams(viewWidth, viewWidth));
                int viewX = 0;
                if (i == 0) {
                    textView.setLayoutParams(new LinearLayout.LayoutParams(itemWidth / 2, LinearLayout.LayoutParams.WRAP_CONTENT));
                    textView.setGravity(Gravity.LEFT);
                    viewX = 0;
                } else if (i == itemDataList.size() - 1) {
                    textView.setLayoutParams(new LinearLayout.LayoutParams(itemWidth / 2, LinearLayout.LayoutParams.WRAP_CONTENT));
                    textView.setGravity(Gravity.RIGHT);
                    viewX = itemWidth * i - viewWidth * i;
                } else {
                    textView.setLayoutParams(new LinearLayout.LayoutParams(itemWidth, LinearLayout.LayoutParams.WRAP_CONTENT));
                    textView.setGravity(Gravity.CENTER);
                    viewX = itemWidth * i - viewWidth * i - viewWidth / 2;
                }
                ll_data.addView(textView);


                //设置view的位置 Set the location of the View
                view.setX(viewX);
                view.setBackground(getResources().getDrawable(R.drawable.nv_compile_progress));
                view_mask.addView(view);
            }

            //默认移动到初始化的推荐位置 , 如果当前选择的有值，设置到这个值得位置 Moves to the recommended location for initialization by default, or to the worth location if the current selection has a value
            setCurrentSpeedPosition(currentSpeed);

        }
    }

    /**
     * 根据传入的倍速 设置 位置
     * Sets the location based on the passed doubling speed
     *
     * @param currentSpeed
     */
    private void setCurrentSpeedPosition(float currentSpeed) {
        int targetLeftIndex = 0;
        //找到所在位置之后计算x值 You find the location and you calculate the x value
        float targetX = 0;
        if (null != itemDataList && itemDataList.size() > 0) {
            //第一个节点和最后一个节点 The first node and the last node
            if (currentSpeed == itemDataList.get(0).value) {
                targetX = 0;
            } else if (currentSpeed == itemDataList.get(itemDataList.size() - 1).value) {
                targetX = view_background.getRight();
            } else {
                //中间节点 intermediate node
                for (int i = 1; i < itemDataList.size() - 1; i++) {
                    //如果不是第0个节 则从第一个节点开始比对，获取当前变速值所在的区间 的左节点是哪一个 If it is not the 0th node, the comparison starts from the first node and obtains the left node of the interval where the current variable speed value is located
                    float indexValue = itemDataList.get(i).value;

                    if (currentSpeed <= indexValue) {
                        targetLeftIndex = i - 1;
                        break;
                    }
                }
                //当前节点区间左右节点对应的值 The value of the left and right nodes of the current node interval
                float baseSpeedIndexValue = itemDataList.get(targetLeftIndex).value;
                float baseSpeedNextIndexValue = itemDataList.get(targetLeftIndex + 1).value;
                targetX = (currentSpeed - baseSpeedIndexValue) / (baseSpeedNextIndexValue - baseSpeedIndexValue) * itemWidth + itemWidth * targetLeftIndex;
            }
        }
        //这里计算出来的targetX是左边x，处理一下按拖拽按钮显示居中 So the targetX calculated here is the X on the left, and I'm going to do that by pressing the drag and drop button
        doMoveToTargetPosition(Math.max((int)( targetX - view_shadow.getWidth() / 2),0));
    }

    /**
     * Sets on speed changed listener.
     * 设置速度更改监听器
     *
     * @param onSpeedChangedListener the on speed changed listener 速度更改监听器
     */
    public void setOnSpeedChangedListener(OnSpeedChangedListener onSpeedChangedListener) {
        this.onSpeedChangedListener = onSpeedChangedListener;
    }

    /**
     * Sets current speed.
     * 设置当前的速度
     *
     * @param speed the speed 当前的速度
     */
    public void setCurrentSpeed(float speed) {
        this.currentSpeed = speed;
    }


    /**
     * The interface On speed changed listener.
     * 速度改变侦听器的接口
     */
    public interface OnSpeedChangedListener {
        /**
         * On speed changed.
         * 速度变化
         *
         * @param speed the speed  速度
         */
        void onSpeedChanged(float speed);
    }

    /**
     * The type Speed param.
     * 速度参数类
     */
    public static class SpeedParam {
        /**
         * The Value.
         * 值
         */
        public float value;
        /**
         * The Show value.
         * 显示值
         */
        public String showValue;

        public SpeedParam(float value) {
            this.value = value;
            this.showValue = value + "X";
        }

    }
}
