package com.meishe.myvideo.view.editview;

import android.content.Context;
import android.os.Build;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.RequiresApi;

import com.meishe.base.utils.Utils;
import com.meishe.base.view.NvPlugBezierView;
import com.meishe.myvideo.R;
import com.meishe.myvideo.interfaces.BottomEventListener;

import java.util.List;

/**
 * The type Edit atomic key frame curve view.
 * 编辑原子特技关键帧视图类
 */
public class EditAtomicCurveView extends RelativeLayout {
    /**
     * 页面使用到的view
     * used view in page
     */
    private TextView mTvChangePoint, mTvReset;
    private View mVPointMask;
    private ImageView mIvChangePoint, mIvConfirm;
    private NvPlugBezierView mBezierView;
    private int currSelected = -1;

    private final boolean mIsPlaying = true;
    private AtomicCurveListener mEventListener;

    public EditAtomicCurveView(Context context) {
        this(context, null);
    }

    public EditAtomicCurveView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public EditAtomicCurveView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView();
        initListener();
    }

    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    public EditAtomicCurveView(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        initView();
        initListener();
    }

    /**
     * Init view.
     * 初始化视图
     */
    protected void initView() {
        setClickable(true);
        setBackgroundColor(getResources().getColor(R.color.black));
        View rootView = LayoutInflater.from(getContext()).inflate(R.layout.view_edit_atomic_curve, this);
        mBezierView = rootView.findViewById(R.id.speed_view);
        mTvChangePoint = rootView.findViewById(R.id.tv_point);
        mIvChangePoint = rootView.findViewById(R.id.iv_point);
        mVPointMask = rootView.findViewById(R.id.v_point_mask);
        mTvReset = rootView.findViewById(R.id.tv_reset);
        mIvConfirm = rootView.findViewById(R.id.iv_confirm);

    }


    /**
     * Init listener.
     * 初始化监听
     */
    protected void initListener() {
        mTvChangePoint.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (Utils.isFastClick()) {
                    return;
                }
                mBezierView.addOrDeletePoint(currSelected);
            }
        });

        mIvConfirm.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mEventListener != null) {
                    mEventListener.onDismiss(true);
                }
            }
        });

        mTvReset.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                mBezierView.reset();
            }
        });

        mBezierView.setOnBezierListener(new NvPlugBezierView.OnBezierListener() {
            @Override
            public void startPlay(String speed) {
                if (!mIsPlaying || mEventListener == null) {
                    return;
                }
                mEventListener.onCurveChanged(speed);
            }

            @Override
            public void onSelectedPoint(int position) {
                currSelected = position;
                List<NvPlugBezierView.BzPoint> list = mBezierView.getList();
                if (position != -1) {
                    if (position == 0 || position == list.size() - 1) {
                        mVPointMask.setVisibility(VISIBLE);
                        mTvChangePoint.setClickable(false);
                    } else {
                        mVPointMask.setVisibility(GONE);
                        mTvChangePoint.setClickable(true);
                    }
                    mTvChangePoint.setText(getResources().getString(R.string.tv_point_remove));
                    mIvChangePoint.setImageResource(R.mipmap.icon_remove_point);
                } else {
                    mTvChangePoint.setClickable(true);
                    mVPointMask.setVisibility(GONE);
                    mTvChangePoint.setText(getResources().getString(R.string.tv_point_add));
                    mIvChangePoint.setImageResource(R.mipmap.icon_add_point);
                }
            }

            @Override
            public void seekPosition(long position) {

            }
        });
    }

    /**
     * 设置事件监听
     * Sets the listener
     *
     * @param listener the listener 监听
     */
    public void setListener(AtomicCurveListener listener) {
        mEventListener = listener;
    }

    /**
     * 更新视图view
     * Update the view
     *
     * @param curveSpeed the speed cure view speed 观察速度
     */
    public void updateView(String curveSpeed) {
        mBezierView.setDuring(1);
        if ((!TextUtils.isEmpty(curveSpeed))) {
            mBezierView.setSpeedPoint(curveSpeed);
        }
    }


    /**
     * The type  curve listener.
     * 曲线监听器
     */
    public static abstract class AtomicCurveListener extends BottomEventListener {
        /**
         * On start play.
         * 开始播放
         *
         * @param curve the curve 曲线
         */
        public abstract void onCurveChanged(String curve);

    }
}
