package com.meishe.myvideo.view.interf;

import com.meishe.engine.interf.IBaseInfo;

import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 * 调整视图合同类
 * Adjust the view contract class
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2020/12/28 15:16
 * @Description :
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public interface AdjustViewContract {

    /**
     * The interface Mvp view.
     * mvp 视图的接口
     */
    interface MvpView extends BaseContract.MvpView {
    }

    /**
     * The interface Presenter.
     * Presenter接口
     *
     * @param <V> the type parameter
     */
    interface Presenter<V> extends BaseContract.Presenter<V> {
        /**
         * On item clicked.
         * 条目点击
         *
         * @param info       the info 信息
         * @param isSelected the is selected 选择
         */
        void onItemClicked(IBaseInfo info, boolean isSelected);

        /**
         * On progress changed.
         * 改变进度
         *
         * @param progress   the progress 进度
         * @param tag        the tag 标签
         * @param isFromUser the is from user 来自用户
         */
        void onProgressChanged(float progress, String tag, boolean isFromUser);

        /**
         * On stop tracking touch.
         * 停止跟踪联系
         */
        void onStopTrackingTouch();

        /**
         * Reset.
         * 重置
         *
         * @param data the data 信息
         */
        void reset(List<IBaseInfo> data);

        /**
         * Confirm.
         * 确认
         */
        void confirm();

        /**
         * Apply to all.
         * 全部应用
         */
        void applyToAll();
    }
}
