package com.meishe.myvideo.view;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.meicam.sdk.NvsStreamingContext;
import com.meishe.myvideo.R;
import com.meishe.myvideo.interfaces.OnMiddleOperationClickListener;

/**
 * 中间的横向操作菜单
 * 播放、暂停、撤销、恢复、全屏操作台
 * Horizontal operation menu in the middle
 * Play, pause, undo, restore, full screen operating platform
 */

public class MYMiddleOperationView extends RelativeLayout implements View.OnClickListener {

    private TextView mTimeRatioText;

    private OnMiddleOperationClickListener onMiddleOperationClickListener;
    private ImageView mPlayBtn;
    private ImageView ivRecover;
    private ImageView ivCancel;
    private ImageView mIvOperationZoom;
    private ImageView mIvAddKeyFrame;
    private ImageView mIvAddKeyFrameCurve;
    private boolean addKeyFrame;
    public MYMiddleOperationView(Context context) {
        super(context);
        initView(context);
    }

    public MYMiddleOperationView(Context context, AttributeSet attrs) {
        super(context, attrs);
        initView(context);
    }

    public MYMiddleOperationView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView(context);
    }

    private void initView(Context context) {
        View view = LayoutInflater.from(context).inflate(R.layout.view_middle_operation, this);
        mTimeRatioText = view.findViewById(R.id.tv_operate_time);
        mPlayBtn = view.findViewById(R.id.iv_operation_play);
        ivRecover = view.findViewById(R.id.iv_operation_recover);
        ivCancel = view.findViewById(R.id.iv_operation_cancel);
        mIvOperationZoom = view.findViewById(R.id.iv_operation_zoom);
        mIvAddKeyFrame = view.findViewById(R.id.iv_add_key_frame);
        mIvAddKeyFrameCurve = view.findViewById(R.id.iv_add_key_frame_curve);

        mPlayBtn.setOnClickListener(this);
        ivRecover.setOnClickListener(this);
        ivCancel.setOnClickListener(this);
        mIvAddKeyFrame.setOnClickListener(this);
        mIvOperationZoom.setOnClickListener(this);
        mIvAddKeyFrameCurve.setOnClickListener(this);
    }

    @Override
    public void onClick(View v) {
        if (onMiddleOperationClickListener == null) {
            return;
        }
        int id = v.getId();
        if (id == R.id.iv_operation_play) {
            int state = NvsStreamingContext.getInstance().getStreamingEngineState();
            if (state == NvsStreamingContext.STREAMING_ENGINE_STATE_PLAYBACK) {
                onMiddleOperationClickListener.onPlayEventCallback(true);
            } else {
                onMiddleOperationClickListener.onPlayEventCallback(false);
            }
        } else if (id == R.id.iv_operation_cancel) {
            onMiddleOperationClickListener.onCancelEventCallback();
        } else if (id == R.id.iv_operation_recover) {
            onMiddleOperationClickListener.onRecoverEventCallback();
        } else if (id == R.id.iv_operation_zoom) {
            onMiddleOperationClickListener.onZoomEventCallback();
        } else if (id == R.id.iv_add_key_frame) {
            if (onMiddleOperationClickListener.onKeyFrameClick(addKeyFrame)) {
                addKeyFrame = !addKeyFrame;
                showKeyFrameView(addKeyFrame);
                onMiddleOperationClickListener.onPostKeyFrameClick();
            }
        } else if (id == R.id.iv_add_key_frame_curve) {
            if (mIvAddKeyFrameCurve.isEnabled()) {
                onMiddleOperationClickListener.onKeyFrameCurveClick();
            }
        }
    }

    public void setOnMiddleOperationClickListener(OnMiddleOperationClickListener listener) {
        this.onMiddleOperationClickListener = listener;
    }

    public void setDurationText(String time) {
        if (mTimeRatioText != null) {
            mTimeRatioText.setText(time);
        }
    }

    public void updateViewState(boolean isPlaying) {
        if (isPlaying) {
            mPlayBtn.setImageResource(R.mipmap.control_bar_ic_pause);
        } else {
            mPlayBtn.setImageResource(R.mipmap.control_bar_ic_play);
        }
    }

    public void updateRecoverState(boolean canAble) {
        if (canAble) {
            ivRecover.setImageResource(R.mipmap.ic_operate_recover);
        } else {
            ivRecover.setImageResource(R.mipmap.ic_operate_recover_enable);
        }
    }

    public void updateCancelState(boolean canAble) {
        if (canAble) {
            ivCancel.setImageResource(R.mipmap.ic_operate_cancel);
        } else {
            ivCancel.setImageResource(R.mipmap.ic_operate_cancel_enable);
        }
    }

    public void updateCancelRecoverVisible(boolean isVisible) {
        ivRecover.setVisibility(isVisible ? VISIBLE : INVISIBLE);
        ivCancel.setVisibility(isVisible ? VISIBLE : INVISIBLE);
    }

    public void updateImageVisible(boolean isVisible) {
        ivRecover.setVisibility(isVisible ? VISIBLE : INVISIBLE);
        ivCancel.setVisibility(isVisible ? VISIBLE : INVISIBLE);
        mIvOperationZoom.setVisibility(isVisible ? VISIBLE : INVISIBLE);
        mTimeRatioText.setVisibility(isVisible ? VISIBLE : INVISIBLE);
    }

    public void showKeyFrameView(boolean add) {
        addKeyFrame = add;
        showKeyFrameView();
        mIvAddKeyFrame.setImageResource(add ? R.mipmap.icon_add_key_frame : R.mipmap.icon_delete_key_frame);
    }

    public void changeKeyFrameCurveState(boolean select) {
        mIvAddKeyFrameCurve.setImageResource(select ? R.mipmap.icon_add_key_frame_curve_select : R.mipmap.icon_add_key_frame_curve_unselect);
        mIvAddKeyFrameCurve.setEnabled(select);
    }

    public void notShowKeyFrameView() {
        mIvAddKeyFrame.setVisibility(GONE);
        notShowKeyFrameCurveView();
    }

    public void notShowKeyFrameCurveView() {
        mIvAddKeyFrameCurve.setVisibility(GONE);
    }

    public void showKeyFrameView() {
        mIvAddKeyFrame.setVisibility(VISIBLE);
        mIvAddKeyFrameCurve.setVisibility(VISIBLE);
    }

    public void changeOperateViewState(int visibility) {
        ivCancel.setVisibility(visibility);
        ivRecover.setVisibility(visibility);
        mIvAddKeyFrame.setVisibility(visibility);
        mIvAddKeyFrameCurve.setVisibility(visibility);
    }
}
