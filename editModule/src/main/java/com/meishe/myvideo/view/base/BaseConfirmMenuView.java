package com.meishe.myvideo.view.base;

import android.content.Context;
import android.graphics.Rect;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.TouchDelegate;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.SeekBar;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.meishe.base.manager.LinearLayoutManagerWrapper;
import com.meishe.base.utils.SizeUtils;
import com.meishe.base.view.decoration.ItemDecoration;
import com.meishe.business.assets.view.AssetsTypeTabView;
import com.meishe.engine.interf.IBaseInfo;
import com.meishe.myvideo.R;
import com.meishe.myvideo.adapter.BaseSelectAdapter;
import com.meishe.myvideo.interfaces.BottomEventListener;
import com.meishe.myvideo.view.CommonConfirmDialog;
import com.meishe.myvideo.view.MYSeekBarTextView;
import com.meishe.myvideo.view.interf.AdjustViewContract;
import com.meishe.myvideo.view.presenter.BaseConfirmPresenter;
import com.meishe.third.adpater.BaseQuickAdapter;

import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2020/12/25 14:04
 * @Description :带确定和应用到全部的菜单基类, Base class if  menu with confirm and apply to all buttons
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public abstract class BaseConfirmMenuView extends RelativeLayout implements AdjustViewContract.MvpView, View.OnClickListener {
    protected RecyclerView mRecyclerView;
    private ImageView mIvConfirm;
    protected BaseSelectAdapter<IBaseInfo> mAdapter;
    private TextView mTvReset;
    private MYSeekBarTextView mSeekBarView;
    private IBaseInfo mBaseInfo;
    protected BaseConfirmPresenter<? extends BaseConfirmMenuView> mPresenter;
    private View mTopView;
    private BottomEventListener mEventListener;
    private View mResetLeftLine;
    private View mIvApplyToAllView;
    private View mTvApplyToAllView;
    protected boolean mNeedShowApply;
    protected boolean needShowSeekBar = true;
    protected AssetsTypeTabView mAssetsTypeTab;
    protected TextView mHintTextView;
    protected TextView mTvSeekBarDesc;
    protected int mCurrentSubType;

    public BaseConfirmMenuView(Context context) {
        this(context, null);
    }

    public BaseConfirmMenuView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public BaseConfirmMenuView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView();
        initData();
        initListener();
    }

    public void setNeedShowApply(boolean needShowApply) {
        this.mNeedShowApply = needShowApply;
        setApplyAble();
    }

    /**
     * Sets need show seekbar.
     * <p>
     * 需要展示seekBar
     *
     * @param needShowSeekBar the need show seekbar
     */
    public void setNeedShowSeekBar(boolean needShowSeekBar) {
        this.needShowSeekBar = needShowSeekBar;
        if (!this.needShowSeekBar) {
            mTopView.setVisibility(GONE);
        }
    }

    public void setEventListener(BottomEventListener listener) {
        this.mEventListener = listener;
    }

    @Override
    public void initData() {
        mPresenter.getData(getContext());
    }

    public void initRecyclerView() {
        mAdapter = getAdapter();
        LinearLayoutManager layoutManager = new LinearLayoutManagerWrapper(getContext(), LinearLayoutManager.HORIZONTAL, false);
        mRecyclerView.setLayoutManager(layoutManager);
        mRecyclerView.setAdapter(mAdapter);
        mRecyclerView.addItemDecoration(new ItemDecoration(7, 7));
    }

    public abstract BaseSelectAdapter<IBaseInfo> getAdapter();

    public void initListener() {
        mIvConfirm.setOnClickListener(this);
        if (mTvReset != null) {
            mTvReset.setOnClickListener(this);
        }
        if (mIvApplyToAllView != null) {
            mIvApplyToAllView.setOnClickListener(this);
        }
        mTvApplyToAllView.setOnClickListener(this);
        mAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                mBaseInfo = mAdapter.getItem(position);
                int oldSelection = mAdapter.getSelectPosition();
                setSelection(position);
                if (mBaseInfo != null) {
                    mSeekBarView.setName(mBaseInfo.getName());
                    onItemClicked(mBaseInfo, oldSelection == position);
                }
            }
        });

        mSeekBarView.setOnSeekBarChangeListener(new MYSeekBarTextView.OnSeekBarListener() {
            @Override
            public void onStopTrackingTouch(int progress, String name) {
                if (mBaseInfo != null) {
                    mPresenter.onStopTrackingTouch();
                    mBaseInfo.setEffectStrength(progress);
                }
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {

            }

            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                if (mBaseInfo == null && mAdapter != null && mAdapter.getSelectPosition() != -1) {
                    mBaseInfo = mAdapter.getItem(mAdapter.getSelectPosition());
                }
                if (mBaseInfo != null) {
                    mPresenter.onProgressChanged(progress, mBaseInfo.getEffectId(), fromUser);
                }
            }
        });
        mAssetsTypeTab.setItemClickedListener(new AssetsTypeTabView.ItemClickedListener() {
            @Override
            public void onItemClicked(int position) {
                mCurrentSubType = position;
                mPresenter.updateData(position, false);
            }
        });
    }

    protected abstract void onItemClicked(IBaseInfo baseInfo, boolean isSelected);


    @Override
    public void setProgress(float progress) {
        showSeekBar();
        mSeekBarView.setProgress((int) (progress * (mSeekBarView.getMaxProgress() - mSeekBarView.getMinProgress())));
    }

    @Override
    public void onDataBack(List<IBaseInfo> list, int index) {
        if (!isShown()) {
            return;
        }
        mAdapter.setNewData(list);
    }

    @Override
    public void onDataChanged(IBaseInfo baseInfo) {
        if (!isShown()) {
            return;
        }
        mAdapter.addData(baseInfo);
    }

    @Override
    public void updateSelectPosition(int selection) {
        if (!isShown()) {
            return;
        }
        mBaseInfo = mAdapter.getItem(selection);
        mAdapter.setSelectPosition(selection);
        if (selection == 0) {
            //滤镜选无隐藏滑竿
            // Filter selection without hidden slide rod
            hideSeekBar();
            mRecyclerView.scrollToPosition(0);
        } else {
            mRecyclerView.scrollToPosition(selection);
        }
    }


    public IBaseInfo getSelectedItem() {
        if (mAdapter == null) {
            return null;
        }
        return mAdapter.getItem(mAdapter.getSelectPosition());
    }

    public void setSeekPress(IBaseInfo baseInfo) {
        mSeekBarView.setProgress((int) baseInfo.getEffectStrength());
    }

    private void hide() {
        if (mEventListener != null) {
            mEventListener.onDismiss(true);
        }
    }

    protected void showSeekBar() {
        mTopView.setVisibility(VISIBLE);
    }

    protected void hideSeekBar() {
        mTopView.setVisibility(GONE);
    }

    protected void goneSeekBar() {
        mTopView.setVisibility(GONE);
    }

    public void setSelection(int selection) {
        mAdapter.setSelectPosition(selection);
    }

    protected void setResetAble(boolean resetAble) {
        int visible = resetAble ? View.VISIBLE : View.GONE;
        if (mTvReset != null) {
            mTvReset.setVisibility(visible);
        }
        if (mResetLeftLine != null) {
            mResetLeftLine.setVisibility(visible);
        }
    }

    /**
     * 设置滑杆描述文字
     */
    protected void setSeekBarDesc(int descID) {
        if (mTvSeekBarDesc != null) {
            mTvSeekBarDesc.setVisibility(VISIBLE);
            mTvSeekBarDesc.setText(descID);
        }
    }

    private void setApplyAble() {
        int visible = mNeedShowApply ? View.VISIBLE : View.GONE;
        if (mResetLeftLine != null) {
            mResetLeftLine.setVisibility(visible);
        }
        if (mIvApplyToAllView != null) {
            mIvApplyToAllView.setVisibility(visible);
        }
        mTvApplyToAllView.setVisibility(visible);
    }

    @Override
    public void initView() {
        View view = LayoutInflater.from(getContext()).inflate(getItemLayoutResId() == 0 ? R.layout.view_menu_base_confirm : getItemLayoutResId(), this);
        mRecyclerView = view.findViewById(R.id.width_confirm_menu_recycleView);
        mTvSeekBarDesc = view.findViewById(R.id.tv_seekbar_desc);
        TextView content = view.findViewById(R.id.tv_content);
        setContentText(content);
        mIvConfirm = view.findViewById(R.id.iv_confirm);
        mTvReset = view.findViewById(R.id.tv_reset);
        if (mTvReset != null) {
            setTouchDelegate(mTvReset, SizeUtils.dp2px(10F));
        }
        mSeekBarView = view.findViewById(R.id.view_seek_bar);
        mTopView = view.findViewById(R.id.rl_top_view);
        mSeekBarView.setStartTextVisible(false);
        mSeekBarView.setEndTextVisible(false);
        mResetLeftLine = view.findViewById(R.id.reset_left_line);
        mIvApplyToAllView = view.findViewById(R.id.iv_apply_all);
        mTvApplyToAllView = view.findViewById(R.id.tv_apply_all);
        mAssetsTypeTab = view.findViewById(R.id.ttv_tab_type);
        mHintTextView = view.findViewById(R.id.tv_hint);
        setTouchDelegate(mTvApplyToAllView, SizeUtils.dp2px(10F));
        initRecyclerView();
        setApplyAble();
        mPresenter = getPresenter();
    }

    protected abstract void setContentText(TextView textView);

    protected abstract BaseConfirmPresenter<? extends BaseConfirmMenuView> getPresenter();

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.tv_reset) {
            if (mBaseInfo == null) {
                return;
            }
            CommonConfirmDialog dialog = new CommonConfirmDialog(getContext(), R.style.dialog);
            dialog.setOnConfirmClickListener(new CommonConfirmDialog.OnConfirmClickListener() {
                @Override
                public void onButtonConfirmClick() {
                    mPresenter.reset(mAdapter.getData());
                    mTopView.setVisibility(GONE);
                    setSelection(-1);
//                    if (getResources().getString(R.string.adjust_black_point).equals(mBaseInfo.getName()) ||
//                            getResources().getString(R.string.adjust_degree).equals(mBaseInfo.getName()) ||
//                            getResources().getString(R.string.adjust_amount).equals(mBaseInfo.getName())) {
//                        mSeekBarView.setSeekProgress(0);
//                    } else {
//                        mSeekBarView.setSeekProgress(50);
//                    }
                }
            });
            dialog.show();
        } else if (id == R.id.iv_confirm) {
            hide();
            mPresenter.confirm();
        } else if (id == R.id.iv_apply_all || id == R.id.tv_apply_all) {
            mPresenter.applyToAll();
        }
    }

    public static void setTouchDelegate(final View view, final int expandTouchWidth) {
        final View parentView = (View) view.getParent();
        parentView.post(new Runnable() {
            @Override
            public void run() {
                final Rect rect = new Rect();
                view.getHitRect(rect);
                rect.top -= expandTouchWidth;
                rect.bottom += expandTouchWidth;
                rect.left -= expandTouchWidth;
                rect.right += expandTouchWidth;
                parentView.setTouchDelegate(new TouchDelegate(rect, view));
            }
        });
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        mPresenter.detach();
    }

    protected int getItemLayoutResId() {
        return 0;
    }
}
