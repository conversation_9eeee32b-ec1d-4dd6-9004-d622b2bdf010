package com.meishe.myvideo.adapter;

import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.meishe.base.bean.MediaData;
import com.meishe.base.utils.AndroidVersionUtils;
import com.meishe.base.utils.FormatUtils;
import com.meishe.base.utils.ImageLoader;
import com.meishe.myvideo.R;
import com.meishe.myvideo.bean.MeidaClip;
import com.meishe.third.adpater.BaseQuickAdapter;
import com.meishe.third.adpater.BaseViewHolder;

/**
 * All rights Reserved, Designed By www.meishesdk.com
 *
 * @Author: ChuChenGuang
 * @CreateDate: 2022/10/31 14:18
 * @Description: clip 选中的视频媒体适配器 The media selected fill adapter
 * @Copyright: www.meishesdk.com Inc. All rights reserved.
 */

public class MediaSelectedFillAdapter extends BaseQuickAdapter<MeidaClip, BaseViewHolder> {
    private int mSelectedPos;
    private final ImageLoader.Options mRoundCornerOptions;

    public MediaSelectedFillAdapter() {
        super(R.layout.item_media_fill_selected);
        mRoundCornerOptions = new ImageLoader
                .Options()
                .roundedCornersSmall(10, true);
    }

    @NonNull
    @Override
    public BaseViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        BaseViewHolder holder = super.onCreateViewHolder(parent, viewType);
        holder.addOnClickListener(R.id.iv_delete);
        return holder;
    }

    /**
     * 删除对应位置的有效片段
     * Deletes a valid fragment for the corresponding location
     *
     * @param position the position 位置
     */
    public void deleteClip(int position) {
        MeidaClip item = getItem(position);
        if (item != null && !TextUtils.isEmpty(item.getFilePath())) {
            int tempPos = mSelectedPos;
            if (tempPos >= 0) {
                mSelectedPos = -1;
                notifyItemChanged(tempPos);
            }
            item.setFilePath("");
            notifyItemChanged(position);
            mSelectedPos = findSelectedPosition();
            if (position != mSelectedPos && mSelectedPos >= 0) {
                notifyItemChanged(mSelectedPos);
            }
        }

    }

    /**
     * 设置自动选中位置的有效片段
     * Sets a valid fragment of the automatically selected location
     *
     * @param mediaData the media data 媒体数据
     * @param position the position
     */
    public void setSelected(MediaData mediaData, int position) {
        if (position >= 0) {
            MeidaClip item = getItem(position);
            if (item != null) {
                item.setFilePath(mediaData.getThumbPath());
            }
            notifyItemChanged(position);
        }
        mSelectedPos = position;
    }

    /**
     * 找到自动选中的位置(自动选中靠前的没有设置有效片段的item)
     * Find the automatically selected position (automatically select the item at the front that does not have a valid section set)
     */
    private int findSelectedPosition() {
        int position = -1;
        for (MeidaClip clip : getData()) {
            position++;
            if (TextUtils.isEmpty(clip.getFilePath())) {
                return position;
            }
        }
        return -1;
    }

    public int getSelectedPosition() {
        return findSelectedPosition();
    }

    /**
     * 列表中是否还有相同的媒体文件
     * Is the same media file still in the list
     *
     * @param filePath the file path 文件路径
     * @return the boolean
     */
    public boolean isDifferentMedia(String filePath) {
        if (!TextUtils.isEmpty(filePath)) {
            for (MeidaClip clip : getData()) {
                if (filePath.equals(clip.getFilePath())) {
                    return false;
                }
            }
        }
        return true;
    }

    @Override
    protected void convert(@NonNull BaseViewHolder holder, MeidaClip templateClip) {
        ImageView cover = holder.getView(R.id.iv_cover);
        TextView tvDuration = holder.getView(R.id.tv_duration);
        ImageView ivDelete = holder.getView(R.id.iv_delete);
        int position = holder.getAdapterPosition();
        String path = templateClip.getFilePath();
        if (position == mSelectedPos) {
            cover.setBackgroundResource(R.drawable.bg_rectangle_round_stroke_red_365e);
        } else {
            cover.setBackgroundResource(0);
        }
        if (TextUtils.isEmpty(path)) {
            tvDuration.setTextColor(mContext.getResources().getColor(R.color.black));
            if (ivDelete.getVisibility() == View.VISIBLE) {
                ivDelete.setVisibility(View.INVISIBLE);
            }
            cover.setImageResource(R.drawable.bg_rectangle_round_white_ffffff);
        } else {
            String filePath = AndroidVersionUtils.isAboveAndroid_Q() ? templateClip.getFilePath() : "file://" + templateClip.getFilePath();
            ImageLoader.loadUrl(mContext, filePath, cover, mRoundCornerOptions);
            tvDuration.setTextColor(mContext.getResources().getColor(R.color.white));
            if (ivDelete.getVisibility() != View.VISIBLE) {
                ivDelete.setVisibility(View.VISIBLE);
            }
        }
        tvDuration.setText(formatDuration(templateClip.getDuration()));
        holder.setText(R.id.tv_index, (holder.getAdapterPosition() + 1) + "");
    }


    private String formatDuration(long duration) {
        return FormatUtils.objectFormat2String(duration / 1000000f) + "s";
    }
}
