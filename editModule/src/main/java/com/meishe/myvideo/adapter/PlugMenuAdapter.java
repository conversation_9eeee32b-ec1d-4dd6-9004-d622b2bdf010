package com.meishe.myvideo.adapter;

import android.content.Context;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.meishe.base.constants.Constants;
import com.meishe.myvideo.R;
import com.meishe.engine.bean.PlugDetail;
import com.meishe.third.adpater.BaseViewHolder;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2020/12/30 16:19
 * @Description :插件特效 Adapter Adapter for speed curve.
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class PlugMenuAdapter extends BaseSelectAdapter<PlugDetail.Param> {
    private final Context mContext;

    public PlugMenuAdapter(Context context) {
        super(R.layout.view_recy_menu_plug);
        mContext = context;
    }

    @Override
    protected void convert(@NonNull BaseViewHolder helper, PlugDetail.Param item) {
        ImageView icon = helper.getView(R.id.icon);
        TextView name = helper.getView(R.id.name);

        name.setText(item.getName());
        int position = helper.getAdapterPosition();
        String valueType = item.valueType;
        String valueDefault = item.valueDefault;
        if (Constants.PlugType.BOOL.equals(valueType)) {
            boolean defaultValue = Boolean.parseBoolean(valueDefault);
            if (defaultValue) {
                icon.setImageResource(item.getResId(item.coverPath + "_selected"));
            } else {
                icon.setImageResource(item.getResId(item.coverPath));
            }
            name.setTextColor(mContext.getResources().getColor(R.color.white_8));
        } else {
            boolean isSelect = getSelectPosition() == position;
            if (isSelect) {
                icon.setImageResource(item.getResId(item.coverPath + "_selected"));
                name.setTextColor(mContext.getResources().getColor(R.color.color_ffff365E));
            } else {
                icon.setImageResource(item.getResId(item.coverPath));
                name.setTextColor(mContext.getResources().getColor(R.color.white_8));
            }
        }

    }
}
