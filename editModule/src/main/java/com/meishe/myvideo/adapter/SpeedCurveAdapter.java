package com.meishe.myvideo.adapter;

import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.meishe.engine.interf.IBaseInfo;
import com.meishe.myvideo.R;
import com.meishe.third.adpater.BaseViewHolder;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2020/12/30 16:19
 * @Description :曲线变速Adapter Adapter for speed curve.
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class SpeedCurveAdapter extends BaseSelectAdapter<IBaseInfo> {

    public SpeedCurveAdapter() {
        super(R.layout.view_change_speed_curve_item);
    }

    @Override
    protected void convert(@NonNull BaseViewHolder helper, IBaseInfo item) {
        ImageView icon = helper.getView(R.id.icon);
        TextView name = helper.getView(R.id.name);
        View ivSelectBg = helper.getView(R.id.fl_select_bg);
        icon.setImageResource(item.getCoverId());
        name.setText(item.getName());
        int position = helper.getAdapterPosition();
        boolean isSelect = getSelectPosition() == position;
        if (position == 0) {
            ivSelectBg.setVisibility(View.INVISIBLE);
            if (isSelect) {
                icon.setImageResource(R.mipmap.icon_original_selected);
            }
        } else {
            if (isSelect) {
                ivSelectBg.setBackgroundResource(R.drawable.bg_round_corners_solid_red_80fc);
                ivSelectBg.setVisibility(View.VISIBLE);
            } else {
                ivSelectBg.setVisibility(View.INVISIBLE);
                ivSelectBg.setBackgroundResource(R.color.colorTranslucent);
            }
        }
    }
}
