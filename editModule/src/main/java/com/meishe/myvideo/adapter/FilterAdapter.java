package com.meishe.myvideo.adapter;

import android.text.TextUtils;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.ImageLoader;
import com.meishe.business.assets.AssetUtils;
import com.meishe.engine.asset.bean.AssetInfo;
import com.meishe.engine.bean.BaseInfo;
import com.meishe.engine.interf.IBaseInfo;
import com.meishe.myvideo.R;
import com.meishe.myvideo.view.MarqueeTextView;
import com.meishe.third.adpater.BaseViewHolder;

import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2020/12/29 13:50
 * @Description :滤镜菜单适配器 Adapter for adjust menu.
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class FilterAdapter extends BaseSelectAdapter<IBaseInfo> {
    private final ImageLoader.Options mOptions;
    private int mAssetSubType = 0;

    public FilterAdapter() {
        super(R.layout.view_menu_effect_item);
        mOptions = new ImageLoader.Options()
                .centerCrop()
                .skipMemoryCache(false);
    }

    public void setAssetSubType(int mAssetSubType) {
        this.mAssetSubType = mAssetSubType;
    }

    /**
     * 设置选中
     * Set selected
     *
     * @param packageId  package id
     * @param effectMode effect mode
     */
    public void setSelected(String packageId, int effectMode) {
        if (TextUtils.isEmpty(packageId)){
            setSelectPosition(0);
            return;
        }
        List<IBaseInfo> data = getData();
        for (int index = 0; index < data.size(); index++) {
            IBaseInfo baseInfo = data.get(index);
            if (effectMode == BaseInfo.EFFECT_MODE_BUILTIN) {
                if (packageId.equals(baseInfo.getEffectId())) {
                    setSelectPosition(index);
                    return;
                }
            } else {
                if (packageId.equals(baseInfo.getPackageId())) {
                    setSelectPosition(index);
                    return;
                }
            }
        }
        setSelectPosition(0);
    }

    @Override
    protected void convert(@NonNull BaseViewHolder helper, IBaseInfo item) {
        AssetInfo assetInfo = (AssetInfo) item;
        ImageView icon = helper.getView(R.id.icon);
        TextView name = helper.getView(R.id.name);
        FrameLayout frameLayout = helper.getView(R.id.frame_pic);
        MarqueeTextView tvAssetPurchased = helper.getView(R.id.tv_asset_purchased);
        if (AssetUtils.assetPurchasedVisible(tvAssetPurchased, mAssetSubType, item.isAuthorized())) {
            tvAssetPurchased.setVisibility(View.VISIBLE);
        } else {
            if (tvAssetPurchased != null) {
                tvAssetPurchased.setVisibility(View.GONE);
            }
        }

        int paddingNo = (int) mContext.getResources().getDimension(R.dimen.dp_px_36);
        //更改封面大小
        //Change cover size
        if (helper.getAdapterPosition() == 0) {
            icon.setPadding(paddingNo, paddingNo, paddingNo, paddingNo);
        } else if (icon.getPaddingLeft() == paddingNo) {
            icon.setPadding(0, 0, 0, 0);
        }

        if (item.getEffectMode() == BaseInfo.EFFECT_MODE_BUILTIN) {
            icon.setImageResource(item.getCoverId());
            icon.setScaleType(ImageView.ScaleType.CENTER);
        } else {
            ImageLoader.loadUrl(mContext, item.getCoverPath(), icon, mOptions);
        }
        icon.setScaleType(ImageView.ScaleType.FIT_XY);
        name.setText(item.getName());
        if (helper.getAdapterPosition() == getSelectPosition()) {
            frameLayout.setBackground(CommonUtils.getRadiusDrawable(3, mContext.getResources().getColor(R.color.color_fffc2b55), 2, -1));
        } else {
            frameLayout.setBackgroundResource(0);
        }

        ImageView ivDownload = helper.getView(R.id.iv_downloading);
        if (!assetInfo.isHadDownloaded() || assetInfo.needUpdate()) {
            ImageLoader.loadUrl(mContext, R.mipmap.downloading, ivDownload);
            int progress = assetInfo.getDownloadProgress();
            if (progress >= 0 && progress < 100) {
                ivDownload.setVisibility(View.VISIBLE);
                icon.setVisibility(View.GONE);
            } else {
                ivDownload.setVisibility(View.GONE);
                icon.setVisibility(View.VISIBLE);
            }
        } else {
            ivDownload.setVisibility(View.GONE);
            icon.setVisibility(View.VISIBLE);
        }
    }

}
