package com.meishe.myvideo.adapter;

import android.text.TextUtils;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.facebook.drawee.backends.pipeline.Fresco;
import com.facebook.drawee.interfaces.DraweeController;
import com.facebook.drawee.view.SimpleDraweeView;
import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.ImageLoader;
import com.meishe.business.assets.AssetUtils;
import com.meishe.engine.asset.bean.AssetInfo;
import com.meishe.engine.bean.CommonData;
import com.meishe.myvideo.R;
import com.meishe.third.adpater.BaseQuickAdapter;
import com.meishe.third.adpater.BaseViewHolder;

import java.util.List;


/**
 * author：yangtailin on 2020/8/31 18:35
 * 动画适配器
 * Animation adapter
 */

public class AnimationAdapter extends BaseQuickAdapter<AssetInfo, BaseViewHolder> {
    private int mSelectedPosition = -1;
    private int mAssetSubType = 0;

    public AnimationAdapter() {
        super(R.layout.item_clip_animation);
    }

    /**
     * 选中某一项
     * Selected item .
     *
     * @param packageId The package id
     */
    public void selected(String packageId) {
        if (TextUtils.isEmpty(packageId)) {
            selected(0);
        } else {
            int index1 = -1;
            List<AssetInfo> data = getData();
            for (int index = 0; index < data.size(); index++) {
                if (packageId.equals(data.get(index).getPackageId())) {
                    index1 = index;
                    break;
                }
            }
            selected(index1);
        }
    }

    /**
     * 选中某一项
     * Selected item .
     *
     * @param position The index of list
     */
    public void selected(int position) {
        if (position == mSelectedPosition) {
            return;
        }
        if (mSelectedPosition >= 0) {
            notifyItemChanged(mSelectedPosition);
        }
        mSelectedPosition = position;
        if (position >= 0 && position < getData().size()) {
            notifyItemChanged(position);
        }
    }

    public int getSelectedPosition() {
        return mSelectedPosition;
    }


    public void setAssetSubType(int mAssetSubType) {
        this.mAssetSubType = mAssetSubType;
    }

    @Override
    protected void convert(@NonNull BaseViewHolder helper, AssetInfo item) {
        SimpleDraweeView ivCover = helper.getView(R.id.iv_cover);
        FrameLayout frameLayout = helper.getView(R.id.frame_pic);
        TextView tvAssetPurchased = helper.getView(R.id.tv_asset_purchased);
        if (AssetUtils.assetPurchasedVisible(tvAssetPurchased, mAssetSubType, item.isAuthorized())) {
            tvAssetPurchased.setVisibility(View.VISIBLE);
        } else {
            if (tvAssetPurchased != null) {
                tvAssetPurchased.setVisibility(View.GONE);
            }
        }

        int paddingNo = (int) mContext.getResources().getDimension(R.dimen.dp_px_36);
        //更改封面大小
        //Change cover size
        if (helper.getAdapterPosition() == 0) {
            ivCover.setPadding(paddingNo, paddingNo, paddingNo, paddingNo);
        } else if (ivCover.getPaddingLeft() == paddingNo) {
            ivCover.setPadding(0, 0, 0, 0);
        }

        if (CommonData.EFFECT_BUILTIN == item.getEffectMode()) {
            ivCover.setImageResource(item.getCoverId());
        } else {
            //asset://android_asset/animation/in/9A0A2A81-A897-4253-B176-9FA04E5C0405.webp
            //file:///android_asset/animation/out/5DE5DF47-2A85-418D-BCDE-073918CFDD0C.webp
            String imageUrl = item.getCoverPath();
            if (imageUrl != null) {
                if (imageUrl.startsWith("file")) {
                    imageUrl = imageUrl.replace("file:/", "asset:");
                }
                DraweeController controller = Fresco.newDraweeControllerBuilder()
                        .setUri(imageUrl)
                        .setAutoPlayAnimations(true)
                        .setOldController(ivCover.getController())
                        .build();
                ivCover.setController(controller);
            }
        }
        helper.setText(R.id.tv_name, item.getName());
        if (helper.getAdapterPosition() == getSelectedPosition()) {
            frameLayout.setBackground(CommonUtils.getRadiusDrawable(3, mContext.getResources().getColor(R.color.color_fffc2b55), 2, -1));
        } else {
            frameLayout.setBackgroundResource(0);
        }

        ImageView ivDownload = helper.getView(R.id.iv_downloading);
        if (!item.isHadDownloaded() || item.needUpdate()) {
            ImageLoader.loadUrl(mContext, R.mipmap.downloading, ivDownload);
            int progress = item.getDownloadProgress();
            if (progress >= 0 && progress < 100) {
                ivDownload.setVisibility(View.VISIBLE);
                ivCover.setVisibility(View.GONE);
            } else {
                ivDownload.setVisibility(View.GONE);
                ivCover.setVisibility(View.VISIBLE);
            }
        } else {
            ivDownload.setVisibility(View.GONE);
            ivCover.setVisibility(View.VISIBLE);
        }
    }

}

