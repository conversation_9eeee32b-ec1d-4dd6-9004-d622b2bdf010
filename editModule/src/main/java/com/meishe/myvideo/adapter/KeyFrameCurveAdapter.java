package com.meishe.myvideo.adapter;

import android.view.View;
import android.widget.ImageView;

import androidx.annotation.NonNull;

import com.meishe.base.utils.ImageLoader;
import com.meishe.engine.bean.CurveAdjustData;
import com.meishe.myvideo.R;
import com.meishe.third.adpater.BaseViewHolder;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2020/12/30 16:19
 * @Description :关键帧曲线Adapter Adapter for key frame curve.
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class KeyFrameCurveAdapter extends BaseSelectAdapter<CurveAdjustData> {

    public KeyFrameCurveAdapter() {
        super(R.layout.item_key_frame_curve);
    }

    @Override
    protected void convert(@NonNull BaseViewHolder helper, CurveAdjustData item) {
        ImageView icon = helper.getView(R.id.icon);
        ImageView mask = helper.getView(R.id.mask);
        int position = helper.getAdapterPosition();
        boolean isSelect = getSelectPosition() == position;
        mask.setVisibility(isSelect? View.VISIBLE: View.GONE);
        ImageLoader.loadUrl(mContext, item.getCover(), icon);
    }
}
