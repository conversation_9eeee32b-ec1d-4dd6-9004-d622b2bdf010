package com.meishe.myvideo.interfaces;


import com.meishe.engine.bean.Plug;
import com.meishe.engine.bean.PlugDetail;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> LiHangZhou
 * @CreateDate :2020/12/29 14:10
 * @Description :底部视图事件监听  注意：加方法要慎重，轻易不要加，能改造原有方法就改造,如果差异非常大可以考虑继承。
 * The bottom view event listener
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public abstract class PlugsEventListener extends BottomEventListener{

    /**
     * 展示添加视图
     * display add plug
     */
    public void onDisplayAddPlug() {

    }

    /**
     * 跳转添加插件特效
     * On jump plug list
     */
    public void onJumpPlugList() {

    }

    /**
     * 参数调节
     * on Param Value Change
     *
     * @param plug  the plug 插件数据
     * @param param the parameter 调节参数
     */
    public void onParamValueChange(Plug plug, PlugDetail.Param param) {

    }

    /**
     * 参数调节开始
     * on Param Value Change Start
     *
     * @param plug  the plug 插件数据
     * @param param the parameter 调节参数
     */
    public void onParamValueChangeStart(Plug plug, PlugDetail.Param param) {

    }

    /**
     * 参数调节完成
     * on Param Value Change Finish
     *
     * @param plug  the plug 插件数据
     * @param param the parameter 调节参数
     */
    public void onParamValueChangeFinish(Plug plug, PlugDetail.Param param) {

    }

    /**
     * 某一项被点击
     * A certain item is clicked
     *
     * @param plug  the plug 插件数据
     * @param param the parameter 调节参数
     */
    public void onParamItemClick(Plug plug, PlugDetail.Param param) {

    }

    /**
     * 某一项被点击
     * A certain item is clicked
     *
     * @param plug the plug 插件数据
     */
    public void onPlugItemClick(Plug plug) {

    }

    /**
     * 关键帧按钮被点击
     * On Key frame button clicked
     *
     * @param plug  the plug 插件数据
     * @param param the parameter 调节参数
     * @param add   true add ,false delete
     */
    public boolean onKeyFrameClick(Plug plug, PlugDetail.Param param, boolean add) {
        return false;
    }

    /**
     * 关键帧按钮曲綫被点击
     * On Key frame curve button clicked
     *
     * @param plug  the plug 插件数据
     * @param param the parameter 调节参数
     */
    public void onKeyFrameCurveClick(Plug plug, PlugDetail.Param param) {

    }

    /**
     * 展示插件列表
     * <p>
     * On show plug list
     */
    public void onShowPlugList() {

    }
}
