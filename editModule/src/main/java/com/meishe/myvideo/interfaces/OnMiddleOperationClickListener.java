package com.meishe.myvideo.interfaces;

/**
 * The interface On middle operation click listener.
 * 此接口中间操作的界面单击监听
 */
public interface OnMiddleOperationClickListener {
    /**
     * 播放控制操作 @param isPlaying the is playing
     */
    void onPlayEventCallback(boolean isPlaying);

    /**
     * 撤销操作 undo operation
     */
    void onCancelEventCallback();

    /**
     * 恢复操作 recovery operation
     */
    void onRecoverEventCallback();

    /**
     * 全屏操作 Full screen operation
     */
    void onZoomEventCallback();

    /**
     * 关键帧按钮被点击
     * On Key frame button clicked
     *
     * @param add true add ,false delete
     */
    boolean onKeyFrameClick(boolean add);

    /**
     * 关键帧按钮被点击后
     * On post key frame click boolean.
     */
    void onPostKeyFrameClick();

    /**
     * 关键帧按钮曲綫被点击
     * On Key frame curve button clicked
     */
    void onKeyFrameCurveClick();
}
