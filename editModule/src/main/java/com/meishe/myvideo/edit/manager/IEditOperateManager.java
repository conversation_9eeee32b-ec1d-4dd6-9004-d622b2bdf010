package com.meishe.myvideo.edit.manager;

import com.meishe.engine.db.TimelineEntity;
import com.meishe.myvideo.edit.observer.EditOperateObserver;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> <PERSON><PERSON><PERSON>Z<PERSON>
 * @CreateDate :2021/05/17 10.49
 * @Description :编辑操作中恢复与撤销的管理类接口 Management class interfaces for restore and undo in edit operations
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public interface IEditOperateManager {
    /**
     * 撤销操作
     * undo operation
     *
     * @return e
     */
    TimelineEntity cancelOperate();

    /**
     * 恢复操作
     * recovery operation
     *
     * @return e
     */
    TimelineEntity recoverOperate();

    /**
     * 添加新操作
     * Add a new operation
     */
    void addOperate();

    /**
     * 注册编辑操作观察者
     * Register edit operate observer
     *
     * @param observer the observer 观察者
     */
    void registerOperateObserver(EditOperateObserver observer);

    /**
     * 注销编辑操作观察者
     * Register edit operate observer
     *
     * @param observer the observer 观察者
     */
    void unregisterOperateObserver(EditOperateObserver observer);

    boolean haveOperate();

    /**
     * Destroy.
     * 销毁
     */
    void destroy();
}
