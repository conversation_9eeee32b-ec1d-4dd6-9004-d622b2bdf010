package com.meishe.myvideo.edit.record;


import android.text.TextUtils;

import com.meishe.base.utils.ResourceUtils;
import com.meishe.engine.bean.BaseInfo;

/**
 * The type Record fx list item.
 * 记录fx列表项
 */
public class AudioFxInfo extends BaseInfo {
    private String effectId;
    private String coverName;
    @Override
    public void setEffectId(String effectId) {
        this.effectId = effectId;
    }

    @Override
    public String getEffectId() {
        return effectId;
    }

    public String getCoverName() {
        return coverName;
    }

    public void setCoverName(String coverName) {
        this.coverName = coverName;
    }

    @Override
    public int getCoverId() {
        if(TextUtils.isEmpty(coverName)){
            return super.getCoverId();
        }
        return ResourceUtils.getMipmapIdByName(coverName);
    }
}
