package com.meishe.myvideo.edit.manager;

import com.meishe.base.utils.LogUtils;
import com.meishe.engine.EditorEngine;
import com.meishe.engine.db.DbManager;
import com.meishe.engine.db.TimelineDataDao;
import com.meishe.engine.db.TimelineEntity;
import com.meishe.myvideo.edit.OperateData;
import com.meishe.myvideo.edit.observer.EditOperateObservable;
import com.meishe.myvideo.edit.observer.EditOperateObserver;

import java.util.ArrayDeque;
import java.util.UUID;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> LiHangZhou
 * @CreateDate :2021/5/17 13:14
 * @Description :编辑操作中恢复与撤销的管理类
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class EditOperateManager implements IEditOperateManager {
    /**
     * The Cancel stack.
     * 取消堆栈
     */
    private final ArrayDeque<OperateData> mCancelStack = new ArrayDeque<>();
    /**
     * The Recover stack.
     * 恢复堆栈
     */
    private final ArrayDeque<OperateData> mRecoverStack = new ArrayDeque<>();
    /**
     * The now operate data.
     * 现在操作数据类
     */
    private OperateData mCurrentData;
    private final TimelineDataDao mTimelineDao;
    private boolean isCancelStackEmpty = true;
    private boolean isRecoverStackEmpty = false;
    private final EditOperateObservable mOperateObservable;

    private EditOperateManager() {
        mTimelineDao = DbManager.get().getTimelineDao();
        mOperateObservable = new EditOperateObservable();
    }

    private final static class Holder {
        private static final IEditOperateManager INSTANCE = new EditOperateManager();
    }

    public static IEditOperateManager get() {
        return Holder.INSTANCE;
    }


    @Override
    public TimelineEntity cancelOperate() {
        if (mCurrentData == null) {
            return null;
        }
        if (mCancelStack.isEmpty()) {
            if (!isCancelStackEmpty) {
                isCancelStackEmpty = true;
                notifyCancel();
            }
            return null;
        }
        mRecoverStack.push(mCurrentData);
        if (isRecoverStackEmpty) {
            isRecoverStackEmpty = false;
            notifyRecover();
        }
        mCurrentData = mCancelStack.pop();
        if (mCancelStack.isEmpty() && !isCancelStackEmpty) {
            isCancelStackEmpty = true;
            notifyCancel();

        }
        return getTimelineData(mCurrentData);
    }

    /**
     * 通知撤销
     * notify undo
     */
    private void notifyCancel() {
        mOperateObservable.onUndo(isCancelStackEmpty);
    }

    /**
     * 通知恢复
     * notify recover
     */
    private void notifyRecover() {
        mOperateObservable.onRecover(isRecoverStackEmpty);
    }

    @Override
    public TimelineEntity recoverOperate() {
        if (mRecoverStack.isEmpty() || mCurrentData == null) {
            return null;
        }
        mCancelStack.push(mCurrentData);
        if (isCancelStackEmpty) {
            isCancelStackEmpty = false;
            notifyCancel();
        }
        mCurrentData = mRecoverStack.pop();
        if (mRecoverStack.isEmpty() && !isRecoverStackEmpty) {
            isRecoverStackEmpty = true;
            notifyRecover();
        }
        return getTimelineData(mCurrentData);
    }

    @Override
    public void addOperate() {
        if (mCurrentData != null) {
            mCancelStack.push(mCurrentData);
        }
        mCurrentData = createOperateData();
        if (isCancelStackEmpty) {
            if (!mCancelStack.isEmpty()) {
                isCancelStackEmpty = false;
                notifyCancel();
            }
        }
        isCancelStackEmpty = mCancelStack.isEmpty();
        mRecoverStack.clear();
        if (!isRecoverStackEmpty) {
            isRecoverStackEmpty = true;
            notifyRecover();
        }
    }

    /**
     * 创建编辑操作数据类
     * Create operate data
     */
    private OperateData createOperateData() {
        TimelineEntity timelineData = new TimelineEntity();
        timelineData.setId(UUID.randomUUID().toString());
        timelineData.setJson(EditorEngine.getInstance().getCurrentTimelineData());
        try {
            mTimelineDao.insertAsset(timelineData);
        } catch (Exception e) {
            try {
                if (mTimelineDao.getTimelineData(timelineData.getId()) != null) {
                    mTimelineDao.updateAsset(timelineData);
                }
            } catch (Exception e2) {
                LogUtils.e(e2);
            }
        }
        return new OperateData().setId(timelineData.getId());
    }

    /**
     * 获取时间线数据类
     * Get timeline data
     */
    private TimelineEntity getTimelineData(OperateData operateData) {
        if (operateData != null) {
           /* TimelineEntity timelineData = mTimelineDao.getTimelineData(operateData.getId());
            LogUtils.d("timelineData=" + timelineData);
            return timelineData;*/
            try {
                return mTimelineDao.getTimelineData(operateData.getId());
            } catch (Exception e) {
                LogUtils.e(e);
            }
        }
        return null;
    }

    /**
     * 注册编辑操作观察者
     * Register edit operate observer
     *
     * @param observer the observer 观察者
     */
    @Override
    public void registerOperateObserver(EditOperateObserver observer) {
        try {
            if (observer != null) {
                mOperateObservable.registerObserver(observer);
            }
        } catch (Exception e) {
            LogUtils.e(e);
        }

    }

    /**
     * 注销编辑操作观察者
     * Register edit operate observer
     *
     * @param observer the observer 观察者
     */
    @Override
    public void unregisterOperateObserver(EditOperateObserver observer) {
        try {
            if (observer != null) {
                mOperateObservable.unregisterObserver(observer);
            }
        } catch (Exception e) {
            LogUtils.e(e);
        }
    }

    @Override
    public void destroy() {
        mCancelStack.clear();
        mRecoverStack.clear();
        mCurrentData = null;
        try {
            if (mTimelineDao != null) {
                mTimelineDao.deleteAsset();
            }
        } catch (Exception e) {
            LogUtils.e(e);
        }
    }

    /**
     * 是否操作过
     * Has it been operated
     *
     * @return boolean true yes 是的,false not 不是的
     */
    @Override
    public boolean haveOperate() {
        return mCancelStack.size() > 0;
    }

}
