package com.meishe.myvideo.edit.observer;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateDate :2021/5/17 14:23
 * @Description :编辑操作观察者 The edit operation observer
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public abstract class EditOperateObserver {
    /**
     * 撤销
     * undo
     *
     * @param isEmpty true is empty 空，false not 非空
     */
    public void onUndo(boolean isEmpty) {

    }

    /**
     * 恢复
     * Recover
     *
     * @param isEmpty true is empty 空，false not 非空
     */
    public void onRecover(boolean isEmpty) {

    }
}
