package com.meishe.myvideo.edit.observer;

import android.database.Observable;

import com.meishe.base.utils.ThreadUtils;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> LiHangZhou
 * @CreateDate :2021/5/17 14:23
 * @Description :编辑操作被观察者 The observable of edit operation
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class EditOperateObservable extends Observable<EditOperateObserver> {
    /**
     * 撤销
     * undo
     *
     * @param isEmpty true is empty 空，false not 非空
     */
    public void onUndo(final boolean isEmpty) {
        ThreadUtils.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                for (int i = mObservers.size() - 1; i >= 0; i--) {
                    mObservers.get(i).onUndo(isEmpty);
                }
            }
        });
    }

    /**
     * 恢复
     * Recover
     *
     * @param isEmpty true is empty 空，false not 非空
     */
    public void onRecover(final boolean isEmpty) {
        ThreadUtils.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                for (int i = mObservers.size() - 1; i >= 0; i--) {
                    mObservers.get(i).onRecover(isEmpty);
                }
            }
        });
    }
}
