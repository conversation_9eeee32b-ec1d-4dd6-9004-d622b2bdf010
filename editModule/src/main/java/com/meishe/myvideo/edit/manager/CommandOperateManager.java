package com.meishe.myvideo.edit.manager;

import android.text.TextUtils;

import com.meishe.base.utils.LogUtils;
import com.meishe.engine.command.CommandManager;
import com.meishe.engine.db.DbManager;
import com.meishe.engine.db.TimelineDataDao;
import com.meishe.engine.db.TimelineEntity;
import com.meishe.myvideo.edit.OperateData;
import com.meishe.myvideo.edit.observer.EditOperateObservable;
import com.meishe.myvideo.edit.observer.EditOperateObserver;

import java.util.ArrayDeque;
import java.util.UUID;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> LiHangZhou
 * @CreateDate :2021/5/17 13:14
 * @Description :编辑操作中恢复与撤销的管理类 Management class of recovery and cancellation in editing operation
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class CommandOperateManager implements IEditOperateManager {
    /**
     * The Cancel stack.
     * 取消堆栈
     */
    private final ArrayDeque<OperateData> mCancelStack = new ArrayDeque<>();
    /**
     * The Recover stack.
     * 恢复堆栈
     */
    private final ArrayDeque<OperateData> mRecoverStack = new ArrayDeque<>();

    private final TimelineDataDao mTimelineDao;
    private boolean isCancelStackEmpty = true;
    private boolean isRecoverStackEmpty = false;
    private final EditOperateObservable mOperateObservable;

    private CommandOperateManager() {
        mTimelineDao = DbManager.get().getTimelineDao();
        mOperateObservable = new EditOperateObservable();
    }

    private final static class Holder {
        private static final IEditOperateManager INSTANCE = new CommandOperateManager();
    }

    public static IEditOperateManager get() {
        return Holder.INSTANCE;
    }


    @Override
    public TimelineEntity cancelOperate() {
        if (mCancelStack.isEmpty()) {
            if (!isCancelStackEmpty) {
                isCancelStackEmpty = true;
                notifyCancel();
            }
            return null;
        }
        OperateData currentData = mCancelStack.pop();
        if (mCancelStack.isEmpty() && !isCancelStackEmpty) {
            isCancelStackEmpty = true;
            notifyCancel();

        }
        if (currentData == null) {
            return null;
        }
        mRecoverStack.push(currentData);
        if (isRecoverStackEmpty) {
            isRecoverStackEmpty = false;
            notifyRecover();
        }
        return getTimelineData(currentData);
    }

    /**
     * 通知撤销
     * notify undo
     */
    private void notifyCancel() {
        mOperateObservable.onUndo(isCancelStackEmpty);
    }

    /**
     * 通知恢复
     * notify recover
     */
    private void notifyRecover() {
        mOperateObservable.onRecover(isRecoverStackEmpty);
    }

    @Override
    public TimelineEntity recoverOperate() {
        if (mRecoverStack.isEmpty()) {
            return null;
        }
        OperateData currentData = mRecoverStack.pop();
        if (mRecoverStack.isEmpty() && !isRecoverStackEmpty) {
            isRecoverStackEmpty = true;
            notifyRecover();
        }
        if (currentData == null) {
            return null;
        }
        mCancelStack.push(currentData);
        if (isCancelStackEmpty) {
            isCancelStackEmpty = false;
            notifyCancel();
        }
        return getTimelineData(currentData);
    }

    @Override
    public void addOperate() {
        OperateData currentData = createOperateData();
        if (currentData == null) {
            return;
        }
        mCancelStack.push(currentData);
        isCancelStackEmpty = false;
        notifyCancel();
        mRecoverStack.clear();
        isRecoverStackEmpty = true;
        notifyRecover();
    }

    /**
     * 创建编辑操作数据类
     * Create operate data
     */
    private OperateData createOperateData() {
        String path = CommandManager.getInstance().saveCommand();
        if (TextUtils.isEmpty(path)) {
            return null;
        }
        TimelineEntity timelineData = new TimelineEntity();
        timelineData.setId(UUID.randomUUID().toString());
        timelineData.setJson(path);
        try {
            mTimelineDao.insertAsset(timelineData);
        } catch (Exception e) {
            try {
                if (mTimelineDao.getTimelineData(timelineData.getId()) != null) {
                    mTimelineDao.updateAsset(timelineData);
                }
            } catch (Exception e2) {
                LogUtils.e(e2);
            }
        }
        return new OperateData().setId(timelineData.getId());
    }

    /**
     * 获取时间线数据类
     * Get timeline data
     */
    private TimelineEntity getTimelineData(OperateData operateData) {
        if (operateData != null) {
           /* TimelineEntity timelineData = mTimelineDao.getTimelineData(operateData.getId());
            LogUtils.d("timelineData=" + timelineData);
            return timelineData;*/
            try {
                return mTimelineDao.getTimelineData(operateData.getId());
            } catch (Exception e) {
                LogUtils.e(e);
            }
        }
        return null;
    }

    /**
     * 注册编辑操作观察者
     * Register edit operate observer
     *
     * @param observer the observer 观察者
     */
    @Override
    public void registerOperateObserver(EditOperateObserver observer) {
        try {
            if (observer != null) {
                mOperateObservable.registerObserver(observer);
            }
        } catch (Exception e) {
            LogUtils.e(e);
        }

    }

    /**
     * 注销编辑操作观察者
     * Register edit operate observer
     *
     * @param observer the observer 观察者
     */
    @Override
    public void unregisterOperateObserver(EditOperateObserver observer) {
        try {
            if (observer != null) {
                mOperateObservable.unregisterObserver(observer);
            }
        } catch (Exception e) {
            LogUtils.e(e);
        }
    }

    @Override
    public void destroy() {
        mCancelStack.clear();
        mRecoverStack.clear();
        try {
            if (mTimelineDao != null) {
                mTimelineDao.deleteAsset();
            }
        } catch (Exception e) {
            LogUtils.e(e);
        }
        CommandManager.getInstance().deleteCache();
    }

    /**
     * 是否操作过
     * Has it been operated
     *
     * @return boolean true yes 是的,false not 不是的
     */
    @Override
    public boolean haveOperate() {
        return mCancelStack.size() > 0;
    }

}
