<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.czc.cutsame">

    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <application>
        <activity
            android:name=".ExportTemplateActivity"
            android:screenOrientation="portrait"
            android:theme="@style/TranslucentFullScreenTheme"/>
        <activity
            android:name=".ExportTemplateSettingActivity"
            android:screenOrientation="portrait"
            android:theme="@style/TranslucentFullScreenTheme" />
        <activity
            android:name=".CutCompileActivity"
            android:screenOrientation="portrait"
            android:theme="@style/TranslucentFullScreenTheme"
            tools:ignore="LockedOrientationActivity" />
        <activity
            android:name=".CutSameEditorActivity"
            android:screenOrientation="portrait"
            android:theme="@style/TranslucentFullScreenTheme"
            tools:ignore="LockedOrientationActivity" />
        <activity
            android:name=".TailorActivity"
            android:screenOrientation="portrait"
            android:theme="@style/TranslucentFullScreenTheme"
            tools:ignore="LockedOrientationActivity" />
        <activity
            android:name=".TemplatePreviewActivity"
            android:screenOrientation="portrait"
            android:theme="@style/TranslucentFullScreenTheme"
            android:launchMode="singleTask"
            tools:ignore="LockedOrientationActivity" />
        <activity
            android:name=".TemplateListActivity"
            android:screenOrientation="portrait"
            android:theme="@style/TranslucentFullScreenTheme"
            android:launchMode="singleTask"
            tools:ignore="LockedOrientationActivity" />
        <activity
            android:name=".MaterialSelectActivity"
            android:screenOrientation="portrait"
            android:launchMode="singleTask"
            android:theme="@style/FullScreenTheme"
            tools:ignore="LockedOrientationActivity" />
    </application>

</manifest>