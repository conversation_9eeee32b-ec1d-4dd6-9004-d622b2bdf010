<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center">

    <RelativeLayout
        android:id="@+id/fragment_base_parent"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center">

        <com.meicam.sdk.NvsLiveWindowExt
            android:layout_centerInParent="true"
            android:id="@+id/fragment_base_live_window"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <ImageView
            android:id="@+id/fragment_base_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:background="@drawable/fragment_base_video"
            android:contentDescription="@null"
            android:visibility="gone" />

        <com.czc.cutsame.view.CaptionBox
            android:layout_centerInParent="true"
            android:id="@+id/caption_box"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="invisible" />
    </RelativeLayout>
</RelativeLayout>