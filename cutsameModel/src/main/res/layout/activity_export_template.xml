<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black_1010"
    tools:context=".ExportTemplateActivity">

    <ImageView
        android:id="@+id/iv_back"
        android:layout_width="@dimen/dp_px_66"
        android:layout_height="@dimen/dp_px_66"
        android:layout_marginStart="@dimen/dp_px_24"
        android:layout_marginLeft="@dimen/dp_px_24"
        android:background="@mipmap/ic_draft_back"
        android:padding="@dimen/dp_px_15"
        tools:ignore="ContentDescription" />

    <ImageView
        android:id="@+id/iv_compile_home"
        android:layout_width="@dimen/dp_px_54"
        android:layout_height="@dimen/dp_px_54"
        android:layout_gravity="end"
        android:layout_marginEnd="@dimen/dp_px_39"
        android:layout_marginRight="@dimen/dp_px_39"
        android:background="@mipmap/ic_cut_same_export_template_compile_home"
        android:contentDescription="@null"
        android:visibility="gone" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="@dimen/dp_px_282"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/iv_cover"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_px_504"
            android:layout_marginLeft="@dimen/dp_px_90"
            android:layout_marginRight="@dimen/dp_px_90"
            android:contentDescription="@null" />

        <LinearLayout
            android:id="@+id/ll_generate_template"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_px_90"
                android:layout_marginLeft="@dimen/dp_px_90"
                android:layout_marginTop="@dimen/dp_px_108"
                android:text="@string/activity_cut_export_template_template_name"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_px_36" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_px_90"
                android:layout_marginLeft="@dimen/dp_px_90"
                android:layout_marginTop="@dimen/dp_px_36"
                android:layout_marginRight="@dimen/dp_px_90"
                android:background="@mipmap/ic_cu_same_export_template_name_bg"
                android:orientation="horizontal">

                <EditText
                    android:id="@+id/et_input_template_name"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_weight="1"
                    android:autofillHints=""
                    android:background="@mipmap/ic_cu_same_export_template_name_bg"
                    android:hint="@string/activity_cut_export_template_please_input_template_name"
                    android:maxLines="1"
                    android:paddingStart="@dimen/dp_px_15"
                    android:paddingLeft="@dimen/dp_px_15"
                    android:singleLine="true"
                    android:textColor="@color/white"
                    android:textColorHint="@color/color_ff707070"
                    android:textSize="@dimen/sp_px_30"
                    tools:ignore="RtlSymmetry,TextFields" />

                <TextView
                    android:id="@+id/tv_name_number"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginEnd="@dimen/dp_px_21"
                    android:layout_marginRight="@dimen/dp_px_21"
                    android:textColor="@color/color_ff707070"
                    android:textSize="@dimen/sp_px_30" />
            </LinearLayout>


            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_px_90"
                android:layout_marginLeft="@dimen/dp_px_90"
                android:layout_marginTop="@dimen/dp_px_66"
                android:text="@string/activity_cut_export_template_template_desc"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_px_36" />

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_px_255"
                android:layout_marginLeft="@dimen/dp_px_90"
                android:layout_marginTop="@dimen/dp_px_36"
                android:layout_marginRight="@dimen/dp_px_90"
                android:background="@mipmap/ic_cut_same_export_template_content_bg">

                <EditText
                    android:id="@+id/et_input_template_desc"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@null"
                    android:gravity="start"
                    android:hint="@string/activity_cut_export_template_please_input_template_desc"
                    android:importantForAutofill="no"
                    android:padding="@dimen/dp_px_15"
                    android:textColor="@color/white"
                    android:textColorHint="@color/color_ff707070"
                    android:textSize="@dimen/sp_px_30"
                    tools:ignore="TextFields" />

                <TextView
                    android:id="@+id/tv_desc_number"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_alignParentRight="true"
                    android:layout_alignParentBottom="true"
                    android:layout_marginEnd="@dimen/dp_px_21"
                    android:layout_marginRight="@dimen/dp_px_21"
                    android:layout_marginBottom="@dimen/dp_px_13"
                    android:textColor="@color/color_ff707070"
                    android:textSize="@dimen/sp_px_30" />

            </RelativeLayout>


            <Button
                android:id="@+id/btn_export_template"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_px_100"
                android:layout_marginLeft="@dimen/dp_px_162"
                android:layout_marginTop="@dimen/dp_px_210"
                android:layout_marginRight="@dimen/dp_px_162"
                android:background="@mipmap/ic_cut_same_export_template_button_bg"
                android:text="@string/activity_cut_export_template_confirm_generate_template"
                android:textColor="@color/white" />

            <TextView
                android:id="@+id/tv_uploading_hint"
                android:layout_width="@dimen/dp_px_480"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="@dimen/dp_px_45"
                android:gravity="center"
                android:text="@string/activity_cut_export_template_uploading_hint"
                android:textColor="@color/color_ffb1b1b1"
                android:textSize="@dimen/sp_px_30"
                android:visibility="gone" />
        </LinearLayout>

        <RelativeLayout
            android:id="@+id/rl_generate_template_progress"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="@dimen/dp_px_249"
            android:background="@color/transparent"
            android:visibility="gone">

            <com.meishe.base.view.CompileProgress
                android:id="@+id/generate_template_progress"
                android:layout_width="@dimen/dp_px_390"
                android:layout_height="@dimen/dp_px_390"
                android:layout_centerHorizontal="true"
                android:layout_marginBottom="@dimen/dp_px_45"
                app:progressBackgroundColor="@color/gray_4a4a"
                app:progressColor="@color/color_ffff365e"
                app:progressWidth="@dimen/dp_px_9" />

            <TextView
                android:id="@+id/tv_generate_template_progress"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_px_190"
                android:layout_alignTop="@+id/generate_template_progress"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="@dimen/dp_px_100"
                android:gravity="center"
                android:textColor="@color/white_8"
                android:textSize="@dimen/sp_px_48" />

            <TextView
                android:id="@+id/tv_generate_template_info"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/generate_template_progress"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="@dimen/dp_px_45"
                android:gravity="center"
                android:maxWidth="@dimen/dp_px_900"
                android:text="@string/activity_cut_export_template_please_not_lock_screen_info"
                android:textColor="@color/white_8"
                android:textSize="@dimen/sp_px_36" />
        </RelativeLayout>
    </LinearLayout>

    <TextView
        android:id="@+id/tv_generate_result"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:gravity="center"
        android:text="@string/activity_cut_export_template_already_save"
        android:textColor="@color/white_8"
        android:textSize="@dimen/sp_px_36"
        android:visibility="gone" />

    <Button
        android:id="@+id/btn_generate_template_cancel"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_gravity="bottom"
        android:background="@color/black_1010"
        android:gravity="center"
        android:paddingTop="@dimen/dp_px_30"
        android:paddingBottom="@dimen/dp_px_99"
        android:text="@string/activity_cut_export_template_cancel"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_px_36"
        android:visibility="gone" />
</FrameLayout>