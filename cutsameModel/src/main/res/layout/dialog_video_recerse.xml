<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="@dimen/dp_px_480"
    android:layout_height="@dimen/dp_px_438"
    android:background="@drawable/bg_rectangle_round_black_2020"

    android:gravity="center_horizontal"
    android:orientation="vertical">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_px_30"
        android:text="@string/video_reverse_ing"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_px_30" />


    <FrameLayout
        android:layout_width="@dimen/dp_px_177"
        android:layout_height="@dimen/dp_px_177"
        android:layout_gravity="center"
        android:layout_marginTop="@dimen/dp_px_45">

        <com.meishe.base.view.CompileProgress
            android:id="@+id/cut_compile_progress"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:progressBackgroundColor="@color/gray_363"
            app:progressColor="@color/red_fc2b" />

        <TextView
            android:id="@+id/tv_progress"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_px_30" />
    </FrameLayout>


    <TextView
        android:id="@+id/tv_cancel"
        android:layout_width="@dimen/dp_px_225"
        android:layout_height="@dimen/dp_px_60"
        android:layout_marginTop="@dimen/dp_px_54"
        android:background="@drawable/bg_rectangle_round_gray_3a3c49"
        android:gravity="center"
        android:text="@string/activity_cut_export_template_cancel"
        android:textColor="@color/white_8"
        android:textSize="@dimen/sp_px_30" />
</LinearLayout>