<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rl_cut_dialog_ratio_parent"
    android:layout_width="@dimen/dp_px_900"
    android:layout_height="@dimen/dp_px_480">

    <ImageView
        android:id="@+id/iv_close"
        android:layout_width="@dimen/dp_px_48"
        android:layout_height="@dimen/dp_px_48"
        android:layout_alignParentEnd="true"
        android:layout_alignParentRight="true"
        android:layout_marginTop="@dimen/dp_px_30"
        android:layout_marginEnd="@dimen/dp_px_39"
        android:layout_marginRight="@dimen/dp_px_39"
        android:background="@mipmap/ic_cut_same_export_template_close"
        android:scaleType="fitXY"
        tools:ignore="ContentDescription" />

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="@dimen/dp_px_24"
        android:text="@string/activity_cut_same_select_ratio"
        android:textColor="@color/black"
        android:textSize="@dimen/sp_px_48"
        tools:ignore="RelativeOverlap" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvRatio"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/tv_title"
        android:layout_marginLeft="@dimen/dp_px_67"
        android:layout_marginTop="@dimen/dp_px_60"
        android:layout_marginRight="@dimen/dp_px_67"
        android:layout_marginBottom="@dimen/dp_px_45" />

</RelativeLayout>