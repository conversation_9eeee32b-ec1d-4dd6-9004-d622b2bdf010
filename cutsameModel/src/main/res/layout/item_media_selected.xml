<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <RelativeLayout
        android:id="@+id/real_image"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <ImageView
            android:id="@+id/iv_cover"
            android:layout_width="@dimen/dp_px_180"
            android:layout_height="@dimen/dp_px_180"
            android:layout_marginTop="@dimen/dp_px_24"
            android:contentDescription="@null"
            android:scaleType="centerCrop" />

        <ImageView
            android:id="@+id/image_group_point"
            android:layout_width="@dimen/dp_px_8"
            android:layout_height="@dimen/dp_px_8"
            android:layout_below="@+id/iv_cover"
            android:layout_marginLeft="@dimen/dp_px_63"
            android:layout_marginTop="@dimen/dp_px_31" />

        <TextView
            android:id="@+id/tv_index"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/iv_cover"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/dp_px_10"
            android:textColor="@color/color_ff707070"
            android:textSize="@dimen/sp_px_33" />

        <LinearLayout
            android:id="@+id/ll_duration"
            android:padding="@dimen/dp_px_15"
            android:layout_width="@dimen/dp_px_180"
            android:layout_height="@dimen/dp_px_180"
            android:layout_marginTop="@dimen/dp_px_24"
            android:gravity="center_vertical"
            android:orientation="vertical">
            <TextView
                android:gravity="left|center_vertical"
                android:id="@+id/tv_trim_duration"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textSize="@dimen/sp_px_30"
                android:textColor="@color/black"/>
            <TextView
                android:gravity="left|center_vertical"
                android:layout_gravity="center"
                android:id="@+id/tv_nice_duration"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textSize="@dimen/sp_px_30"
                android:textColor="@color/black"/>
        </LinearLayout>
    </RelativeLayout>

    <ImageView
        android:id="@+id/iv_delete"
        android:layout_width="@dimen/dp_px_51"
        android:layout_height="@dimen/dp_px_51"
        android:layout_marginStart="@dimen/dp_px_154"
        android:layout_marginLeft="@dimen/dp_px_154"
        android:contentDescription="@null"
        android:scaleType="fitXY"
        android:src="@mipmap/close_white_round" />

    <TextView
        android:id="@+id/tv_duration"
        android:layout_width="@dimen/dp_px_100"
        android:layout_height="@dimen/dp_px_50"
        android:layout_alignTop="@+id/real_image"
        android:layout_marginStart="@dimen/dp_px_40"
        android:layout_marginLeft="@dimen/dp_px_40"
        android:layout_marginTop="@dimen/dp_px_87"
        android:gravity="center"
        android:textColor="@color/black"
        android:textSize="@dimen/sp_px_33" />
</RelativeLayout>
