<resources>
    <string name="activity_tailor_sure">confirm</string>
    <string name="activity_cut_editor_import">export</string>
    <string name="activity_cut_editor_detail">edit</string>
    <string name="activity_cut_editor_video">video edit</string>
    <string name="activity_cut_editor_text">caption edit</string>
    <string name="activity_cut_editor_click_editor">click to edit</string>
    <string name="activity_cut_compile_tip1">Please do not lock the screen or switch to other applications.</string>
    <string name="activity_cut_compile_tip2">Saved to album.</string>
    <string name="activity_cut_compile_tip3">Export failed</string>
    <string name="template_used_num">usage %s</string>
    <string name="template_duration_used_num">duration&#160;%s &#160;&#160;usage&#160;%s&#160;&#160;clips %d</string>
    <string name="template_duration">duration&#160;%s &#160;&#160;clips %d</string>
    <string name="template_not_ready">Resources download, please hold on.</string>
    <string name="template_install_error">Template installation failed.</string>
    <string name="num_unit_w">w</string>
    <string name="clip_same_template">Use it</string>
    <string name="cut_compile_complete">done</string>
    <string name="cut_dialog_resolution_title">select resolution</string>
    <string name="cut_dialog_resolution_tip1">resolution</string>
    <string name="cut_dialog_resolution_tip2">The higher the resolution, the clearer the video.</string>
    <string name="cut_dialog_resolution_tip3">(recommend)</string>
    <string name="cut_dialog_resolution_button_text">Confirm export</string>
    <string name="cut_editor_title">edit</string>

    <!--素材选择-->
    <string name="select_material_content">select content</string>
    <string name="select_material_next">next</string>
    <string name="material_video">video</string>
    <string name="material_photo">photo</string>
    <string name="material_all">all</string>
    <string name="select_all">all</string>
    <string name="clip_need_video">need video</string>
    <string name="clip_need_photo">need photo</string>
    <string name="video_too_short">Video is too short.</string>
    <string name="selected_material_num_hint">select %d clips</string>
    <string name="selected_same_assets">Recommended to fill in the same photos if the clips are marked with the same color.</string>
    <string name="nothing">Not yet</string>
    <string name="imported">imported</string>
    <string name="int1080">1080p</string>
    <string name="int720">720p</string>
    <string name="int360">360p</string>
    <string name="int480">480p</string>
    <string name="int576">576p</string>
    <string name="int4K">4K</string>

    <string name="replace">replace</string>
    <string name="cut">cut</string>
    <string name="volume">volume</string>

    <string name="template_default_creator">meicam creator</string>
    <string name="activity_cut_export_template">export template</string>
    <string name="activity_cut_export_template_clip">clip</string>
    <string name="activity_cut_export_template_caption">caption</string>
    <string name="activity_cut_export_template_groups">groups</string>
    <string name="open_original_voice">Open the original voice</string>
    <string name="close_original_voice">Close the original voice</string>
    <string name="activity_cut_export_template_main_track">main track</string>
    <string name="activity_cut_export_template_pip">PIP %d</string>
    <string name="activity_cut_export_template_clip_param">clip %d</string>
    <string name="activity_cut_export_template_video_image">video or picture</string>
    <string name="activity_cut_export_template_no_groups">no groups</string>
    <string name="activity_cut_export_template_lock">lock</string>
    <string name="activity_cut_export_template_unlimited">unlimited</string>
    <string name="activity_cut_export_template_only_picture">only picture</string>
    <string name="activity_cut_export_template_only_video">only video</string>
    <string name="activity_cut_export_template_apply_to_all">apply to all clip</string>
    <string name="activity_cut_export_template_cancel">cancel</string>
    <string name="activity_cut_export_template_confirm">confirm</string>
    <string name="activity_cut_export_template_lock_source_no_group">lock source no group</string>
    <string name="activity_cut_export_template_only_video_no_group">only video no group</string>
    <string name="activity_cut_export_template_only_image_no_group">only image no group</string>
    <string name="activity_cut_export_template_have_group_limit_change_footage_type">have group limit change footage type</string>
    <string name="activity_cut_export_template_select_number">select(%d)</string>
    <string name="activity_cut_export_template_cancel_group">cancel group</string>
    <string name="activity_cut_export_template_select_all">select all</string>
    <string name="activity_cut_export_template_group_param">group %d</string>
    <string name="activity_cut_export_template_template_name">template name</string>
    <string name="no_voice">No voice</string>
    <string name="activity_cut_export_template_please_input_template_name">please input template name</string>
    <string name="activity_cut_export_template_template_desc">template desc</string>
    <string name="activity_cut_export_template_please_input_template_desc">please input template desc</string>
    <string name="activity_cut_export_template_please_not_lock_screen_info">Please do not lock the screen or switch to another application</string>
    <string name="activity_cut_export_template_already_save">already save please see from mine</string>
    <string name="activity_cut_export_template_uploading_hint">After the upload is successful, log in the main account "self made material" on the material platform to download the template and authorize</string>
    <string name="activity_cut_export_template_confirm_generate_template">confirm export</string>
    <string name="activity_cut_export_template_confirm_upload_template">confirm upload</string>
    <string name="activity_cut_export_template_mine">mine</string>
    <string name="activity_cut_same_select_ratio">select ratio</string>
    <string name="activity_cut_same_16_9">16:9(default)</string>
    <string name="activity_cut_same_9_16">9:16</string>
    <string name="activity_cut_same_1_1">1:1</string>
    <string name="activity_cut_same_4_3">4:3</string>
    <string name="activity_cut_same_3_4">3:4</string>
    <string name="activity_cut_same_9_18">9:18</string>
    <string name="activity_cut_export_not_allow_lock_all_clip">not allow lock all clip</string>
    <string name="activity_cut_export_no_template_data">no template data</string>
    <string name="video_reverse_ing">video reverse</string>
    <string name="video_reverse_fail">video reverse fail</string>
    <string name="export_template_audio">audio</string>
    <string name="tv_tip_no_net">The network is not available. Please check the network Settings</string>
    <string name="file_size_about">File size is about</string>

    <string name="tab_template_1">Recommend</string>
    <string name="tab_template_2">Purchased</string>

    <string name="tv_view_edit_volume_title">native volume</string>
    <string name="num_200">200</string>
    <string name="num_0">0</string>

    <string name="asset_purchased">Purchased</string>
    <string name="error_clip_file_is_invalid">HDR material editing is not supported at present</string>
    <string name="convert_failed">Import failed, please try again</string>
    <string name="activity_tailor_rect_select_hint">Drag to select the video display area</string>
    <string name="title_template_list">Template</string>
    <string name="ck_tip_template_group_clip">Video clips tagged with the same content will automatically be filled in with the same content</string>
    <string name="ck_tip_template_group_caption">Caption marked with the same content will  automatically be filled in with the same content</string>

    <string name="cut_same_clip_trim_in">trim in</string>
    <string name="cut_same_clip_trim_out">trim out</string>

    <string name="view_data_picker_title">Select inPoint of clip</string>
    <string name="hour">hou</string>
    <string name="minute">min</string>
    <string name="second">sec</string>
    <string name="frame">fra</string>

    <string name="cut_same_clip_duration">Clip duration：</string>
    <string name="cut_same_clip_nice_duration">Nice duration：</string>

    <string name="hint_template_upload_failed">Template Upload failed.</string>
    <string name="hint_template_export_failed">Template export failed.</string>
    <string name="hint_input_keyword_to_search_template">Input keyword to search template</string>
</resources>