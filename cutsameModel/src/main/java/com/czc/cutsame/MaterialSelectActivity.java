package com.czc.cutsame;

import static com.meicam.sdk.NvsAssetPackageManager.TEIMPLATE_FOOTAGE_TYPE_FREEZE_FRAME;
import static com.meicam.sdk.NvsAssetPackageManager.TEIMPLATE_FOOTAGE_TYPE_IMAGE;
import static com.meicam.sdk.NvsAssetPackageManager.TEIMPLATE_FOOTAGE_TYPE_VIDEO;
import static com.meicam.sdk.NvsAssetPackageManager.TEIMPLATE_FOOTAGE_TYPE_VIDEO_IMAGE;
import static com.meishe.logic.constant.PagerConstants.DATA_TEMPLATE;
import static com.meishe.logic.constant.PagerConstants.SELECTED_TYPE;
import static com.meishe.logic.constant.PagerConstants.TEMPLATE_CLIP;
import static com.meishe.logic.constant.PagerConstants.TEMPLATE_CLIP_LIST;
import static com.meishe.logic.constant.PagerConstants.TEMPLATE_ID;
import static com.meishe.logic.constant.PagerConstants.TEMPLATE_NEED_EDIT;
import static com.meishe.logic.constant.PagerConstants.TEMPLATE_RATIO;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.Parcelable;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewpager.widget.ViewPager;

import com.czc.cutsame.adapter.MediaSelectedAdapter;
import com.czc.cutsame.bean.Template;
import com.czc.cutsame.bean.TemplateClip;
import com.czc.cutsame.fragment.MediaFragment;
import com.czc.cutsame.util.ConfigUtil;
import com.czc.cutsame.view.SelectRatioDialog;
import com.czc.cutsame.view.VideoReverseDialog;
import com.meicam.sdk.NvsAVFileInfo;
import com.meicam.sdk.NvsStreamingContext;
import com.meicam.sdk.NvsTimeline;
import com.meicam.sdk.NvsTimelineAnimatedSticker;
import com.meicam.sdk.NvsTimelineCaption;
import com.meicam.sdk.NvsTimelineCompoundCaption;
import com.meicam.sdk.NvsTimelineVideoFx;
import com.meicam.sdk.NvsVideoClip;
import com.meicam.sdk.NvsVideoFx;
import com.meicam.sdk.NvsVideoTrack;
import com.meishe.base.adapter.CommonFragmentAdapter;
import com.meishe.base.bean.MediaData;
import com.meishe.base.bean.MediaTag;
import com.meishe.base.manager.LinearLayoutManagerWrapper;
import com.meishe.base.model.BaseActivity;
import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.LogUtils;
import com.meishe.base.utils.ToastUtils;
import com.meishe.base.view.decoration.ItemDecoration;
import com.meishe.engine.EditorEngine;
import com.meishe.engine.asset.AssetsManager;
import com.meishe.engine.asset.bean.CloudPathMapBean;
import com.meishe.engine.bean.AnimationData;
import com.meishe.engine.bean.MeicamTimeline;
import com.meishe.engine.bean.bridges.FileInfoBridge;
import com.meishe.engine.bean.template.RatioInfo;
import com.meishe.engine.constant.NvsConstants;
import com.meishe.engine.db.AssetEntity;
import com.meishe.engine.editor.EditorController;
import com.meishe.engine.util.ConvertFileManager;
import com.meishe.engine.util.RatioUtil;
import com.meishe.engine.util.WhiteList;
import com.meishe.engine.view.ConvertProgressPop;
import com.meishe.third.adpater.BaseQuickAdapter;
import com.meishe.third.pop.XPopup;
import com.meishe.third.tablayout.SlidingTabLayout;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;


/**
 * author : lhz
 * date   : 2020/10/20
 * desc   :素材选择页面 Material selection page
 */
public class MaterialSelectActivity extends BaseActivity {
    public final static int TYPE_DEFAULT = 0;

    /**
     * 选中一个一个就跑
     * Pick one and run
     */
    public final static int TYPE_ONE_FINISH = 1;
    private ImageView mIvBack;
    private TextView mTvNext;
    private RecyclerView mRvMediaList;
    private ProgressBar mLoadingView;

    private MediaSelectedAdapter mSelectedAdapter;
    private List<Fragment> mFragmentList = new ArrayList<>(3);
    private List<String> mTabTitleList = new ArrayList<>(3);
    private String mTemplateId;
    private Template mTemplate;
    private int mType;
    private List<TemplateClip> mClipList = null;
    private VideoReverseDialog videoReverseDialog = null;
    /**
     * 倒放成功数量 reverse success num
     */
    private int reverseSuccessNum = 0;
    private boolean mNeedEdit;

    @Override
    protected int bindLayout() {
        return R.layout.activity_template_material;
    }

    @Override
    protected void initData(Bundle savedInstanceState) {
        Intent intent = getIntent();
        if (intent != null) {
            mTemplate = intent.getParcelableExtra(DATA_TEMPLATE);
            Bundle bundle = intent.getExtras();
            if (bundle != null) {
                //类型 type
                mType = bundle.getInt(SELECTED_TYPE, TYPE_DEFAULT);
                if (mType == TYPE_DEFAULT) {
                    //默认类型需要模板路径 The default type requires a template path
                    mClipList = intent.getParcelableArrayListExtra(TEMPLATE_CLIP_LIST);
                    mTemplateId = intent.getStringExtra(TEMPLATE_ID);
                    mNeedEdit = intent.getBooleanExtra(TEMPLATE_NEED_EDIT, true);
                } else if (mType == TYPE_ONE_FINISH) {
                    //单选素材，需要片段 Radio selection material, need snippets
                    TemplateClip templateClip = bundle.getParcelable(TEMPLATE_CLIP);
                    mClipList = new ArrayList<>(1);
                    mClipList.add(templateClip);
                }
            }
        }
        mTabTitleList.clear();
        mTabTitleList.add(getResources().getString(R.string.material_all));
        mTabTitleList.add(getResources().getString(R.string.material_video));
        mTabTitleList.add(getResources().getString(R.string.material_photo));

        mFragmentList.clear();
        mFragmentList.add(MediaFragment.create(MediaData.TYPE_ALL, mMediaListener));
        mFragmentList.add(MediaFragment.create(MediaData.TYPE_VIDEO, mMediaListener));
        mFragmentList.add(MediaFragment.create(MediaData.TYPE_PHOTO, mMediaListener));
        mSelectedAdapter = new MediaSelectedAdapter();
    }

    @Override
    protected void initView() {
        mIvBack = findViewById(R.id.iv_back);
        SlidingTabLayout tabLayout = findViewById(R.id.tl_select_media);
        mLoadingView = findViewById(R.id.loading);
        ViewPager viewPager = findViewById(R.id.vp_select_media);
        // RelativeLayout flSelectedParent = findViewById(R.id.rl_media_selected);
        TextView tvMediaNum = findViewById(R.id.tv_selected_num);
        ImageView imagePoint = findViewById(R.id.image_point);
        mTvNext = findViewById(R.id.tv_next);
        mRvMediaList = findViewById(R.id.rv_selected_list);
        viewPager.setOffscreenPageLimit(3);
        viewPager.setAdapter(new CommonFragmentAdapter(getSupportFragmentManager(), mFragmentList));
        tabLayout.setViewPager(viewPager, mTabTitleList);

        if (mType == TYPE_DEFAULT || mType == TYPE_ONE_FINISH) {
            // 选则列表 Choose the list
            mRvMediaList.setLayoutManager(new LinearLayoutManagerWrapper(this, RecyclerView.HORIZONTAL, false));
            mRvMediaList.addItemDecoration(new ItemDecoration(10, 10));
            mRvMediaList.setAdapter(mSelectedAdapter);
            mTvNext.setEnabled(false);
            if (mClipList == null) {
                LogUtils.e("mClipList == null");
                return;
            }
            if (mClipList.size() == 0) {
                showSelectRatioView();
                dealNextDisplay(true);
                return;
            }
            mSelectedAdapter.setNewData(mClipList);
            tvMediaNum.setText(String.format(getString(R.string.selected_material_num_hint), mClipList.size()));
            if (mType != TYPE_ONE_FINISH) {
                for (TemplateClip templateClip : mClipList) {
                    if (templateClip.isHasGroup()) {
                        tvMediaNum.setText(getString(R.string.selected_same_assets));
                        imagePoint.setVisibility(View.VISIBLE);
                        break;
                    }
                }
            }
        }
        initListener();
    }

    private void initListener() {
        mIvBack.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onBackPressed();
            }
        });
        mTvNext.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                final List<TemplateClip> list = mSelectedAdapter.getData();
                if (ConfigUtil.needConvert()) {
                    goConvertOrNext(list);
                } else {
                    checkReverseOrNext(list);
                }
            }
        });
        mSelectedAdapter.setOnItemChildClickListener(new BaseQuickAdapter.OnItemChildClickListener() {
            @Override
            public void onItemChildClick(
                    @NonNull BaseQuickAdapter adapter, @NonNull View view, int position) {
                if (view.getVisibility() != View.VISIBLE) {
                    return;
                }
                TemplateClip item = mSelectedAdapter.getItem(position);
                if (item != null) {
                    String filePath = item.getFilePath();
                    for (int i = 0; i < mSelectedAdapter.getData().size(); i++) {
                        TemplateClip templateClip = mSelectedAdapter.getItem(i);
                        if (templateClip != null && templateClip.getFootageId().equals(item.getFootageId())) {
                            //注意此处，item中的关于媒体文件的属性被清空。
                            // Notice here that the properties of the media file in Item are cleared.
                            mSelectedAdapter.deleteClip(i);
                        }
                    }
                    if (!mSelectedAdapter.hasSameMedia(filePath)) {
                        dealMediaUnselected(item);
                    }
                    if (mSelectedAdapter.getSelectedPosition() >= 0) {
                        mRvMediaList.scrollToPosition(mSelectedAdapter.getSelectedPosition());
                        dealNextDisplay(false);
                    }
                }
            }
        });
    }

    private void checkReverseOrNext(final List<TemplateClip> list) {
        final List<Integer> listPosition = new ArrayList<>();
        reverseSuccessNum = 0;
        for (int i = 0; i < list.size(); i++) {
            TemplateClip clip = list.get(i);
            if (clip.getMediaType() == MediaData.TYPE_VIDEO && TextUtils.isEmpty(clip.getReversePath()) && clip.getNeedReverse()) {
                listPosition.add(i);
            }
        }

        if (listPosition.size() == 0) {
            goNext(list);
            return;
        }
        //需要反转的视频 Video that needs to be reversed.
        if (videoReverseDialog == null) {
            videoReverseDialog = (VideoReverseDialog) new XPopup.Builder(MaterialSelectActivity.this).asCustom(new VideoReverseDialog(MaterialSelectActivity.this));
            videoReverseDialog.setOnConvertListener(new VideoReverseDialog.OnConvertListener() {
                @Override
                public void onConvertFinish(String path, boolean convertSuccess) {
                    if (convertSuccess) {
                        TemplateClip clip = list.get(listPosition.get(reverseSuccessNum));
                        clip.setReversePath(path);
                        reverseSuccessNum++;
                        if (reverseSuccessNum == listPosition.size()) {
                            videoReverseDialog.dismiss();
                            if (mType == TYPE_ONE_FINISH) {
                                dealFinishResult(clip);
                            } else {
                                showSelectRatioView();
                            }
                        } else {
                            clip = list.get(listPosition.get(reverseSuccessNum));
                            videoReverseDialog.setPath(clip.getFilePath());
                            videoReverseDialog.doAfterShow();
                        }
                    } else {
                        videoReverseDialog.dismiss();
                        ToastUtils.showShort(R.string.video_reverse_fail);
                    }
                }
            });
        }
        videoReverseDialog.setPath(list.get(listPosition.get(0)).getFilePath());
        videoReverseDialog.show();
    }

    private void goConvertOrNext(final List<TemplateClip> list) {
        boolean needConvert = false;
        ConvertFileManager.ConvertParam convertParam = new ConvertFileManager.ConvertParam();
        for (TemplateClip templateClip : list) {
            String path = templateClip.getFilePath();
            if (WhiteList.isCovert4KFileWhiteList(path)) {
                needConvert = true;
                convertParam.appendParam(path, "", templateClip.getNeedReverse());
            }
        }
        if (needConvert) {
            convert(this, convertParam, new ConvertFileManager.EventListener() {
                @Override
                public void onConvertFinish(ConvertFileManager.ConvertParam convertParam, boolean convertSuccess) {
                    if (convertSuccess) {
                        if (convertParam != null) {
                            Map<String, ConvertFileManager.ConvertParam.Param> paramMap = convertParam.getParamMap();
                            if (paramMap != null) {
                                for (TemplateClip templateClip : list) {
                                    String filePath = templateClip.getFilePath();
                                    ConvertFileManager.ConvertParam.Param param = paramMap.get(filePath);
                                    if (param != null) {
                                        if (param.isReverseConvert()) {
                                            templateClip.setReversePath(param.getDstFile());
                                        } else {
                                            templateClip.setFilePath(param.getDstFile());
                                        }
                                    }
                                }
                            }
                        }
                        goNext(list);
                    } else {
                        ToastUtils.make()
                                .setGravity(Gravity.CENTER, 0, 0)
                                .setDurationIsLong(false)
                                .show(R.string.convert_failed);
                    }
                }
            });
        } else {
            checkReverseOrNext(list);
           // goNext(list);
        }
    }

    /**
     * Convert.
     * 转码
     *
     * @param context      the context 上下文
     * @param convertParam the convert parameter 转码参数
     * @param listener     the listener 转码回调
     */
    private void convert(Context context, ConvertFileManager.ConvertParam convertParam, ConvertFileManager.EventListener listener) {
        ConvertProgressPop.create(context, convertParam, listener).show();
    }

    private void goNext(List<TemplateClip> list) {
        if (mType == TYPE_ONE_FINISH) {
            dealFinishResult(list.get(0));
        } else {
            showSelectRatioView();
        }
    }

    public void showSelectRatioView() {
        int supportedAspectRatio = EditorController.getInstance().getAssetPackageSupportedAspectRatio(mTemplateId, EditorController.ASSET_PACKAGE_TYPE_TEMPLATE);
        if (mTemplate != null) {
            int defaultAspectRatio = mTemplate.getDefaultAspectRatio();
            if (defaultAspectRatio == supportedAspectRatio) {
                toCutSameEditor(defaultAspectRatio);
                return;
            }
            List<RatioInfo> ratioInfos = RatioUtil.getSupportedAspectRatios(defaultAspectRatio, supportedAspectRatio);
            SelectRatioDialog selectRatioDialog = (SelectRatioDialog) new XPopup.Builder(MaterialSelectActivity.this)
                    .asCustom(new SelectRatioDialog(MaterialSelectActivity.this, defaultAspectRatio, ratioInfos, new SelectRatioDialog.OnSelectRatioListener() {
                        @Override
                        public void onSelectRatio(int tag) {
                            toCutSameEditor(tag);
                        }
                    }));
            selectRatioDialog.show();
        } else {
            toCutSameEditor(RatioUtil.getARatioFromAspectRatios(supportedAspectRatio));
        }
    }

    private void toCutSameEditor(final int tag) {
        EditorController.getInstance().changeTemplateAspectRatio(mTemplateId, tag);
        List<NvsStreamingContext.templateFootageInfo> footageInfoList = new ArrayList<>();
        List<TemplateClip> mTemplateClips = mSelectedAdapter.getData();
        for (TemplateClip templateClip : mTemplateClips) {
            NvsStreamingContext.templateFootageInfo footageInfo = new NvsStreamingContext.templateFootageInfo();
            footageInfo.footageId = templateClip.getFootageId();
            footageInfo.reverseFilePath = templateClip.getReversePath();
            footageInfo.filePath = templateClip.getFilePath();
            footageInfoList.add(footageInfo);
        }
        FileInfoBridge.sCurrentProject = mTemplateId;
        final NvsTimeline timeline = NvsStreamingContext.getInstance().createTimeline(mTemplateId, footageInfoList, 0);
        if (timeline != null) {
            int count = timeline.videoTrackCount();
            for (int trackIndex = 0; trackIndex < count; trackIndex++) {
                NvsVideoTrack videoTrack = timeline.getVideoTrackByIndex(trackIndex);
                int clipCount = videoTrack.getClipCount();
                for (int clipIndex = 0; clipIndex < clipCount; clipIndex++) {
                    NvsVideoClip clip = videoTrack.getClipByIndex(clipIndex);
                    String filePath = clip.getFilePath();
                    if (!TextUtils.isEmpty(filePath)) {
                        FileInfoBridge.putFileInFo(mTemplateId, new FileInfoBridge.FileInfo(filePath).setFilePath(filePath).getAVFileInfo());
                    }
                }
            }
            List<String> assetsList = getAssetsList(timeline);
            if (CommonUtils.isEmpty(assetsList)) {
                goEditPage(tag, timeline);
                return;
            }
            mLoadingView.setVisibility(View.VISIBLE);
            AssetsManager.getAssetsUrl(assetsList, new AssetsManager.CloudMapRequestCallBack() {
                @Override
                public void onSuccess(CloudPathMapBean mapBeans) {
                    mLoadingView.setVisibility(View.GONE);
                    if (mapBeans != null) {
                        List<CloudPathMapBean.PathMap> elements = mapBeans.getElements();
                        if (!CommonUtils.isEmpty(elements)) {
                            for (CloudPathMapBean.PathMap element : elements) {
                                AssetEntity assets = AssetsManager.get().getAssets(element.getId());
                                if (assets == null) {
                                    assets = CloudPathMapBean.create(element);
                                    AssetsManager.get().insertAssets(assets);
                                }
                                FileInfoBridge.putFileInFo(mTemplateId, new FileInfoBridge.FileInfo(element.getId())
                                        .setFileName(element.getDisplayName())
                                        .setFileNameZh(element.getDisplayNameZhCn())
                                        .setFilePath(element.getCoverUrl())
                                        .setCategory(element.getCategory())
                                        .setType(element.getType())
                                        .setKind(element.getKind())
                                        .setCustomDisPlayName(element.getCustomDisPlayName()));
                            }
                        }
                    }
                    goEditPage(tag, timeline);
                }

                @Override
                public void onError() {
                    mLoadingView.setVisibility(View.GONE);
                    goEditPage(tag, timeline);
                }
            });
        }
    }

    private void goEditPage(int tag, NvsTimeline timeline) {
        MeicamTimeline meicamTimeline = EditorEngine.getInstance().recoverFromCloudDraft(timeline);
        EditorEngine.getInstance().setCurrentTimeline(meicamTimeline);
        EditorEngine.getInstance().checkTrackDuration();
        Intent it = new Intent(MaterialSelectActivity.this, CutSameEditorActivity.class);
        if (mSelectedAdapter != null) {
            it.putParcelableArrayListExtra(TEMPLATE_CLIP_LIST, (ArrayList<? extends Parcelable>) mSelectedAdapter.getData());
        }
        it.putExtra(TEMPLATE_ID, mTemplateId);
        it.putExtra(TEMPLATE_NEED_EDIT, mNeedEdit);
        it.putExtra(TEMPLATE_RATIO, tag);
        startActivity(it);
        finish();
    }

    @Override
    protected void requestData() {

    }

    /**
     * Get all package info
     * 获取所有的资源包信息
     *
     * @return package info list 资源包信息集合
     */
    private List<String> getAssetsList(NvsTimeline timeline) {
        Set<String> filterSet = new HashSet<>();
        /*
         * 动画和滤镜
         * Animation and filter
         */
        for (int i = 0; i < timeline.videoTrackCount(); i++) {
            NvsVideoTrack nvsVideoTrack = timeline.getVideoTrackByIndex(i);
            for (int j = 0; j < nvsVideoTrack.getClipCount(); j++) {
                NvsVideoClip nvsVideoClip = nvsVideoTrack.getClipByIndex(j);

                /*
                 * 动画
                 * Animation
                 * */
                AnimationData animationData = getVideoClipAnimation(nvsVideoClip);
                if (animationData != null) {
                    addToSet(filterSet, animationData.getPackageID());
                    addToSet(filterSet, animationData.getPackageID2());
                }
                /*
                 * 滤镜
                 * Filter
                 * */
                for (int k = 0; k < nvsVideoClip.getFxCount(); k++) {
                    NvsVideoFx videoFx = nvsVideoClip.getFxByIndex(k);
                    if (videoFx == null) {
                        continue;
                    }
                    addToSet(filterSet, videoFx.getVideoFxPackageId());
                }
            }
        }


        /*
         * 时间线滤镜
         * Timeline  filter
         */

        NvsTimelineVideoFx firstTimelineVideoFx = timeline.getFirstTimelineVideoFx();
        while (firstTimelineVideoFx != null) {
            addToSet(filterSet, firstTimelineVideoFx.getTimelineVideoFxPackageId());
            firstTimelineVideoFx = timeline.getNextTimelineVideoFx(firstTimelineVideoFx);
        }

        /*
         * 字幕和贴纸
         * Caption and sticker
         */
        NvsTimelineCompoundCaption compoundCaption = timeline.getFirstCompoundCaption();
        while (compoundCaption != null) {
            addToSet(filterSet, compoundCaption.getCaptionStylePackageId());
            compoundCaption = timeline.getNextCaption(compoundCaption);
        }

        NvsTimelineCaption firstCaption = timeline.getFirstCaption();
        while (firstCaption != null) {
            addToSet(filterSet, firstCaption.getCaptionStylePackageId());
            addToSet(filterSet, firstCaption.getModularCaptionAnimationPackageId());
            addToSet(filterSet, firstCaption.getModularCaptionContextPackageId());
            addToSet(filterSet, firstCaption.getModularCaptionInAnimationPackageId());
            addToSet(filterSet, firstCaption.getModularCaptionOutAnimationPackageId());
            addToSet(filterSet, firstCaption.getModularCaptionRendererPackageId());
            firstCaption = timeline.getNextCaption(firstCaption);
        }
        NvsTimelineAnimatedSticker firstAnimatedSticker = timeline.getFirstAnimatedSticker();
        while (firstAnimatedSticker != null) {
            addToSet(filterSet, firstAnimatedSticker.getAnimatedStickerPackageId());
            addToSet(filterSet, firstAnimatedSticker.getAnimatedStickerPeriodAnimationPackageId());
            addToSet(filterSet, firstAnimatedSticker.getAnimatedStickerInAnimationPackageId());
            addToSet(filterSet, firstAnimatedSticker.getAnimatedStickerOutAnimationPackageId());
            firstAnimatedSticker = timeline.getNextAnimatedSticker(firstAnimatedSticker);
        }
        if (!filterSet.isEmpty()) {
            List<String> data = new ArrayList<>(filterSet);
            return data;
        }
        return null;
    }


    private void addToSet(Set<String> set, String packageId) {
        if (TextUtils.isEmpty(packageId)) {
            return;
        }
        set.add(packageId);
    }

    /**
     * 获取clip的动画
     * Gets clip animation.
     *
     * @param videoClip the video clip
     * @return the clip animation
     */
    public AnimationData getVideoClipAnimation(NvsVideoClip videoClip) {
        AnimationData animationData = new AnimationData();
        NvsVideoFx videoFx = videoClip.getPropertyVideoFx();
        if (videoFx == null) {
            return animationData;
        }
        String packageID, postPackageID;
        packageID = videoFx.getStringVal(NvsConstants.PACKAGE_ID);
        if (TextUtils.isEmpty(packageID)) {
            postPackageID = videoFx.getStringVal(NvsConstants.POST_PACKAGE_ID);
            if (!TextUtils.isEmpty(postPackageID)) {
                animationData.setPackageID(postPackageID);
                animationData.setInPoint((long) videoFx.getFloatVal(NvsConstants.PACKAGE_EFFECT_IN));
                animationData.setOutPoint((long) videoFx.getFloatVal(NvsConstants.PACKAGE_EFFECT_OUT));
                animationData.setIsAnimationIn(videoFx.getBooleanVal(NvsConstants.PACKAGE_TYPE_ANIMATION_IN));
            }
        } else {
            animationData.setPackageID(packageID);
            animationData.setInPoint((long) videoFx.getFloatVal(NvsConstants.PACKAGE_EFFECT_IN));
            animationData.setOutPoint((long) videoFx.getFloatVal(NvsConstants.PACKAGE_EFFECT_OUT));
            animationData.setIsAnimationIn(videoFx.getBooleanVal(NvsConstants.PACKAGE_TYPE_ANIMATION_IN));
        }
        String package2ID, postPackage2ID;
        package2ID = videoFx.getStringVal(NvsConstants.PACKAGE2_ID);
        if (TextUtils.isEmpty(package2ID)) {
            postPackage2ID = videoFx.getStringVal(NvsConstants.POST_PACKAGE2_ID);
            if (!TextUtils.isEmpty(postPackage2ID)) {
                animationData.setPackageID2(postPackage2ID);
                animationData.setInPoint2((long) videoFx.getFloatVal(NvsConstants.PACKAGE2_EFFECT_IN));
                animationData.setOutPoint2((long) videoFx.getFloatVal(NvsConstants.PACKAGE2_EFFECT_OUT));
            }
        } else {
            animationData.setPackageID2(package2ID);
            animationData.setInPoint2((long) videoFx.getFloatVal(NvsConstants.PACKAGE2_EFFECT_IN));
            animationData.setOutPoint2((long) videoFx.getFloatVal(NvsConstants.PACKAGE2_EFFECT_OUT));
        }
        return animationData;
    }

    /**
     * 处理媒体未选中
     * Handle media unchecked
     */
    private void dealMediaUnselected(TemplateClip clip) {
        if (clip != null) {
            int[] tag = (int[]) clip.getTag();
            for (int i = 0; i < mFragmentList.size(); i++) {
                Fragment fragment = mFragmentList.get(i);
                if (fragment != null) {
                    ((MediaFragment) fragment).dealSelectedState(tag[i], false);
                    tag[i] = -1;
                }
            }
            dealNextDisplay(false);
        }

    }

    /**
     * 处理媒体选中
     * Handle media selection
     */
    private void dealMediaSelect(TemplateClip clip, MediaData mediaData) {
        if (clip.getTag() == null) {
            clip.setTag(new int[]{-1, -1, -1});
        }
        int[] index = (int[]) clip.getTag();
        MediaTag tag = (MediaTag) mediaData.getTag();//tag为空说明有问题。 A null tag indicates a problem.
        if (MediaData.TYPE_ALL == tag.getType()) {
            index[0] = tag.getIndex();
            index[1] = ((MediaFragment) mFragmentList.get(1)).dealSelected(mediaData.getThumbPath());
            index[2] = ((MediaFragment) mFragmentList.get(2)).dealSelected(mediaData.getThumbPath());
        } else if (MediaData.TYPE_PHOTO == tag.getType()) {
            index[2] = tag.getIndex();
            index[0] = ((MediaFragment) mFragmentList.get(0)).dealSelected(mediaData.getThumbPath());
        } else {
            index[1] = tag.getIndex();
            index[0] = ((MediaFragment) mFragmentList.get(0)).dealSelected(mediaData.getThumbPath());
        }
    }

    /**
     * 处理下一步的展示样式
     * Handle the next presentation style
     */
    private void dealNextDisplay(boolean enable) {
        if (mTvNext.isEnabled() == enable) {
            return;
        }
        if (enable) {
            mTvNext.setBackgroundResource(R.drawable.bg_rectangle_round_red365);
            mTvNext.setTextColor(getResources().getColor(R.color.white));
        } else {
            mTvNext.setBackgroundResource(R.drawable.bg_rectangle_round_gray4b4);
            mTvNext.setTextColor(getResources().getColor(R.color.gray_a4a));
        }
        mTvNext.setEnabled(enable);
    }

    @Override
    public void onRequestPermissionsResult(int requestCode,
                                           @NonNull String[] permissions,
                                           @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        //判断如果同意的情况下就去把权限请求设置给当前fragment的 Set the request to the current fragment if it is approved
        for (int i = 0; i < mFragmentList.size(); i++) {
            mFragmentList.get(i).onRequestPermissionsResult(requestCode, permissions, grantResults);
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        for (int i = 0; i < mFragmentList.size(); i++) {
            mFragmentList.get(i).onActivityResult(requestCode, resultCode, data);
        }
    }

    /**
     * 处理选择一个素材就finish的结果
     * Handle the result of selecting an ingredient on Finish
     */
    private void dealFinishResult(TemplateClip clip) {
        Intent it = new Intent();
        it.putExtra(TEMPLATE_CLIP, clip);
        setResult(RESULT_OK, it);
        finish();
    }

    private MediaFragment.MediaChangeListener mMediaListener = new MediaFragment.MediaChangeListener() {
        @Override
        public boolean onMediaChange(final MediaData mediaData) {
            final TemplateClip clip = mSelectedAdapter.getItem(mSelectedAdapter.getSelectedPosition());
            long duration = 0;
            if (clip != null) {
                NvsAVFileInfo nvsAVFileInfo = EditorController.getInstance().getFileInfo(mediaData.getPath());
                if (nvsAVFileInfo == null) {
                    return false;
                }
                duration = nvsAVFileInfo.getDuration();
                if ((clip.getType() == TEIMPLATE_FOOTAGE_TYPE_VIDEO) || (clip.getType() == TEIMPLATE_FOOTAGE_TYPE_FREEZE_FRAME)) {
                    if (mediaData.getType() == MediaData.TYPE_VIDEO && clip.getTrimDuration() > duration) {
                        //类型匹配但是时长过短 The type matches but the length is too short
                        ToastUtils.showShort(R.string.video_too_short);
                        return false;
                    } else if (mediaData.getType() == MediaData.TYPE_PHOTO) {
                        //类型不匹配 type mismatch
                        ToastUtils.showShort(R.string.clip_need_video);
                        return false;
                    }
                } else if (clip.getType() == TEIMPLATE_FOOTAGE_TYPE_IMAGE) {
                    if (mediaData.getType() == MediaData.TYPE_VIDEO) {
                        //类型不匹配 type mismatch
                        ToastUtils.showShort(R.string.clip_need_photo);
                        return false;
                    }
                } else if (clip.getType() == TEIMPLATE_FOOTAGE_TYPE_VIDEO_IMAGE) {
                    if (mediaData.getType() == MediaData.TYPE_VIDEO && clip.getTrimDuration() > duration) {
                        //类型匹配但是时长过短 The type matches but the length is too short
                        ToastUtils.showShort(R.string.video_too_short);
                        return false;
                    }
                } else {
                    //类型错了，这里基本上0概率 Wrong type, basically zero probability here
                    return false;
                }
                // LogUtils.d("type=" + clip.getType());
            } else {
                return false;
            }
            if (mType == TYPE_DEFAULT || mType == TYPE_ONE_FINISH) {
                List<TemplateClip> list = mSelectedAdapter.getData();
                if (mediaData.getType() != MediaData.TYPE_PHOTO) {
//                    setMediaSelected(clip, mediaData, -1);
                    long maxDuration = 0;
                    for (int i = 0; i < list.size(); i++) {
                        TemplateClip templateClip = list.get(i);
                        if (templateClip.getFootageId().equals(clip.getFootageId())) {
                            maxDuration = Math.max(maxDuration, templateClip.getTrimDuration());
                        }
                    }
                    if (maxDuration > duration) {
                        //类型匹配但是时长过短 The type matches but the length is too short
                        ToastUtils.showShort(R.string.video_too_short);
                        return true;
                    }
                }
                for (int i = 0; i < list.size(); i++) {
                    TemplateClip templateClip = list.get(i);
                    if (templateClip.getFootageId().equals(clip.getFootageId())) {

                        if (TextUtils.isEmpty(templateClip.getFilePath())) {
                            setMediaSelected(templateClip, mediaData, i);
                        }
                    }
                }

            }
            return true;
        }
    };

    public void setMediaSelected(TemplateClip templateClip, MediaData mediaData, int position) {
        templateClip.setMediaType(mediaData.getType());
        dealMediaSelect(templateClip, mediaData);
        if (templateClip.isHasGroup() && position != -1) {
            mSelectedAdapter.setSelected(mediaData, position);
        } else {
            mSelectedAdapter.setSelected(mediaData);
        }
        if (mSelectedAdapter.getSelectedPosition() == -1) {
            dealNextDisplay(true);
            mRvMediaList.scrollToPosition(mSelectedAdapter.getData().size() - 1);
        } else {
            mRvMediaList.scrollToPosition(mSelectedAdapter.getSelectedPosition());
        }
    }
}
