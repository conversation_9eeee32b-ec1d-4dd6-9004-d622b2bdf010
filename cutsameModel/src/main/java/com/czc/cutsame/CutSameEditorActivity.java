package com.czc.cutsame;

import android.app.Activity;
import android.content.Intent;
import android.graphics.PointF;
import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.SeekBar;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.viewpager.widget.ViewPager;

import com.czc.cutsame.bean.TemplateClip;
import com.czc.cutsame.bean.TransformData;
import com.czc.cutsame.fragment.CaptionEditVideoFragment;
import com.czc.cutsame.fragment.CutEditorVpFragment;
import com.czc.cutsame.fragment.adapter.CommonFragmentCustomTabAdapter;
import com.czc.cutsame.fragment.interf.CaptionOperationListener;
import com.czc.cutsame.fragment.iview.CutSameEditorView;
import com.czc.cutsame.fragment.presenter.CutEditorVpPresenter;
import com.czc.cutsame.fragment.presenter.CutSameEditorPresenter;
import com.czc.cutsame.view.ResolutionBottomDialog;
import com.meishe.player.view.TailorView;
import com.google.android.material.tabs.TabLayout;
import com.meicam.sdk.NvsTimeline;
import com.meishe.base.manager.AppManager;
import com.meishe.base.model.BaseMvpActivity;
import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.FormatUtils;
import com.meishe.base.utils.LogUtils;
import com.meishe.base.utils.ScreenUtils;
import com.meishe.base.utils.Utils;
import com.meishe.base.view.PlayControlView;
import com.meishe.engine.EditorEngine;
import com.meishe.engine.editor.EditorController;
import com.meishe.engine.interf.VideoFragmentListenerWithClick;

import java.util.ArrayList;
import java.util.List;

import static com.czc.cutsame.TailorActivity.INTENT_RECT_DATA;
import static com.czc.cutsame.TailorActivity.INTENT_TRAM;
import static com.meishe.logic.constant.PagerConstants.FROM_PAGE;
import static com.meishe.logic.constant.PagerConstants.FROM_TEMPLATE;
import static com.meishe.logic.constant.PagerConstants.REQUEST_CODE_1;
import static com.meishe.logic.constant.PagerConstants.TEMPLATE_CLIP;
import static com.meishe.logic.constant.PagerConstants.TEMPLATE_ID;
import static com.meishe.logic.constant.PagerConstants.TEMPLATE_NEED_EDIT;

/**
 * All rights Reserved, Designed By www.meishesdk.com
 *
 * <AUTHOR> CaoZhiChao
 * @CreateDate : 2020/11/27 16:29
 * @Description : 通用模板的编辑 edit activity
 * @Copyright : www.meishesdk.com Inc. All rights reserved.
 */
public class CutSameEditorActivity extends BaseMvpActivity<CutSameEditorPresenter> implements CutSameEditorView, VideoFragmentListenerWithClick, CutEditorVpFragment.SeekPositionListener, CaptionOperationListener {
    public static final int INTENT_FLAG = 10;
    private CaptionEditVideoFragment mBaseVideoFragment;
    private TabLayout mTableLayout;
    private ViewPager mCutEditorViewPager;
    private List<CutEditorVpFragment> mFragmentList = new ArrayList<>();
    private List<String> mTabTitleList = new ArrayList<>();
    private PlayControlView mPlayControlView;
    private int mState = -1;
    private String mTemplateId;
    private ResolutionBottomDialog mResolutionBottomDialog;
    private long mJumpTime = 0;
    private boolean mNeedEdit;

    @Override
    protected int bindLayout() {
        return R.layout.activity_cut_same_editor;
    }
    @Override
    protected void initData(Bundle savedInstanceState) {
        Intent intent = getIntent();
        if (intent != null) {
            mTemplateId = intent.getStringExtra(TEMPLATE_ID);
            mNeedEdit = intent.getBooleanExtra(TEMPLATE_NEED_EDIT, true);
        }
        mPresenter.initData(intent);
        FragmentManager supportFragmentManager = getSupportFragmentManager();
        List<Fragment> fragments = supportFragmentManager.getFragments();
        if (!CommonUtils.isEmpty(fragments)) {
            for (Fragment fragment : fragments) {
                supportFragmentManager.beginTransaction().remove(fragment).commitAllowingStateLoss();
                LogUtils.d("remove fragment:" + fragment);
            }
        }
    }

    @Override
    protected void initView() {
        addVideoFragment();
        findViewById(R.id.cut_editor_import).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mResolutionBottomDialog == null) {
                    mResolutionBottomDialog = new ResolutionBottomDialog(CutSameEditorActivity.this);
                    mResolutionBottomDialog.setResolutionBottomDialogListener(new ResolutionBottomDialog.ResolutionBottomDialogListener() {

                        @Override
                        public void onDone(int resolution) {
                            if (Utils.isFastClick()) {
                                return;
                            }
                            Bundle bundle = new Bundle();
                            bundle.putString(CutCompileActivity.TEMPLATE_ID, mTemplateId);
                            bundle.putInt(CutCompileActivity.COMPILE_RESOLUTION, resolution);
                            AppManager.getInstance().jumpActivity(CutSameEditorActivity.this, CutCompileActivity.class, bundle);
                        }
                    });
                }
                mResolutionBottomDialog.show();
            }
        });
        View editDetailView = findViewById(R.id.cut_edit_detail);
        if (!mNeedEdit) {
            editDetailView.setVisibility(View.GONE);
        }
        editDetailView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                try {
                    EditorController.getInstance().seekTimeline(0);
                    Class<?> aClass = Class.forName("com.meishe.myvideo.activity.DraftEditActivity");
                    EditorEngine.getInstance().setCurrentTimeline(EditorController.getInstance().getNvsTimeline());
                    Intent intent = new Intent(CutSameEditorActivity.this, aClass);
                    intent.putExtra(FROM_PAGE, FROM_TEMPLATE);
                    startActivity(intent);
                    finish();
                } catch (ClassNotFoundException e) {
                    LogUtils.e(e);
                }
            }
        });
        mTableLayout = findViewById(R.id.cut_editor_tab);
        mCutEditorViewPager = findViewById(R.id.cut_editor_view_pager);
        RelativeLayout realTitleBar = findViewById(R.id.real_titlebar);
        LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) realTitleBar.getLayoutParams();
        layoutParams.topMargin = ScreenUtils.getStatusBarHeight();
        realTitleBar.setLayoutParams(layoutParams);
        initViewPager();
        ImageView cutEditorClose = findViewById(R.id.cut_editor_close);
        cutEditorClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
        mPlayControlView = findViewById(R.id.cut_editor_play_view);
        initPlayView();
    }

    private void initPlayView() {
        int duration = (int) (EditorController.getInstance().getTimelineDuration() / 1000f);
        mPlayControlView.setMax(duration);
        String nvStringTime = FormatUtils.microsecond2Time(EditorController.getInstance().getTimelineDuration());
        mPlayControlView.setStartText("00:00");
        mPlayControlView.setCurrentText(nvStringTime);
        mPlayControlView.setListener(new PlayControlView.OnSeekBarListener() {
            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                EditorController.getInstance().stop();
                mState = TailorView.FROM_USER;
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {

            }

            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                long nvTime = progress * 1000;
                String nvStringTime = FormatUtils.microsecond2Time(nvTime);
                mPlayControlView.setStartText(nvStringTime);
                if (mState != TailorView.FROM_VIDEO) {
                    EditorController.getInstance().seekTimeline(nvTime);
                }
            }
        });
        mPlayControlView.setOnPlayClickListener(new PlayControlView.OnPlayClickListener() {
            @Override
            public void onPlayClick() {
                if (EditorController.getInstance().isPlaying()) {
                    EditorController.getInstance().stop();
                } else {
                    EditorController.getInstance().playNow();
                }
            }
        });
    }

    private void initViewPager() {
        boolean addCaption = !mPresenter.noCaption();
        mTabTitleList.clear();
        mTabTitleList.add(getResources().getString(R.string.activity_cut_editor_video));
        if (addCaption) {
            mTabTitleList.add(getResources().getString(R.string.activity_cut_editor_text));
        }

        mFragmentList.clear();
        mFragmentList.add(CutEditorVpFragment.newInstance(CutEditorVpPresenter.VIDEO, mTemplateId, this));
        if (addCaption) {
            mFragmentList.add(CutEditorVpFragment.newInstance(CutEditorVpPresenter.CAPTION, mTemplateId, this));
        }

        mCutEditorViewPager.setOffscreenPageLimit(mFragmentList.size());
        CommonFragmentCustomTabAdapter commonFragmentCustomTabAdapter = new CommonFragmentCustomTabAdapter(getSupportFragmentManager(), getApplicationContext(), R.layout.cut_same_tablayout_tab_layout, mFragmentList, mTabTitleList);
        mCutEditorViewPager.setAdapter(commonFragmentCustomTabAdapter);
        mTableLayout.setupWithViewPager(mCutEditorViewPager);
        for (int i = 0; i < mTableLayout.getTabCount(); i++) {
            TabLayout.Tab tab = mTableLayout.getTabAt(i);
            if (tab == null) {
                return;
            }
            tab.setCustomView(commonFragmentCustomTabAdapter.getTabView(i));
            if (tab.getCustomView() == null) {
                return;
            }
            TextView textView = tab.getCustomView().findViewById(R.id.cut_editor_tab_title);
            ImageView imageView = tab.getCustomView().findViewById(R.id.cut_editor_tab_img);
            if (i == 0) {
                textView.setText(R.string.activity_cut_editor_video);
                textView.setTextColor(getResources().getColor(R.color.activity_tailor_button_background));
                imageView.setBackgroundResource(R.drawable.cut_editor_tab_video_select);
            } else {
                textView.setText(R.string.activity_cut_editor_text);
                textView.setTextColor(getResources().getColor(R.color.white));
                imageView.setBackgroundResource(R.drawable.cut_editor_tab_text_unselect);
            }
        }
        mTableLayout.addOnTabSelectedListener(new TabLayout.OnTabSelectedListener() {
            @Override
            public void onTabSelected(TabLayout.Tab tab) {
                if (tab.getCustomView() == null) {
                    return;
                }
                int position = tab.getPosition();
                if (position == 0) {
                    if (mBaseVideoFragment != null) {
                        mBaseVideoFragment.notShowCaptionBox();
                    }
                    tab.getCustomView().findViewById(R.id.cut_editor_tab_img).setBackgroundResource(R.drawable.cut_editor_tab_video_select);
                } else if (position == 1) {
                    tab.getCustomView().findViewById(R.id.cut_editor_tab_img).setBackgroundResource(R.drawable.cut_editor_tab_text_select);
                }
                TextView textView = tab.getCustomView().findViewById(R.id.cut_editor_tab_title);
                textView.setTextColor(getResources().getColor(R.color.activity_tailor_button_background));
            }

            @Override
            public void onTabUnselected(TabLayout.Tab tab) {
                if (tab.getCustomView() == null) {
                    return;
                }
                int position = tab.getPosition();
                if (position == 0) {
                    tab.getCustomView().findViewById(R.id.cut_editor_tab_img).setBackgroundResource(R.drawable.cut_editor_tab_video_unselect);
                } else if (position == 1) {
                    tab.getCustomView().findViewById(R.id.cut_editor_tab_img).setBackgroundResource(R.drawable.cut_editor_tab_text_unselect);
                }
                TextView textView = tab.getCustomView().findViewById(R.id.cut_editor_tab_title);
                textView.setTextColor(getResources().getColor(R.color.white));
            }

            @Override
            public void onTabReselected(TabLayout.Tab tab) {

            }
        });

    }

    private void addVideoFragment() {
        FragmentManager fragmentManager = getSupportFragmentManager();
        mBaseVideoFragment = CaptionEditVideoFragment.newInstance(false);
        mBaseVideoFragment.setVideoFragmentListener(this);
        fragmentManager.beginTransaction().add(R.id.cut_editor_fragment_container, mBaseVideoFragment).commitAllowingStateLoss();
        fragmentManager.beginTransaction().show(mBaseVideoFragment);
    }

    @Override
    public void playBackEOF(NvsTimeline timeline) {
        if (mBaseVideoFragment != null) {
            notShowCaptionBox();
            mPlayControlView.setProgress((int) (EditorController.getInstance().getTimelineDuration() / 1000f));
        }
    }

    @Override
    public void playStopped(NvsTimeline timeline) {

    }

    @Override
    public void playbackTimelinePosition(NvsTimeline timeline, long stamp) {
        mState = TailorView.FROM_VIDEO;
        mPlayControlView.setProgress((int) (stamp / 1000f));
    }

    @Override
    public void streamingEngineStateChanged(int state) {
        boolean playing = EditorController.getInstance().isPlaying();
        if (mBaseVideoFragment != null) {
            if (playing) {
                mBaseVideoFragment.notShowCaptionBox();
            }
        }
        mPlayControlView.changPlayState(playing);
        if (playing) {
            mState = TailorView.FROM_VIDEO;
            for (Fragment fragment : mFragmentList) {
                CutEditorVpFragment cutEditorVpFragment = (CutEditorVpFragment) fragment;
                cutEditorVpFragment.clearSelectOnPlay();
            }
        }
    }

    @Override
    public void onSeekingTimelinePosition(NvsTimeline timeline, long position) {
//        if (mState != TailorView.FROM_USER){
//            mPlayControlView.setProgress(TimeFormatUtil.formatMsToMillisecond(position));
//        }
    }

    @Override
    public boolean clickPlayButtonByOthers() {
        return false;
    }

    @Override
    public boolean clickLiveWindowByOthers() {
        return false;
    }

    @Override
    public void connectTimelineWithLiveWindow() {
        if (mBaseVideoFragment != null) {
            mBaseVideoFragment.setCaptionOperationListener(this);
        }
    }

    public TemplateClip getTemplateClip(List<Integer> clipList, List<Integer> trackList, int trackIndex, long inPoint) {
        return mPresenter.getTemplateClip(clipList, trackList, trackIndex, inPoint);
    }

    public void showCaptionBox(List<PointF> list, float scaleValue) {
        mBaseVideoFragment.showCaptionBox(list, scaleValue);
    }

    public void showCompoundCaptionBox(List<PointF> list, List<List<PointF>> childPoints, float scaleValue) {
        mBaseVideoFragment.showCaptionBox(list, childPoints, scaleValue);
    }

    public void notShowCaptionBox() {
        mBaseVideoFragment.notShowCaptionBox();
    }

    @Override
    protected void onPause() {
        super.onPause();
        mJumpTime = EditorController.getInstance().nowTime();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        //返回的结果是来自于Activity B
        // The returned result is from Activity B.
        if (requestCode == INTENT_FLAG) {
            if (resultCode == Activity.RESULT_OK) {
                mPresenter.updateTimeline();
                long newTrim = data != null ? data.getLongExtra(INTENT_TRAM, 0) : 0;
                TransformData transformData = data.getParcelableExtra(INTENT_RECT_DATA);
//
                CutEditorVpFragment cutEditorVpFragment = mFragmentList.get(mTableLayout.getSelectedTabPosition());
                if (transformData != null) {
                    mPresenter.changClipRect(transformData, cutEditorVpFragment.getSelectedClipInfo(), EditorController.getInstance().getNvsTimeline().getVideoResolution());
                }
                cutEditorVpFragment.changClipTrim(newTrim);
            } else if (resultCode == Activity.RESULT_CANCELED) {
                mPresenter.updateTimeline();
                //mBeforeTimeLine.loadTimelineToTemplate();
            }
            EditorController.getInstance().seekTimeline(mJumpTime);
            setPlayControlViewProgress(mJumpTime);
        } else if (requestCode == REQUEST_CODE_1) {
            if (data != null) {
                TemplateClip templateClip = data.getParcelableExtra(TEMPLATE_CLIP);
                mPresenter.updateTemplateClip(templateClip);
            }
        }
        for (Fragment fragment : mFragmentList) {
            fragment.onActivityResult(requestCode, resultCode, data);
        }
    }

    @Override
    protected void onStop() {
        super.onStop();
        if (mResolutionBottomDialog != null) {
            mResolutionBottomDialog.dismiss();
        }
    }

    @Override
    public void seekPosition(long position) {
        setPlayControlViewProgress(position);
    }

    @Override
    public void reConnectTimelineWithLiveWindow() {
        mBaseVideoFragment.connectTimelineWithLiveWindow();
    }

    private void setPlayControlViewProgress(long position) {
//        if (mState != TailorView.FROM_USER) {
        mPlayControlView.setProgress((int) (position / 1000f));
//        }
    }

    @Override
    public void onCaptionClicked(int captionIndex) {
        if (mFragmentList != null && mFragmentList.size() > 1) {
            CutEditorVpFragment cutEditorVpFragment = mFragmentList.get(1);
            cutEditorVpFragment.showCaptionEditPop(captionIndex);
        }
    }
}