package com.czc.cutsame;

import com.meishe.base.utils.Utils;
import com.meishe.logic.utils.NvsServerClient;
import com.meishe.net.custom.RequestCallback;
import com.meishe.net.custom.SimpleDownListener;

import java.util.HashMap;
import java.util.Map;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> <PERSON><PERSON><PERSON>Zhou
 * @CreateDate :2020/12/8 13:42
 * @Description :通用模板模块的网络请求接口api
 * Cut the same module network request interface API
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class CutSameNetApi {
    /**
     * 获取特效素材资源列表
     * Gets a list of effects material resources
     * @param tag         Object 请求标识
     * @param type        int  资源类型
     * @param aspectRatio int 资源比例
     * @param categoryId  int 资源分类id
     * @param page        int 资源页数
     * @param pageSize    int 资源页数大小
     * @param callback    RequestCallback 请求回调
     */
    public static void getMaterialList(Object tag, int type, int aspectRatio, int categoryId,
                                       int page, int pageSize, RequestCallback<?> callback) {
        Map<String, String> params = new HashMap<>(2);
        params.put("command", "listMaterial");
        params.put("acceptAspectRatio", String.valueOf(aspectRatio));
        params.put("category", String.valueOf(categoryId));
        params.put("page", String.valueOf(page));
        params.put("pageSize", String.valueOf(pageSize));
        params.put("type", String.valueOf(type));
        params.put("lang", Utils.isZh() ? "zh_CN" : "en");
        String apiName = "materialinfo/index.php";
        NvsServerClient.get().requestGet(tag, apiName, params, callback);
    }

    /**
     * 获取模板分类列表
     * Gets a list of template categories
     * @param tag      Object 请求标识
     * @param callback RequestCallback 请求回调
     */
    public static void getTemplateCategory(Object tag, RequestCallback<?> callback) {
        Map<String, String> params = new HashMap<>(2);
        params.put("type", "19");
        params.put("lang", Utils.isZh() ? "zh_CN" : "en");
        String apiName = "api/my/template/listCategories";
        NvsServerClient.get().requestGet(tag, apiName, params, callback);
    }

    /**
     * 获取模板对应分类的列表
     * Gets a list of the corresponding categories for the template
     * @param tag        Object 请求标识
     * @param page       int 请求页数
     * @param pageSize   int 页数的大小
     * @param categoryId String 模板分类id
     * @param callback   RequestCallback 请求回调
     */
    public static void getTemplateList(Object tag, int page, int pageSize, String categoryId, RequestCallback<?> callback) {
        Map<String, String> params = new HashMap<>(6);
        params.put("type", "19");
        params.put("category", categoryId);
        params.put("keyword", "");
        params.put("pageNum", String.valueOf(page));
        params.put("pageSize", String.valueOf(pageSize));
        params.put("lang", Utils.isZh() ? "zh_CN" : "en");
        String apiName = "api/my/template/listTemplatesFinal";
        NvsServerClient.get().requestGet(tag, apiName, params, callback);
    }

    /**
     * 通知服务器被使用的模板
     * Notifies the server of the template being used
     * @param tag        Object 请求标识
     * @param templateId String 模板id
     * @param callback   RequestCallback 请求回调
     */
    public static void uploadUsedTemplate(Object tag, String templateId, RequestCallback<?> callback) {
        Map<String, Object> params = new HashMap<>(2);
        params.put("id", templateId);
        params.put("action", 1);
        String apiName = "materialcenter/myvideo/templateInteraction";
        NvsServerClient.get().requestPost(tag, NvsServerClient.DEFAULT_URL, apiName, params, callback);
    }

    /**
     * 下载实现方法
     * Download implementation method
     * @param tag      下载标识
     * @param url      下载地址
     * @param filePath 文件路径
     * @param fileName 文件名
     * @param listener 下载监听器
     */
    public static void download(String tag, String url, String filePath, String fileName, SimpleDownListener listener) {
        NvsServerClient.get().download(tag, url, filePath, fileName, listener);
    }

    /**
     * 取消所有请求
     * Cancel all requests
     */
    public static void cancelAll() {
        NvsServerClient.get().cancelAll();
    }

    /**
     * 取消某个请求
     * Cancel a request
     * @param tag 唯一标识
     */
    public static void cancelRequest(Object tag) {
        NvsServerClient.get().cancelRequest(tag);
    }
}
