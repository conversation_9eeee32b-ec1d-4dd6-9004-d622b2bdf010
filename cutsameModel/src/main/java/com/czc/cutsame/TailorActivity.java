package com.czc.cutsame;

import android.app.Activity;
import android.content.Intent;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.fragment.app.FragmentManager;

import com.meishe.player.view.bean.TailorClip;
import com.czc.cutsame.bean.TransformData;
import com.czc.cutsame.fragment.RectSelectVideoFragment;
import com.meishe.player.view.MultiThumbnailSequenceView2;
import com.meishe.player.view.TailorView;
import com.meicam.sdk.NvsTimeline;
import com.meishe.base.model.BaseActivity;
import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.FormatUtils;
import com.meishe.base.utils.LogUtils;
import com.meishe.engine.EditorEngine;
import com.meishe.engine.bean.CommonData;
import com.meishe.engine.bean.MeicamTimeline;
import com.meishe.engine.bean.MeicamVideoClip;
import com.meishe.engine.bean.MeicamVideoTrack;
import com.meishe.engine.editor.EditorController;
import com.meishe.engine.interf.StreamingConstant;
import com.meishe.engine.interf.VideoFragmentListenerWithClick;

/**
 * All rights Reserved, Designed By www.meishesdk.com
 *
 * <AUTHOR> CaoZhiChao
 * @CreateDate : 2020/11/27 16:29
 * @Description : 通用模板的视频裁剪 Cut video clip Activity
 * @Copyright : www.meishesdk.com Inc. All rights reserved.
 */
public class TailorActivity extends BaseActivity implements VideoFragmentListenerWithClick {
    public static final String VIDEO_PATH = "videoPath";
    public static final String VIDEO_LIMIT = "videoLimit";
    public static final String VIDEO_TYPE = "videoType";
    public static final String START_TRIM = "startTrim";
    public static final String RAW_RATIO = "raw_ratio";
    public static final String TRANSFORM_DATA = "transform_data";
    public static final String INTENT_TRAM = "intentTrim";
    public static final String INTENT_RECT_DATA = "rectData";
    private RectSelectVideoFragment mBaseVideoFragment;
    private String mVideoPath;
    private long mVideoLimit;
    private int mVideoType;
    private long mStartTrim = 0;
    private TailorView mTailorView;
    private TailorClip mTailorClip;
    private int mState = -1;
    private long mNowStartTime = 0;
    private float mRawRatio;
    private TransformData mTransformData;

    @Override
    protected int bindLayout() {
        return R.layout.activity_tailor;
    }

    @Override
    protected void initData(Bundle savedInstanceState) {
        Bundle extras = getIntent().getExtras();
        if (null != extras) {
            mVideoPath = extras.getString(VIDEO_PATH);
            mVideoLimit = extras.getLong(VIDEO_LIMIT);
            mVideoType = extras.getInt(VIDEO_TYPE);
            mStartTrim = extras.getLong(START_TRIM);
            mRawRatio = extras.getFloat(RAW_RATIO);
            mTransformData = extras.getParcelable(TRANSFORM_DATA);
            mNowStartTime = mStartTrim;
        }
        if (!TextUtils.isEmpty(mVideoPath)) {
            MeicamTimeline nvsTimeline = new MeicamTimeline.TimelineBuilder(EditorController.getInstance()
                    .getStreamingContext(), MeicamTimeline.TimelineBuilder.BUILD_NORMAL)
                    .setVideoResolution(EditorEngine.getVideoEditResolution(mVideoPath))
                    .build();
            EditorController.getInstance().setNvsTimeline(nvsTimeline);
            EditorController.getInstance().seekTimeline(mNowStartTime);
            long duration;
            String videoType = CommonData.CLIP_VIDEO;
            if (mVideoType == StreamingConstant.VideoClipType.VIDEO_CLIP_TYPE_AV) {
                duration = EditorController.getInstance().getVideoDuration(mVideoPath);
            } else {
                videoType = CommonData.CLIP_IMAGE;
                duration = mVideoLimit;
            }
            mTailorClip = new TailorClip(mVideoPath, mVideoLimit, 0, duration);
            MeicamVideoTrack meicamVideoTrack = nvsTimeline.appendVideoTrack();
            MeicamVideoClip newClip = meicamVideoTrack.addVideoClip(mVideoPath, 0, 0, duration);
            newClip.setType(videoType);
        } else {
            LogUtils.e("initData: error! mVideoPath is empty!");
        }
    }

    @Override
    protected void initView() {
        addVideoFragment();
        ImageView activityTailorBack = findViewById(R.id.activity_tailor_back);
        activityTailorBack.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent();
                setResult(Activity.RESULT_CANCELED, intent);
                finish();
            }
        });
        Button activityTailorSure = findViewById(R.id.activity_tailor_sure);
        activityTailorSure.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent();
                intent.putExtra(INTENT_RECT_DATA, mBaseVideoFragment.getTransFromDataInFile());
                intent.putExtra(INTENT_TRAM, mNowStartTime);
                // 设置返回码和返回携带的数据 Sets the return code and returns the data carried
                setResult(Activity.RESULT_OK, intent);
                finish();
            }
        });
        Drawable drawable = CommonUtils.getRadiusDrawable(-1, -1,
                getResources().getDimensionPixelOffset(R.dimen.dp_px_150), getResources().getColor(R.color.activity_tailor_button_background));
        activityTailorSure.setBackground(drawable);
        mTailorView = findViewById(R.id.activity_tailor_view);
        boolean isImage = mVideoType != StreamingConstant.VideoClipType.VIDEO_CLIP_TYPE_AV;
        if (!isImage) {
            mTailorView.setVisibility(View.VISIBLE);
            mTailorView.setOnScrollListener(new MultiThumbnailSequenceView2.OnScrollListener() {
                @Override
                public void onScrollChanged(int dx, int oldDx) {
                    long nowTime = EditorController.getInstance().lengthToDuration(dx, mTailorView.getPixelPerMicrosecond());
                    mNowStartTime = nowTime;
                    if (!EditorController.getInstance().isPlaying()) {
                        EditorController.getInstance().seekTimeline(nowTime);
                    }
                }

                @Override
                public void onScrollStopped() {
                    EditorController.getInstance().playNow(EditorController.getInstance().nowTime() + mVideoLimit);
                }

                @Override
                public void onSeekingTimeline() {
                    EditorController.getInstance().stop();
                }
            });
            mTailorView.setTailorClip(mTailorClip);
            mTailorView.setState(TailorView.FROM_USER);
            mTailorView.postDelayed(new Runnable() {
                @Override
                public void run() {
                    mTailorView.seekNvsMultiThumbnailSequenceView(EditorController.getInstance().durationToLength(mStartTrim, mTailorView.getPixelPerMicrosecond()));
                }
            }, 200);
        }
        TextView activityTailorTextLimit = findViewById(R.id.activity_tailor_text_limit);
        if (isImage) {
            activityTailorTextLimit.setVisibility(View.INVISIBLE);
        }
        String text = FormatUtils.objectFormat2String(mVideoLimit / 1000000f) + "S";
        activityTailorTextLimit.setText(text);
    }

    private void addVideoFragment() {
        FragmentManager fragmentManager = getSupportFragmentManager();
        mBaseVideoFragment = RectSelectVideoFragment.newInstance(true);
        if (mRawRatio != 0) {
            mBaseVideoFragment.showRectSelectView(mRawRatio, mTransformData);
        }
        mBaseVideoFragment.setVideoFragmentListener(this);
        fragmentManager.beginTransaction().add(R.id.activity_tailor_fragment_container, mBaseVideoFragment).commitAllowingStateLoss();
        fragmentManager.beginTransaction().show(mBaseVideoFragment);
    }

    @Override
    public void playBackEOF(NvsTimeline timeline) {
        EditorController.getInstance().playNow(mNowStartTime, mNowStartTime + mVideoLimit);
    }

    @Override
    public void playStopped(NvsTimeline timeline) {
    }

    @Override
    public void playbackTimelinePosition(NvsTimeline timeline, long stamp) {
        mState = TailorView.FROM_VIDEO;
        mTailorView.seekToPosition(stamp, mState, mNowStartTime);
    }

    @Override
    public void streamingEngineStateChanged(int state) {
        if (EditorController.getInstance().isPlaying()) {
            mState = TailorView.FROM_VIDEO;
            mTailorView.setState(mState);
        } else {
            mTailorView.setState(TailorView.FROM_USER);
        }
    }

    @Override
    public void onSeekingTimelinePosition(NvsTimeline timeline, long position) {

    }

    @Override
    public boolean clickPlayButtonByOthers() {
        controllerVideoFragmentClick();
        return true;
    }

    @Override
    public boolean clickLiveWindowByOthers() {
        controllerVideoFragmentClick();
        return true;
    }

    @Override
    public void connectTimelineWithLiveWindow() {
        EditorController.getInstance().playNow(mNowStartTime, mNowStartTime + mVideoLimit);
    }

    private void controllerVideoFragmentClick() {
        if (!EditorController.getInstance().isPlaying()) {
            EditorController.getInstance().playNow(EditorController.getInstance().nowTime(), mNowStartTime + mVideoLimit);
        } else {
            EditorController.getInstance().stop();
        }
    }
}