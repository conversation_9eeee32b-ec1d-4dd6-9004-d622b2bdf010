package com.czc.cutsame.fragment.presenter;

import android.text.TextUtils;

import com.czc.cutsame.CutSameNetApi;
import com.czc.cutsame.R;
import com.czc.cutsame.bean.Template;
import com.czc.cutsame.bean.TemplateCategory;
import com.czc.cutsame.fragment.iview.TemplateView;
import com.meishe.base.model.Presenter;
import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.FileUtils;
import com.meishe.base.utils.LogUtils;
import com.meishe.base.utils.ThreadUtils;
import com.meishe.base.utils.Utils;
import com.meishe.base.utils.ZipUtils;
import com.meishe.engine.EngineNetApi;
import com.meishe.engine.asset.AssetsManager;
import com.meishe.engine.asset.IAssetsManager;
import com.meishe.engine.asset.bean.AssetDownloadInfo;
import com.meishe.engine.asset.bean.AssetInfo;
import com.meishe.engine.asset.bean.AssetList;
import com.meishe.engine.asset.bean.AssetsConstants;
import com.meishe.engine.asset.bean.RequestParam;
import com.meishe.engine.util.PathUtils;
import com.meishe.net.custom.BaseResponse;
import com.meishe.net.custom.RequestCallback;
import com.meishe.net.custom.SimpleDownListener;
import com.meishe.net.model.Progress;

import java.io.File;
import java.io.IOException;
import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.List;

/**
 * author : lhz
 * date   : 2020/11/3
 * desc   :模板逻辑处理类
 * Template logic handles classes
 */
public class TemplatePresenter extends Presenter<TemplateView> {
    private static final String TEMPLATE_POSTFIX = ".template";
    private static final String VIDEO_POSTFIX = ".mp4";
    private static final String INFO_POSTFIX = ".json";
    public static final int PAGE_NUM = 12;
    private boolean hasNext;
    private int mPage;
    private int currentTotal;
    private String mKeyword;

    @Override
    public void attachView(TemplateView templateView) {
        super.attachView(templateView);
    }

    /**
     * 获取模板分类
     * <p></p>
     * Get template classification
     */
    public void getTemplateCategory() {
        CutSameNetApi.getTemplateCategory(this, new RequestCallback<TemplateCategory>() {
            @Override
            public void onSuccess(BaseResponse<TemplateCategory> response) {
                if (response != null && response.getData() != null) {
                    if (response.getData().categories != null && getView() != null) {
                        getView().onTemplateCategoryBack(response.getData().categories);
                    }
                }
            }

            @Override
            public void onError(BaseResponse<TemplateCategory> response) {
                getView().onTemplateCategoryBack(null);
            }
        });
    }

    /**
     * 获取模板对应分类的列表
     * <p></p>
     * Gets the list of categories corresponding to the template
     *
     * @param page    int 请求页数
     * @param subType int 模板子类型id
     */
    public void getTemplateList(final int page, int subType, final boolean isRefresh) {
        mPage = page;
        RequestParam param = new RequestParam(AssetsConstants.AssetsTypeData.TEMPLATE.type, subType, AssetsConstants.AssetsTypeData.TEMPLATE.category, AssetsConstants.AssetsTypeData.TEMPLATE.kind, mKeyword);

        AssetsManager.get().getAssetsList(param, -1, -1, page, PAGE_NUM, true, new IAssetsManager.AssetsRequestCallback<AssetList>() {

            @Override
            public void onSuccess(BaseResponse<AssetList> response) {
                if (response == null || response.getData() == null) {
                    if (page == 0) {
                        getView().onTemplateListBack(null);
                    } else {
                        getView().onMoreTemplateBack(null);
                    }
                    return;
                }
                List<AssetInfo> realAssetList = response.getData().realAssetList;
                if (CommonUtils.isEmpty(realAssetList)) {
                    if (page == 0) {
                        getView().onTemplateListBack(null);
                    } else {
                        getView().onMoreTemplateBack(null);
                    }
                    hasNext = response.getData().total > currentTotal;
                    return;
                }
                List<Template> list = new ArrayList<>();
                for (AssetInfo assetInfo : realAssetList) {
                    if (assetInfo.getCategory() == 2) {
                        continue;
                    }
                    Template template = Template.create(assetInfo);
                    if (template.getProducer() == null) {
                        Template.Producer producer = new Template.Producer();
                        producer.setNickname(Utils.getApp().getResources().getString(R.string.template_default_creator));
                        producer.setIconUrl("https://qasset.meishesdk.com/my/default_icon.png");
                        template.setProducer(producer);
                    }
                    list.add(template);
                }

                if (page == 0) {
                    getView().onTemplateListBack(list);
                    currentTotal = list.size();
                } else {
                    currentTotal += list.size();
                    getView().onMoreTemplateBack(list);
                }
                hasNext = response.getData().total > currentTotal;
            }

            @Override
            public void onError(BaseResponse<AssetList> response) {
                getView().onTemplateListBack(null);
            }
        });
      /*  CutSameNetApi.getTemplateList(this, page, PAGE_NUM, categoryId, new RequestCallback<TemplateList>() {
            @Override
            public void onSuccess(BaseResponse<TemplateList> response) {
                if (response != null && response.getData() != null) {
                    if (response.getData().getElements() != null && getView() != null) {
                        if (page == 0) {
                            mPage = 0;
                            canLoadMore = response.getData().getTotal() > response.getData().getElements().size();
                            getView().onTemplateListBack(response.getData().getElements());
                        } else {
                            canLoadMore = response.getData().getTotal() > PAGE_NUM * mPage + response.getData().getElements().size();
                            mPage++;
                            getView().onMoreTemplateBack(response.getData().getElements());
                        }
                        hasNext = true;
                    } else {
                        hasNext = false;
                    }
                } else {
                    hasNext = false;
                }
            }

            @Override
            public void onError(BaseResponse<TemplateList> response) {
                getView().onTemplateListBack(null);
            }
        });*/
    }

    /**
     * 加载更多模板
     * <p></p>
     * Loading more templates
     *
     * @param subType the category id 类别id
     * @return the more template 更多的模板
     */
    public boolean getMoreTemplate(int subType) {
        if (hasNext) {
            getTemplateList((mPage + 1), subType, false);
        }
        return hasNext;
    }

    /**
     * 下载对应的模板资源
     * <p></p>
     * download template resource
     *
     * @param url        String 下载链接
     * @param downDir    String 文件所在目录
     * @param fileName   String 文件名称
     * @param isTemplate boolean true则是下载模板文件，false则不然。
     */
    public void download(String url, String downDir, final String fileName, final boolean isTemplate) {
        CutSameNetApi.download(fileName, url, downDir, fileName, new DownloadListener(fileName, getView(), fileName, isTemplate));
    }

    /**
     * 下载对应的模板资源
     * <p></p>
     * download template resource
     *
     * @param packageId  the packageId 包id
     * @param url        String 下载链接
     * @param isTemplate boolean true则是下载模板文件，false则不然。
     */
    public void download(final String packageId, final String url, final int subType, final boolean isTemplate) {
        final String filePath = PathUtils.getTemplateDir() + File.separator + packageId;
        final DownloadListener downloadListener = new DownloadListener(url, getView(), packageId, isTemplate);
        if (!TextUtils.isEmpty(url)) {
            AssetsManager.get().downLoad(packageId, url, filePath, "", subType, downloadListener);
        } else {
            EngineNetApi.downloadOrClick(packageId, "1", new RequestCallback<AssetDownloadInfo>() {
                @Override
                public void onSuccess(BaseResponse<AssetDownloadInfo> response) {
                    AssetDownloadInfo data = response.getData();
                    String packageUrl = data.getPackageUrl();
                    if (TextUtils.isEmpty(packageUrl)) {
                        downloadListener.onError(new Progress());
                        return;
                    }
                    AssetsManager.get().downLoad(packageId, packageUrl, filePath, "", subType, downloadListener);
                }

                @Override
                public void onError(BaseResponse<AssetDownloadInfo> response) {
                    downloadListener.onError(new Progress());
                }
            });
        }
    }

    /**
     * 检查模板是否需要更新
     * <p></p>
     * Check if the template needs to be updated
     *
     * @param template the template 模板
     * @return the boolean
     */
    public boolean checkTemplateUpdate(Template template) {
        File file = getTemplatePath(template.getId());
        try {
            if (file != null) {
                String templateName = file.getName();
                String[] split = templateName.split("\\.");
                if (split.length == 3 && Integer.parseInt(split[1]) >= template.getVersion()
                        || split.length == 2 && template.getVersion() <= 1) {
                    /*
                     * 不用更新
                     * Don't need to update
                     * */
                    if (getView() != null) {
                        getView().onDownloadTemplateSuccess(file.getAbsolutePath(), getFilePath(template.getId(), ".lic"), true);
                    }
                    return false;
                } else {
                    /*
                     * 需要更新，则删除文件夹中的内容
                     * If an update is needed, the contents of the folder are deleted
                     * */
                    FileUtils.deleteFilesInDir(file.getParentFile());
                }
            }
            download(template.getId(), template.getPackageUrl(), template.getSubType(), true);
            //不需要下载预览视频文件，EXOPlayer 已经做了缓存
            //There is no need to Download Preview video files.
            // Exoplayer has been cached.
            //downloadPreviewVideo(template);
        } catch (Exception e) {
            LogUtils.e(e);
        }
        return true;
    }

    @Deprecated
    private void downloadPreviewVideo(Template template) {
//        String fileName = template.getPreviewVideoUrl();
//        String fileDir = PathUtils.getTemplateDir() + File.separator + template.getId();
//        if (!TextUtils.isEmpty(fileName)) {
//            try {
//                /*
//                 * 截取文件名
//                 * Intercept file name
//                 * */
//                fileName = fileName.substring(fileName.lastIndexOf("/") + 1);
//            } catch (Exception e) {
//                LogUtils.e(e);
//                fileName = template.getId() + "." + template.getVersion() + VIDEO_POSTFIX;
//            }
//            download(template.getPreviewVideoUrl(), fileDir, fileName, false);
//        }
    }

    /**
     * 获取下载后的模板预览视频路径
     * <p></p>
     * Get the template preview video path after downloading
     *
     * @param templateId the template id 模板编号
     * @return the video path 视频路径
     */
    public String getVideoPath(String templateId) {
        return getFilePath(templateId, VIDEO_POSTFIX);
    }

    /**
     * 获取信息文件路径
     * <p></p>
     * Get the information file path after downloading
     *
     * @param templateId the template id 模板编号
     * @return the information file path 视频路径
     */
    public String getInfoPath(String templateId) {
        return getFilePath(templateId, INFO_POSTFIX);
    }

    /**
     * 根据文件后缀获取文件路径
     * <p></p>
     * Get the file path by postfix
     *
     * @param templateId the template id 模板编号
     * @param postfix    the file postfix 文件后缀
     * @return the file path 文件路径
     */
    private String getFilePath(String templateId, String postfix) {
        if (TextUtils.isEmpty(postfix)) {
            return null;
        }
        String templatePath = PathUtils.getTemplateDir() + "/" + templateId;
        if (FileUtils.isDir(templatePath)) {
            List<File> files = FileUtils.listFilesInDir(templatePath);
            if (files != null && files.size() > 0) {
                for (File file : files) {
                    if (file.getName().toLowerCase().endsWith(postfix)) {
                        return file.getAbsolutePath();
                    }
                }
            }

        }
        return null;
    }


    /**
     * 获取下载后的模板文件
     * <p></p>
     * Get the downloaded template file
     */
    private File getTemplatePath(String templateId) {
        String templatePath = PathUtils.getTemplateDir() + "/" + templateId;
        if (FileUtils.isDir(templatePath)) {
            List<File> files = FileUtils.listFilesInDir(templatePath);
            if (files != null && files.size() > 0) {
                for (File file : files) {
                    if (file.getName().endsWith(TEMPLATE_POSTFIX)) {
                        return file;
                    }
                }
            }
        }
        return null;
    }

    public void setKeyword(String keyword) {
        mKeyword = keyword;
    }


    public String getKeyword() {
        return mKeyword;
    }

    static class DownloadListener extends SimpleDownListener {
        private final boolean mIsTemplate;
        private final String mFileName;
        private final WeakReference<TemplateView> mTarget;

        /**
         * Instantiates a new Simple down listener.
         *
         * @param tag        the tag
         * @param view       the View
         * @param fileName   the fileName
         * @param isTemplate is template or not
         */
        public DownloadListener(Object tag, TemplateView view, String fileName, boolean isTemplate) {
            super(tag);
            mTarget = new WeakReference<>(view);
            mIsTemplate = isTemplate;
            mFileName = fileName;
        }

        public WeakReference<TemplateView> getTarget() {
            return mTarget;
        }

        @Override
        public void onFinish(final File file, Progress progress) {
            final String filePath = file.getAbsolutePath();
            if (filePath.endsWith(".zip")) {
                ThreadUtils.getIoPool().execute(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            String fileSavedPath = file.getParentFile().getAbsolutePath();
                            final List<File> fileList = ZipUtils.unzipFile(file, new File(fileSavedPath));
                            /*
                             * 删除webp文件，文件叫大
                             * Delete the webp files， it is big.
                             */
                            try {
                                if (!CommonUtils.isEmpty(fileList)) {
                                    for (File fileItem : fileList) {
                                        if (fileItem.getName().endsWith(".webp")) {
                                            FileUtils.delete(fileItem);
                                        }

                                    }
                                }
                            } catch (Exception e) {
                                LogUtils.d(e);
                            }
                            /*
                             * 删除zip文件
                             * Delete the zip files
                             */
                            FileUtils.delete(filePath);
                            ThreadUtils.runOnUiThread(new Runnable() {
                                @Override
                                public void run() {
                                    String licPath = "";
                                    String packagePath = "";

                                    if (CommonUtils.isEmpty(fileList)) {
                                        onError(new Progress());
                                        return;
                                    }
                                    for (File fileItem : fileList) {
                                        String absolutePath = fileItem.getAbsolutePath();
                                        if (absolutePath.endsWith(TEMPLATE_POSTFIX)) {
                                            packagePath = absolutePath;
                                        }
                                        if (absolutePath.endsWith(".lic")) {
                                            licPath = absolutePath;
                                        }
                                    }
                                    if (getTarget() != null && getTarget().get() != null) {
                                        getTarget().get().onDownloadTemplateSuccess(packagePath, licPath, mIsTemplate);
                                    }
                                }
                            });
                        } catch (IOException e) {
                            onError(new Progress());
                        }
                    }
                });
                return;
            }
            if (getTarget() != null && getTarget().get() != null) {
                getTarget().get().onDownloadTemplateSuccess(filePath, "", mIsTemplate);
            }
        }

        @Override
        public void onError(Progress progress) {
            FileUtils.delete(PathUtils.getTemplateDir() + File.separator + mFileName);
        }
    }
}
