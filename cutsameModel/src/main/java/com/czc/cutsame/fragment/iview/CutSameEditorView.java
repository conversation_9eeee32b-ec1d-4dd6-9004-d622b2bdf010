package com.czc.cutsame.fragment.iview;

import com.meishe.base.model.IBaseView;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2021/12/8 16:17
 * @Description :MVP中的剪同款view接口 The interface of cut same in MVP
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public interface CutSameEditorView extends IBaseView {
    /**
     * Finish.
     */
    void finish();


    /**
     * Re connect timeline with live window.
     * 重新连接timeline到live window
     */
    void reConnectTimelineWithLiveWindow();
}
