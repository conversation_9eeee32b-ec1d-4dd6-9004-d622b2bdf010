package com.czc.cutsame.fragment;

import android.app.Activity;
import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RelativeLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.czc.cutsame.R;
import com.meicam.sdk.NvsLiveWindowExt;
import com.meicam.sdk.NvsTimeline;
import com.meicam.sdk.NvsVideoResolution;
import com.meishe.base.manager.AppManager;
import com.meishe.base.model.BaseFragment;
import com.meishe.base.utils.LogUtils;
import com.meishe.engine.EngineCallbackManager;
import com.meishe.engine.bean.MeicamTimeline;
import com.meishe.engine.editor.EditorController;
import com.meishe.engine.interf.VideoFragmentListenerWithClick;
import com.meishe.engine.observer.EngineCallbackObserver;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2020/11/3 16:16
 * 基础的视频fragment类
 * The basic video Fragment class
 */
public abstract class BaseVideoFragment extends BaseFragment {

    public static final String SHOW_BUTTON = "showButton";
    private EngineCallbackObserver mCallbackObserver;
    protected NvsLiveWindowExt mNvsLiveWindow;
    protected VideoFragmentListenerWithClick mVideoFragmentListener;
    private ImageView mFragmentBaseButton;
    private boolean mIsShowButton = false;
    protected RelativeLayout mPlayerLayout;

    public BaseVideoFragment() {
    }

    /**
     * On lazy load.
     * 延迟加载
     */
    @Override
    protected void onLazyLoad() {

    }

    /**
     * Bind layout int.
     * 绑定布局
     *
     * @return the int
     */
    @Override
    protected int bindLayout() {
        return R.layout.cut_same_base_fragment_video;
    }

    /**
     * Init view.
     * 初始化视图
     *
     * @param mRootView the m root view
     */
    @Override
    protected void initView(View mRootView) {
        mPlayerLayout = mRootView.findViewById(R.id.fragment_base_parent);
        mNvsLiveWindow = mRootView.findViewById(R.id.fragment_base_live_window);
        mNvsLiveWindow.setBackgroundColor(0.133f, 0.133f, 0.133f);
        mFragmentBaseButton = mRootView.findViewById(R.id.fragment_base_button);
        mNvsLiveWindow.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mVideoFragmentListener != null && mVideoFragmentListener.clickLiveWindowByOthers()) {

                } else {
                    if (!EditorController.getInstance().isPlaying()) {
                        EditorController.getInstance().playNow();
                    } else {
                        EditorController.getInstance().stop();
                    }
                }
            }
        });
        EditorController.getInstance().connectTimelineWithLiveWindow(mNvsLiveWindow);
        setLiveWindowRatio();
        mFragmentBaseButton = mRootView.findViewById(R.id.fragment_base_button);
        mFragmentBaseButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mVideoFragmentListener == null || !mVideoFragmentListener.clickPlayButtonByOthers()) {
                    if (!EditorController.getInstance().isPlaying()) {
                        EditorController.getInstance().playNow();
                    } else {
                        EditorController.getInstance().stop();
                    }
                }
            }
        });
    }

    /**
     * Connect timeline with live window.
     * 连接时间轴与活动窗口
     */
    public void connectTimelineWithLiveWindow() {
        EditorController.getInstance().connectTimelineWithLiveWindow(mNvsLiveWindow);
        EditorController.getInstance().seekTimeline();
    }


    /**
     * Sets live window ratio.
     * 设置活动窗口比率
     */
    private void setLiveWindowRatio() {
        MeicamTimeline nvsTimeline = EditorController.getInstance().getNvsTimeline();
        if (nvsTimeline == null) {
            LogUtils.e("timeline is null!");
            return;
        }
        final NvsVideoResolution videoRes = nvsTimeline.getVideoResolution();
        if (videoRes == null) {
            return;
        }
        if (mPlayerLayout.getWidth() == 0 && mPlayerLayout.getHeight() == 0) {
            mPlayerLayout.post(new Runnable() {
                @Override
                public void run() {
                    setLiveWindowRatio(videoRes);
                    EditorController.getInstance().seekTimeline();
                    onLiveWindowSizeChanged();
                }
            });
        } else {
            setLiveWindowRatio(videoRes);
            EditorController.getInstance().seekTimeline();
            onLiveWindowSizeChanged();
        }

    }

    protected abstract void onLiveWindowSizeChanged();

    /**
     * Sets live window ratio.
     * 设置活动窗口比率
     *
     * @param resolution timeline 的分辨率
     */
    public void setLiveWindowRatio(@NonNull NvsVideoResolution resolution) {
        ViewGroup.LayoutParams layoutParams = mNvsLiveWindow.getLayoutParams();
        int layoutWidth = mPlayerLayout.getWidth();
        int layoutHeight = mPlayerLayout.getHeight();
        float layoutRatio = layoutWidth * 1F / layoutHeight;
        float timelineRatio = resolution.imageWidth * 1.0F / resolution.imageHeight;
        if (timelineRatio > layoutRatio) {
            layoutParams.height = (int) (layoutWidth / timelineRatio);
        } else {
            layoutParams.width = (int) (layoutHeight * timelineRatio);
        }
        mNvsLiveWindow.setLayoutParams(layoutParams);
        //mNvsLiveWindow.repaintVideoFrame();
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EngineCallbackManager.get().registerCallbackObserver(mCallbackObserver = new EngineCallbackObserver() {
            @Override
            public boolean isActive() {
                Activity activity = AppManager.getInstance().currentActivity();
                return isVisible() && getActivity() != null && getActivity().equals(activity);
            }

            @Override
            public void onPlaybackPreloadingCompletion(NvsTimeline nvsTimeline) {

            }

            @Override
            public void onPlaybackStopped(NvsTimeline nvsTimeline) {
                changePlayButtonState(true);
                if (mVideoFragmentListener != null) {
                    mVideoFragmentListener.playStopped(nvsTimeline);
                }
            }

            @Override
            public void onPlaybackEOF(NvsTimeline nvsTimeline) {
                changePlayButtonState(true);
                if (mVideoFragmentListener != null) {
                    mVideoFragmentListener.playBackEOF(nvsTimeline);
                }
            }

            @Override
            public void onSeekingTimelinePosition(NvsTimeline nvsTimeline, long l) {
                if (mVideoFragmentListener != null) {
                    mVideoFragmentListener.onSeekingTimelinePosition(nvsTimeline, l);
                }
            }

            @Override
            public void onPlaybackTimelinePosition(NvsTimeline nvsTimeline, long cur_position) {
                if (mVideoFragmentListener != null) {
                    mVideoFragmentListener.playbackTimelinePosition(nvsTimeline, cur_position);
                }
            }

            @Override
            public void onStreamingEngineStateChanged(int i) {
                boolean playing = EditorController.getInstance().isPlaying();
                changePlayButtonState(!playing);
                if (mVideoFragmentListener != null) {
                    mVideoFragmentListener.streamingEngineStateChanged(i);
                }
            }
        });
    }

    /**
     * Init data.
     * 初始化数据
     */
    @Override
    protected void initData() {
        Bundle bundle = getArguments();
        if (bundle != null) {
            mIsShowButton = bundle.getBoolean(SHOW_BUTTON);
        }
        if (mVideoFragmentListener != null) {
            mVideoFragmentListener.connectTimelineWithLiveWindow();
        }
        changePlayButtonState(false);
    }


    /**
     * Sets video fragment listener.
     * 设置视频片段监听器
     *
     * @param videoFragmentListener the video fragment listener 视频片段监听器
     */
    public void setVideoFragmentListener(VideoFragmentListenerWithClick videoFragmentListener) {
        mVideoFragmentListener = videoFragmentListener;
    }

    /**
     * On pause.
     * 暂停
     */
    @Override
    public void onPause() {
        super.onPause();
        EditorController.getInstance().stop();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        EngineCallbackManager.get().unregisterCallbackObserver(mCallbackObserver);
    }

    /**
     * Change play button state.
     * 改变播放按钮状态
     *
     * @param show the show
     */
    protected void changePlayButtonState(boolean show) {
        if (mIsShowButton && mFragmentBaseButton != null) {
            mFragmentBaseButton.setVisibility(show ? View.VISIBLE : View.GONE);
        }
    }

    protected void clickPlayButton(){
        if (mFragmentBaseButton != null) {
            mFragmentBaseButton.performClick();
        }
    }
}
