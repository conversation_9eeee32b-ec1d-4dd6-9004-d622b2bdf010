package com.czc.cutsame.fragment;

import android.graphics.PointF;
import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;

import com.czc.cutsame.R;
import com.czc.cutsame.fragment.interf.CaptionOperationListener;
import com.czc.cutsame.view.CaptionBox;
import com.meishe.base.utils.CommonUtils;
import com.meishe.engine.util.CoordinateUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2021/11/19 15:29
 * @Description :可以编辑字幕的fragment 类 The fragment for editing caption and compound caption.
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class CaptionEditVideoFragment extends BaseVideoFragment {
    private CaptionBox mCaptionBox;

    public CaptionEditVideoFragment() {
    }

    public static CaptionEditVideoFragment newInstance(boolean showButton) {
        CaptionEditVideoFragment fragment = new CaptionEditVideoFragment();
        Bundle args = new Bundle();
        args.putBoolean(SHOW_BUTTON, showButton);
        fragment.setArguments(args);
        return fragment;
    }

    /**
     * Bind layout int.
     * 绑定布局
     *
     * @return the int
     */
    @Override
    protected int bindLayout() {
        return R.layout.cut_same_fragment_video;
    }

    /**
     * Init view.
     * 初始化视图
     *
     * @param mRootView the m root view
     */
    @Override
    protected void initView(View mRootView) {
        super.initView(mRootView);
        mCaptionBox = mRootView.findViewById(R.id.caption_box);
    }

    public void setCaptionOperationListener(CaptionOperationListener listener) {
        mCaptionBox.setOperationListener(listener);
    }

    @Override
    protected void onLiveWindowSizeChanged() {
        mCaptionBox.post(new Runnable() {
            @Override
            public void run() {
                ViewGroup.LayoutParams layoutParams = mCaptionBox.getLayoutParams();
                layoutParams.width = mNvsLiveWindow.getWidth();
                layoutParams.height = mNvsLiveWindow.getHeight();
                mCaptionBox.setLayoutParams(layoutParams);
            }
        });
    }

    /**
     * 显示字幕边框
     * Show caption box
     *
     * @param list the pointF list of caption
     * @param scaleValue scale value
     */
    public void showCaptionBox(List<PointF> list, float scaleValue) {
        mCaptionBox.setVisibility(View.VISIBLE);
        mCaptionBox.setPointFList(transformCoordinates(list, scaleValue));
    }

    /**
     * 显示复合字幕边框
     * Show compound caption box
     *
     * @param list      the pointF list of caption
     * @param childList the child pointF list of caption
     * @param scaleValue scale value
     */
    public void showCaptionBox(List<PointF> list, List<List<PointF>> childList, float scaleValue) {
        if (CommonUtils.isEmpty(childList) || CommonUtils.isEmpty(list)) {
            return;
        }
        mCaptionBox.setVisibility(View.VISIBLE);
        List<List<PointF>> childListNew = new ArrayList<>();
        for (List<PointF> pointFS : childList) {
            childListNew.add(transformCoordinates(pointFS, scaleValue));
        }
        mCaptionBox.setPointFList(transformCoordinates(list, scaleValue), childListNew);
    }

    /**
     * 隐藏字幕边框
     * Not show caption box
     */
    public void notShowCaptionBox() {
        mCaptionBox.setVisibility(View.INVISIBLE);
    }

    /**
     * 转换坐标
     * transform coordinates
     */
    private List<PointF> transformCoordinates(List<PointF> list, float scaleValue) {
        if (list == null || list.size() < 4) {
            return null;
        }
        List<PointF> newList = new ArrayList<>(list.size());
        for (int i = 0; i < list.size(); i++) {
            PointF pointF = mNvsLiveWindow.mapCanonicalToView(list.get(i));
            newList.add(pointF);
        }
        PointF center = new PointF((newList.get(0).x + newList.get(2).x) / 2F, (newList.get(0).y + newList.get(2).y) / 2);
        for (PointF pointF : newList) {
            CoordinateUtil.transformData(pointF, center, scaleValue, 0);
        }
        return newList;
    }
}
