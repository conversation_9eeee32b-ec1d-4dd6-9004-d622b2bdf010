package com.czc.cutsame.fragment;

import android.content.Intent;
import android.graphics.PointF;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.CheckBox;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.czc.cutsame.CutSameEditorActivity;
import com.czc.cutsame.MaterialSelectActivity;
import com.czc.cutsame.R;
import com.czc.cutsame.TailorActivity;
import com.czc.cutsame.bean.TemplateClip;
import com.czc.cutsame.fragment.adapter.CutCaptionAdapter;
import com.czc.cutsame.fragment.adapter.CutVideoAdapter;
import com.czc.cutsame.fragment.iview.CutEditorVpView;
import com.czc.cutsame.fragment.presenter.CutEditorVpPresenter;
import com.czc.cutsame.pop.CaptionEditPop;
import com.czc.cutsame.pop.VideoEditPop;
import com.czc.cutsame.pop.VideoEditVolumePop;
import com.czc.cutsame.util.NvTemplateDataAdjustTool;
import com.meicam.sdk.NvsObject;
import com.meishe.base.constants.Constants;
import com.meishe.base.manager.AppManager;
import com.meishe.base.manager.LinearLayoutManagerWrapper;
import com.meishe.base.model.BaseMvpFragment;
import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.LogUtils;
import com.meishe.base.utils.Utils;
import com.meishe.base.view.decoration.ItemDecoration;
import com.meishe.engine.bean.CommonData;
import com.meishe.engine.bean.MeicamCaptionClip;
import com.meishe.engine.bean.MeicamCompoundCaptionClip;
import com.meishe.engine.bean.MeicamVideoClip;
import com.meishe.engine.bean.template.MeicamNvsTemplateFootageCorrespondingClipInfo;
import com.meishe.engine.bean.template.TemplateCaptionDesc;
import com.meishe.engine.editor.EditorController;
import com.meishe.engine.interf.StreamingConstant;
import com.meishe.third.adpater.BaseQuickAdapter;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.czc.cutsame.fragment.presenter.CutEditorVpPresenter.VIDEO;
import static com.meishe.logic.constant.PagerConstants.REQUEST_CODE_1;
import static com.meishe.logic.constant.PagerConstants.SELECTED_TYPE;
import static com.meishe.logic.constant.PagerConstants.TEMPLATE_CLIP;
import static com.meishe.logic.constant.PagerConstants.TYPE_ONE_FINISH;

/**
 * Created by CaoZhiChao on 2020/11/4 18:11
 * 剪切编辑器Vp类
 * Shear editor Vp class
 */
public class CutEditorVpFragment extends BaseMvpFragment<CutEditorVpPresenter> implements CutEditorVpView {
    private static final String TYPE = "type";
    private static final String TEMPLATE_ID = "templateID";
    private RecyclerView mFragmentRecyclerView;
    private CutVideoAdapter mCutVideoAdapter;
    private CutCaptionAdapter mCutCaptionAdapter;
    private List<MeicamNvsTemplateFootageCorrespondingClipInfo> mVideoData = new ArrayList<>();
    private List<TemplateCaptionDesc> mCaptionData = new ArrayList<>();
    private String mType;
    private String mTemplateId;
    private SeekPositionListener mSeekPositionListener;
    private MeicamNvsTemplateFootageCorrespondingClipInfo selectedClipInfo;
    private CaptionEditPop mCaptionEditPop;
    private MeicamVideoClip mSelectedVideoClip;
    private CheckBox mCheckBox;

    public CutEditorVpFragment() {
    }

    public CutEditorVpFragment(SeekPositionListener seekPositionListener) {
        mSeekPositionListener = seekPositionListener;
    }

    public static CutEditorVpFragment newInstance(String type, String templateId, SeekPositionListener seekPositionListener) {
        CutEditorVpFragment fragment = new CutEditorVpFragment(seekPositionListener);
        Bundle args = new Bundle();
        args.putString(TYPE, type);
        args.putString(TEMPLATE_ID, templateId);
        fragment.setArguments(args);
        return fragment;
    }

    /**
     * Bind layout int.
     * 绑定布局
     *
     * @return the int
     */
    @Override
    protected int bindLayout() {
        return R.layout.fragment_cut_editor;
    }

    /**
     * On lazy load.
     * 延迟加载
     */
    @Override
    protected void onLazyLoad() {
        if (mCutCaptionAdapter != null && mCutCaptionAdapter.getSelectIndex() > 0) {
            if (CutEditorVpPresenter.CAPTION.equals(mType)) {
                if (getActivity() instanceof CutSameEditorActivity) {
                    TemplateCaptionDesc item = mCutCaptionAdapter.getItem(mCutCaptionAdapter.getSelectIndex());
                    if (item != null && mPresenter != null) {
                        if (item.isCaption()) {
                            ((CutSameEditorActivity) getActivity()).showCaptionBox(mPresenter.getCaptionPointList(item), mPresenter.getCaptionScaleValue(item));
                        } else if (item.isCompoundCaption()) {
                            List<List<PointF>> childPointList = new ArrayList<List<PointF>>();
                            ((CutSameEditorActivity) getActivity()).showCompoundCaptionBox(mPresenter.getCompoundCaptionPointList(item, childPointList), childPointList, 1);
                        }
                    }
                }
            }
        }
    }

    /**
     * Init view.
     * 初始化视图
     *
     * @param rootView the root view
     */
    @Override
    protected void initView(View rootView) {
        mFragmentRecyclerView = rootView.findViewById(R.id.fragment_recycler_view);
        mCheckBox = rootView.findViewById(R.id.checkbox);
    }

    /**
     * Init data.
     * 初始化数据
     */
    @Override
    protected void initData() {
        Bundle bundle = getArguments();
        if (bundle != null) {
            mType = bundle.getString(TYPE);
            mTemplateId = bundle.getString(TEMPLATE_ID);
        }
        if (TextUtils.equals(mType, VIDEO)) {
            mCheckBox.setText(getString(R.string.ck_tip_template_group_clip));
        } else {
            mCheckBox.setText(getString(R.string.ck_tip_template_group_caption));
        }
        initRecycleView();
        mPresenter.onDataReady(mType, mTemplateId);
        EditorController.getInstance().playNow2(0);
    }

    private void initRecycleView() {
        LinearLayoutManager layoutManager = new LinearLayoutManagerWrapper(getContext(), LinearLayoutManager.HORIZONTAL, false);
        mFragmentRecyclerView.setLayoutManager(layoutManager);
        mFragmentRecyclerView.addItemDecoration(new ItemDecoration(18, 18));
    }

    /**
     * Chang clip trim.
     * 修剪
     *
     * @param newTrimIn the new trim in
     */
    public void changClipTrim(long newTrimIn) {
        if (mPresenter != null) {
            mPresenter.changClipTrim(newTrimIn, selectedClipInfo);
        }
    }

    public MeicamNvsTemplateFootageCorrespondingClipInfo getSelectedClipInfo() {
        return selectedClipInfo;
    }

    /**
     * Gets caption data.
     * 获得字幕数据
     *
     * @param captionClipList the caption clip list 字幕列表
     */


    @Override

    public void getCaptionData(List<TemplateCaptionDesc> captionClipList) {
        int groupIndex = -1;
        mCaptionData = captionClipList;
        //不可删除，这里要找到所有的GroupID,设置给TemplateCaptionDesc
        // Cannot be deleted, all GroupIDs need to be found here and set to TemplateCaptionDesc.
        List<MeicamCaptionClip> allCaptionClips = EditorController.getInstance().getAllCaption(mCaptionData);
        //设置编组信息
        //说明没有编组，适配AE模板，再计算一下编组信息
        //Set Group Information
        // Explain that there is no grouping. Adapt the AE template and calculate the grouping information again.
        Map<String, List<TemplateCaptionDesc>> map = new HashMap<>();
        String key = "";
        List<TemplateCaptionDesc> tempList = null;
        for (TemplateCaptionDesc templateCaptionDesc : mCaptionData) {
            key = templateCaptionDesc.getGroupID();
            if (TextUtils.isEmpty(key)) {
                continue;
            }
            tempList = map.get(key);
            if (null == tempList) {
                tempList = new ArrayList<>();
                map.put(key, tempList);
            }
            tempList.add(templateCaptionDesc);
        }

        for (List<TemplateCaptionDesc> templateClips : map.values()) {
            if (templateClips.size() > 1) {
                //有同一编组 Having the same formation.
                if (mCheckBox.getVisibility() != View.VISIBLE) {
                    mCheckBox.setVisibility(View.VISIBLE);
                }
                groupIndex += 1;
                for (TemplateCaptionDesc templateClip : templateClips) {
                    templateClip.setGroupIndex(groupIndex);
                }
            }
        }

        mCutCaptionAdapter = new CutCaptionAdapter();
        mCutCaptionAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, final int position) {
                mCutCaptionAdapter.setSelectIndex(position);
                TemplateCaptionDesc captionDesc = mCaptionData.get(position);
                if (mCutCaptionAdapter.getSelectIndex() == mCutCaptionAdapter.getBeforeSelectIndex()) {
                    showCaptionEditPop(captionDesc, position, 0);
                } else {
                    mPresenter.seekToCaptionStartTime(captionDesc);
                }
                mCutCaptionAdapter.setBeforeSelectIndex(position);
            }
        });
        mFragmentRecyclerView.setAdapter(mCutCaptionAdapter);
        mCutCaptionAdapter.setNewData(mCaptionData);
        mPresenter.getCaptionBitmap(mCaptionData);
    }

    /**
     * 显示字幕编辑弹窗
     * Displays a caption edit pop
     */
    public void showCaptionEditPop(final TemplateCaptionDesc captionDesc, final int position, final int index) {
        if (mCaptionEditPop == null) {
            mCaptionEditPop = CaptionEditPop.create(getActivity());
        }
        mCaptionEditPop.setEventListener(new CaptionEditPop.EventListener() {
            @Override
            public void onConfirm(String text) {
                if (captionDesc.isCaption()) {
                    MeicamCaptionClip selectedTimelineCaption = EditorController.getInstance().
                            getCaptionByTemplateCaptionDesc(captionDesc);
                    if (selectedTimelineCaption == null) {
                        LogUtils.e("changeCaptionText: nvsTimelineCaption is NULL! " + captionDesc.replaceId);
                        return;
                    }
                    if (!mCheckBox.isChecked()) {
                        selectedTimelineCaption.setText(text);
                        captionDesc.setText(text);
                        mCutCaptionAdapter.refreshTextByIndex(text, position);
                        mPresenter.seekToCaptionStartTime(captionDesc);
                        return;
                    }
                    String groupID = selectedTimelineCaption.getTemplateAttachment(NvsObject.TEMPLATE_KEY_FX_GROUP);
                    if (!TextUtils.isEmpty(groupID)) {
                        for (int i = 0; i < mCaptionData.size(); i++) {
                            TemplateCaptionDesc templateCaptionDesc = mCaptionData.get(i);
                            if (templateCaptionDesc.isCaption()) {
                                if (groupID.equals(templateCaptionDesc.getGroupID())) {
                                    MeicamCaptionClip meicamCaptionClip = EditorController.getInstance().
                                            getCaptionByTemplateCaptionDesc(templateCaptionDesc);
                                    templateCaptionDesc.setText(text);
                                    meicamCaptionClip.setText(text);
                                    mCutCaptionAdapter.refreshTextByIndex(text, i);
                                }
                            }
                        }
                    } else {
                        selectedTimelineCaption.setText(text);
                        captionDesc.setText(text);
                        mCutCaptionAdapter.refreshTextByIndex(text, position);
                    }
                } else if (captionDesc.isCompoundCaption()) {
                    MeicamCompoundCaptionClip nvsTimelineCaption = EditorController.getInstance().
                            getCompCaptionByTemplateCaptionDesc(captionDesc);
                    if (nvsTimelineCaption == null) {
                        LogUtils.e("changeCaptionText: nvsTimelineCaption is NULL! " + captionDesc.replaceId);
                        return;
                    }
                    if (text.isEmpty()) {
                        text = " \r\n ";
                    }
                    captionDesc.setText(index, text);
                    nvsTimelineCaption.setText(index, text);
                    mCutCaptionAdapter.refreshTextByIndex(text, position);
                }
                mPresenter.seekToCaptionStartTime(captionDesc);
            }
        });
        String text = "";
        if (captionDesc.isCaption()) {
            text = captionDesc.text;
            if (TextUtils.isEmpty(text)) {
                text = captionDesc.originalText;
            }
        } else if (captionDesc.isCompoundCaption()) {
            if (CommonUtils.isIndexAvailable(index, captionDesc.itemList)) {
                text = captionDesc.getText(index);
                if (TextUtils.isEmpty(text) || " \r\n ".equals(text)) {
                    text = captionDesc.getOriginalText(index);
                }
            }
        }
        mCaptionEditPop.setCaptionText(text);
        mCaptionEditPop.show();
    }

    /**
     * 显示字幕编辑弹窗
     * Displays a caption edit pop
     */
    public void showCaptionEditPop(int index) {
        final int position = mCutCaptionAdapter.getBeforeSelectIndex();
        if (position < 0) {
            return;
        }
        final TemplateCaptionDesc captionDesc = mCutCaptionAdapter.getItem(position);
        if (captionDesc.isCaption()) {
            index = 0;
        }
        showCaptionEditPop(captionDesc, position, index);
    }

    /**
     * Gets caption bitmap.
     * 获得字幕位图
     *
     * @param index the index
     */
    @Override
    public void getCaptionBitmap(int index) {
        if (mCutCaptionAdapter != null) {
            mCutCaptionAdapter.notifyItemChanged(index);
        }
    }

    /**
     * Gets video data.
     * 获取视频数据
     *
     * @param templateClipList the template clip list
     */
    @Override
    public void getVideoData(List<MeicamNvsTemplateFootageCorrespondingClipInfo> templateClipList) {

        for (MeicamNvsTemplateFootageCorrespondingClipInfo info : templateClipList) {
            if (info.getGroupIndex() != -1) {
                mCheckBox.setVisibility(View.VISIBLE);
                break;
            }
        }
        mVideoData = templateClipList;
        mCutVideoAdapter = new CutVideoAdapter();
        mCutVideoAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, final View view, final int position) {
                if (mCutVideoAdapter.getSelectIndex() == position
                        && selectedClipInfo != null && selectedClipInfo.canReplace && mSelectedVideoClip != null) {
                    view.postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            showVideoEditPop(view, mSelectedVideoClip);
                        }
                    }, 100);
                } else {
                    mCutVideoAdapter.setSelectIndex(position);
                    selectedClipInfo = mVideoData.get(position);
                    if (selectedClipInfo == null) {
                        return;
                    }
                    mSelectedVideoClip = EditorController.getInstance().getVideoClipByTemplateFootageCorrespondingClipInfo(
                            selectedClipInfo);
                    if (mSelectedVideoClip == null) {
                        LogUtils.e("videoClip==null");
                        return;
                    }
                    if (!selectedClipInfo.canReplace) {
                        mCutVideoAdapter.setBeforeSelectIndex(position);
                        EditorController.getInstance().seekTimeline(selectedClipInfo.getRealInpoint());
                        if (mSeekPositionListener != null) {
                            mSeekPositionListener.seekPosition(selectedClipInfo.getRealInpoint());
                        }
                        return;
                    }
                    EditorController.getInstance().seekTimeline(selectedClipInfo.getRealInpoint());
                    if (mSeekPositionListener != null) {
                        mSeekPositionListener.seekPosition(selectedClipInfo.getRealInpoint());
                    }
                    mFragmentRecyclerView.scrollToPosition(position);
                    mCutVideoAdapter.setBeforeSelectIndex(position);
                }
            }
        });
        mFragmentRecyclerView.setAdapter(mCutVideoAdapter);
        for (MeicamNvsTemplateFootageCorrespondingClipInfo info : templateClipList) {
            EditorController.getInstance().handleTemplateFootageCorrespondingClipInfo(info);
        }
        Collections.sort(templateClipList, (meicamNvsTemplateFootageCorrespondingClipInfo, t1) -> (int) (meicamNvsTemplateFootageCorrespondingClipInfo.getRealInpoint() - t1.getRealInpoint()));
        mCutVideoAdapter.setNewData(templateClipList);
    }

    private VideoEditPop mVideoEditPop;

    private void showVideoEditPop(View view, final MeicamVideoClip videoClip) {
        if (getContext() == null) {
            return;
        }
        mVideoEditPop = VideoEditPop.create(view, getContext());
        if (mSelectedVideoClip != null) {
            mVideoEditPop.setVolumeVisible(mSelectedVideoClip.getNvsVideoType() == 0);
        }
        mVideoEditPop.setEventListener(new VideoEditPop.EventListener() {
            @Override
            public void onReplace() {
                if (Utils.isFastClick()) {
                    return;
                }
                if (getActivity() instanceof CutSameEditorActivity) {

                    //找出最长时长的片段 Find the longest segment.
                    MeicamNvsTemplateFootageCorrespondingClipInfo tempClip = selectedClipInfo;
                    if (mCheckBox.isChecked()) {
                        for (MeicamNvsTemplateFootageCorrespondingClipInfo info : mVideoData) {
                            if (selectedClipInfo.getFootageID().equals(info.getFootageID())) {
                                if ((tempClip.trimOut - tempClip.trimIn) < (info.trimOut - info.trimIn)) {
                                    tempClip = info;
                                }
                            }
                        }
                    }
                    Bundle bundle = new Bundle();
                    bundle.putInt(SELECTED_TYPE, TYPE_ONE_FINISH);
                    TemplateClip templateClip = ((CutSameEditorActivity) getActivity()).getTemplateClip(
                            tempClip.getClipIndexInTimelineList(),
                            tempClip.getClipTrackIndexInTimelineList(),
                            tempClip.trackIndex, tempClip.inpoint);
                    templateClip = templateClip.copy();
                    //清空路径 Clear Path.
                    templateClip.setFilePath("");
                    templateClip.setReversePath("");
                    bundle.putParcelable(TEMPLATE_CLIP, templateClip);
                    AppManager.getInstance().jumpActivityForResult(getActivity(), MaterialSelectActivity.class, bundle, REQUEST_CODE_1);
                }

            }

            @Override
            public void onCut() {
                if (Utils.isFastClick()) {
                    return;
                }
                Bundle bundle = new Bundle();
                bundle.putString(TailorActivity.VIDEO_PATH, videoClip.getFilePath());
                Object attachment = videoClip.getAttachment(CommonData.ATTACHMENT_KEY_VIDEO_RATIO);
                if (attachment instanceof Float) {
                    float ratio = (float) attachment;
                    bundle.putFloat(TailorActivity.RAW_RATIO, ratio);
                }
                bundle.putParcelable(TailorActivity.TRANSFORM_DATA, NvTemplateDataAdjustTool.getInitCutData(selectedClipInfo));
                bundle.putLong(TailorActivity.VIDEO_LIMIT, (selectedClipInfo.trimOut - selectedClipInfo.trimIn));
                int clipType = StreamingConstant.VideoClipType.VIDEO_CLIP_TYPE_AV;
                long trimIn = videoClip.getTrimIn();
                if (videoClip.getNvsVideoType() != 0) {
                    trimIn = 0;
                    clipType = StreamingConstant.VideoClipType.VIDEO_CLIP_TYPE_IMAGE;
                }
                bundle.putInt(TailorActivity.VIDEO_TYPE, clipType);
                bundle.putLong(TailorActivity.START_TRIM, trimIn);
                AppManager.getInstance().jumpActivityForResult(getActivity(), TailorActivity.class, bundle, CutSameEditorActivity.INTENT_FLAG);
            }

            @Override
            public void onVolume() {
                if (Utils.isFastClick()) {
                    return;
                }
                if (getContext() == null) {
                    return;
                }

                showVideoEditVolumePop();
            }
        });
        mVideoEditPop.show();
    }

    private VideoEditVolumePop mVideoEditVolumePop = null;

    private void showVideoEditVolumePop() {
        if (mSelectedVideoClip == null) {
            return;
        }
        int volume = (int) (mSelectedVideoClip.getVolume() * Constants.maxVolumeProgress / Constants.maxNvVolume);
        mVideoEditVolumePop = VideoEditVolumePop.create(getContext(), volume,
                new VideoEditVolumePop.VideoVolumeListener() {
                    @Override
                    public void changeVolume(int progress) {
                        mSelectedVideoClip.setVolume(progress * Constants.maxNvVolume / Constants.maxVolumeProgress);
                        EditorController.getInstance().playNow();
                    }
                });
        mVideoEditVolumePop.show();
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == REQUEST_CODE_1 && VIDEO.equals(mType)) {
            mVideoEditPop.dismiss();
            if (data != null) {
                TemplateClip templateClip = data.getParcelableExtra(TEMPLATE_CLIP);
                if (!mCheckBox.isChecked()) {
                    mCutVideoAdapter.notifyItemChanged(mCutVideoAdapter.getSelectIndex());
                    mPresenter.dealVideoReplace(templateClip);
                    return;
                }
                for (int i = 0; i < mVideoData.size(); i++) {
                    MeicamNvsTemplateFootageCorrespondingClipInfo clipInfo = mVideoData.get(i);
                    if (templateClip.getFootageId().equals(clipInfo.getFootageID())) {
                        TemplateClip clip = ((CutSameEditorActivity) getActivity()).getTemplateClip(
                                clipInfo.getClipIndexInTimelineList(),
                                clipInfo.getClipTrackIndexInTimelineList(),
                                clipInfo.trackIndex, clipInfo.inpoint);
                        clip.setFilePath(templateClip.getFilePath());
                        mCutVideoAdapter.notifyItemChanged(i);
                        mPresenter.dealVideoReplace(clip);
                    }
                }

            }
        }
    }

    /**
     * Need seek position.
     * 需要找位置
     *
     * @param position the position
     */
    @Override
    public void needSeekPosition(long position, List<PointF> pointFList, float scaleValue) {
        if (mSeekPositionListener != null) {
            mSeekPositionListener.seekPosition(position);
        }
        if (CutEditorVpPresenter.CAPTION.equals(mType)) {
            if (getActivity() instanceof CutSameEditorActivity) {
                ((CutSameEditorActivity) getActivity()).showCaptionBox(pointFList, scaleValue);
            }
        }
    }

    @Override
    public void needSeekCompoundPosition(long position, List<PointF> pointFList, List<List<PointF>> childPointFList, float scaleValue) {
        if (mSeekPositionListener != null) {
            mSeekPositionListener.seekPosition(position);
        }
        if (CutEditorVpPresenter.CAPTION.equals(mType)) {
            if (getActivity() instanceof CutSameEditorActivity) {
                ((CutSameEditorActivity) getActivity()).showCompoundCaptionBox(pointFList, childPointFList, scaleValue);
            }
        }
    }

    /**
     * Clear select on play.
     * 清除播放时的选择
     */
    public void clearSelectOnPlay() {
        if (mCutCaptionAdapter != null) {
            mCutCaptionAdapter.setSelectIndex(-1);
            mCutCaptionAdapter.setBeforeSelectIndex(-2);
        }

        if (mCutVideoAdapter != null) {
            mCutVideoAdapter.setSelectIndex(-1);
            mCutVideoAdapter.setBeforeSelectIndex(-2);
        }
    }

    /**
     * On destroy view.
     * 摧毁视图
     */
    @Override
    public void onDestroyView() {
        super.onDestroyView();
        mSelectedVideoClip = null;
    }

    /**
     * The interface Seek position listener.
     * 接口查找位置侦听器
     */
    public interface SeekPositionListener {
        /**
         * Seek position.
         * 寻找位置
         *
         * @param position the position
         */
        void seekPosition(long position);
    }
}
