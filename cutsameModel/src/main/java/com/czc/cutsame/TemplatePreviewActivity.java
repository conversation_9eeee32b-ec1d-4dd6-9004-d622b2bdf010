package com.czc.cutsame;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.os.Parcelable;
import android.text.TextUtils;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.media3.common.MediaItem;
import androidx.media3.common.Player;
import androidx.media3.exoplayer.ExoPlayer;
import androidx.media3.ui.PlayerView;

import com.czc.cutsame.bean.Template;
import com.czc.cutsame.bean.TemplateCategory;
import com.czc.cutsame.bean.TemplateClip;
import com.czc.cutsame.fragment.iview.TemplateView;
import com.czc.cutsame.fragment.presenter.TemplatePresenter;
import com.czc.cutsame.util.ConfigUtil;
import com.czc.cutsame.util.VideoCache;
import com.meicam.sdk.NvsAssetPackageManager;
import com.meishe.base.manager.AppManager;
import com.meishe.base.model.BaseMvpActivity;
import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.FormatUtils;
import com.meishe.base.utils.LogUtils;
import com.meishe.base.utils.ScreenUtils;
import com.meishe.base.utils.ToastUtils;
import com.meishe.base.utils.Utils;
import com.meishe.engine.EngineCallbackManager;
import com.meishe.engine.EngineNetApi;
import com.meishe.engine.asset.bean.AssetDownloadInfo;
import com.meishe.engine.bean.template.MeicamNvsTemplateFootageCorrespondingClipInfo;
import com.meishe.engine.bean.template.MeicamNvsTemplateFootageDesc;
import com.meishe.engine.editor.EditorController;
import com.meishe.engine.observer.EngineCallbackObserver;
import com.meishe.engine.util.PathUtils;
import com.meishe.net.custom.BaseResponse;
import com.meishe.net.custom.RequestCallback;

import java.io.File;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static androidx.media3.common.Player.STATE_BUFFERING;
import static androidx.media3.common.Player.STATE_READY;
import static com.meishe.logic.constant.PagerConstants.DATA_TEMPLATE;
import static com.meishe.logic.constant.PagerConstants.TEMPLATE_CLIP_LIST;
import static com.meishe.logic.constant.PagerConstants.TEMPLATE_ID;
import static com.meishe.logic.constant.PagerConstants.TEMPLATE_IS_FROM_LOCAL;
import static com.meishe.logic.constant.PagerConstants.TEMPLATE_NEED_EDIT;

/**
 * author : lhz
 * date   : 2020/11/4
 * desc   :模板预览页面
 * Template Preview Page
 */
public class TemplatePreviewActivity extends BaseMvpActivity<TemplatePresenter> implements TemplateView {
    public static boolean IS_DEBUG = false;
    private ImageView mIvBack, mIvStop;
    private PlayerView mVideoView;
    private ExoPlayer mExoPlayer;
    private TextView mTvUserName;
    private TextView mTvDescription;
    private TextView mTvUsedNum;
    private TextView mTvClipSame;
    private Template mTemplate;
    private String mTemplatePath;
    private StringBuilder mTemplateId;
    private List<TemplateClip> mClipList = null;
    private boolean mIsFromLocal;
    private View mRootView;
    private boolean mIsBack = false;
    private int mInstallState = NvsAssetPackageManager.ASSET_PACKAGE_STATUS_INSTALLING;
    private EngineCallbackObserver mCallbackObserver;
    private ProgressBar mLoadingView;

    @Override
    protected int bindLayout() {
        return R.layout.activity_template_preview;
    }

    @Override
    protected void initData(Bundle savedInstanceState) {
        Intent intent = getIntent();
        if (intent != null) {
            mTemplate = intent.getParcelableExtra(DATA_TEMPLATE);
            mIsFromLocal = intent.getBooleanExtra(TEMPLATE_IS_FROM_LOCAL, false);
            if (mTemplate != null) {
                if (mTemplate.isFromLocal()) {
                    mTemplatePath = mTemplate.getPackageUrl();
                }
            }
        }
    }

    @Override
    protected void initView() {
        mIvBack = findViewById(R.id.iv_back);
        mIvStop = findViewById(R.id.iv_stop_hint);
        mVideoView = findViewById(R.id.video_view);
        mTvUserName = findViewById(R.id.tv_user_name);
        mTvDescription = findViewById(R.id.tv_description);
        mTvUsedNum = findViewById(R.id.tv_used_num);
        mTvClipSame = findViewById(R.id.tv_clip);
        mRootView = findViewById(R.id.rl_root_view);
        mLoadingView = findViewById(R.id.loading_video_view);
        RelativeLayout.LayoutParams layoutParams = (RelativeLayout.LayoutParams) mIvBack.getLayoutParams();
        layoutParams.topMargin = (int) (ScreenUtils.getStatusBarHeight() + getResources().getDimension(R.dimen.title_margin_top));
        mIvBack.setLayoutParams(layoutParams);
        initializePlayer();
        //initVideoViewSize();
        initListener();
    }

    private void initializePlayer() {
        File cacheFile = new File(PathUtils.getTemplateDir(), "video");
        // 本地最多保存512M, 按照LRU原则删除老数据
        // Save up to 512M locally and delete old data according to LRU principles.
//        mExoPlayer = ExoPlayerFactory.newSimpleInstance(this,
//                new DefaultRenderersFactory(this),
//                new DefaultTrackSelector(), new DefaultLoadControl());

        mExoPlayer = new ExoPlayer.Builder(this).build();
        mVideoView.setPlayer(mExoPlayer);
    }

    @Deprecated
    private void initVideoViewSize() {
        mRootView.post(new Runnable() {
            @Override
            public void run() {
                if (isDestroyed()) {
                    return;
                }
                ViewGroup.LayoutParams videoParams = mVideoView.getLayoutParams();
                if (videoParams != null && mTemplate != null && mTemplate.getUseNum() != -1) {
                    float ratio = mTemplate.getRatio();
                    String previewVideoUrl = mTemplate.getPreviewVideoUrl();
                    if (!TextUtils.isEmpty(previewVideoUrl) && mIsFromLocal) {
                        int[] videoSize = EditorController.getAVFileSize(previewVideoUrl);
                        if (videoSize != null) {
                            ratio = videoSize[0] * 1.0F / videoSize[1];
                        }
                    }
                    int screenWidth = mRootView.getWidth();
                    int screenHeight = mRootView.getHeight();
                    float viewRatio = screenWidth * 1.0F / screenHeight;
                    if (ratio > viewRatio) {
                        //宽对齐Wide alignment
                        videoParams.width = screenWidth;
                        videoParams.height = (int) (screenWidth / ratio);
                    } else {
                        videoParams.width = (int) (screenHeight * ratio);
                        videoParams.height = screenHeight;
                    }
                    mVideoView.setLayoutParams(videoParams);
                }
            }
        });
    }


    @SuppressLint("ClickableViewAccessibility")
    private void initListener() {
        mIvBack.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mIsBack = true;
                onBackPressed();
            }
        });
        mVideoView.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                //VideoView的点击事件无效，而且只有Down事件,所以用onTouch
                //The click event for VideoView is invalid and only has a Down event, so onTouch is used.
                if (event.getAction() == MotionEvent.ACTION_DOWN) {
                    if (mExoPlayer != null && mExoPlayer.isPlaying()) {
                        mExoPlayer.setPlayWhenReady(false);
                        mIvStop.setVisibility(View.VISIBLE);
                    } else {
                        mIvStop.setVisibility(View.GONE);
                        playVideo();
                    }
                }
                return false;
            }
        });
        mTvClipSame.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (Utils.isFastClick()) {
                    return;
                }
                if (mInstallState == NvsAssetPackageManager.ASSET_PACKAGE_STATUS_INSTALLING) {
                    ToastUtils.showShort(R.string.template_not_ready);
                    return;
                }
                if (!isInstallSuccess(mInstallState)) {
                    ToastUtils.showShort(R.string.template_install_error);
                    return;
                }
//                if (!TextUtils.isEmpty(mTemplatePath) && mClipList != null && mTemplateId != null && mTemplate != null) {
                clickAssetItem(mTemplate.getId());
                Intent intent = new Intent(TemplatePreviewActivity.this, MaterialSelectActivity.class);
                intent.putExtra(DATA_TEMPLATE, mTemplate);
                intent.putExtra(TEMPLATE_ID, mTemplateId.toString());
                intent.putExtra(TEMPLATE_NEED_EDIT, mTemplate.getCategory() != 3);
                intent.putParcelableArrayListExtra(TEMPLATE_CLIP_LIST, (ArrayList<? extends Parcelable>) mClipList);
                startActivity(intent);
                //finish();
//                } else {
//                    ToastUtils.showShort(R.string.template_not_ready);
//                }
            }
        });

        mExoPlayer.addListener(new Player.Listener() {
            @Override
            public void onPlaybackStateChanged(int playbackState) {
                Player.Listener.super.onPlaybackStateChanged(playbackState);
                if (playbackState == STATE_BUFFERING) {
                    mLoadingView.setVisibility(View.VISIBLE);
                } else if (playbackState == STATE_READY) {
                    mLoadingView.setVisibility(View.INVISIBLE);
                }
            }
        });
    }

    /**
     * Click asset item.
     * 点击资源项
     *
     * @param packageId the package Id 包id
     */
    public void clickAssetItem(String packageId) {
        if (TextUtils.isEmpty(packageId)) {
            return;
        }
        EngineNetApi.downloadOrClick(packageId, "2", new RequestCallback<AssetDownloadInfo>() {
            @Override
            public void onSuccess(BaseResponse<AssetDownloadInfo> response) {
                LogUtils.d("clickAssetItem success");
            }

            @Override
            public void onError(BaseResponse<AssetDownloadInfo> response) {
                LogUtils.d("clickAssetItem error");
            }
        });
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (mCallbackObserver != null) {
            EngineCallbackManager.get().unregisterCallbackObserver(mCallbackObserver);
        }
        if (mExoPlayer != null) {
            mExoPlayer.setPlayWhenReady(false);
            mExoPlayer.release();
            mExoPlayer = null;
        }
        VideoCache.release();
    }

    private void playVideo() {
        if (!mIsBack) {
            if (mExoPlayer != null) {
                mExoPlayer.setPlayWhenReady(true);
            }
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        if (mExoPlayer != null) {
            mExoPlayer.setPlayWhenReady(false);
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        mIsBack = false;
        if (mExoPlayer != null) {
            if (!mExoPlayer.isPlaying()) {
                mIvStop.setVisibility(View.GONE);
                playVideo();
            }
        }
    }

    @Override
    protected void requestData() {
        if (mTemplate != null) {
            if (IS_DEBUG) {
                mTemplatePath = mTemplate.getPackageUrl();
                startInstallAssets(mTemplatePath, mTemplate.getLicPath());
            } else {
                Template.Producer producer = mTemplate.getProducer();
                if (producer != null) {
                    String name = "@" + producer.getNickName();
                    mTvUserName.setText(name);
                }
                mTvDescription.setText(Utils.isZh() ? mTemplate.getDescriptionZhCn() : mTemplate.getDescription());
                if (mTemplate.getUseNum() == -1) {
                    mTvUsedNum.setText(String.format(getString(R.string.template_duration),
                            FormatUtils.sec2Time((int) (mTemplate.getDuration() / 1000)), mTemplate.getShotsNumber()));
                } else {
                    mTvUsedNum.setText(String.format(getString(R.string.template_duration_used_num),
                            FormatUtils.sec2Time((int) (mTemplate.getDuration() / 1000)), formatNumber(mTemplate.getUseNum()), mTemplate.getShotsNumber()));
                }

                if (!mTemplate.isFromLocal()) {
                    if (!mPresenter.checkTemplateUpdate(mTemplate)) {
                        //不需要更新 not need to be updated
                        playVideo(mTemplate.getPreviewVideoUrl());
                    } else {
                        mLoadingView.setVisibility(View.VISIBLE);
                    }
                } else {
                    if (ConfigUtil.isToC() || mTemplate.isFromLocal()) {
                        startInstallAssets(mTemplatePath, mTemplate.getLicPath());
                        playVideo(mTemplate.getPreviewVideoUrl());
                    } else {
                        if (!mPresenter.checkTemplateUpdate(mTemplate)) {
                            //不需要更新 not need to be updated
                            playVideo(mTemplate.getPreviewVideoUrl());
                        } else {
                            mLoadingView.setVisibility(View.VISIBLE);
                        }
                    }
                }
            }
        }
    }

    private void playVideo(String videoPath) {
        if (!TextUtils.isEmpty(videoPath)) {
            Uri uri = Uri.parse(videoPath);
            MediaItem mediaItem = MediaItem.fromUri(uri);
            mExoPlayer.setMediaItem(mediaItem);
            mExoPlayer.prepare();
            mExoPlayer.setPlayWhenReady(true);

//
//            DefaultBandwidthMeter defaultBandwidthMeter = new DefaultBandwidthMeter();
//            DataSource.Factory factory = new DefaultDataSourceFactory(this, com.google.android.exoplayer2.util.Util.getUserAgent(this, "meiying"), defaultBandwidthMeter);
//            CacheDataSourceFactory cachedDataSourceFactory = new CacheDataSourceFactory(VideoCache.getInstance(), factory);
//            ExtractorsFactory extractorsFactory = new DefaultExtractorsFactory();
//            MediaSource mediaSource = new ExtractorMediaSource(uri,
//                    cachedDataSourceFactory, extractorsFactory, null, null);
//            if (mExoPlayer != null) {
//                mExoPlayer.prepare(mediaSource);
//                mExoPlayer.setRepeatMode(Player.REPEAT_MODE_ONE);
//            }
        }
    }

    @Override
    public void onTemplateCategoryBack(List<TemplateCategory.Category> categoryList) {

    }

    @Override
    public void onTemplateListBack(List<Template> templateList) {

    }

    @Override
    public void onMoreTemplateBack(List<Template> templateList) {

    }

    @Override
    public void onDownloadTemplateSuccess(String templatePath, String licPath, boolean isTemplate) {
        if (isTemplate) {
            mLoadingView.setVisibility(View.INVISIBLE);
            mTemplatePath = templatePath;
            startInstallAssets(mTemplatePath, licPath);
            playVideo(mTemplate.getPreviewVideoUrl());
        }
    }

    private void startInstallAssets(String templatePath, String licPath) {
        mTemplateId = new StringBuilder();
        try {
            String packageId = templatePath;
            packageId = packageId.substring(packageId.lastIndexOf("/") + 1);
            packageId = packageId.split("\\.")[0];
            //由于升级的存在，这里取巧了，先卸载后安装。 Because of the existence of upgrade, here take advantage of, install after uninstall first
            EditorController.getInstance().uninstallAssetPackage(packageId, EditorController.ASSET_PACKAGE_TYPE_TEMPLATE);
        } catch (Exception e) {
            LogUtils.e("Exception=" + e);
        }
        EngineCallbackManager.get().registerCallbackObserver(mCallbackObserver = new EngineCallbackObserver() {
            @Override
            public boolean isActive() {
                return !isFinishing() && TemplatePreviewActivity.this.equals(AppManager.getInstance().currentActivity());
            }

            @Override
            public void onFinishAssetPackageInstallation(String packageId, String filePath, int packageType, int error) {
                LogUtils.d("error = " + error);
                if (isInstallSuccess(error)) {
                    int groupIndex = -1;
                    List<MeicamNvsTemplateFootageDesc> templateList = EditorController.getInstance().getTemplateFootageDescVideo(mTemplateId.toString());
                    if (templateList != null) {
                        mClipList = new ArrayList<>();
                        for (MeicamNvsTemplateFootageDesc footage : templateList) {
                            if (!footage.canReplace) {
                                //audio类型无用。Do not replace the audio.

                                continue;
                            }
                            List<MeicamNvsTemplateFootageCorrespondingClipInfo> correspondingClipInfos = new ArrayList<>();
                            for (NvsAssetPackageManager.NvsTemplateFootageCorrespondingClipInfo clipInfo : footage.correspondingClipInfos) {
                                MeicamNvsTemplateFootageCorrespondingClipInfo correspondingClipInfo = new MeicamNvsTemplateFootageCorrespondingClipInfo();
                                correspondingClipInfo.setNvsTemplateFootageCorrespondingClipInfo(clipInfo);
                                correspondingClipInfos.add(correspondingClipInfo);
                            }
                            boolean hasGroup = false;
                            if (!CommonUtils.isEmpty(correspondingClipInfos) && correspondingClipInfos.size() >= 2) {
                                hasGroup = true;
                                groupIndex += 1;
                            }
                            for (MeicamNvsTemplateFootageCorrespondingClipInfo clipInfo : correspondingClipInfos) {
                                mClipList.add(new TemplateClip()
                                        .setDuration(clipInfo.outpoint - clipInfo.inpoint)
                                        .setTrimDuration(clipInfo.trimOut - clipInfo.trimIn)
                                        .setTrimIn(clipInfo.trimIn)
                                        .setInPoint(clipInfo.inpoint)
                                        .setNeedReverse(clipInfo.needReverse)
                                        .setTrackIndex(clipInfo.trackIndex)
                                        .setType(footage.type)
                                        .setHasGroup(hasGroup)
                                        .setGroupIndex(groupIndex)
                                        .setClipIndex(clipInfo.clipIndex)
                                        .setClipIndexInTimelineList(footage.getClipIndexInTimelineList())
                                        .setClipTrackIndexInTimelineList(footage.getClipTrackIndexInTimelineList())
                                        .setFootageId(footage.id));
                            }
                        }
                        Collections.sort(mClipList);
                        if (groupIndex == -1) {
                            //说明没有编组，适配AE模板，再计算一下编组信息
                            // Explain that there is no grouping. Adapt the AE template and calculate
                            // the grouping information again.
                            Map<String, List<TemplateClip>> map = new HashMap<>();
                            String key = "";
                            List<TemplateClip> tempList = null;
                            for (TemplateClip templateClip : mClipList) {
                                key = templateClip.getFootageId();
                                tempList = map.get(key);
                                if (null == tempList) {
                                    tempList = new ArrayList<>();
                                    map.put(key, tempList);
                                }
                                tempList.add(templateClip);
                            }

                            for (List<TemplateClip> templateClips : map.values()) {
                                if (templateClips.size() > 1) {
                                    //有同一编组 It is the same group id.
                                    groupIndex += 1;
                                    for (TemplateClip templateClip : templateClips) {
                                        templateClip.setGroupIndex(groupIndex);
                                        templateClip.setHasGroup(true);
                                    }
                                }
                            }
                        }


                        mTemplate.setShotsNumber(mClipList == null ? 0 : mClipList.size());
                        if (mTemplate.getUseNum() == -1) {
                            mTvUsedNum.setText(String.format(getString(R.string.template_duration),
                                    FormatUtils.sec2Time((int) (mTemplate.getDuration() / 1000)), mTemplate.getShotsNumber()));
                        } else {
                            mTvUsedNum.setText(String.format(getString(R.string.template_duration_used_num),
                                    FormatUtils.sec2Time((int) (mTemplate.getDuration() / 1000)), formatNumber(mTemplate.getUseNum()), mClipList.size()));
                        }
                    }
                }
                mInstallState = error;
//                if (CommonUtils.isEmpty(mClipList)) {
//                    mInstallState = NvsAssetPackageManager.ASSET_PACKAGE_MANAGER_ERROR_INVALID_PACKAGE;
//                }
                EngineCallbackManager.get().unregisterCallbackObserver(mCallbackObserver);
                mCallbackObserver = null;
            }

            @Override
            public void onFinishAssetPackageUpgrading(String s, String s1, int i, int i1) {
                mInstallState = i1;
                if (CommonUtils.isEmpty(mClipList)) {
                    mInstallState = NvsAssetPackageManager.ASSET_PACKAGE_MANAGER_ERROR_INVALID_PACKAGE;
                }
                EngineCallbackManager.get().unregisterCallbackObserver(mCallbackObserver);
                mCallbackObserver = null;
            }

        });
        EditorController.getInstance().installAssetPackagedNoSynchronous(templatePath, licPath, EditorController.ASSET_PACKAGE_TYPE_TEMPLATE, mTemplateId);
    }

    private boolean isInstallSuccess(int error) {
        return (error == EditorController.ASSET_PACKAGE_MANAGER_ERROR_NO_ERROR) || (error == EditorController.ASSET_PACKAGE_MANAGER_ERROR_ALREADY_INSTALLED);
    }


    /**
     * Format number string.
     * 格式字符串数
     *
     * @param number the number
     * @return the string
     */
    public String formatNumber(int number) {
        if (number < 10000) {
            return number + "";
        }
        return FormatUtils.objectFormat2String(number / 10000f) + getString(R.string.num_unit_w);
    }
}
