package com.czc.cutsame.presenter;

import android.graphics.Bitmap;
import android.text.TextUtils;

import com.czc.cutsame.R;
import com.czc.cutsame.iview.IExportTemplate;
import com.meicam.sdk.NvsStreamingContext;
import com.meicam.sdk.NvsTimeline;
import com.meishe.base.model.Presenter;
import com.meishe.base.utils.FileIOUtils;
import com.meishe.base.utils.LogUtils;
import com.meishe.base.utils.ThreadUtils;
import com.meishe.base.utils.ToastUtils;
import com.meishe.base.utils.Utils;
import com.meishe.engine.EditorEngine;
import com.meishe.engine.EngineCallbackManager;
import com.meishe.engine.ExportTemplateManager;
import com.meishe.engine.asset.AssetsManager;
import com.meishe.engine.asset.bean.TemplateUploadParam;
import com.meishe.engine.bean.MeicamTimeline;
import com.meishe.engine.bean.template.ExportTemplateSection;
import com.meishe.engine.observer.EngineCallbackObserver;
import com.meishe.libplugin.PluginManager;
import com.meishe.libplugin.user.IUserPlugin;
import com.meishe.net.custom.BaseResponse;
import com.meishe.net.custom.RequestCallback;
import com.meishe.net.model.Progress;
import com.meishe.net.utils.IOUtils;

import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> LiHangZhou
 * @CreateDate :2021/5/13 16:04
 * @Description :导出模板逻辑处理类 Export template presenter
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class ExportTemplatePresenter extends Presenter<IExportTemplate> {
    /**
     * compile占用上传时间的百分比 The percent of  compiling cast time in uploading total time.
     */
    private final int PROGRESS_PERCENT_COMPILE = 52;
    private final int PROGRESS_MAX = 100;
    private final int PROGRESS_UPLOAD_TOTAL = 98;

    private NvsStreamingContext mStreamingContext;
    private MeicamTimeline mTimeline;
    private int mCurrentProgress = PROGRESS_PERCENT_COMPILE;
    private EngineCallbackObserver mCallbackObserver;

    private List<ExportTemplateSection> mTemplateSectionList;
    private ExportTemplateManager mExportManager;
    private ThreadUtils.SimpleTask<Boolean> mCheckAccessTask;


    /**
     * 初始化数据
     * init data
     *
     * @param templateSectionList the template list 模板片段集合
     */
    public boolean initData(List<ExportTemplateSection> templateSectionList) {
        mTemplateSectionList = templateSectionList;
        mTimeline = EditorEngine.getInstance().getCurrentTimeline();
        mStreamingContext = EditorEngine.getInstance().getStreamingContext();
        initListener();
        grabImageFromTimelineAsync();
        return true;
    }

    private void initListener() {
        EngineCallbackManager.get().registerCallbackObserver(mCallbackObserver = new EngineCallbackObserver() {
            @Override
            public boolean isActive() {
                return getView().isActive();
            }

            @Override
            public void onImageGrabbedArrived(Bitmap bitmap, long time) {
                if (getView() != null) {
                    getView().imageGrabbedArrived(bitmap);
                }
            }
        });
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        EngineCallbackManager.get().unregisterCallbackObserver(mCallbackObserver);
        if (mExportManager != null) {
            mExportManager.release();
        }
    }

    /**
     * 检查是否上传
     * Check whether to upload
     *
     * @param uploadParam 上传模板信息 the template upload parameter.
     */
    private void checkUpload(TemplateUploadParam uploadParam) {
        if (uploadParam != null) {
            mCurrentProgress = PROGRESS_PERCENT_COMPILE;
            final IUserPlugin userPlugin = PluginManager.get().getUserPlugin();
            //上传模板 Upload template.
            AssetsManager.get().uploadTemplate("UserUpload", userPlugin.getToken(), uploadParam, new RequestCallback<Object>() {
                @Override
                public void onSuccess(BaseResponse<Object> response) {
                    if (getView() != null) {
                        getView().onCompileProgress(PROGRESS_MAX);
                    }
                    dealWithExportComplete(true);
                    if (mExportManager != null) {
                        mExportManager.dealWithExportComplete(true);
                    }
                }

                @Override
                public void onError(BaseResponse<Object> response) {
                    dealWithExportComplete(false);
                }

                @Override
                public void uploadProgress(Progress progress) {
                    int tempProgress = (int) (PROGRESS_PERCENT_COMPILE + progress.fraction * (100 - PROGRESS_PERCENT_COMPILE));
                    if (tempProgress > PROGRESS_UPLOAD_TOTAL) {
                        tempProgress = PROGRESS_UPLOAD_TOTAL;
                    }
                    //The progress should be more and more big.
                    // If it suddenly becomes smaller, it means that the network is abnormal,
                    // and the upload is repeated, and it needs to be cancelled
                    //数据应该是越来越大，如果突然变小，说明网络异常，重复上传了，需要取消
                    if (mCurrentProgress > tempProgress) {
                        AssetsManager.get().cancelTask("UserUpload");
                        ToastUtils.showShort(Utils.getApp().getResources().getString(R.string.user_hint_assets_net_error));
                        dealWithExportComplete(false);
                        return;
                    }
                    if (getView() != null) {
                        getView().onCompileProgress(tempProgress);
                    }
                    mCurrentProgress = tempProgress;
                }
            });
        }
    }

    /**
     * 从时间线上抓图
     * Grab image from timeline
     */
    private void grabImageFromTimelineAsync() {
        if (mTimeline == null || mStreamingContext == null) {
            return;
        }
        EditorEngine.getInstance().grabImageFromTimelineAsync(mTimeline, 0, null);
    }

    /**
     * 是否需要上传
     * Do you need to upload?
     *
     * @return true need 需要上传，false not 不需要。
     */
    public boolean needUpload() {
        IUserPlugin userPlugin = PluginManager.get().getUserPlugin();
        return userPlugin != null && userPlugin.isLogin();
    }

    /**
     * 取消导出模板
     * Cancel export template
     */
    public void cancelExportTemplate() {
        if (mExportManager != null) {
            mExportManager.cancelTask();
        }
      /*  if (mStreamingContext != null) {
            mStreamingContext.stop();
            mStreamingContext.setCompileConfigurations(null);
        }
        mCancelExport = true;*/
        IUserPlugin userPlugin = PluginManager.get().getUserPlugin();
        if (userPlugin != null && userPlugin.isLogin()) {
            AssetsManager.get().cancelTask("UserUpload");
        }
        if (mCheckAccessTask != null) {
            ThreadUtils.cancel(mCheckAccessTask);
            mCheckAccessTask = null;
        }
        ToastUtils.showShort(R.string.activity_cut_export_template_cancel);
        dealWithExportComplete(false);
    }

    /**
     * 处理模板导出完成
     * A method to handle template export completion
     *
     * @param success true success 成功，false failed 失败
     */
    private void dealWithExportComplete(boolean success) {
        if (getView() != null) {
            getView().onCompileComplete(success);
        }
    }

    /**
     * 导出模板
     * Export template
     *
     * @param templateName the template name 模板名称
     * @param templateDesc the template description 模板描述
     */
    public void exportTemplate(final String templateName, final String templateDesc) {
        if (TextUtils.isEmpty(templateName) || TextUtils.isEmpty(templateDesc)) {
            return;
        }

        mExportManager = new ExportTemplateManager();
        mExportManager.setOnExportListener(new ExportTemplateManager.OnExportListener() {

            @Override
            public void onCompleted(final TemplateUploadParam uploadParam, boolean success) {
                if (success && needUpload()) {
                    ThreadUtils.executeByFixed(1, mCheckAccessTask = new ThreadUtils.SimpleTask<Boolean>() {
                        @Override
                        public Boolean doInBackground() throws Throwable {
                            return FileIOUtils.waitUntilAccess(uploadParam.materialFile);
                        }

                        @Override
                        public void onSuccess(Boolean result) {
                            mCheckAccessTask = null;
                            if (result && isActive()) {
                                ThreadUtils.runOnUiThread(new Runnable() {
                                    @Override
                                    public void run() {
                                        boolean canRead = IOUtils.canRead(uploadParam.materialFile.getAbsolutePath());
                                        LogUtils.d("checkUpload: can read = "+canRead);
                                        if (canRead) {
                                            checkUpload(uploadParam);
                                        } else {
                                            ThreadUtils.runOnUiThreadDelayed(this, 100);
                                        }
                                    }
                                });
                            } else {
                                dealWithExportComplete(false);
                            }
                        }
                    });
                } else {
                    dealWithExportComplete(success);
                }
            }

            @Override
            public void onCanceled(boolean isCanceled) {
                if (isCanceled && !needUpload()) {
                    ToastUtils.showShort(R.string.activity_cut_export_template_cancel);
                }
            }

            @Override
            public void onFailed(NvsTimeline timeline) {
                LogUtils.d("onCompileFailed");
            }

            @Override
            public void onProgress(float progress) {
                if (getView() != null) {
                    if (needUpload()) {
                        getView().onCompileProgress((int) (progress / 100F * PROGRESS_PERCENT_COMPILE));
                    } else {
                        getView().onCompileProgress((int) progress);
                    }
                }
            }

            @Override
            public boolean isActive() {
                try {
                    return ExportTemplatePresenter.this.getView() != null && ExportTemplatePresenter.this.getView().isActive();
                } catch (Exception e) {
                    LogUtils.e(e);
                }
                return false;
            }
        });
        if (mExportManager.exportTemplate(mTimeline, mStreamingContext, templateName, templateDesc, mTemplateSectionList)) {
            if (getView() != null) {
                getView().onCompileStart();
            }
        } else {
            if (getView() != null) {
                getView().onCompileComplete(false);
            }
        }
    }

}
