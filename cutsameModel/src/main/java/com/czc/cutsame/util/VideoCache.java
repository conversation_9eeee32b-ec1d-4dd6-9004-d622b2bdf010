package com.czc.cutsame.util;

import androidx.media3.datasource.cache.LeastRecentlyUsedCacheEvictor;
import androidx.media3.datasource.cache.SimpleCache;

import com.meishe.engine.util.PathUtils;

import java.io.File;

/**
 * All rights Reserved, Designed By www.meishesdk.com
 *
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2022/6/22 13:16
 * @Description:
 * @Copyright: www.meishesdk.com Inc. All rights reserved.
 */
public class VideoCache {
    private static SimpleCache sDownloadCache;

    public static SimpleCache getInstance() {
        if (sDownloadCache == null) {
            File cacheFile = new File(PathUtils.getTemplateDir(), "video");
            sDownloadCache = new SimpleCache(cacheFile, new LeastRecentlyUsedCacheEvictor(512 * 1024 * 1024));
        }
        return sDownloadCache;
    }

    public static void release() {
        if (sDownloadCache != null) {
            sDownloadCache.release();
            sDownloadCache = null;
        }
    }
}
