package com.czc.cutsame.view;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.PointF;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffXfermode;
import android.graphics.Rect;
import android.graphics.RectF;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;

import androidx.annotation.Nullable;

import com.czc.cutsame.R;
import com.meishe.base.utils.SizeUtils;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2021/11/17 13:09
 * @Description :区域选择view The view for rect selection
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class RectSelectView extends View {
    private static final int TWO_FINGER = 2;
    private static final int ONE_FINGER = 1;
    private int mSlop;
    private int mInnerPadding;
    private Paint mPaint;
    private float mRectRatio = 0.5F;
    private Rect mRect = new Rect();
    /**
     * 线宽的一半
     * Half width of stroke
     */
    private int mHalfStrokeWidth;
    private float lastEventX;
    private float lastEventY;
    private long lastEventTime;
    private EventListener mEventListener;
    private boolean mIsTwoFingerEvent;
    private double mTwoFingerStartLength;

    private PointF mTwoFingerOldPoint = new PointF();

    public RectSelectView(Context context) {
        super(context);
    }

    public RectSelectView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        mHalfStrokeWidth = SizeUtils.dp2px(1);
        TypedArray ta = context.obtainStyledAttributes(attrs, R.styleable.RectSelectView);
        mInnerPadding = (int) ta.getDimension(R.styleable.RectSelectView_innerPadding, SizeUtils.dp2px(29));
        ta.recycle();
        mPaint = new Paint();
        mPaint.setColor(Color.WHITE);
        mPaint.setStyle(Paint.Style.STROKE);
        mPaint.setAntiAlias(true);
        mPaint.setStrokeWidth(mHalfStrokeWidth * 2);
        mSlop = ViewConfiguration.get(getContext()).getScaledTouchSlop();
    }

    public void setEventListener(EventListener listener) {
        this.mEventListener = listener;
    }

    public void setRatio(float ratio) {
        mRectRatio = ratio;
        invalidate();
    }


    /**
     * Get rect size float [ ].
     *
     * @return the float [ ]
     */
    public float[] getRectSize() {
        float[] rectSize = new float[2];
        rectSize[0] = mRect.right - mRect.left + mHalfStrokeWidth * 2F;
        rectSize[1] = mRect.bottom - mRect.top + mHalfStrokeWidth * 2F;
        return rectSize;
    }

    /**
     * Get rect size float [ ].
     *
     * @return the float [ ]
     */
    public RectF getRect() {
        RectF rectF = new RectF();
        rectF.left = mRect.left - mHalfStrokeWidth + 0.5F;
        rectF.right = mRect.right + mHalfStrokeWidth - 0.5F;
        rectF.top = mRect.top - mHalfStrokeWidth + 0.5F;
        rectF.bottom = mRect.bottom + mHalfStrokeWidth - 0.5F;
        return rectF;
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        drawRect(canvas);
    }

    private void drawRect(Canvas canvas) {
        int layerId = canvas.saveLayer(new RectF(0, 0, getWidth(), getHeight()), mPaint);
        canvas.drawColor(getContext().getResources().getColor(R.color.color_b3ffffff));
        mPaint.setStyle(Paint.Style.FILL_AND_STROKE);
        mPaint.setXfermode(new PorterDuffXfermode(PorterDuff.Mode.CLEAR));
        initRect(mRectRatio);
        canvas.drawRect(mRect, mPaint);
        mPaint.setXfermode(null);
        mPaint.setColor(Color.WHITE);
        mPaint.setStyle(Paint.Style.STROKE);
        canvas.drawRect(mRect, mPaint);
        canvas.restoreToCount(layerId);
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        int pointerCount = event.getPointerCount();
        if (pointerCount > TWO_FINGER) {
            return false;
        }
        if (((event.getAction() & MotionEvent.ACTION_MASK) == MotionEvent.ACTION_DOWN) && (pointerCount == ONE_FINGER)) {
            mIsTwoFingerEvent = false;
        }

        if (pointerCount == TWO_FINGER) {
            mIsTwoFingerEvent = true;
            return twoFingerTouch(event);
        } else {
            return oneFingerTouch(event);
        }
    }

    private boolean oneFingerTouch(MotionEvent event) {
        if (mIsTwoFingerEvent) {
            if (event.getAction() == MotionEvent.ACTION_UP) {
                mIsTwoFingerEvent = false;
            }
            return false;
        }
        int action = event.getAction();
        float eventX = event.getX();
        float eventY = event.getY();
        switch (action) {
            case MotionEvent.ACTION_DOWN:
                lastEventTime = System.currentTimeMillis();
                lastEventX = eventX;
                lastEventY = eventY;
                if (mEventListener != null) {
                    mEventListener.onActionDown();
                }
                break;
            case MotionEvent.ACTION_MOVE:
                if (mEventListener != null) {
                    mEventListener.onActionMove(eventX - lastEventX, eventY - lastEventY);
                }
                lastEventX = eventX;
                lastEventY = eventY;
                break;
            case MotionEvent.ACTION_UP:
                if (Math.abs(eventX - lastEventX) < mSlop && Math.abs(eventY - lastEventY) < mSlop ) {
                    if (System.currentTimeMillis() - lastEventTime < ViewConfiguration.getTapTimeout()) {
                        if (mEventListener != null) {
                            mEventListener.onClick();
                        }
                    } else {
                        if (mEventListener != null) {
                            mEventListener.onActionUp();
                        }
                    }
                } else {
                    if (mEventListener != null) {
                        mEventListener.onActionUp();
                    }
                }
                lastEventX = 0;
                lastEventY = 0;
                break;
            default:
                break;
        }
        return true;
    }

    private boolean twoFingerTouch(MotionEvent event) {
        if ((event.getAction() & MotionEvent.ACTION_MASK) == MotionEvent.ACTION_POINTER_DOWN) {
            float xLen = event.getX(0) - event.getX(1);
            float yLen = event.getY(0) - event.getY(1);
            mTwoFingerStartLength = Math.sqrt((xLen * xLen) + (yLen * yLen));
            mTwoFingerOldPoint.set(xLen, yLen);
        } else if ((event.getAction() & MotionEvent.ACTION_MASK) == MotionEvent.ACTION_MOVE) {
            float xLen = event.getX(0) - event.getX(1);
            float yLen = event.getY(0) - event.getY(1);
            double twoFingerEndLength = Math.sqrt(xLen * xLen + yLen * yLen);

            float oldDegree = (float) Math.toDegrees(Math.atan2(mTwoFingerOldPoint.x, mTwoFingerOldPoint.y));
            float newDegree = (float) Math.toDegrees(Math.atan2((event.getX(0) - event.getX(1)), (event.getY(0) - event.getY(1))));
            float scalePercent = (float) (twoFingerEndLength / mTwoFingerStartLength);
            float degree = newDegree - oldDegree;
            if (mEventListener != null) {
                mEventListener.onScaleAndRotate(scalePercent, degree);
            }
            mTwoFingerStartLength = twoFingerEndLength;
            mTwoFingerOldPoint.set(xLen, yLen);
        } else if ((event.getAction() & MotionEvent.ACTION_MASK) == MotionEvent.ACTION_POINTER_UP){
            if (mEventListener != null) {
                mEventListener.onActionUp();
            }
        }
        return true;
    }

    private void initRect(float ratio) {
        int width = (int) (getWidth() - (mInnerPadding * 2F) - (mHalfStrokeWidth * 2));
        int height = getHeight();
        float viewRatio = width * 1.0F / height;
        if (ratio > viewRatio) {
            mRect.left = mHalfStrokeWidth + mInnerPadding;
            mRect.right = mRect.left + width;
            int rectHeight = (int) (width / ratio);
            mRect.top = (int) ((height - rectHeight) / 2F + mHalfStrokeWidth);
            mRect.bottom = mRect.top + rectHeight - mHalfStrokeWidth * 2;
        } else {
            mRect.top = mHalfStrokeWidth;
            mRect.bottom = height - mHalfStrokeWidth;
            int rectWidth = (int) (height * ratio);
            mRect.left = (int) ((width - rectWidth) / 2F + mHalfStrokeWidth) + mInnerPadding;
            mRect.right = mRect.left + rectWidth - mHalfStrokeWidth * 2;
        }
    }

    public interface EventListener {

        /**
         * On action down.
         */
        void onActionDown();

        /**
         * On action move.
         *
         * @param deltaX the delta x
         * @param deltaY the delta y
         */
        void onActionMove(float deltaX, float deltaY);

        /**
         * On scale.
         *
         * @param scale the scale
         */
        void onScaleAndRotate(float scale, float degree);

        /**
         * On action up.
         */
        void onActionUp();

        /**
         * On click.
         */
        void onClick();
    }
}
