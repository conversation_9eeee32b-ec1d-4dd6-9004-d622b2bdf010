package com.czc.cutsame.view;

import android.app.Dialog;
import android.content.Context;
import android.util.DisplayMetrics;
import android.view.Gravity;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import com.czc.cutsame.R;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Locale;

/**
 * Created by liuwan on 2016/9/28.
 */
public class CustomDatePicker {

    /**
     * 定义结果回调接口
     * The interface Result handler.
     */
    public interface ResultHandler {
        void onDataChanged(String time);
    }

    private ResultHandler mHandler;
    private Context context;
    private Dialog datePickerDialog;
    private DatePickerView mHourView, mMinuteView, mSecondView, mFrameView;
    private ArrayList<String> mHours, mMinutes, mSeconds, mFrames;
    /**
     * 当前选中的时、分、秒、帧
     * The currently selected hour, minute, second, and frame.
     */
    private String mCurrentHour, mCurrentMinute, mCurrentSecond, mCurrentFrame;

    public CustomDatePicker(Context context, ResultHandler resultHandler) {
        this.context = context;
        this.mHandler = resultHandler;
        initDialog();
        initView();
    }

    private void initDialog() {
        if (datePickerDialog == null) {
            datePickerDialog = new Dialog(context, R.style.TimePickerDialog);
            datePickerDialog.setCancelable(true);
            datePickerDialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
            datePickerDialog.setContentView(R.layout.view_date_picker);
            Window window = datePickerDialog.getWindow();
            window.setGravity(Gravity.BOTTOM);
            WindowManager manager = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
            DisplayMetrics dm = new DisplayMetrics();
            manager.getDefaultDisplay().getMetrics(dm);
            WindowManager.LayoutParams lp = window.getAttributes();
            lp.width = dm.widthPixels;
            window.setAttributes(lp);
        }
    }

    private void initView() {
        mHourView = (DatePickerView) datePickerDialog.findViewById(R.id.pv_hour);
        mHourView.setUnit(context.getResources().getString(R.string.hour));
        mMinuteView = (DatePickerView) datePickerDialog.findViewById(R.id.pv_minute);
        mMinuteView.setUnit(context.getResources().getString(R.string.minute));
        mSecondView = (DatePickerView) datePickerDialog.findViewById(R.id.pv_second);
        mSecondView.setUnit(context.getResources().getString(R.string.second));
        mFrameView = (DatePickerView) datePickerDialog.findViewById(R.id.pv_frame);
        mFrameView.setUnit(context.getResources().getString(R.string.frame));
    }

    private void initTimer() {
        initArrayList();
        loadComponent();
    }

    private void initArrayList() {
        if (mHours == null) {
            mHours = new ArrayList<>();
        }
        if (mMinutes == null) {
            mMinutes = new ArrayList<>();
        }
        if (mSeconds == null) {
            mSeconds = new ArrayList<>();
        }
        if (mFrames == null) {
            mFrames = new ArrayList<>();
        }
        mHours.clear();
        mMinutes.clear();
        mSeconds.clear();
        mFrames.clear();
    }

    private void loadComponent() {
        int count = 60;
        for (int index = 0; index < count; index++) {
            mHours.add(index + "");
        }
        count = 60;
        for (int index = 0; index < count; index++) {
            mMinutes.add(index + "");
            mSeconds.add(index + "");
        }

        count = 25;
        for (int index = 0; index < count; index++) {
            mFrames.add(index + "");
        }
        mHourView.setData(mHours);
        mMinuteView.setData(mMinutes);
        mSecondView.setData(mSeconds);
        mFrameView.setData(mFrames);
        mHourView.setSelected(0);
        mMinuteView.setSelected(0);
        mSecondView.setSelected(0);
        mFrameView.setSelected(0);
        executeScroll();
    }

    private void addListener() {
        mHourView.setOnSelectListener(new DatePickerView.onSelectListener() {
            @Override
            public void onSelect(String text) {
                mCurrentHour = text;
            }
        });

        mMinuteView.setOnSelectListener(new DatePickerView.onSelectListener() {
            @Override
            public void onSelect(String text) {
                mCurrentMinute = text;
            }
        });

        mSecondView.setOnSelectListener(new DatePickerView.onSelectListener() {
            @Override
            public void onSelect(String text) {
                mCurrentSecond = text;
            }
        });

        mFrameView.setOnSelectListener(new DatePickerView.onSelectListener() {
            @Override
            public void onSelect(String text) {
                mCurrentFrame = text;
            }
        });
        datePickerDialog.findViewById(R.id.tv_confirm).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                datePickerDialog.hide();
                if (mHandler != null) {
                    mHandler.onDataChanged(mCurrentHour + ":" + mCurrentMinute + ":" + mCurrentSecond + ":" + mCurrentFrame);
                }
            }
        });

        datePickerDialog.findViewById(R.id.tv_cancel).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                datePickerDialog.hide();
            }
        });
    }

    private void executeScroll() {
        mHourView.setCanScroll(mHours.size() > 1);
        mMinuteView.setCanScroll(mMinutes.size() > 1);
        mSecondView.setCanScroll(mSeconds.size() > 1);
        mFrameView.setCanScroll(mFrames.size() > 1);
    }

    public void show(String data) {
        initTimer();
        addListener();
        setSelectedTime(data);
        datePickerDialog.show();
    }

    /**
     * 设置日期控件是否可以循环滚动
     * Set is loop or not.
     *
     * @param  isLoop It is loop or not.
     */
    public void setIsLoop(boolean isLoop) {
        this.mHourView.setIsLoop(isLoop);
        this.mMinuteView.setIsLoop(isLoop);
        this.mSecondView.setIsLoop(isLoop);
        this.mFrameView.setIsLoop(isLoop);
    }

    /**
     * 设置日期控件默认选中的时间
     * Set selected time
     *
     * @param time the time
     */
    public void setSelectedTime(String time) {
        String[] str = time.split(":");
        if (str.length < 4) {
            return;
        }
        mCurrentHour = getIntString(str[0]);
        mCurrentMinute = getIntString(str[1]);
        mCurrentSecond = getIntString(str[2]);
        mCurrentFrame = getIntString(str[3]);
        mHourView.setSelected(mCurrentHour);
        mMinuteView.setSelected(mCurrentMinute);
        mSecondView.setSelected(mCurrentSecond);
        mFrameView.setSelected(mCurrentFrame);
        executeScroll();
    }

    private String getIntString(String data){
        if (data.startsWith("0")) {
            data = data.substring(data.indexOf("0") + 1);
        }
        return data;
    }

    /**
     * 验证字符串是否是一个合法的日期格式
     * Verify if the string is in a valid date format.
     *
     * @param date the date
     * @param template the template
     */
    private boolean isValidDate(String date, String template) {
        boolean convertSuccess = true;
        // 指定日期格式 Specify date format.
        SimpleDateFormat format = new SimpleDateFormat(template, Locale.CHINA);
        try {
            // 设置lenient为false. 否则SimpleDateFormat会比较宽松地验证日期，比如2015/02/29会被接受，并转换成2015/03/01
            // Set lenient to false Otherwise, SimpleDateFormat will verify the date more leniently,
            // such as 2015/02/29, which will be accepted and converted to 2015/03/01.
            format.setLenient(false);
            format.parse(date);
        } catch (Exception e) {
            // 如果throw java.text.ParseException或者NullPointerException，就说明格式不对
            // If throw java. text. ParseException or NullPointerException, it indicates that the format is incorrect.
            convertSuccess = false;
        }
        return convertSuccess;
    }
}
