package com.czc.cutsame.view;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.czc.cutsame.R;
import com.meicam.sdk.NvsMediaFileConvertor;
import com.meicam.sdk.NvsStreamingContext;
import com.meishe.base.utils.GsonUtils;
import com.meishe.base.view.CompileProgress;
import com.meishe.engine.observer.ConvertFileObserver;
import com.meishe.engine.util.ConvertFileManager;
import com.meishe.engine.util.PathUtils;
import com.meishe.engine.util.WhiteList;
import com.meishe.logic.bean.SettingParameter;
import com.meishe.logic.manager.PreferencesManager;
import com.meishe.third.pop.core.CenterPopupView;

import java.util.Hashtable;
import java.util.Map;

/**
 * All rights Reserved, Designed By www.meishesdk.com
 *
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @CreateDate : 2021/02/01 15:29
 * @Description : 视频倒放进度
 * video reverse
 * @Copyright : www.meishesdk.com Inc. All rights reserved.
 */
public class VideoReverseDialog extends CenterPopupView {


    private TextView tvProgress;
    private CompileProgress compileProgress;
    private ConvertFileManager mConvertFileManager;
    private OnConvertListener onConvertListener;
    private String mPath;

    public VideoReverseDialog(@NonNull Context context) {
        super(context);

    }

    @Override
    protected int getImplLayoutId() {
        return R.layout.dialog_video_recerse;
    }

    @Override
    protected void initPopupContent() {
        super.initPopupContent();
        initView();
    }

    private ConvertFileObserver convertFileObserver = new ConvertFileObserver() {
        @Override
        public void onConvertProgress(float progress) {
            String text = (int) progress + "%";
            tvProgress.setText(text);
            compileProgress.setProgress((int) progress);
        }

        @Override
        public void onConvertFinish(ConvertFileManager.ConvertParam convertParam, boolean convertSuccess) {
            if (onConvertListener != null) {
                Map<String, ConvertFileManager.ConvertParam.Param> paramMap = convertParam.getParamMap();
                if (paramMap != null && paramMap.size() > 0 && !TextUtils.isEmpty(mPath)) {
                    ConvertFileManager.ConvertParam.Param param = paramMap.get(mPath);
                    if (param != null) {
                        onConvertListener.onConvertFinish(param.getDstFile(), convertSuccess);
                    }
                }
            }
        }

        @Override
        public void onConvertCancel() {
            dismiss();
        }
    };


    private void initView() {
        TextView tvCancel = findViewById(R.id.tv_cancel);
        tvProgress = findViewById(R.id.tv_progress);
        compileProgress = findViewById(R.id.cut_compile_progress);
        tvCancel.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                mConvertFileManager.cancelConvert();
            }
        });
    }

    public void setOnConvertListener(OnConvertListener onConvertListener) {
        mConvertFileManager = ConvertFileManager.getInstance();
        mConvertFileManager.registerConvertFileObserver(convertFileObserver);
        this.onConvertListener = onConvertListener;
    }

    public void setPath(String path) {
        mPath = path;
    }

    @Override
    public void dismiss() {
        super.dismiss();
        unregisterConvertFileObserver();
    }

    @Override
    public void doAfterShow() {
        super.doAfterShow();
        if (!TextUtils.isEmpty(mPath)) {
            String dstPath = PathUtils.getVideoConvertFilePath(PathUtils.getFileName(mPath));
            ConvertFileManager.ConvertParam convertParam = new ConvertFileManager.ConvertParam();
            Hashtable<String, Object> hashtable = null;
            if (WhiteList.isCovert4KFileWhiteList(mPath)) {
                hashtable = new Hashtable<>();
                int height = 720;
                SettingParameter parameter = GsonUtils.fromJson(PreferencesManager.get().getSettingParams(), SettingParameter.class);
                if (parameter != null &&  parameter.getCompileResolution() == NvsStreamingContext.COMPILE_VIDEO_RESOLUTION_GRADE_1080) {
                    height = 1080;
                }
                hashtable.put(NvsMediaFileConvertor.CONVERTOR_CUSTOM_VIDEO_HEIGHT, height);
            }
            convertParam.appendParam(mPath, dstPath, true, 0, hashtable);
            mConvertFileManager.convertFile(convertParam);
        }
    }

    public void unregisterConvertFileObserver() {
        if (mConvertFileManager != null) {
            mConvertFileManager.unregisterConvertFileObserver(convertFileObserver);
        }
    }

    public interface OnConvertListener {
        void onConvertFinish(String path, boolean convertSuccess);
    }
}
