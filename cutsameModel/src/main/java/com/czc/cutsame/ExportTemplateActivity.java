package com.czc.cutsame;

import android.app.Activity;
import android.content.Intent;
import android.graphics.Bitmap;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.Gravity;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.czc.cutsame.iview.IExportTemplate;
import com.czc.cutsame.presenter.ExportTemplatePresenter;
import com.czc.cutsame.util.ConfigUtil;
import com.meishe.base.manager.AppManager;
import com.meishe.base.model.BaseMvpActivity;
import com.meishe.base.utils.NetUtils;
import com.meishe.base.utils.ScreenUtils;
import com.meishe.base.utils.ToastUtils;
import com.meishe.base.utils.Utils;
import com.meishe.base.view.CompileProgress;
import com.meishe.engine.bean.template.ExportTemplateSection;

import java.io.Serializable;
import java.util.ArrayList;

import static com.meishe.logic.constant.PagerConstants.BUNDLE_SAVE_DRAFT;
import static com.meishe.logic.constant.PagerConstants.TEMPLATE_CLIP_DATA;

/**
 * <AUTHOR>
 * @CreateDate :2020/12/30 15:14
 * @Description : 导出模板页面 Export Template page
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class ExportTemplateActivity extends BaseMvpActivity<ExportTemplatePresenter> implements View.OnClickListener, IExportTemplate {
    private static final int TEMPLATE_NAME_MAX_LENGTH = 14;
    private static final int TEMPLATE_DESC_MAX_LENGTH = 60;

    private EditText mEtInputTemplateName;
    private EditText mEtInputTemplateDesc;
    private Button mBtnExportTemplate;
    private View mBackView;
    private LinearLayout mLlGenerateTemplate;
    private RelativeLayout mRlGenerateTemplateProgress;
    private TextView mTvGenerateResult;
    private CompileProgress mGenerateTemplateProgress;
    /**
     * 显示进度
     * show progress
     */
    private TextView mTvGenerateTemplateProgress;


    private Button mBtnGenerateTemplateCancel;
    private ImageView mIvHome;
    private TextView mTvNameNumber;
    private TextView mTvDescNumber;
    private ImageView mIvCover;

    @Override
    protected int bindLayout() {
        return R.layout.activity_export_template;
    }


    @SuppressWarnings("unchecked")
    @Override
    protected void initData(Bundle savedInstanceState) {
        Intent intent = getIntent();
        if (intent != null) {
            Serializable serializable = intent.getSerializableExtra(TEMPLATE_CLIP_DATA);
            if (serializable != null) {
                mPresenter.initData((ArrayList<ExportTemplateSection>) serializable);
            }
        }
    }


    @Override
    protected void initView() {
        mBackView = findViewById(R.id.iv_back);
        FrameLayout.LayoutParams layoutParams = (FrameLayout.LayoutParams) mBackView.getLayoutParams();
        layoutParams.topMargin = (int) (ScreenUtils.getStatusBarHeight() + getResources().getDimension(R.dimen.title_margin_top));
        mBackView.setLayoutParams(layoutParams);

        mIvHome = findViewById(R.id.iv_compile_home);
        FrameLayout.LayoutParams layoutParamsHome = (FrameLayout.LayoutParams) mIvHome.getLayoutParams();
        layoutParamsHome.topMargin = (int) (ScreenUtils.getStatusBarHeight() + getResources().getDimension(R.dimen.title_margin_top));
        mIvHome.setLayoutParams(layoutParamsHome);

        mIvCover = findViewById(R.id.iv_cover);
        mLlGenerateTemplate = findViewById(R.id.ll_generate_template);
        mRlGenerateTemplateProgress = findViewById(R.id.rl_generate_template_progress);
        mTvGenerateResult = findViewById(R.id.tv_generate_result);

        mGenerateTemplateProgress = findViewById(R.id.generate_template_progress);
        mTvGenerateTemplateProgress = findViewById(R.id.tv_generate_template_progress);
        mBtnGenerateTemplateCancel = findViewById(R.id.btn_generate_template_cancel);

        mEtInputTemplateName = findViewById(R.id.et_input_template_name);
        mEtInputTemplateDesc = findViewById(R.id.et_input_template_desc);
        mBtnExportTemplate = findViewById(R.id.btn_export_template);
        View uploadingHint = findViewById(R.id.tv_uploading_hint);
        if (ConfigUtil.isToC()) {
            mBtnExportTemplate.setText(getResources().getString(R.string.activity_cut_export_template_confirm_generate_template));
            uploadingHint.setVisibility(View.GONE);
        } else {
            if (mPresenter.needUpload()) {
                mBtnExportTemplate.setText(getResources().getString(R.string.activity_cut_export_template_confirm_upload_template));
                uploadingHint.setVisibility(View.VISIBLE);
            } else {
                mBtnExportTemplate.setText(getResources().getString(R.string.activity_cut_export_template_confirm_generate_template));
                uploadingHint.setVisibility(View.GONE);
            }
        }

        mTvNameNumber = findViewById(R.id.tv_name_number);
        mTvDescNumber = findViewById(R.id.tv_desc_number);
        mLlGenerateTemplate.setVisibility(View.VISIBLE);

        mTvNameNumber.setText(String.valueOf(TEMPLATE_NAME_MAX_LENGTH));
        mTvDescNumber.setText(String.valueOf(TEMPLATE_DESC_MAX_LENGTH));
        initListener();
    }

    private void initListener() {
        mBtnExportTemplate.setOnClickListener(this);
        mBackView.setOnClickListener(this);
        mBtnGenerateTemplateCancel.setOnClickListener(this);
        mIvHome.setOnClickListener(this);
        mEtInputTemplateName.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                int length = s.length();
                String str = s.toString();
                if (length > TEMPLATE_NAME_MAX_LENGTH) {
                    mEtInputTemplateName.setText(str.substring(0, TEMPLATE_NAME_MAX_LENGTH));
                    mEtInputTemplateName.requestFocus();
                    mEtInputTemplateName.setSelection(mEtInputTemplateName.getText().length());
                } else {
                    int i = TEMPLATE_NAME_MAX_LENGTH - length;
                    mTvNameNumber.setText(String.valueOf(i));
                }

            }

            @Override
            public void afterTextChanged(Editable s) {

            }
        });


        mEtInputTemplateDesc.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                int length = s.length();
                String str = s.toString();
                if (length > TEMPLATE_DESC_MAX_LENGTH) {
                    mEtInputTemplateDesc.setText(str.substring(0, TEMPLATE_DESC_MAX_LENGTH));
                    mEtInputTemplateDesc.requestFocus();
                    mEtInputTemplateDesc.setSelection(mEtInputTemplateDesc.getText().length());
                } else {
                    int i = TEMPLATE_DESC_MAX_LENGTH - length;
                    mTvDescNumber.setText(String.valueOf(i));
                }
            }

            @Override
            public void afterTextChanged(Editable s) {

            }
        });

    }

    @Override
    public void onClick(View view) {
        if (view.getId() == R.id.iv_back) {
            cancelTemplate();
            finish();
        } else if (view.getId() == R.id.btn_export_template) {
            if (mPresenter.needUpload() && !NetUtils.isNetworkAvailable(this)) {
                ToastUtils.showShort(Utils.getApp().getResources().getString(R.string.user_hint_assets_net_error));
                return;
            }
            String templateName = mEtInputTemplateName.getText().toString();
            if (TextUtils.isEmpty(templateName)) {
                ToastUtils.showShort(R.string.activity_cut_export_template_please_input_template_name);
                return;
            }
            String templateDesc = mEtInputTemplateDesc.getText().toString();
            if (TextUtils.isEmpty(templateDesc)) {
                ToastUtils.showShort(R.string.activity_cut_export_template_please_input_template_desc);
                return;
            }
            mPresenter.exportTemplate(templateName, templateDesc);
        } else if (view.getId() == R.id.btn_generate_template_cancel) {
            cancelTemplate();
        } else if (view.getId() == R.id.iv_compile_home) {
            Bundle bundle = new Bundle();
            bundle.putBoolean(BUNDLE_SAVE_DRAFT, true);
            Intent intent = new Intent("com.meishe.myvideo.action.MAIN");
            intent.putExtras(bundle);
            startActivity(intent);
            finish();
        }
    }

    @Override
    public void onBackPressed() {
        cancelTemplate();
        super.onBackPressed();
    }

    /**
     * 取消导出模板
     * Cancel exporting template
     */
    private void cancelTemplate() {
        if (mPresenter != null && mRlGenerateTemplateProgress.isShown()) {
            mPresenter.cancelExportTemplate();
        }
    }

    @Override
    public void imageGrabbedArrived(Bitmap bitmap) {
        mIvCover.setImageBitmap(bitmap);
    }

    @Override
    public void onCompileStart() {
        mRlGenerateTemplateProgress.setFocusable(true);
        mRlGenerateTemplateProgress.requestFocus();
        mRlGenerateTemplateProgress.setVisibility(View.VISIBLE);
        mBtnGenerateTemplateCancel.setVisibility(View.VISIBLE);
        mLlGenerateTemplate.setVisibility(View.GONE);
    }

    @Override
    public void onCompileProgress(int progress) {
        mGenerateTemplateProgress.setProgress(progress);
        String text = progress + "%";
        mTvGenerateTemplateProgress.setText(text);
    }

    @Override
    public void onCompileComplete(boolean success) {
        if (success) {
            mTvGenerateResult.setVisibility(View.VISIBLE);
            mIvHome.setVisibility(View.VISIBLE);
            mRlGenerateTemplateProgress.setVisibility(View.GONE);
            mBtnGenerateTemplateCancel.setVisibility(View.GONE);
            mIvCover.setVisibility(View.GONE);
        } else {
            mRlGenerateTemplateProgress.setVisibility(View.GONE);
            mBtnGenerateTemplateCancel.setVisibility(View.GONE);
            mRlGenerateTemplateProgress.setFocusable(false);
            mGenerateTemplateProgress.setProgress(0);
            mTvGenerateTemplateProgress.setText("0%");
            mLlGenerateTemplate.setVisibility(View.VISIBLE);
            int hintId;
            if (ConfigUtil.isToC()){
                hintId = R.string.hint_template_export_failed;
            } else {
                hintId = R.string.hint_template_upload_failed;
            }
            ToastUtils.make().setGravity(Gravity.CENTER, 0,0).show(hintId);
        }
    }

    @Override
    public boolean isActive() {
        Activity activity = AppManager.getInstance().currentActivity();
        return !isFinishing() && this.equals(activity);
    }
}