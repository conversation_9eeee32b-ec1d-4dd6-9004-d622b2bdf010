apply plugin: 'com.android.library'

android {
    compileSdkVersion rootProject.android.extCompileSdkVersion
    buildToolsVersion rootProject.android.extBuildToolsVersion

    defaultConfig {
        minSdkVersion rootProject.android.extMinSdkVersion
        targetSdkVersion rootProject.android.extTargetSdkVersion
        versionCode rootProject.config.extVersionCode
        versionName rootProject.config.extVersionName

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {

        release {
            minifyEnabled false
            matchingFallbacks = ['release']
            consumerProguardFiles 'proguard-rules.pro'
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }

        debug {
            minifyEnabled false
            signingConfig signingConfigs.debug
            matchingFallbacks = ['debug']
            debuggable true
        }
    }
}

dependencies {
    implementation fileTree(dir: "libs", include: ["*.jar"])
    implementation rootProject.ext.dependencies.extAppcompat
    implementation rootProject.ext.dependencies.extConstraintLayout
    testImplementation rootProject.ext.dependencies.extTestJunit
    androidTestImplementation rootProject.ext.dependencies.extAndroidTestRunner
    androidTestImplementation rootProject.ext.dependencies.extTestEspresso
    implementation rootProject.ext.dependencies.extSwiperefreshlayout
    implementation rootProject.ext.dependencies.exoplayer
    // Add specific ExoPlayer core and UI components
    implementation rootProject.ext.dependencies.exoplayerUI
    implementation project(path: ':libLogic')
    implementation project(path: ':libEngine')
    implementation rootProject.ext.dependencies.extSupportDesign
    implementation project(path: ':libPlayer')
    implementation project(path: ':libPlugin')
}