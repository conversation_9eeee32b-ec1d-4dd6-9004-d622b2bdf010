package com.meishe.speaker;

import com.iflytek.cloud.ErrorCode;
import com.iflytek.cloud.InitListener;
import com.iflytek.cloud.SpeechConstant;
import com.iflytek.cloud.SpeechError;
import com.iflytek.cloud.SpeechRecognizer;
import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.LogUtils;
import com.meishe.base.utils.Utils;
import com.meishe.speaker.bean.Speech;
import com.meishe.speaker.bean.VoiceParam;

import java.util.ArrayList;
import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2023/6/14 10:40
 * @Description :
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class VoiceDictationHelperWrapper {
   private SpeechRecognizer mRecognizer;

   private VoiceDictationHelperWrapper() {
   }

   private void setParam() {
      // 清空参数 Clear parameter.
      mRecognizer.setParameter(SpeechConstant.PARAMS, null);
      // 设置引擎 Set Engine.
      // mRecognizer.setParameter(SpeechConstant.ENGINE_TYPE, SpeechConstant.TYPE_LOCAL);
      mRecognizer.setParameter(SpeechConstant.ENGINE_TYPE, SpeechConstant.TYPE_CLOUD);
      // 设置返回结果格式 Format return results.
      mRecognizer.setParameter(SpeechConstant.RESULT_TYPE, "json");
      //mIat.setParameter(MscKeys.REQUEST_AUDIO_URL,"true");
      // 设置本地识别资源,如果是离线识别，需要设置
      // Set local recognition resources. If it is offline recognition, it needs to be set
      // mRecognizer.setParameter(ResourceUtil.ASR_RES_PATH, getLocalResource());

      //设置方言，默认即可,在线听写支持多种小语种，若想了解请下载在线听写能力，参看其speechDemo
      //Set the dialect by default. Online dictation supports multiple minor languages. If you want to learn more, please download the online dictation ability and refer to its speechDemo.
      mRecognizer.setParameter(SpeechConstant.ACCENT, "mandarin");
      // 设置语言
      //set language
      mRecognizer.setParameter(SpeechConstant.LANGUAGE, "zh_cn");

      // 设置语音前端点:静音超时时间，即用户多长时间不说话则当做超时处理
      //Set the endpoint before voice: mute timeout, which means that the user will be treated as timeout if they do not speak for a long time.
      mRecognizer.setParameter(SpeechConstant.VAD_BOS, "10000");

      // 设置语音后端点:后端点静音检测时间，即用户停止说话多长时间内即认为不再输入， 自动停止录音
      // Set the endpoint after voice: the backend point's mute detection time, which means that the user stops speaking for a certain amount of time, and it is considered that there is no longer input, automatically stopping recording.
      mRecognizer.setParameter(SpeechConstant.VAD_EOS, "10000");

      // 设置标点符号,设置为"0"返回结果无标点,设置为"1"返回结果有标点
      // Set punctuation mark to "0" to return results without punctuation, and set to "1" to return results with punctuation.
      mRecognizer.setParameter(SpeechConstant.ASR_PTT, "1");
      mRecognizer.setParameter(SpeechConstant.KEY_SPEECH_TIMEOUT, "30000");

      // 设置音频保存路径，保存音频格式支持pcm、wav，设置路径为sd卡请注意WRITE_EXTERNAL_STORAGE权限
      // Set the audio save path. The save audio format supports pcm and wav, and set the path to SD card. Please pay attention to WRITE_ EXTERNAL_ STORAGE permission.
      // mRecognizer.setParameter(SpeechConstant.AUDIO_FORMAT, "wav");
      //mRecognizer.setParameter(SpeechConstant.ASR_AUDIO_PATH, Environment.getExternalStorageDirectory() + "/msc/iat.wav");
   }


   private static class Holder {
      private static VoiceDictationHelperWrapper INSTANCE = new VoiceDictationHelperWrapper();
   }

   /**
    * Get voice dictation helper wrapper.
    *
    * @return the voice dictation helper wrapper
    */
   public static VoiceDictationHelperWrapper get() {
      return VoiceDictationHelperWrapper.Holder.INSTANCE;
   }


   private int mIndex;
   private VoiceDictationHelper mCurrentVoiceDictationHelper;

   /**
    * Start dictation.
    *
    * @param paramList the param list
    * @param listener  the listener
    */
   public void startDictation(final List<List<VoiceParam>> paramList, final SpeechWrapListener listener){
      if (CommonUtils.isEmpty(paramList)) {
         return;
      }
      if (listener != null) {
         listener.onBeginOfSpeech();
      }
      if (mRecognizer == null) {
         mRecognizer = SpeechRecognizer.createRecognizer(Utils.getApp(), new InitListener() {
            @Override
            public void onInit(int code) {
               LogUtils.d("code=" + code);
               if (code != ErrorCode.SUCCESS) {
                  if (listener != null) {
                     listener.onError(null);
                  }
                  //"初始化失败，错误码：" + code+",请点击网址https://www.xfyun.cn/document/error-code查询解决方案";
                  //Initialization failed with error code: "+code+", please click on the
                  // website https://www.xfyun.cn/document/error-code Query Solution ";
                  return;
               }
               setParam();
               start(paramList, listener);
            }
         });
      } else {
         start(paramList, listener);
      }
   }

   private void start(List<List<VoiceParam>> paramList, final SpeechWrapListener listener) {
      int count = paramList.size();
      mIndex = 0;
      List<Speech> resultList = new ArrayList<>();
      VoiceDictationHelper.SpeechListener speechListener = new VoiceDictationHelper.SpeechListener() {
         @Override
         public void onError(SpeechError speechError) {
            LogUtils.e("onError: index = "+mIndex);
            if (mIndex == count - 1) {
               if (listener != null) {
                  listener.onError(speechError);
               }
               cancelDictation();
            } else {
               if (mCurrentVoiceDictationHelper != null) {
                  mCurrentVoiceDictationHelper.stopDictation();
               }
               VoiceDictationHelperWrapper.this.startDictation(paramList, ++mIndex, this);
            }
         }

         @Override
         public void onSampleResult(Speech result) {
            LogUtils.d("onSampleResult: index = "+mIndex);
            resultList.add(result);
            if (mIndex == count - 1) {
               if (listener != null) {
                  listener.onSampleResult(resultList);
               }
               if (mCurrentVoiceDictationHelper != null) {
                  mCurrentVoiceDictationHelper.stopDictation();
                  mCurrentVoiceDictationHelper = null;
               }
            } else {
               if (mCurrentVoiceDictationHelper != null) {
                  mCurrentVoiceDictationHelper.stopDictation();
               }
               VoiceDictationHelperWrapper.this.startDictation(paramList, ++mIndex,this);
            }
         }
      };
      startDictation(paramList, 0, speechListener);
   }

   private void startDictation(List<List<VoiceParam>> paramList, int index, VoiceDictationHelper.SpeechListener speechListener) {
      mCurrentVoiceDictationHelper = new VoiceDictationHelper();
      mCurrentVoiceDictationHelper.setRecognizer(mRecognizer);
      mCurrentVoiceDictationHelper.startDictation(paramList.get(index), speechListener);
   }

   public boolean isListening() {
      if (mCurrentVoiceDictationHelper != null) {
         return mCurrentVoiceDictationHelper.isListening();
      }
      return false;
   }

   /**
    * Cancel dictation.
    */
   public void cancelDictation(){
      if (mCurrentVoiceDictationHelper != null) {
         mCurrentVoiceDictationHelper.cancelDictation();
         mRecognizer = null;
         mCurrentVoiceDictationHelper = null;
      }
   }

   /**
    * The interface Speech wrap listener.
    */
   public interface SpeechWrapListener{
      /**
       * On prepare list.
       * 准备数据
       *
       * @param type the type 数据类型
       * @return the list 数据结果
       */
      List<List<VoiceParam>> onPrepare(int type);

      /**
       * On sample result.
       * 获取结果
       *
       * @param result the result
       */
      void onSampleResult(List<Speech> result);

      /**
       * On begin of speech.
       * 开始识别
       */
      void onBeginOfSpeech();

      /**
       * On error.
       * 识别错误
       *
       * @param speechError the speech error 错误信息
       */
      void onError(SpeechError speechError);
   }
}
