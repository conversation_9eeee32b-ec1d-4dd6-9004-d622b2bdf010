apply plugin: 'com.android.library'

android {
    compileSdkVersion rootProject.android.extCompileSdkVersion
    buildToolsVersion rootProject.android.extBuildToolsVersion


    defaultConfig {
        minSdkVersion rootProject.android.extMinSdkVersion
        targetSdkVersion rootProject.android.extTargetSdkVersion
        versionCode rootProject.config.extVersionCode
        versionName rootProject.config.extVersionName
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

    }

    buildTypes {
        release {
            consumerProguardFiles 'proguard-rules.pro'
        }
    }
}

dependencies {
    api fileTree(include: ['*.jar'], dir: 'libs')
    testImplementation rootProject.ext.dependencies.extTestJunit
    androidTestImplementation rootProject.ext.dependencies.extAndroidTestRunner
    androidTestImplementation rootProject.ext.dependencies.extTestEspresso
    implementation project(path: ':libBase')
    implementation project(path: ':libEngine')
    implementation project(path: ':libLogic')
}
