package com.meishe.draft.db;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;

import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/5/18 15:56
 * @Description :Job信息dao.The dao of job information.
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
@Dao
public interface JobInfoDao {
   @Insert
   void insert(JobInfoEntity... info);

   @Update
   void update(JobInfoEntity... info);

   @Delete
   void deleteJob(JobInfoEntity... info);

   @Query("DELETE FROM JobInfoEntity WHERE projectUuid = :projectId")
   void deleteJob(String projectId);

   @Query("DELETE FROM JobInfoEntity WHERE jobId = :jobId")
   void deleteByJobId(String jobId);

   @Query("DELETE  FROM JobInfoEntity")
   void deleteAll();

   @Query("SELECT * FROM JobInfoEntity WHERE projectUuid = :projectId AND jobId = :jobId")
   JobInfoEntity getJobInfo(String projectId, String jobId);

   @Query("SELECT * FROM JobInfoEntity WHERE jobId = :jobId")
   JobInfoEntity getJobInfo(String jobId);

   @Query("SELECT * FROM JobInfoEntity")
   List<JobInfoEntity> getJob();
}
