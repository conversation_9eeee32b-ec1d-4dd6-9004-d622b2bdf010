package com.meishe.draft.db;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;

import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/5/18 15:56
 * @Description :资源信息dao.The dao of resource information.
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
@Dao
public interface ResourceDao {
   @Insert
   void insert(ResourceEntity... info);

   @Update
   void update(ResourceEntity... info);

   @Delete
   void delete(ResourceEntity... info);

   @Query("DELETE FROM ResourceEntity")
   void deleteAll();

   @Query("SELECT * FROM ResourceEntity WHERE id =  :key")
   ResourceEntity get(String key);

   @Query("SELECT * FROM ResourceEntity")
   List<ResourceEntity> get();
}
