package com.meishe.draft.db;

import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.room.Entity;
import androidx.room.PrimaryKey;

import com.meishe.engine.bean.bridges.FileInfoBridge;


/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/3/18 15:25
 * @Description :资源entity The resource for local draft.
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
@Entity
public class ResourceEntity implements Cloneable{
   @PrimaryKey
   @NonNull
   private String id = "123456789";

   public String filePath;
   /**
    * 缩略图、导出和波形，需要用到远程路径
    * Thumbnails, exports, and waveforms require remote paths
    */
   public String remotePath;

   /**
    * 左声道波形
    * The url for left channel
    */
   private String leftChannelUrl;

   /**
    * 右声道波形
    * The url for right channel
    */
   private String rightChannelUrl;
   private String fileName;
   private String fileNameZh;
   private String customDisPlayName;
   private long duration;
   private int width;
   private int height;
   private int category;
   private int type;
   private int kind;
   private boolean isAssets;

   private String urlPrefix;
   private long interval;
   private String extension;
   /**
    * 资源的真实Id
    * The real id
    */
   private String realId;

   public ResourceEntity() {

   }

   public ResourceEntity(FileInfoBridge.FileInfo fileInfo) {
      id = fileInfo.key;
      copy(fileInfo);
   }

   @NonNull
   public String getId() {
      return id;
   }

   public void setId(@NonNull String id) {
      this.id = id;
   }

   public String getFilePath() {
      return filePath;
   }

   public void setFilePath(String filePath) {
      this.filePath = filePath;
   }

   public String getRemotePath() {
      return remotePath;
   }

   public void setRemotePath(String remotePath) {
      this.remotePath = remotePath;
   }

   public String getLeftChannelUrl() {
      return leftChannelUrl;
   }

   public void setLeftChannelUrl(String leftChannelUrl) {
      this.leftChannelUrl = leftChannelUrl;
   }

   public String getRightChannelUrl() {
      return rightChannelUrl;
   }

   public void setRightChannelUrl(String rightChannelUrl) {
      this.rightChannelUrl = rightChannelUrl;
   }

   public String getFileName() {
      return fileName;
   }

   public void setFileName(String fileName) {
      this.fileName = fileName;
   }

   public String getFileNameZh() {
      return fileNameZh;
   }

   public void setFileNameZh(String fileNameZh) {
      this.fileNameZh = fileNameZh;
   }

   public String getCustomDisPlayName() {
      return customDisPlayName;
   }

   public void setCustomDisPlayName(String customDisPlayName) {
      this.customDisPlayName = customDisPlayName;
   }

   public long getDuration() {
      return duration;
   }

   public void setDuration(long duration) {
      this.duration = duration;
   }

   public int getWidth() {
      return width;
   }

   public void setWidth(int width) {
      this.width = width;
   }

   public int getHeight() {
      return height;
   }

   public void setHeight(int height) {
      this.height = height;
   }

   public int getCategory() {
      return category;
   }

   public void setCategory(int category) {
      this.category = category;
   }

   public int getType() {
      return type;
   }

   public void setType(int type) {
      this.type = type;
   }

   public int getKind() {
      return kind;
   }

   public void setKind(int kind) {
      this.kind = kind;
   }

   public boolean isAssets() {
      return isAssets;
   }

   public void setAssets(boolean assets) {
      isAssets = assets;
   }

   public String getUrlPrefix() {
      return urlPrefix;
   }

   public void setUrlPrefix(String urlPrefix) {
      this.urlPrefix = urlPrefix;
   }

   public long getInterval() {
      return interval;
   }

   public void setInterval(long interval) {
      this.interval = interval;
   }

   public String getExtension() {
      return extension;
   }

   public void setExtension(String extension) {
      this.extension = extension;
   }

   public String getRealId() {
      return realId;
   }

   public void setRealId(String realId) {
      this.realId = realId;
   }

   public ResourceEntity copy(FileInfoBridge.FileInfo fileInfo) {
      setRemotePath(fileInfo.remotePath);
      setFilePath(fileInfo.filePath);
      setLeftChannelUrl(fileInfo.getLeftChannelUrl());
      setRightChannelUrl(fileInfo.getRightChannelUrl());
      setFileName(fileInfo.fileName);
      setFileNameZh(fileInfo.fileNameZh);
      setCustomDisPlayName(fileInfo.customDisPlayName);
      setDuration(fileInfo.duration);
      setWidth(fileInfo.width);
      setHeight(fileInfo.height);
      setCategory(fileInfo.category);
      setType(fileInfo.type);
      setKind(fileInfo.kind);
      setAssets(fileInfo.isAssets());
      setRealId(fileInfo.resourceId);
      FileInfoBridge.ThumbNailInfo nailInfo = fileInfo.getThumbNailInfo();
      if (nailInfo != null) {
         setUrlPrefix(nailInfo.urlPrefix);
         setInterval(nailInfo.interval);
         setExtension(nailInfo.extension);
      }
      return this;
   }

   public FileInfoBridge.FileInfo create(){
      FileInfoBridge.FileInfo fileInfo = new FileInfoBridge.FileInfo(id);
      fileInfo.remotePath = remotePath;
      fileInfo.filePath = filePath;
      fileInfo.setLeftChannelUrl(leftChannelUrl);
      fileInfo.setRightChannelUrl(rightChannelUrl);
      fileInfo.setAssets(isAssets);
      fileInfo.width = width;
      fileInfo.height = height;
      fileInfo.category = category;
      fileInfo.type = type;
      fileInfo.kind = kind;
      fileInfo.duration = duration;
      fileInfo.fileName = fileName;
      fileInfo.fileNameZh = fileNameZh;
      fileInfo.customDisPlayName = customDisPlayName;
      fileInfo.setResourceId(realId);
      if (!TextUtils.isEmpty(urlPrefix)) {
         fileInfo.setThumbNailInfo(new FileInfoBridge.ThumbNailInfo(urlPrefix, interval, extension));
      }
      return fileInfo;
   }
}
