package com.meishe.draft.db;

import androidx.annotation.NonNull;
import androidx.room.Entity;
import androidx.room.Ignore;
import androidx.room.PrimaryKey;

import com.meishe.base.utils.LogUtils;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/3/18 15:25
 * @Description :本地草稿entity The entity for local draft.
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
@Entity
public class LocalDraftEntity implements Cloneable{
   @PrimaryKey
   @NonNull
   private String id = "123456789";

   private long modifiedAt;

   private long cloudModifiedAt;

   /**
    * 创建时间
    */
   private long createAt;

   private String coverPath;

   /**
    * 草稿名称
    * Name of draft document
    */
   private String name;

   /**
    * 草稿时长
    * The draft time
    */
   private String duration;

   /**
    * 文件夹路径
    * folder path
    */
   private String dirPath;

   /**
    * 文件大小
    * this file size
    */
   private String fileSize;

   /**
    * 是否是云草稿
    * Is Cloud or not
    */
   private boolean isCloud;

   /**
    * 远端id
    * The remote id
    */
   private String remoteId;
   private String infoJsonPath;
   private String cloudToLocalMapInfo;
   private String templatePath;

   @Ignore
   public LocalDraftEntity(@NonNull String id) {
      this.id = id;
   }

   public LocalDraftEntity() {
   }

   @NonNull
   public String getId() {
      return id;
   }

   public void setId(@NonNull String id) {
      this.id = id;
   }

   public long getModifiedAt() {
      return modifiedAt;
   }

   public void setModifiedAt(long modifiedAt) {
      this.modifiedAt = modifiedAt;
   }

   public String getName() {
      return name;
   }

   public void setName(String name) {
      this.name = name;
   }

   public String getDuration() {
      return duration;
   }

   public void setDuration(String duration) {
      this.duration = duration;
   }

   public String getDirPath() {
      return dirPath;
   }

   public void setDirPath(String dirPath) {
      this.dirPath = dirPath;
   }

   public String getCoverPath() {
      return coverPath;
   }

   public void setCoverPath(String coverPath) {
      this.coverPath = coverPath;
   }

   public String getFileSize() {
      return fileSize;
   }

   public void setFileSize(String fileSize) {
      this.fileSize = fileSize;
   }

   public long getCreateAt() {
      return createAt;
   }

   public void setCreateAt(long createAt) {
      this.createAt = createAt;
   }

   public boolean isCloud() {
      return isCloud;
   }

   public void setCloud(boolean cloud) {
      isCloud = cloud;
   }

   public String getInfoJsonPath() {
      return infoJsonPath;
   }

   public void setInfoJsonPath(String infoJsonPath) {
      this.infoJsonPath = infoJsonPath;
   }

   public String getCloudToLocalMapInfo() {
      return cloudToLocalMapInfo;
   }

   public void setCloudToLocalMapInfo(String cloudToLocalMapInfo) {
      this.cloudToLocalMapInfo = cloudToLocalMapInfo;
   }

   public String getTemplatePath() {
      return templatePath;
   }

   public void setTemplatePath(String templatePath) {
      this.templatePath = templatePath;
   }

   public long getCloudModifiedAt() {
      return cloudModifiedAt;
   }

   public void setCloudModifiedAt(long cloudModifiedAt) {
      this.cloudModifiedAt = cloudModifiedAt;
   }

   public String getRemoteId() {
      return remoteId;
   }

   public void setRemoteId(String remoteId) {
      this.remoteId = remoteId;
   }

   @NonNull
   @Override
   public LocalDraftEntity clone(){
      try {
         return (LocalDraftEntity) super.clone();
      } catch (CloneNotSupportedException e) {
         LogUtils.e(e);
      }
      return null;
   }
}
