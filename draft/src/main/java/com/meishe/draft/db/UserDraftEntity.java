package com.meishe.draft.db;

import androidx.annotation.NonNull;
import androidx.room.Entity;
import androidx.room.PrimaryKey;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/3/28 14:14
 * @Description :用户草稿entity the entity of User to draft
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
@Entity
public class UserDraftEntity {
   /**
    * 主键，用户Id
    * The primary key  , package id
    */
   @PrimaryKey()
   @NonNull
   private String id = "123456";

   private String userId;

   public String getId() {
      return id;
   }

   public void setId(String id) {
      this.id = id;
   }

   public String getUserId() {
      return userId;
   }

   public void setUserId(String userId) {
      this.userId = userId;
   }


   @Override
   public String toString() {
      return "UserDraftEntity{" +
              "id=" + id +
              ", userId='" + userId + '\'' +
              '}';
   }
}
