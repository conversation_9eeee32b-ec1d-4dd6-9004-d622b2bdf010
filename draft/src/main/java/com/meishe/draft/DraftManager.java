package com.meishe.draft;

import android.graphics.Bitmap;
import android.os.Environment;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.FileIOUtils;
import com.meishe.base.utils.FileUtils;
import com.meishe.base.utils.FormatUtils;
import com.meishe.base.utils.ImageUtils;
import com.meishe.base.utils.LogUtils;
import com.meishe.base.utils.ThreadUtils;
import com.meishe.base.utils.TimeUtils;
import com.meishe.base.utils.Utils;
import com.meishe.draft.data.DraftData;
import com.meishe.draft.data.DraftType;
import com.meishe.draft.db.DraftDbManager;
import com.meishe.draft.db.LocalDraftDao;
import com.meishe.draft.db.LocalDraftEntity;
import com.meishe.draft.db.ProjectDao;
import com.meishe.draft.db.ProjectEntity;
import com.meishe.draft.db.ResourceEntity;
import com.meishe.draft.db.UserDraftDao;
import com.meishe.draft.db.UserDraftEntity;
import com.meishe.draft.observer.DraftObservable;
import com.meishe.draft.observer.DraftObserver;
import com.meishe.engine.bean.MeicamTimeline;
import com.meishe.engine.bean.MeicamVideoClip;
import com.meishe.engine.bean.MeicamVideoTrack;
import com.meishe.engine.local.LTimelineData;
import com.meishe.libplugin.PluginManager;
import com.meishe.libplugin.user.IUserPlugin;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.UUID;


public class DraftManager {
    private static final String TAG = "DraftManager";
    private static volatile DraftManager sDraftLocal;
    private final static String IMAGE_BACKGROUND_DRAFT = "draft";
    private static final String JSON_KEY_FILE_SIZE = "fileSize";
    private static final String JSON_KEY_LAST_MODIFY_TIME = "lastModifiedTime";
    private static final String JSON_KEY_PROJECT_ID = "projectId";
    private static final String JSON_KEY_COVER_PATH = "coverPath";
    private static final String JSON_KEY_COVER_IMAGE_PATH = "coverImagePath";
    private static final String JSON_KEY_DURING = "projectDuring";
    private static final String JSON_KEY_PROJECT_NAME = "projectName";
    private static final String NON_USER_ID = "common";
    private static final boolean SAVE_DRAFT_TO_SDCARD = false;
    private final LocalDraftDao mLocalDraftDao;
    private final ProjectDao mProjectDao;
    private final UserDraftDao mUserDraftDao;
    private DraftObservable mDraftObservable;

    private String mCurrentDraftDir;
    private DraftData mCurrentDraftData;


    private DraftManager() {
        mDraftObservable = new DraftObservable();
        mLocalDraftDao = DraftDbManager.get().getLocalDraftDao();
        mUserDraftDao = DraftDbManager.get().getUserDraftDao();
        mProjectDao = DraftDbManager.get().getProjectDao();
    }

    public static DraftManager getInstance() {
        if (sDraftLocal == null) {
            synchronized (DraftManager.class) {
                if (sDraftLocal == null) {
                    sDraftLocal = new DraftManager();
                }
            }
        }
        return sDraftLocal;
    }

    /**
     * 更新草稿
     * Update the draft data
     *
     * @param duration   the duration
     * @param bitmap     the cover bitmap , is null ,not update
     * @param updateTime the update time , true update ,false not
     */
    public void updateDraft(final MeicamTimeline timeline, long duration, Bitmap bitmap, boolean updateTime) {
        updateDraft(timeline, duration, bitmap, updateTime, null);
    }

    /**
     * 更新草稿
     * Update the draft data
     *
     * @param duration   the duration
     * @param bitmap     the cover bitmap , is null ,not update
     * @param updateTime the update time , true update ,false not
     */
    public void updateDraft(final MeicamTimeline timeline, final long duration, final Bitmap bitmap, final boolean updateTime,
                            final DraftObserver observer) {
        updateDraft(timeline, duration, "", bitmap, updateTime, false, observer);
    }

    /**
     * 更新草稿
     * Update the draft data
     *
     * @param duration   the duration
     * @param bitmap     the cover bitmap , is null ,not update
     * @param updateTime the update time , true update ,false not
     */
    public void updateDraft(final MeicamTimeline timeline, final long duration, final String name, final Bitmap bitmap, final boolean updateTime, final boolean isCloud, final DraftObserver observer) {
        final String[] json = {timeline != null ? timeline.toDraftJson() : null};
        ThreadUtils.getIoPool().execute(new Runnable() {
            @Override
            public void run() {
                if (TextUtils.isEmpty(mCurrentDraftDir)) {
                    LogUtils.e("error ,when the draftDir is null ,can not update draft!!");
                    return;
                }

                String currentTime = String.valueOf(System.currentTimeMillis());
                String draftDir = null;
                if (timeline != null) {
                    draftDir = timeline.getDraftDir();
                }
                if (draftDir == null) {
                    draftDir = getCurrentDraftDir(currentTime);
                }
                String coverPath = timeline.getCoverImagePath();
                if (bitmap != null) {
                    //为空则不更新 If bitmap is null, do not update
                    coverPath = saveCover(bitmap, draftDir, currentTime);
                }
                if (!updateTime) {
                    //不更新时间 Do not update time.
                    String lastModifiedTime = timeline.getLastModifiedTime();
                    if (!TextUtils.isEmpty(lastModifiedTime)) {
                        //但是如果发现lastModifiedTime为空，则更新时间;
                        // But if lastModifiedTime is found to be empty, update the time;
                        currentTime = lastModifiedTime;
                    }
                }

                String fileSize = getDraftFileSize(timeline);
                String[] keys = new String[]{JSON_KEY_LAST_MODIFY_TIME, JSON_KEY_COVER_PATH, JSON_KEY_COVER_IMAGE_PATH, JSON_KEY_DURING, JSON_KEY_FILE_SIZE};
                String[] values = new String[]{currentTime, coverPath, coverPath, FormatUtils.microsecond2Time(duration), fileSize};

                json[0] = addParamToJson(json[0], keys, values);
                FileIOUtils.writeFileFromString(getDraftPath(draftDir), json[0]);
                LocalDraftEntity entity = mLocalDraftDao.getDraft(timeline.getProjectId());
                if (entity != null) {
                    entity.setDuration(String.valueOf(duration));
                    entity.setModifiedAt(Long.parseLong(currentTime));
                    if (updateTime) {
                        entity.setCloudModifiedAt(Long.parseLong(currentTime));
                    }
                    entity.setCoverPath(coverPath);
                    entity.setDirPath(draftDir);
                    entity.setFileSize(fileSize);
                    mLocalDraftDao.updateDraft(entity);
                } else {
                    String projectId = timeline.getProjectId();
                    entity = new LocalDraftEntity(projectId);
                    entity.setModifiedAt(Long.parseLong(currentTime));
                    if (TextUtils.isEmpty(name)) {
                        entity.setName(currentTime);
                    } else {
                        entity.setName(name);
                    }
                    entity.setCoverPath(coverPath);
                    entity.setDirPath(draftDir);
                    entity.setId(projectId);
                    entity.setFileSize(fileSize);
                    entity.setDuration(String.valueOf(duration));
                    entity.setCreateAt(Long.parseLong(currentTime));
                    entity.setCloud(isCloud);
                    mLocalDraftDao.insertDraft(entity);
                    saveUserInfo(projectId);
                }
                notifyDataChanged(observer);
            }
        });
    }


    /**
     * Update cloud draft.
     * 更新云草稿
     *
     * @param projectId           the project id 项目id
     * @param name                the name 项目名称
     * @param coverPath           the cover path 封面路径
     * @param templatePath        the template path 模板路径
     * @param infoJsonPath        the info json path info.json路径
     * @param cloudToLocalMapInfo the cloud to local map info 云路径和本地草稿映射数据
     * @param duration            the duration 时长
     * @param fileSize         the file size 文件的大小
     * @param modifiedTime        the modified time 编辑时间
     * @param observer            the observer 观察者引用
     */
    public void updateCloudDraft(final String projectId, final String remoteId, final String name, final String coverPath, final String templatePath, final String infoJsonPath, final String cloudToLocalMapInfo, final long duration, final long fileSize, final String modifiedTime, final DraftObserver observer) {
        ThreadUtils.getIoPool().execute(new Runnable() {
            @Override
            public void run() {
                if (TextUtils.isEmpty(mCurrentDraftDir)) {
                    LogUtils.e("error ,when the draftDir is null ,can not update draft!!");
                    return;
                }
                long localModifiedTime = System.currentTimeMillis();
                String draftDir = getCurrentDraftDir(String.valueOf(localModifiedTime));
                LocalDraftEntity entity = mLocalDraftDao.getDraft(projectId);
                if (entity != null) {
                    if (duration > 0) {
                        entity.setDuration(String.valueOf(duration));
                    }
                    entity.setRemoteId(remoteId);
                    entity.setCloudModifiedAt(Long.parseLong(modifiedTime));
                    entity.setModifiedAt(localModifiedTime);
                    entity.setCreateAt(Long.parseLong(modifiedTime));
                    entity.setCoverPath(coverPath);
                    entity.setDirPath(draftDir);
                    if (fileSize > 0) {
                        entity.setFileSize(String.valueOf(fileSize));
                    }
                    entity.setTemplatePath(templatePath);
                    entity.setCloud(true);
                    entity.setInfoJsonPath(infoJsonPath);
                    if (!TextUtils.isEmpty(cloudToLocalMapInfo)) {
                        entity.setCloudToLocalMapInfo(cloudToLocalMapInfo);
                    }
                    mLocalDraftDao.updateDraft(entity);
                } else {
                    entity = new LocalDraftEntity(projectId);
                    entity.setCloudModifiedAt(Long.parseLong(modifiedTime));
                    entity.setModifiedAt(localModifiedTime);
                    if (TextUtils.isEmpty(name)) {
                        entity.setName(modifiedTime);
                    } else {
                        entity.setName(name);
                    }
                    entity.setCoverPath(coverPath);
                    entity.setDirPath(draftDir);
                    entity.setId(projectId);
                    entity.setRemoteId(remoteId);
                    if (fileSize > 0) {
                        entity.setFileSize(String.valueOf(fileSize));
                    }
                    if (duration > 0) {
                        entity.setDuration(String.valueOf(duration));
                    }
                    entity.setCreateAt(Long.parseLong(modifiedTime));
                    entity.setTemplatePath(templatePath);
                    entity.setInfoJsonPath(infoJsonPath);
                    if (!TextUtils.isEmpty(cloudToLocalMapInfo)) {
                        entity.setCloudToLocalMapInfo(cloudToLocalMapInfo);
                    }
                    entity.setCloud(true);
                    mLocalDraftDao.insertDraft(entity);
                    saveUserInfo(projectId);
                }
                notifyDataChanged(observer);
            }
        });
    }

    /**
     * 保存草稿
     * Save the draft data
     *
     * @param timeline the timeline
     * @param duration the duration
     * @param bitmap   the cover bitmap
     */
    public void saveDraft(final MeicamTimeline timeline, final long duration, final Bitmap bitmap) {
        /*
         * 保存的永远是新建的
         * Saved is always new
         */
        setCurrentDraftDir("");
        final String currentTime = String.valueOf(System.currentTimeMillis());
        String draftDir = timeline.getDraftDir();
        if (draftDir == null) {
            draftDir = getCurrentDraftDir(currentTime);
        }
        final String projectId = timeline.getProjectId();
        timeline.setProjectName(currentTime);
        timeline.setProjectDuring(FormatUtils.microsecond2Time(duration));
        timeline.setDuration(duration);
        final String[] json = {timeline.toDraftJson()};
        final String finalDraftDir = draftDir;
        ThreadUtils.getIoPool().execute(new Runnable() {
            @Override
            public void run() {
                String fileSize = getDraftFileSize(timeline);
                String coverPath = timeline.getCoverImagePath();
                if (bitmap != null) {
                    //为空则不更新 If empty, do not update.
                    coverPath = saveCover(bitmap, finalDraftDir, currentTime);
                }
                String[] keys = new String[]{JSON_KEY_LAST_MODIFY_TIME, JSON_KEY_COVER_PATH, JSON_KEY_COVER_IMAGE_PATH, JSON_KEY_FILE_SIZE};
                String[] values = new String[]{currentTime, coverPath, coverPath, fileSize};
                json[0] = addParamToJson(json[0], keys, values);
                String jsonInfoPath = getDraftPath(finalDraftDir);
                FileIOUtils.writeFileFromString(jsonInfoPath, json[0]);
                //插入表
                //insert table
                LocalDraftEntity entity = new LocalDraftEntity(projectId);
                entity.setId(projectId);
                entity.setDuration(String.valueOf(duration));
                entity.setModifiedAt(Long.parseLong(currentTime));
                entity.setName(currentTime);
                entity.setCoverPath(coverPath);
                entity.setDirPath(finalDraftDir);
                entity.setFileSize(fileSize);
                entity.setCloud(false);
                entity.setCreateAt(Long.parseLong(currentTime));
                mLocalDraftDao.insertDraft(entity);
                saveUserInfo(projectId);
                notifyDataChanged(null);
            }
        });

    }

    private void saveUserInfo(final String projectId) {
        UserDraftEntity userDraftEntity = new UserDraftEntity();
        userDraftEntity.setUserId(getUserId());
        userDraftEntity.setId(projectId);
        mUserDraftDao.insert(userDraftEntity);
    }

    private void deleteUserInfo(final String projectId) {
        UserDraftEntity userDraft = mUserDraftDao.getUserDraft(projectId);
        if (userDraft != null) {
            mUserDraftDao.delete(userDraft);
        }
    }

    @Nullable
    private static String getUserId() {
        IUserPlugin userPlugin = PluginManager.get().getUserPlugin();
        String userId = null;
        if (userPlugin != null && userPlugin.isLogin()) {
            userId = userPlugin.getUserId();
        }
        if (TextUtils.isEmpty(userId)) {
            userId = NON_USER_ID;
        }
        return userId;
    }

    /**
     * 保存封面图
     * Save the cover bitmap
     *
     * @param bitmap      the cover bitmap
     * @param draftDir    the cover saved path
     * @param currentTime the current timestamp
     */
    private String saveCover(Bitmap bitmap, String draftDir, String currentTime) {
        File file = new File(draftDir + File.separator + "cover" + currentTime + ".png");
        List<File> fileList = FileUtils.listFilesInDir(file.getParentFile());
        if (fileList != null && fileList.size() > 0) {
            for (int i = 0; i < fileList.size(); i++) {
                File temp = fileList.get(i);
                if (temp.getName().endsWith(".png")) {
                    if (temp.delete()) {
                        fileList.remove(i);
                        i--;
                    }
                }
            }
        }
        ImageUtils.save(bitmap, file, Bitmap.CompressFormat.PNG);
        return file.getAbsolutePath();
    }


    /**
     * 向所给json中添加参数
     * Add the params to target json
     *
     * @param json   the target json
     * @param keys   the keys
     * @param values the values
     * @return the String value
     */
    private String addParamToJson(String json, String[] keys, String[] values) {
        if (keys == null || values == null || keys.length != values.length || TextUtils.isEmpty(json)) {
            LogUtils.e("param error");
            return json;
        }
        try {
            JSONObject jsonObject = new JSONObject(json);
            for (int index = 0; index < keys.length; index++) {
                addParamToJson(jsonObject, keys[index], values[index]);
            }
            return jsonObject.toString();
        } catch (JSONException e) {
            LogUtils.e(e);
        }
        return json;
    }

    /**
     * 向所给json中添加参数
     * Add the params to target json
     *
     * @param jsonObject the target JSONObject
     * @param key        the key
     * @param value      the value
     * @return the JSONObject
     */
    private JSONObject addParamToJson(@NonNull JSONObject jsonObject, String key, Object value) {
        if (key != null && value != null) {
            try {
                jsonObject.put(key, value);
            } catch (JSONException e) {
                LogUtils.e(e);
            }
        }
        return jsonObject;
    }

    /**
     * 获取当前草稿的目录路径
     */
    private String getCurrentDraftDir(String draftName) {
        if (TextUtils.isEmpty(mCurrentDraftDir)) {
            mCurrentDraftDir = newDraftDir(draftName);
        }
        return mCurrentDraftDir;
    }

    public static String newDraftDir(String draftName){
        String draftDirPath = getDraftRootPath(getUserId());
        if (!TextUtils.isEmpty(draftDirPath)) {
            File folder = new File(draftDirPath + File.separator + draftName);
            if (!folder.exists()) {
                folder.mkdir();
            }
           return folder.getAbsolutePath();
        }
        return null;
    }

    /**
     * 设置当前的草稿目录路径
     * Set current draft dir
     *
     * @param draftDir the draft dir
     */
    public void setCurrentDraftDir(String draftDir) {
        mCurrentDraftDir = draftDir;
    }

    /**
     * 获取当前的草稿目录路径
     * Get current draft dir
     */
    public String getCurrentDraftDir() {
        return mCurrentDraftDir;
    }


    public void copyDraft(final DraftData data, final DraftChangedListener listener) {
        ThreadUtils.getIoPool().execute(new Runnable() {
            @Override
            public void run() {
                final DraftData cloneData = data.clone();
                long modifiedTime = System.currentTimeMillis();
                final String projectId = UUID.randomUUID().toString().toUpperCase();
                try {
                    String currentTime = TimeUtils.millis2String(modifiedTime,
                            new SimpleDateFormat("yyyy.MM.dd HH:mm"));
                    cloneData.setLastModifyTime(currentTime);
                    File parentFile = new File(data.getDirPath()).getParentFile();
                    String dirPath = null;
                    if (parentFile != null) {
                        dirPath = parentFile.getAbsolutePath() + File.separator + projectId;
                    }
                    if (dirPath == null) {
                        dirPath = getDirPath(modifiedTime);
                    }
                    File dirFile = new File(dirPath);
                    if (!dirFile.exists()) {
                        dirFile.mkdir();
                    }
                    cloneData.setDirPath(dirPath);
                    if (!data.isOnlyCloud()) {
                        String json = data.getJsonData();
                        JSONObject jsonObject = new JSONObject(json);
                        jsonObject.put(JSON_KEY_LAST_MODIFY_TIME, String.valueOf(modifiedTime));
                        jsonObject.put(JSON_KEY_PROJECT_ID, projectId);
                        json = jsonObject.toString();
                        cloneData.setJsonData(json);
                        FileIOUtils.writeFileFromString(getDraftPath(dirPath), json);
                    }
                    cloneData.setLastModifyTimeLong(modifiedTime);
                    cloneData.setProjectId(projectId);
                    LocalDraftEntity oldDraft = mLocalDraftDao.getDraft(data.getProjectId());
                    if (oldDraft != null) {
                        LocalDraftEntity entity = oldDraft.clone();
                        entity.setId(projectId);
                        entity.setModifiedAt(modifiedTime);
                        entity.setCreateAt(modifiedTime);
                        entity.setDirPath(dirPath);
                        entity.setRemoteId("");
                        entity.setCloud(false);
                        mLocalDraftDao.insertDraft(entity);
                    }
                    //资源文件需要同步更新 Resource files need to be updated.
                    List<ResourceEntity> resource = mProjectDao.getResource(data.getProjectId());
                    if (!CommonUtils.isEmpty(resource)) {
                        for (ResourceEntity resourceEntity : resource) {
                            ProjectEntity projectEntity = new ProjectEntity();
                            projectEntity.setProjectResourceId(projectId + resourceEntity.getId());
                            projectEntity.setResourceId(resourceEntity.getId());
                            projectEntity.setProjectId(projectId);
                            mProjectDao.insert(projectEntity);
                        }
                    }
                    data.setUpload(false);
                    saveUserInfo(projectId);
                } catch (Exception e) {
                    LogUtils.e("addExtraParamToJson error: " + e.fillInStackTrace());
                }
                if (listener != null) {
                    ThreadUtils.runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            listener.onCopySuccess(cloneData);
                        }
                    });
                }
                notifyDataChanged(null);
            }
        });
    }

    private List<DraftData> getDraftDataFromDb() {
        List<LocalDraftEntity> allData = new ArrayList<>();
        List<LocalDraftEntity> common = mUserDraftDao.getDraftList(NON_USER_ID);
        String userId = getUserId();
        if (!CommonUtils.isEmpty(common)) {
            allData.addAll(common);
        }
        if (!NON_USER_ID.equals(userId)) {
            List<LocalDraftEntity> draft = mUserDraftDao.getDraftList(userId);
            if (!CommonUtils.isEmpty(draft)) {
                allData.addAll(draft);
            }
        }
        List<DraftData> dataList = new ArrayList<>();
        for (LocalDraftEntity draftEntity : allData) {
            DraftData draftData = DraftData.create(draftEntity);
            dataList.add(draftData);
            String jsonFile = getDraftPath(draftData.getDirPath());
            if (!TextUtils.isEmpty(jsonFile)) {
                draftData.setJsonData(FileIOUtils.readFile2String(jsonFile, "utf-8"));
            }
        }
        return dataList;
    }


    /**
     * Gets draft data.
     * 获取草稿数据
     * @param userId the user id 用户id，如果为空，表示查找的是老数据
     * @return the draft data
     */
    public List<DraftData> getDraftData(String userId) {
        String path = getDraftRootPath(userId);
        if (TextUtils.isEmpty(path)) {
            return null;
        }
        File file = new File(path);
        if (!file.exists()) {
            return null;
        }
        String[] list = file.list();
        if (list == null || list.length == 0) {
            return null;
        }
        List<DraftData> localData = new ArrayList<>();
        for (String name : list) {
            if (userId != null && !userId.equals(name)) {
                continue;
            }
            String folder = path + File.separator + name;
            File folderFile = new File(folder);
            if (!folderFile.exists()) {
                continue;
            }
            String[] draftList = folderFile.list();
            for (String draftFolder : draftList) {
                DraftData draftData = new DraftData();
                String draftDir = folder + File.separator + draftFolder;
                draftData.setFileName(draftFolder);
                if (!parseJsonData(draftData, draftDir)) {
                    continue;
                }
                localData.add(draftData);
            }
        }
        return localData;
    }

    private boolean parseJsonData(DraftData draftData, String folder) {
        String jsonFile = getDraftPath(folder);
        if (!new File(jsonFile).exists()) {
            return false;
        }
        draftData.setDirPath(folder);
        String jsonData = FileIOUtils.readFile2String(jsonFile, "utf-8");
        if (TextUtils.isEmpty(jsonData)) {
            return false;
        }
        draftData.setJsonData(jsonData);
        draftData.setUpload(false);
        try {
            JSONObject jsonObject = new JSONObject(jsonData);
            Object fileSize = jsonObject.opt(JSON_KEY_FILE_SIZE);
            if ((fileSize instanceof String)) {
                draftData.setFileSize((String) fileSize);
            }
            Object modifyTime = jsonObject.opt(JSON_KEY_LAST_MODIFY_TIME);
            if (modifyTime instanceof String) {
                String currentTime = TimeUtils.millis2String(Long.parseLong((String) modifyTime), new SimpleDateFormat("yyyy.MM.dd HH:mm"));
                draftData.setLastModifyTime(currentTime);
                draftData.setLastModifyTimeLong(Long.parseLong((String) modifyTime));
            }
            Object projectId = jsonObject.opt(JSON_KEY_PROJECT_ID);
            if (projectId instanceof String) {
                if (!TextUtils.isEmpty((CharSequence) projectId)) {
                    draftData.setProjectId((String) projectId);
                    draftData.getCloudInfo().uuid = (String) projectId;
                }
            }
            Object coverPath = jsonObject.opt(JSON_KEY_COVER_PATH);
            if (coverPath instanceof String) {
                draftData.setCoverPath((String) coverPath);
            }

            coverPath = jsonObject.opt(JSON_KEY_COVER_IMAGE_PATH);
            if (coverPath instanceof String) {
                draftData.setCoverPath((String) coverPath);
            }

            Object during = jsonObject.opt(JSON_KEY_DURING);
            if (during instanceof String) {
                draftData.setDuration((String) during);
            }

            Object projectName = jsonObject.opt(JSON_KEY_PROJECT_NAME);
            if (projectName instanceof String) {
                draftData.setFileName((String) projectName);
            }
        } catch (Exception e) {
            LogUtils.e("getDraftData error: " + e.fillInStackTrace());
        }
        return true;
    }

    /**
     * 获取草稿数据
     *
     * @return
     */
    public List<DraftData> getAllDraftData() {
        List<DraftData> draftData = getDraftDataFromDb();
        boolean isFromDb = true;
        if (CommonUtils.isEmpty(draftData)) {
            String userId = getUserId();
            if (NON_USER_ID.equals(userId)) {
                //查询老草稿地址
                //Query old draft address
                draftData = getDraftData(null);
            }
            //查询和用户相关的草稿地址
            //Query draft address related to user
            List<DraftData> userDraftData = getDraftData(userId);
            if (!CommonUtils.isEmpty(userDraftData)) {
                draftData.addAll(userDraftData);
            }
            isFromDb = false;
            LogUtils.d("from file");
        } else {
            LogUtils.d("from db");
        }
        if (!CommonUtils.isEmpty(draftData)) {
            Collections.sort(draftData);
            if (!isFromDb) {
                final List<DraftData> finalDraftData = draftData;
                ThreadUtils.getIoPool().execute(new Runnable() {
                    @Override
                    public void run() {
                        for (DraftData draftDatum : finalDraftData) {
                            //老草稿没有入库，这里将老草稿入库
                            // The old draft has not been stored in the database, so it will be stored here.
                            LocalDraftEntity draft = mLocalDraftDao.getDraft(draftDatum.getProjectId());
                            if (draft == null) {
                                mLocalDraftDao.insertDraft(DraftData.createLocalEntity(draftDatum));
                            }
                        }
                    }
                });
            }
        }
        return draftData;
    }

    private String getDraftFileSize(MeicamTimeline timeline) {
        Set<String> allClipPath = new HashSet<>();
        int count = timeline.videoTrackCount();
        if (count > 0) {
            for (int index = 0; index < count; index++) {
                MeicamVideoTrack videoTrack = timeline.getVideoTrack(index);
                int clipCount = videoTrack.getClipCount();
                if (clipCount > 0) {
                    for (int i = 0; i < clipCount; i++) {
                        MeicamVideoClip videoClip = videoTrack.getVideoClip(i);
                        if (videoClip != null) {
                            String filePath = videoClip.getFilePath();
                            if (!TextUtils.isEmpty(filePath)) {
                                allClipPath.add(filePath);
                            }
                        }
                    }
                }
            }
        }
        return String.valueOf(getGroupFileSize(allClipPath));
    }

    private long getGroupFileSize(Set<String> clipInfoArray) {
        long fileSize = -1;
        if (CommonUtils.isEmpty(clipInfoArray)) {
            return fileSize;
        }
        for (String filePath : clipInfoArray) {
            if (TextUtils.isEmpty(filePath)) {
                LogUtils.e("filePath==null");
                continue;
            }
            long clipSize = FileUtils.getFileLength(filePath);
            if (clipSize > 0) {
                fileSize += clipSize;
            }
        }
        return fileSize;
    }

    public void renameDraft(final DraftData draftData, final String name, final long lastModifiedTime) {
        ThreadUtils.getIoPool().execute(new Runnable() {
            @Override
            public void run() {
                LTimelineData lTimelineData = LTimelineData.fromJson(draftData.getJsonData());
                if (lTimelineData != null) {
                    String json = lTimelineData.toJson();
                    String[] keys = new String[]{JSON_KEY_PROJECT_NAME, JSON_KEY_LAST_MODIFY_TIME};
                    String[] values = new String[]{name, String.valueOf(lastModifiedTime)};
                    String dirPath = draftData.getDirPath();
                    boolean success = FileIOUtils.writeFileFromString(getDraftPath(dirPath),
                            addParamToJson(json, keys, values));
                    LocalDraftEntity entity = mLocalDraftDao.getDraft(draftData.getProjectId());
                    if (entity != null) {
                        entity.setName(name);
                        mLocalDraftDao.updateDraft(entity);
                    }
                    LogUtils.e("success = " + success + ", dir = " + dirPath);
                    notifyDataChanged(null);
                }
            }
        });

    }

    public void deleteDraft(final DraftData draftData) {
        if (draftData == null) {
            return;
        }
        ThreadUtils.getIoPool().execute(new Runnable() {
            @Override
            public void run() {
                FileUtils.delete(draftData.getDirPath());
                String projectId = draftData.getProjectId();
                if (!TextUtils.isEmpty(projectId)) {
                    LocalDraftEntity draft = mLocalDraftDao.getDraft(projectId);
                    if (draft != null) {
                        mLocalDraftDao.deleteDraft(draft);
                    }
                    deleteUserInfo(projectId);
                    mProjectDao.delete(projectId);
                }
                notifyDataChanged(null);
            }
        });
    }

    private String getDraftPath(String fileDir) {
        if (!TextUtils.isEmpty(fileDir)) {
            fileDir = fileDir + File.separator + "config.json";
        }
        return fileDir;
    }

    public static String getCoverConfigPath(String fileDir) {
        if (!TextUtils.isEmpty(fileDir)) {
            fileDir = fileDir + File.separator + "config_cover.json";
        }
        return fileDir;
    }


    public String getDirPath(long modifiedTime) {
        String draftDirPath = getDraftRootPath(getUserId());
        if (!TextUtils.isEmpty(draftDirPath)) {
            File folder = new File(draftDirPath + File.separator + modifiedTime);
            if (!folder.exists()) {
                folder.mkdir();
            }
            return folder.getAbsolutePath();
        }
        return null;
    }

    /**
     * Get draft root dir string.
     * 获取草稿根路径
     * @return the string
     */
    public static String getDraftRootDir(){
        return getDraftRootPath(getUserId());
    }

    private static String getDraftRootPath(String userId) {
        File dataDir;
        if (SAVE_DRAFT_TO_SDCARD) {
            dataDir = Environment.getExternalStorageDirectory();
            if (dataDir != null) {
                String dir = dataDir.getAbsolutePath() + File.separator + "MYVideo" + File.separator + IMAGE_BACKGROUND_DRAFT;
                if (!TextUtils.isEmpty(userId)) {
                    dir += File.separator + userId;
                }
                return dir;
            }
        } else {
            dataDir = Utils.getApp().getExternalFilesDir(null);
            if (dataDir != null) {
                String dir = dataDir.getAbsolutePath() + File.separator + IMAGE_BACKGROUND_DRAFT;
                if (!TextUtils.isEmpty(userId)) {
                    dir += File.separator + userId;
                }
                return dir;
            }
        }
        return null;
    }

    /**
     * 注册资源变动观察者
     * Register as a resource change observer
     *
     * @param observer the observer
     */
    public void registerDraftObserver(DraftObserver observer) {
        try {
            mDraftObservable.registerObserver(observer);
        } catch (Exception e) {
            LogUtils.e(e);
        }

    }

    /**
     * 取消注册资源变动观察者
     * Unregister as a resource change observer
     *
     * @param observer the observer
     */
    public void unregisterDraftObserver(DraftObserver observer) {
        try {
            mDraftObservable.unregisterObserver(observer);
        } catch (Exception e) {
            LogUtils.e(e);
        }

    }

    /**
     * Notify data changed.
     * 通知观察者
     *
     * @param observer the observer
     */
    public void notifyDataChanged(DraftObserver observer) {
        if (mDraftObservable != null) {
            mDraftObservable.setDraftChange(observer);
        }
    }

    public void deleteDraftFromNet(List<String> list) {
        if (!CommonUtils.isEmpty(list)) {
            for (String uuid : list) {
                LocalDraftEntity draft = mLocalDraftDao.getDraft(uuid);
                if (draft != null) {
                    draft.setCloud(false);
                    mLocalDraftDao.updateDraft(draft);
                }
            }
            notifyDataChanged(null);
        }
    }

    public void setCurrentDraft(DraftData item) {
        mCurrentDraftData = item;
    }

    public DraftData getCurrentDraftData() {
        return mCurrentDraftData;
    }

    /**
     * 根据草稿类型获取草稿数据
     * Get draft data by draft type
     * @param draftType 草稿类型
     * @return 草稿数据列表
     */
    public List<DraftData> getDraftDataByType(DraftType draftType) {
        List<LocalDraftEntity> allData = new ArrayList<>();
        List<LocalDraftEntity> common = mLocalDraftDao.getDraftByTypeOrderByTime(draftType.getValue());
        String userId = getUserId();
        if (!CommonUtils.isEmpty(common)) {
            allData.addAll(common);
        }
        if (!NON_USER_ID.equals(userId)) {
            List<LocalDraftEntity> draft = mUserDraftDao.getDraftListByTypeOrderByTime(userId, draftType.getValue());
            if (!CommonUtils.isEmpty(draft)) {
                allData.addAll(draft);
            }
        }
        List<DraftData> dataList = new ArrayList<>();
        for (LocalDraftEntity draftEntity : allData) {
            DraftData draftData = DraftData.create(draftEntity);
            dataList.add(draftData);
            String jsonFile = getDraftPath(draftData.getDirPath());
            if (!TextUtils.isEmpty(jsonFile)) {
                draftData.setJsonData(FileIOUtils.readFile2String(jsonFile, "utf-8"));
            }
        }
        return dataList;
    }

    /**
     * 获取用户草稿数据
     * Get user draft data
     * @return 用户草稿数据列表
     */
    public List<DraftData> getUserDraftData() {
        return getDraftDataByType(DraftType.USER_DRAFT);
    }

    /**
     * 获取模板数据
     * Get template data
     * @return 模板数据列表
     */
    public List<DraftData> getTemplateData() {
        return getDraftDataByType(DraftType.TEMPLATE);
    }

    /**
     * 创建模板草稿
     * Create template draft
     * @param templateName 模板名称
     * @param templatePreviewPath 模板预览视频路径
     * @param templateJsonData 模板JSON数据
     * @param coverPath 封面路径
     * @param duration 时长
     * @return 创建的模板草稿数据
     */
    public DraftData createTemplateDraft(String templateName, String templatePreviewPath,
                                       String templateJsonData, String coverPath, long duration) {
        String projectId = UUID.randomUUID().toString().toUpperCase();
        String currentTime = String.valueOf(System.currentTimeMillis());
        String draftDir = getDraftRootPath(getUserId()) + File.separator + projectId;

        try {
            // 创建草稿目录
            FileUtils.createOrExistsDir(draftDir);

            // 保存JSON数据
            String jsonInfoPath = getDraftPath(draftDir);
            FileIOUtils.writeFileFromString(jsonInfoPath, templateJsonData);

            // 创建数据库实体
            LocalDraftEntity entity = new LocalDraftEntity(projectId);
            entity.setId(projectId);
            entity.setDuration(String.valueOf(duration));
            entity.setModifiedAt(Long.parseLong(currentTime));
            entity.setName(templateName);
            entity.setCoverPath(coverPath);
            entity.setDirPath(draftDir);
            entity.setCloud(false);
            entity.setCreateAt(Long.parseLong(currentTime));
            entity.setDraftType(DraftType.TEMPLATE.getValue());
            entity.setTemplatePreviewPath(templatePreviewPath);

            // 插入数据库
            mLocalDraftDao.insertDraft(entity);

            // 创建DraftData对象
            DraftData draftData = DraftData.create(entity);
            draftData.setJsonData(templateJsonData);

            // 通知数据变化
            notifyDataChanged(draftData);

            return draftData;
        } catch (Exception e) {
            LogUtils.e("创建模板草稿失败", e);
            return null;
        }
    }

    public interface DraftChangedListener{
        void onCopySuccess(DraftData draftData);
    }
}
