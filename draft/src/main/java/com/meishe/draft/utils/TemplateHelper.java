package com.meishe.draft.utils;

import android.text.TextUtils;

import com.meishe.base.utils.LogUtils;
import com.meishe.draft.DraftManager;
import com.meishe.draft.data.DraftData;
import com.meishe.draft.data.DraftType;

/**
 * 模板相关的工具类
 * Template related utility class
 */
public class TemplateHelper {

    /**
     * 从模板创建用户草稿
     * Create user draft from template
     * @param templateData 模板数据
     * @param userVideoData 用户选择的视频数据（JSON格式）
     * @param newDraftName 新草稿名称
     * @return 创建的用户草稿数据
     */
    public static DraftData createDraftFromTemplate(DraftData templateData, String userVideoData, String newDraftName,String previewPath) {
        if (templateData == null || !templateData.isTemplate()) {
            LogUtils.e("Invalid template data");
            return null;
        }

        if (TextUtils.isEmpty(userVideoData)) {
            LogUtils.e("User video data is empty");
            return null;
        }

        try {
            // 获取模板的JSON数据
            String templateJsonData = templateData.getJsonData();
            if (TextUtils.isEmpty(templateJsonData)) {
                LogUtils.e("Template JSON data is empty");
                return null;
            }

            // 合并模板数据和用户视频数据
            String mergedJsonData = mergeTemplateWithUserData(templateJsonData, userVideoData);

            // 创建新的用户草稿
            DraftData newDraft = DraftManager.getInstance().createTemplateDraft(
                
                newDraftName,
                    previewPath,
                    mergedJsonData,
                templateData.getCoverPath(),
                templateData.getDurationLong()
            );

            if (newDraft != null) {
                // 确保新草稿是用户草稿类型
                newDraft.setDraftType(DraftType.USER_DRAFT);
                LogUtils.d("Successfully created draft from template: " + newDraftName);
            }

            return newDraft;
        } catch (Exception e) {
            LogUtils.e("Failed to create draft from template", e);
            return null;
        }
    }

    /**
     * 合并模板数据和用户视频数据
     * Merge template data with user video data
     * @param templateJsonData 模板JSON数据
     * @param userVideoData 用户视频数据
     * @return 合并后的JSON数据
     */
    private static String mergeTemplateWithUserData(String templateJsonData, String userVideoData) {
        // 这里需要根据具体的JSON结构来实现合并逻辑
        // 简单示例：将用户视频数据替换模板中的占位符
        // TODO: 根据实际的JSON结构实现具体的合并逻辑
        
        // 示例实现：假设需要替换模板中的视频轨道数据
        try {
            // 这里应该使用JSON解析库（如Gson）来处理JSON数据的合并
            // 当前只是一个简单的字符串替换示例
            return templateJsonData.replace("\"videoTracks\":[]", "\"videoTracks\":" + userVideoData);
        } catch (Exception e) {
            LogUtils.e("Failed to merge template with user data", e);
            return templateJsonData;
        }
    }

    /**
     * 检查模板是否有效
     * Check if template is valid
     * @param templateData 模板数据
     * @return true if valid, false otherwise
     */
    public static boolean isValidTemplate(DraftData templateData) {
        if (templateData == null) {
            return false;
        }

        if (!templateData.isTemplate()) {
            return false;
        }

        if (TextUtils.isEmpty(templateData.getJsonData())) {
            return false;
        }

        return true;
    }

    /**
     * 获取模板预览视频路径
     * Get template preview video path
     * @param templateData 模板数据
     * @return 预览视频路径
     */
    public static String getTemplatePreviewPath(DraftData templateData) {
        if (templateData == null || !templateData.isTemplate()) {
            return null;
        }
        return templateData.getTemplatePreviewPath();
    }
}
