package com.meishe.draft.data;

/**
 * 草稿类型枚举
 * Draft type enumeration
 */
public enum DraftType {
    /**
     * 用户草稿
     * User draft
     */
    USER_DRAFT(0),
    
    /**
     * 模板
     * Template
     */
    TEMPLATE(1);
    
    private final int value;
    
    DraftType(int value) {
        this.value = value;
    }
    
    public int getValue() {
        return value;
    }
    
    public static DraftType fromValue(int value) {
        for (DraftType type : DraftType.values()) {
            if (type.getValue() == value) {
                return type;
            }
        }
        return USER_DRAFT; // 默认返回用户草稿类型
    }
}
