apply from: "config.gradle"
buildscript {
    repositories {
        maven { url 'https://maven.aliyun.com/repository/public/' }
        maven { url 'https://maven.aliyun.com/repository/gradle-plugin' }
        maven { url 'https://maven.aliyun.com/nexus/content/groups/public/' }
        maven { url 'https://maven.aliyun.com/nexus/content/repositories/jcenter' }
        maven { url 'https://maven.aliyun.com/nexus/content/repositories/google' }
        maven { url 'https://maven.aliyun.com/nexus/content/repositories/gradle-plugin' }
        maven { url 'https://jitpack.io' }
        maven { url 'https://repo1.maven.org/maven2/' }
        maven { url "https://maven.aliyun.com/nexus/content/repositories/releases" }
        // Add Google's Maven repository for ExoPlayer
        maven { url "https://maven.google.com" }
        // Add JCenter as a fallback
        maven { url "https://jcenter.bintray.com" }
        google()
        jcenter()
        maven{url'https://repo1.maven.org/maven2/'}

    }
    dependencies {
        classpath 'com.android.tools.build:gradle:7.1.1'
        classpath 'com.github.kezong:fat-aar:1.3.8'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:1.9.0"
        //Bugly符号表插件zz
        //classpath 'com.tencent.bugly:symtabfileuploader:latest.release'
       // classpath 'org.greenrobot:greendao-gradle-plugin:3.2.2' // add plugin
        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

allprojects {
    repositories {
        maven { url 'https://maven.aliyun.com/repository/public/' }
        maven { url 'https://maven.aliyun.com/repository/gradle-plugin' }
        maven { url 'https://maven.aliyun.com/nexus/content/groups/public/' }
        maven { url 'https://maven.aliyun.com/nexus/content/repositories/jcenter' }
        maven { url 'https://maven.aliyun.com/nexus/content/repositories/google' }
        maven { url 'https://maven.aliyun.com/nexus/content/repositories/gradle-plugin' }
        maven { url 'https://jitpack.io' }
        maven { url 'https://repo1.maven.org/maven2/' }
        maven { url "https://maven.aliyun.com/nexus/content/repositories/releases" }
        // Add Google's Maven repository for ExoPlayer
        maven { url "https://maven.google.com" }
        // Add JCenter as a fallback
        maven { url "https://jcenter.bintray.com" }
        mavenCentral()
        google()
        jcenter()
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}
