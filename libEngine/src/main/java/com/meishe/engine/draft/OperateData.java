package com.meishe.engine.draft;

/**
 * author：yangtailin on 2020/6/17 17:24
 * 草稿的数据类
 */
public class OperateData {
    private String mDraftData;
    private String mTag;
    private String mDraftFilePath;

    public OperateData(String draftData, String tag) {
        this.mDraftData = draftData;
        this.mTag = tag;
    }

    public String getDraftData() {
        return mDraftData;
    }

    public void setDraftData(String draftData) {
        this.mDraftData = draftData;
    }

    public String getTag() {
        return mTag;
    }

    public void setTag(String mTag) {
        this.mTag = mTag;
    }

    public String getDraftFilePath() {
        return mDraftFilePath;
    }

    public void setDraftFilePath(String draftFilePath) {
        this.mDraftFilePath = draftFilePath;
    }
}
