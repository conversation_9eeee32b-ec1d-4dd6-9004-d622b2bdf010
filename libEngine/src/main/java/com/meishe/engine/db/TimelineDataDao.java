package com.meishe.engine.db;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;


/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateDate :2021/05/17 10.49
 * @Description :时间线数据增删改查接口 Database add delete change check interface
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
@Dao
public interface TimelineDataDao {
    @Insert
    void insertAsset(TimelineEntity... timelineData);

    @Update
    void updateAsset(TimelineEntity... timelineData);

    @Delete
    void deleteAsset(TimelineEntity... timelineData);

    @Query("DELETE  FROM TimelineEntity")
    void deleteAll();

    @Query("SELECT * FROM TimelineEntity WHERE id = :id")
    TimelineEntity getTimelineData(String id);
}
