package com.meishe.engine.db;

import androidx.annotation.NonNull;
import androidx.room.Entity;
import androidx.room.PrimaryKey;

import com.meishe.engine.asset.bean.AssetInfo;


/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2021/04/06 15:10
 * @Description :用户资源数据库关联表 User resource database association
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
@Entity
public class UserAssetsEntity {
    /**
     * 主键，用户Id
     * The primary key  , package id
     */
    @PrimaryKey(autoGenerate = true)
    @NonNull
    private int id = 0;

    private String token;

    private String paramKey;
    /**
     * 资源包Id
     * The package id
     */
    private String assetsId;

    public static UserAssetsEntity create(String token, String key, AssetInfo assetInfo) {
        UserAssetsEntity entity = new UserAssetsEntity();
        entity.setToken(token);
        entity.setParamKey(key);
        entity.setAssetsId(assetInfo.getId());
        entity.setId(0);
        return entity;
    }


    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }


    public String getAssetsId() {
        return assetsId;
    }

    public void setAssetsId(String assetsId) {
        this.assetsId = assetsId;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getParamKey() {
        return paramKey;
    }

    public void setParamKey(String paramKey) {
        this.paramKey = paramKey;
    }

    @Override
    public String toString() {
        return "UserAssetsEntity{" +
                "token='" + token + '\'' +
                ", assetsId='" + assetsId + '\'' +
                '}';
    }
}
