package com.meishe.engine.asset;

import android.content.Context;

import com.meishe.engine.asset.bean.AssetList;
import com.meishe.engine.asset.bean.RequestParam;
import com.meishe.engine.bean.BaseInfo;
import com.meishe.net.custom.BaseResponse;
import com.meishe.net.custom.RequestCallback;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2021/3/22 16:01
 * @Description :
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class AssetsProxy extends IAssetsManager {

    public AssetsProxy() {
    }

    @Override
    public int[] getAspectRatio(float ratio) {
        int[] data = new int[2];
        data[0] = BaseInfo.AspectRatio_All;
        return data;
    }

    @Override
    public void getAssetsList(String token, RequestParam param, int aspectRatio, int ratioFlag, int page, int pageSize, final RequestCallback<AssetList> callback) {
        //从网络获取
        //Get assets from net
        AssetsManager.get().getAssetListNew(token, param,
                aspectRatio, ratioFlag, page, pageSize, new RequestCallback<AssetList>() {
        //AssetsManager.get().getAssetList(param, aspectRatio, page, pageSize, new RequestCallback<AssetList>() {
            @Override
            public void onSuccess(BaseResponse<AssetList> response) {
                if (callback != null) {
                    callback.onSuccess(response);
                }
            }

            @Override
            public void onError(BaseResponse<AssetList> response) {
                if (callback != null) {
                    callback.onError(response);
                }
            }
        });
    }

    @Override
    void getLocalAssetList(RequestParam param, AssetCallback callback) {
        AssetsManager.get().getAssetsListFromDb(null, param, callback);
    }

    @Override
    public String getErrorMsg(Context context, int type) {
        return "";
    }
}
