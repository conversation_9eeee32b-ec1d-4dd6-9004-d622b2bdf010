package com.meishe.engine.asset.bean;

import com.meishe.base.utils.CommonUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class BackgrondBean implements Serializable {
   private int materialCount;
   private List<MaterialList> materialList;

   public int getMaterialCount() {
      return materialCount;
   }

   public void setMaterialCount(int materialCount) {
      this.materialCount = materialCount;
   }

   public List<MaterialList> getMaterialList() {
      return materialList;
   }

   public void setMaterialList(List<MaterialList> materialList) {
      this.materialList = materialList;
   }

   public AssetList toAssetsList(){
      AssetList list = new AssetList();
      list.type = AssetInfo.ASSET_PACKAGE_BACKGROUND;
      list.total = materialCount;
      if (!CommonUtils.isEmpty(materialList)) {
         List<AssetInfo> assetInfoList = new ArrayList<>();
         for (MaterialList materialListItem : materialList) {
            assetInfoList.add(materialListItem.create());
         }
         list.realAssetList = assetInfoList;
      }
      return list;

   }

   public static class MaterialList implements Serializable{
      private String id;
      private String materialType;
      private String version;
      private String packageUrl;
      private String coverUrl;
      private String displayNamezhCN;


      public String getId() {
         return id;
      }

      public void setId(String id) {
         this.id = id;
      }

      public String getMaterialType() {
         return materialType;
      }

      public void setMaterialType(String materialType) {
         this.materialType = materialType;
      }

      public String getVersion() {
         return version;
      }

      public void setVersion(String version) {
         this.version = version;
      }

      public String getPackageUrl() {
         return packageUrl;
      }

      public void setPackageUrl(String packageUrl) {
         this.packageUrl = packageUrl;
      }

      public String getCoverUrl() {
         return coverUrl;
      }

      public void setCoverUrl(String coverUrl) {
         this.coverUrl = coverUrl;
      }

      public String getDisplayNamezhCN() {
         return displayNamezhCN;
      }

      public void setDisplayNamezhCN(String displayNamezhCN) {
         this.displayNamezhCN = displayNamezhCN;
      }

      public AssetInfo create() {
         AssetInfo assetInfo = new AssetInfo();
         assetInfo.setCoverPath(packageUrl);
         assetInfo.setType(AssetInfo.ASSET_PACKAGE_BACKGROUND);
         assetInfo.setDownloadUrl(packageUrl);
         assetInfo.setPackageId(getId());
         assetInfo.setName(displayNamezhCN);
         return assetInfo;
      }
   }

}
