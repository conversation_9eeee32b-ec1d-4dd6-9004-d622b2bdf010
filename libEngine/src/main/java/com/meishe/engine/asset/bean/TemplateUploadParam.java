package com.meishe.engine.asset.bean;

import java.io.File;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2021/3/25 17:03
 * @Description :上传模板用参数 Param for template uploading.
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class TemplateUploadParam {
    /**
     * 素材文件
     * The material file
     */
    public File materialFile;
    /**
     * 封面
     * The cover file
     */
    public File coverFile;
    /**
     * 预览视频文件
     * The preview video file
     */
    public File previewVideoFile;

    public String templateDescFilePath;
    /**
     * 自定义名称
     * The custom display name
     */
    public String customDisplayName;
    /**
     * 素材type
     * The material type
     */
    public int materialType = AssetsConstants.AssetsTypeData.TEMPLATE.type;

    /**
     * 素材描述
     * The description
     */
    public String description;
    /**
     * 素材中文描述
     * The chinese description
     */
    public String descriptinZhCn;

    /**
     * 是否通用 0：非通用 1：通用
     * The ratio flag 0：common 1：not common
     */
    public int ratioFlag;

    /**
     * 智能标签Id 以空格分隔 拼接而成的字符串(暂时后台未做校验)
     * The intel tags，The string composed of smart tag IDs separated by spaces
     * (No verification in the background temporarily)
     *
     */
    public int intelTags;

    @Override
    public String toString() {
        return "TemplateUploadParam{" +
                "materialFile=" + materialFile +
                ", coverFile=" + coverFile +
                ", previewVideoFile=" + previewVideoFile +
                ", templateDescFilePath='" + templateDescFilePath + '\'' +
                ", customDisplayName='" + customDisplayName + '\'' +
                ", materialType=" + materialType +
                ", description='" + description + '\'' +
                ", descriptinZhCn='" + descriptinZhCn + '\'' +
                ", ratioFlag=" + ratioFlag +
                ", intelTags=" + intelTags +
                '}';
    }
}
