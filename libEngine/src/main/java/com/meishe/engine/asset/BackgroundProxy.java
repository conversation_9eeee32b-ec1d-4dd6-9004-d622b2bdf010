package com.meishe.engine.asset;

import android.content.Context;
import android.text.TextUtils;

import com.meishe.engine.asset.bean.AssetList;
import com.meishe.engine.asset.bean.RequestParam;
import com.meishe.engine.bean.BaseInfo;
import com.meishe.libplugin.PluginManager;
import com.meishe.libplugin.user.IUserPlugin;
import com.meishe.net.custom.BaseResponse;
import com.meishe.net.custom.RequestCallback;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2021/3/22 16:01
 * @Description :背景资源管理代理类 The proxy about background assets manager
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class BackgroundProxy extends IAssetsManager {

    public BackgroundProxy() {
        setUserPlugin(PluginManager.get().getUserPlugin());
    }

    @Override
    public int[] getAspectRatio(float ratio) {
        int[] data = new int[2];
        data[0] = BaseInfo.AspectRatio_All;
        return data;
    }

    @Override
    public void getAssetsList(final String token, final RequestParam param, final int aspectRatio, final int ratioFlag, final int page, final int pageSize, final RequestCallback<AssetList> callback) {
        //从网络获取
        //Get assets from net.
        AssetsManager.get().getBackGroundAssetList(token, param,
                aspectRatio, page, pageSize, new RequestCallback<AssetList>() {
            @Override
            public void onSuccess(BaseResponse<AssetList> response) {
                if (response != null && response.getCode() == -2) {
                    loginAndRequest(token, param, aspectRatio, page, pageSize, callback);
                    return;
                }
                if (callback != null) {
                    callback.onSuccess(response);
                }
            }

            @Override
            public void onError(BaseResponse<AssetList> response) {
                if (callback != null) {
                    callback.onError(response);
                }
            }
        });
    }

    @Override
    void getLocalAssetList(RequestParam param, AssetCallback callback) {
        AssetsManager.get().getAssetsListFromDb(null, param, callback);
    }

    @Override
    public String getErrorMsg(Context context, int type) {
        return "";
    }


    private void loginAndRequest(final String oldToken, final RequestParam param, final int aspectRatio, final int page, final int pageSize, final RequestCallback<AssetList> callback) {
        IUserPlugin userPlugin = getUserPlugin();
        if (userPlugin != null) {
            userPlugin.login(new IUserPlugin.ILoginCallBack() {
                @Override
                public void onLoginSuccess(String token) {
                    if (!TextUtils.isEmpty(oldToken)) {
                        AssetsManager.get().updateUserAssetsInfo(oldToken, token);
                    }
                    AssetsManager.get().getBackGroundAssetList(token, param, aspectRatio, page, pageSize, callback);
                }

                @Override
                public void onLoginFailed(int code) {
                    if (callback != null) {
                        BaseResponse<AssetList> response = new BaseResponse<>();
                        response.setCode(code);
                        callback.onError(response);
                    }
                }
            });
        } else {
            if (callback != null) {
                BaseResponse<AssetList> response = new BaseResponse<>();
                callback.onError(response);
            }
        }
    }
}
