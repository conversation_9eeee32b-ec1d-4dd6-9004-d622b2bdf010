package com.meishe.engine.asset.bean;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR> <PERSON><PERSON> <PERSON><PERSON> guang on 2020/6/17 13:38Ed
 * 网络资源包的信息列表
 * A list of information for network resource bundles
 */

public class AssetList implements Serializable {
    public int type;
    public int total;
    public int pageNum;
    public int pageSize;
    public boolean hasNext;
    public ArrayList<NvAssetInfo> list;
    /**
     * 真实资源列表（网络数据源的转化）
     * List of real resources,Network data source transformation
     */
    public List<AssetInfo> realAssetList;
    public List<NvAssetInfo> elements;

    public class NvAssetInfo implements Serializable {
        public String id;
        public String uuid;
        public int category;
        public int kind;
        public String name;
        public String desc;
        public String tags;
        public int version;
        public int type;
        public String minAppVersion;
        public String packageUrl;
        public int packageSize;
        public String coverUrl;
        public int supportedAspectRatio;
        public String previewVideoUrl;
        public String packageRelativePath;
        public String infoUrl;
        public long templateTotalDuration;
        public String description;
        public String descriptionZhCn;

        /**
         * display Name if asset is custom
         * <P></>
         * 自定义名称名称
         */
        public String customDisplayName;
        /**
         * English name
         * <P></>
         * 英文名称
         */
        public String displayName;
        /**
         * Chinese name
         * <p></>
         * 中文名称
         */
        public String displayNameZhCn;

        /**
         * Indicates Whether the asset is authorized.
         * <p></>
         * 是否已经授权
         */
        public boolean authed;
        /**
         * ratio flag 0: uncommon 1: common
         * <p></>
         * 比例flag
         */
        public int ratioFlag;

        /**
         * Possessor.
         * If it is 1, it is the current owner.
         * <p>
         * 拥有者， 如果是1，是当前拥有者
         */
        public int possessor;
        /**
         * 是 post package
         * is Post package
         */
        public int isPostPackage = 1;

        /**
         * duration
         * <p>
         * 时长
         */
        public long duration;

        /**
         * Default aspect ratio
         * <p>
         * 默认支持的比例
         */
        public int defaultAspectRatio = 1;

        /**
         * User info
         * <p>
         * 用户信息
         */
        public UserInfo userInfo;

        /**
         * Interactive Result data
         * <p>
         * 交互信息
         */
        public InteractiveResultDto queryInteractiveResultDto;

        /**
         * the download amount
         * <p></>
         * 下载量
         */
        public int downloadAmount;
        /**
         * 授权路径
         * <p></>
         * The lic path
         */
        private String licPath;

        public String getLicPath() {
            return licPath;
        }

        public void setLicPath(String licPath) {
            this.licPath = licPath;
        }

        @Override
        public String toString() {
            return "NvAssetInfo{" +
                    "id='" + id + '\'' +
                    ", category=" + category +
                    ", kind=" + kind +
                    ", name='" + name + '\'' +
                    ", desc='" + desc + '\'' +
                    ", tags='" + tags + '\'' +
                    ", version=" + version +
                    ", type=" + type +
                    ", minAppVersion='" + minAppVersion + '\'' +
                    ", packageUrl='" + packageUrl + '\'' +
                    ", packageSize=" + packageSize +
                    ", coverUrl='" + coverUrl + '\'' +
                    ", supportedAspectRatio=" + supportedAspectRatio +
                    ", previewVideoUrl='" + previewVideoUrl + '\'' +
                    ", packageRelativePath='" + packageRelativePath + '\'' +
                    ", infoUrl='" + infoUrl + '\'' +
                    ", templateTotalDuration=" + templateTotalDuration +
                    ", description='" + description + '\'' +
                    ", descriptionZhCn='" + descriptionZhCn + '\'' +
                    ", customDisplayName='" + customDisplayName + '\'' +
                    ", displayName='" + displayName + '\'' +
                    ", displayNameZhCn='" + displayNameZhCn + '\'' +
                    ", authed=" + authed +
                    ", ratioFlag=" + ratioFlag +
                    ", possessor=" + possessor +
                    '}';
        }
    }

    public static class UserInfo implements Serializable{
        public String nickname;
        public String iconUrl;
    }


    public static class InteractiveResultDto implements Serializable {
        public int likeNum;
        public int useNum;
        public String materialId;
    }

    @Override
    public String toString() {
        return "AssetList{" +
                "type=" + type +
                ", total=" + total +
                ", hasNext=" + hasNext +
                ", list=" + list +
                ", realAssetList=" + realAssetList +
                ", elements=" + elements +
                '}';
    }
}
