package com.meishe.engine.command;


import com.meishe.annotation.Undo;
import com.meishe.engine.EditorEngine;
import com.meishe.engine.bean.MeicamTimeline;
import com.meishe.engine.bean.MeicamWaterMark;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/9/7 19:09
 * @Description :水印命令 The water mark command
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class WaterMarkCommand {
    private static final String TAG = "waterMark";
    public static final int PARAM_FILE_PATH = 1;
    public static final int PARAM_MARGIN_X = 2;
    public static final int PARAM_MARGIN_Y = 3;
    public static final int PARAM_WIDTH = 4;
    public static final int PARAM_HEIGHT = 5;

    @Undo(className ="WaterMarkSetList", function = "setParam",
            param = {"int|paramType", "Object|param", "boolean|needSaveData"})
    public static void setParam(MeicamWaterMark waterMark, int paramType, Object param, boolean... needSaveOperate){
        Object oldParam = null;
        switch (paramType) {
            case PARAM_FILE_PATH:
                oldParam = waterMark.getWatermarkFilePath();
                waterMark.setWatermarkFilePath((String) param);
                break;
            case PARAM_MARGIN_X:
                oldParam = waterMark.getMarginX();
                waterMark.setMarginX((Integer) param);
                break;
            case PARAM_MARGIN_Y:
                oldParam = waterMark.getMarginY();
                waterMark.setMarginY((Integer) param);
                break;
            case PARAM_WIDTH:
                oldParam = waterMark.getDisplayWidth();
                waterMark.setDisplayWidth((Integer) param);
                break;
            case PARAM_HEIGHT:
                oldParam = waterMark.getDisplayHeight();
                waterMark.setDisplayHeight((Integer) param);
                break;
            default:
                break;
        }
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(waterMark);
            String tagAndKey = tag + paramType;
            Object[] unDoParam = new Object[]{paramType, oldParam, new boolean[]{false}};
            Object[] redoParam = new Object[]{paramType, param};
            CommandUtil.saveOperate("WaterMarkSetList", unDoParam, redoParam, tag, tagAndKey);
        }
    }

    private static String getTag(MeicamWaterMark waterMark) {
        return TAG;
    }

    public static MeicamWaterMark getItByTag(String tag){
        MeicamTimeline currentTimeline = EditorEngine.getInstance().getCurrentTimeline();
        return currentTimeline.getMeicamWaterMark();
    }
}
