package com.meishe.engine.command;


import static com.meishe.engine.bean.CommonData.VIDEO_FX_AR_SCENE;
import static com.meishe.engine.bean.MeicamVideoFx.SubType.SUB_TYPE_CLIP_FILTER;
import static com.meishe.engine.bean.MeicamVideoFx.SubType.SUB_TYPE_TIMELINE_FILTER;

import androidx.annotation.NonNull;

import com.meishe.annotation.Undo;
import com.meishe.base.utils.LogUtils;
import com.meishe.engine.EditorEngine;
import com.meishe.engine.bean.MeicamAudioFx;
import com.meishe.engine.bean.MeicamMaskRegionInfo;
import com.meishe.engine.bean.MeicamTimeline;
import com.meishe.engine.bean.MeicamVideoClip;
import com.meishe.engine.bean.MeicamVideoFx;
import com.meishe.engine.bean.MeicamVideoTrack;
import com.meishe.engine.bean.NvMaskModel;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/8/12 15:45
 * @Description :videoClip命令 The command of the videoClip
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class VideoClipCommand {
    private static final String TAG = "videoClip";

    /**
     * Sets speed.
     * 设置速度
     *
     * @param videoClip        the video clip
     * @param speed            the speed 速度
     * @param isKeepAudioPitch the is keep audio pitch 是否保持音调不变
     * @param needSaveData     the need save data operate 是否保存数据
     */
    @Undo(className = "SetSpeedCommand", function = "setSpeed", param = {"double|speed", "boolean|isKeepAudioPitch", " boolean...|needSaveData"})
    public static void setSpeed(MeicamVideoClip videoClip, double speed, boolean isKeepAudioPitch, boolean... needSaveData) {
        if (CommandUtil.needSaveOperate(needSaveData)) {
            String tag = getTag(videoClip);
            SetSpeedCommand command = CommandManager.getInstance().getCommand(tag, SetSpeedCommand.class);
            if (command == null) {
                command = new SetSpeedCommand(tag, new SetSpeedCommand.UndoParam(videoClip.getSpeed(), isKeepAudioPitch, false));
                CommandManager.getInstance().putCommand(tag, command);
            }
            command.setRedoParam(new SetSpeedCommand.RedoParam(speed, isKeepAudioPitch));
        }
        videoClip.setCurveSpeed("");
        videoClip.setCurveSpeedName("");
        videoClip.setSpeed(speed, isKeepAudioPitch);
        if (!CommandUtil.needSaveOperate(needSaveData)) {
            MeicamTimeline currentTimeline = EditorEngine.getInstance().getCurrentTimeline();
            MeicamVideoTrack videoTrack = currentTimeline.getVideoTrack(videoClip.getTrackIndex());
            int clipCount = videoTrack.getClipCount();
            for (int index = 0; index < clipCount; index++) {
                videoTrack.getVideoClip(index).updateInAndOutPoint();
            }
        } else {
            videoClip.updateInAndOutPoint();
        }
    }

    /**
     * Sets curve speed.
     * 设置曲线变速
     *
     * @param videoClip       the video clip
     * @param curveSpeed      the curve speed 速度曲线
     * @param name            the name 曲线名字
     * @param needSaveOperate the need save data operate 是否保存操作
     */
    @Undo(className = "SetCurveSpeedCommand", function = "setCurveSpeed",
            param = {"String|curveSpeed", "String|name", " boolean...|needSaveOperate"})
    public static void setCurveSpeed(MeicamVideoClip videoClip, String curveSpeed, String name, boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(videoClip);
            SetCurveSpeedCommand command = CommandManager.getInstance().getCommand(tag, SetCurveSpeedCommand.class);
            if (command == null) {
                command = new SetCurveSpeedCommand(tag, new SetCurveSpeedCommand.UndoParam(videoClip.getCurveSpeed(),
                        videoClip.getCurveSpeedName(), false));
                CommandManager.getInstance().putCommand(tag, command);
            }
            command.setRedoParam(new SetCurveSpeedCommand.RedoParam(curveSpeed, name));
        }
        videoClip.setCurveSpeedName(name);
        videoClip.setCurveSpeed(curveSpeed);
        if (!CommandUtil.needSaveOperate(needSaveOperate)) {
            MeicamTimeline currentTimeline = EditorEngine.getInstance().getCurrentTimeline();
            MeicamVideoTrack videoTrack = currentTimeline.getVideoTrack(videoClip.getTrackIndex());
            int clipCount = videoTrack.getClipCount();
            for (int index = 0; index < clipCount; index++) {
                videoTrack.getVideoClip(index).updateInAndOutPoint();
            }
        } else {
            videoClip.updateInAndOutPoint();
        }
    }

    @Undo(className = "InAndOutPointCommand", function = "updateInAndOutPoint",
            param = {"boolean...|needSaveData"})
    public static void updateInAndOutPoint(MeicamVideoClip videoClip, boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(videoClip);
            InAndOutPointCommand command = CommandManager.getInstance().getCommand(tag, InAndOutPointCommand.class);
            if (command == null) {
                command = new InAndOutPointCommand(tag, new InAndOutPointCommand.UndoParam(false));
                CommandManager.getInstance().putCommand(tag, command);
            }
            command.setRedoParam(new InAndOutPointCommand.RedoParam());
        }
        videoClip.updateInAndOutPoint();
    }

    /**
     * Zoom key frame.
     * 缩放关键帧
     *
     * @param videoClip       the video clip
     * @param scaleFactor     the scale factor 缩放参数
     * @param needSaveOperate the need save data operate 是否需要保存操作
     */
    @Undo(className = "ZoomKeyFrameCommand", function = "zoomKeyFrame",
            param = {"double|scaleFactor", "boolean...|needSaveOperate"})
    public static void zoomKeyFrame(MeicamVideoClip videoClip, double scaleFactor, boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(videoClip);
            ZoomKeyFrameCommand command = CommandManager.getInstance().getCommand(tag, ZoomKeyFrameCommand.class);
            if (command == null) {
                command = new ZoomKeyFrameCommand(tag, new ZoomKeyFrameCommand.UndoParam(1F / scaleFactor, false));
                CommandManager.getInstance().putCommand(tag, command);
            }
            command.setRedoParam(new ZoomKeyFrameCommand.RedoParam(scaleFactor));
        }
        int videoFxCount = videoClip.getVideoFxCount();
        for (int index = 0; index < videoFxCount; index++) {
            MeicamVideoFx videoFx = videoClip.getVideoFx(index);
            videoFx.keyFrameProcessor().zoomKeyFrame(scaleFactor);
        }
    }

    /**
     * Sets volume.
     * 设置音量
     *
     * @param videoClip       the video clip
     * @param volume          the volume 音量
     * @param needSaveOperate the need save data operate 是否保存数据
     */
    @Undo(className = "SetVolumeCommand", function = "setVolume", param = {"float|volume", " boolean|needSaveOperate"})
    public static void setVolume(MeicamVideoClip videoClip, float volume, boolean needSaveOperate) {
        if (needSaveOperate) {
            String tag = getTag(videoClip);
            SetVolumeCommand command = CommandManager.getInstance().getCommand(tag, SetVolumeCommand.class);
            if (command == null) {
                command = new SetVolumeCommand(tag, new SetVolumeCommand.UndoParam(videoClip.getVolume(), false));
                CommandManager.getInstance().putCommand(tag, command);
            }
            command.setRedoParam(new SetVolumeCommand.RedoParam(volume));
        }
        videoClip.setVolume(volume);
    }

    @Undo(className = "TrimInCommand", function = "changeTrimInAndOut", param = {"long|trimIn", "long|trimOut", " boolean|affectSibling", "boolean...|needSaveOperate"})
    public static void changeTrimInAndOut(MeicamVideoClip videoClip, long trimIn, long trimOut, boolean affectSibling, boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(videoClip);
            TrimInCommand command = CommandManager.getInstance().getCommand(tag, TrimInCommand.class);
            if (command == null) {
                command = new TrimInCommand(tag, new TrimInCommand.UndoParam(videoClip.getTrimIn(), videoClip.getTrimOut(), affectSibling, false));
                CommandManager.getInstance().putCommand(tag, command);
            }
            command.setRedoParam(new TrimInCommand.RedoParam(trimIn, trimOut, affectSibling));
        }
        videoClip.setTrimIn(trimIn, affectSibling);
        videoClip.setTrimOut(trimOut, affectSibling);

        if (!CommandUtil.needSaveOperate(needSaveOperate)) {
            MeicamTimeline currentTimeline = EditorEngine.getInstance().getCurrentTimeline();
            MeicamVideoTrack videoTrack = currentTimeline.getVideoTrack(videoClip.getTrackIndex());
            int clipCount = videoTrack.getClipCount();
            for (int index = 0; index < clipCount; index++) {
                videoTrack.getVideoClip(index).updateInAndOutPoint();
            }
        } else {
            videoClip.updateInAndOutPoint();
        }
    }

    @Undo(className = "AppendFxCommand", function = "removeFx",
            param = {"String|buildType", "String|subType", "String|fxId", "int|index", "boolean...|needSaveOperate"})
    public static MeicamVideoFx appendFx(MeicamVideoClip videoClip, String buildType, String subType,
                                         String fxId, boolean... needSaveOperate) {
        MeicamVideoFx videoFx = videoClip.appendFx(buildType, subType, fxId);
        if (videoFx != null) {
            if (CommandUtil.needSaveOperate(needSaveOperate)) {
                String tag = getTag(videoClip);
                String tagAndKey = tag + buildType + subType + fxId;
                Object[] unDoParam = new Object[]{buildType, subType, fxId, videoFx.getIndex(), new boolean[]{false}};
                Object[] redoParam = new Object[]{buildType, subType, fxId};
                CommandUtil.saveOperate("AppendFxCommand", unDoParam, redoParam, tag, tagAndKey);
            }
            videoFx.setExtraTag(getExtraTag(videoClip));
        }
        return videoFx;
    }


    @Undo(className = "RemoveFxCommand", function = "appendFx",
            param = {"String|buildType", "String|subType", "String|fxId", "boolean...|needSaveOperate"})
    public static void removeFx(MeicamVideoClip videoClip, String buildType, String subType, String fxId, int index, boolean... needSaveOperate) {
        MeicamVideoFx videoFx = videoClip.getVideoFx(buildType, subType, fxId, index);
        if (videoFx != null) {
            videoClip.removeVideoFx(videoFx);
            if (CommandUtil.needSaveOperate(needSaveOperate)) {
                String tag = getTag(videoClip);
                String tagAndKey = tag + buildType + subType + fxId + videoFx.getIndex();
                Object[] unDoParam = new Object[]{buildType, subType, fxId, new boolean[]{false}};
                Object[] redoParam = new Object[]{buildType, subType, fxId, videoFx.getIndex()};
                CommandUtil.saveOperate("RemoveFxCommand", unDoParam, redoParam, tag, tagAndKey);
            }
        }
    }

    @Undo(className = "RemoveFxCommand1", function = "appendFx", param = {"MeicamVideoFx|videoFx", "boolean|deleteSameType", "boolean...|needSaveOperate"})
    public static MeicamVideoFx removeFx(MeicamVideoClip videoClip, MeicamVideoFx videoFx, boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(videoClip);
            String tagAndKey = tag + videoFx.getType() + videoFx.getSubType() + videoFx.getDesc() + videoFx.getIndex();
            Object[] unDoParam = new Object[]{videoFx.clone(), false, new boolean[]{false}};
            Object[] redoParam = new Object[]{videoFx};
            CommandUtil.saveOperate("RemoveFxCommand1", unDoParam, redoParam, tag, tagAndKey);
        }
        return videoClip.removeVideoFx(videoFx);
    }

    @Undo(className = "AppendFxCommandExt", function = "removeFx", param = {"MeicamVideoFx|videoFx", "boolean...|needSaveOperate"})
    public static MeicamVideoFx appendFx(MeicamVideoClip videoClip, MeicamVideoFx videoFx, boolean deleteSameType, boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(videoClip);
            String tagAndKey = tag + videoFx.getType() + videoFx.getSubType() + videoFx.getDesc() + videoFx.getIndex();
            Object[] unDoParam = new Object[]{videoFx, new boolean[]{false}};
            Object[] redoParam = new Object[]{videoFx, deleteSameType};
            CommandUtil.saveOperate("AppendFxCommandExt", unDoParam, redoParam, tag, tagAndKey);
        }
        MeicamVideoFx newVideoFx = videoClip.appendVideoFxFromFx(videoFx, deleteSameType);
        if (newVideoFx != null) {
            newVideoFx.setExtraTag(getExtraTag(videoClip));
        }
        return newVideoFx;
    }

    @Undo(className = "AppendFxCommandExtDeleteOrNot", function = "removeFx", param = {"MeicamVideoFx|videoFx", "boolean...|needSaveOperate"})
    public static MeicamVideoFx appendFx(MeicamVideoClip videoClip, boolean deleteSameType, MeicamVideoFx videoFx, boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(videoClip);
            String tagAndKey = tag + videoFx.getType() + videoFx.getSubType() + videoFx.getDesc();
            Object[] unDoParam = new Object[]{videoFx,  new boolean[]{false}};
            Object[] redoParam = new Object[]{deleteSameType, videoFx};
            CommandUtil.saveOperate("AppendFxCommandExtDeleteOrNot", unDoParam, redoParam, tag, tagAndKey);
        }
        MeicamVideoFx newVideoFx = videoClip.appendVideoFxFromFx(videoFx, deleteSameType);
        if (newVideoFx != null) {
            newVideoFx.setExtraTag(getExtraTag(videoClip));
        }
        return newVideoFx;
    }

    @Undo(className = "InsertFxCommandExt", function = "removeFx", param = {"MeicamVideoFx|videoFx", "boolean...|needSaveOperate"})
    public static MeicamVideoFx insertVideoFxFromFx(MeicamVideoClip videoClip, MeicamVideoFx videoFx, int index, boolean deleteSameType, boolean... needSaveOperate) {
        MeicamVideoFx newVideoFx = videoClip.insertVideoFxFromFx(videoFx, index, deleteSameType);
        if (newVideoFx != null) {
            newVideoFx.setExtraTag(getExtraTag(videoClip));
            if (CommandUtil.needSaveOperate(needSaveOperate)) {
                String tag = getTag(videoClip);
                String tagAndKey = tag + videoFx.getType() + videoFx.getSubType() + videoFx.getDesc() + newVideoFx.getIndex();
                Object[] unDoParam = new Object[]{newVideoFx, new boolean[]{false}};
                Object[] redoParam = new Object[]{videoFx, index, deleteSameType};
                CommandUtil.saveOperate("InsertFxCommandExt", unDoParam, redoParam, tag, tagAndKey);
            }
        }
        return newVideoFx;
    }

    @Undo(className = "AppendFilterCommand", function = "appendFilter", param = {"MeicamVideoFx|videoFx", "boolean|isTimeline", "boolean...|needSaveOperate"})
    public static MeicamVideoFx appendFilter(MeicamVideoClip videoClip, MeicamVideoFx videoFx, boolean isTimeline, boolean... needSaveOperate) {
        MeicamVideoFx oldVideoFx = videoClip.getVideoFxByType(isTimeline ? SUB_TYPE_TIMELINE_FILTER : SUB_TYPE_CLIP_FILTER);
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(videoClip);
            Object[] redoParam = new Object[]{videoFx.clone(), isTimeline};
            if (oldVideoFx == null) {
                oldVideoFx = videoFx.clone();
                oldVideoFx.setDesc("");
            } else {
                oldVideoFx = oldVideoFx.clone();
            }
            Object[] unDoParam = new Object[]{oldVideoFx, isTimeline, new boolean[]{false}};
            CommandUtil.saveOperate("AppendFilterCommand", unDoParam, redoParam, tag, tag);
        }
        MeicamVideoFx newVideoFx = videoClip.appendFilterFromFx(videoFx, false);
        if (newVideoFx != null) {
            newVideoFx.setExtraTag(getExtraTag(videoClip));
        }
        return newVideoFx;
    }

    @Undo(className = "SetPropCommand", function = "setProp", param = {"String|assetsPath", "String|packageId", "boolean...|needSaveOperate"})
    public static void setProp(MeicamVideoClip videoClip, String assetsPath, String packageId, boolean... needSaveOperate) {
        if (videoClip == null) {
            LogUtils.e("videoClip is null !");
            return;
        }
        String propId = videoClip.getPropId();
        String propPath = videoClip.getPropPath();

        videoClip.setProp(assetsPath, packageId);
        MeicamVideoFx arSceneFxEx = videoClip.getArSceneFxEx();
        if (arSceneFxEx != null) {
            arSceneFxEx.setExtraTag(getExtraTag(videoClip));
        }
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(videoClip);
            Object[] unDoParam = new Object[]{propPath, propId, new boolean[]{false}};
            Object[] redoParam = new Object[]{assetsPath, packageId,};
            CommandUtil.saveOperate("SetPropCommand", unDoParam, redoParam, tag, tag);
        }
    }

    @Undo(className = "AppendFilterCommandExt", function = "appendFilter",
            param = {"String|buildType", "String|fxId", "boolean|isTimeline", "boolean...|needSaveOperate"})
    public static MeicamVideoFx appendFilter(MeicamVideoClip videoClip,
                                             String buildType, String fxId, boolean isTimeline, boolean... needSaveOperate) {
        MeicamVideoFx oldVideoFx = videoClip.getVideoFxByType(isTimeline ? SUB_TYPE_TIMELINE_FILTER : SUB_TYPE_CLIP_FILTER);
        MeicamVideoFx videoFx = videoClip.appendFilter(buildType, fxId, isTimeline);
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(videoClip);
            Object[] redoParam = new Object[]{buildType, fxId, isTimeline};
            if (oldVideoFx == null) {
                fxId = "";
            } else {
                buildType = oldVideoFx.getType();
                fxId = oldVideoFx.getDesc();
            }
            Object[] unDoParam = new Object[]{buildType, fxId, isTimeline, new boolean[]{false}};
            CommandUtil.saveOperate("AppendFilterCommandExt", unDoParam, redoParam, tag, tag);
        }
        if (videoFx != null) {
            videoFx.setExtraTag(getExtraTag(videoClip));
        }
        return videoFx;
    }


    @Undo(className = "OpenShapeCommand", function = "closeShapeFx", param = {"boolean...|needSaveOperate"})
    public static boolean openShapeFx(MeicamVideoClip videoClip, boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(videoClip);
            OpenShapeCommand command = CommandManager.getInstance().getCommand(tag, OpenShapeCommand.class);
            if (command == null) {
                command = new OpenShapeCommand(tag,
                        new OpenShapeCommand.UndoParam(false));
                CommandManager.getInstance().putCommand(tag, command);
            }
            command.setRedoParam(new OpenShapeCommand.RedoParam());
        }

        boolean result = videoClip.openShapeFx();
        MeicamVideoFx arSceneFxEx = videoClip.getArSceneFxEx();
        if (arSceneFxEx != null) {
            arSceneFxEx.setExtraTag(getExtraTag(videoClip));
        }
        return result;
    }

    @Undo(className = "CloseShapeCommand", function = "openShapeFx", param = {"boolean...|needSaveOperate"})
    public static boolean closeShapeFx(MeicamVideoClip videoClip, boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(videoClip);
            CloseShapeCommand command = CommandManager.getInstance().getCommand(tag, CloseShapeCommand.class);
            if (command == null) {
                command = new CloseShapeCommand(tag,
                        new CloseShapeCommand.UndoParam(false));
                CommandManager.getInstance().putCommand(tag, command);
            }
            command.setRedoParam(new CloseShapeCommand.RedoParam());
        }
        boolean result = videoClip.closeShapeFx();
        MeicamVideoFx arSceneFxEx = videoClip.getArSceneFxEx();
        if (arSceneFxEx != null) {
            arSceneFxEx.setExtraTag(getExtraTag(videoClip));
        }
        return result;
    }

    @Undo(className = "ResetBeautyShapeCommand", function = "restoreBeautyShape", param = {"MeicamVideoFx|videoFx"})
    public static void resetBeautyOrShapeFx(MeicamVideoClip videoClip, boolean isShape, boolean... needSaveOperate) {
        MeicamVideoFx arSceneFxEx = videoClip.getVideoFxById(VIDEO_FX_AR_SCENE);
        if (arSceneFxEx == null) {
            return;
        }
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(videoClip);
            ResetBeautyShapeCommand command = CommandManager.getInstance().getCommand(tag, ResetBeautyShapeCommand.class);
            if (command == null) {
                command = new ResetBeautyShapeCommand(tag,
                        new ResetBeautyShapeCommand.UndoParam(arSceneFxEx.clone()));
                CommandManager.getInstance().putCommand(tag, command);
            }
            command.setRedoParam(new ResetBeautyShapeCommand.RedoParam(isShape));
        }
        if (isShape) {
            videoClip.resetShapeFx();
        } else {
            videoClip.resetBeautyFx();
        }
        arSceneFxEx.setExtraTag(getExtraTag(videoClip));
    }

    static void restoreBeautyShape(MeicamVideoClip videoClip, MeicamVideoFx videoFx) {
        MeicamVideoFx arSceneFxEx = videoClip.getVideoFxById(VIDEO_FX_AR_SCENE);
        if (arSceneFxEx == null) {
            return;
        }
        arSceneFxEx.copyParam(videoFx);
        arSceneFxEx.setExtraTag(getExtraTag(videoClip));
    }

    @Undo(className = "SetBeautyParamCommand", function = "applyBeautyVideoFx", param = {"String|key", "float|value", "boolean...|needSaveOperate"})
    public static boolean applyBeautyVideoFx(MeicamVideoClip videoClip, String key, float value, boolean... needSaveOperate) {
        MeicamVideoFx arSceneFxEx = videoClip.getVideoFxById(VIDEO_FX_AR_SCENE);
        if (arSceneFxEx == null) {
            return false;
        }
        arSceneFxEx.setExtraTag(getExtraTag(videoClip));
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(videoClip);
            String keyAndTag = tag + key;
            SetBeautyParamCommand command = CommandManager.getInstance().getCommand(keyAndTag, SetBeautyParamCommand.class);
            if (command == null) {
                command = new SetBeautyParamCommand(tag,
                        new SetBeautyParamCommand.UndoParam(key, arSceneFxEx.getFloatVal(key), false));
                CommandManager.getInstance().putCommand(keyAndTag, command);
            }
            SetBeautyParamCommand.RedoParam redoParam = command.getRedoParam();
            if (redoParam != null) {
                redoParam.setKey(key);
                redoParam.setValue(value);
            } else {
                command.setRedoParam(new SetBeautyParamCommand.RedoParam(key, value));
            }
        }
        return videoClip.setBeautyAndShapeFxParam(key, value);
    }

    @Undo(className = "SetShapeParamCommand", function = "applyShapeVideoFx",
            param = {"String|faceID", "String|packageID", "String|degreeName", "float|value", "boolean...|needSaveOperate"})
    public static boolean applyShapeVideoFx(MeicamVideoClip videoClip,
                                            String faceID, String packageID, String degreeName, float value, boolean... needSaveOperate) {
        MeicamVideoFx arSceneFxEx = videoClip.getVideoFxById(VIDEO_FX_AR_SCENE);
        if (arSceneFxEx == null) {
            return false;
        }

        arSceneFxEx.setExtraTag(getExtraTag(videoClip));
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(videoClip);
            String keyAndTag = tag + faceID + degreeName;
            SetShapeParamCommand command = CommandManager.getInstance().getCommand(keyAndTag, SetShapeParamCommand.class);
            if (command == null) {
                command = new SetShapeParamCommand(tag,
                        new SetShapeParamCommand.UndoParam(faceID, arSceneFxEx.getStringVal(faceID), degreeName, arSceneFxEx.getFloatVal(degreeName), false));
                CommandManager.getInstance().putCommand(keyAndTag, command);
            }
            SetShapeParamCommand.RedoParam redoParam = command.getRedoParam();
            if (redoParam != null) {
                redoParam.setFaceID(faceID);
                redoParam.setPackageID(packageID);
                redoParam.setDegreeName(degreeName);
                redoParam.setValue(value);
            } else {
                command.setRedoParam(new SetShapeParamCommand.RedoParam(faceID, packageID, degreeName, value));
            }
        }
        return videoClip.applyShapeFx(faceID, packageID, degreeName, value);
    }

    @Undo(className = "SetOpacityCommand", function = "setOpacity",
            param = {"float|opacity", "boolean...|needSaveOperate"})
    public static void setOpacity(MeicamVideoClip videoClip, float opacity, boolean... needSaveOperate) {
        MeicamVideoFx videoFx = videoClip.findPropertyVideoFx();
        if (videoFx == null) {
            return;
        }
        videoFx.setExtraTag(getExtraTag(videoClip));
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(videoClip);
            SetOpacityCommand command = CommandManager.getInstance().getCommand(tag, SetOpacityCommand.class);
            if (command == null) {
                command = new SetOpacityCommand(tag,
                        new SetOpacityCommand.UndoParam(videoClip.getOpacity(), false));
                CommandManager.getInstance().putCommand(tag, command);
            }
            SetOpacityCommand.RedoParam redoParam = command.getRedoParam();
            if (redoParam != null) {
                redoParam.setOpacity(opacity);
            } else {
                command.setRedoParam(new SetOpacityCommand.RedoParam(opacity));
            }
        }
        videoClip.setOpacity(opacity);
    }

    @Undo(className = "AppendAudioFxCommand", function = "removeAudioFx",
            param = {"int|index", "boolean...|needSaveOperate"})
    public static MeicamAudioFx appendAudioFx(MeicamVideoClip videoClip, String fxId, boolean... needSaveOperate) {
        MeicamAudioFx meicamAudioFx = videoClip.appendAudioFx(fxId);
        if (meicamAudioFx == null) {
            return null;
        }
        meicamAudioFx.setExtraTag(getExtraTag(videoClip));
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(videoClip);
            AppendAudioFxCommand command = CommandManager.getInstance().getCommand(tag, AppendAudioFxCommand.class);
            if (command == null) {
                command = new AppendAudioFxCommand(tag,
                        new AppendAudioFxCommand.UndoParam(meicamAudioFx.getIndex(), false));
                CommandManager.getInstance().putCommand(tag, command);
            }
            AppendAudioFxCommand.RedoParam redoParam = command.getRedoParam();
            if (redoParam != null) {
                redoParam.setFxId(fxId);
            } else {
                command.setRedoParam(new AppendAudioFxCommand.RedoParam(fxId));
            }
        }
        return meicamAudioFx;
    }

    @Undo(className = "RemoveAudioFxCommand", function = "appendAudioFx",
            param = {"String|fxId", "boolean...|needSaveOperate"})
    public static void removeAudioFx(MeicamVideoClip videoClip, int index, boolean... needSaveOperate) {
        MeicamAudioFx meicamAudioFx = videoClip.removeAudioFx(index);
        if (meicamAudioFx == null) {
            return;
        }
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(videoClip);
            RemoveAudioFxCommand command = CommandManager.getInstance().getCommand(tag, RemoveAudioFxCommand.class);
            if (command == null) {
                command = new RemoveAudioFxCommand(tag,
                        new RemoveAudioFxCommand.UndoParam(meicamAudioFx.getDesc(), false));
                CommandManager.getInstance().putCommand(tag, command);
            }
            RemoveAudioFxCommand.RedoParam redoParam = command.getRedoParam();
            if (redoParam != null) {
                redoParam.setIndex(index);
            } else {
                command.setRedoParam(new RemoveAudioFxCommand.RedoParam(index));
            }
        }
    }

    public static final int PARAM_ORG_DURATION = 1;
    public static final int PARAM_ORG_WIDTH = 2;
    public static final int PARAM_ORG_HEIGHT = 3;
    public static final int PARAM_BLENDING_MODE = 4;
    public static final int PARAM_FILE_PATH = 5;
    public static final int PARAM_MOVE_TRIM = 6;
    public static final int PARAM_FADE_IN_DURATION = 7;
    public static final int PARAM_FADE_OUT_DURATION = 8;
    public static final int PARAM_REVERSE_FILE_PATH = 9;
    public static final int PARAM_VIDEO_TYPE = 10;
    public static final int PARAM_VIDEO_THUMBNAIL_INFO = 11;
    public static final int PARAM_VIDEO_CONVERT_SUCCESS = 12;
    public static final int PARAM_VIDEO_IS_REVERSE = 13;
    public static final int PARAM_ENABLE_RAW_SOURCE_MODE = 14;

    public static final String MASK_KEY_LAST_SCALE = "lastScale";
    public static final String MASK_KEY_LAST_VIEW_SCALE = "viewScale";
    public static final String MASK_KEY_REGION = "region";

    @Undo(className = "VideoSetParamCommand", function = "setParam",
            param = {"int|paramType", "Object|param", "boolean...|needSaveOperate"})
    public static void setParam(MeicamVideoClip videoClip, int paramType, Object param, boolean... needSaveOperate) {

        Object oldParam = null;
        try {
            switch (paramType) {
                case PARAM_ORG_DURATION:
                    oldParam = videoClip.getOrgDuration();
                    videoClip.setOrgDuration((Long) param);
                    break;
                case PARAM_ORG_WIDTH:
                    oldParam = videoClip.getOriginalWidth();
                    videoClip.setOriginalWidth((Integer) param);
                    break;
                case PARAM_ORG_HEIGHT:
                    oldParam = videoClip.getOriginalHeight();
                    videoClip.setOriginalHeight((Integer) param);
                    break;
                case PARAM_BLENDING_MODE:
                    oldParam = videoClip.getBlendingMode();
                    videoClip.setBlendingMode((Integer) param);
                    break;
                case PARAM_FILE_PATH:
                    oldParam = videoClip.getFilePath();
                    videoClip.changeFilePath((String) param);
                    break;
                case PARAM_MOVE_TRIM:
                    oldParam = videoClip.getTrimOffSet();
                    videoClip.moveTrimPoint((Long) param);
                    break;
                case PARAM_FADE_IN_DURATION:
                    oldParam = videoClip.getFadeInDuration();
                    videoClip.setFadeInDuration((Long) param);
                    break;
                case PARAM_FADE_OUT_DURATION:
                    oldParam = videoClip.getFadeOutDuration();
                    videoClip.setFadeOutDuration((Long) param);
                    break;
                case PARAM_REVERSE_FILE_PATH:
                    oldParam = videoClip.getReverseFilePath();
                    String path = (String) param;
                    videoClip.setReverseFilePath(path);
                    break;
                case PARAM_VIDEO_TYPE:
                    oldParam = videoClip.getVideoType();
                    videoClip.setVideoType((String) param);
                    break;
                case PARAM_VIDEO_THUMBNAIL_INFO:
                    oldParam = videoClip.getThumbNailInfo();
                    videoClip.setThumbNailInfo(param == null ? null : (MeicamVideoClip.ThumbNailInfo) param);
                    break;
                case PARAM_VIDEO_CONVERT_SUCCESS:
                    oldParam = videoClip.isConvertSuccess();
                    videoClip.setConvertSuccess((Boolean) param);
                    break;
                case PARAM_VIDEO_IS_REVERSE:
                    oldParam = videoClip.getVideoReverse();
                    videoClip.setVideoReverse((Boolean) param);
                    break;
                case PARAM_ENABLE_RAW_SOURCE_MODE:
                    oldParam = videoClip.isEnableRawSourceMode();
                    videoClip.enableRawSourceMode((Boolean) param);
                    break;
                default:
                    break;
            }
            if (CommandUtil.needSaveOperate(needSaveOperate)) {
                String tag = getTag(videoClip);
                String tagAndKey = getTag(videoClip) + paramType;
                Object[] unDoParam = new Object[]{paramType, oldParam, new boolean[]{false}};
                Object[] redoParam = new Object[]{paramType, param};
                CommandUtil.saveOperate("VideoSetParamCommand", unDoParam, redoParam, tag, tagAndKey);
            }
        } catch (Exception e) {
            LogUtils.e(e);
        }
    }


    @Undo(className = "VideoSetTrimOutCommand", function = "setTrimOut",
            param = {"long|trimOut", "boolean|affectSibling", "boolean...|needSaveOperate"})
    public static void setTrimOut(MeicamVideoClip videoClip, long trimOut, boolean affectSibling, boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(videoClip);
            Object[] unDoParam = new Object[]{videoClip.getTrimOut(), affectSibling, new boolean[]{false}};
            Object[] redoParam = new Object[]{trimOut, affectSibling};
            CommandUtil.saveOperate("VideoSetTrimOutCommand", unDoParam, redoParam, tag, tag);
        }
        videoClip.setTrimOut(trimOut, affectSibling);
        if (!CommandUtil.needSaveOperate(needSaveOperate)) {
            MeicamTimeline currentTimeline = EditorEngine.getInstance().getCurrentTimeline();
            MeicamVideoTrack videoTrack = currentTimeline.getVideoTrack(videoClip.getTrackIndex());
            int clipCount = videoTrack.getClipCount();
            for (int index = 0; index < clipCount; index++) {
                videoTrack.getVideoClip(index).updateInAndOutPoint();
            }
        } else {
            videoClip.updateInAndOutPoint();
        }
    }


    @Undo(className = "VideoSetTrimInCommand", function = "setTrimIn",
            param = {"long|trimIn", "boolean|affectSibling", "boolean...|needSaveOperate"})
    public static void setTrimIn(MeicamVideoClip videoClip, long trimIn, boolean affectSibling, boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(videoClip);
            Object[] unDoParam = new Object[]{videoClip.getTrimIn(), affectSibling, new boolean[]{false}};
            Object[] redoParam = new Object[]{trimIn, affectSibling};
            CommandUtil.saveOperate("VideoSetTrimInCommand", unDoParam, redoParam, tag, tag);
        }
        videoClip.setTrimIn(trimIn, affectSibling);
        if (!CommandUtil.needSaveOperate(needSaveOperate)) {
            MeicamTimeline currentTimeline = EditorEngine.getInstance().getCurrentTimeline();
            MeicamVideoTrack videoTrack = currentTimeline.getVideoTrack(videoClip.getTrackIndex());
            int clipCount = videoTrack.getClipCount();
            for (int index = 0; index < clipCount; index++) {
                videoTrack.getVideoClip(index).updateInAndOutPoint();
            }
        } else {
            videoClip.updateInAndOutPoint();
        }
    }


    @Undo(className = "UpdateMaskModelCommand", function = "updateMaskModel",
            param = {"String|key", "Object|object", "boolean...|needSaveOperate"})
    public static void updateMaskModel(MeicamVideoClip videoClip, String key, Object object, boolean... needSaveOperate) {
        Object lastParam = null;
        try {
            NvMaskModel maskModel = videoClip.maskModel;
            if (MASK_KEY_REGION.equals(key)) {
                lastParam = maskModel.regionInfo;
                maskModel.regionInfo = (MeicamMaskRegionInfo) object;
            } else if (MASK_KEY_LAST_SCALE.equals(key)) {
                lastParam = maskModel.transform.lastScale;
                maskModel.transform.lastScale = (float) object;
            } else if (MASK_KEY_LAST_VIEW_SCALE.equals(key)) {
                lastParam = maskModel.transform.viewScale;
                maskModel.transform.viewScale = (float) object;
            }
        } catch (Exception e) {
        }
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(videoClip);
            Object[] unDoParam = new Object[]{key, lastParam, new boolean[]{false}};
            Object[] redoParam = new Object[]{key, object};
            CommandUtil.saveOperate("UpdateMaskModelCommand", unDoParam, redoParam, tag, tag);
        }
    }

    @NonNull
    private static String getExtraTag(MeicamVideoClip videoClip) {
        return videoClip.getTrackIndex() + "|" + videoClip.getIndex();
    }

    public static MeicamVideoClip getItByTag(String tag) {
        try {
            String[] split = tag.replaceAll(TAG, "").split("\\|");
            int trackIndex = Integer.parseInt(split[0]);
            int clipIndex = Integer.parseInt(split[1]);
            return EditorEngine.getInstance().getVideoClip(trackIndex, clipIndex);
        } catch (Exception e) {
            LogUtils.e(e);
        }
        return null;
    }

    public static String getTag(MeicamVideoClip videoClip) {
        return TAG + videoClip.getTrackIndex() + "|" + videoClip.getIndex();
    }

    public static MeicamVideoFx findPropertyVideoFx(MeicamVideoClip videoClip) {
        MeicamVideoFx videoFx = videoClip.findPropertyVideoFx();
        if (videoFx != null) {
            videoFx.setExtraTag(getExtraTag(videoClip));
        }
        return videoFx;
    }
}
