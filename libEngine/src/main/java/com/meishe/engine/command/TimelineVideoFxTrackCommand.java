package com.meishe.engine.command;

import com.meishe.annotation.Undo;
import com.meishe.base.utils.LogUtils;
import com.meishe.engine.EditorEngine;
import com.meishe.engine.bean.MeicamTimeline;
import com.meishe.engine.bean.MeicamTimelineVideoFilterAndAdjustClip;
import com.meishe.engine.bean.MeicamTimelineVideoFxClip;
import com.meishe.engine.bean.MeicamTimelineVideoFxTrack;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/9/2 17:39
 * @Description :时间线特技轨道命令 The timeline video fx track command.
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class TimelineVideoFxTrackCommand {
    private static final String TAG = "FilterAndAdjustTrack";

    /**
     * Remove filter and adjust clip boolean.
     * 删除滤镜和调节clip
     *
     * @param videoFxTrack    the video fx track 特效轨道
     * @param clip            the clip
     * @param needSaveOperate the need save operate 是否保存数据
     * @return the boolean
     */
    @Undo(className = "RemoveFilterAndAdjustCommand", function = "addFilterAndAdjustClip",
            param = {"MeicamTimelineVideoFilterAndAdjustClip|clip", "boolean...|needSaveOperate"})
    public static boolean removeFilterAndAdjustClip(MeicamTimelineVideoFxTrack videoFxTrack,
                                                    MeicamTimelineVideoFilterAndAdjustClip clip, boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(videoFxTrack, true);
            String tagAndKey = tag + "|" + clip.getInPoint();
            // 此处要使用clone数据，因为如果后边还有更改inPoint、outPoint的指令，对象里面的inPoint、outPoint会发生变化，之后调用saveOperation就会把更改后的inPoint和outPoint保存
            // removeClip undo redo的时候就会找不对对应的clip。
            Object[] unDoParam = new Object[]{clip.clone(), new boolean[]{false}};
            Object[] redoParam = new Object[]{clip.clone()};
            // LogUtils.d("TimelineVideoFxTrack,index="+clip.getIndex()+",inPoint="+clip.getInPoint()+",trackIndex="+clip.getTrackIndex()+",name="+clip.getText());
            CommandUtil.saveOperate("RemoveFilterAndAdjustCommand", unDoParam, redoParam, tag, tagAndKey);
        }
        return videoFxTrack.removeFilterAndAdjustClip(clip);
    }


    /**
     * Add filter and adjust clip
     * 添加滤镜和调节clip
     *
     * @param videoFxTrack    the video fx track 特技轨道
     * @param clip            the clip
     * @param needSaveOperate the need save operate 是否保存数据
     * @return the meicam timeline video filter and adjust clip
     */
    @Undo(className = "AddFilterAndAdjustCommand", function = "removeFilterAndAdjustClip",
            param = {"MeicamTimelineVideoFilterAndAdjustClip|clip", "boolean...|needSaveOperate"})
    public static MeicamTimelineVideoFilterAndAdjustClip addFilterAndAdjustClip(MeicamTimelineVideoFxTrack videoFxTrack,
                                                                                MeicamTimelineVideoFilterAndAdjustClip clip, boolean... needSaveOperate) {
        MeicamTimelineVideoFilterAndAdjustClip filterAndAdjustClip = videoFxTrack.addFilterAndAdjustClip(clip);
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(videoFxTrack, true);
            String tagAndKey = tag + "|" + clip.getInPoint();
            // 此处要使用clone数据，因为如果后边还有更改inPoint、outPoint的指令，对象里面的inPoint、outPoint会发生变化，之后调用saveOperation就会把更改后的inPoint和outPoint保存
            // removeClip undo redo的时候就会找不对对应的clip。
            Object[] unDoParam = new Object[]{filterAndAdjustClip.clone(), new boolean[]{false}};
            Object[] redoParam = new Object[]{clip.clone()};
            // LogUtils.d("TimelineVideoFxTrack,index="+clip.getIndex()+",inPoint="+clip.getInPoint()+",trackIndex="+clip.getTrackIndex()+",name="+clip.getText());
            CommandUtil.saveOperate("AddFilterAndAdjustCommand", unDoParam, redoParam, tag, tagAndKey);
        }
        return filterAndAdjustClip;
    }

    @Undo(className = "AddFilterAndAdjustCommand1", function = "removeFilterAndAdjustClip",
            param = {"MeicamTimelineVideoFilterAndAdjustClip|clip", "boolean...|needSaveOperate"})
    public static MeicamTimelineVideoFilterAndAdjustClip addFilterAndAdjustClip(MeicamTimelineVideoFxTrack videoFxTrack, String type, long inPoint, long duration, boolean... needSaveOperate) {
        MeicamTimelineVideoFilterAndAdjustClip clip = videoFxTrack.addFilterAndAdjustClip(type, inPoint, duration);
        if (clip == null) {
            return null;
        }
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(videoFxTrack, true);
            String tagAndKey = tag + "|" + clip.getInPoint();
            // 此处要使用clone数据，因为如果后边还有更改inPoint、outPoint的指令，对象里面的inPoint、outPoint会发生变化，之后调用saveOperation就会把更改后的inPoint和outPoint保存
            // removeClip undo redo的时候就会找不对对应的clip。
            Object[] unDoParam = new Object[]{clip.clone(), new boolean[]{false}};
            Object[] redoParam = new Object[]{type, inPoint, duration};
            CommandUtil.saveOperate("AddFilterAndAdjustCommand1", unDoParam, redoParam, tag, tagAndKey);
            // CommandManager.getInstance().logCommand();
        }
        return clip;
    }

    @Undo(className = "AddTimelineVideoFxClipCommand", function = "removeClip",
            param = {"long|inPoint", "boolean...|needSaveOperate"})
    public static MeicamTimelineVideoFxClip addFxClip(MeicamTimelineVideoFxTrack videoFxTrack,
                                                      String type, long inPoint, long duration, String videoFxName, boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(videoFxTrack, false);
            Object[] unDoParam = new Object[]{inPoint, new boolean[]{false}};
            Object[] redoParam = new Object[]{type, inPoint, duration, videoFxName};
            CommandUtil.saveOperate("AddTimelineVideoFxClipCommand", unDoParam, redoParam, tag, tag);
        }
        return videoFxTrack.addFxClip(type, inPoint, duration, videoFxName);
    }

    @Undo(className = "AddTimelineVideoFxClipCommand1", function = "removeClip",
            param = {"long|inPoint", "boolean...|needSaveOperate"})
    public static MeicamTimelineVideoFxClip addFxClip(MeicamTimelineVideoFxTrack videoFxTrack,
                                                      MeicamTimelineVideoFxClip videoFxClip, boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(videoFxTrack, false);
            Object[] unDoParam = new Object[]{videoFxClip.getInPoint(), new boolean[]{false}};
            Object[] redoParam = new Object[]{videoFxClip};
            CommandUtil.saveOperate("AddTimelineVideoFxClipCommand1", unDoParam, redoParam, tag, tag);
        }
        return videoFxTrack.addFxClip(videoFxClip);
    }

    @Undo(className = "AddTimelineVideoFxClipCommand2", function = "removeClip",
            param = {"long|inPoint", "boolean...|needSaveOperate"})
    public static MeicamTimelineVideoFxClip addFxClip(MeicamTimelineVideoFxTrack videoFxTrack,
                                                      MeicamTimelineVideoFxClip fxClip, long inPoint,
                                                      long duration, String videoFxName, boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(videoFxTrack, false);
            Object[] unDoParam = new Object[]{inPoint, new boolean[]{false}};
            Object[] redoParam = new Object[]{fxClip, inPoint, duration, videoFxName};
            CommandUtil.saveOperate("AddTimelineVideoFxClipCommand2", unDoParam, redoParam, tag, tag);
        }
        return videoFxTrack.addFxClip(fxClip, inPoint, duration, videoFxName);
    }

    @Undo(className = "AddTimelineVideoFxClipCommand3", function = "removeClip",
            param = {"long|inPoint", "boolean...|needSaveOperate"})
    public static MeicamTimelineVideoFxClip addFxClip(MeicamTimelineVideoFxTrack videoFxTrack,
                                                      MeicamTimelineVideoFxClip fxClip, boolean needCreateTag, long inPoint,
                                                      long duration, String videoFxName, boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(videoFxTrack, false);
            Object[] unDoParam = new Object[]{inPoint, new boolean[]{false}};
            Object[] redoParam = new Object[]{fxClip, false, inPoint, duration, videoFxName};
            CommandUtil.saveOperate("AddTimelineVideoFxClipCommand3", unDoParam, redoParam, tag, tag);
        }
        return videoFxTrack.addFxClip(fxClip, inPoint, duration, videoFxName, needCreateTag);
    }

    @Undo(className = "RemoveTimelineVideoClipFxCommand", function = "addFxClip",
            param = {"MeicamTimelineVideoFxClip|videoFxClip", "boolean...|needSaveOperate"})
    public static MeicamTimelineVideoFxClip removeClip(MeicamTimelineVideoFxTrack videoFxTrack, long inPoint, boolean... needSaveOperate) {
        MeicamTimelineVideoFxClip videoFxClip = videoFxTrack.removeClip(inPoint);
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(videoFxTrack, false);
            Object[] unDoParam = new Object[]{videoFxClip, new boolean[]{false}};
            Object[] redoParam = new Object[]{inPoint};
            CommandUtil.saveOperate("RemoveTimelineVideoClipFxCommand", unDoParam, redoParam, tag, tag);
        }
        return videoFxClip;
    }

    @Undo(className = "RemoveTimelineVideoClipFxCommand1", function = "addFxClip",
            param = {"MeicamTimelineVideoFxClip|videoFxClip", "boolean...|needSaveOperate"})
    public static boolean removeClip(MeicamTimelineVideoFxTrack videoFxTrack,
                                     MeicamTimelineVideoFxClip fxClip, boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(videoFxTrack, false);
            Object[] unDoParam = new Object[]{fxClip.clone(), new boolean[]{false}};
            Object[] redoParam = new Object[]{fxClip.clone()};
            CommandUtil.saveOperate("RemoveTimelineVideoClipFxCommand1", unDoParam, redoParam, tag, tag);
        }
        return videoFxTrack.removeClip(fxClip);
    }

    private static String getTag(MeicamTimelineVideoFxTrack track, boolean isFilterOrAdjust) {
        return TAG + track.getIndex() + "|" + isFilterOrAdjust;
    }

    public static MeicamTimelineVideoFxTrack getItByTag(String tag) {
        try {
            tag = tag.replaceAll(TAG, "");
            String[] split = tag.split("\\|");
            int trackIndex = Integer.parseInt(split[0]);
            boolean isFilterOrAdjust = Boolean.parseBoolean(split[1]);
            MeicamTimeline timeline = EditorEngine.getInstance().getCurrentTimeline();
            MeicamTimelineVideoFxTrack videoFxTrack = isFilterOrAdjust ? timeline.getFilterAndAdjustTimelineTrack(trackIndex) : timeline.getTimelineFxTrack(trackIndex);
            if (videoFxTrack == null) {
                if (isFilterOrAdjust) {
                    videoFxTrack = timeline.addFilterAndAdjustTrack(trackIndex);
                } else {
                    videoFxTrack = timeline.addTimelineFxTrack(trackIndex);
                }
            }
            return videoFxTrack;
        } catch (Exception e) {
            LogUtils.e(e);
            LogUtils.e(tag);
        }
        return null;
    }
}
