package com.meishe.engine.command;


import android.text.TextUtils;

import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.FileUtils;
import com.meishe.base.utils.LogUtils;
import com.meishe.base.utils.ThreadUtils;
import com.meishe.engine.util.PathUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/8/22 14:44
 * @Description :命令管理器 The command manager
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class CommandManager {
    private static CommandManager sCommandManager;
    private CommandHolder commandHolder;
    private static int sCurrentIndex;
    private static volatile String mDir;

    private CommandManager() {
    }

    /**
     * Get instance command manager.
     * 获取单例
     *
     * @return the command manager
     */
    public static CommandManager getInstance() {
        if (sCommandManager == null) {
            sCommandManager = new CommandManager();
        }
        return sCommandManager;
    }

    /**
     * Put command.
     * 保存命令
     *
     * @param tag     the tag 命令tag
     * @param command the command 命令
     */
    public void putCommand(String tag, Command command) {
        if (commandHolder == null) {
            commandHolder = new CommandHolder();
            mDir = PathUtils.getOperatorCacheDir();
        }
        command.setIndex(sCurrentIndex++);
        commandHolder.putCommand(tag, command);
    }

    /**
     * Get command t.
     * 查询命令
     *
     * @param <T>     the type parameter
     * @param tag     the tag 命令tag
     * @param command the command 命令class 类
     * @return the t
     */
    public <T extends Command> T getCommand(String tag, Class<T> command) {
        if (commandHolder == null) {
            return null;
        }
        return commandHolder.getCommand(tag, command);
    }

    /**
     * Clear command.
     * 清空命令
     */
    public void clearCommand() {
        commandHolder = null;
        sCurrentIndex = 0;
    }

    /**
     * Sets command tag.
     * 设置命令tag
     *
     * @param commandTag the command tag
     */
    public void setCommandTag(String commandTag) {
        if (commandHolder == null) {
            commandHolder = new CommandHolder();
        }
        commandHolder.setCommandTag(commandTag);
    }

    /**
     * Save command string.
     * 保存命令到本地
     *
     * @return the string
     */
    public String saveCommand() {
        String path = null;
        if (commandHolder != null && !CommonUtils.isEmpty(commandHolder.data)) {
            path = writeObjectToFile(commandHolder);
            clearCommand();
        }
        return path;
    }

    /**
     * Gets command tag.
     * 获取命令tag
     *
     * @return the command tag
     */
    public String getCommandTag() {
        return commandHolder == null ? "" : commandHolder.getCommandTag();
    }

    public void logCommand() {
        if (commandHolder != null) {
            commandHolder.logCommand();
        }
    }

    public static class CommandHolder implements Serializable {
        private Map<String, List<Command>> data = new LinkedHashMap<>();
        private String commandTag;

        /**
         * Put command.
         * 保存命令
         *
         * @param tag     the tag 命令tag
         * @param command the command 命令
         */
        public void putCommand(String tag, Command command) {
            List<Command> commands = data.get(tag);
            if (commands == null) {
                commands = new ArrayList<>();
                data.put(tag, commands);
            }
            commands.add(command);
        }

        /**
         * Sets command tag.
         * 设置命令tag
         *
         * @param commandTag the command tag
         */
        public void setCommandTag(String commandTag) {
            this.commandTag = commandTag;
        }

        /**
         * Gets all command.
         * 获取所有的命令
         *
         * @return the all command
         */
        public List<Command> getAllCommand() {
            if (data.isEmpty()) {
                return null;
            }
            List<Command> allCommand = new ArrayList<>();
            Set<Map.Entry<String, List<Command>>> entries = data.entrySet();
            for (Map.Entry<String, List<Command>> entry : entries) {
                List<Command> value = entry.getValue();
                if (!CommonUtils.isEmpty(value)) {
                    allCommand.addAll(value);
                }
            }
            Collections.sort(allCommand, new Comparator<Command>() {
                @Override
                public int compare(Command o1, Command o2) {
                    return o1.getIndex() - o2.getIndex();
                }
            });
            return allCommand;
        }

        /**
         * Gets command tag.
         * 获取命令tag
         *
         * @return the command tag
         */
        public String getCommandTag() {
            return commandTag;
        }

        /**
         * Gets command.
         * 查询命令
         *
         * @param <T>     the type parameter
         * @param tag     the tag 命令tag
         * @param command the command 命令class类
         * @return the command
         */
        public <T extends Command> T getCommand(String tag, Class<T> command) {
            if (data.isEmpty()) {
                return null;
            }
            List<Command> commands = data.get(tag);
            if (CommonUtils.isEmpty(commands)) {
                return null;
            }
            for (Command command1 : commands) {
                if (command1.getClass().equals(command)) {
                    try {
                        return (T) command1;
                    } catch (Exception e) {
                        LogUtils.e(e);
                    }
                }
            }
            return null;
        }

        public void logCommand() {
            if (data != null) {
                for (Map.Entry<String, List<Command>> item : data.entrySet()) {
                    LogUtils.d("tag=" + item.getKey());
                    List<Command> commandList = item.getValue();
                    if (commandList != null) {
                        for (int i = 0; i < commandList.size(); i++) {
                            Command command = commandList.get(i);
                            LogUtils.d("command=" + command);
                        }
                    }
                }
            }
        }
    }

    private static String writeObjectToFile(Object obj) {
        if (TextUtils.isEmpty(mDir)) {
            mDir = PathUtils.getOperatorCacheDir();
        }
        File file = new File(mDir + File.separator + System.currentTimeMillis() + ".dat");
        FileOutputStream out;
        try {
            out = new FileOutputStream(file);
            ObjectOutputStream objOut = new ObjectOutputStream(out);
            objOut.writeObject(obj);
            objOut.flush();
            objOut.close();
        } catch (IOException e) {
            LogUtils.e(e);
        }
        return file.getAbsolutePath();
    }

    public void deleteCache() {
        ThreadUtils.getIoPool().execute(new Runnable() {
            @Override
            public void run() {
                if (!TextUtils.isEmpty(mDir)) {
                    File file = new File(mDir);
                    FileUtils.delete(file.getParentFile());
                    mDir = null;
                }
            }
        });
    }

    /**
     * Read object from file command holder.
     * 从文件中还原对象
     *
     * @param path the path 文件路径
     * @return the command holder 命令持有者
     */
    public static CommandHolder readObjectFromFile(String path) {
        CommandHolder temp = null;
        File file = new File(path);
        FileInputStream in;
        try {
            in = new FileInputStream(file);
            ObjectInputStream objIn = new ObjectInputStream(in);
            temp = (CommandHolder) objIn.readObject();
            objIn.close();
        } catch (Exception e) {
            LogUtils.e(e);
        }
        return temp;
    }
}
