package com.meishe.engine.command;

import android.graphics.PointF;

import com.meicam.sdk.NvsColor;
import com.meishe.annotation.Undo;
import com.meishe.base.bean.FloatPoint;
import com.meishe.base.utils.LogUtils;
import com.meishe.engine.EditorEngine;
import com.meishe.engine.bean.ClipInfo;
import com.meishe.engine.bean.MeicamCompoundCaptionClip;
import com.meishe.engine.bean.MeicamStickerCaptionTrack;
import com.meishe.engine.bean.MeicamTimeline;
import com.meishe.engine.util.ColorUtil;

import java.util.UUID;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/9/6 11:04
 * @Description :组合字幕命令 The compound caption command.
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class CompCaptionCommand {
    private final static String TAG = "compCaption";

    public static final int PARAM_SCALE = 1;
    public static final int PARAM_ROTATION = 2;
    public static final int PARAM_TRANS_X = 3;
    public static final int PARAM_TRANS_Y = 4;

    @Undo(className = "CompCaptionSetParamCommand", function = "setParam",
            param = {"int|paramType", "Object|param", "boolean...|needSaveOperate"})
    public static void setParam(MeicamCompoundCaptionClip captionClip, int paramType, Object param, boolean... needSavaOperate) {
        Object oldParam = null;
        try {
            switch (paramType) {
                case PARAM_SCALE:
                    oldParam = captionClip.getScaleX();
                    captionClip.setScaleX((Float) param);
                    captionClip.setScaleY((Float) param);
                    break;
                case PARAM_ROTATION:
                    oldParam = captionClip.getRotation();
                    captionClip.setRotation((Float) param);
                    break;
                case PARAM_TRANS_X:
                    oldParam = captionClip.getTranslationX();
                    captionClip.setTranslationX((Float) param);
                    break;
                case PARAM_TRANS_Y:
                    oldParam = captionClip.getTranslationY();
                    captionClip.setTranslationY((Float) param);
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            LogUtils.e(e);
        }

        if (CommandUtil.needSaveOperate(needSavaOperate)) {
            String tag = getTag(captionClip);
            String tagAndKey = tag + paramType;
            Object[] unDoParam = new Object[]{paramType, oldParam, new boolean[]{false}};
            Object[] redoParam = new Object[]{paramType, param};
            CommandUtil.saveOperate("CompCaptionSetParamCommand", unDoParam, redoParam, tag, tagAndKey);
        }

    }

    @Undo(className = "CompCaptionScaleCommand", function = "scaleCaption",
            param = {"float|scaleFactor", "com.meishe.base.bean.FloatPoint|anchor", "boolean...|needSaveOperate"})
    public static void scaleCaption(MeicamCompoundCaptionClip captionClip, float scaleFactor, FloatPoint anchor, boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(captionClip);
            String tagAndKey = tag + UUID.randomUUID();
            PointF assetAnchor = captionClip.getAssetAnchor();
            float anchorX = 0;
            float anchorY = 0;
            if (assetAnchor != null) {
                anchorX = assetAnchor.x;
                anchorY = assetAnchor.y;
            }
            Object[] unDoParam = new Object[]{1F / scaleFactor, new FloatPoint(anchorX, anchorY), new boolean[]{false}};
            Object[] redoParam = new Object[]{scaleFactor, anchor};
            CommandUtil.saveOperate("CompCaptionScaleCommand", unDoParam, redoParam, tag, tagAndKey);
        }
        captionClip.scaleCaption(scaleFactor, new PointF(anchor.x, anchor.y));
    }

    @Undo(className = "CompCaptionRotateCommand", function = "rotateCaption",
            param = {"float|angle", "com.meishe.base.bean.FloatPoint|anchor", "boolean...|needSaveOperate"})
    public static void rotateCaption(MeicamCompoundCaptionClip captionClip, float angle, FloatPoint anchor, boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(captionClip);
            String tagAndKey = tag + UUID.randomUUID();
            PointF assetAnchor = captionClip.getAssetAnchor();
            float anchorX = anchor.x;
            float anchorY = anchor.y;
            if (assetAnchor != null) {
                anchorX = assetAnchor.x;
                anchorY = assetAnchor.y;
            }

            Object[] unDoParam = new Object[]{-angle, new FloatPoint(anchorX, anchorY), new boolean[]{false}};
            Object[] redoParam = new Object[]{angle, anchor};
            CommandUtil.saveOperate("CompCaptionRotateCommand", unDoParam, redoParam, tag, tagAndKey);
        }
        captionClip.rotateCaption(angle, new PointF(anchor.x, anchor.y));
    }
    
    @Undo(className = "CompCaptionTranslateCommand", function = "translateCaption",
            param = {"com.meishe.base.bean.FloatPoint|trans", "com.meishe.base.bean.FloatPoint|anchor", "boolean...|needSaveOperate"})
    public static void translateCaption(MeicamCompoundCaptionClip captionClip, FloatPoint trans, FloatPoint anchor, boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(captionClip);
            String tagAndKey = tag + UUID.randomUUID();
            PointF assetAnchor = captionClip.getAssetAnchor();
            float anchorX = 0;
            float anchorY = 0;
            if (assetAnchor != null) {
                anchorX = assetAnchor.x;
                anchorY = assetAnchor.y;
            }
            Object[] unDoParam = new Object[]{new FloatPoint(-trans.x, - trans.y), new FloatPoint(anchorX, anchorY), new boolean[]{false}};
            Object[] redoParam = new Object[]{trans, anchor};
            CommandUtil.saveOperate("CompCaptionTranslateCommand", unDoParam, redoParam, tag, tagAndKey);
        }
        captionClip.translateCaption(new PointF(trans.x, trans.y), new PointF(anchor.x, anchor.y));
    }

    @Undo(className = "CompCaptionSetTextCommand", function = "setParam",
            param = {"int|index", "String|text", "boolean...|needSaveOperate"})
    public static void setText(MeicamCompoundCaptionClip captionClip, int index, String text, boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(captionClip);
            String tagAndKey = tag + index;
            Object[] unDoParam = new Object[]{index, text, new boolean[]{false}};
            Object[] redoParam = new Object[]{index, captionClip.getText(index)};
            CommandUtil.saveOperate("CompCaptionSetTextCommand", unDoParam, redoParam, tag, tagAndKey);
        }
        captionClip.setText(index, text);

    }

    @Undo(className = "CompCaptionSetTextColorCommand", function = "setParam",
            param = {"int|index", "String|colorValue", "boolean...|needSaveOperate"})
    public static void setTextColor(MeicamCompoundCaptionClip captionClip, int index, String colorValue, boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(captionClip);
            String tagAndKey = tag + index;
            Object[] unDoParam = new Object[]{index, colorValue, new boolean[]{false}};
            Object[] redoParam = new Object[]{index, captionClip.getTextColor(index)};
            CommandUtil.saveOperate("CompCaptionSetTextColorCommand", unDoParam, redoParam, tag, tagAndKey);
        }
        NvsColor nvsColor = ColorUtil.colorToNvsColor(colorValue);
        captionClip.setTextColor(index, nvsColor);
    }

    public static String getTag(MeicamCompoundCaptionClip stickerClip) {
        return TAG + stickerClip.getTrackIndex() + "|" + stickerClip.getIndex();
    }

    public static MeicamCompoundCaptionClip getItByTag(String tag) {
        tag = tag.replaceAll(TAG, "");
        String[] split = tag.split("\\|");
        int trackIndex = Integer.parseInt(split[0]);
        int clipIndex = Integer.parseInt(split[1]);
        MeicamTimeline timeline = EditorEngine.getInstance().getCurrentTimeline();
        MeicamStickerCaptionTrack track = timeline.findStickCaptionTrack(trackIndex);
        if (track != null) {
            ClipInfo<?> clip = track.getCaptionStickerClip(clipIndex);
            if (clip instanceof MeicamCompoundCaptionClip) {
                return (MeicamCompoundCaptionClip) clip;
            }
        }
        return null;
    }
}
