package com.meishe.engine.command;

import com.meicam.sdk.NvsVideoResolution;
import com.meishe.annotation.Undo;
import com.meishe.base.constants.Constants;
import com.meishe.base.utils.CommonUtils;
import com.meishe.engine.EditorEngine;
import com.meishe.engine.bean.ClipInfo;
import com.meishe.engine.bean.MeicamAudioTrack;
import com.meishe.engine.bean.MeicamCaptionClip;
import com.meishe.engine.bean.MeicamStickerCaptionTrack;
import com.meishe.engine.bean.MeicamTimeline;
import com.meishe.engine.bean.MeicamTimelineVideoFxClip;
import com.meishe.engine.bean.MeicamTimelineVideoFxTrack;
import com.meishe.engine.bean.MeicamVideoTrack;
import com.meishe.engine.bean.MeicamWaterMark;

import java.util.ArrayList;
import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/8/25 18:11
 * @Description :时间线命令 The timeline command
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class TimelineCommand {
    private static final String TAG = "timeline";
    @Undo(className ="SpiltAICaptionCommand", function = "mergeAICaption",
            param = {"long|timeStamp"})
    public static void spiltAICaption(MeicamTimeline timeline, long timeStamp, boolean... needSaveData) {
        if (CommandUtil.needSaveOperate(needSaveData)) {
            String tag = getTag();
            SpiltAICaptionCommand command = CommandManager.getInstance().getCommand(tag, SpiltAICaptionCommand.class);
            if (command == null) {
                command = new SpiltAICaptionCommand(tag, new SpiltAICaptionCommand.UndoParam(timeStamp));
                CommandManager.getInstance().putCommand(tag, command);
            }
            command.setRedoParam(new SpiltAICaptionCommand.RedoParam(timeStamp));
        }
        EditorEngine.getInstance().spiltAICaption(timeStamp);
    }


    @Undo(className = "AddFilterAndAdjustTrackCommand", function = "removeFilterAndAdjustTrack",
            param = {"int|trackIndex"})
    public static MeicamTimelineVideoFxTrack addFilterAndAdjustTrack(MeicamTimeline timeline,
                                                                     int trackIndex, boolean... needSaveData) {
        if (CommandUtil.needSaveOperate(needSaveData)) {
            String tag = getTag();
            AddFilterAndAdjustTrackCommand command = CommandManager.getInstance().getCommand(tag, AddFilterAndAdjustTrackCommand.class);
            if (command == null) {
                command = new AddFilterAndAdjustTrackCommand(tag, new AddFilterAndAdjustTrackCommand.UndoParam(trackIndex));
                CommandManager.getInstance().putCommand(tag, command);
            }
            command.setRedoParam(new AddFilterAndAdjustTrackCommand.RedoParam(trackIndex));
        }
        return timeline.addFilterAndAdjustTrack(trackIndex);
    }

    static void removeFilterAndAdjustTrack(MeicamTimeline timeline, int trackIndex) {
        timeline.removeFilterAndAdjustTrack(trackIndex);
        //删除尾部的空轨道
        int trackCount = timeline.getFilterAndAdjustTimelineTracksCount();
        if (trackCount > 0) {
            for (int index = trackCount - 1; index >= 0; index--) {
                MeicamTimelineVideoFxTrack track = timeline.getFilterAndAdjustTimelineTrack(index);
                if (track != null && track.getFilterAndAdjustCount() > 0) {
                    break;
                }
                timeline.removeFilterAndAdjustTrack(index);
            }
        }
    }

    @Undo(className = "RemoveFilterAndAdjustTrackCommand", function = "addFilterAndAdjustTrack",
            param = {"int|trackIndex", "boolean...|needSaveData"})
    public static void removeFilterAndAdjustTrack(MeicamTimeline timeline, int trackIndex, boolean... needSaveData) {
        timeline.removeFilterAndAdjustTrack(trackIndex);
        if (CommandUtil.needSaveOperate(needSaveData)) {
            String tag = getTag();
            RemoveFilterAndAdjustTrackCommand command = CommandManager.getInstance().getCommand(tag, RemoveFilterAndAdjustTrackCommand.class);
            if (command == null) {
                command = new RemoveFilterAndAdjustTrackCommand(tag, new RemoveFilterAndAdjustTrackCommand.UndoParam(trackIndex, false));
                CommandManager.getInstance().putCommand(tag, command);
            }
            command.setRedoParam(new RemoveFilterAndAdjustTrackCommand.RedoParam(trackIndex));
        }
    }

    @Undo(className = "AddFxTrackCommand", function = "removeTimelineFxTrack",
            param = {"int|trackIndex", "boolean...|needSaveData"})
    public static MeicamTimelineVideoFxTrack addTimelineFxTrack(MeicamTimeline timeline, int trackIndex, boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag();
            AddFxTrackCommand command = CommandManager.getInstance().getCommand(tag, AddFxTrackCommand.class);
            if (command == null) {
                command = new AddFxTrackCommand(tag, new AddFxTrackCommand.UndoParam(trackIndex, false));
                CommandManager.getInstance().putCommand(tag, command);
            }
            command.setRedoParam(new AddFxTrackCommand.RedoParam(trackIndex));
        }
        return timeline.addTimelineFxTrack(trackIndex);
    }

    @Undo(className = "RemoveFxTrackCommand", function = "addTimelineFxTrack",
            param = {"int|trackIndex", "boolean...|needSaveData"})
    public static MeicamTimelineVideoFxTrack removeTimelineFxTrack(MeicamTimeline timeline, int trackIndex, boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag();
            Object[] unDoParam = new Object[]{trackIndex, new boolean[]{false}};
            Object[] redoParam = new Object[]{trackIndex};
            CommandUtil.saveOperate("RemoveFxTrackCommand", unDoParam, redoParam, tag, tag);
        }
        return timeline.removeTimelineFxTrack(trackIndex);
    }

    @Undo(className = "RemoveFxFromClipCommand", function = "addTimelineVideoFxInClipList",
            param = {"String|type", "long|inPoint", "long|duration", "String|videoFxName", "boolean...|needSaveData"})
    public static boolean removeTimelineFxFromClipList(MeicamTimeline timeline, MeicamTimelineVideoFxClip timelineVideoFx, boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag();
            RemoveFxFromClipCommand command = CommandManager.getInstance().getCommand(tag, RemoveFxFromClipCommand.class);
            if (command == null) {
                command = new RemoveFxFromClipCommand(tag, new RemoveFxFromClipCommand.UndoParam(timelineVideoFx.getClipType(), timelineVideoFx.getInPoint(), timelineVideoFx.getOutPoint() - timelineVideoFx.getInPoint(), timelineVideoFx.getDesc(), false));
                CommandManager.getInstance().putCommand(tag, command);
            }
            command.setRedoParam(new RemoveFxFromClipCommand.RedoParam(timelineVideoFx));
        }
        return timeline.removeTimelineFxFromClipList(timelineVideoFx);
    }

    @Undo(className = "AddFxFromClipCommand", function = "removeTimelineFxFromClipList",
            param = {"MeicamTimelineVideoFxClip|timelineVideoFx", "boolean...|needSaveData"})
    public static MeicamTimelineVideoFxClip addTimelineVideoFxInClipList(MeicamTimeline timeline, String type, long inPoint, long duration, String videoFxName, boolean... needSaveOperate) {
        MeicamTimelineVideoFxClip videoFxClip = timeline.addTimelineVideoFxInClipList(type, inPoint, duration, videoFxName);
        if (videoFxClip == null) {
            return null;
        }
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag();
            AddFxFromClipCommand command = CommandManager.getInstance().getCommand(tag, AddFxFromClipCommand.class);
            if (command == null) {
                command = new AddFxFromClipCommand(tag, new AddFxFromClipCommand.UndoParam(videoFxClip, false));
                CommandManager.getInstance().putCommand(tag, command);
            }
            command.setRedoParam(new AddFxFromClipCommand.RedoParam(type, inPoint, duration, videoFxName));
        }
        return videoFxClip;
    }

    @Undo(className = "AddCaptionStickerTrackCommand", function = "removeStickCaptionTrack",
            param = {"int|trackIndex", "boolean...|needSaveData"})
    public static MeicamStickerCaptionTrack addStickCaptionTrack(MeicamTimeline timeline, int trackIndex, boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag();
            Object[] unDoParam = new Object[]{trackIndex, new boolean[]{false}};
            Object[] redoParam = new Object[]{trackIndex};
            CommandUtil.saveOperate("AddCaptionStickerTrackCommand", unDoParam, redoParam, tag, tag);
        }
        return timeline.addStickCaptionTrack(trackIndex);
    }

    @Undo(className = "RemoveCaptionStickerTrackCommand", function = "addStickCaptionTrack",
            param = {"int|trackIndex", "boolean...|needSaveData"})
    public static boolean removeStickCaptionTrack(MeicamTimeline timeline, int trackIndex, boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag();
            Object[] unDoParam = new Object[]{trackIndex, new boolean[]{false}};
            Object[] redoParam = new Object[]{trackIndex};
            CommandUtil.saveOperate("RemoveCaptionStickerTrackCommand", unDoParam, redoParam, tag, tag);
        }
        return timeline.removeStickCaptionTrack(trackIndex);
    }

    @Undo(className = "AddWaterMarkCommand", function = "deleteWatermark",
            param = {"boolean|resetWaterMark", "boolean...|needSaveData"})
    public static MeicamWaterMark addWatermark(MeicamTimeline timeline, String path, int width, int height, int x, int y, boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag();
            Object[] unDoParam = new Object[]{true, false};
            Object[] redoParam = new Object[]{path, width, height, x, y};
            CommandUtil.saveOperate("AddWaterMarkCommand", unDoParam, redoParam, tag, tag);
        }
        return timeline.addWatermark(path, width, height, x, y);
    }

    @Undo(className = "DeleteWaterMarkCommand", function = "addWatermark",
            param = {"String|path", "int|width", "int|height", "int|x", "int|y", "boolean...|needSaveData"})
    public static void deleteWatermark(MeicamTimeline timeline, boolean resetWaterMark, boolean... needSaveOperate) {
        MeicamWaterMark waterMark = timeline.getMeicamWaterMark();
        timeline.deleteWatermark(resetWaterMark);
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag();
            DeleteWaterMarkCommand command = CommandManager.getInstance().getCommand(tag, DeleteWaterMarkCommand.class);
            if (command == null) {
                command = new DeleteWaterMarkCommand(tag, new DeleteWaterMarkCommand.UndoParam(waterMark.getWatermarkFilePath(), waterMark.getDisplayWidth(), waterMark.getDisplayHeight(), waterMark.getMarginX(), waterMark.getMarginY(), false));
                CommandManager.getInstance().putCommand(tag, command);
            }
            command.setRedoParam(new DeleteWaterMarkCommand.RedoParam(resetWaterMark));
        }
    }


    @Undo(className = "AppendAudioTrackCommand", function = "removeAudioTrack",
            param = {"int|trackIndex", "boolean...|needSaveData"})
    public static MeicamAudioTrack appendAudioTrack(MeicamTimeline timeline, boolean... needSaveOperate) {
        MeicamAudioTrack audioTrack = timeline.appendAudioTrack();
        if (audioTrack == null) {
            return null;
        }
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag();
            int trackIndex = audioTrack.getIndex();
            String tagAndKey = tag + trackIndex + "audioTrack";
            Object[] unDoParam = new Object[]{trackIndex, new boolean[]{false}};
            Object[] redoParam = new Object[]{};
            CommandUtil.saveOperate("AppendAudioTrackCommand", unDoParam, redoParam, tag, tagAndKey);
        }
        return audioTrack;
    }

    @Undo(className = "RemoveAudioTrackCommand", function = "appendAudioTrack",
            param = {"boolean...|needSaveData"})
    public static MeicamAudioTrack removeAudioTrack(MeicamTimeline timeline, int trackIndex, boolean... needSaveOperate) {
        MeicamAudioTrack audioTrack = timeline.removeAudioTrack(trackIndex);
        if (audioTrack == null) {
            return null;
        }
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag();
            String tagAndKey = tag + trackIndex + "audioTrack";
            Object[] unDoParam = new Object[]{new boolean[]{false}};
            Object[] redoParam = new Object[]{trackIndex};
            CommandUtil.saveOperate("RemoveAudioTrackCommand", unDoParam, redoParam, tag, tagAndKey);
        }
        return audioTrack;
    }

    @Undo(className = "AppendVideoTrackCommand", function = "removeVideoTrack",
            param = {"int|index", "boolean...|needSaveData"})
    public static MeicamVideoTrack appendVideoTrack(MeicamTimeline meicamTimeline, boolean... needSaveOperate) {
        MeicamVideoTrack videoTrack = meicamTimeline.appendVideoTrack();
        if (videoTrack == null) {
            return null;
        }
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag();
            Object[] unDoParam = new Object[]{videoTrack.getIndex(), new boolean[]{false}};
            Object[] redoParam = new Object[]{};
            CommandUtil.saveOperate("AppendVideoTrackCommand", unDoParam, redoParam, tag, tag);
        }
        return videoTrack;
    }


    @Undo(className = "RemoveVideoTrackCommand", function = "insertVideoTrack", param = {"int|trackIndex", "boolean...|needSaveData"})
    public static MeicamVideoTrack removeVideoTrack(MeicamTimeline meicamTimeline, int trackIndex, boolean... needSaveOperate) {
        MeicamVideoTrack videoTrack = meicamTimeline.removeVideoTrack(trackIndex);
        if (videoTrack == null) {
            return null;
        }
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag();
            Object[] unDoParam = new Object[]{trackIndex, new boolean[]{false}};
            Object[] redoParam = new Object[]{trackIndex};
            CommandUtil.saveOperate("RemoveVideoTrackCommand", unDoParam, redoParam, tag, tag);
        }
        return videoTrack;
    }

    @Undo(className = "InsertVideoTrackCommand", function = "removeVideoTrack", param = {"int|trackIndex", "boolean...|needSaveData"})
    public static MeicamVideoTrack insertVideoTrack(MeicamTimeline meicamTimeline, int trackIndex, boolean... needSaveOperate) {
        MeicamVideoTrack videoTrack = meicamTimeline.insertVideoTrack(trackIndex);
        if (videoTrack == null) {
            return null;
        }
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag();
            Object[] unDoParam = new Object[]{trackIndex, new boolean[]{false}};
            Object[] redoParam = new Object[]{trackIndex};
            CommandUtil.saveOperate("InsertVideoTrackCommand", unDoParam, redoParam, tag, tag);
        }
        return videoTrack;
    }

    @Undo(className = "ChangeSizeCommand", function = "changeVideoSize",
            param = {"int|imageWidth", "int|imageHeight", "boolean...|needSaveData"})
    public static void changeVideoSize(MeicamTimeline meicamTimeline, int imageWidth, int imageHeight, boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag();
            NvsVideoResolution resolution = meicamTimeline.getVideoResolution();
            Object[] unDoParam = new Object[]{resolution.imageWidth, resolution.imageHeight, new boolean[]{false}};
            Object[] redoParam = new Object[]{imageWidth, imageHeight};
            CommandUtil.saveOperate("ChangeSizeCommand", unDoParam, redoParam, tag, tag);
        }
        meicamTimeline.changeVideoSize(imageWidth, imageHeight);
    }


    static void mergeAICaption(MeicamTimeline timeline, long timeStamp) {
        List<MeicamCaptionClip> captionClips = getAllAICaption(timeline, timeStamp);
        if (CommonUtils.isEmpty(captionClips)) {
            return;
        }
        for (int index = captionClips.size() - 1; index >= 0; index--) {
            MeicamCaptionClip current = captionClips.get(index);
            MeicamCaptionClip next = null;
            if (CommonUtils.isIndexAvailable(index + 1, captionClips)) {
                next = captionClips.get(index + 1);
            }
            if (next != null && current != null && next.getInPoint() == current.getOutPoint()
                    && next.getInPoint() == timeStamp) {
                MeicamStickerCaptionTrack captionTrack = timeline.findStickCaptionTrack(next.getTrackIndex());
                if (captionTrack != null) {
                    captionTrack.removeStickerCaptionClip(next);
                }
                current.setOutPoint(next.getOutPoint());
            }
        }
    }

    private static String getTag() {
        return TAG;
    }

    public static MeicamTimeline getItByTag(String tag) {
        return EditorEngine.getInstance().getCurrentTimeline();
    }

    /**
     * Gets all ai caption.
     * 获取所有的AI字幕
     *
     * @return the all ai caption
     */
    private static List<MeicamCaptionClip> getAllAICaption(MeicamTimeline timeline, long timeStamp) {
        List<MeicamCaptionClip> clipInfoList = new ArrayList<>();
        if (timeline != null) {
            for (int i = 0; i < timeline.getStickerCaptionTrackCount(); i++) {
                MeicamStickerCaptionTrack meicamStickerCaptionTrack = timeline.findStickCaptionTrack(i);
                if (meicamStickerCaptionTrack == null) {
                    continue;
                }
                for (int j = 0; j < meicamStickerCaptionTrack.getClipCount(); j++) {
                    ClipInfo<?> clipInfo = meicamStickerCaptionTrack.getCaptionStickerClip(j);
                    long inPoint = clipInfo.getInPoint();
                    long outPoint = clipInfo.getOutPoint();
                    if (isAICaption(clipInfo) && timeStamp >= inPoint && timeStamp <= outPoint) {
                        if (clipInfo instanceof MeicamCaptionClip) {
                            clipInfoList.add((MeicamCaptionClip) clipInfo);
                        }
                    }
                }
            }
        }
        return clipInfoList;
    }

    /**
     * Gets all ai caption.
     * 获取所有的AI字幕
     *
     * @return the all ai caption
     */
    public static List<ClipInfo<?>> getAllAICaption(MeicamTimeline timeline) {
        List<ClipInfo<?>> clipInfoList = new ArrayList<>();
        if (timeline != null) {
            for (int i = 0; i < timeline.getStickerCaptionTrackCount(); i++) {
                MeicamStickerCaptionTrack meicamStickerCaptionTrack = timeline.findStickCaptionTrack(i);
                if (meicamStickerCaptionTrack == null) {
                    continue;
                }
                for (int j = 0; j < meicamStickerCaptionTrack.getClipCount(); j++) {
                    ClipInfo<?> clipInfo = meicamStickerCaptionTrack.getCaptionStickerClip(j);
                    if (isAICaption(clipInfo)) {
                        clipInfoList.add(clipInfo);
                    }
                }

            }
        }
        return clipInfoList;
    }


    /**
     * 获取某个视频片段对应的 AI caption
     * get AI Caption form video clip
     *
     * @param clipInfo the video clip 视频片段
     */
    public static List<ClipInfo<?>> getAICaptionFromVideoClip(MeicamTimeline timeline, ClipInfo<?> clipInfo) {
        if (clipInfo == null) {
            return null;
        }
        List<ClipInfo<?>> captionList = new ArrayList<>();

        long inPoint = clipInfo.getInPoint();
        long outPoint = clipInfo.getOutPoint();
        List<ClipInfo<?>> allAICaption = getAllAICaption(timeline);
        if (CommonUtils.isEmpty(allAICaption)) {
            return null;
        }

        for (int i = 0; i < allAICaption.size(); i++) {
            ClipInfo<?> captionInfo = allAICaption.get(i);
            if (captionInfo == null) {
                continue;
            }
            if (isAICaption(captionInfo)) {
                long captionInPoint = captionInfo.getInPoint();
                long captionInfoOutPoint = captionInfo.getOutPoint();
                if (captionInPoint >= inPoint && captionInfoOutPoint <= outPoint) {
                    captionList.add(captionInfo);
                }
            }
        }
        return captionList;
    }

    /**
     * is AI caption
     *
     * @param clipInfo the clip info
     * @return Is ai caption or not. true:yes; false:no.
     */
    private static boolean isAICaption(ClipInfo<?> clipInfo) {
        return (clipInfo instanceof MeicamCaptionClip && ((MeicamCaptionClip) clipInfo).getOperationType() == Constants.TYPE_AI_CAPTION);
    }
}
