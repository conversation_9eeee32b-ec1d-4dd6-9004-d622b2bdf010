package com.meishe.engine.command;

import android.text.TextUtils;

import com.meishe.annotation.Undo;
import com.meishe.base.utils.LogUtils;
import com.meishe.engine.EditorEngine;
import com.meishe.engine.bean.MeicamPosition2D;
import com.meishe.engine.bean.MeicamTrackVideoFx;
import com.meishe.engine.bean.MeicamVideoTrack;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2023/6/26 16:07
 * @Description :轨道视频特技命令 The track video fx command
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class TrackVideoFxCommand {
    private static final String TAG = "trackVideoFx";

    @Undo(className = "TrackVideoFxSetStringValCommand", function = "setStringVal",
            param = {"String|key", "String|value", "boolean...|needSaveOperate"})
    public static void setStringVal(MeicamTrackVideoFx videoFx, String key, String value, boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(videoFx);
            String tagAndKey = tag + key+"string";

            Object[] unDoParam = new Object[]{key, videoFx.getStringVal(key),  new boolean[]{false}};
            Object[] redoParam = new Object[]{key, value};
            CommandUtil.saveOperate("TrackVideoFxSetStringValCommand", unDoParam, redoParam, tag, tagAndKey);
        }
        videoFx.setStringVal(key, value);
    }

    @Undo(className = "TrackVideoFxSetBooleanValCommand", function = "setBooleanVal",
            param = {"String|key", "boolean|value", " boolean...|needSaveOperate"})
    public static void setBooleanVal(MeicamTrackVideoFx videoFx, String key, boolean value, boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(videoFx);
            String tagAndKey = tag + key;

            Object[] unDoParam = new Object[]{key, videoFx.getBooleanVal(key),  new boolean[]{false}};
            Object[] redoParam = new Object[]{key, value};
            CommandUtil.saveOperate("TrackVideoFxSetBooleanValCommand", unDoParam, redoParam, tag, tagAndKey);
        }
        videoFx.setBooleanVal(key, value);
    }

    @Undo(className = "TrackVideoFxSetObjectValCommand", function = "setObjectVal",
            param = {"String|key", "Object|value", " boolean...|needSaveOperate"})
    public static void setObjectVal(MeicamTrackVideoFx videoFx, String key, Object value, boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(videoFx);
            String tagAndKey = tag + key;

            Object[] unDoParam = new Object[]{key, videoFx.getObjectVal(key),  new boolean[]{false}};
            Object[] redoParam = new Object[]{key, value};
            CommandUtil.saveOperate("TrackVideoFxSetObjectValCommand", unDoParam, redoParam, tag, tagAndKey);
        }
        videoFx.setObjectVal(key, value);
    }

    @Undo(className = "TrackVideoFxSetFloatValCommand", function = "setFloatVal",
            param = {"String|key", "float|value", " boolean...|needSaveOperate"})
    public static void setFloatVal(MeicamTrackVideoFx videoFx, String key, float value, boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(videoFx);
            String tagAndKey = tag + key;

            Object[] unDoParam = new Object[]{key, videoFx.getFloatVal(key, 0),  new boolean[]{false}};
            Object[] redoParam = new Object[]{key, value};
            CommandUtil.saveOperate("TrackVideoFxSetFloatValCommand", unDoParam, redoParam, tag, tagAndKey);
        }
        videoFx.setFloatVal(key, value);
    }

    @Undo(className = "TrackVideoFxSetIntValCommand", function = "setFloatVal",
            param = {"String|key", "int|value", " boolean...|needSaveOperate"})
    public static void setIntVal(MeicamTrackVideoFx videoFx, String key, int value, boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(videoFx);
            String tagAndKey = tag + key;
            Object[] unDoParam = new Object[]{key, videoFx.getIntVal(key),  new boolean[]{false}};
            Object[] redoParam = new Object[]{key, value};
            CommandUtil.saveOperate("TrackVideoFxSetIntValCommand", unDoParam, redoParam, tag, tagAndKey);
        }
        videoFx.setIntVal(key, value);
    }

    @Undo(className = "TrackVideoFxSetPosition2DCommand", function = "setPosition2DVal",
            param = {"String|key", "MeicamPosition2D|value", " boolean...|needSaveOperate"})
    public static void setPosition2DVal(MeicamTrackVideoFx videoFx, String key, MeicamPosition2D value, boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(videoFx);
            String tagAndKey = tag + key;
            Object[] unDoParam = new Object[]{key, videoFx.getPosition2DVal(key),  new boolean[]{false}};
            Object[] redoParam = new Object[]{key, value};
            CommandUtil.saveOperate("TrackVideoFxSetPosition2DCommand", unDoParam, redoParam, tag, tagAndKey);
        }
        videoFx.setPosition2DVal(key, value);
    }

    @Undo(className = "TrackVideoFxSetColorCommand", function = "setColorVal",
            param = {"String|key", "String|value", " boolean...|needSaveOperate"})
    public static void setColorVal(MeicamTrackVideoFx videoFx, String key, String value, boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(videoFx);
            String tagAndKey = tag + key+"color";
            Object[] unDoParam = new Object[]{key, videoFx.getColor(key),  new boolean[]{false}};
            Object[] redoParam = new Object[]{key, value};
            CommandUtil.saveOperate("TrackVideoFxSetColorCommand", unDoParam, redoParam, tag, tagAndKey);
        }
        videoFx.setColor(key, value);
    }

    @Undo(className = "TrackVideoFxSetMenuValCommand", function = "setMenuVal",
            param = {"String|key", "String|value", " boolean...|needSaveOperate"})
    public static void setMenuVal(MeicamTrackVideoFx videoFx, String key, String value, boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(videoFx);
            String tagAndKey = tag + key+"menu";

            Object[] unDoParam = new Object[]{key, videoFx.getMenuVal(key),  new boolean[]{false}};
            Object[] redoParam = new Object[]{key, value};
            CommandUtil.saveOperate("TrackVideoFxSetMenuValCommand", unDoParam, redoParam, tag, tagAndKey);
        }
        videoFx.setMenuVal(key, value);
    }

    @Undo(className = "TrackVideoFxSetExprValCommand", function = "setExprVar",
            param = {"String|key", "double|value", " boolean...|needSaveOperate"})
    public static void setExprVar(MeicamTrackVideoFx videoFx, String key, double value, boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(videoFx);
            String tagAndKey = tag + key;
            double dValue = 0;
            String stringVal = videoFx.getStringVal(key);
            if (!TextUtils.isEmpty(stringVal)) {
                dValue = Double.parseDouble(stringVal);
            }
            Object[] unDoParam = new Object[]{key, dValue,  new boolean[]{false}};
            Object[] redoParam = new Object[]{key, value};
            CommandUtil.saveOperate("TrackVideoFxSetExprValCommand", unDoParam, redoParam, tag, tagAndKey);
        }
        videoFx.setExprVar(key, value);
    }

    @Undo(className = "TrackVideoFxSetIntensityCommand", function = "setIntensity",
            param = {"float|intensity", " boolean...|needSaveOperate"})
    public static void setIntensity(MeicamTrackVideoFx videoFx, float intensity, boolean... needSaveOperate) {
        if (videoFx != null) {
            if (CommandUtil.needSaveOperate(needSaveOperate)) {
                String tag = getTag(videoFx);
                Object[] unDoParam = new Object[]{videoFx.getIntensity(), new boolean[]{false}};
                Object[] redoParam = new Object[]{intensity};
                CommandUtil.saveOperate("TrackVideoFxSetIntensityCommand", unDoParam, redoParam, tag, tag);
            }
            videoFx.setIntensity(intensity);
        }
    }

    @Undo(className = "TrackVideoFxChangeInPointCommand", function = "changeInPoint",
            param = {"long|inPoint", " boolean...|needSaveOperate"})
    public static void changeInPoint(MeicamTrackVideoFx videoFx, long inPoint, boolean... needSaveOperate) {
        if (videoFx != null) {
            if (CommandUtil.needSaveOperate(needSaveOperate)) {
                String tag = getTag(videoFx);
                Object[] unDoParam = new Object[]{videoFx.getInPoint(), new boolean[]{false}};
                Object[] redoParam = new Object[]{inPoint};
                CommandUtil.saveOperate("TrackVideoFxChangeInPointCommand", unDoParam, redoParam, tag, tag);
            }
            videoFx.setInPoint(inPoint);
        }
    }

    @Undo(className = "TrackVideoFxChangeOutPointCommand", function = "changeOutPoint",
            param = {"long|inPoint", " boolean...|needSaveOperate"})
    public static void changeOutPoint(MeicamTrackVideoFx videoFx, long outPoint, boolean... needSaveOperate) {
        if (videoFx != null) {
            if (CommandUtil.needSaveOperate(needSaveOperate)) {
                String tag = getTag(videoFx);
                Object[] unDoParam = new Object[]{videoFx.getOutPoint(), new boolean[]{false}};
                Object[] redoParam = new Object[]{outPoint};
                CommandUtil.saveOperate("TrackVideoFxChangeOutPointCommand", unDoParam, redoParam, tag, tag);
            }
            videoFx.setOutPoint(outPoint);
        }
    }

    @Undo(className = "TrackVideoFxBindTimelineFxCommand",
            function = "removeTimelineFxTag", param = {"String|timelineCreateTag", " boolean...|needSaveOperate"})
    public static void bindTimelineFxTag(MeicamTrackVideoFx videoFx, String timelineCreateTag,
                                         boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(videoFx);
            String tagAndKey = tag +"TrackVideoFxBindTimelineFx";
            Object[] unDoParam = new Object[]{timelineCreateTag, new boolean[]{false}};
            Object[] redoParam = new Object[]{timelineCreateTag};
            CommandUtil.saveOperate("TrackVideoFxBindTimelineFxCommand", unDoParam, redoParam, tag, tagAndKey);
        }
        videoFx.addTimelineFxTagSet(timelineCreateTag);
    }

    @Undo(className = "TrackVideoFxRemoveTimelineFxCommand",
            function = "bindTimelineFxTag", param = {"String|timelineCreateTag", " boolean...|needSaveOperate"})
    public static void removeTimelineFxTag(MeicamTrackVideoFx videoFx, String timelineCreateTag,
                                         boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(videoFx);
            String tagAndKey = tag +"TrackVideoFxBindTimelineFx";
            Object[] unDoParam = new Object[]{timelineCreateTag, new boolean[]{false}};
            Object[] redoParam = new Object[]{timelineCreateTag};
            CommandUtil.saveOperate("TrackVideoFxRemoveTimelineFxCommand", unDoParam, redoParam, tag, tagAndKey);
        }
        videoFx.removeTimelineFxTagSet(timelineCreateTag);
    }

    private static String getTag(MeicamTrackVideoFx videoFx) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(TAG);
        try {
            String extraTag = videoFx.getExtraTag();
            if (!TextUtils.isEmpty(extraTag)) {
                stringBuilder.append(extraTag);
            }
        } catch (Exception e) {
            LogUtils.e(e.getMessage());
        }
        return stringBuilder.toString();
    }

    public static MeicamTrackVideoFx getItByTag(String tag) {
        String[] split = tag.replaceAll(TAG, "").split("\\|");
        try {
            int videoTrackIndex = Integer.parseInt(split[0]);
            String fxTag = split[1];
            MeicamVideoTrack videoTrack = EditorEngine.getInstance().getVideoTrack(videoTrackIndex);
            if (videoTrack != null) {
                return videoTrack.getVideoFx(fxTag);
            }
        } catch (Exception e) {
            LogUtils.e(e);
        }
        return null;
    }
}
