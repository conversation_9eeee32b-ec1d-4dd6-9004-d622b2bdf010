package com.meishe.engine.command;

import com.meishe.annotation.Undo;
import com.meishe.base.utils.LogUtils;
import com.meishe.engine.EditorEngine;
import com.meishe.engine.bean.MeicamAudioClip;
import com.meishe.engine.bean.MeicamAudioFx;
import com.meishe.engine.bean.MeicamAudioTrack;
import com.meishe.engine.bean.MeicamTimeline;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/9/6 11:06
 * @Description :音频命令 The audio command.
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class AudioCommand {
    private final static String TAG = "audio";
    public static final int PARAM_AUDIO_TYPE = 1;
    public static final int PARAM_DRAW_TEXT = 2;
    public static final int PARAM_ADD_INDEX = 3;

    @Undo(className = "AudioSetSpeedCommand", function = "setSpeed",
            param = {"double|speed", "boolean|keepAudioPitch", "boolean...|needSaveOperate"})
    public static void setSpeed(MeicamAudioClip audioClip, double speed, boolean keepAudioPitch, boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(audioClip);
            Object[] unDoParam = new Object[]{audioClip.getSpeed(), audioClip.isKeepAudioPitch(), new boolean[]{false}};
            Object[] redoParam = new Object[]{speed, keepAudioPitch};
            CommandUtil.saveOperate("AudioSetSpeedCommand", unDoParam, redoParam, tag, tag);
        }
        audioClip.setSpeed(speed, keepAudioPitch);
    }

    @Undo(className = "AudioUpdateInAndOutPointCommand", function = "updateInAndOutPoint",
            param = {"boolean...|needSaveOperate"})
    public static void updateInAndOutPoint(MeicamAudioClip audioClip, boolean... needSaveOperate) {
        audioClip.updateInAndOutPoint();
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(audioClip);
            Object[] unDoParam = new Object[]{new boolean[]{false}};
            Object[] redoParam = new Object[]{};
            CommandUtil.saveOperate("AudioUpdateInAndOutPointCommand", unDoParam, redoParam, tag, tag);
        }
    }

    @Undo(className = "AudioSetTrimInCommand", function = "setTrimIn",
            param = {"long|trimIn", "boolean...|needSaveOperate"})
    public static void setTrimIn(MeicamAudioClip audioClip, long trimIn, boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(audioClip);
            Object[] unDoParam = new Object[]{audioClip.getTrimIn(), new boolean[]{false}};
            Object[] redoParam = new Object[]{trimIn};
            CommandUtil.saveOperate("AudioSetTrimInCommand", unDoParam, redoParam, tag, tag);
        }
        audioClip.setTrimIn(trimIn);
        if (!CommandUtil.needSaveOperate(needSaveOperate)) {
            MeicamTimeline currentTimeline = EditorEngine.getInstance().getCurrentTimeline();
            MeicamAudioTrack audioTrack = currentTimeline.getAudioTrack(audioClip.getTrackIndex());
            int clipCount = audioTrack.getClipCount();
            for (int index = audioTrack.getIndex(); index < clipCount; index++) {
                audioTrack.getAudioClip(index).updateInAndOutPoint();
            }
        } else {
            audioClip.updateInAndOutPoint();
        }
    }

    @Undo(className = "AudioSetTrimOutCommand", function = "setTrimOut",
            param = {"long|trimOut", "boolean...|needSaveOperate"})
    public static void setTrimOut(MeicamAudioClip audioClip, long trimOut, boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(audioClip);
            Object[] unDoParam = new Object[]{audioClip.getTrimOut(), new boolean[]{false}};
            Object[] redoParam = new Object[]{trimOut};
            CommandUtil.saveOperate("AudioSetTrimOutCommand", unDoParam, redoParam, tag, tag);
        }
        audioClip.setTrimOut(trimOut);
        if (!CommandUtil.needSaveOperate(needSaveOperate)) {
            MeicamTimeline currentTimeline = EditorEngine.getInstance().getCurrentTimeline();
            MeicamAudioTrack audioTrack = currentTimeline.getAudioTrack(audioClip.getTrackIndex());
            int clipCount = audioTrack.getClipCount();
            for (int index = audioTrack.getIndex(); index < clipCount; index++) {
                audioTrack.getAudioClip(index).updateInAndOutPoint();
            }
        } else {
            audioClip.updateInAndOutPoint();
        }
    }

    @Undo(className = "AudioSetFadeInCommand", function = "setFadeInDuration",
            param = {"long|duration", "boolean...|needSaveOperate"})
    public static void setFadeInDuration(MeicamAudioClip audioClip, long duration, boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(audioClip);
            Object[] unDoParam = new Object[]{audioClip.getFadeInDuration(), new boolean[]{false}};
            Object[] redoParam = new Object[]{duration};
            CommandUtil.saveOperate("AudioSetFadeInCommand", unDoParam, redoParam, tag, tag);
        }
        audioClip.setFadeInDuration(duration);
    }

    @Undo(className = "AudioSetFadeOutCommand", function = "setFadeOutDuration",
            param = {"long|duration", "boolean...|needSaveOperate"})
    public static void setFadeOutDuration(MeicamAudioClip audioClip, long duration, boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(audioClip);
            Object[] unDoParam = new Object[]{audioClip.getFadeOutDuration(), new boolean[]{false}};
            Object[] redoParam = new Object[]{duration};
            CommandUtil.saveOperate("AudioSetFadeOutCommand", unDoParam, redoParam, tag, tag);
        }
        audioClip.setFadeOutDuration(duration);
    }

    @Undo(className = "AudioSetVolumeCommand", function = "setVolume",
            param = {"float|volume", "boolean...|needSaveOperate"})
    public static void setVolume(MeicamAudioClip audioClip, float volume, boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(audioClip);
            Object[] unDoParam = new Object[]{audioClip.getVolume(), new boolean[]{false}};
            Object[] redoParam = new Object[]{volume};
            CommandUtil.saveOperate("AudioSetVolumeCommand", unDoParam, redoParam, tag, tag);
        }
        audioClip.setVolume(volume);
    }

    @Undo(className = "AudioSetParamCommand", function = "setParam",
            param = {"int|paramType", "Object|param", "boolean...|needSaveOperate"})
    public static void setParam(MeicamAudioClip audioClip, int paramType, Object param, boolean... needSaveOperate) {
        Object oldParam = null;
        try {
            switch (paramType) {
                case PARAM_AUDIO_TYPE:
                    oldParam = audioClip.getAudioType();
                    audioClip.setAudioType((Integer) param);
                    break;
                case PARAM_DRAW_TEXT:
                    oldParam = audioClip.getDrawText();
                    audioClip.setDrawText((String) param);
                    break;
                case PARAM_ADD_INDEX:
                    oldParam = audioClip.getAddIndex();
                    audioClip.setAddIndex((Integer) param);
                    break;
                default:
                    break;
            }

        } catch (Exception e) {
            LogUtils.e(e);
        }
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(audioClip);
            String tagAndKey = tag + paramType;
            Object[] unDoParam = new Object[]{paramType, oldParam, new boolean[]{false}};
            Object[] redoParam = new Object[]{paramType, param};
            CommandUtil.saveOperate("AudioSetParamCommand", unDoParam, redoParam, tag, tagAndKey);
        }
    }

    @Undo(className = "AudioAppendAudioFxCommand", function = "removeAudioFx",
            param = {"String|fxId", "boolean...|needSaveOperate"})
    public static MeicamAudioFx appendAudioFx(MeicamAudioClip audioClip, String fxId, boolean... needSaveOperate) {
        MeicamAudioFx audioFx = audioClip.appendAudioFx(fxId);
        if (audioFx == null) {
            return null;
        }
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(audioClip);
            String tagAndKey = tag + fxId;
            Object[] unDoParam = new Object[]{fxId, new boolean[]{false}};
            Object[] redoParam = new Object[]{fxId};
            CommandUtil.saveOperate("AudioAppendAudioFxCommand", unDoParam, redoParam, tag, tagAndKey);
        }
        return audioFx;
    }

    @Undo(className = "AudioRemoveAudioFxCommand", function = "appendAudioFx",
            param = {"String|fxId", "boolean...|needSaveOperate"})
    public static MeicamAudioFx removeAudioFx(MeicamAudioClip audioClip, String fxId, boolean... needSaveOperate) {
        MeicamAudioFx audioFx = audioClip.removeAudioFx(fxId);
        if (audioFx == null) {
            return null;
        }
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(audioClip);
            String tagAndKey = tag + fxId;
            Object[] unDoParam = new Object[]{fxId, new boolean[]{false}};
            Object[] redoParam = new Object[]{fxId};
            CommandUtil.saveOperate("AudioRemoveAudioFxCommand", unDoParam, redoParam, tag, tagAndKey);
        }
        return audioFx;
    }


    public static String getTag(MeicamAudioClip clip) {
        return TAG + clip.getTrackIndex() + "|" + clip.getIndex();
    }

    public static MeicamAudioClip getItByTag(String tag) {
        tag = tag.replaceAll(TAG, "");
        String[] split = tag.split("\\|");
        int trackIndex = Integer.parseInt(split[0]);
        int clipIndex = Integer.parseInt(split[1]);
        MeicamTimeline timeline = EditorEngine.getInstance().getCurrentTimeline();
        MeicamAudioTrack track = timeline.getAudioTrack(trackIndex);
        if (track != null) {
            return track.getAudioClip(clipIndex);
        }
        return null;
    }
}
