package com.meishe.engine.command;

import android.text.TextUtils;
import android.util.Pair;

import androidx.annotation.NonNull;

import com.meishe.annotation.Undo;
import com.meishe.base.bean.FloatPoint;
import com.meishe.base.utils.LogUtils;
import com.meishe.engine.EditorEngine;
import com.meishe.engine.bean.KeyFrameProcessor;
import com.meishe.engine.bean.MeicamCaptionClip;
import com.meishe.engine.bean.MeicamCompoundCaptionClip;
import com.meishe.engine.bean.MeicamFxParam;
import com.meishe.engine.bean.MeicamKeyFrame;
import com.meishe.engine.bean.MeicamMaskRegionInfo;
import com.meishe.engine.bean.MeicamPosition2D;
import com.meishe.engine.bean.MeicamStickerCaptionTrack;
import com.meishe.engine.bean.MeicamStickerClip;
import com.meishe.engine.bean.MeicamTimeline;
import com.meishe.engine.bean.MeicamTimelineVideoFxClip;
import com.meishe.engine.bean.MeicamTimelineVideoFxTrack;
import com.meishe.engine.bean.MeicamVideoClip;
import com.meishe.engine.bean.MeicamVideoFx;
import com.meishe.engine.interf.IKeyFrameProcessor;

import java.security.InvalidParameterException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/8/31 11:16
 * @Description :关键帧持有者命令 The key frame holder command
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class KeyFrameHolderCommand {
    private static final String TAG = "KeyFrameHolder";
    private static final String TYPE_TIME_LINE_FX = "timelineFx";
    private static final String TYPE_VIDEO_FX = "videoFx";
    private static final String TYPE_CAPTION_STICKER = "captionSticker";

    /**
     * Add key frame.
     * 添加关键帧
     * @param keyFrameHolder the key frame processor 关键帧处理器
     * @param params            the key 关键帧参数
     * @param atTime            the at time atTime
     * @param offsetTime        the at offset Time 偏移量
     * @param needSaveOperate   the need save operate 是否需要保存操作
     * @return the meicam key frame
     */
    @Undo(className ="AddKeyFrameCommand", function = "removeKeyFrame",
            param = {"String[]|key", "long|atTime", "boolean...|needSaveOperate"})
    public static MeicamKeyFrame addKeyFrame(IKeyFrameProcessor<?> keyFrameHolder, List<MeicamFxParam<?>> params, long atTime, long offsetTime, boolean... needSaveOperate) {
        KeyFrameProcessor<?> keyFrameProcessor = keyFrameHolder.keyFrameProcessor();
        String tag = getTag(keyFrameHolder);
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String keyAndTag = tag + atTime;
            String[] keys = new String[params.size()];
            for (int index = 0; index < params.size(); index++) {
                keys[index] = params.get(index).getKey();
            }
            AddKeyFrameCommand command = CommandManager.getInstance().getCommand(keyAndTag, AddKeyFrameCommand.class);
            if (command == null) {
                command = new AddKeyFrameCommand(tag, new AddKeyFrameCommand.UndoParam(keys, atTime, false));
                CommandManager.getInstance().putCommand(keyAndTag, command);
            }
            command.setRedoParam(new AddKeyFrameCommand.RedoParam(params, atTime, offsetTime));
        }
        MeicamKeyFrame keyFrame = keyFrameProcessor.addKeyFrame(atTime);
        if (keyFrame != null) {
            keyFrame.setOffsetTime(offsetTime);
            if (keyFrameHolder instanceof MeicamCaptionClip || keyFrameHolder instanceof MeicamStickerClip) {
                keyFrame.setCurrentKeyFrameTime(atTime);
            }
            for (MeicamFxParam<?> meicamFxParam : params) {
                String paramTypeItem = meicamFxParam.getType();
                String key = meicamFxParam.getKey();
                Object value = meicamFxParam.getValue();
                if (MeicamFxParam.TYPE_BOOLEAN.equals(paramTypeItem)) {
                    keyFrame.setBooleanVal(key, (Boolean) value);
                } else if (MeicamFxParam.TYPE_FLOAT.equals(paramTypeItem)) {
                    float fValue;
                    if (value instanceof Double) {
                        double dValue = (double) value;
                        fValue = (float) dValue;
                    } else {
                        fValue = (float) value;
                    }
                    keyFrame.setFloatVal(key, fValue);
                } else if (MeicamFxParam.TYPE_OBJECT.equals(paramTypeItem)) {
                    if (value instanceof MeicamMaskRegionInfo) {
                        keyFrame.setArbDataVal(key, (MeicamMaskRegionInfo) value);
                    }
                } else if (MeicamFxParam.TYPE_INT.equals(paramTypeItem)) {
                    keyFrame.setIntVal(key, (Integer) value);
                } else if (MeicamFxParam.TYPE_COLOR.equals(paramTypeItem)) {
                    keyFrame.setColor(key, (String) value);
                } else if (MeicamFxParam.TYPE_POSITION_2D.equals(paramTypeItem)) {
                    keyFrame.setPosition2DVal(key, (MeicamPosition2D) value);
                }
            }
            keyFrame.setExtraTag(tag);
        }
        return keyFrame;
    }

    @Undo(className ="RemoveKeyFrameCommand", function = "addKeyFrame",
            param = {"java.util.List<MeicamFxParam<?>>|param", "long|atTime", "long|offset", "boolean...|needSaveOperate"})
    public static boolean removeKeyFrame(IKeyFrameProcessor<?> keyFrameProcessor, String[] key, long atTime, boolean... needSaveOperate){
        KeyFrameProcessor<?> processor = keyFrameProcessor.keyFrameProcessor();
        MeicamKeyFrame keyFrame;
        if (key!= null && key.length > 0) {
            keyFrame = processor.getKeyFrame(key[0], atTime);
        } else {
            keyFrame = processor.getKeyFrame(atTime);
        }
        if (keyFrame == null) {
            return false;
        }
        keyFrame = keyFrame.clone();
        boolean result = processor.removeKeyFrame(key, atTime);

        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(keyFrameProcessor);
            String tagAndKey = tag + atTime;
            List<MeicamFxParam<?>> params = keyFrame.getParams();
            Object[] unDoParam = new Object[]{params, atTime, keyFrame.getOffsetTime(), new boolean[]{false}};
            Object[] redoParam = new Object[]{key, atTime};
            CommandUtil.saveOperate("RemoveKeyFrameCommand", unDoParam, redoParam, tag, tagAndKey);
        }
        return result;
    }

    @Undo(className ="CutKeyFrameCurveCommand", function = "mergeKeyFrameCurve",
            param = {"long|atTime", "String|key"})
    public static boolean cutKeyFrameCurve(IKeyFrameProcessor<?> keyFrameProcessor, long atTime, String key, boolean... needSaveOperate) {
        if (keyFrameProcessor == null) {
            return false;
        }
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(keyFrameProcessor);
            String keyAndTag = tag +key + atTime;
            CutKeyFrameCurveCommand command = CommandManager.getInstance().getCommand(keyAndTag, CutKeyFrameCurveCommand.class);
            if (command == null) {
                command = new CutKeyFrameCurveCommand(tag, new CutKeyFrameCurveCommand.UndoParam(atTime, key));
                CommandManager.getInstance().putCommand(keyAndTag, command);
            }
            command.setRedoParam(new CutKeyFrameCurveCommand.RedoParam(atTime, key));
        }
        return keyFrameProcessor.keyFrameProcessor().cutKeyFrameCurve(atTime, key);
    }


    @Undo(className ="AddKeyFrameCurveCommand", function = "restoreKeyFrame",
            param = {"java.util.Map<Long, MeicamKeyFrame>|oldData"})
    public static boolean addKeyFrameCurve(IKeyFrameProcessor<?> keyFrameProcessor, String key, long atTime, FloatPoint startControlPoint, FloatPoint endControlPoint, int id, boolean...needSaveOperate) {
        if (keyFrameProcessor == null) {
            return false;
        }
        KeyFrameProcessor<?> frameProcessor = keyFrameProcessor.keyFrameProcessor();
        Pair<MeicamKeyFrame, MeicamKeyFrame> framePair = EditorEngine.getInstance().getFramePair(keyFrameProcessor,
                key, atTime);
        if (framePair == null) {
            return false;
        }
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(keyFrameProcessor);
            String tagAndKey = key + atTime;
            Map<Long, MeicamKeyFrame> newKeyFrameMap = findAndCloneKeyFrame(frameProcessor);
            Object[] unDoParam = new Object[]{newKeyFrameMap};
            Object[] redoParam = new Object[]{key, atTime, startControlPoint, endControlPoint, id};
            CommandUtil.saveOperate("AddKeyFrameCurveCommand", unDoParam, redoParam, tag, tagAndKey);
        }
        return frameProcessor.addKeyFrameCurve(framePair, startControlPoint, endControlPoint, id);
    }

    static boolean mergeKeyFrameCurve(IKeyFrameProcessor<?> keyFrameProcessor, long atTime, String key) {
        return keyFrameProcessor.keyFrameProcessor().mergeKeyFrame(atTime, key);
    }

    @Undo(className ="ResetKeyFrameCommand", function = "restoreKeyFrame",
            param = {"java.util.Map<Long, MeicamKeyFrame>|oldData"})
    public static void resetKeyFrame(IKeyFrameProcessor<?> keyFrameProcessor, boolean fromNvsFx, boolean... needSaveOperate) {
        KeyFrameProcessor<?> frameProcessor = keyFrameProcessor.keyFrameProcessor();
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(keyFrameProcessor);
            ResetKeyFrameCommand command = CommandManager.getInstance().getCommand(tag, ResetKeyFrameCommand.class);
            if (command == null) {
                Map<Long, MeicamKeyFrame> newKeyFrameMap = findAndCloneKeyFrame(frameProcessor);
                command = new ResetKeyFrameCommand(tag, new ResetKeyFrameCommand.UndoParam(newKeyFrameMap));
                CommandManager.getInstance().putCommand(tag, command);
            }
            command.setRedoParam(new ResetKeyFrameCommand.RedoParam(fromNvsFx));
        }
        frameProcessor.resetKeyFrame(fromNvsFx);
    }

    @NonNull
    private static Map<Long, MeicamKeyFrame> findAndCloneKeyFrame(KeyFrameProcessor<?> frameProcessor) {
        Map<Long, MeicamKeyFrame> newKeyFrameMap = new HashMap<>();
        Map<Long, MeicamKeyFrame> keyFrameMap = frameProcessor.getKeyFrameMap(null);
        Set<Map.Entry<Long, MeicamKeyFrame>> entries = keyFrameMap.entrySet();
        if (!entries.isEmpty()) {
            for (Map.Entry<Long, MeicamKeyFrame> entry : entries) {
                MeicamKeyFrame value = entry.getValue();
                if (value != null) {
                    newKeyFrameMap.put(entry.getKey(), value.clone());
                }
            }
        }
        return newKeyFrameMap;
    }

    @Undo(className ="ReverseKeyFrameCommand", function = "restoreKeyFrame",
            param = {"java.util.Map<Long, MeicamKeyFrame>|oldData"})
    public static void reverseKeyFrame(IKeyFrameProcessor<?> keyFrameProcessor, boolean... needSaveOperate) {
        KeyFrameProcessor<?> frameProcessor = keyFrameProcessor.keyFrameProcessor();
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(keyFrameProcessor);
            Map<Long, MeicamKeyFrame> newKeyFrameMap = findAndCloneKeyFrame(frameProcessor);
            Object[] unDoParam = new Object[]{newKeyFrameMap};
            Object[] redoParam = new Object[]{};
            CommandUtil.saveOperate("ReverseKeyFrameCommand", unDoParam, redoParam, tag, tag);
        }
        frameProcessor.reverseKeyFrame();
    }

    @Undo(className ="UpdateKeyFrameControlPointsCommand", function = "updateKeyFrameControlPoints",
            param = {"boolean...|needSaveOperate"})
    public static void updateKeyFrameControlPoints(IKeyFrameProcessor<?> keyFrameProcessor, boolean... needSaveOperate) {
        KeyFrameProcessor<?> frameProcessor = keyFrameProcessor.keyFrameProcessor();
        frameProcessor.updateKeyFrameControlPoints();

        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(keyFrameProcessor);
            Object[] unDoParam = new Object[]{new boolean[]{false}};
            Object[] redoParam = new Object[]{};
            CommandUtil.saveOperate("UpdateKeyFrameControlPointsCommand", unDoParam, redoParam, tag, tag);
        }
    }


    @Undo(className ="PostChangeKeyFrame", function = "preChangeKeyFrame",
            param = {"boolean...|needSaveOperate"})
    public static void postChangeKeyFrame(IKeyFrameProcessor<?> keyFrameHolder, Map<Long, MeicamKeyFrame> oldData, boolean... needSaveOperate) {
        KeyFrameProcessor<?> frameProcessor = keyFrameHolder.keyFrameProcessor();
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(keyFrameHolder);
            String tagAndKey = tag + System.currentTimeMillis();
            Map<Long, MeicamKeyFrame> newKeyFrameMap = findAndCloneKeyFrame(frameProcessor);
            Object[] unDoParam = new Object[]{new boolean[]{false}};
            Object[] redoParam = new Object[]{newKeyFrameMap};
            CommandUtil.saveOperate("PostChangeKeyFrame", unDoParam, redoParam, tag, tagAndKey);
        } else {
            frameProcessor.restoreKeyFrame(oldData);
        }
    }


    @Undo(className ="PreChangeKeyFrame", function = "postChangeKeyFrame",
            param = {"java.util.Map<Long, MeicamKeyFrame>|oldData","boolean...|needSaveOperate"})
    public static void preChangeKeyFrame(IKeyFrameProcessor<?> keyFrameProcessor, boolean... needSaveOperate){
        KeyFrameProcessor<?> frameProcessor = keyFrameProcessor.keyFrameProcessor();
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(keyFrameProcessor);
            String tagAndKey = tag + System.currentTimeMillis();
            Map<Long, MeicamKeyFrame> newKeyFrameMap = findAndCloneKeyFrame(frameProcessor);
            Object[] unDoParam = new Object[]{newKeyFrameMap, new boolean[]{false}};
            Object[] redoParam = new Object[]{new boolean[]{false}};
            CommandUtil.saveOperate("PreChangeKeyFrame", unDoParam, redoParam, tag, tagAndKey);
        } else {
            frameProcessor.removeAllKeyFrame();
        }
    }

    static void restoreKeyFrame(IKeyFrameProcessor<?> keyFrameProcessor, Map<Long, MeicamKeyFrame> oldData){
        keyFrameProcessor.keyFrameProcessor().restoreKeyFrame(oldData);
    }

    @Undo(className ="RestoreKeyFrameCommand", function = "restoreKeyFrame",
            param = {"java.util.Map<Long, MeicamKeyFrame>|oldData"})
    public static void restoreKeyFrame(IKeyFrameProcessor<?> keyFrameProcessor, Map<Long, MeicamKeyFrame> oldData, boolean... needSaveOperate){
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(keyFrameProcessor);
            Map<Long, MeicamKeyFrame> newKeyFrameMap = findAndCloneKeyFrame(keyFrameProcessor.keyFrameProcessor());
            Object[] unDoParam = new Object[]{newKeyFrameMap};
            Object[] redoParam = new Object[]{oldData};
            CommandUtil.saveOperate("RestoreKeyFrameCommand", unDoParam, redoParam, tag, tag);
        }
        restoreKeyFrame(keyFrameProcessor, oldData);
    }

    static String getTag(IKeyFrameProcessor<?> keyFrameProcessor){
        StringBuilder stringBuilder = new StringBuilder();
        if (keyFrameProcessor instanceof MeicamVideoFx) {
            MeicamVideoFx videoFx = (MeicamVideoFx) keyFrameProcessor;
            String extraTag = videoFx.getExtraTag();
            if (!TextUtils.isEmpty(extraTag)) {
                stringBuilder.append(extraTag).append("[");
            } else {
                throw new InvalidParameterException("The extra tag is null! You need set the extraTag of MeicamVideoFx.");
            }
            stringBuilder.append(TYPE_VIDEO_FX).append("|");
            stringBuilder.append(videoFx.getType()).append("|").
                    append(videoFx.getSubType()).append("|").
                    append(videoFx.getDesc());
        } else if (keyFrameProcessor instanceof MeicamTimelineVideoFxClip) {
            MeicamTimelineVideoFxClip clip = (MeicamTimelineVideoFxClip) keyFrameProcessor;
            stringBuilder.append(TYPE_TIME_LINE_FX).append("|")
                    .append(clip.getTrackIndex()).append("|")
                    .append(clip.getIndex());
        } else if (keyFrameProcessor instanceof MeicamCaptionClip) {
            MeicamCaptionClip captionClip = (MeicamCaptionClip) keyFrameProcessor;
            stringBuilder.append(TYPE_CAPTION_STICKER).append("|")
                    .append(captionClip.getTrackIndex()).append("|")
                    .append(captionClip.getIndex());
        } else if (keyFrameProcessor instanceof MeicamStickerClip) {
            MeicamStickerClip stickerClip = (MeicamStickerClip) keyFrameProcessor;
            stringBuilder.append(TYPE_CAPTION_STICKER).append("|")
                    .append(stickerClip.getTrackIndex()).append("|")
                    .append(stickerClip.getIndex());
        } else if (keyFrameProcessor instanceof MeicamCompoundCaptionClip) {
            MeicamCompoundCaptionClip captionClip = (MeicamCompoundCaptionClip) keyFrameProcessor;
            stringBuilder.append(TYPE_CAPTION_STICKER).append("|")
                    .append(captionClip.getTrackIndex()).append("|")
                    .append(captionClip.getIndex());
        }
        return stringBuilder.toString();
    }

    public static IKeyFrameProcessor<?> getItByTag(String tag){
        try {
            tag = tag.replaceAll(TAG, "");
            int index = tag.lastIndexOf("[");
            String lastTag = tag.substring(index + 1);
            String[] subSplit = lastTag.split("\\|");
            String name = subSplit[0];
            if (TYPE_TIME_LINE_FX.equals(name)) {
                int trackIndex = Integer.parseInt(subSplit[1]);
                int clipIndex = Integer.parseInt(subSplit[2]);
                MeicamTimeline timeline = EditorEngine.getInstance().getCurrentTimeline();
                MeicamTimelineVideoFxTrack timelineFxTrack = timeline.getTimelineFxTrack(trackIndex);
                if (timelineFxTrack != null) {
                    return timelineFxTrack.getClip(clipIndex);
                }
            } else if (TYPE_CAPTION_STICKER.equals(name)) {
                int trackIndex = Integer.parseInt(subSplit[1]);
                int clipIndex = Integer.parseInt(subSplit[2]);
                MeicamTimeline timeline = EditorEngine.getInstance().getCurrentTimeline();
                MeicamStickerCaptionTrack track = timeline.findStickCaptionTrack(trackIndex);
                if (track != null) {
                    return (IKeyFrameProcessor<?>) track.getCaptionStickerClip(clipIndex);
                }
            } else if (TYPE_VIDEO_FX.equals(name)) {
                String type = subSplit[1];
                String subType = subSplit[2];
                String desc = subSplit[3];
                String extraTag = "";
                if (index > 0) {
                    extraTag = tag.substring(0, index);
                }
                MeicamVideoClip videoClip = VideoClipCommand.getItByTag(extraTag);
                if (videoClip != null) {
                    MeicamVideoFx videoFx = videoClip.getVideoFxByType(type, subType);
                    if (videoFx == null) {
                        videoFx = videoClip.getVideoFxById(desc);
                    }
                    return videoFx;
                }
            }
        } catch (Exception e) {
            LogUtils.e(e);
            LogUtils.e(tag);
        }
        return null;
    }
}
