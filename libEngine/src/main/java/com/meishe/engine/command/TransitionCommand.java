package com.meishe.engine.command;

import com.meishe.annotation.Undo;
import com.meishe.base.utils.LogUtils;
import com.meishe.engine.EditorEngine;
import com.meishe.engine.bean.MeicamTimeline;
import com.meishe.engine.bean.MeicamTransition;
import com.meishe.engine.bean.MeicamVideoTrack;

public class TransitionCommand {
    private static final String TAG = "Transition";
    public static final int PARAM_ICON_RESOURCE_ID = 1;
    public static final int PARAM_ICON_PATH = 2;
    public static final int PARAM_DURATION = 3;


    @Undo(className = "TransitionSetParamCommand", function = "setParam",
            param = {"int|paramType", "Object|param", "boolean...|needSaveOperate"})
    public static void setParam(MeicamTransition transition, int paramType, Object param, boolean... needSaveOperate) {
        Object oldParam = null;
        try {
            switch (paramType) {
                case PARAM_ICON_RESOURCE_ID:
                    oldParam = transition.getIconResourceId();
                    transition.setIconResourceId((Integer) param);
                    break;
                case PARAM_ICON_PATH:
                    oldParam = transition.getIconPath();
                    transition.setIconPath((String) param);
                    break;
                case PARAM_DURATION:
                    oldParam = transition.getDuration();
                    transition.setDuration((Long) param);
                    break;
                default:
                    break;
            }

        } catch (Exception e) {
            LogUtils.e(e);
        }
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(transition);
            String tagAndKey = tag + paramType;
            Object[] unDoParam = new Object[]{paramType, oldParam, new boolean[]{false}};
            Object[] redoParam = new Object[]{paramType, param};
            CommandUtil.saveOperate("TransitionSetParamCommand", unDoParam, redoParam, tag, tagAndKey);
        }
    }

    private static String getTag(MeicamTransition transition) {
        return TAG + transition.getTrackIndex() + "|"+ transition.getIndex();
    }

    public static MeicamTransition getItByTag(String tag){
        tag = tag.replaceAll(TAG, "");
        String[] split = tag.split("\\|");
        MeicamTimeline currentTimeline = EditorEngine.getInstance().getCurrentTimeline();
        MeicamVideoTrack videoTrack = currentTimeline.getVideoTrack(Integer.parseInt(split[0]));
        if (videoTrack != null) {
            return videoTrack.getTransition(Integer.parseInt(split[1]));
        }
        return null;
    }
}
