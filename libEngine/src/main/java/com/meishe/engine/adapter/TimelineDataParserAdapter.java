package com.meishe.engine.adapter;


import com.meicam.sdk.NvsObject;

/**
 * author：yangtailin on 2020/8/11 17:08
 * TimelineData数据转成磁盘数据适配器，只有写逻辑
 * The TimelineData data was converted to a disk data adapter with only write logic
 * @param <T> the type parameter
 */
public interface TimelineDataParserAdapter<T> {
    /**
     * Parse to local data t.
     * 解析到本地数据t
     * @return the t
     */
    T parseToLocalData();

    /**
     * Recover from local data
     * 从本地恢复
     * @param t the t
     */
    void recoverFromLocalData(T t);

    /**
     * Recover from timeline data
     * 从timeline恢复
     * @param nvsObject the t
     */
    void recoverFromTimelineData(NvsObject nvsObject);
}
