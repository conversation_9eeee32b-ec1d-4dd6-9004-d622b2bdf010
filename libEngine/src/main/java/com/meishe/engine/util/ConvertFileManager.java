package com.meishe.engine.util;

import android.text.TextUtils;

import com.meicam.sdk.NvsAVFileInfo;
import com.meicam.sdk.NvsMediaFileConvertor;
import com.meicam.sdk.NvsStreamingContext;
import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.FileUtils;
import com.meishe.base.utils.LogUtils;
import com.meishe.base.utils.ThreadUtils;
import com.meishe.engine.observer.ConvertFileObservable;
import com.meishe.engine.observer.ConvertFileObserver;

import java.io.File;
import java.util.ArrayList;
import java.util.Hashtable;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * The type Convert file manager.
 * 此类为 转换文件管理器
 */
public class ConvertFileManager implements NvsMediaFileConvertor.MeidaFileConvertorCallback, IConvertManager {
    private static final String TEMP_NAME = "_meicamTemp";
    private static final int MAX_PROGRESS = 99;
    private NvsMediaFileConvertor mMediaFileConvert;
    private static ConvertFileManager mInstance;
    private ConvertParam mConvertParam;
    private ConvertFileObservable convertFileObservable;

    private ConvertFileManager() {
        convertFileObservable = new ConvertFileObservable();
    }

    /**
     * Gets instance.
     * 获得实例
     *
     * @return the instance
     */
    public static ConvertFileManager getInstance() {
        if (mInstance == null) {
            mInstance = new ConvertFileManager();
        }
        return mInstance;
    }

    @Override
    public void onProgress(long taskId, float progress) {
        if (mConvertParam != null && !CommonUtils.isEmpty(mConvertParam.paramMap)) {
            int size = mConvertParam.paramMap.size();
            int p = (int) (progress * 100);
            Set<Map.Entry<String, ConvertParam.Param>> entries = mConvertParam.paramMap.entrySet();
            if (!entries.isEmpty()) {
                for (Map.Entry<String, ConvertParam.Param> entry : entries) {
                    ConvertParam.Param param = entry.getValue();
                    if (param.getTaskId() == taskId) {
                        param.setProgress((int) (progress * 100));
                    } else {
                        p = p + param.getProgress();
                    }
                }
            }
            p = p / size;
            if (p > MAX_PROGRESS) {
                p = MAX_PROGRESS;
            }
            if (convertFileObservable != null) {
                convertFileObservable.onConvertProgress(p);
            }
        }

    }

    @Override
    public void onFinish(long taskId, String srcFile, String dstFile, int errorCode) {
        LogUtils.d("onFinish: errorCode = " + errorCode + ", srcFile = " + srcFile);
        if (mConvertParam != null) {
            int count = mConvertParam.paramMap.size();
            boolean success = true;
            Set<Map.Entry<String, ConvertParam.Param>> entries = mConvertParam.paramMap.entrySet();
            if (!entries.isEmpty()) {
                for (Map.Entry<String, ConvertParam.Param> entry : entries) {
                    ConvertParam.Param param = entry.getValue();
                    if (param.getTaskId() == taskId) {
                        param.setFinish(true);
                        param.setSuccess(errorCode == NvsMediaFileConvertor.CONVERTOR_ERROR_CODE_NO_ERROR);

                    }
                    if (param.isFinish()) {
                        count--;
                    }
                    if (success) {
                        success = param.isSuccess();
                    }
                }
            }
            if (count == 0) {
                if (!reconvert()) {
                    finishConvert();
                    handleConvertResult(success);
                }
            }
        }
    }

    /**
     * 再次转码，如果出现一次错误，不再执行
     * Convert again. If an error occurs, it will not be executed again.
     *
     * @return 再次转码是否执行 Whether transcoding is performed again？ true:yes; false:no
     */
    private boolean reconvert() {
        LogUtils.d("start...");
        Set<Map.Entry<String, ConvertParam.Param>> entries = mConvertParam.paramMap.entrySet();
        List<ConvertParam.Param> reconvertParam = new ArrayList<>();
        if (!entries.isEmpty()) {
            for (Map.Entry<String, ConvertParam.Param> entry : entries) {
                ConvertParam.Param param = entry.getValue();
                if (!param.isSuccess() && !param.isReconvertExecute()) {
                  /*  if (WhiteList.isHDR(param.getSrcFile())) {
                    }*/
                    reconvertParam.add(param);
                }
            }
        }
        if (reconvertParam.isEmpty()) {
            return false;
        }
        NvsStreamingContext.getInstance().clearCachedResources(false);
        for (ConvertParam.Param param : reconvertParam) {
            reconvertBySoft(param);
        }
        return true;
    }


    /**
     * 再次尝试用软编码转码
     * Reconvert by soft decoder.
     **/
    private void reconvertBySoft(ConvertParam.Param param) {
        LogUtils.d("start...");
        param.setReconvertExecute(true);
        param.setFinish(false);
        if (mMediaFileConvert == null) {
            return;
        }
        String srcFile = param.getSrcFile();
        long toPosition = param.getToPosition();
        if (toPosition == -1) {
            NvsAVFileInfo avFileInfo = NvsStreamingContext.getInstance().getAVFileInfo(srcFile);
            if (avFileInfo != null) {
                toPosition = avFileInfo.getDuration();
            }
        }
        Hashtable<String, Object> configurations = param.getConfigurations();
        /*
         * 禁止使用硬件编码
         * Hardware encoding is prohibited
         */
        if (configurations == null) {
            configurations = new Hashtable<>();
        }
        configurations.put(NvsMediaFileConvertor.CONVERTOR_DISABLE_HARDWARE_VIDEO_DECODER, true);
        long taskId = mMediaFileConvert.convertMeidaFile(srcFile, param.getDstFile(),
                param.isReverseConvert(), param.getFromPosition(), toPosition, configurations);
        param.setTaskId(taskId);
        LogUtils.d("reconvertBySoft: srcFile = " + srcFile + ", taskId = " + taskId);
    }

    @Override
    public void notifyAudioMuteRage(long l, long l1, long l2) {

    }

    /**
     * 转换倒放文件,目前仅支持执行完一次convertParam所给的任务后才能继续下一个。
     * Convert file
     *
     * @param convertParam 转换参数
     */
    @Override
    public void convertFile(ConvertParam convertParam) {
        NvsStreamingContext.getInstance().clearCachedResources(false);
        if (convertParam == null || convertParam.paramMap == null || convertParam.paramMap.isEmpty()) {
            LogUtils.e("convertParam== null");
            return;
        }
        if (mConvertParam != null) {
            cancelConvert();
        }
        if (mMediaFileConvert == null) {
            mMediaFileConvert = new NvsMediaFileConvertor();
        }
        mMediaFileConvert.setMeidaFileConvertorCallback(this, null);
        mConvertParam = convertParam;
        Set<Map.Entry<String, ConvertParam.Param>> entries = convertParam.paramMap.entrySet();

        for (Map.Entry<String, ConvertParam.Param> entry : entries) {
            ConvertParam.Param param = entry.getValue();
            if (TextUtils.isEmpty(param.getSrcFile()) || TextUtils.isEmpty(param.getDstFile())) {
                continue;
            }
            param.setDstFile(param.getDstFile() + TEMP_NAME);
            long toPosition = param.getToPosition();
            if (toPosition == -1) {
                NvsAVFileInfo avFileInfo = NvsStreamingContext.getInstance().getAVFileInfo(param.getSrcFile());
                if (avFileInfo != null) {
                    toPosition = avFileInfo.getDuration();
                }
            }
            Hashtable<String, Object> configurations = param.getConfigurations();
            configurations.put(NvsMediaFileConvertor.CONVERTOR_DISABLE_HARDWARE_VIDEO_DECODER, false);
            param.setTaskId(mMediaFileConvert.convertMeidaFile(param.getSrcFile(), param.getDstFile(),
                    param.isReverseConvert(), param.getFromPosition(), toPosition, configurations));
        }

    }

    /**
     * Gets dest file path.
     * 获取目标文件的路径
     *
     * @param srcFilePath the src file path 源文件
     * @param destFolder  the dest folder 目标文件夹
     * @return the dest file path 目标文件路径
     */
    public static String getDestFilePath(String srcFilePath, String destFolder) {
        return destFolder + File.separator + FileUtils.getFileMD5ToString(srcFilePath);
    }

    /**
     * Cancel convert.
     * 取消转换
     */
    @Override
    public void cancelConvert() {
        if (mMediaFileConvert == null) {
            return;
        }
        if (mConvertParam != null && mConvertParam.paramMap != null && !mConvertParam.paramMap.isEmpty()) {
            Set<Map.Entry<String, ConvertParam.Param>> entries = mConvertParam.paramMap.entrySet();
            for (Map.Entry<String, ConvertParam.Param> entry : entries) {
                mMediaFileConvert.cancelTask(entry.getValue().getTaskId());
            }
        }
        finishConvert();
        ThreadUtils.getIoPool().execute(new Runnable() {
            @Override
            public void run() {
                if (mConvertParam != null && mConvertParam.paramMap != null && !mConvertParam.paramMap.isEmpty()) {
                    Set<Map.Entry<String, ConvertParam.Param>> entries = mConvertParam.paramMap.entrySet();
                    for (Map.Entry<String, ConvertParam.Param> entry : entries) {
                        FileUtils.delete(entry.getValue().dstFile);
                    }
                    mConvertParam = null;
                }
                ThreadUtils.runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        if (convertFileObservable != null) {
                            convertFileObservable.onConvertCancel();
                        }
                    }
                });
            }
        });
    }

    private void finishConvert() {
        if (mMediaFileConvert != null) {
            mMediaFileConvert.release();
        }
        mMediaFileConvert = null;
    }


    /**
     * 注册app进入后台的监听
     * register background observer
     */
    @Override
    public void registerConvertFileObserver(ConvertFileObserver observer) {
        if (observer != null) {
            convertFileObservable.registerObserver(observer);
        }
    }

    /**
     * 注销app进入后台的监听
     * unregister background observer
     */
    @Override
    public void unregisterConvertFileObserver(ConvertFileObserver observer) {
        try {
            mConvertParam = null;
            if (observer != null) {
                convertFileObservable.unregisterObserver(observer);
            }
        } catch (Exception e) {
            LogUtils.e(e);
        }
    }

    private void handleConvertResult(final boolean success) {
        ThreadUtils.getIoPool().execute(new Runnable() {
            @Override
            public void run() {
                /*
                 *转码好后，重命名 rename the file after converting.
                 */
                if (success) {
                    if (mConvertParam != null && mConvertParam.paramMap != null && !mConvertParam.paramMap.isEmpty()) {
                        Set<Map.Entry<String, ConvertParam.Param>> entries = mConvertParam.paramMap.entrySet();
                        for (Map.Entry<String, ConvertParam.Param> entry : entries) {
                            ConvertParam.Param param = entry.getValue();
                            File file = new File(param.dstFile);
                            String newName = file.getName().replace(TEMP_NAME, "");
                            boolean rename = FileUtils.rename(file, newName);
                            LogUtils.d("handleConvertResult: rename = " + rename + ", destPath before = " + param.dstFile);
                            if (rename) {
                                param.dstFile = param.dstFile.replace(TEMP_NAME, "");
                            }
                            LogUtils.d("handleConvertResult：destPath after = " + param.dstFile);
                        }
                    }
                }
                ThreadUtils.runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        if (convertFileObservable != null) {
                            convertFileObservable.onConvertFinish(mConvertParam, success);
                        }
                    }
                });
            }
        });
    }

    public static abstract class EventListener {

        /**
         * On convert finish.
         * 转码结束
         *
         * @param convertParam   the convert param 转码后的参数
         * @param convertSuccess the convert success 是否转码成功
         */
        public abstract void onConvertFinish(ConvertFileManager.ConvertParam convertParam, boolean convertSuccess);

        /**
         * On convert cancel.
         * 转码取消
         */
        public void onConvertCancel(){

        }
    }
}
