package com.meishe.engine.util;

import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.meicam.sdk.NvsVideoTrack;
import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.LogUtils;
import com.meishe.engine.EditorEngine;
import com.meishe.engine.bean.CommonData;
import com.meishe.engine.bean.MeicamTimeline;
import com.meishe.engine.bean.MeicamTimelineVideoFxClip;
import com.meishe.engine.bean.MeicamTimelineVideoFxTrack;
import com.meishe.engine.bean.MeicamTrackVideoFx;
import com.meishe.engine.bean.MeicamVideoClip;
import com.meishe.engine.bean.MeicamVideoFx;
import com.meishe.engine.bean.MeicamVideoTrack;
import com.meishe.engine.command.ClipCommand;
import com.meishe.engine.command.TimelineFxCommand;
import com.meishe.engine.command.TimelineVideoFxTrackCommand;
import com.meishe.engine.command.TrackVideoFxCommand;
import com.meishe.engine.command.VideoClipCommand;
import com.meishe.engine.command.VideoFxCommand;
import com.meishe.engine.command.VideoTrackCommand;
import com.meishe.engine.constant.NvsConstants;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;

import static com.meishe.engine.bean.CommonData.MAIN_TRACK_INDEX;
import static com.meishe.engine.bean.MeicamVideoFx.SubType.SUB_TYPE_FROM_TIMELINE;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2023/6/30 10:50
 * @Description :The binder of timelineFx 时间线特效绑定处理类
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class TimelineFxBinder {

   /**
    * Bind to target.
    * 绑定到目标
    *
    * @param videoFxClip the video fx clip
    */
   public static void bindToTarget(MeicamTimelineVideoFxClip videoFxClip, String oldTimelineTag) {
      if (videoFxClip == null) {
         LogUtils.e("Param is invalid!");
         return;
      }
      if (videoFxClip.getIntensity() == 1) {
         LogUtils.d("The videoFx is bound to noting");
         return;
      }
      MeicamTimeline timeline = EditorEngine.getInstance().getCurrentTimeline();
      if (timeline == null) {
         LogUtils.e("Timeline is null!");
         return;
      }

      eachMainTrack(timeline, (videoTrack, trackVideoFx) -> {
         if (trackVideoFx.hasTag(oldTimelineTag)) {
            addTimelineFxToVideoTrack(videoTrack, videoFxClip);
         }
      });
      eachPipTrack(timeline, (videoClip, videoFx) -> {
         if (videoFx != null && videoFx.hasTag(oldTimelineTag)) {
            addTimelineFxToVideoClip(videoClip, videoFxClip);
         }
      });
   }


   /**
    * Unbind timeline video fx.
    * 解除绑定timeline特效
    *
    * @param videoClip the video clip
    */
   public static void unbindTimelineVideoFx(MeicamVideoClip videoClip){
      if (videoClip == null) {
         return;
      }
      if (videoClip.getTrackIndex() == CommonData.MAIN_TRACK_INDEX) {
         return;
      }
      eachVideoFx(videoClip, videoFx -> {
         if (videoFx != null && !CommonUtils.isEmpty(videoFx.getTimelineFxTag())) {
            videoClip.removeVideoFx(videoFx);
         }
      });
   }


   /**
    * Remove target video fx.
    * 删除目标特效
    *
    * @param videoFxClip the video fx clip 时间线特效
    */
   public static void removeTargetVideoFx(MeicamTimelineVideoFxClip videoFxClip) {
      if (videoFxClip == null) {
         LogUtils.e("Param is null !");
         return;
      }
      if (videoFxClip.getIntensity() == 1) {
         LogUtils.d("The videoFx is bound to noting");
         return;
      }
      String createTag = videoFxClip.getExtraTag();
      if (TextUtils.isEmpty(createTag)) {
         LogUtils.e("CreateTag is null !");
         return;
      }
      MeicamTimeline timeline = EditorEngine.getInstance().getCurrentTimeline();
      if (timeline == null) {
         LogUtils.e("Timeline is null!");
         return;
      }

      eachMainTrack(timeline, (videoTrack, trackVideoFx) -> {
         if (trackVideoFx.hasTag(createTag)) {
            VideoTrackCommand.removeTrackVideoFx(videoTrack, trackVideoFx.getInPoint());
         }
      });

      eachPipTrack(timeline, (videoClip, videoFx) -> {
         if (videoFx.hasTag(createTag)) {
            VideoClipCommand.removeFx(videoClip, videoFx);
         }
      });
   }


   /**
    * Update timeline fx target when clip to main track.
    * 当clip切换到主轨道时，更新timeline 特效
    *
    * @param clip the clip
    */
   public static void updateTimelineFxTarget(MeicamVideoClip clip, boolean isToMain) {
      MeicamTimeline timeline = EditorEngine.getInstance().getCurrentTimeline();
      if (timeline == null) {
         return;
      }
      MeicamVideoTrack videoTrack = timeline.getVideoTrack(MAIN_TRACK_INDEX);
      if (isToMain) {
         //画中画轨道切换到主轨道后，画中画轨道携带的timelineFx也需要绑定到主轨
         // After switching to the main track, the timeline Fx carried by the picture in picture track also
         // needs to be bound to the main track.
         eachVideoFx(clip, videoFx -> {
            if (videoFx != null){
               Set<String> timelineFxTag = videoFx.getTimelineFxTag();
               if (!CommonUtils.isEmpty(timelineFxTag)) {
                  VideoClipCommand.removeFx(clip, videoFx);
                  MeicamTimelineVideoFxClip timelineFx = findTimelineFxByTag(timelineFxTag);
                  if (timelineFx != null) {
                     //主轨道 Main track
                     addTimelineFxToVideoTrack(videoTrack, timelineFx);
                  }
               }
            }
         });
      } else {
         //主轨道切换到画中画轨道后，主轨道携带的timelineFx不需要绑定到画中画轨道，但是需要处理timelineFx的outPoint，outPoint不能超过主轨道时长.
         // After switching from the main track to the picture-in-a-picture track, the timeline Fx carried by the main track
         // does not need to be bound to the picture-in-a-picture track, but needs to handle the outPoint of the timeline Fx,
         // which cannot exceed the duration of the main track.
         long trackOutPoint = clip.getOutPoint();
         if (videoTrack != null) {
            MeicamVideoClip videoClip = videoTrack.getVideoClip(videoTrack.getClipCount() - 1);
            if (videoClip != null) {
               trackOutPoint = videoClip.getOutPoint();
            }
         }
         long finalTrackOutPoint = trackOutPoint;
         eachMainTrack(timeline, (videoTrack1, videoFx) -> {
            if (videoFx == null) {
               return;
            }
            eachTimelineFx(timeline, (timelineFxTrack, videoFxClip) -> {
               if (videoFxClip == null || (!videoFx.hasTag(videoFxClip.getExtraTag()))) {
                  return;
               }

               long fxClipOutPoint = videoFxClip.getOutPoint();

               //如果timelineFx的outPoint超过轨道时长，则缩短timelineFx的outPoint，并修改轨道特效的outPoint
               //If the outPoint of timeline Fx exceeds the track duration,
               // shorten the outPoint of timeline Fx and modify the outPoint of the track effect.
               if (fxClipOutPoint > finalTrackOutPoint) {
                  ClipCommand.setOutPoint(videoFxClip, finalTrackOutPoint);
                  TrackVideoFxCommand.changeOutPoint(videoFx, finalTrackOutPoint);
               }
            });
         });
      }
   }

   /**
    * Change timeline fx to target.
    * 修改timeline 特技到指定目标
    *
    *
    * @param timelineVideoFxClip the timeline video fx clip timeline特技
    * @param trackIndex          the track index 轨道index
    * @param clipIndex          the clip index  clip index
    */
   public static boolean changeTimelineFxToTarget(MeicamTimelineVideoFxClip timelineVideoFxClip, int trackIndex, int clipIndex){
      if (timelineVideoFxClip == null) {
         LogUtils.e("Param is invalid !");
         return false;
      }

      MeicamTimeline timeline = EditorEngine.getInstance().getCurrentTimeline();
      if (timeline == null) {
         LogUtils.e("timeline is null !");
         return false;
      }
      String createTag = timelineVideoFxClip.getExtraTag();
      if (trackIndex < 0) {
         if (timelineVideoFxClip.getIntensity() < 1) {
            TimelineFxCommand.setIntensity(timelineVideoFxClip, 1);
         }
         removeMainTrackFxFromTimelineFx(timeline, createTag);
         removePipClipFxFromTimeline(timeline, createTag);
         return true;
      }
      TimelineFxCommand.setIntensity(timelineVideoFxClip, 0);
      MeicamVideoTrack videoTrack = timeline.getVideoTrack(trackIndex);
      if (videoTrack == null) {
         return false;
      }
      if (trackIndex == 0) {
         removePipClipFxFromTimeline(timeline, createTag);

         int fxCount = videoTrack.getVideoFxCount();
         for (int fxIndex = fxCount - 1; fxIndex >= 0; fxIndex--) {
            MeicamTrackVideoFx videoFx = videoTrack.getVideoFx(fxIndex);
            if (videoFx != null) {
               //已经添加的特效，不再添加
               // The special effects that have been added will no longer be added.
               if (videoFx.hasTag(createTag)) {
                  return false;
               }
            }
         }

         //主轨道 Main track
         MeicamTrackVideoFx trackVideoFx = addTimelineFxToVideoTrack(videoTrack, timelineVideoFxClip);
         return trackVideoFx != null;
      } else {
         removeMainTrackFxFromTimelineFx(timeline, createTag);
         removePipClipFxFromTimeline(timeline, createTag);
         MeicamVideoClip videoClip = videoTrack.getVideoClip(clipIndex);
         if (videoClip != null) {

            int videoFxCount = videoClip.getVideoFxCount();
            for (int fxIndex = videoFxCount - 1; fxIndex >= 0; fxIndex--) {
               MeicamVideoFx clipVideoFx = videoClip.getVideoFx(fxIndex);
               //已经添加的特效，不再添加
               // The special effects that have been added will no longer be added.
               if (clipVideoFx.hasTag(createTag)) {
                  return false;
               }
            }
            MeicamVideoFx videoFx = addTimelineFxToVideoClip(videoClip, timelineVideoFxClip);
            return videoFx != null;
         }
      }
      return false;
   }

   @Nullable
   private static MeicamVideoFx addTimelineFxToVideoClip(MeicamVideoClip videoClip, MeicamTimelineVideoFxClip timelineVideoFxClip) {
      MeicamVideoFx videoFx = VideoClipCommand.appendFx(videoClip, NvsConstants.TYPE_RAW_PACKAGE, SUB_TYPE_FROM_TIMELINE, timelineVideoFxClip.getDesc());
      if (videoFx != null) {
         VideoFxCommand.bindTimelineFxTag(videoFx, timelineVideoFxClip.getExtraTag());
         long fxInPoint = timelineVideoFxClip.getInPoint();
         long fxOutPoint = timelineVideoFxClip.getOutPoint();
         changeFxInAndOutPointInClip(videoClip, videoFx, fxInPoint, fxOutPoint);
      }
      return videoFx;
   }

   private static void removeMainTrackFxFromTimelineFx(@NonNull MeicamTimeline timeline, String createTag) {
      eachMainTrack(timeline, (videoTrack, trackVideoFx) -> {
         if (trackVideoFx != null && trackVideoFx.hasTag(createTag)) {
            VideoTrackCommand.removeTrackVideoFx(videoTrack, trackVideoFx.getInPoint());
         }
      });
   }

   private static void removePipClipFxFromTimeline(@NonNull MeicamTimeline meicamTimeline, String createTag) {
      eachPipTrack(meicamTimeline, (videoClip, videoFx) -> {
         if (videoFx != null && videoFx.hasTag(createTag)) {
            VideoClipCommand.removeFx(videoClip, videoFx);
         }
      });
   }

   /**
    * Update timeline fx target in and out point.
    * 更新timeline特效作用的目标对象的出入点
    *
    * @param timelineVideoFxClip the timeline video fx clip
    */
   public static void updateTimelineFxTargetInAndOutPoint(MeicamTimelineVideoFxClip timelineVideoFxClip) {
      if (timelineVideoFxClip == null) {
         LogUtils.e("TimelineVideoFxClip is null!");
         return;
      }
      MeicamTimeline timeline = EditorEngine.getInstance().getCurrentTimeline();
      if (timeline == null) {
         LogUtils.e("Timeline is null!");
         return;
      }
      String createTag = timelineVideoFxClip.getExtraTag();
      if (TextUtils.isEmpty(createTag)) {
         LogUtils.e("The createTag is null!");
         return;
      }

      if (timelineVideoFxClip.getIntensity() == 1) {
         return;
      }

      long timelineFxInPoint = timelineVideoFxClip.getInPoint();
      long timelineFxOutPoint = timelineVideoFxClip.getOutPoint();

      eachMainTrack(timeline, (videoTrack, videoFx) -> {
         if (videoFx != null && videoFx.hasTag(createTag)) {
            TrackVideoFxCommand.changeOutPoint(videoFx, timelineFxOutPoint);
            TrackVideoFxCommand.changeInPoint(videoFx, timelineFxInPoint);
         }
      });

      eachPipTrack(timeline, (videoClip, videoFx) -> {
         if (videoFx != null && videoFx.hasTag(createTag)) {
            changeFxInAndOutPointInClip(videoClip, videoFx, timelineFxInPoint, timelineFxOutPoint);
         }
      });
   }

   /**
    * Update timeline fx target when video clip drag.
    * Video clip 拖动时，更新timelineFx绑定的目标的出入点
    *
    * @param dragClip the drag clip
    */
   public static void updateTimelineFxTargetVideoClipInPip(MeicamVideoClip dragClip) {
      if (dragClip == null) {
         LogUtils.d("Removed clip is null!");
         return;
      }
      eachVideoFx(dragClip, videoFx -> {
         MeicamTimelineVideoFxClip timelineVideoFxClip = findTimelineFxByTag(videoFx.getTimelineFxTag());
         if (timelineVideoFxClip != null) {
            changeFxInAndOutPointInClip(dragClip, videoFx, timelineVideoFxClip.getInPoint(), timelineVideoFxClip.getOutPoint());
         }
      });
   }

   /**
    * Update timeline fx target when video clip drag.
    * Video clip 拖动时，更新timelineFx绑定的目标的出入点
    *
    * @param dragClip the drag clip
    * @param offset the offset of inPoint and outPoint
    */
   public static void updateTimelineFxTargetVideoClipInMain(MeicamVideoClip dragClip, long offset) {
      if (dragClip == null) {
         LogUtils.d("Removed clip is null!");
         return;
      }
      MeicamTimeline timeline = EditorEngine.getInstance().getCurrentTimeline();
      if (timeline == null) {
         LogUtils.d("Timeline clip is null!");
         return;
      }
      long clipInPoint = dragClip.getInPoint();
      //这里的outPoint是变化前的值 The outPoint here is the value before the change.
      long clipOutPoint = dragClip.getOutPoint() - offset;

      eachMainTrack(timeline, (videoTrack, videoFx) -> {
         if (videoFx == null) {
            return;
         }
         MeicamTimelineVideoFxClip timelineVideoFxClip = findTimelineFxByTag(videoFx.getTimelineFxTagSet());
         if (timelineVideoFxClip != null) {
            long fxClipInPoint = timelineVideoFxClip.getInPoint();
            long fxClipOutPoint = timelineVideoFxClip.getOutPoint();
            if (fxClipInPoint <= clipOutPoint && fxClipInPoint >= clipInPoint) {
               //左边界：将要超过clip的inPoint处
               //右边界：将要超过clip的OutPoint处
               //左把手向右移动时，分两种情况：
               // 1、没有移动到右边界位置时，长度缩短，缩短距离为offset， 即fx的InPoint不变，outPoint减少offset的长度
               // 2、移动到右边界位置后，长度不再缩短，需要修改fx的InPoint， fx的InPoint即clip的OutPoint
               //右把手移动时，分两种情况
               //1、offset缩短，
               // （1）没有移动到右边界位置时，fx也缩短， 即fx的InPoint不变，outPoint减少offset的长度
               // （2）移动到右边界位置后，长度不再缩短，需要修改fx的InPoint， fx的InPoint即clip的OutPoint
               // 2、offset延长，fx长度和位置不变

               //Left boundary: At the inPoint that will exceed the clip
               //Right boundary: At the OutPoint that will exceed the clip
               // When the left handle moves to the right, there are two situations:
               // 1. When it does not move to the right boundary position, the length is shortened to offset,
               // which means that the InPoint of fx remains unchanged, while the OutPoint decreases the length
               // of the offset.
               // 2. After moving to the right boundary position, the length will no longer be shortened.
               // It is necessary to modify the InPoint of fx, which is the OutPoint of the clip.
               // When the right handle is moved, there are two situations
               // 1. Offset shortening,
               // (1) When it does not move to the right boundary position, fx also shortens,
               // meaning that the InPoint of fx remains unchanged, while the OutPoint reduces
               // the length of the offset.
               // (2) After moving to the right boundary position, the length will no longer be shortened.
               // It is necessary to modify the InPoint of fx, which is the OutPoint of the clip.
               // 2. Offset extension, with the length and position of fx unchanged.

               LogUtils.d("updateTimelineFxTargetVideoClipInMain: offset = "+offset );

               if (clipOutPoint == fxClipInPoint) {
                  long inPoint = clipOutPoint + offset;
                  ClipCommand.setInPoint(timelineVideoFxClip, inPoint);
                  long outPoint = inPoint + (fxClipOutPoint - fxClipInPoint);
                  ClipCommand.setOutPoint(timelineVideoFxClip, outPoint);
                  LogUtils.d( "The timeline fx is in the end of videoClip out point: in = "+ inPoint + ", out = "+outPoint);
                  TrackVideoFxCommand.changeInPoint(videoFx, inPoint);
                  TrackVideoFxCommand.changeOutPoint(videoFx, outPoint);
               } else {
                  long offsetValue = -offset;
                  long maxDurationChangeValue = clipOutPoint - fxClipInPoint;
                  long durationChangeValue = offsetValue;
                  if (durationChangeValue > maxDurationChangeValue) {
                     durationChangeValue = maxDurationChangeValue;
                  }
                  long inChange = offsetValue - durationChangeValue;
                  long fxInPointValue = fxClipInPoint - inChange;
                  long fxDuration = fxClipOutPoint - fxClipInPoint - durationChangeValue;
                  ClipCommand.setInPoint(timelineVideoFxClip, fxInPointValue);
                  ClipCommand.setOutPoint(timelineVideoFxClip, fxInPointValue + fxDuration);

                  TrackVideoFxCommand.changeInPoint(videoFx, fxInPointValue);
                  TrackVideoFxCommand.changeOutPoint(videoFx, fxInPointValue + fxDuration);

                  LogUtils.d("The timeline fx can be shorted: in = "+ fxInPointValue + ", out = "+(fxInPointValue + fxDuration));
               }
            }
         }
      });
   }



   /**
    * Update timeline fx target when video clip drag.
    * Video clip 拖动时，更新timelineFx绑定的目标的出入点
    *
    * @param from the from index
    * @param to the to index
    */
   public static void updateTimelineFxTargetVideoClipInMain(int from, int to) {

      MeicamTimeline timeline = EditorEngine.getInstance().getCurrentTimeline();
      if (timeline == null) {
         LogUtils.d("Timeline clip is null!");
         return;
      }
      MeicamVideoTrack videoTrack = timeline.getVideoTrack(MAIN_TRACK_INDEX);
      if (videoTrack == null) {
         LogUtils.d("VideoTrack clip is null!");
         return;
      }
      MeicamVideoClip fromVideoClip = videoTrack.getVideoClip(from);
      MeicamVideoClip toVideoClip = videoTrack.getVideoClip(to);
      if (fromVideoClip == null || toVideoClip == null) {
         LogUtils.d("Param is invalid !");
         return;
      }
      long fromVideoClipInPoint = fromVideoClip.getInPoint();
      long fromVideoClipOutPoint = fromVideoClip.getOutPoint();
      long toVideoClipInPoint = toVideoClip.getInPoint();
      long toVideoClipOutPoint = toVideoClip.getOutPoint();

      long fromDuration = fromVideoClipInPoint - fromVideoClipOutPoint;

      long distance;
      long startPosition;
      long endPosition;
      boolean isToFont = from > to;
      if (isToFont) {
         //从后往前移动,当前位置的videoClip绑定的timelineFx向前移动到to的位置，to到from中间的向后移动当前videoClip的长度
         // Moving from back to front, the timeline Fx bound to the video clip at the current position is moved
         // forward to the position of to, and the length of the current video clip is moved backwards from to in the middle.
         //这里distance 是个负值 Here, distance is a negative value
         distance = toVideoClipInPoint - fromVideoClipInPoint;
         startPosition = toVideoClipInPoint;
         endPosition = fromVideoClipInPoint;
      } else {
         //从前往后移动，当前位置的videoClip绑定的timelineFx向后移动到to的位置，from到to中间的向前移动当前videoClip的长度
         // Moving from front to back, the timeline Fx bound to the current position of the videoClip is moved
         // backwards to the position of to, and the length of the current videoClip is moved forward
         // in the middle from to.
         distance = toVideoClipOutPoint - fromVideoClipOutPoint;
         startPosition = fromVideoClipOutPoint;
         endPosition = toVideoClipOutPoint;
      }

      long trackDuration = videoTrack.getDuration();
      List<MeicamTimelineVideoFxClip> data = new ArrayList<>();
      eachMainTrack(timeline, (videoTrack1, trackVideoFx) -> {
         if (trackVideoFx == null) {
            return;
         }
         MeicamTimelineVideoFxClip timelineFxByTag = findTimelineFxByTag(trackVideoFx.getTimelineFxTagSet());
         if (timelineFxByTag != null) {
            long fxClipInPoint = timelineFxByTag.getInPoint();
            if ((fxClipInPoint >= startPosition && fxClipInPoint <= endPosition)
                    || (fxClipInPoint >= fromVideoClipInPoint && fxClipInPoint <= fromVideoClipOutPoint)) {
               data.add(timelineFxByTag);
            }
         }
      });
      Collections.sort(data, (o1, o2) -> (int) (o2.getInPoint() - o1.getInPoint()));

      long deltaPoint = 0;
      for (MeicamTimelineVideoFxClip datum : data) {

         MeicamTrackVideoFx videoFx = videoTrack.getVideoFx(datum.getInPoint());
         if (videoFx == null) {
            continue;
         }
         long fxClipInPoint = videoFx.getInPoint();
         long fxClipOutPoint = videoFx.getOutPoint();
         LogUtils.d( "updateTimelineFxTargetVideoClipInMain: fxClipInPoint = "+fxClipInPoint + ", fxClipOutPoint = "+fxClipOutPoint);
         if (fxClipInPoint >= startPosition && fxClipInPoint < endPosition) {
            deltaPoint = isToFont ? -fromDuration : fromDuration;
         }

         if (fxClipInPoint >= fromVideoClipInPoint && fxClipInPoint < fromVideoClipOutPoint) {
            deltaPoint = distance;
         }

         long inPoint = fxClipInPoint + deltaPoint;
         long outPoint = fxClipOutPoint + deltaPoint;
         LogUtils.d( "updateTimelineFxTargetVideoClipInMain: inPoint = "+inPoint + ", outPoint = "+outPoint + ", deltaPoint = "+deltaPoint);

         if (outPoint > trackDuration) {
            outPoint = trackDuration;
         }
         TrackVideoFxCommand.changeOutPoint(videoFx, outPoint);
         TrackVideoFxCommand.changeInPoint(videoFx, inPoint);
         ClipCommand.setOutPoint(datum, outPoint);
         ClipCommand.setInPoint(datum, inPoint);

      }
   }

   private static void changeFxInAndOutPointInClip(MeicamVideoClip videoClip, MeicamVideoFx videoFx, long fxInPoint, long fxOutPoint) {
      long clipInPoint = videoClip.getInPoint();
      long fxDuration = fxOutPoint - fxInPoint;
      long inPoint = 0;
      if (clipInPoint < fxInPoint){
         inPoint = fxInPoint - clipInPoint;
      } else {
         fxDuration = fxOutPoint - clipInPoint;
      }
      long maxDurationInClip = (videoClip.getOutPoint() - clipInPoint) - inPoint;
      if (maxDurationInClip < fxDuration) {
         fxDuration = maxDurationInClip;
      }
      VideoFxCommand.changeOutPoint(videoFx, inPoint + fxDuration);
      VideoFxCommand.changeInPoint(videoFx, inPoint);
   }

   private static MeicamTrackVideoFx addTimelineFxToVideoTrack(MeicamVideoTrack videoTrack, MeicamTimelineVideoFxClip timelineFx) {
      MeicamTrackVideoFx trackVideoFx = VideoTrackCommand.appendTrackVideoFx(videoTrack, timelineFx.getClipType(),
              timelineFx.getDesc(), timelineFx.getInPoint(), timelineFx.getOutPoint() - timelineFx.getInPoint(), NvsVideoTrack.TRACK_ADD_VIDEO_FX_FLAGS_RENDER_AT_CLIP_RAW_FILTER, System.nanoTime() + "");
      if (trackVideoFx != null) {
         TrackVideoFxCommand.bindTimelineFxTag(trackVideoFx, timelineFx.getExtraTag());
      }
      return trackVideoFx;
   }

   public static void updateTimelineFxTargetWhenVideoClipRemoved(MeicamVideoClip removedClip) {
      if (removedClip == null) {
         LogUtils.d("Removed clip is null!");
         return;
      }
      MeicamTimeline timeline = EditorEngine.getInstance().getCurrentTimeline();
      if (timeline == null) {
         LogUtils.e("Timeline is null!");
         return;
      }
      int trackIndex = removedClip.getTrackIndex();
      long removedClipOutPoint = removedClip.getOutPoint();
      long removedClipInPoint = removedClip.getInPoint();
      long trackOutPoint = removedClipOutPoint;
      if (trackIndex == MAIN_TRACK_INDEX) {

         MeicamVideoTrack videoTrack = timeline.getVideoTrack(trackIndex);
         if (videoTrack != null) {
            MeicamVideoClip videoClip = videoTrack.getVideoClip(videoTrack.getClipCount() - 1);
            if (videoClip != null) {
               trackOutPoint = videoClip.getOutPoint();
            }
         }
         long finalTrackOutPoint = trackOutPoint;
         eachMainTrack(timeline, (videoTrack1, videoFx) -> {
            if (videoFx == null) {
               return;
            }
            eachTimelineFx(timeline, (timelineFxTrack, videoFxClip) -> {
               if (videoFxClip == null || (!videoFx.hasTag(videoFxClip.getExtraTag()))) {
                  return;
               }
               //如果删除的videoClip是主轨clip，并且主轨是timelineFx的作用对象
               //如果timelineFx的inPoint处于删除的videoClip区间范围，需要删除timelineFx
               // If the deleted videoClip is the main track clip and the main track is the target of timeline Fx
               //If the inPoint of timeline Fx is within the deleted videoClip range,
               // timeline Fx needs to be deleted.
               long fxClipInPoint = videoFxClip.getInPoint();
               long fxClipOutPoint = videoFxClip.getOutPoint();
               if (fxClipInPoint < removedClipOutPoint && fxClipInPoint >= removedClipInPoint) {
                  TimelineVideoFxTrackCommand.removeClip(timelineFxTrack, videoFxClip);
                  VideoTrackCommand.removeTrackVideoFx(videoTrack1, videoFx.getInPoint());
               }
               //如果timelineFx的outPoint超过轨道时长，则缩短timelineFx的outPoint，并修改轨道特效的outPoint
               //If the outPoint of timeline Fx exceeds the track duration,
               // shorten the outPoint of timeline Fx and modify the outPoint of the track effect.
               if (fxClipOutPoint > finalTrackOutPoint) {
                  ClipCommand.setOutPoint(videoFxClip, finalTrackOutPoint);
                  TrackVideoFxCommand.changeOutPoint(videoFx, finalTrackOutPoint);
               }
            });
         });
      } else {

         //如果videoClip处于画中画轨道，直接删除和videoClip关联的timelineFx
         // If the videoClip is in the picture in picture track,
         // directly delete the timeline Fx associated with the videoClip.
         eachVideoFx(removedClip, videoFx -> eachTimelineFx(timeline, (videoFxTrack, timelineVideoFxClip) -> {
            if (timelineVideoFxClip != null && videoFx.hasTag(timelineVideoFxClip.getExtraTag())) {
               TimelineVideoFxTrackCommand.removeClip(videoFxTrack, timelineVideoFxClip);
            }
         }));
      }
   }

   private static void eachTimelineFx(MeicamTimeline timeline, Func2<MeicamTimelineVideoFxTrack,MeicamTimelineVideoFxClip> func2) {
      int trackCount = timeline.getTimelineFxTrackCount();
      for (int fxTrackIndex = trackCount - 1; fxTrackIndex >= 0; fxTrackIndex--) {
         MeicamTimelineVideoFxTrack timelineFxTrack = timeline.getTimelineFxTrack(fxTrackIndex);
         if (timelineFxTrack == null) {
            continue;
         }
         int clipCount = timelineFxTrack.getClipCount();
         for (int index = clipCount - 1; index >= 0; index--) {
            func2.exe(timelineFxTrack, timelineFxTrack.getClip(index));
         }
      }
   }

   private static void eachMainTrack(MeicamTimeline timeline, @NonNull Func2<MeicamVideoTrack, MeicamTrackVideoFx> func) {
      MeicamVideoTrack videoTrack = timeline.getVideoTrack(CommonData.MAIN_TRACK_INDEX);
      if (videoTrack != null) {
         int videoFxCount = videoTrack.getVideoFxCount();
         for (int index = videoFxCount - 1; index >= 0; index--) {
            func.exe(videoTrack, videoTrack.getVideoFx(index));
         }
      }
   }

   private static void eachPipTrack(MeicamTimeline timeline, @NonNull Func2<MeicamVideoClip, MeicamVideoFx> func) {
      int trackCount = timeline.videoTrackCount();
      if (trackCount > 1) {
         for (int trackIndex = 1; trackIndex < trackCount; trackIndex++) {
            MeicamVideoTrack track = timeline.getVideoTrack(trackIndex);
            if (track == null) {
               continue;
            }
            int clipCount = track.getClipCount();
            for (int clipIndex = 0; clipIndex < clipCount; clipIndex++) {
               MeicamVideoClip videoClip = track.getVideoClip(clipIndex);
               if (videoClip == null) {
                  continue;
               }
               int videoFxCount = videoClip.getVideoFxCount();
               for (int index = videoFxCount - 1; index >= 0; index--) {
                  func.exe(videoClip, videoClip.getVideoFx(index));
               }
            }
         }
      }
   }

   private static void eachVideoFx(MeicamVideoClip videoClip, @NonNull Func<MeicamVideoFx> func) {
      int videoFxCount = videoClip.getVideoFxCount();
      for (int index = videoFxCount - 1; index >= 0; index--) {
         func.exe(videoClip.getVideoFx(index));
      }
   }

   private static MeicamTimelineVideoFxClip findTimelineFxByTag(Set<String> tagSet) {
      MeicamTimeline currentTimeline = EditorEngine.getInstance().getCurrentTimeline();
      if (currentTimeline == null) {
         return null;
      }
      if (tagSet == null) {
         return null;
      }
      int timelineFxTrackCount = currentTimeline.getTimelineFxTrackCount();
      for (int trackIndex = 0; trackIndex < timelineFxTrackCount; trackIndex++) {
         MeicamTimelineVideoFxTrack timelineFxTrack = currentTimeline.getTimelineFxTrack(trackIndex);
         if (timelineFxTrack == null) {
            continue;
         }
         int clipCount = timelineFxTrack.getClipCount();
         for (int index = 0; index < clipCount; index++) {
            MeicamTimelineVideoFxClip clip = timelineFxTrack.getClip(index);
            if (clip != null && tagSet.contains(clip.getExtraTag())) {
               return clip;
            }
         }
      }
      return null;
   }



   /**
    * The interface Func.
    * 方法接口
    *
    * @param <T> the type parameter 方法参数
    */
   public interface Func<T>{
      /**
       * Exe.
       *
       * @param t the t
       */
      void exe(T t);
   }

   /**
    * The interface Func.
    * 方法接口2
    *
    * @param <T> the type parameter 方法参数
    * @param <T2> the type parameter2 方法参数2
    */
   public interface Func2<T, T2>{
      /**
       * Exe.
       *
       * @param t the t
       * @param t2 the t2
       */
      void exe(T t, T2 t2);
   }
}
