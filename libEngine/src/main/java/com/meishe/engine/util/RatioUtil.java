package com.meishe.engine.util;

import com.meishe.engine.bean.template.RatioInfo;
import com.meishe.engine.constant.NvsConstants;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * All rights Reserved, Designed By www.meishesdk.com
 *
 * @Author: LiFei
 * @CreateDate: 2021/1/13 16:16
 * @Description:
 * @Copyright: www.meishesdk.com Inc. All rights reserved.
 */
public class RatioUtil {
    public static String COLON_TAG = ":";
    public static String V_TAG = "v";
    private static int[] ASPECTRATIOS = {NvsConstants.AspectRatio.AspectRatio_16v9,
            NvsConstants.AspectRatio.AspectRatio_1v1,
            NvsConstants.AspectRatio.AspectRatio_9v16,
            NvsConstants.AspectRatio.AspectRatio_4v3,
            NvsConstants.AspectRatio.AspectRatio_3v4,
            NvsConstants.AspectRatio.AspectRatio_18v9,
            NvsConstants.AspectRatio.AspectRatio_9v18,
            NvsConstants.AspectRatio.AspectRatio_21v9,
            NvsConstants.AspectRatio.AspectRatio_9v21};
    private static String[] ASPECTRATIOSTR = {"16v9", "1v1", "9v16", "4v3", "3v4", "18v9", "9v18", "21v9", "9v21"};

    public static String getAspectRatioStr(int rawValue, String defaultValue) {
        String str = defaultValue;
        if (rawValue == NvsConstants.AspectRatio.AspectRatio_16v9) {
            str = "16:9";
        } else if (rawValue == NvsConstants.AspectRatio.AspectRatio_1v1) {
            str = "1:1";
        } else if (rawValue == NvsConstants.AspectRatio.AspectRatio_9v16) {
            str = "9:16";
        } else if (rawValue == NvsConstants.AspectRatio.AspectRatio_4v3) {
            str = "4:3";
        } else if (rawValue == NvsConstants.AspectRatio.AspectRatio_3v4) {
            str = "3:4";
        } else if (rawValue == NvsConstants.AspectRatio.AspectRatio_18v9) {
            str = "18:9";
        } else if (rawValue == NvsConstants.AspectRatio.AspectRatio_9v18) {
            str = "9:18";
        } else if (rawValue == NvsConstants.AspectRatio.AspectRatio_21v9) {
            str = "21:9";
        } else if (rawValue == NvsConstants.AspectRatio.AspectRatio_9v21) {
            str = "9:21";
        }
        return str;
    }

    public static int getAspectRatio(String ratios) {
        int index = Arrays.asList(ASPECTRATIOSTR).indexOf(ratios);
        return (index < 0) ? 1 : ASPECTRATIOS[index];
    }


    public static List<RatioInfo> getSupportedAspectRatios(int defaultRatio, int supportedAspectRatio) {
        List<RatioInfo> ratios = new ArrayList<>();
        for (int indexRatio : ASPECTRATIOS) {
            if ((indexRatio & supportedAspectRatio) != 0) {
                RatioInfo ratioInfo = new RatioInfo();
                String ratioStr = getAspectRatioStr(indexRatio, "16:9");
                ratioInfo.setTag(indexRatio);
                ratioInfo.setName((defaultRatio == indexRatio) ? ratioStr + "(默认)" : ratioStr);
                ratios.add(ratioInfo);
            }
        }
        return ratios;
    }

    /**
     * get a ratio from aspect ratios
     * 从支持的比例中找一个比例
     *
     * @param supportedAspectRatio 支持的比例 the supported aspect ratio
     * @return 比例 the ratio
     */
    public static int getARatioFromAspectRatios(int supportedAspectRatio) {
        for (int indexRatio : ASPECTRATIOS) {
            if ((indexRatio & supportedAspectRatio) != 0) {
                return indexRatio;
            }
        }
        return ASPECTRATIOS[0];
    }

}
