package com.meishe.engine.util;

import java.security.MessageDigest;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2021/10/20 13:54
 * @Description :加密工具 Encryption tool
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class Encryption {

    /**
     * 将字符串转成MD5值
     * Convert string to MD5
     *
     * @param string the string
     * @return the value
     */
    public static String stringToMd5(String string) {
        byte[] hash;

        try {
            hash = MessageDigest.getInstance("MD5").digest(string.getBytes("UTF-8"));
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
        StringBuilder hex = new StringBuilder(hash.length * 2);
        for (byte b : hash) {
            if ((b & 0xFF) < 0x10) {
                hex.append("0");
            }
            hex.append(Integer.toHexString(b & 0xFF));
        }

        return hex.toString();
    }
}
