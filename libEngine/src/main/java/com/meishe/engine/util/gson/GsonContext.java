package com.meishe.engine.util.gson;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.meishe.base.utils.LogUtils;
import com.meishe.engine.bean.ClipInfo;
import com.meishe.engine.bean.CommonData;
import com.meishe.engine.bean.MeicamAudioClip;
import com.meishe.engine.bean.MeicamAudioTrack;
import com.meishe.engine.bean.MeicamCaptionClip;
import com.meishe.engine.bean.MeicamCompoundCaptionClip;
import com.meishe.engine.bean.MeicamStickerCaptionTrack;
import com.meishe.engine.bean.MeicamStickerClip;
import com.meishe.engine.bean.MeicamTimelineVideoFxClip;
import com.meishe.engine.bean.MeicamTimelineVideoFxTrack;
import com.meishe.engine.bean.MeicamVideoClip;
import com.meishe.engine.bean.MeicamVideoTrack;
import com.meishe.engine.bean.TrackInfo;
import com.meishe.engine.bean.MeicamBackgroundStory;
import com.meishe.engine.bean.MeicamStoryboardInfo;

import java.lang.reflect.Type;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2020/7/3 19:25
 * @Description :特殊的Gson上下文，里面注册了用于适配的TypeAdapter
 * Special gson context, which registers the TypeAdapter for adaptation
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class GsonContext {
    private volatile static GsonContext sGsonContext;
    private Gson mGson;

    private GsonContext() {
    }

    public static GsonContext getInstance() {
        if (sGsonContext == null) {
            synchronized (GsonContext.class) {
                if (sGsonContext == null) {
                    sGsonContext = new GsonContext();
                }
            }
        }
        return sGsonContext;
    }

    private Gson getGson() {
        if (mGson == null) {
            RuntimeTypeAdapterFactory<TrackInfo> trackFactory = RuntimeTypeAdapterFactory.of(TrackInfo.class, "base")
                    .registerSubtype(MeicamAudioTrack.class, CommonData.TRACK_AUDIO)
                    .registerSubtype(MeicamVideoTrack.class, CommonData.TRACK_VIDEO)
                    .registerSubtype(MeicamStickerCaptionTrack.class, CommonData.TRACK_STICKER_CAPTION)
                    .registerSubtype(MeicamTimelineVideoFxTrack.class, CommonData.TRACK_TIMELINE_FX);
            RuntimeTypeAdapterFactory<ClipInfo> clipFactory = RuntimeTypeAdapterFactory.of(ClipInfo.class, "base")
                    .registerSubtype(MeicamAudioClip.class, CommonData.CLIP_AUDIO)
                    .registerSubtype(MeicamVideoClip.class, CommonData.CLIP_VIDEO)
                    .registerSubtype(MeicamCaptionClip.class, CommonData.CLIP_CAPTION)
                    .registerSubtype(MeicamCompoundCaptionClip.class, CommonData.CLIP_COMPOUND_CAPTION)
                    .registerSubtype(MeicamStickerClip.class, CommonData.CLIP_STICKER)
                    .registerSubtype(MeicamTimelineVideoFxClip.class, CommonData.CLIP_TIMELINE_FX);
            RuntimeTypeAdapterFactory<MeicamStoryboardInfo> storyboardFactory = RuntimeTypeAdapterFactory.of(MeicamStoryboardInfo.class, "Storyboard")
                    .registerSubtype(MeicamBackgroundStory.class, "BackgroundStory")
                    .registerSubtype(MeicamStoryboardInfo.class, "Storyboard");
            mGson = new GsonBuilder()
                    .registerTypeAdapterFactory(trackFactory)
                    .registerTypeAdapterFactory(clipFactory)
                    .registerTypeAdapterFactory(storyboardFactory)
                    .serializeSpecialFloatingPointValues()
                    .create();
        }
        return mGson;
    }

    /**
     * This method serializes the specified object into its equivalent Json representation.
     * <p>
     * 将对象序列化
     *
     * @param obj 需要序列化的对象 the object for which Json representation is to be created setting for Gson.
     * @return Json描述 Json representation
     */
    public String toJson(Object obj) {
        String data = null;
        try {
            data = getGson().toJson(obj);
        } catch (Exception e) {
            LogUtils.e(e);
        }
        return data;
    }

    /**
     * This method deserializes the specified Json into an object of the specified type.
     * <p>
     * 反序列化json为指定对象
     *
     * @param <T>      执行对象的类型 the type of the desired object
     * @param json     需要反序列化的json串 the string from which the object is to be deserialized
     * @param classOfT 对象的Class.The specific genericized type of src. You can obtain this type by using the
     *                 {@link com.google.gson.reflect.TypeToken} class. For example, to get the type for
     *                 {@code Collection<Foo>}, you should use:
     *                 <pre>
     *                                 Type typeOfT = new TypeToken&lt;Collection&lt;Foo&gt;&gt;(){}.getType();
     *                                 </pre>
     * @return an object of type T from the string. Returns {@code null} if {@code json} is {@code null}.
     */
    public <T> T fromJson(String json, Class<T> classOfT) {
        T result = null;
        try {
            result = getGson().fromJson(json, (Type) classOfT);
        } catch (Exception e) {
            LogUtils.e(e);
        }
        return result;
    }
}
