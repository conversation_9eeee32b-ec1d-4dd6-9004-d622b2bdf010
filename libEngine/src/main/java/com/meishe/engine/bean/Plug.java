package com.meishe.engine.bean;

import com.meishe.base.utils.Utils;

import java.io.Serializable;

/**
 * All rights Reserved, Designed By www.meishesdk.com
 *
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2022/6/16 15:09
 * @Description: 特效插件 The effect plug
 * @Copyright: www.meishesdk.com Inc. All rights reserved.
 */
public class Plug implements Serializable {
    private String name;
    private String enName;
    public String plugName;
    private int trackIndex;
    private int clipIndex;
    private long inPoint;
    public String effectPath;
    public String coverPath;
    private String plugDesc;
    private String plugDescEn;
    private boolean isDisplay = true;

    public int getTrackIndex() {
        return trackIndex;
    }

    public void setTrackIndex(int trackIndex) {
        this.trackIndex = trackIndex;
    }

    public int getClipIndex() {
        return clipIndex;
    }

    public void setClipIndex(int clipIndex) {
        this.clipIndex = clipIndex;
    }

    public long getInPoint() {
        return inPoint;
    }

    public void setInPoint(long inPoint) {
        this.inPoint = inPoint;
    }

    public boolean isDisplay() {
        return isDisplay;
    }

    public void setDisplay(boolean display) {
        isDisplay = display;
    }

    public String getPlugDesc() {
        return Utils.isZh() ? plugDesc : plugDescEn;
    }

    public String getName() {
        return Utils.isZh() ? name : enName;
    }
}
