package com.meishe.engine.bean;

import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.meicam.sdk.NvsAudioClip;
import com.meicam.sdk.NvsAudioFx;
import com.meicam.sdk.NvsObject;
import com.meicam.sdk.NvsVolume;
import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.LogUtils;
import com.meishe.engine.adapter.TimelineDataParserAdapter;
import com.meishe.engine.adapter.parser.IResourceParser;
import com.meishe.engine.bean.bridges.FileInfoBridge;
import com.meishe.engine.constant.NvsConstants;
import com.meishe.engine.local.LMeicamAudioClip;
import com.meishe.engine.local.LMeicamAudioFx;
import com.meishe.engine.util.DeepCopyUtil;
import com.meishe.engine.util.gson.GsonContext;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import static com.meishe.engine.constant.NvsConstants.TYPE_BUILD_IN;


/**
 * All rights reserved,Designed by www.meishesdk.com
 * 版权所有:www.meishesdk.com
 *
 * <AUTHOR> LiHangZhou
 * @CreateDate :2021/05/20 10:07
 * @Description : 音频片段 The audio clip
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class MeicamAudioClip extends ClipInfo<NvsAudioClip> implements Cloneable, Serializable, TimelineDataParserAdapter<LMeicamAudioClip>, IResourceParser {

    /**
     * 录音文件
     * The record file
     */
    public static final int AUDIO_RECORD_FILE = 1;

    /**
     * 正在录音
     * In record
     */
    public static final int AUDIO_RECORD_ING = 2;

    /**
     * 音乐
     * The music file
     */
    public static final int AUDIO_MUSIC = 3;

    /**
     * 视频提取
     * The audio file from video
     */
    public static final int VIDEO_MUSIC = 4;

    private long id = -1;
    /**
     * 音频文件路径
     * The audio file path
     */
    private String filePath;

    /**
     * 远程路径
     * The remote path of video clip
     */
    private String remotePath;

    /**
     * 左声道波形
     * The url for left channel
     */
    private String leftChannelUrl;

    /**
     * 右声道波形
     * The url for right channel
     */
    private String rightChannelUrl;

    /**
     * 裁入点
     * the trim in point of audio clip
     */
    private long trimIn = 0L;
    /**
     * 裁出点
     * the trim in point of audio clip
     */
    private long trimOut = 0L;
    /**
     * 片段音量值
     * The clip volume
     */
    private float volume = 1f;
    /**
     * 片段变速值
     * The clip speed
     */
    private double speed = 1.0D;
    /**
     * 淡入时长
     * The fade in duration
     */
    private long fadeInDuration;
    /**
     * 淡出时长
     * The fade out duration
     */
    private long fadeOutDuration;
    /**
     * 原始时长
     * The original duration
     */
    private long originalDuration;
    /**
     * 音频类型
     * The audio type
     */
    private int audioType;
    /**
     * 音频名称
     * The audio name
     */
    private String audioName;
    /**
     * 音频特效集合
     * The audio fx list
     */
    private List<MeicamAudioFx> audioFxList = new ArrayList<>();

    private String resourceId;
    /**
     * 是否变调
     * Keep audio pitch or not
     */
    private boolean keepAudioPitch = true;

    private float[] recordArray = new float[0];
    /**
     * 添加顺序
     * The add index
     */
    private int addIndex = 0;

    public MeicamAudioClip() {
        super(null, CommonData.CLIP_AUDIO);
    }

    MeicamAudioClip(NvsAudioClip nvsAudioClip, String audioPath, long inPoint, long trimIn, long trimOut) {
        super(nvsAudioClip, CommonData.CLIP_AUDIO);
        this.filePath = audioPath;
        this.inPoint = inPoint;
        this.trimIn = trimIn;
        this.trimOut = trimOut;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    @Override
    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public void updateInAndOutPoint() {
        NvsAudioClip audioClip = getObject();
        if (audioClip != null) {
            super.setInPoint(audioClip.getInPoint());
            super.setOutPoint(audioClip.getOutPoint());
        }
    }

    @Override
    public void setInPoint(long inPoint) {
        NvsAudioClip audioClip = getObject();
        if (audioClip != null) {
            super.setInPoint(audioClip.getInPoint());
        }
    }

    @Override
    public void setOutPoint(long outPoint) {
        NvsAudioClip audioClip = getObject();
        if (audioClip != null) {
            super.setOutPoint(audioClip.getOutPoint());
        }
    }

    @Override
    public long getTrimIn() {
        return trimIn;
    }

    public void setTrimIn(long trimIn) {
        NvsAudioClip audioClip = getObject();
        if (audioClip != null) {
            this.trimIn = audioClip.changeTrimInPoint(trimIn, false);
        }
    }

    public long getTrimOut() {
        return trimOut;
    }

    public void setTrimOut(long trimOut) {
        setTrimOut(trimOut, false);
    }

    /**
     * Sets trim out.
     *
     * @param trimOut       the trim out trimIn 时间点
     * @param affectSibling true affects other clip in the same track,false not 是否影响同轨道上其他片段(true/false)
     */
    public void setTrimOut(long trimOut, boolean affectSibling) {
        NvsAudioClip audioClip = getObject();
        this.trimOut = trimOut;
        if (audioClip != null) {
            this.trimOut = audioClip.changeTrimOutPoint(trimOut, affectSibling);
        }
    }


    public float getVolume() {
        return volume;
    }

    public boolean isKeepAudioPitch() {
        return keepAudioPitch;
    }

    public void setKeepAudioPitch(boolean keepAudioPitch) {
        NvsAudioClip nvsAudioClip = getObject();
        if (nvsAudioClip != null) {
            nvsAudioClip.changeSpeed(speed, keepAudioPitch);
            this.keepAudioPitch = keepAudioPitch;
        }
    }

    public void setVolume(float volume) {
        if (invalidFloat(volume)) {
            return;
        }
        NvsAudioClip nvsAudioClip = getObject();
        if (nvsAudioClip != null) {
            this.volume = volume;
            nvsAudioClip.setVolumeGain(volume, volume);
        }
    }

    @Override
    public double getSpeed() {
        return speed;
    }

    public void setSpeed(double speed, boolean keepAudioPitch) {
        if (invalidDouble(speed)) {
            return;
        }
        this.speed = speed;
        NvsAudioClip nvsAudioClip = getObject();
        if (nvsAudioClip != null) {
            //速度改变了，修改上层数据
            //The speed has changed. Modify the upper data.
            nvsAudioClip.changeSpeed(speed, keepAudioPitch);
            this.keepAudioPitch = keepAudioPitch;
            super.setInPoint(nvsAudioClip.getInPoint());
            super.setOutPoint(nvsAudioClip.getOutPoint());
        }
    }

    public long getFadeInDuration() {
        return fadeInDuration;
    }

    public void setFadeInDuration(long fadeInDuration) {
        NvsAudioClip nvsAudioClip = getObject();
        if (nvsAudioClip != null) {
            this.fadeInDuration = fadeInDuration;
            nvsAudioClip.setFadeInDuration(fadeInDuration);
        }
    }

    public long getFadeOutDuration() {
        return fadeOutDuration;
    }

    public void setFadeOutDuration(long fadeOutDuration) {
        NvsAudioClip nvsAudioClip = getObject();
        if (nvsAudioClip != null) {
            this.fadeOutDuration = fadeOutDuration;
            nvsAudioClip.setFadeOutDuration(fadeOutDuration);
        }
    }

    public int getAudioType() {
        return audioType;
    }

    public void setAudioType(int mAudioType) {
        this.audioType = mAudioType;
    }

    public String getDrawText() {
        return audioName;
    }

    public void setDrawText(String mDrawText) {
        this.audioName = mDrawText;
    }

    public long getOriginalDuration() {
        return originalDuration;
    }

    public void setOriginalDuration(long originalDuration) {
        this.originalDuration = originalDuration;
    }

    public float[] getRecordArray() {
        return recordArray;
    }

    public void setRecordArray(float[] recordArray) {
        this.recordArray = recordArray;
    }

    public int getAddIndex() {
        return addIndex;
    }

    public void setAddIndex(int addIndex) {
        this.addIndex = addIndex;
    }

    /**
     * 添加特效
     * Add fx
     *
     * @param fxId the fx id 特效标识
     * @return MeicamAudioFx 特效
     */
    public MeicamAudioFx appendAudioFx(String fxId) {
        //每次添加新的先移除旧的
        //Remove the old one before adding the new one
        removeAudioFx(0);
        NvsAudioFx nvsAudioFx = appendNvsAudioFx(fxId);
        if (nvsAudioFx != null) {
            nvsAudioFx.setBooleanVal(NvsConstants.AUDIO_FX_CHANGE_SPEED, false);
            MeicamAudioFx audioFx = createVideoFx(fxId, nvsAudioFx);
            audioFxList.add(audioFx);
            return audioFx;
        }
        return null;
    }

    private MeicamAudioFx createVideoFx(String fxId, NvsAudioFx nvsVideoFx) {
        return new MeicamAudioFx(nvsVideoFx, 0, TYPE_BUILD_IN, fxId);
    }

    /**
     * 添加底层特效
     * Append audio fx
     *
     * @param fxId the fx id 特效标识
     * @return NvsVideoFx the fx 底层特效
     */
    private NvsAudioFx appendNvsAudioFx(String fxId) {
        NvsAudioClip audioClip = getObject();
        if (audioClip == null) {
            return null;
        }
        return audioClip.appendFx(fxId);
    }

    /**
     * 根据特效id获取特效
     * Get audio fx by type
     *
     * @param fxId the fx id 特效id
     * @return MeicamAudioFx the fx 特效
     */
    public MeicamAudioFx getAudioFxById(String fxId) {
        if (TextUtils.isEmpty(fxId)) {
            return null;
        }
        for (MeicamAudioFx audioFx : audioFxList) {
            if (fxId.equals(audioFx.getDesc())) {
                return audioFx;
            }
        }
        return null;
    }

    /**
     * 根据特效索引获取特效
     * Get audio fx by index
     *
     * @param index the fx index 特效索引
     * @return MeicamAudioFx the fx 特效
     */
    public MeicamAudioFx getAudioFx(int index) {
        if (!CommonUtils.isIndexAvailable(index, audioFxList)) {
            return null;
        }
        for (MeicamAudioFx audioFx : audioFxList) {
            if (audioFx.getIndex() == index) {
                return audioFx;
            }
        }
        return null;
    }

    /**
     * 获取音轨特效数量
     * Get audio fx count
     *
     * @return the audio fx count
     */
    public int getAudioFxCount() {
        return audioFxList == null ? 0 : audioFxList.size();
    }

    /**
     * 删除特效 remove audio fx
     *
     * @return audioFx removed
     */
    public MeicamAudioFx removeAudioFx(String fxId) {
        MeicamAudioFx audioFx = getAudioFxById(fxId);
        if (audioFx != null) {
            if (audioFx.getObject() != null) {
                getObject().removeFx(audioFx.getObject().getIndex());
                audioFxList.remove(audioFx);
            } else {
                LogUtils.e("remove audio fx failed!!!");
            }
        }
        return audioFx;
    }

    /**
     * 删除特效 remove audio fx
     *
     * @return audioFx removed
     */
    public MeicamAudioFx removeAudioFx(int index) {
        MeicamAudioFx audioFx = getAudioFx(index);
        if (audioFx != null) {
            if (audioFx.getObject() != null) {
                getObject().removeFx(audioFx.getObject().getIndex());
                audioFxList.remove(audioFx);
            } else {
                LogUtils.e("remove audio fx failed!!!");
            }
        }
        return audioFx;
    }

    /**
     * 删除所有特效
     * remove all audio fx
     */
    public void removeAllAudioFx() {
        NvsAudioClip nvsAudioClip = getObject();
        if (nvsAudioClip != null) {
            for (MeicamAudioFx audioFx : audioFxList) {
                NvsAudioFx nvsAudioFx = audioFx.getObject();
                if (nvsAudioFx != null) {
                    nvsAudioClip.removeFx(nvsAudioFx.getIndex());
                }
            }
            audioFxList.clear();
        }
    }

    public String getRemotePath() {
        return remotePath;
    }

    public void setRemotePath(String remotePath) {
        this.remotePath = remotePath;
    }

    public String getLeftChannelUrl() {
        return leftChannelUrl;
    }

    public void setLeftChannelUrl(String leftChannelUrl) {
        this.leftChannelUrl = leftChannelUrl;
    }

    public String getRightChannelUrl() {
        return rightChannelUrl;
    }

    public void setRightChannelUrl(String rightChannelUrl) {
        this.rightChannelUrl = rightChannelUrl;
    }

    @Override
    public void loadData() {
        NvsAudioClip audioClip = getObject();
        if (audioClip == null) {
            return;
        }
        setFilePath(audioClip.getFilePath());
        setInPoint(audioClip.getInPoint());
        setOutPoint(audioClip.getOutPoint());
        setIndex(audioClip.getIndex());
        setAddIndex(audioClip.getIndex());
        trimIn = audioClip.getTrimIn();
        trimOut = audioClip.getTrimOut();
        speed = audioClip.getSpeed();
        fadeInDuration = audioClip.getFadeInDuration();
        fadeOutDuration = audioClip.getFadeOutDuration();
        setAudioType(MeicamAudioClip.AUDIO_RECORD_FILE);
        FileInfoBridge.FileInfo fileInfo = FileInfoBridge.getFileInfo(getFilePath());
        if (fileInfo != null) {
            originalDuration = fileInfo.duration;
            audioName = fileInfo.fileName;
            remotePath = fileInfo.remotePath;
            leftChannelUrl = fileInfo.getLeftChannelUrl();
            rightChannelUrl = fileInfo.getRightChannelUrl();
        }
        if (audioFxList != null) {
            audioFxList.clear();
            int fxCount = audioClip.getFxCount();
            for (int i = 0; i < fxCount; i++) {
                NvsAudioFx nvsAudioFx = audioClip.getFxByIndex(i);
                if (nvsAudioFx != null) {
                    MeicamAudioFx meicamAudioFx = new MeicamAudioFx(nvsAudioFx, i, TYPE_BUILD_IN, nvsAudioFx.getBuiltinAudioFxName());
                    meicamAudioFx.recoverFromTimelineData(nvsAudioFx);
                    audioFxList.add(meicamAudioFx);
                }
            }
        }
        NvsVolume volumeGain = audioClip.getVolumeGain();
        if (volumeGain != null) {
            setVolume(volumeGain.leftVolume);
        }
    }

    private void setOtherAttribute(NvsAudioClip nvsAudioClip) {
        if (nvsAudioClip == null) {
            return;
        }
        setObject(nvsAudioClip);
        nvsAudioClip.setFadeInDuration(getFadeInDuration());
        nvsAudioClip.setFadeOutDuration(getFadeOutDuration());
        nvsAudioClip.setVolumeGain(volume, volume);
        nvsAudioClip.changeSpeed(getSpeed(), isKeepAudioPitch());
        if (audioFxList != null) {
            for (MeicamAudioFx meicamAudioFx : audioFxList) {
                String type = meicamAudioFx.getType();
                if (TYPE_BUILD_IN.equals(type)) {
                    nvsAudioClip.appendFx(meicamAudioFx.getDesc());
                }
            }
        }

    }

    /**
     * Copy audio data from old to new
     * <p>
     * 从旧clip中复制数据到新clip
     *
     * @param oldClip old audio clip 旧的audio clip
     */
    void copyData(MeicamAudioClip oldClip) {
        loadData();
        if (oldClip == null) {
            LogUtils.e("param is error");
            return;
        }
        setType(oldClip.getType());
        setAudioType(oldClip.getAudioType());
        setFadeInDuration(oldClip.getFadeInDuration());
        setFadeOutDuration(oldClip.getFadeOutDuration());
        setVolume(oldClip.getVolume());
        setSpeed(oldClip.getSpeed(), oldClip.isKeepAudioPitch());
        setOutPoint(0);
        leftChannelUrl = (oldClip.leftChannelUrl);
        remotePath = oldClip.remotePath;
        setDrawText(oldClip.getDrawText());
        if (oldClip.audioFxList != null) {
            for (MeicamAudioFx meicamAudioFx : oldClip.audioFxList) {
                String type = meicamAudioFx.getType();
                if (CommonData.TYPE_BUILD_IN.equals(type)) {
                    addFx(meicamAudioFx.getDesc());
                }
            }
        }
    }

    /**
     * Add effect
     *
     * @param desc the description of effect 特效描述信息
     * @return the MeicamAudioFx
     */
    public MeicamAudioFx addFx(String desc) {
        NvsAudioClip object = getObject();
        if (object != null) {
            NvsAudioFx nvsAudioFx = object.appendFx(desc);
            if (nvsAudioFx != null) {
                MeicamAudioFx meicamAudioFx = new MeicamAudioFx(nvsAudioFx, audioFxList.size(), CommonData.TYPE_BUILD_IN, desc);
                audioFxList.add(meicamAudioFx);
                return meicamAudioFx;
            }
        }
        return null;
    }

    /**
     * 设置模板附件
     * Set template attachment
     *
     * @param key   the key 键
     * @param value the value 值
     */
    public void setTemplateAttachment(String key, String value) {
        NvsAudioClip audioClip = getObject();
        if (audioClip != null) {
            audioClip.setTemplateAttachment(key, value);
        }
    }

    @NonNull
    @Override
    public Object clone() {
        Object clone = DeepCopyUtil.deepClone(this);
        if (clone == null) {
            String cloneString = GsonContext.getInstance().toJson(this);
            if (!TextUtils.isEmpty(cloneString)) {
                clone = GsonContext.getInstance().fromJson(cloneString, this.getClass());
            }
        }
        if (clone == null) {
            clone = new MeicamAudioClip();
        }
        return clone;
    }

    @Override
    public LMeicamAudioClip parseToLocalData() {
        LMeicamAudioClip local = new LMeicamAudioClip();
        setCommonData(local);
        local.setId(getId());
        local.setFilePath(getFilePath());
        local.setTrimIn(getTrimIn());
        local.setTrimOut(getTrimOut());
        local.setVolume(getVolume());
        local.setSpeed(getSpeed());
        local.setFadeInDuration(getFadeInDuration());
        local.setFadeOutDuration(getFadeOutDuration());
        local.setOriginalDuration(getOriginalDuration());
        local.setAudioType(getAudioType());
        local.setDrawText(getDrawText());
        local.setResourceId(resourceId);
        local.setKeepAudioPitch(isKeepAudioPitch());
        local.setRemotePath(remotePath);
        local.setLeftChannelUrl(leftChannelUrl);
        local.setRightChannelUrl(getRightChannelUrl());
        if (!CommonUtils.isEmpty(audioFxList)) {
            for (MeicamAudioFx meicamAudioFx : audioFxList) {
                local.getMeicamAudioFxes().add(meicamAudioFx.parseToLocalData());
            }
        }
        return local;
    }

    @Override
    public void recoverFromLocalData(LMeicamAudioClip local) {
        setId(local.getId());
        setVolume(local.getVolume());
        setSpeed(local.getSpeed(), local.isKeepAudioPitch());
        setFadeInDuration(local.getFadeInDuration());
        setFadeOutDuration(local.getFadeOutDuration());
        setOriginalDuration(local.getOriginalDuration());
        setAudioType(local.getAudioType());
        setDrawText(local.getDrawText());
        setKeepAudioPitch(local.isKeepAudioPitch());
        setLeftChannelUrl(local.getLeftChannelUrl());
        setRightChannelUrl(local.getRightChannelUrl());
        setRemotePath(local.getRemotePath());
        List<LMeicamAudioFx> audioFxes = local.getMeicamAudioFxes();
        if (!CommonUtils.isEmpty(audioFxes)) {
            for (LMeicamAudioFx lAudioFx : audioFxes) {
                MeicamAudioFx audioFx = addFx(lAudioFx.getDesc());
                if (audioFx != null) {
                    audioFx.recoverFromLocalData(lAudioFx);
                }
            }
        }
    }

    @Override
    public void recoverFromTimelineData(NvsObject nvsObject) {
        if (!(nvsObject instanceof NvsAudioClip)) {
            return;
        }
        setObject((NvsAudioClip) nvsObject);
        loadData();
    }

    @Override
    public void parseToResourceId(MeicamTimeline timeline) {
        if (timeline == null) {
            return;
        }
        if (!TextUtils.isEmpty(filePath)) {
            MeicamResource resource = new MeicamResource();
            MeicamResource.PathInfo path = new MeicamResource.PathInfo("path", filePath, true);
            resource.addPathInfo(path);
            resourceId = timeline.getPlaceId(resource);
        }
    }
}
