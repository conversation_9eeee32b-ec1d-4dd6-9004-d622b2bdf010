package com.meishe.engine.bean;

import com.meicam.sdk.NvsObject;
import com.meishe.base.bean.FloatPoint;
import com.meishe.engine.adapter.TimelineDataParserAdapter;
import com.meishe.engine.local.LMeicamKeyframeControlPoints;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2021/8/9 18:24
 * @Description :关键帧控制点 Keyframe control points
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class MeicamKeyframeControlPoints implements TimelineDataParserAdapter<LMeicamKeyframeControlPoints>, Serializable {
    /**
     * the id
     * 关键帧曲线的id
     */
    private int id;

    /**
     * the font point id
     * 关键帧曲线的前控制点id
     */
    private int fontPointId;

    /**
     * the back point id
     * 关键帧曲线的后控制点id
     */
    private int backPointId;

    /**
     * The forward control point
     * 前置控制点
     */
    private FloatPoint forwardControlPoint;
    /**
     * The backward control point
     * 后置控制点
     */
    private FloatPoint backwardControlPoint;

    /**
     * The backward control point
     * 后置控制点
     */
    private transient Map<String, FloatPoint> backwardControlPointMap = new HashMap<>();

    /**
     * The forward control point
     * 前置控制点
     */
    private transient Map<String, FloatPoint> forwardControlPointMap = new HashMap<>();


    public void addBackwardControlPoint(String key, FloatPoint pointF) {
        if (backwardControlPointMap == null) {
            backwardControlPointMap = new HashMap<>();
        }
        backwardControlPointMap.put(key, pointF);
    }

    public void addForwardControlPoint(String key, FloatPoint pointF) {
        if (forwardControlPointMap == null) {
            forwardControlPointMap = new HashMap<>();
        }
        forwardControlPointMap.put(key, pointF);
    }

    public Map<String, FloatPoint> getForwardControlPointMap() {
        if (forwardControlPointMap == null) {
            forwardControlPointMap = new HashMap<>();
        }
        return forwardControlPointMap;
    }

    public Map<String, FloatPoint> getBackwardControlPointMap() {
        if (backwardControlPointMap == null) {
            backwardControlPointMap = new HashMap<>();
        }
        return backwardControlPointMap;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getFontPointId() {
        return fontPointId;
    }

    public void setFontPointId(int fontPointId) {
        this.fontPointId = fontPointId;
    }

    public int getBackPointId() {
        return backPointId;
    }

    public void setBackPointId(int backPointId) {
        this.backPointId = backPointId;
    }

    public FloatPoint getForwardControlPoint() {
        return forwardControlPoint;
    }

    public void setForwardControlPoint(FloatPoint forwardControlPoint) {
        this.forwardControlPoint = forwardControlPoint;
    }

    public FloatPoint getBackwardControlPoint() {
        return backwardControlPoint;
    }

    public void setBackwardControlPoint(FloatPoint backwardControlPoint) {
        this.backwardControlPoint = backwardControlPoint;
    }

    /**
     * Check Whether the data is invalid.
     * 检查数据是否无效
     *
     * @return true：无效 false:有效 true：Invalid； false：Valid
     */
    public boolean isInvalid() {
        return ((backwardControlPointMap == null || backwardControlPointMap.isEmpty())
                && (forwardControlPointMap == null || forwardControlPointMap.isEmpty()));
    }

    @Override
    public LMeicamKeyframeControlPoints parseToLocalData() {
        LMeicamKeyframeControlPoints local = new LMeicamKeyframeControlPoints();
        local.setBackwardControlPoint(getBackwardControlPoint());
        local.setForwardControlPoint(getForwardControlPoint());
        local.setId(getId());
        local.setFontPointId(getFontPointId());
        local.setBackPointId(getBackPointId());
        return local;
    }

    @Override
    public void recoverFromLocalData(LMeicamKeyframeControlPoints local) {
        if (local == null) {
            return;
        }
        setId(local.getId());
        setFontPointId(local.getFontPointId());
        setBackPointId(local.getBackPointId());
        setBackwardControlPoint(local.getBackwardControlPoint());
        setForwardControlPoint(local.getForwardControlPoint());
    }

    @Override
    public void recoverFromTimelineData(NvsObject nvsObject) {

    }
}
