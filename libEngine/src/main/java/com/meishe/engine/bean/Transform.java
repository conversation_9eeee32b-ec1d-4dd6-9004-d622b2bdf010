package com.meishe.engine.bean;

import java.io.Serializable;

/**
 * All rights Reserved, Designed By www.meishesdk.com
 *
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2021/7/29 16:38
 * @Description: 移动数据The transform data
 * @Copyright: www.meishesdk.com Inc. All rights reserved.
 */
public class Transform implements Serializable,Cloneable {
    public float scaleX = 1.0f;
    public float scaleY = 1.0f;
    public float transformX = 0f;
    public float transformY = 0f;
    public float rotation = 0f;

    public float lastScale = 1;
    public float viewScale = 1;

    @Override
    public Transform clone() {
        try {
            return (Transform) super.clone();
        } catch (CloneNotSupportedException e) {
        }
        return new Transform();
    }
}
