package com.meishe.engine.bean;

import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.meicam.sdk.NvsVideoTransition;
import com.meishe.engine.adapter.TimelineDataParserAdapter;
import com.meishe.engine.adapter.parser.IResourceParser;
import com.meishe.engine.bean.bridges.FileInfoBridge;
import com.meishe.engine.constant.NvsConstants;
import com.meishe.engine.local.LMeicamTransition;

import java.io.Serializable;

import static com.meishe.engine.bean.CommonData.EFFECT_BUILTIN;
import static com.meishe.engine.bean.CommonData.TYPE_BUILD_IN;
import static com.meishe.engine.bean.CommonData.TYPE_PACKAGE;


/**
 * All rights reserved,Designed by www.meishesdk.com
 * 版权所有:www.meishesdk.com
 *
 * <AUTHOR> LiHangZhou
 * @CreateDate :2021/05/20 10:07
 * @Description : 转场 The transition of clip
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class MeicamTransition extends NvsObject<NvsVideoTransition> implements Cloneable, Serializable,
        TimelineDataParserAdapter<LMeicamTransition>, IResourceParser {
    /**
     * 转场索引
     * The transition index
     */
    private int index;
    /**
     * 转场类型
     * The transition type
     */
    private String type;
    /**
     * 内建转场为名字 包转场为id。
     * The transition fx id
     */
    private String desc;
    /**
     * 转场名称
     * The transition name
     */
    private String displayName;
    /**
     * 转场中文名称
     * The transition zh name
     */
    private String displayNamezhCN;
    /**
     * 转场图标路径
     * The transition icon path
     */
    private String iconPath;
    /**
     * 转场图标id
     * The transition icon id
     */
    private int iconResourceId;
    /**
     * 转场时长
     * The transition duration
     */
    private long duration = CommonData.TIMEBASE;
    private String resourceId;
    private int trackIndex;

    MeicamTransition(NvsVideoTransition videoTransition, int index, String type, String desc) {
        super(videoTransition);
        this.index = index;
        this.type = type;
        this.desc = desc;
    }

    void bindToTimeline() {
    }

    @Override
    void loadData() {
        NvsVideoTransition videoTransition = getObject();
        if (videoTransition != null) {
            if (videoTransition.getVideoTransitionType() == EFFECT_BUILTIN) {
                type = TYPE_BUILD_IN;
                desc = videoTransition.getBuiltinVideoTransitionName();
            } else {
                type = TYPE_PACKAGE;
                desc = videoTransition.getVideoTransitionPackageId();
            }
            duration = videoTransition.getVideoTransitionDuration();
            FileInfoBridge.FileInfo fileInfo = FileInfoBridge.getFileInfo(desc);
            if (fileInfo != null) {
                iconPath = fileInfo.filePath;
            }
        }
    }

    public int getIndex() {
        return index;
    }

    void setIndex(int index) {
        this.index = index;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public String getDisplayNamezhCN() {
        return displayNamezhCN;
    }

    public void setDisplayNamezhCN(String displayNamezhCN) {
        this.displayNamezhCN = displayNamezhCN;
    }

    public String getIconPath() {
        return iconPath;
    }

    public void setIconPath(String iconPath) {
        this.iconPath = iconPath;
    }

    public int getIconResourceId() {
        return iconResourceId;
    }

    public void setIconResourceId(int iconResourceId) {
        this.iconResourceId = iconResourceId;
    }

    public long getDuration() {
        return duration;
    }

    public void setDuration(long duration) {
        NvsVideoTransition object = getObject();
        if (object != null) {
            object.setVideoTransitionDuration(duration, NvsVideoTransition.VIDEO_TRANSITION_DURATION_MATCH_MODE_STRETCH);
            this.duration = duration;
        }
    }

    public String getResourceId() {
        return resourceId;
    }

    public void setResourceId(String resourceId) {
        this.resourceId = resourceId;
    }

    @Override
    public boolean equals(@Nullable Object object) {
        if (this == object) {
            return true;
        }
        if (object instanceof MeicamTransition) {
            MeicamTransition transition = (MeicamTransition) object;
            return this.getIndex() == (transition.getIndex());
        }
        return super.equals(object);
    }

    @NonNull
    @Override
    public String toString() {
        return "Transition{index=" + index + ",des=" + desc + ",name=" + displayNamezhCN + ",type=" + type + "}";
    }

    @NonNull
    @Override
    public Object clone() {
        try {
            return super.clone();
        } catch (CloneNotSupportedException e) {
            e.printStackTrace();
        }
        return "";
    }

    @Override
    public LMeicamTransition parseToLocalData() {
        LMeicamTransition local = new LMeicamTransition();
        local.setDesc(getDesc());
        local.setDisplayName(getDisplayName());
        local.setDisplayNamezhCN(getDisplayNamezhCN());
        local.setDuration(getDuration());
        local.setIconPath(getIconPath());
        local.setIconResourceId(getIconResourceId());
        local.setIndex(getIndex());
        local.setType(getType());
        local.setResourceId(resourceId);
        local.setTrackIndex(getTrackIndex());
        return local;
    }

    @Override
    public void recoverFromLocalData(LMeicamTransition lTransition) {
        setDisplayName(lTransition.getDisplayName());
        setDisplayNamezhCN(lTransition.getDisplayNamezhCN());
        setDuration(lTransition.getDuration());
        setIconPath(lTransition.getIconPath());
        setIconResourceId(lTransition.getIconResourceId());
        setTrackIndex(lTransition.getTrackIndex());
    }

    @Override
    public void recoverFromTimelineData(com.meicam.sdk.NvsObject nvsObject) {
        if (nvsObject instanceof NvsVideoTransition) {
            setObject((NvsVideoTransition) nvsObject);
            loadData();
        }
    }

    @Override
    public void parseToResourceId(MeicamTimeline timeline) {
        if (timeline == null) {
            return;
        }
        MeicamResource resource = null;
        if (!TextUtils.isEmpty(iconPath)) {
            resource = new MeicamResource();
            resource.addPathInfo(new MeicamResource.PathInfo("iconPath", iconPath, false));
        }

        if (NvsConstants.TYPE_PACKAGE.equals(type)) {
            if (!TextUtils.isEmpty(desc)) {
                if (resource == null) {
                    resource = new MeicamResource();
                    resource.addPathInfo(new MeicamResource.PathInfo("path", desc, false));
                }
            }
        }
        if (resource != null) {
            resourceId = timeline.getPlaceId(resource);
        }
    }

    public void setTrackIndex(int index) {
        trackIndex = index;
    }

    public int getTrackIndex() {
        return trackIndex;
    }
}
