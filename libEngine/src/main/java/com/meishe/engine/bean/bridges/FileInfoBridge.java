package com.meishe.engine.bean.bridges;

import android.text.TextUtils;

import com.meicam.sdk.NvsAVFileInfo;
import com.meicam.sdk.NvsSize;
import com.meicam.sdk.NvsStreamingContext;
import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.LogUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/3/7 14:00
 * @Description :文件和Meicam数据的中间媒介 The bridge between Meicam data and file data
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class FileInfoBridge {
    public static String sCurrentProject;
    private static Map<String,Map<String,FileInfo>> fileInfoMap = new HashMap<>();

    public static FileInfo getFileInfo(String projectId, String filePath){
        Map<String, FileInfo> fileInfoMap = FileInfoBridge.fileInfoMap.get(projectId);
        if (fileInfoMap != null) {
            return fileInfoMap.get(filePath);
        }
        return null;
    }

    public static FileInfo getFileInfo(String filePath){
        if (TextUtils.isEmpty(sCurrentProject)) {
            LogUtils.w("sCurrentProject is null!");
        }
        Map<String, FileInfo> fileInfoMap = FileInfoBridge.fileInfoMap.get(sCurrentProject);
        if (fileInfoMap != null) {
            return fileInfoMap.get(filePath);
        }
        return null;
    }

    public static void putFileInFo(String projectId, FileInfo fileInfo){
        if (fileInfo != null) {
            Map<String, FileInfo> stringFileInfoMap = fileInfoMap.get(projectId);
            if (stringFileInfoMap == null) {
                stringFileInfoMap = new HashMap<>();
                fileInfoMap.put(projectId, stringFileInfoMap);
            }
            stringFileInfoMap.put(fileInfo.key, fileInfo);
        }
    }

    public static List<FileInfo> getFileInfoList(String projectId) {
        if (CommonUtils.isEmpty(fileInfoMap)) {
            return null;
        }
        Map<String, FileInfo> stringFileInfoMap = fileInfoMap.get(projectId);
        if (CommonUtils.isEmpty(stringFileInfoMap)) {
            return null;
        }
        List<FileInfo> list = new ArrayList<>();
        Set<Map.Entry<String, FileInfo>> entries = stringFileInfoMap.entrySet();
        for (Map.Entry<String, FileInfo> entry : entries) {
            FileInfo value = entry.getValue();
            if (value != null) {
                list.add(value);
            }
        }
        return list;
    }

    /**
     * Has boolean.
     * 是否有该工程数据
     * @param projectId the project id
     * @return the boolean
     */
    public static boolean has(String projectId){
        return fileInfoMap.containsKey(projectId);
    }

    public static void clearData(String projectId){
        if (fileInfoMap.containsKey(projectId)) {
            fileInfoMap.remove(projectId);
        }
    }

    public static class FileInfo{
        public String filePath;
        public long fileSize;

        public String resourceId;
        /**
         * 缩略图、导出和波形，需要用到远程路径
         * Thumbnails, exports and waveforms, requiring remote paths.
         */
        public String remotePath;

        /**
         * 左声道波形
         * The url for left channel
         */
        private String leftChannelUrl;

        /**
         * 右声道波形
         * The url for right channel
         */
        private String rightChannelUrl;

        public String key;
        public String fileName;
        public String fileNameZh;
        public String customDisPlayName;
        public long duration = Long.MAX_VALUE;
        public int width;
        public int height;
        public int category;
        public int type;
        public int kind;
        private boolean isAssets;

        private ThumbNailInfo thumbNailInfo;

        public FileInfo(String key) {
            this.key = key;
        }

        public void setKey(String key){
            this.key = key;
        }

        public FileInfo setFilePath(String filePath){
            this.filePath = filePath;
            return this;
        }

        public FileInfo setFileName(String fileName) {
            this.fileName = fileName;
            return this;
        }

        public FileInfo setFileNameZh(String fileNameZh) {
            this.fileNameZh = fileNameZh;
            return this;
        }

        public FileInfo setCustomDisPlayName(String customDisPlayName) {
            this.customDisPlayName = customDisPlayName;
            return this;
        }

        public FileInfo setWidth(int width) {
            this.width = width;
            return this;
        }

        public FileInfo setHeight(int height) {
            this.height = height;
            return this;
        }

        public FileInfo setCategory(int category) {
            this.category = category;
            return this;
        }

        public FileInfo setType(int type) {
            this.type = type;
            return this;
        }

        public FileInfo setKind(int kind) {
            this.kind = kind;
            return this;
        }

        public FileInfo setResourceId(String resourceId) {
            this.resourceId = resourceId;
            return this;
        }

        public String getRemotePath() {
            return remotePath;
        }

        public FileInfo setRemotePath(String remotePath) {
            this.remotePath = remotePath;
            return this;
        }

        public String getLeftChannelUrl() {
            return leftChannelUrl;
        }

        public FileInfo setLeftChannelUrl(String leftChannelUrl) {
            this.leftChannelUrl = leftChannelUrl;
            return this;
        }

        public String getRightChannelUrl() {
            return rightChannelUrl;
        }

        public FileInfo setRightChannelUrl(String rightChannelUrl) {
            this.rightChannelUrl = rightChannelUrl;
            return this;
        }

        public FileInfo setDuration(long duration) {
            if (duration == 0) {
                this.duration =  Long.MAX_VALUE;
            } else {
                this.duration = duration;
            }
            return this;
        }

        public boolean isAssets() {
            return isAssets;
        }

        public FileInfo setAssets(boolean assets) {
            isAssets = assets;
            return this;
        }

        public ThumbNailInfo getThumbNailInfo() {
            return thumbNailInfo;
        }

        public void setThumbNailInfo(ThumbNailInfo thumbNailInfo) {
            this.thumbNailInfo = thumbNailInfo;
        }

        public long getFileSize() {
            return fileSize;
        }

        public FileInfo setFileSize(long fileSize) {
            this.fileSize = fileSize;
            return this;
        }

        public FileInfo getAVFileInfo() {
            if (!TextUtils.isEmpty(remotePath)) {
                NvsAVFileInfo avFileInfo = NvsStreamingContext.getInstance().getAVFileInfo(remotePath);
                if (avFileInfo != null) {
                    if (avFileInfo.getAVFileType() == NvsAVFileInfo.AV_FILE_TYPE_IMAGE) {
                        //duration = Long.MAX_VALUE;
                    } else {
                        //duration = avFileInfo.getDuration();
                    }
                    String[] split = remotePath.split("/");
                    if (split.length > 0) {
                        fileName = split[split.length - 1];
                    }
                    int rotation = avFileInfo.getVideoStreamRotation(0);
                    NvsSize dimension = avFileInfo.getVideoStreamDimension(0);
                    if (rotation % 2 != 0) {
                        height = dimension.width;
                        width = dimension.height;
                    } else {
                        width = dimension.width;
                        height = dimension.height;
                    }
                }
            } else {
                NvsAVFileInfo avFileInfo = NvsStreamingContext.getInstance().getAVFileInfo(filePath);
                if (avFileInfo != null) {
                    if (avFileInfo.getAVFileType() != NvsAVFileInfo.AV_FILE_TYPE_IMAGE) {
                        duration = avFileInfo.getDuration();
                    }
                }
            }
            return this;
        }
    }

    public static class ThumbNailInfo{
        public String urlPrefix;
        public long interval;
        public String extension;

        public ThumbNailInfo(String urlPrefix, long interval, String extension) {
            this.urlPrefix = urlPrefix;
            this.interval = interval;
            this.extension = extension;
        }
    }
}
