package com.meishe.engine.bean;

import android.graphics.PointF;
import android.text.TextUtils;

import com.meicam.sdk.NvsLiveWindowExt;
import com.meicam.sdk.NvsObject;
import com.meishe.engine.adapter.TimelineDataParserAdapter;
import com.meishe.engine.adapter.parser.IResourceParser;
import com.meishe.engine.local.LMeicamWaterMark;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * All rights Reserved, Designed By www.meishesdk.com
 *
 * @Author: ChuChenGuang
 * @CreateDate: 2021/5/20 10:30
 * @Description: 水印 The water mark
 * @Copyright: www.meishesdk.com Inc. All rights reserved.
 */
public class MeicamWaterMark implements Cloneable, Serializable, TimelineDataParserAdapter<LMeicamWaterMark>, IResourceParser {
    /**
     * The path of watermark file must be PNG, JPG, CAF file
     * 水印文件的路径，需为PNG、JPG、CAF文件
     */
    private String watermarkFilePath;
    /**
     * Watermark in the X direction of the margin
     * 水印在X方向的边距
     */
    private int marginX;
    /**
     * Watermark in the Y direction of the margin
     * 水印在Y方向的边距
     */
    private int marginY;
    /**
     * The width of the watermark displayed in the timeline. If 0 is used, the width of the image file is used
     * 水印在timeline中显示的宽度，为0则使用图片文件的宽度
     */
    private int displayWidth;
    /**
     * The height of the watermark displayed in the timeline is 0, and the height of the image file is used
     * 水印在timeline中显示的高度，为0则使用图片文件的高度
     */
    private int displayHeight;
    /**
     * The upper layer has four vertices
     * 上层数据四个顶点
     */
    private transient List<PointF> list = new ArrayList<>();
    private String resourceId;

    MeicamWaterMark(String watermarkPath, List<PointF> list) {
        this.watermarkFilePath = watermarkPath;
        this.list = list;
    }

    MeicamWaterMark(String path, int width, int height, int x, int y) {
        this.watermarkFilePath = path;
        this.displayWidth = width;
        this.displayHeight = height;
        this.marginX = x;
        this.marginY = y;
    }

    public String getWatermarkFilePath() {
        return watermarkFilePath;
    }

    public void setWatermarkFilePath(String mWatermarkPath) {
        this.watermarkFilePath = mWatermarkPath;
    }

    public int getMarginX() {
        return marginX;
    }

    public void setMarginX(int mWatermarkX) {
        this.marginX = mWatermarkX;
    }

    public int getMarginY() {
        return marginY;
    }

    public void setMarginY(int mWatermarkY) {
        this.marginY = mWatermarkY;
    }

    public int getDisplayWidth() {
        return displayWidth;
    }

    public void setDisplayWidth(int mWatermarkW) {
        this.displayWidth = mWatermarkW;
    }

    public int getDisplayHeight() {
        return displayHeight;
    }

    public void setDisplayHeight(int mWatermarkH) {
        this.displayHeight = mWatermarkH;
    }

    public List<PointF> getList() {
        return list;
    }

    public void setList(List<PointF> list) {
        this.list = list;
    }

    public String getResourceId() {
        return resourceId;
    }

    public void setResourceId(String resourceId) {
        this.resourceId = resourceId;
    }

    public void setPointList(NvsLiveWindowExt mLiveWindow, int timeLineWidth, int timeLineHeight) {
        if (list == null) {
            list = new ArrayList<>();
        } else {
            list.clear();
        }

        int leftTopPointX = marginX - timeLineWidth / 2;
        int leftTopPointY = timeLineHeight / 2 - marginY;

        PointF leftTop = new PointF(leftTopPointX, leftTopPointY);

        PointF rightBottom = new PointF((leftTopPointX + displayWidth), (leftTopPointY - displayHeight));

        PointF leftBottom = new PointF(leftTop.x, rightBottom.y);
        PointF rightTop = new PointF(rightBottom.x, leftTop.y);


        list.add(mLiveWindow.mapCanonicalToView(leftTop));
        list.add(mLiveWindow.mapCanonicalToView(leftBottom));
        list.add(mLiveWindow.mapCanonicalToView(rightBottom));
        list.add(mLiveWindow.mapCanonicalToView(rightTop));
    }

    @Override
    public LMeicamWaterMark parseToLocalData() {
        LMeicamWaterMark local = new LMeicamWaterMark(watermarkFilePath);
        local.setWatermarkH(getDisplayHeight());
        local.setWatermarkW(getDisplayWidth());
        local.setWatermarkX(getMarginX());
        local.setWatermarkY(getMarginY());
        local.setResourceId(getResourceId());
        return local;
    }

    @Override
    public void recoverFromLocalData(LMeicamWaterMark lMeicamWaterMark) {

    }

    @Override
    public void recoverFromTimelineData(NvsObject nvsObject) {

    }

    @Override
    public void parseToResourceId(MeicamTimeline timeline) {
        if (timeline == null) {
            return;
        }
        if (!TextUtils.isEmpty(watermarkFilePath)) {
            MeicamResource resource = new MeicamResource();
            resource.addPathInfo(new MeicamResource.PathInfo("path", watermarkFilePath, false));
            resourceId = timeline.getPlaceId(resource);
        }
    }
}
