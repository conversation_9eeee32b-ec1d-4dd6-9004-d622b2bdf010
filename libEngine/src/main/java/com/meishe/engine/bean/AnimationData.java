package com.meishe.engine.bean;

import static com.meishe.engine.asset.bean.AssetInfo.ASSET_ANIMATION_GROUP;
import static com.meishe.engine.asset.bean.AssetInfo.ASSET_ANIMATION_IN;

/**
 * author：yangtailin on 2020/9/2 10:41
 * This class is an animated data class
 * 此类为动画数据类
 */
/**
 * All rights reserved,Designed by www.meishesdk.com
 * 版权所有:www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2020/12/18 10:20
 * @Description : 此类为动画数据类 This class is an animated data class
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class AnimationData {

    public static final int PACKAGE = 0;
    public static final int POST_PACKAGE = 1;
    private long inPoint;
    private long outPoint;
    private long inPoint2;
    private long outPoint2;
    private String packageID;
    private String packageID2;
    private String mPackageFxPath;
    private int isPostPackage;
    /**
     * ASSET_ANIMATION_IN 代表packageID是入动画 ASSET_ANIMATION_GROUP代表是组合动画
     * ASSET_ANIMATION_IN represents that the packageID is the in animation
     * and ASSET_ANIMATION_GROUP is composite animation.
     */
    private int isAnimationIn = -1;

    /**
     * 获取入点
     * Gets in point.
     *
     * @return the in point
     */
    public long getInPoint() {
        return inPoint;
    }

    /**
     * 设置入点
     * Sets in point.
     *
     * @param inPoint the in point
     */
    public void setInPoint(long inPoint) {
        this.inPoint = inPoint;
    }

    /**
     * 获取结束点
     * Gets out point.
     *
     * @return the out point
     */
    public long getOutPoint() {
        return outPoint;
    }

    /**
     * 设置结束点
     * Sets out point.
     *
     * @param outPoint the out point
     */
    public void setOutPoint(long outPoint) {
        this.outPoint = outPoint;
    }

    /**
     * 获取包特效路径
     * Gets package fx path.
     *
     * @return the package fx path
     */
    public String getPackageFxPath() {
        return mPackageFxPath;
    }

    /**
     * 设置包特效路径
     * Sets package fx path.
     *
     * @param packageFxPath the package fx path
     */
    public void setPackageFxPath(String packageFxPath) {
        this.mPackageFxPath = packageFxPath;
    }

    public int isPostPackage() {
        return isPostPackage;
    }

    public void setIsPostPackage(int isPostPackage) {
        this.isPostPackage = isPostPackage;
    }

    public long getInPoint2() {
        return inPoint2;
    }

    public void setInPoint2(long inPoint2) {
        this.inPoint2 = inPoint2;
    }

    public long getOutPoint2() {
        return outPoint2;
    }

    public void setOutPoint2(long outPoint2) {
        this.outPoint2 = outPoint2;
    }

    public String getPackageID() {
        return packageID;
    }

    public void setPackageID(String packageID) {
        this.packageID = packageID;
    }

    public String getPackageID2() {
        return packageID2;
    }

    public void setPackageID2(String packageID2) {
        this.packageID2 = packageID2;
    }

    public boolean getIsAnimationIn() {
        return isAnimationIn == ASSET_ANIMATION_IN;
    }

    public void setIsAnimationIn(boolean isAnimationIn) {
        this.isAnimationIn = isAnimationIn ? ASSET_ANIMATION_IN : ASSET_ANIMATION_GROUP;
    }

    @Override
    public String toString() {
        return "AnimationData{" +
                "inPoint=" + inPoint +
                ", outPoint=" + outPoint +
                ", inPoint2=" + inPoint2 +
                ", outPoint2=" + outPoint2 +
                ", packageID='" + packageID + '\'' +
                ", packageID2='" + packageID2 + '\'' +
                ", mPackageFxPath='" + mPackageFxPath + '\'' +
                ", isPostPackage=" + isPostPackage +
                '}';
    }
}
