package com.meishe.engine.bean;

import com.google.gson.annotations.SerializedName;
import com.meicam.sdk.NvsVideoFx;
import com.meishe.engine.adapter.TimelineDataParserAdapter;
import com.meishe.engine.constant.NvsConstants;
import com.meishe.engine.local.LMeicamAdjustData;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * All rights Reserved, Designed By www.meishesdk.com
 *
 * @Author: yangtailin
 * @CreateDate: 2020/6/17 10:30
 * @Description: 调节参数 Adjust data
 * @Copyright: www.meishesdk.com Inc. All rights reserved.
 */
public class MeicamAdjustData extends NvsObject<Map<String, NvsVideoFx>> implements Cloneable, Serializable, TimelineDataParserAdapter<LMeicamAdjustData> {

    ///调整校色
    ///亮度，对比度，饱和度，高光，阴影，色温，色调，褐色，暗角，锐度
    ///Adjust color correction
    ///Brightness, contrast, saturation, highlight, shadow, color temperature, hue, brown, dark angle, sharpness
    /**
     * 亮度
     * brightness
     */
    @SerializedName("Brightness")
    private float mBrightness;
    /**
     * 对比度
     * contrast
     */
    @SerializedName("Contrast")
    private float mContrast;
    /**
     * 饱和度
     * Saturation
     */
    @SerializedName("Saturation")
    private float mSaturation;
    /**
     * 高光
     * Highlight
     */
    @SerializedName("Highlight")
    private float mHighlight;
    /**
     * 阴影
     * Shadow
     */
    @SerializedName("Shadow")
    private float mShadow;
    /**
     * 褐色  -1，0
     * Blackpoint Value is from -1 to 0
     */
    @SerializedName("Blackpoint")
    private float mBlackPoint;
    /**
     * 暗角
     * Dark degree Value is from 0 to 1
     */
    @SerializedName("Degree")
    private float mDegree;
    /**
     * 锐度 取值范围0-1
     * Amount Value is from 0 to 1
     */
    @SerializedName("Amount")
    private float mAmount;
    /**
     * 色温
     * Temperature
     */
    @SerializedName("Temperature")
    private float mTemperature;
    /**
     * 色调
     * Tint
     */
    @SerializedName("Tint")
    private float mTint;

    MeicamAdjustData() {
    }

    public float getBrightness() {
        return mBrightness;
    }

    public void setBrightness(float brightness) {
        if (Float.isNaN(brightness)) {
            return;
        }
        Map<String, NvsVideoFx> object = getObject();
        if (object != null) {
            NvsVideoFx nvsVideoFx = object.get(NvsConstants.ADJUST_TYPE_BASIC_IMAGE_ADJUST);
            if (nvsVideoFx != null) {
                nvsVideoFx.setFloatVal(NvsConstants.ADJUST_BRIGHTNESS, brightness);
            }
        }
        mBrightness = brightness;
    }

    public float getContrast() {
        return mContrast;
    }

    public void setContrast(float contrast) {
        if (Float.isNaN(contrast)) {
            return;
        }
        mContrast = contrast;
    }

    public float getSaturation() {
        return mSaturation;
    }

    public void setSaturation(float saturation) {
        if (Float.isNaN(saturation)) {
            return;
        }
        mSaturation = saturation;
    }

    public float getHighlight() {
        return mHighlight;
    }

    public void setHighlight(float highlight) {
        if (Float.isNaN(highlight)) {
            return;
        }
        mHighlight = highlight;
    }

    public float getShadow() {
        return mShadow;
    }

    public void setShadow(float shadow) {
        if (Float.isNaN(shadow)) {
            return;
        }
        mShadow = shadow;
    }

    public float getBlackPoint() {
        return mBlackPoint;
    }

    public void setBlackPoint(float blackPoint) {
        if (Float.isNaN(blackPoint)) {
            return;
        }
        mBlackPoint = blackPoint;
    }

    public float getDegree() {
        return mDegree;
    }

    public void setDegree(float degree) {
        if (Float.isNaN(degree)) {
            return;
        }
        mDegree = degree;
    }

    public float getAmount() {
        return mAmount;
    }

    public void setAmount(float amount) {
        if (Float.isNaN(amount)) {
            return;
        }
        mAmount = amount;
    }

    public float getTemperature() {
        return mTemperature;
    }

    public void setTemperature(float temperature) {
        if (Float.isNaN(temperature)) {
            return;
        }
        mTemperature = temperature;
    }

    public float getTint() {
        return mTint;
    }

    public void setTint(float tint) {
        if (Float.isNaN(tint)) {
            return;
        }
        mTint = tint;
    }

    public void reset() {
        mBrightness = 0;
        mContrast = 0;
        mSaturation = 0;
        mHighlight = 0;
        mShadow = 0;
        mBlackPoint = 0;
        mDegree = 0;
        mAmount = 0;
        mTemperature = 0;
        mTint = 0;
    }


    public void setAdjustData(MeicamAdjustData meicamAdjustData) {
        if (meicamAdjustData == null) {
            return;
        }
        mBrightness = meicamAdjustData.getBrightness();
        mContrast = meicamAdjustData.getContrast();
        mSaturation = meicamAdjustData.getSaturation();
        mHighlight = meicamAdjustData.getHighlight();
        mShadow = meicamAdjustData.getShadow();
        mBlackPoint = meicamAdjustData.getBlackPoint();
        mDegree = meicamAdjustData.getDegree();
        mAmount = meicamAdjustData.getAmount();
        mTemperature = meicamAdjustData.getTemperature();
        mTint = meicamAdjustData.getTint();
    }

    @Override
    public LMeicamAdjustData parseToLocalData() {
        LMeicamAdjustData local = new LMeicamAdjustData();
        local.setBrightness(getBrightness());
        local.setContrast(getContrast());
        local.setSaturation(getSaturation());
        local.setHighlight(getHighlight());
        local.setShadow(getShadow());
        local.setBlackPoint(getBlackPoint());
        local.setDegree(getDegree());
        local.setAmount(getAmount());
        local.setTemperature(getTemperature());
        local.setTint(getTint());
        return local;
    }


    public Map<String, Float> getKeyToValueMap() {
        Map<String, Float> data = new HashMap<>();
        data.put(NvsConstants.ADJUST_AMOUNT, getAmount());
        data.put(NvsConstants.ADJUST_DEGREE, getDegree());
        data.put(NvsConstants.ADJUST_BLACKPOINT, getBlackPoint());
        data.put(NvsConstants.ADJUST_TINT, getTint());
        data.put(NvsConstants.ADJUST_TEMPERATURE, getTemperature());
        data.put(NvsConstants.ADJUST_SHADOW, getShadow());
        data.put(NvsConstants.ADJUST_HIGHTLIGHT, getHighlight());
        data.put(NvsConstants.ADJUST_SATURATION, getSaturation());
        data.put(NvsConstants.ADJUST_CONTRAST, getContrast());
        data.put(NvsConstants.ADJUST_BRIGHTNESS, getBrightness());
        return data;
    }

    public void setValue(String key, float value) {
        if (NvsConstants.ADJUST_AMOUNT.equals(key)) {
            setAmount(value);
        } else if (NvsConstants.ADJUST_DEGREE.equals(key)) {
            setDegree(value);
        } else if (NvsConstants.ADJUST_BLACKPOINT.equals(key)) {
            setBlackPoint(value);
        } else if (NvsConstants.ADJUST_TINT.equals(key)) {
            setTint(value);
        } else if (NvsConstants.ADJUST_TEMPERATURE.equals(key)) {
            setTemperature(value);
        } else if (NvsConstants.ADJUST_SHADOW.equals(key)) {
            setShadow(value);
        } else if (NvsConstants.ADJUST_HIGHTLIGHT.equals(key)) {
            setHighlight(value);
        } else if (NvsConstants.ADJUST_SATURATION.equals(key)) {
            setSaturation(value);
        } else if (NvsConstants.ADJUST_CONTRAST.equals(key)) {
            setContrast(value);
        } else if (NvsConstants.ADJUST_BRIGHTNESS.equals(key)) {
            setBrightness(value);
        }
    }

    @Override
    public void recoverFromLocalData(LMeicamAdjustData lMeicamAdjustData) {

    }

    @Override
    public void recoverFromTimelineData(com.meicam.sdk.NvsObject nvsObject) {

    }
}
