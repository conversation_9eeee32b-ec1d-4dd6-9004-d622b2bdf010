package com.meishe.engine.bean;

import static com.meishe.engine.bean.MeicamFxParam.TYPE_BOOLEAN;
import static com.meishe.engine.bean.MeicamFxParam.TYPE_COLOR;
import static com.meishe.engine.bean.MeicamFxParam.TYPE_FLOAT;
import static com.meishe.engine.bean.MeicamFxParam.TYPE_INT;
import static com.meishe.engine.bean.MeicamFxParam.TYPE_MENU;
import static com.meishe.engine.bean.MeicamFxParam.TYPE_OBJECT;
import static com.meishe.engine.bean.MeicamFxParam.TYPE_POSITION_2D;
import static com.meishe.engine.bean.MeicamFxParam.TYPE_STRING;
import static com.meishe.engine.bean.MeicamFxParam.TYPE_STRING_OLD;
import static com.meishe.engine.constant.NvsConstants.AMPLITUDE;
import static com.meishe.engine.constant.NvsConstants.KEY_BACKGROUND_IMAGE_PATH;
import static com.meishe.engine.constant.NvsConstants.KEY_BACKGROUND_MODE;
import static com.meishe.engine.constant.NvsConstants.KEY_CROPPER_REGION_INFO;
import static com.meishe.engine.constant.NvsConstants.KEY_PROPERTY_MASK_REGION_INFO;
import static com.meishe.engine.constant.NvsConstants.TYPE_BUILD_IN;

import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.google.gson.annotations.SerializedName;
import com.meicam.sdk.NvsArbitraryData;
import com.meicam.sdk.NvsColor;
import com.meicam.sdk.NvsMaskRegionInfo;
import com.meicam.sdk.NvsPosition2D;
import com.meicam.sdk.NvsVideoFx;
import com.meishe.base.bean.FloatPoint;
import com.meishe.base.constants.Constants;
import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.GsonUtils;
import com.meishe.base.utils.LogUtils;
import com.meishe.engine.adapter.TimelineDataParserAdapter;
import com.meishe.engine.bean.bridges.AtomicFxBridge;
import com.meishe.engine.constant.ColorsConstants;
import com.meishe.engine.constant.NvsConstants;
import com.meishe.engine.interf.IKeyFrameProcessor;
import com.meishe.engine.local.LMaskRegionInfoData;
import com.meishe.engine.local.LMeicamFxParam;
import com.meishe.engine.local.LMeicamMaskInfo;
import com.meishe.engine.local.LMeicamMaskRegionInfo;
import com.meishe.engine.local.LMeicamVideoFx;
import com.meishe.engine.util.ColorUtil;
import com.meishe.engine.util.DeepCopyUtil;
import com.meishe.engine.util.gson.GsonContext;

import java.io.Serializable;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;

/**
 * All rights reserved,Designed by www.meishesdk.com
 * 版权所有:www.meishesdk.com
 *
 * <AUTHOR> LiHangZhou
 * @CreateDate :2021/05/20 10:07
 * @Description : 特效 The video clip fx
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class MeicamVideoFx extends NvsObject<NvsVideoFx> implements Cloneable, Serializable,
        TimelineDataParserAdapter<LMeicamVideoFx>, IKeyFrameProcessor<NvsVideoFx> {

    public static final float INVALID_VALUE = -10000;
    public static final int INVALID_VALUE_INT = -10000;
    private long inPointInClip;
    private long outPointInClip;

    @Override
    public KeyFrameProcessor<NvsVideoFx> keyFrameProcessor() {
        if (mKeyFrameHolder == null) {
            mKeyFrameHolder = new KeyFrameProcessor<>(this);
        }
        return mKeyFrameHolder;
    }

    /**
     * Constants for subType of effect
     * 特效子类型常量
     */
    public class SubType {
        /**
         * Filter added in clip
         * 安装在Clip上的滤镜
         */
        public final static String SUB_TYPE_CLIP_FILTER = "clipFilter";

        /**
         * Effect added in clip
         * 安装在Clip上的特效
         */
        public final static String SUB_TYPE_CLIP_EFFECT = "clipEffect";
        /**
         * Filter added in timeline
         * 安装在timeline的滤镜
         */
        public final static String SUB_TYPE_TIMELINE_FILTER = "timelineFilter";
        /**
         * plug Fx
         * 插件特效
         */
        public final static String SUB_TYPE_PLUG_FX = "plugFx";
        /**
         * Transform for cropper
         * 用于裁剪的位移特效
         */
        public static final String SUB_TYPE_CROPPER_TRANSFORM = "cropper_transform";
        /**
         * Cropper
         * 裁剪特效
         */
        public static final String SUB_TYPE_CROPPER = "cropper";

        /**
         * Cropper extra
         * 新的裁剪特效
         */
        public static final String SUB_TYPE_CROPPER_EXT = "cropper_ext";

        public static final String SUB_TYPE_POST_CROPPER_TRANSFORM = "post_cropper_transform";
        /**
         * Mask
         * 蒙版特效
         */
        public static final String SUB_TYPE_MASK = "mask";
        /**
         * Adjust
         * 调节特效
         */
        public static final String SUB_TYPE_ADJUST = "adjust";
        /**
         * Alpha
         * Alpha特效
         */
        public static final String SUB_TYPE_ALPHA = "alpha";

        /**
         * 背景分割
         * segment
         */
        public static final String SUB_TYPE_SEGMENT = "segment";


        /**
         * AR Scene
         */
        public static final String SUB_TYPE_AR_SCENE = "AR Scene";

        /**
         * Master keyer
         */
        public static final String SUB_TYPE_MASTER_KEYER = "Master keyer";

        /**
         * the videoFx from timeline
         */
        public static final String SUB_TYPE_FROM_TIMELINE = "from timeline";
    }

    /**
     * Constants for subType of effect key.
     * This value is used to mark subType value
     * by accessing {NvsVideoFx.setAttachment(String key, Object value)} as first param.
     * <p>
     * 特效子类型标记key值，这个值用来调用NvsVideoFx.setAttachment(String key, Object value)时的key值，
     * 用来标记NvsVideoFx的子类型
     */
    public static final String ATTACHMENT_KEY_SUB_TYPE = "subType";

    /**
     * Class type tag, used to distinguish classification types when gosn deserializes
     * <p>
     * 类的类型标记，Gosn反序列化的时候用来区分类类型
     */
    protected String classType = "videoFx";
    /**
     * index. It is necessary
     * <p>
     * 索引 必传
     */
    protected int index;
    /**
     * type. builtin: built in; package
     * 特效类型（取值：builtin： 内建；package：包）
     */
    protected String type;
    /**
     * subTpe {@see SubType}
     * 特效次类型，{@see SubType}
     */
    protected String subType;

    /**
     * Clip description
     * If it is a built-in  effect, it is the name of the effect.
     * If it is a package  effect, it is the package ID of the  effect
     * <p>
     * 特效描述，如果是内建特效，是特效的名称，如果是包特效，则是特效的包id
     */
    protected String desc;
    /**
     * intensity
     * <p>
     * 强度
     */
    protected float intensity = 1;
    /**
     * Fx params
     * 特效参数
     */
    @SerializedName("fxParams")
    protected Map<String, MeicamFxParam<?>> mMeicamFxParam = new TreeMap<>();

    /**
     * Indicates whether it is a local effect. true:yes false:no
     * <p>
     * 表明是否是局部的特效 true:yes false:no
     */
    private boolean isRegional = false;

    /**
     * Region data for region effect
     * <p>
     * 区域特效数据
     */
    private float[] regionData;

    @SerializedName("maskRegionInfoData")
    public MaskRegionInfoData maskRegionInfoData;
    @SerializedName("meicamMaskInfo")
    private MeicamMaskInfo meicamMaskInfo;

    /**
     * The info for region effect of fx
     * 特效的区域特效
     */
    private MeicamMaskRegionInfo regionInfo;

    private boolean filterMask;

    private long inPoint;

    private long outPoint;

    /**
     * 时间线特效的标记集合
     * the tag set of timeline fx
     */
    private Set<String> timelineFxTagSet;

    private KeyFrameProcessor<NvsVideoFx> mKeyFrameHolder;

    MeicamVideoFx(NvsVideoFx videoFx, String type, String subType, String desc) {
        super(videoFx);
        if (videoFx != null) {
            this.index = videoFx.getIndex();
            inPointInClip = videoFx.getInPoint();
            outPointInClip = videoFx.getOutPoint();
        }
        this.type = type;
        this.subType = subType;
        this.desc = desc;
    }

    public int getIndex() {
        return index;
    }

    void setIndex(int index) {
        this.index = index;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public float getIntensity() {
        return intensity;
    }

    public void setSubType(String subType) {
        this.subType = subType;
    }

    public String getSubType() {
        return subType;
    }

    public boolean isRegional() {
        return isRegional;
    }

    /**
     * 设置特效区域
     * Set fx regional
     *
     * @param regional the regional 区域
     */
    public void setRegional(boolean regional) {
        NvsVideoFx object = getObject();
        if (object != null) {
            object.setRegional(regional);
            isRegional = regional;
        }
    }

    public float[] getRegionData() {
        return regionData;
    }

    public void setRegionData(float[] regionData) {
        if (regionData == null) {
            LogUtils.e("regionData is null");
            return;
        }

        NvsVideoFx object = getObject();
        if (object == null) {
            LogUtils.e("object is null");
            return;
        }
        object.setRegion(regionData);
        this.regionData = regionData;
    }

    @Deprecated
    public void setMeicamMaskInfo(MeicamMaskInfo meicamMaskInfo) {
        this.meicamMaskInfo = meicamMaskInfo;
    }

    @Deprecated
    public MeicamMaskInfo getMeicamMaskInfo() {
        return meicamMaskInfo;
    }

    @Deprecated
    public MaskRegionInfoData getMaskRegionInfoData() {
        return maskRegionInfoData;
    }

    @Deprecated
    public void setMaskRegionInfoData(MaskRegionInfoData maskRegionInfoData) {
        this.maskRegionInfoData = maskRegionInfoData;
    }

    public void setIntensity(float intensity) {
        if (Float.isNaN(intensity) || Float.isInfinite(intensity)) {
            return;
        }
        NvsVideoFx object = getObject();
        if (object != null) {
            this.intensity = intensity;
            object.setFilterIntensity(intensity);
        }
    }

    public void setFilterMask(boolean filterMask) {
        NvsVideoFx object = getObject();
        if (object != null) {
            object.setFilterMask(filterMask);
        }
        this.filterMask = filterMask;
    }

    @Deprecated
    public Map<String, MeicamFxParam<?>> getMeicamFxParam() {
        return mMeicamFxParam;
    }

    /**
     * Get key for key frame string.
     * 获取用于关键帧的关键字
     *
     * @return the string
     */
    public String getKeyForKeyFrame() {
        Set<String> keys = mMeicamFxParam.keySet();
        if (!CommonUtils.isEmpty(keys)) {
            Object[] objects = keys.toArray();
            return String.valueOf(objects[0]);
        }
        return null;
    }

    @Override
    public long getInPoint() {
        return inPoint;
    }

    @Override
    public long getOutPoint() {
        return outPoint;
    }

    public Set<String> getTimelineFxTag() {
        return timelineFxTagSet;
    }

    void setTimelineFxTag(Set<String> timelineFxTag) {
        this.timelineFxTagSet = timelineFxTag;
    }

    public void addTimelineFxTag(String timelineFxTag) {
        if (timelineFxTagSet == null) {
            timelineFxTagSet = new HashSet<>();
        }
        timelineFxTagSet.add(timelineFxTag);
    }

    /**
     * Remove timeline fx tag set.
     * 删除tag
     *
     * @param timelineFxTag the timeline fx tag
     */
    public void removeTimelineFxTagSet(String timelineFxTag) {
        if (timelineFxTagSet == null) {
            return;
        }
        timelineFxTagSet.remove(timelineFxTag);
    }

    public boolean hasTag(String tag){
        if (timelineFxTagSet == null) {
            return false;
        }
        return timelineFxTagSet.contains(tag);
    }


    @Override
    void loadData() {
        NvsVideoFx nvsVideoFx = getObject();
        if (nvsVideoFx != null) {
            index = nvsVideoFx.getIndex();
            intensity = nvsVideoFx.getFilterIntensity();
            isRegional = nvsVideoFx.getRegional();
            String builtinVideoFxName = nvsVideoFx.getBuiltinVideoFxName();
            if (TextUtils.isEmpty(builtinVideoFxName)) {
                desc = nvsVideoFx.getVideoFxPackageId();
            } else {
                //desc = builtinVideoFxName;
                List<String[]> fxParams = NvsConstants.getFxParams(builtinVideoFxName);
                if (fxParams.size() != 0) {
                    for (String[] fxParam : fxParams) {
                        String type = fxParam[0];
                        String key = fxParam[1];
                        if (TYPE_FLOAT.equals(type)) {
                            setFloatVal(key, (float) nvsVideoFx.getFloatVal(key));
                        } else if (TYPE_BOOLEAN.equals(type)) {
                            setBooleanVal(key, nvsVideoFx.getBooleanVal(key));
                        } else if (TYPE_STRING.equals(type)) {
                            if (AMPLITUDE.equals(key)) {
                                setExprVar(key, nvsVideoFx.getExprVar(key));
                            } else if (KEY_BACKGROUND_MODE.equals(key)) {
                                setMenuVal(key, nvsVideoFx.getMenuVal(key));
                            } else {
                                setStringVal(key, nvsVideoFx.getStringVal(key));
                            }
                        } else if (TYPE_OBJECT.equals(type)) {
                            if (NvsConstants.KEY_MASK_REGION_INFO.equals(key)) {
                                MeicamMaskRegionInfo maskRegionInfo = new MeicamMaskRegionInfo();
                                maskRegionInfo.setRegionInfo((NvsMaskRegionInfo) getArbDataValFromNvs(NvsConstants.KEY_MASK_REGION_INFO));
                                setObjectVal(NvsConstants.KEY_MASK_REGION_INFO, maskRegionInfo);
                            }  else if (KEY_PROPERTY_MASK_REGION_INFO.equals(key)) {
                                MeicamMaskRegionInfo maskRegionInfo = new MeicamMaskRegionInfo();
                                maskRegionInfo.setRegionInfo((NvsMaskRegionInfo) getArbDataValFromNvs(NvsConstants.KEY_PROPERTY_MASK_REGION_INFO));
                                setObjectVal(NvsConstants.KEY_PROPERTY_MASK_REGION_INFO, maskRegionInfo);
                            } else if (NvsConstants.MasterKeyer.KEYER_MODE.equals(key)) {
                                setMenuVal(key, nvsVideoFx.getMenuVal(key));
                            } else if (NvsConstants.MasterKeyer.KEY_COLOR.equals(key)) {
                                NvsColor colorVal = nvsVideoFx.getColorVal(key);
                                if (colorVal != null) {
                                    setColor(key, ColorUtil.nvsColorToHexString(colorVal));
                                }
                            }
                        }
                    }
                } else {
                    PlugDetail plugDetail = AtomicFxBridge.getParamListByEffectID(builtinVideoFxName);
                    if (plugDetail != null) {
                        for (PlugDetail.Param param : plugDetail.paramList) {
                            String valueType = param.valueType;
                            String paramName = param.paramName;
                            if (Constants.PlugType.BOOL.equals(valueType)) {
                                //bool形式的
                                boolean nvsValue = nvsVideoFx.getBooleanVal(paramName);
                                setBooleanVal(paramName, nvsValue);
                            } else if (Constants.PlugType.FLOAT.equals(valueType)) {
                                //float形式的
                                float nvsValue = (float) nvsVideoFx.getFloatVal(paramName);
                                setFloatVal(paramName, nvsValue);
                            } else if (Constants.PlugType.PATH.equals(valueType)
                                    || Constants.PlugType.STRING.equals(valueType)
                                    || Constants.PlugType.CURVE.equals(valueType)) {
                                String nvsValue = nvsVideoFx.getStringVal(paramName);
                                setStringVal(paramName, nvsValue);
                            } else if (Constants.PlugType.COLOR.equals(valueType)) {
                                NvsColor nvsColor = nvsVideoFx.getColorVal(paramName);
                                if (nvsColor != null) {
                                    String color = ColorUtil.nvsColorToHexString(nvsColor);
                                    setColor(paramName, color);
                                }
                            } else if (Constants.PlugType.MENU.equals(valueType)) {
                                String nvsValue = nvsVideoFx.getMenuVal(paramName);
                                setMenuVal(paramName, nvsValue);
                            } else if (Constants.PlugType.INT.equals(valueType) ||
                                    Constants.PlugType.INT_CHOOSE.equals(valueType)) {
                                int nvsValue = nvsVideoFx.getIntVal(paramName);
                                setIntVal(paramName, nvsValue);
                            }
                        }
                    }
                }


            }
            NvsMaskRegionInfo regionInfo = nvsVideoFx.getRegionInfo();
            if (regionInfo != null) {
                MeicamMaskRegionInfo maskRegionInfo = new MeicamMaskRegionInfo();
                maskRegionInfo.setRegionInfo(regionInfo);
                setRegionInfo(maskRegionInfo);
            }
        }
        keyFrameProcessor().recoverFromTimelineData(getObject());
    }

    /**
     * Set menu value
     * 设置menu值
     *
     * @param key   key键
     * @param value value值
     */
    public void setMenuVal(String key, String value) {
        NvsVideoFx object = getObject();
        if (object != null) {
            object.setMenuVal(key, value);
        }
        MeicamFxParam<String> param = new MeicamFxParam<>(TYPE_MENU, key, value);
        mMeicamFxParam.put(param.getKey(), param);
    }


    /**
     * Get menu value
     * 获取menu值
     *
     * @param key key键
     */
    public String getMenuVal(String key) {
        MeicamFxParam<?> meicamFxParam = mMeicamFxParam.get(key);
        if (meicamFxParam == null) {
            return null;
        }
        if (TYPE_MENU.equals(meicamFxParam.getType())) {
            return (String) meicamFxParam.getValue();
        }
        return null;
    }

    /**
     * Set Expr Var
     * 设置ExprVar值
     *
     * @param key   key键
     * @param value value值
     */
    public void setExprVar(String key, double value) {
        NvsVideoFx object = getObject();
        if (object != null) {
            object.setExprVar(key, value);
        }
        MeicamFxParam<String> param = new MeicamFxParam<>(TYPE_STRING, key, String.valueOf(value));
        mMeicamFxParam.put(param.getKey(), param);
    }

    /**
     * Set String value into fxParam map
     * * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value.
     * 设置String类型的特效参数，如果有这个key值，新的会替换旧的
     *
     * @param key   key
     * @param value value
     */
    public void setStringVal(String key, String value) {
        NvsVideoFx object = getObject();
        if (object != null) {
            object.setStringVal(key, value);
        }
        MeicamFxParam<String> param = new MeicamFxParam<>(TYPE_STRING, key, value);
        mMeicamFxParam.put(param.getKey(), param);
    }

    /**
     * Get String val from fxParam map
     * <p>
     * 从fxParam中获取String类型的值
     *
     * @param key key
     * @return value
     */
    public String getStringVal(String key) {
        MeicamFxParam<?> meicamFxParam = mMeicamFxParam.get(key);
        if (meicamFxParam == null) {
            return null;
        }
        if (TYPE_STRING.equals(meicamFxParam.getType())) {
            return (String) meicamFxParam.getValue();
        }
        return null;
    }

    /**
     * Get float val from fxParam map
     * <p>
     * 从fxParam中获取float类型的值
     *
     * @param key key
     * @return value
     */
    public float getFloatVal(String key) {
        MeicamFxParam<?> meicamFxParam = mMeicamFxParam.get(key);
        if (meicamFxParam == null) {
            return INVALID_VALUE;
        }
        if (TYPE_FLOAT.equals(meicamFxParam.getType())) {
            Object value = meicamFxParam.getValue();
            if (value instanceof Float) {
                return (float) value;
            } else if (value instanceof Double) {
                double resultD = (double) value;
                return (float) resultD;
            }
        }
        return INVALID_VALUE;
    }


    /**
     * Get float val from fxParam map
     * <p>
     * 从fxParam中获取float类型的值
     *
     * @param key          key
     * @param defaultValue the default value
     * @return value
     */
    public float getFloatVal(String key, float defaultValue) {
        float floatVal = getFloatVal(key);
        boolean valueEquals = Math.abs(floatVal - INVALID_VALUE) < 0.000000002;
        return valueEquals ? defaultValue : floatVal;
    }

    /**
     * Get float val from NvsObject
     * <p>
     * 从NvsObject中获取float类型的值
     *
     * @param key key
     * @return value
     */
    public float getNvsFloatVal(String key) {
        NvsVideoFx object = getObject();
        if (object != null) {
            return (float) object.getFloatVal(key);
        }
        return INVALID_VALUE;
    }

    /**
     * Get boolean val from fxParam map
     * <p>
     * 从fxParam中获取boolean类型的值
     *
     * @param key key
     * @return value
     */
    public boolean getBooleanVal(String key) {
        MeicamFxParam<?> meicamFxParam = mMeicamFxParam.get(key);
        if (meicamFxParam == null) {
            return false;
        }
        if (TYPE_BOOLEAN.equals(meicamFxParam.getType())) {
            Object value = meicamFxParam.getValue();
            if (value instanceof Boolean) {
                return (boolean) value;
            }
        }
        return false;
    }

    /**
     * Get  val from fxParam map
     * <p>
     * 从fxParam中是否存在该值
     *
     * @param key key
     * @return value
     */
    public boolean getParamVal(String key) {
        MeicamFxParam<?> meicamFxParam = mMeicamFxParam.get(key);
        return meicamFxParam != null;
    }

    /**
     * Set Boolean value into fxParam map
     * * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value.
     * 设置Boolean类型的特效参数，如果有这个key值，新的会替换旧的
     *
     * @param key   key
     * @param value value
     */
    public void setBooleanVal(String key, boolean value) {
        NvsVideoFx object = getObject();
        if (object != null) {
            object.setBooleanVal(key, value);
        }
        MeicamFxParam<Boolean> param = new MeicamFxParam<>(TYPE_BOOLEAN, key, value);
        mMeicamFxParam.put(param.getKey(), param);
    }

    /**
     * Set float value into fxParam map
     * * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value.
     * 设置float类型的特效参数，如果有这个key值，新的会替换旧的
     *
     * @param key   key
     * @param value value
     */
    public void setFloatVal(String key, float value) {
        if (Float.isNaN(value) || Float.isInfinite(value)) {
            return;
        }
        NvsVideoFx object = getObject();
        if (object != null) {
            object.setFloatVal(key, value);
        }
        MeicamFxParam<Float> param = new MeicamFxParam<>(TYPE_FLOAT, key, value);
        mMeicamFxParam.put(param.getKey(), param);
    }

    public void setPosition2DVal(String key, MeicamPosition2D value) {

        NvsVideoFx object = getObject();
        if (object != null) {
            object.setPosition2DVal(key, new NvsPosition2D(value.x, value.y));
        }
        MeicamFxParam<MeicamPosition2D> param = new MeicamFxParam<>(TYPE_POSITION_2D, key, value);
        mMeicamFxParam.put(param.getKey(), param);
    }

    public MeicamPosition2D getPosition2DVal(String key) {
        MeicamFxParam<?> meicamFxParam = mMeicamFxParam.get(key);
        if (meicamFxParam == null) {
            return null;
        }
        if (TYPE_POSITION_2D.equals(meicamFxParam.getType())) {
            return (MeicamPosition2D) meicamFxParam.getValue();
        }
        return null;
    }

    /**
     * Set Int value into fxParam map
     * * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value.
     * 设置int类型的特效参数，如果有这个key值，新的会替换旧的
     *
     * @param key   key
     * @param value value
     */
    public void setIntVal(String key, int value) {
        if (Float.isNaN(value)) {
            //防止gson解析报错,IllegalArgumentException: JSON forbids NaN and infinities: NaN
            //Prevent error in gson parsing:IllegalArgumentException: JSON forbids NaN and infinities: NaN.
            value = 0;
        }
        NvsVideoFx object = getObject();
        if (object != null) {
            object.setIntVal(key, value);
        }
        MeicamFxParam<Integer> param = new MeicamFxParam<>(TYPE_INT, key, value);
        mMeicamFxParam.put(param.getKey(), param);
    }

    /**
     * Get int val from fxParam map
     * <p>
     * 从fxParam中获取int类型的值
     *
     * @param key key
     * @return value
     */
    public int getIntVal(String key) {
        MeicamFxParam<?> meicamFxParam = mMeicamFxParam.get(key);
        if (meicamFxParam == null) {
            return INVALID_VALUE_INT;
        }
        if (TYPE_INT.equals(meicamFxParam.getType())) {
            Object value = meicamFxParam.getValue();
            if (value instanceof Integer) {
                return (int) value;
            }
        }
        return INVALID_VALUE_INT;
    }

    /**
     * Set Object value into fxParam map.
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value.
     * 给特效参数设置对象，如果有这个key值，新的会替换旧的
     *
     * @param key   key
     * @param value value
     * @param <T>   参数化对象 Parameterized object
     */
    public <T> void setObjectVal(String key, T value) {
        NvsVideoFx object = getObject();
        if (object != null) {
            if (value instanceof MeicamMaskRegionInfo) {
                MeicamMaskRegionInfo meicamMaskRegionInfo = (MeicamMaskRegionInfo) value;
                object.setArbDataVal(key, meicamMaskRegionInfo.getMaskRegionInfo());
            }
        }
        MeicamFxParam<T> param = new MeicamFxParam<>(TYPE_OBJECT, key, value);
        mMeicamFxParam.put(param.getKey(), param);
    }

    /**
     * 从底层特效取得argb 数据
     * Get arb data from NvsFx
     *
     * @param key the key键值
     * @return NvsArbitraryData
     */
    NvsArbitraryData getArbDataValFromNvs(String key) {
        NvsVideoFx nvsVideoFx = getObject();
        if (nvsVideoFx != null) {
            return nvsVideoFx.getArbDataVal(key);
        }
        return null;
    }

    /**
     * Gets object val.
     * 获取存入的Object对象
     *
     * @param key the key
     * @return the object val
     */
    public Object getObjectVal(String key) {
        NvsVideoFx object = getObject();
        if (object != null) {
            MeicamFxParam<?> meicamFxParam = mMeicamFxParam.get(key);
            if (meicamFxParam != null) {
                return meicamFxParam.getValue();
            }
        }
        return null;
    }

    /**
     * 设置颜色
     * Set color
     *
     * @param key   key 键值
     * @param value color 颜色
     */
    public void setColor(String key, String value) {
        if (TextUtils.isEmpty(key) || TextUtils.isEmpty(value)) {
            return;
        }
        setNvsColor(key, ColorUtil.colorToNvsColor(value));
        MeicamFxParam<String> param = new MeicamFxParam<>(TYPE_COLOR, key, value);
        mMeicamFxParam.put(param.getKey(), param);
    }

    /**
     * 设置颜色
     * Set color
     *
     * @param key   key 键值
     * @param value color 颜色
     */
    public void setColor(String key,  NvsColor value) {
        if (TextUtils.isEmpty(key) || value == null) {
            return;
        }
        setNvsColor(key, value);
        MeicamFxParam<String> param = new MeicamFxParam<>(TYPE_COLOR, key, ColorUtil.nvsColorToHexString(value));
        mMeicamFxParam.put(param.getKey(), param);
    }

    /**
     * Gets color.
     * 获取颜色值
     *
     * @param key the key
     * @return the color
     */
    public String getColor(String key) {
        MeicamFxParam<?> meicamFxParam = mMeicamFxParam.get(key);
        if (meicamFxParam == null) {
            return null;
        }
        if (TYPE_COLOR.equals(meicamFxParam.getType())) {
            Object value = meicamFxParam.getValue();
            if (value instanceof String) {
                return (String) value;
            }
        }
        return null;
    }


    /**
     * 设置颜色
     * Set color
     *
     * @param key   key 键值
     * @param color color 颜色
     */
    private boolean setNvsColor(String key, NvsColor color) {
        NvsVideoFx object = getObject();
        if (object != null) {
            object.setColorVal(key, color);
            return true;
        }
        return false;
    }

    /**
     * Find key frame time long.
     * 在当前时间点查找某参数的关键帧时间
     *
     * @param fxParam the fx param 特效参数
     * @param time    the time 当前时间点
     * @param flags   the flags flag，用于决定向前查找还是向后查找
     * @return the long 查找的关键帧时间
     */
    public long findKeyFrameTime(String fxParam, long time, int flags) {
        return getObject().findKeyframeTime(fxParam, time, flags);
    }

    /**
     * Sets float val at time.
     * 设置某个时间的float值
     *
     * @param key   the key key值
     * @param value the value value值
     * @param time  the time 时间点
     */
    public void setFloatValAtTime(String key, float value, long time) {
        if (Float.isNaN(value) || Float.isInfinite(value)) {
            return;
        }
        NvsVideoFx object = getObject();
        if (object != null) {
            object.setFloatValAtTime(key, value, time);
        }
    }

    /**
     * Get float val at time float.
     * 查找某个时间的float 值
     *
     * @param key  the key key值
     * @param time the time 时间点
     * @return the float value值
     */
    public float getFloatValAtTime(String key, long time) {
        NvsVideoFx object = getObject();
        if (object != null) {
            return (float) object.getFloatValAtTime(key, time);
        }
        return INVALID_VALUE;
    }

    /**
     * 设置默认背景
     * Set default background
     */
    public void setDefaultBackground() {
        setMenuVal(KEY_BACKGROUND_MODE, NvsConstants.VALUE_COLOR_BACKGROUND_MODE);
        setColor(NvsConstants.KEY_BACKGROUND_COLOR, ColorsConstants.BACKGROUND_DEFAULT_COLOR);
        setFloatVal(NvsConstants.FX_TRANSFORM_2D_SCALE_X, 1.0F);
        setFloatVal(NvsConstants.FX_TRANSFORM_2D_SCALE_Y, 1.0F);
        setFloatVal(NvsConstants.FX_TRANSFORM_2D_TRANS_X, 0F);
        setFloatVal(NvsConstants.FX_TRANSFORM_2D_TRANS_Y, 0F);
        setFloatVal(NvsConstants.FX_TRANSFORM_2D_ROTATION, 0F);
        setFloatVal(NvsConstants.KEY_BACKGROUND_BLUR_RADIUS, -1);
        setStringVal(KEY_BACKGROUND_IMAGE_PATH, "");
        setStringVal(NvsConstants.POST_PACKAGE_ID, "");
        setStringVal(NvsConstants.PACKAGE_ID, "");
        setBooleanVal(NvsConstants.IS_POST_STORY_BOARD_3D, false);
        setFloatVal(NvsConstants.PACKAGE_EFFECT_IN, 0);
        setFloatVal(NvsConstants.PACKAGE_EFFECT_OUT, 0);
    }

    /**
     * 设置背景
     * Set  background
     */
    public void setBackground() {
        NvsVideoFx nvsVideoFx = getObject();
        if (nvsVideoFx != null) {
            MeicamFxParam<?> meicamFxParam = mMeicamFxParam.get(KEY_BACKGROUND_MODE);
            if (meicamFxParam != null && meicamFxParam.getValue().equals(NvsConstants.VALUE_COLOR_BACKGROUND_MODE)) {
                nvsVideoFx.setMenuVal(KEY_BACKGROUND_MODE, NvsConstants.VALUE_COLOR_BACKGROUND_MODE);
                MeicamFxParam<?> fxParam = mMeicamFxParam.get(NvsConstants.KEY_BACKGROUND_COLOR);
                if (fxParam != null && fxParam.getValue() != null) {
                    nvsVideoFx.setColorVal(NvsConstants.KEY_BACKGROUND_COLOR, ColorUtil.colorToNvsColor((String) fxParam.getValue()));
                }
            } else if (meicamFxParam != null && meicamFxParam.getValue().equals(NvsConstants.VALUE_IMAGE_BACKGROUND_MODE)) {
                nvsVideoFx.setMenuVal(KEY_BACKGROUND_MODE, NvsConstants.VALUE_IMAGE_BACKGROUND_MODE);
                setStringValue(KEY_BACKGROUND_IMAGE_PATH);
            } else {
                nvsVideoFx.setMenuVal(KEY_BACKGROUND_MODE, NvsConstants.VALUE_BLUR_BACKGROUND_MODE);
                setFloatValue(NvsConstants.KEY_BACKGROUND_BLUR_RADIUS);
            }
            setFloatValue(NvsConstants.FX_TRANSFORM_2D_SCALE_X);
            setFloatValue(NvsConstants.FX_TRANSFORM_2D_SCALE_Y);
            setFloatValue(NvsConstants.FX_TRANSFORM_2D_TRANS_X);
            setFloatValue(NvsConstants.FX_TRANSFORM_2D_TRANS_Y);
            setFloatValue(NvsConstants.FX_TRANSFORM_2D_ROTATION);

            if (mMeicamFxParam.get(NvsConstants.POST_PACKAGE_ID) == null && mMeicamFxParam.get(NvsConstants.PACKAGE_ID) == null) {
                MeicamFxParam<String> paramNine = new MeicamFxParam<>("String", NvsConstants.POST_PACKAGE_ID, "");
                MeicamFxParam<Boolean> paramTen = new MeicamFxParam<>("boolean", NvsConstants.IS_POST_STORY_BOARD_3D, false);
                MeicamFxParam<Float> paramEle = new MeicamFxParam<>("float", NvsConstants.PACKAGE_EFFECT_IN, 0f);
                MeicamFxParam<Float> paramTwe = new MeicamFxParam<>("float", NvsConstants.PACKAGE_EFFECT_OUT, 0f);
                MeicamFxParam<String> paramThird = new MeicamFxParam<>("String", NvsConstants.PACKAGE_ID, "");
                mMeicamFxParam.put(NvsConstants.POST_PACKAGE_ID, paramNine);
                mMeicamFxParam.put(NvsConstants.IS_POST_STORY_BOARD_3D, paramTen);
                mMeicamFxParam.put(NvsConstants.PACKAGE_EFFECT_IN, paramEle);
                mMeicamFxParam.put(NvsConstants.PACKAGE_EFFECT_OUT, paramTwe);
                mMeicamFxParam.put(NvsConstants.PACKAGE_ID, paramThird);
            }
            setStringValue(NvsConstants.POST_PACKAGE_ID);
            setStringValue(NvsConstants.PACKAGE_ID);
            setBooleanValue(NvsConstants.IS_POST_STORY_BOARD_3D);
            setFloatValue(NvsConstants.PACKAGE_EFFECT_IN);
            setFloatValue(NvsConstants.PACKAGE_EFFECT_OUT);
        }
    }

    /**
     * 设置String类型属性
     * Set String value
     *
     * @param key the key 键值
     */
    private void setStringValue(String key) {
        MeicamFxParam<?> fxParam = mMeicamFxParam.get(key);
        Object value;
        if (fxParam != null && fxParam.getValue() != null) {
            value = fxParam.getValue();
            if (value instanceof String) {
                getObject().setStringVal(key, (String) value);
            }
        }
    }

    /**
     * 设置boolean类型属性
     * Set boolean value
     *
     * @param key the key 键值
     */
    private void setBooleanValue(String key) {
        MeicamFxParam<?> fxParam = mMeicamFxParam.get(key);
        Object value;
        boolean b = false;
        if (fxParam != null && fxParam.getValue() != null) {
            value = fxParam.getValue();
            if (value instanceof Boolean) {
                b = (boolean) value;
            }
        }
        getObject().setBooleanVal(key, b);
    }

    /**
     * 设置float类型属性
     * Set float value
     *
     * @param key the key 键值
     */
    private void setFloatValue(String key) {
        MeicamFxParam<?> fxParam = mMeicamFxParam.get(key);
        Object value;
        float number;
        if (fxParam != null && fxParam.getValue() != null) {
            value = fxParam.getValue();
            if (value instanceof Float) {
                number = (float) value;
            } else {
                number = Float.parseFloat(value.toString());
            }
            getObject().setFloatVal(key, number);
        }
    }

    /**
     * Copy param.
     * 复制参数
     * @param videoFx the video fx
     */
    public void copyParam(MeicamVideoFx videoFx){
        if (videoFx == null) {
            return;
        }
        setValue(getObject(), videoFx.mMeicamFxParam);
    }

    private void setValue(NvsVideoFx videoFx, Map<String, MeicamFxParam<?>> fxParam) {
        Set<String> keySet = fxParam.keySet();
        for (String key : keySet) {
            MeicamFxParam<?> meicamFxParam = fxParam.get(key);
            if (meicamFxParam == null) {
                continue;
            }
            if (TYPE_STRING.equals(meicamFxParam.getType())) {
                if (AMPLITUDE.equals(meicamFxParam.getKey())) {
                    Object value = meicamFxParam.getValue();
                    if (value != null) {
                        try {
                            videoFx.setExprVar(key, Double.parseDouble((String) value));
                        } catch (Exception ignore) {
                        }
                    }
                } else {
                    videoFx.setStringVal(key, (String) meicamFxParam.getValue());
                }
            } else if (TYPE_MENU.equals(meicamFxParam.getType())) {
                videoFx.setMenuVal(key, (String) meicamFxParam.getValue());
            } else if (TYPE_INT.equals(meicamFxParam.getType())) {
                Object value = meicamFxParam.getValue();
                if (value instanceof Integer) {
                    int intValue = (int) value;
                    setIntVal(meicamFxParam.getKey(), intValue);
                } else if (value instanceof Double) {
                    double dValue = (double) value;
                    setIntVal(meicamFxParam.getKey(), (int) dValue);
                }
            } else if (TYPE_BOOLEAN.equals(meicamFxParam.getType())) {
                videoFx.setBooleanVal(key, (Boolean) meicamFxParam.getValue());
            } else if (TYPE_FLOAT.equals(meicamFxParam.getType())) {
                Object value = meicamFxParam.getValue();
                if (value instanceof Float) {
                    float floatValue = (float) value;
                    videoFx.setFloatVal(key, floatValue);
                } else if (value instanceof Double) {
                    videoFx.setFloatVal(key, (Double) value);
                }
            } else if (TYPE_POSITION_2D.equals(meicamFxParam.getType())) {
                Object value = meicamFxParam.getValue();
                if (value instanceof MeicamPosition2D) {
                    MeicamPosition2D position2D = (MeicamPosition2D) value;
                    videoFx.setPosition2DVal(key, new NvsPosition2D(position2D.x, position2D.y));
                }
            } else if (TYPE_OBJECT.equals(meicamFxParam.getType())) {
                Object value = meicamFxParam.getValue();
                if (value instanceof NvsArbitraryData) {
                    videoFx.setArbDataVal(key, (NvsArbitraryData) value);
                } else if (KEY_CROPPER_REGION_INFO.equals(key) || KEY_PROPERTY_MASK_REGION_INFO.equals(key)) {
                    try {
                        String json = GsonUtils.toJson(value);
                        MeicamMaskRegionInfo maskRegionInfo = GsonUtils.fromJson(json, MeicamMaskRegionInfo.class);
                        videoFx.setArbDataVal(key, maskRegionInfo.getMaskRegionInfo());
                        return;
                    } catch (Exception e) {
                        LogUtils.e("1,error:" + e.getMessage());
                    }
                    try {
                        NvsMaskRegionInfo result = GsonUtils.fromJson(GsonUtils.toJson(value), NvsMaskRegionInfo.class);
                        if (result != null) {
                            videoFx.setArbDataVal(key, result);
                        }
                    } catch (Exception e) {
                        LogUtils.e("2,error:" + e.getMessage());
                    }
                }
            } else if (TYPE_COLOR.equals(meicamFxParam.getType())) {
                setColor(meicamFxParam.getKey(), (String) meicamFxParam.getValue());
            }
        }
    }


    void bindToTimeline() {
        NvsVideoFx object = getObject();
        if (object != null) {
            if (outPointInClip - inPointInClip > 0) {
                object.changeOutPoint(outPointInClip);
                object.changeInPoint(inPointInClip);
            }
            setValue(object, mMeicamFxParam);
            object.setFilterIntensity(getIntensity());
            MeicamMaskInfo meicamMaskInfo = getMeicamMaskInfo();
            if (null != meicamMaskInfo) {
                //这种方法不推荐，但是为了兼容老数据，保留
                //This method is not recommended, but to be compatible with old data.
                object.setBooleanVal(NvsConstants.KEY_MASK_KEEP_RGB, true);
                object.setBooleanVal(NvsConstants.KEY_MASK_INVERSE_REGION, meicamMaskInfo.isRevert());
                object.setFloatVal(NvsConstants.KEY_MASK_FEATHER_WIDTH, meicamMaskInfo.getFeatherWidth());
                setObjectVal(NvsConstants.KEY_MASK_REGION_INFO, meicamMaskInfo.getMaskRegionInfo());
            }

            /*还原关键帧
             * Restore key frame
             * */
            keyFrameProcessor().bindToTimeline();

            /*if (keyFrameMap != null && !keyFrameMap.isEmpty()) {
                Iterator<Map.Entry<Long, MeicamKeyFrame>> item = keyFrameMap.entrySet().iterator();
                while (item.hasNext()) {
                    Map.Entry<Long, MeicamKeyFrame> next = item.next();
                    if (next.getKey() > getOutPoint()) {
                        *//*删除无效的关键帧*//*
                        item.remove();
                    } else {
                        MeicamKeyFrame meicamKeyFrame = next.getValue();
                        meicamKeyFrame.setObject(getObject());
                        meicamKeyFrame.bindToTimeline(true);
                    }
                }
                updateKeyFrameControlPoints();
            }*/
        }
    }

    public void setAttachment() {
        setAttachment(ATTACHMENT_KEY_SUB_TYPE, getSubType());
    }

    public MeicamMaskRegionInfo getRegionInfo() {
        return regionInfo;
    }

    public void setRegionInfo(MeicamMaskRegionInfo regionInfo) {
        if (regionInfo == null) {
            return;
        }
        NvsVideoFx object = getObject();
        if (object != null) {
            object.setRegionInfo(regionInfo.getMaskRegionInfo());
            this.regionInfo = regionInfo;
        }
    }

    public void setInPoint(long inPoint) {
        this.inPoint = inPoint;
    }

    public void setOutPoint(long outPoint) {
        this.outPoint = outPoint;
    }

    public void changInPointInClip(long inPoint){
        NvsVideoFx object = getObject();
        if (object != null) {
            object.changeInPoint(inPoint);
        }
        this.inPointInClip = inPoint;
    }

    public void changOutPointInClip(long outPoint){
        NvsVideoFx object = getObject();
        if (object != null) {
            object.changeOutPoint(outPoint);
        }
        this.outPointInClip = outPoint;
    }

    public long getInPointInClip() {
        return inPointInClip;
    }

    public long getOutPointInClip() {
        return outPointInClip;
    }

    @NonNull
    @Override
    public MeicamVideoFx clone() {
        MeicamVideoFx clone = (MeicamVideoFx) DeepCopyUtil.deepClone(this);
        if (clone == null) {
            String jsonData = GsonContext.getInstance().toJson(this);
            if (!TextUtils.isEmpty(jsonData)) {
                return GsonContext.getInstance().fromJson(jsonData, MeicamVideoFx.class);
            }
        }
        if (clone == null) {
            clone = new MeicamVideoFx(null, getType(), getSubType(), getDesc());
            LogUtils.e("Just create a virtual object!");
        }
        return clone;
    }


    @Override
    public LMeicamVideoFx parseToLocalData() {
        LMeicamVideoFx local = new LMeicamVideoFx();
        setCommonData(local);
        return local;
    }

    @Override
    public void recoverFromLocalData(LMeicamVideoFx lVideoFx) {
        setExtraTag(lVideoFx.getExtraTag());
        setIntensity(lVideoFx.getIntensity());
        setInPoint(lVideoFx.getInPoint());
        setOutPoint(lVideoFx.getOutPoint());
        setTimelineFxTag(lVideoFx.getTimelineFxTag());
        LMaskRegionInfoData maskRegionInfoData = lVideoFx.getMaskRegionInfoData();
        if (null != maskRegionInfoData) {
            MaskRegionInfoData regionInfoData = new MaskRegionInfoData();
            regionInfoData.recoverFromLocalData(maskRegionInfoData);
            setMaskRegionInfoData(regionInfoData);
        }
        LMeicamMaskInfo lMeicamMaskInfo = lVideoFx.getLMeicamMaskRegionInfo();
        if (null != lMeicamMaskInfo) {
            MeicamMaskInfo meicamMaskInfo = new MeicamMaskInfo();
            meicamMaskInfo.recoverFromLocalData(lMeicamMaskInfo);
            setMeicamMaskInfo(meicamMaskInfo);
        }
        List<LMeicamFxParam<?>> meicamFxParam = lVideoFx.getMeicamFxParam();

        if (SubType.SUB_TYPE_MASK.equals(lVideoFx.getSubType())) {
            for (LMeicamFxParam<?> fxParam : meicamFxParam) {
                String key = fxParam.getKey();
                if (NvsConstants.KEY_MASK_GENERATOR.equals(key)){
                    fxParam.setKey(KEY_PROPERTY_MASK_REGION_INFO);
                } else if (NvsConstants.KEY_MASK_INVERSE_REGION.equals(key)) {
                    fxParam.setKey(NvsConstants.KEY_PROPERTY_MASK_INVERSE_REGION);
                } else if (NvsConstants.KEY_MASK_FEATHER_WIDTH.equals(key)) {
                    fxParam.setKey(NvsConstants.KEY_PROPERTY_MASK_FEATHER_WIDTH);
                }
            }
        }

        //遍历查询需要获取的参数，避免多次遍历 Traversal of the query parameters need to be obtained to avoid multiple traversal
        Map<String, LMeicamFxParam<?>> tempData = new HashMap<>();
        if (!CommonUtils.isEmpty(meicamFxParam)) {
            for (LMeicamFxParam<?> fxParam : meicamFxParam) {
                if (NvsConstants.KEY_BACKGROUND_COLOR.equals(fxParam.getKey())
                        || KEY_BACKGROUND_IMAGE_PATH.equals(fxParam.getKey())
                        || NvsConstants.AMPLITUDE.equals(fxParam.getKey())
                        || NvsConstants.KEY_BACKGROUND_BLUR_RADIUS.equals(fxParam.getKey())) {
                    tempData.put(fxParam.getKey(), fxParam);
                }
            }
        }

        if (!CommonUtils.isEmpty(meicamFxParam)) {
            for (LMeicamFxParam<?> fxParam : meicamFxParam) {
                if (TYPE_STRING.equals(fxParam.getType()) || TYPE_STRING_OLD.equals(fxParam.getType())
                        || TYPE_MENU.equals(fxParam.getType())) {
                    //背景参数 Param of background
                    if (NvsConstants.VALUE_COLOR_BACKGROUND_MODE.equals(fxParam.getValue())) {
                        setMenuVal(KEY_BACKGROUND_MODE, NvsConstants.VALUE_COLOR_BACKGROUND_MODE);
                        LMeicamFxParam<?> lMeicamFxParam = tempData.get(NvsConstants.KEY_BACKGROUND_COLOR);
                        if (lMeicamFxParam != null && lMeicamFxParam.getValue() instanceof String) {
                            setColor(NvsConstants.KEY_BACKGROUND_COLOR, (String) lMeicamFxParam.getValue());
                        }
                    } else if (NvsConstants.VALUE_IMAGE_BACKGROUND_MODE.equals(fxParam.getValue())) {
                        setMenuVal(KEY_BACKGROUND_MODE, NvsConstants.VALUE_IMAGE_BACKGROUND_MODE);
                        LMeicamFxParam<?> lMeicamFxParam = tempData.get(KEY_BACKGROUND_IMAGE_PATH);
                        if (lMeicamFxParam != null && lMeicamFxParam.getValue() instanceof String) {
                            setStringVal(KEY_BACKGROUND_IMAGE_PATH, (String) lMeicamFxParam.getValue());
                        }
                    } else if (NvsConstants.VALUE_BLUR_BACKGROUND_MODE.equals(fxParam.getValue())) {
                        setMenuVal(KEY_BACKGROUND_MODE, NvsConstants.VALUE_BLUR_BACKGROUND_MODE);
                        float radius = 0;
                        LMeicamFxParam<?> lMeicamFxParam = tempData.get(NvsConstants.KEY_BACKGROUND_BLUR_RADIUS);
                        if (lMeicamFxParam != null) {
                            Object valueRadius = lMeicamFxParam.getValue();
                            if (valueRadius != null) {
                                if (valueRadius instanceof Float) {
                                    radius = (float) valueRadius;
                                } else {
                                    radius = Float.parseFloat(valueRadius.toString());
                                }
                            }
                            setFloatVal(NvsConstants.KEY_BACKGROUND_BLUR_RADIUS, radius);
                        }
                    } else if (NvsConstants.MasterKeyer.KEY_COLOR.equals(fxParam.getKey())) {
                        setColor(fxParam.getKey(), (String) fxParam.getValue());
                    } else if (TYPE_MENU.equals(fxParam.getType())) {
                        setMenuVal(fxParam.getKey(), (String) fxParam.getValue());
                    } else {
                        //其他 Other
                        setStringVal(fxParam.getKey(), (String) fxParam.getValue());
                    }
                } else if (TYPE_BOOLEAN.equals(fxParam.getType())) {
                    setBooleanVal(fxParam.getKey(), (Boolean) fxParam.getValue());
                } else if (TYPE_INT.equals(fxParam.getType())) {
                    Object value = fxParam.getValue();
                    if (value instanceof Integer) {
                        int intValue = (int) value;
                        setIntVal(fxParam.getKey(), intValue);
                    } else if (value instanceof Double) {
                        double dValue = (double) value;
                        setIntVal(fxParam.getKey(), (int) dValue);
                    }

                } else if (TYPE_FLOAT.equals(fxParam.getType())) {
                    Object value = fxParam.getValue();
                    if (value instanceof Float) {
                        float floatValue = (float) value;
                        setFloatVal(fxParam.getKey(), floatValue);
                    } else if (value instanceof Double) {
                        double dValue = (double) value;
                        setFloatVal(fxParam.getKey(), (float) dValue);
                    }
                } else if (TYPE_POSITION_2D.equals(fxParam.getType())) {
                    Object value = fxParam.getValue();
                    if (value instanceof MeicamPosition2D) {
                        setPosition2DVal(fxParam.getKey(), (MeicamPosition2D) fxParam.getValue());
                    }
                } else if (TYPE_OBJECT.equals(fxParam.getType())) {
                    String key = fxParam.getKey();
                    if (NvsConstants.KEY_MASK_REGION_INFO.equals(key) || KEY_PROPERTY_MASK_REGION_INFO.equals(key)) {
                        convertObject(key, fxParam.getValue());
                    } else if (NvsConstants.MasterKeyer.KEYER_MODE.equals(key)) {
                        setMenuVal(key, String.valueOf(fxParam.getValue()));
                    } else if (NvsConstants.MasterKeyer.KEY_COLOR_POSITION.equals(key)) {
                        Object value = fxParam.getValue();
                        if (value != null) {
                            String json = GsonUtils.toJson(fxParam.getValue());
                            value = GsonUtils.fromJson(json, FloatPoint.class);
                            setObjectVal(key, value);
                        }
                    }
                } else if (TYPE_COLOR.equals(fxParam.getType())) {
                    setColor(fxParam.getKey(), (String) fxParam.getValue());
                }

                //正则表达式 Regular expression value
                if (NvsConstants.AMPLITUDE.equals(fxParam.getKey())) {
                    LMeicamFxParam<?> lMeicamFxParam = tempData.get(NvsConstants.AMPLITUDE);
                    if (lMeicamFxParam != null && lMeicamFxParam.getValue() instanceof String) {
                        try {
                            setExprVar(NvsConstants.AMPLITUDE, Double.parseDouble((String) lMeicamFxParam.getValue()));
                        } catch (Exception ignore) {
                        }
                    }
                }
            }
        }
        LMeicamMaskRegionInfo localRegionInfo = lVideoFx.getRegionInfo();
        if (localRegionInfo != null) {
            MeicamMaskRegionInfo regionInfo = new MeicamMaskRegionInfo();
            regionInfo.recoverFromLocalData(localRegionInfo);
            setRegionInfo(regionInfo);
        }
        keyFrameProcessor().recoverFromLocalData(lVideoFx.getKeyFrameProcessor());
    }

    @Override
    public void recoverFromTimelineData(com.meicam.sdk.NvsObject nvsObject) {
        if (nvsObject instanceof NvsVideoFx) {
            setObject((NvsVideoFx) nvsObject);
            loadData();
        }
    }

    /**
     * 转化成需要的对象类
     * Convert object to set object value
     *
     * @param key   the key 键值
     * @param value the value 值
     */
    private void convertObject(String key, Object value) {
        if (KEY_CROPPER_REGION_INFO.equals(key) || KEY_PROPERTY_MASK_REGION_INFO.equals(key)) {
            try {
                String json = GsonUtils.toJson(value);
                value = GsonUtils.fromJson(json, MeicamMaskRegionInfo.class);
            } catch (Exception e) {
                LogUtils.e("error:" + e.getMessage());
            }
        }
        setObjectVal(key, value);
    }

    protected void setCommonData(LMeicamVideoFx local) {
        local.setExtraTag(getExtraTag());
        local.setIndex(getIndex());
        local.setType(getType());
        local.setSubType(getSubType());
        local.setDesc(getDesc());
        local.setIntensity(getIntensity());
        local.setInPoint(getInPoint());
        local.setOutPoint(getOutPoint());
        local.setTimelineFxTag(getTimelineFxTag());
        MaskRegionInfoData maskRegionInfoData = getMaskRegionInfoData();
        if (null != maskRegionInfoData) {
            LMaskRegionInfoData localData = maskRegionInfoData.parseToLocalData();
            local.setMaskRegionInfoData(localData);
        }
        MeicamMaskInfo meicamMaskInfo = getMeicamMaskInfo();
        if (null != meicamMaskInfo) {
            LMeicamMaskInfo lMeicamMaskInfo = meicamMaskInfo.parseToLocalData();
            local.setLMeicamMaskRegionInfo(lMeicamMaskInfo);
        }
        Set<String> keySet = mMeicamFxParam.keySet();
        for (String key : keySet) {
            MeicamFxParam<?> meicamFxParam = mMeicamFxParam.get(key);
            if (meicamFxParam != null) {
                local.getMeicamFxParam().add(meicamFxParam.parseToLocalData());
            }
        }
        local.setAttachment(attachment);
        MeicamMaskRegionInfo regionInfo = getRegionInfo();
        if (regionInfo != null) {
            local.setRegionInfo(regionInfo.parseToLocalData());
        }
        local.setKeyFrameProcessor(keyFrameProcessor().parseToLocalData());
    }

    protected void setCommonRecoverData(LMeicamVideoFx local) {
        setIndex(local.getIndex());
        String type = local.getType();
        if ("0".equals(type)) {
            type = TYPE_BUILD_IN;
        } else if ("1".equals(type)) {
            type = CommonData.TYPE_PACKAGE;
        }
        setType(type);
        setSubType(local.getSubType());
        setDesc(local.getDesc());
        setIntensity(local.getIntensity());
        LMaskRegionInfoData maskRegionInfoData = local.getMaskRegionInfoData();
        if (null != maskRegionInfoData) {
            MaskRegionInfoData maskData = new MaskRegionInfoData();
            maskData.recoverFromLocalData(maskRegionInfoData);
            setMaskRegionInfoData(maskData);
        }
        LMeicamMaskInfo lMaskRegionInfo = local.getLMeicamMaskRegionInfo();
        if (null != lMaskRegionInfo) {
            MeicamMaskInfo meicamMaskInfo = new MeicamMaskInfo();
            meicamMaskInfo.recoverFromLocalData(lMaskRegionInfo);
            setMeicamMaskInfo(meicamMaskInfo);
        }
        setValue(local);
    }

    private void setValue(LMeicamVideoFx local) {
        List<LMeicamFxParam<?>> meicamFxParam = local.getMeicamFxParam();
        if (!CommonUtils.isEmpty(meicamFxParam)) {
            for (LMeicamFxParam<?> fxParam : meicamFxParam) {
                if (TYPE_STRING.equals(fxParam.getType())) {
                    setStringVal(fxParam.getKey(), (String) fxParam.getValue());
                } else if (TYPE_BOOLEAN.equals(fxParam.getType())) {
                    setBooleanVal(fxParam.getKey(), (Boolean) fxParam.getValue());
                } else if (TYPE_FLOAT.equals(fxParam.getType())) {
                    Object value = fxParam.getValue();
                    if (value instanceof Float) {
                        float floatValue = (float) value;
                        setFloatVal(fxParam.getKey(), floatValue);
                    } else if (value instanceof Double) {
                        setFloatVal(fxParam.getKey(), (float) value);
                    }
                } else if (TYPE_POSITION_2D.equals(fxParam.getType())) {
                    Object value = fxParam.getValue();
                    if (value instanceof MeicamPosition2D) {
                        setPosition2DVal(fxParam.getKey(), (MeicamPosition2D) value);
                    }
                } else if (TYPE_OBJECT.equals(fxParam.getType())) {
                    setObjectVal(fxParam.getKey(), fxParam.getValue());
                } else if (TYPE_MENU.equals(fxParam.getType())) {
                    setMenuVal(fxParam.getKey(), (String) fxParam.getValue());
                } else if (TYPE_INT.equals(fxParam.getType())) {
                    Object value = fxParam.getValue();
                    if (value instanceof Integer) {
                        int intValue = (int) value;
                        setIntVal(fxParam.getKey(), intValue);
                    } else if (value instanceof Double) {
                        double dValue = (double) value;
                        setIntVal(fxParam.getKey(), (int) dValue);
                    }
                } else if (TYPE_COLOR.equals(fxParam.getType())) {
                    setColor(fxParam.getKey(), (String) fxParam.getValue());
                }
            }
        }
    }
}
