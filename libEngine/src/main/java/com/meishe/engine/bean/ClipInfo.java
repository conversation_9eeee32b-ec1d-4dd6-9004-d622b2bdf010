package com.meishe.engine.bean;

import android.annotation.SuppressLint;

import androidx.annotation.NonNull;

import com.meishe.engine.local.LClipInfo;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by CaoZhiChao on 2020/7/3 17:39
 */
public class ClipInfo<T> extends NvsObject<T> implements Cloneable, Comparable<ClipInfo<?>>, Serializable {

    /**
     * The client does not pay attention to the index, but looks up according to the inPoint.
     *
     * 客户端不关注index，而是根据inPoint查找。
     */
    private int index = -1;

    /**
     * index of track
     * <P></>
     * clip在轨道中的索引
     */
    private int trackIndex = -1;
    /**
     * type. It is necessary
     * <p>
     * 类型 必传
     */
    protected String type = "base";
    /**
     * inPoint
     * <p>
     * 入点
     */
    long inPoint;
    /**
     * outPoint
     * <p>
     * 出点
     */
    long outPoint;

    /**
     * Set the Z value.The higher the Z value, the higher the clip is
     * 设置Z值。Z值越大，clip越处于上层
     */
    protected float zValue;

    /**
     * 关键帧信息集合，黄线可以忽略，因为LongSparseArray无法序列化，clone方法重写了，需要可以序列化。
     * The key frame info map
     */
    @SuppressLint("UseSparseArrays")
    protected Map<Long, MeicamKeyFrame> keyFrameMap = new HashMap<>();

    public ClipInfo(T t) {
        super(t);
    }

    public ClipInfo(T t, String type) {
        super(t);
        this.type = type;
    }

    @Deprecated
    public ClipInfo(String type) {
        super(null);
        this.type = type;
    }

    public int getIndex() {
        return index;
    }

    void setIndex(int index) {
        this.index = index;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }


    /**
     * Get inPoint
     * <p>
     * 获取入点
     *
     * @return inPoint
     */
    @Override
    public long getInPoint() {
        return inPoint;
    }

    /**
     * Set inPoint
     * <p>
     * 设置入点
     *
     * @param inPoint 入点 inPoint
     */
    public void setInPoint(long inPoint) {
        this.inPoint = inPoint;
    }


    /**
     * Get outPoint
     * <p>
     * 获取出点
     *
     * @return outPoint
     */
    @Override
    public long getOutPoint() {
        return outPoint;
    }

    /**
     * Set outPoint
     * <p>
     * 设置出点
     *
     * @param outPoint 出点 outPoint
     */
    public void setOutPoint(long outPoint) {
        this.outPoint = outPoint;
    }

    public int getTrackIndex() {
        return trackIndex;
    }

    void setTrackIndex(int trackIndex) {
        this.trackIndex = trackIndex;
    }

    @Override
    public int compareTo(ClipInfo clipInfo) {
        if (inPoint < clipInfo.getInPoint()) {
            return -1;
        } else if (inPoint > clipInfo.getInPoint()) {
            return 1;
        }
        return 0;
    }

    /**
     * Invalid float .
     * 无效的float
     *
     * @param number the number
     * @return the boolean
     */
    public boolean invalidFloat(float number) {
        return Float.isNaN(number) || Float.isInfinite(number);
    }

    /**
     * 是否是非法的Double值
     * Whether the Double value is invalid
     *
     * @param value the double value double值
     * @return the boolean true is invalid 非法的，false not合法
     */
    public boolean invalidDouble(Double value) {
        return Double.isNaN(value) || Double.isInfinite(value);
    }

    protected void setCommonData(LClipInfo lClipInfo) {
        lClipInfo.setIndex(getIndex());
        lClipInfo.setInPoint(getInPoint());
        lClipInfo.setOutPoint(getOutPoint());
        lClipInfo.setType(getType());
        lClipInfo.setCreateTag(getCreateTag());
    }

    public LClipInfo parseToLocalData() {
        return null;
    }

    public String getFilePath() {
        return null;
    }

    public long getTrimIn() {
        return 0;
    }

    public double getSpeed() {
        return 0;
    }

    public void setZValue(float zValue) {
        this.zValue = zValue;
    }

    public float getZValue() {
        return zValue;
    }

    @NonNull
    @Override
    public Object clone() {
        return clone(true);
    }

    public Object clone(boolean changeCreateTag) {
        try {
            return super.clone();
        } catch (CloneNotSupportedException e) {
            e.printStackTrace();
        }
        return new Object();
    }

}
