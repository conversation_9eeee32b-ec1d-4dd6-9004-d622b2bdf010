package com.meishe.engine.bean;

import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.meicam.sdk.NvsObject;
import com.meicam.sdk.NvsTimeline;
import com.meicam.sdk.NvsTimelineVideoFx;
import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.LogUtils;
import com.meishe.base.utils.Utils;
import com.meishe.engine.adapter.TimelineDataParserAdapter;
import com.meishe.engine.bean.bridges.FileInfoBridge;
import com.meishe.engine.constant.NvsConstants;
import com.meishe.engine.local.LMeicamTimelineVideoFilterAndAdjustClip;
import com.meishe.engine.local.LMeicamTimelineVideoFxClip;
import com.meishe.engine.local.LMeicamTimelineVideoFxTrack;
import com.meishe.engine.util.DeepCopyUtil;
import com.meishe.engine.util.gson.GsonContext;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.meishe.engine.bean.MeicamTimelineVideoFxClip.ClipFxType.SUB_TYPE_TIMELINE_ADJUST;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2020/7/3 19:25
 * @Description :timeline特效轨道  timeline videoFx track
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class MeicamTimelineVideoFxTrack extends TrackInfo<NvsTimeline> implements Cloneable, Serializable, TimelineDataParserAdapter<LMeicamTimelineVideoFxTrack> {

    /**
     * clip数组
     * An ordered collection of clip info.
     */
    protected List<MeicamTimelineVideoFxClip> clipInfos = new ArrayList<>();

    /**
     * 滤镜和调节所对应的字段
     * Fields corresponding to filter and adjustment
     */
    protected List<MeicamTimelineVideoFilterAndAdjustClip> filterAndAdjustClips = new ArrayList<>();


    /**
     * Constructs an MeicamTimelineVideoFxTrack
     *
     * @param timeline 时间线 NvsTimeline
     * @param index    轨道index the track index
     */
    public MeicamTimelineVideoFxTrack(NvsTimeline timeline, int index, String trackType) {
        super(timeline, trackType, index);
    }

    /**
     * Add clip
     * <p>
     * 添加clip
     *
     * @param fxClip 特效数据 video fx data
     * @return 特效 timeline videoFx
     */
    public MeicamTimelineVideoFxClip addFxClip(MeicamTimelineVideoFxClip fxClip) {
        if (getObject() == null || fxClip == null) {
            return null;
        }

        MeicamTimelineVideoFxClip videoFxClip = addFxClip(fxClip, fxClip.getInPoint(), fxClip.getOutPoint() - fxClip.getInPoint(), fxClip.getDesc());
        if (videoFxClip != null) {
            videoFxClip.copyData(fxClip);
        }
        return videoFxClip;
    }


    /**
     * Add clip
     * <p>
     * 添加clip
     *
     * @param fxClip      特效数据 video fx data
     * @param inPoint     入点 inPoint
     * @param duration    时长 duration
     * @param videoFxName 特效名称 videoFx Name
     * @return 特效 timeline videoFx
     */
    public MeicamTimelineVideoFxClip addFxClip(MeicamTimelineVideoFxClip fxClip, long inPoint, long duration, String videoFxName) {
        return addFxClip(fxClip, inPoint, duration, videoFxName, false);
    }

    /**
     * Add clip
     * <p>
     * 添加clip
     *
     * @param fxClip      特效数据 video fx data
     * @param inPoint     入点 inPoint
     * @param duration    时长 duration
     * @param videoFxName 特效名称 videoFx Name
     * @param videoFxName 是否需要新建tag need crate tag
     * @return 特效 timeline videoFx
     */
    public MeicamTimelineVideoFxClip addFxClip(MeicamTimelineVideoFxClip fxClip, long inPoint, long duration, String videoFxName, boolean needCrateTag) {
        if (getObject() == null || fxClip == null) {
            return null;
        }
        MeicamTimelineVideoFxClip videoFxClip = addFxClip(fxClip.getClipType(), inPoint, duration, videoFxName);
        if (videoFxClip != null) {
            videoFxClip.copyData(fxClip);
            if (needCrateTag) {
                videoFxClip.generateCreateTag();
            }
        }
        return videoFxClip;
    }

    /**
     * Add clip
     * <p>
     * 添加 特效 滤镜和调节clip
     *
     * @param type        特效类型 type builtin:内建特效 package：包特效
     * @param inPoint     入点 inPoint
     * @param duration    时长 duration
     * @param videoFxName 特效名称 videoFx Name
     * @return 特效 timeline videoFx
     */
    public MeicamTimelineVideoFxClip addFxClip(String type, long inPoint, long duration, String videoFxName) {
        if (getObject() == null || TextUtils.isEmpty(type) || duration <= 0) {
            return null;
        }
        NvsTimelineVideoFx nvsTimelineVideoFx = addNvsTimelineVideoFx(type, inPoint, duration, videoFxName);
        return addMeicamFxClip(nvsTimelineVideoFx);
    }

    /**
     * 增加特效上层数据
     * Add meicam fx clip meicam timeline video fx clip.
     *
     * @param nvsTimelineVideoFx the nvs timeline video fx
     * @return the meicam timeline video fx clip
     */
    public MeicamTimelineVideoFxClip addMeicamFxClip(NvsTimelineVideoFx nvsTimelineVideoFx) {
        if (nvsTimelineVideoFx == null) {
            return null;
        }
        MeicamTimelineVideoFxClip videoFxClip = new MeicamTimelineVideoFxClip(nvsTimelineVideoFx,
                nvsTimelineVideoFx.getTimelineVideoFxType() == 1 ? CommonData.TYPE_PACKAGE : CommonData.TYPE_BUILD_IN, nvsTimelineVideoFx.getInPoint(),
                nvsTimelineVideoFx.getOutPoint() - nvsTimelineVideoFx.getInPoint(), nvsTimelineVideoFx.getTimelineVideoFxType() == 1 ?
                nvsTimelineVideoFx.getTimelineVideoFxPackageId() : nvsTimelineVideoFx.getBuiltinTimelineVideoFxName());
        videoFxClip.setIntensity(1.0F);
        videoFxClip.setTrackIndex(getIndex());
        videoFxClip.setIndex(clipInfos.size());
        videoFxClip.setSubType(MeicamTimelineVideoFxClip.ClipFxType.SUB_TYPE_TIMELINE_FX);
        clipInfos.add(videoFxClip);
        if (!clipInfos.isEmpty()) {
            float zValue = getzValue();
            for (MeicamTimelineVideoFxClip clipInfo : clipInfos) {
                clipInfo.setZValue(zValue);
            }
        }
        Collections.sort(clipInfos);
        for (int index = 0; index < clipInfos.size(); index++) {
            MeicamTimelineVideoFxClip clip = clipInfos.get(index);
            clip.setIndex(index);
        }
        return videoFxClip;
    }

    /**
     * 添加 滤镜和调节clip
     * Add filter and adjust clip meicam timeline video filter and adjust clip.
     *
     * @param oldClip the old clip
     * @return the meicam timeline video filter and adjust clip
     */
    public MeicamTimelineVideoFilterAndAdjustClip addFilterAndAdjustClip(MeicamTimelineVideoFilterAndAdjustClip oldClip) {
        if (getObject() == null || oldClip == null) {
            return null;
        }

        MeicamTimelineVideoFilterAndAdjustClip newClip = new MeicamTimelineVideoFilterAndAdjustClip(getObject(), oldClip.getType(), oldClip.getInPoint(),
                oldClip.getOutPoint() - oldClip.getInPoint());
        newClip.setTrackIndex(getIndex());
        newClip.setDisplayText(oldClip.getText());

        Map<String, MeicamTimelineVideoFxClip> oldMapClip = oldClip.getFilterAndAdjustClipInfos();
        Set<Map.Entry<String, MeicamTimelineVideoFxClip>> entries = oldMapClip.entrySet();
        for (Map.Entry<String, MeicamTimelineVideoFxClip> entry : entries) {
            MeicamTimelineVideoFxClip adjustVideoFx = newClip.addAdjustTimelineFx(entry.getValue(), newClip.getType());
            MeicamTimelineVideoFxClip videoFxClip = entry.getValue();
            if (adjustVideoFx != null) {
                adjustVideoFx.copyData(videoFxClip);
            }
        }
        addEffectToCollection(newClip);
        return newClip;
    }

    private void addEffectToCollection(MeicamTimelineVideoFilterAndAdjustClip filterOrAdjustClip) {
        if (filterAndAdjustClips == null) {
            filterAndAdjustClips = new ArrayList<>();
        }
        filterAndAdjustClips.add(filterOrAdjustClip);
        if (!filterAndAdjustClips.isEmpty()) {
            float zValue = getzValue();
            for (MeicamTimelineVideoFilterAndAdjustClip filterAndAdjustClip : filterAndAdjustClips) {
                filterAndAdjustClip.setZValue(zValue);
            }
        }
        Collections.sort(filterAndAdjustClips);
        for (int index = 0; index < filterAndAdjustClips.size(); index++) {
            MeicamTimelineVideoFilterAndAdjustClip clip = filterAndAdjustClips.get(index);
            clip.setIndex(index);
        }
    }

    /**
     * 添加 滤镜和调节clip
     * Add filter and adjust clip meicam timeline video filter and adjust clip.
     *
     * @param type     the type 片段类型
     * @param inPoint  the in point
     * @param duration the duration
     * @return the meicam timeline video filter and adjust clip
     */
    public MeicamTimelineVideoFilterAndAdjustClip addFilterAndAdjustClip(String type, long inPoint, long duration) {
        if (getObject() == null) {
            return null;
        }
        MeicamTimelineVideoFilterAndAdjustClip meicamTimelineVideoFilterAndAdjustClip = new MeicamTimelineVideoFilterAndAdjustClip(getObject(), type, inPoint, duration);
        meicamTimelineVideoFilterAndAdjustClip.setTrackIndex(getIndex());
        addEffectToCollection(meicamTimelineVideoFilterAndAdjustClip);
        return meicamTimelineVideoFilterAndAdjustClip;
    }

    /**
     * 获取调节片段
     * Gets filter and adjust clip.
     *
     * @param inPoint the in point
     * @return the filter and adjust clip
     */
    public MeicamTimelineVideoFilterAndAdjustClip getFilterAndAdjustClip(long inPoint) {
        for (MeicamTimelineVideoFilterAndAdjustClip meicamTimelineVideoFilterAndAdjustClip : filterAndAdjustClips) {
            if (meicamTimelineVideoFilterAndAdjustClip.getInPoint() == inPoint) {
                return meicamTimelineVideoFilterAndAdjustClip;
            }
        }
        return null;
    }

    /**
     * 添加底层timeline特效
     * Add nvs timeline video fx nvs timeline video fx.
     *
     * @param type        the type
     * @param inPoint     the in point
     * @param duration    the duration
     * @param videoFxName the video fx name
     * @return the nvs timeline video fx
     */
    private NvsTimelineVideoFx addNvsTimelineVideoFx(String type, long inPoint, long duration, String videoFxName) {
        NvsTimelineVideoFx nvsTimelineVideoFx;
        if (CommonData.TYPE_BUILD_IN.equals(type)) {
            nvsTimelineVideoFx = getObject().addBuiltinTimelineVideoFx(inPoint, duration, videoFxName);
        } else {
            nvsTimelineVideoFx = getObject().addPackagedTimelineVideoFx(inPoint, duration, videoFxName);
        }
        if (nvsTimelineVideoFx != null) {
            nvsTimelineVideoFx.setZValue(getIndex());
        }
        return nvsTimelineVideoFx;
    }

    /**
     * Remove clip
     * <P></l>
     * 删除Clip
     *
     * @param clip 特效 timeline VideoFx
     * @return 是否移除成功 Is success or not.true:yes; false:no.
     */
    public boolean removeClip(MeicamTimelineVideoFxClip clip) {
        if (clip == null) {
            LogUtils.e("clip is null!");
            return false;
        }
        int index = clip.getIndex();
        if (CommonUtils.isIndexAvailable(index, clipInfos)) {
            MeicamTimelineVideoFxClip remove = clipInfos.remove(index);
            if (remove != null) {
                getObject().removeTimelineVideoFx(remove.getObject());
                if (remove.getInPoint() != clip.getInPoint() || remove.getOutPoint() != clip.getOutPoint()) {
                    LogUtils.e("You may not be deleting the desired clip! Check it!");
                }
            }
            for (int i = index; i < clipInfos.size(); i++) {
                clip = clipInfos.get(i);
                clip.setIndex(i);
            }
            return true;
        } else {
            LogUtils.e("removeClip failed!,index = " + index + " is invalid" + ",inPoint=" + clip.getInPoint() + ",outPoint=" + clip.getOutPoint());
        }
        return false;
    }

    /**
     * Remove clip
     * <P></l>
     * 删除Clip
     *
     * @param index 特效index timeline VideoFx index
     * @return 是否移除成功 Is success or not.true:yes; false:no.
     */
    public boolean removeClip(int index) {
        if (!CommonUtils.isIndexAvailable(index, clipInfos)) {
            LogUtils.e("index is invalid");
            return false;
        }
        MeicamTimelineVideoFxClip clip = clipInfos.get(index);
        NvsTimelineVideoFx object = clip.getObject();
        if (object != null) {
            getObject().removeTimelineVideoFx(object);
        }
        clipInfos.remove(index);
        for (int i = index; i < clipInfos.size(); i++) {
            clip = clipInfos.get(i);
            clip.setIndex(i);
        }
        return true;
    }

    /**
     * Remove clip
     * <P></l>
     * 删除Clip
     *
     * @param inPoint 特效的inPoint timeline VideoFx in point
     * @return MeicamTimelineVideoFxClip 删除的videoFx
     */
    public MeicamTimelineVideoFxClip removeClip(long inPoint) {
        if (CommonUtils.isEmpty(clipInfos)) {
            return null;
        }
        for (MeicamTimelineVideoFxClip clipInfo : clipInfos) {
            if ((clipInfo.getInPoint() == inPoint)) {
                NvsTimelineVideoFx object = clipInfo.getObject();
                if (object != null) {
                    getObject().removeTimelineVideoFx(object);
                }
                clipInfos.remove(clipInfo);
                for (int i = clipInfo.getIndex(); i < clipInfos.size(); i++) {
                    MeicamTimelineVideoFxClip videoFxClip = clipInfos.get(i);
                    videoFxClip.setIndex(i);
                }
                return clipInfo;
            }
        }
        return null;
    }


    /**
     * 移除滤镜clip
     * Remove filter and adjust clip boolean.
     *
     * @param clip the clip
     * @return the boolean
     */
    public boolean removeFilterAndAdjustClip(MeicamTimelineVideoFilterAndAdjustClip clip) {
        boolean isSuccess = false;
        int clipIndex = clip.getIndex();
        if (CommonUtils.isIndexAvailable(clipIndex, filterAndAdjustClips)) {
            MeicamTimelineVideoFilterAndAdjustClip remove = filterAndAdjustClips.remove(clipIndex);
            if (remove != null) {
                isSuccess = remove.removeFilterAndAdjustClipFx();
            }
        } else {
            LogUtils.e("remove failed,error index=" + clipIndex + ",inPoint=" + clip.getInPoint() + ",trackIndex=" + clip.getTrackIndex() + ",name=" + clip.getText());
            for (int i = 0; i < filterAndAdjustClips.size(); i++) {
                MeicamTimelineVideoFilterAndAdjustClip filterAndAdjustClip = filterAndAdjustClips.get(i);
                LogUtils.e("Local clip index=" + filterAndAdjustClip.getIndex() + ",inPoint=" + filterAndAdjustClip.getInPoint()
                        + ",trackIndex=" + clip.getTrackIndex() + ",name=" + filterAndAdjustClip.getText());
            }
        }
        for (int i = clipIndex; i < filterAndAdjustClips.size(); i++) {
            clip = filterAndAdjustClips.get(i);
            clip.setIndex(i);
        }
        return isSuccess;
    }

    /**
     * 获取特效片段
     * Get meicam video clip
     *
     * @param timestamp 时间戳
     * @return MeicamTimelineVideoFxClip 特效片段
     */
    public MeicamTimelineVideoFxClip getClipByTimelinePosition(long timestamp) {
        for (MeicamTimelineVideoFxClip item : clipInfos) {
            if (timestamp >= item.getInPoint() && timestamp <= item.getOutPoint()) {
                return item;
            }
        }
        return null;
    }

    @NonNull
    @Override
    public MeicamTimelineVideoFxTrack clone() {
        MeicamTimelineVideoFxTrack clone = (MeicamTimelineVideoFxTrack) DeepCopyUtil.deepClone(this);
        if (clone == null) {
            String jsonData = GsonContext.getInstance().toJson(this);
            if (!TextUtils.isEmpty(jsonData)) {
                return GsonContext.getInstance().fromJson(jsonData, MeicamTimelineVideoFxTrack.class);
            }
        }
        if (clone == null) {
            clone = new MeicamTimelineVideoFxTrack(null, getIndex(), getType());
        }
        return clone;
    }

    public long getClipDuration() {
        int lastIndex = clipInfos.size() - 1;
        if (CommonUtils.isIndexAvailable(lastIndex, clipInfos)) {
            return clipInfos.get(lastIndex).getOutPoint();
        }
        return 0;
    }

    @Override
    public int getClipCount() {
        return clipInfos == null ? 0 : clipInfos.size();
    }

    public int getFilterAndAdjustCount() {
        return filterAndAdjustClips == null ? 0 : filterAndAdjustClips.size();
    }

    /**
     * Get clip
     * <P></>
     * 获取clip
     *
     * @param index the clip index
     * @return MeicamTimelineVideoFxClip
     */
    public MeicamTimelineVideoFxClip getClip(int index) {
        if (CommonUtils.isIndexAvailable(index, clipInfos)) {
            return clipInfos.get(index);
        }
        return null;
    }

    /**
     * Get clip
     * <P></>
     * 获取 调节 滤镜 clip
     *
     * @param index the clip index
     * @return MeicamTimelineVideoFxClip
     */
    public MeicamTimelineVideoFilterAndAdjustClip getFilterAndAdjustClip(int index) {
        if (CommonUtils.isIndexAvailable(index, filterAndAdjustClips)) {
            return filterAndAdjustClips.get(index);
        }
        return null;
    }

    @Override
    public LMeicamTimelineVideoFxTrack parseToLocalData() {
        /*
         * 储存timeline特效
         *Save the timeline effec
         */
        LMeicamTimelineVideoFxTrack local = new LMeicamTimelineVideoFxTrack(getIndex());
        local.setType(getType());
        for (MeicamTimelineVideoFxClip clipInfo : clipInfos) {
            local.getClipInfoList().add(clipInfo.parseToLocalData());
        }
        /*
         * 储存 滤镜 调节
         * Save the filter and adjust
         */
        for (MeicamTimelineVideoFilterAndAdjustClip adjustClip : filterAndAdjustClips) {
            local.getFilterAndAdjustClips().add(adjustClip.parseToLocalData());
        }

        setCommondData(local);
        return local;
    }

    @Override
    public void recoverFromLocalData(LMeicamTimelineVideoFxTrack lTrackInfo) {
        /*
         * 恢复特效
         * Restore effect
         */
        List<LMeicamTimelineVideoFxClip> clipInfoList = lTrackInfo.getClipInfoList();
        if (!CommonUtils.isEmpty(clipInfoList)) {
            for (LMeicamTimelineVideoFxClip lMeicamTimelineVideoFxClip : clipInfoList) {
                MeicamTimelineVideoFxClip videoFxClip = addFxClip(lMeicamTimelineVideoFxClip.getClipType(),
                        lMeicamTimelineVideoFxClip.getInPoint(),
                        lMeicamTimelineVideoFxClip.getOutPoint() - lMeicamTimelineVideoFxClip.getInPoint(),
                        lMeicamTimelineVideoFxClip.getDesc());
                if (videoFxClip != null) {
                    videoFxClip.recoverFromLocalData(lMeicamTimelineVideoFxClip);
                }
            }
        }
        /*
         * 恢复 滤镜 调节
         * Restore filter and adjust
         */
        List<LMeicamTimelineVideoFilterAndAdjustClip> filterAndAdjustClipInfos = lTrackInfo.getFilterAndAdjustClips();
        if (!CommonUtils.isEmpty(filterAndAdjustClipInfos)) {

            for (LMeicamTimelineVideoFilterAndAdjustClip lAdjustClip : filterAndAdjustClipInfos) {

                Map<String, LMeicamTimelineVideoFxClip> map = lAdjustClip.getFilterAndAdjustClipInfos();

                MeicamTimelineVideoFilterAndAdjustClip adjustClip = addFilterAndAdjustClip(lAdjustClip.getType(),
                        lAdjustClip.getInPoint(),
                        lAdjustClip.getOutPoint() - lAdjustClip.getInPoint());
                if (adjustClip == null) {
                    continue;
                }
                adjustClip.recoverFromLocalData(lAdjustClip);
                for (Map.Entry<String, LMeicamTimelineVideoFxClip> entry : map.entrySet()) {
                    LMeicamTimelineVideoFxClip lMeicamTimelineVideoFxClip = entry.getValue();
                    if (lMeicamTimelineVideoFxClip == null) {
                        continue;
                    }
                    MeicamTimelineVideoFxClip meicamTimelineVideoFxClip = adjustClip.addAdjustTimelineFx(lMeicamTimelineVideoFxClip.getClipType(), lAdjustClip.getType(),
                            lMeicamTimelineVideoFxClip.getDesc());
                    if (meicamTimelineVideoFxClip != null) {
                        meicamTimelineVideoFxClip.recoverFromLocalData(lMeicamTimelineVideoFxClip);
                    }
                }
            }
        }

    }

    @Override
    public void recoverFromTimelineData(NvsObject nvsObject) {
        if (!(nvsObject instanceof NvsTimelineVideoFx)) {
            return;
        }
        NvsTimelineVideoFx nvsTimelineVideoFx = (NvsTimelineVideoFx) nvsObject;
        String packageId = nvsTimelineVideoFx.getTimelineVideoFxPackageId();
        if (!TextUtils.isEmpty(packageId)) {
            FileInfoBridge.FileInfo fileInfo = FileInfoBridge.getFileInfo(packageId);
            if (fileInfo != null) {
                if (fileInfo.category == 1) {
                    //滤镜
                    //Filter
                    addFilterOrAdjustFx(nvsTimelineVideoFx, MeicamTimelineVideoFxClip.ClipFxType.SUB_TYPE_TIMELINE_FILTER);
                } else if (fileInfo.category == 2) {
                    //特效
                    //Effect
                    MeicamTimelineVideoFxClip videoFxClip = addMeicamFxClip(nvsTimelineVideoFx);
                    if (videoFxClip != null) {
                        videoFxClip.recoverFromTimelineData(nvsTimelineVideoFx);
                    }
                }
            } else {
                //默认是特效
                //Default is effect
                MeicamTimelineVideoFxClip videoFxClip = addMeicamFxClip(nvsTimelineVideoFx);
                if (videoFxClip != null) {
                    videoFxClip.recoverFromTimelineData(nvsTimelineVideoFx);
                }
            }
        } else {
            String fxName = nvsTimelineVideoFx.getBuiltinTimelineVideoFxName();
            //滤镜和调节参数
            //Filter and adjust parameter
            if (fxName.equals(NvsConstants.MOSAICNAME)
                    || fxName.equals(NvsConstants.BLURNAME)
                    || NvsConstants.FX_SHARPEN.equals(fxName)
                    || NvsConstants.FX_VIGNETTE.equals(fxName)
                    || NvsConstants.ADJUST_TYPE_BASIC_IMAGE_ADJUST.equals(fxName)
                    || NvsConstants.ADJUST_TINT.equals(fxName)) {
                addFilterOrAdjustFx(nvsTimelineVideoFx, MeicamTimelineVideoFxClip.ClipFxType.SUB_TYPE_TIMELINE_ADJUST);
            } else {
                //默认是特效
                //Default is effect
                MeicamTimelineVideoFxClip videoFxClip = addMeicamFxClip(nvsTimelineVideoFx);
                if (videoFxClip != null) {
                    videoFxClip.recoverFromTimelineData(nvsTimelineVideoFx);
                }
            }
        }
    }

    private void addFilterOrAdjustFx(NvsTimelineVideoFx nvsTimelineVideoFx, String subType) {
        long inPoint = nvsTimelineVideoFx.getInPoint();
        MeicamTimelineVideoFilterAndAdjustClip filterAndAdjustClip = getFilterAndAdjustClip(inPoint);
        if (filterAndAdjustClip == null) {
            filterAndAdjustClip = addFilterAndAdjustClip(subType, inPoint,
                    nvsTimelineVideoFx.getOutPoint() - inPoint);

        }
        if (filterAndAdjustClip != null) {
            String packageId = nvsTimelineVideoFx.getTimelineVideoFxPackageId();
            boolean isAdjust = SUB_TYPE_TIMELINE_ADJUST.equals(subType);

            if (isAdjust) {
                int indexString = 1;
                List<MeicamTimelineVideoFilterAndAdjustClip> adjustClipList = new ArrayList<>();
                for (int index = 0; index < filterAndAdjustClips.size(); index++) {
                    MeicamTimelineVideoFilterAndAdjustClip clip = filterAndAdjustClips.get(index);
                    if (SUB_TYPE_TIMELINE_ADJUST.equals(clip.getType())) {
                        adjustClipList.add(clip);
                    }
                }
                for (int index = 0; index < adjustClipList.size(); index++) {
                    MeicamTimelineVideoFilterAndAdjustClip adjustClip = adjustClipList.get(index);
                    if (adjustClip.getInPoint() == filterAndAdjustClip.getInPoint()) {
                        indexString = index + 1;
                    }
                }
                if (Utils.isZh()) {
                    filterAndAdjustClip.setDisplayText("调节" + indexString);
                } else {
                    filterAndAdjustClip.setDisplayText("Adjust" + indexString);
                }
            }
            FileInfoBridge.FileInfo fileInfo = FileInfoBridge.getFileInfo(packageId);
            if (fileInfo != null) {
                if (Utils.isZh()) {
                    if (!TextUtils.isEmpty(fileInfo.fileNameZh)) {
                        filterAndAdjustClip.setDisplayText(fileInfo.fileNameZh);
                    } else {
                        filterAndAdjustClip.setDisplayText(fileInfo.fileName);
                    }
                } else {
                    if (!TextUtils.isEmpty(fileInfo.fileName)) {
                        filterAndAdjustClip.setDisplayText(fileInfo.fileName);
                    } else {
                        filterAndAdjustClip.setDisplayText(fileInfo.fileNameZh);
                    }
                }
            }
            String desc = isAdjust ? nvsTimelineVideoFx.getBuiltinTimelineVideoFxName() : packageId;
            String type = isAdjust ? CommonData.TYPE_BUILD_IN : CommonData.TYPE_PACKAGE;
            filterAndAdjustClip.addAdjustTimelineFx(nvsTimelineVideoFx, type, subType, desc);
        }
    }

    /**
     * clear clips 清除clip
     */
    public void clearClip() {
        for (int index = clipInfos.size() - 1; index >= 0; index--) {
            removeClip(clipInfos.get(index));
        }
        for (int index = filterAndAdjustClips.size() - 1; index >= 0; index--) {
            removeFilterAndAdjustClip(filterAndAdjustClips.get(index));
        }
    }
}
