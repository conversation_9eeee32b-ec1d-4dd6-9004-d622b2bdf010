package com.meishe.engine.bean.bridges;

import com.google.gson.reflect.TypeToken;
import com.meishe.base.utils.GsonUtils;
import com.meishe.base.utils.ResourceUtils;
import com.meishe.engine.bean.Plug;
import com.meishe.engine.bean.PlugDetail;
import com.meishe.engine.bean.PlugKind;

import java.util.List;

/**
 * All rights Reserved, Designed By www.meishesdk.com
 *
 * @Author: Chu<PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2022/8/10 14:18
 * @Description: 获取插件特效参数 The atomic fx bridge
 * @Copyright: www.meishesdk.com Inc. All rights reserved.
 */
public class AtomicFxBridge {
    private static List<PlugKind> plugKinds;

    /**
     * 根据特效id获取参数列表
     * Gets param list by effect id.
     *
     * @param id the id
     * @return the param list by effect id
     */
    public static PlugDetail getParamListByEffectID(String id) {
        Plug plug = getPlugByEffectID(id);
        if (plug != null) {
            return getPlugDetail(plug.effectPath);
        }
        return null;
    }

    /**
     * 根据特效ID获取插件信息
     * Gets plug by effect id.
     *
     * @param desc the desc
     * @return the plug by effect id
     */
    public static Plug getPlugByEffectID(String desc) {
        plugKinds = getPlugKindList();
        for (int i = 0; i < plugKinds.size(); i++) {
            PlugKind kind = plugKinds.get(i);
            List<String> plugKeys = kind.effectKeyList;
            for (int j = 0; j < plugKeys.size(); j++) {
                if (plugKeys.get(j).equals(desc)) {
                    List<Plug> plugs = getPlugList(kind.effectListPath);
                    return plugs.get(j);
                }
            }

        }
        return null;
    }

    public static PlugDetail getPlugDetail(String path) {
        return GsonUtils.fromJson(ResourceUtils.
                readAssets2String(path, "UTF-8"), PlugDetail.class);
    }

    public static List<Plug> getPlugList(String path) {
        return GsonUtils.fromJson(ResourceUtils.
                        readAssets2String(path, "UTF-8"),
                new TypeToken<List<Plug>>() {
                }.getType());
    }

    public static List<PlugKind> getPlugKindList() {
        if (plugKinds == null || plugKinds.size() == 0) {
            String path = "plug_effect/info.json";
            plugKinds = GsonUtils.fromJson(ResourceUtils.
                            readAssets2String(path, "UTF-8"),
                    new TypeToken<List<PlugKind>>() {
                    }.getType());
        }
        return plugKinds;
    }

}
