package com.meishe.engine.bean;

import androidx.annotation.NonNull;

import com.meicam.sdk.NvsObject;
import com.meishe.engine.adapter.TimelineDataParserAdapter;
import com.meishe.engine.local.LMeicamPosition2D;
import com.meishe.engine.util.DeepCopyUtil;

import java.io.Serializable;

/**
 * All rights Reserved, Designed By www.meishesdk.com
 *
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2021/5/13 16:59
 * @Description: The Position2D
 * @Copyright: www.meishesdk.com Inc. All rights reserved.
 */
public class MeicamPosition2D implements Serializable, Cloneable, TimelineDataParserAdapter<LMeicamPosition2D> {
    public float x;
    public float y;

     public MeicamPosition2D(float var1, float var2) {
        this.x = var1;
        this.y = var2;
    }

    @NonNull
    @Override
    public MeicamPosition2D clone() {
        return (MeicamPosition2D) DeepCopyUtil.deepClone(this);
    }

    @Override
    public LMeicamPosition2D parseToLocalData() {
        return new LMeicamPosition2D(x, y);
    }

    @Override
    public void recoverFromLocalData(LMeicamPosition2D lPosition2D) {
        x = lPosition2D.x;
        y = lPosition2D.y;
    }

    @Override
    public void recoverFromTimelineData(NvsObject nvsObject) {

    }
}
