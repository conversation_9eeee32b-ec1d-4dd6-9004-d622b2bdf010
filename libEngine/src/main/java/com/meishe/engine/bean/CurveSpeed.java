package com.meishe.engine.bean;

import java.io.Serializable;

/**
 * All rights Reserved, Designed By www.meishesdk.com
 *
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2021/4/26 16:02
 * @Description:
 * @Copyright: www.meishesdk.com Inc. All rights reserved.
 */
public class CurveSpeed implements Serializable {
    private String speed;
    private String speedOriginal;

    public CurveSpeed(String speed, String speedOriginal) {
        this.speed = speed;
        this.speedOriginal = speedOriginal;
    }

    public String getSpeed() {
        return speed;
    }

    public void setSpeed(String speed) {
        this.speed = speed;
    }

    public String getSpeedOriginal() {
        return speedOriginal;
    }

    public void setSpeedOriginal(String speedOriginal) {
        this.speedOriginal = speedOriginal;
    }
}
