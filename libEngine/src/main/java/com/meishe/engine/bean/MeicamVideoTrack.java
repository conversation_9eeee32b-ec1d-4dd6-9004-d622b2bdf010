package com.meishe.engine.bean;

import android.annotation.SuppressLint;
import android.text.TextUtils;
import android.util.SparseArray;
import android.util.SparseIntArray;

import androidx.annotation.NonNull;

import com.meicam.sdk.NvsObject;
import com.meicam.sdk.NvsTrackCaption;
import com.meicam.sdk.NvsTrackCompoundCaption;
import com.meicam.sdk.NvsTrackVideoFx;
import com.meicam.sdk.NvsVideoClip;
import com.meicam.sdk.NvsVideoFx;
import com.meicam.sdk.NvsVideoTrack;
import com.meicam.sdk.NvsVideoTransition;
import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.LogUtils;
import com.meishe.engine.adapter.TimelineDataParserAdapter;
import com.meishe.engine.constant.NvsConstants;
import com.meishe.engine.local.LMeicamTrackVideoFx;
import com.meishe.engine.local.LMeicamTransition;
import com.meishe.engine.local.LMeicamVideoClip;
import com.meishe.engine.local.LMeicamVideoTrack;
import com.meishe.engine.util.WhiteList;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.meishe.base.constants.Constants.TIME_BASE;
import static com.meishe.engine.bean.MeicamFxParam.TYPE_BOOLEAN;
import static com.meishe.engine.bean.MeicamFxParam.TYPE_FLOAT;
import static com.meishe.engine.bean.MeicamFxParam.TYPE_INT;
import static com.meishe.engine.bean.MeicamFxParam.TYPE_STRING;
import static com.meishe.engine.constant.NvsConstants.TYPE_BUILD_IN;
import static com.meishe.engine.constant.NvsConstants.TYPE_PACKAGE;

/**
 * All rights reserved,Designed by www.meishesdk.com
 * 版权所有:www.meishesdk.com
 *
 * <AUTHOR> LiHangZhou
 * @CreateDate :2021/05/20 10:07
 * @Description : 视频轨道 The video track
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class MeicamVideoTrack extends TrackInfo<NvsVideoTrack> implements Cloneable, Serializable, TimelineDataParserAdapter<LMeicamVideoTrack> {
    /**
     * 转场列表
     * The transition list
     */
    private List<MeicamTransition> mTransitionInfoList = new ArrayList<>();
    /**
     * 视频片段列表
     * The meican video clip list
     */
    private List<MeicamVideoClip> mVideoClipList = new ArrayList<>();
    /**
     * 是否是静音
     * Mute or not
     */
    private boolean mIsMute = false;

    protected List<MeicamCaptionClip> captionList = new ArrayList<>();

    protected List<MeicamCompoundCaptionClip> compoundCaptionList = new ArrayList<>();

    private List<MeicamTrackVideoFx> trackVideoFxList;

    MeicamVideoTrack(NvsVideoTrack videoTrack, int index) {
        super(videoTrack, CommonData.TRACK_VIDEO, index);
    }

    List<MeicamVideoClip> getVideoClipList() {
        return mVideoClipList;
    }

    void setVideoClipList(List<MeicamVideoClip> mVideoClipList) {
        this.mVideoClipList = mVideoClipList;
    }

    List<MeicamTransition> getTransitionInfoList() {
        return mTransitionInfoList;
    }

    void setTransitionInfoList(List<MeicamTransition> transitionInfoList) {
        this.mTransitionInfoList = transitionInfoList;
    }

    @Override
    void setIndex(int index) {
        super.setIndex(index);
        for (MeicamVideoClip videoClip : mVideoClipList) {
            videoClip.setTrackIndex(index);
        }
    }

    /**
     * 添加视频片段
     * Add video clip
     *
     * @param videoPath the video path 视频或者图片路径
     * @param index     the add index  添加到的索引位置
     * @param trimIn    the trim in point  裁入点
     * @param trimOut   the trim out point  裁出点
     * @return MeicamVideoClip the video clip视频片段
     **/
    public MeicamVideoClip addVideoClip(String videoPath, int index, long trimIn, long trimOut) {
        NvsVideoTrack nvsVideoTrack = getObject();
        if (nvsVideoTrack != null) {
            NvsVideoClip nvsVideoClip = nvsVideoTrack.insertClip(videoPath, trimIn, trimOut, index);
            if (nvsVideoClip != null) {
                return doCommonOperation(nvsVideoClip, createVideoClip(nvsVideoClip), true, true);
            }
        }
        return null;
    }

    /**
     * 添加视频片段
     * Add video clip
     *
     * @param videoPath the video path 片段路径
     * @param inPoint   the inPoint in timeline
     * @param trimIn    the trim in point  裁入点
     * @param trimOut   the trim out point  裁出点
     * @return MeicamVideoClip the video clip视频片段
     **/
    public MeicamVideoClip addVideoClip(String videoPath, long inPoint, long trimIn, long trimOut) {
        NvsVideoTrack nvsVideoTrack = getObject();
        if (nvsVideoTrack != null) {
            NvsVideoClip nvsVideoClip = nvsVideoTrack.addClip(videoPath, inPoint, trimIn, trimOut);
            if (nvsVideoClip != null) {
                return doCommonOperation(nvsVideoClip, createVideoClip(nvsVideoClip), true, false);
            }
        }
        return null;
    }

    /**
     * 插入视频片段
     * Insert video clip
     *
     * @param videoClip the old videoClip  旧的片段
     * @param index     the add index  添加到的索引位置
     * @return MeicamVideoClip the video clip视频片段
     **/
    public MeicamVideoClip insertVideoClip(MeicamVideoClip videoClip, int index) {
        if (videoClip != null) {
            return insertVideoClip(videoClip, index, videoClip.getTrimIn(), videoClip.getTrimOut());
        }
        return null;
    }

    /**
     * 插入视频片段
     * Insert video clip
     *
     * @param videoClip the old videoClip  旧的片段
     * @param index     the add index  添加到的索引位置
     * @param trimIn    the trim in point  裁入点
     * @param trimOut   the trim out point  裁出点
     * @return MeicamVideoClip the video clip视频片段
     **/
    public MeicamVideoClip insertVideoClip(MeicamVideoClip videoClip, int index, long trimIn, long trimOut) {
        NvsVideoTrack nvsVideoTrack = getObject();
        if (nvsVideoTrack != null && videoClip != null) {
            NvsVideoClip nvsVideoClip = nvsVideoTrack.insertClip(videoClip.getVideoReverse() ? videoClip.getReverseFilePath() : videoClip.getFilePath(), trimIn, trimOut, index);
            if (nvsVideoClip != null) {
                videoClip.inPoint = nvsVideoClip.getInPoint();
                videoClip.outPoint = nvsVideoClip.getOutPoint();
                videoClip.trimIn = nvsVideoClip.getTrimIn();
                videoClip.trimOut = nvsVideoClip.getTrimOut();
                /*带有VideoClip旧数据而且有inPoint的时候调用doCommonOperation方法的insert参数都要是true，因为要调整转场
                * When there is old VideoClip data and there is an inPoint, the insert parameter of the
                * doCommonOperation method is always true because the transition needs to be adjusted.
                * */
                doCommonOperation(nvsVideoClip, videoClip, false, true);
                videoClip.bindToTimeline();
                if (videoClip.getSpeed() > 0 && videoClip.getSpeed() != 1) {
                    updateAllInAndOutPoint();
                }
                return videoClip;
            } else {
                LogUtils.e("add video clip failed!!!");
            }
        }
        return null;
    }

    /**
     * 添加视频片段
     * Add video clip
     *
     * @param videoClip the old videoClip  旧的片段
     * @param inPoint   the inPoint in timeline
     * @param trimIn    the trim in point  裁入点
     * @param trimOut   the trim out point  裁出点
     * @return MeicamVideoClip the video clip视频片段
     **/
    public MeicamVideoClip addVideoClip(MeicamVideoClip videoClip, long inPoint, long trimIn, long trimOut) {
        NvsVideoTrack nvsVideoTrack = getObject();
        if (nvsVideoTrack != null && videoClip != null) {
            NvsVideoClip nvsVideoClip = nvsVideoTrack.addClip(videoClip.getVideoReverse() ? videoClip.getReverseFilePath() : videoClip.getFilePath(), inPoint, trimIn, trimOut);
            if (nvsVideoClip != null) {
                videoClip.inPoint = nvsVideoClip.getInPoint();
                videoClip.outPoint = nvsVideoClip.getOutPoint();
                videoClip.trimIn = nvsVideoClip.getTrimIn();
                videoClip.trimOut = nvsVideoClip.getTrimOut();
                /*带有VideoClip旧数据而且有inPoint的时候调用doCommonOperation方法的insert参数都要是true，因为要调整转场
                 * When there is old VideoClip data and there is an inPoint, the insert parameter of the
                 * doCommonOperation method is always true because the transition needs to be adjusted.
                 * */
                doCommonOperation(nvsVideoClip, videoClip, false, true);
                videoClip.bindToTimeline();
                if (videoClip.getSpeed() > 0 && videoClip.getSpeed() != 1) {
                    updateAllInAndOutPoint();
                }
                return videoClip;
            } else {
                LogUtils.e("add video clip failed!!!");
            }
        }
        return null;
    }

    /**
     * 添加视频片段
     * Append video clip
     *
     * @param videoPath the video path 视频或者图片路径
     * @param videoType the video type 片段类型
     * @param trimIn    the trim in point 裁入点
     * @param trimOut   the trim out point 裁出点
     * @return MeicamVideoClip the video clip视频片段
     **/
    public MeicamVideoClip appendVideoClip(String videoPath, String videoType, long trimIn, long trimOut) {
        MeicamVideoClip videoClip = appendVideoClip(videoPath, trimIn, trimOut);
        if (videoClip != null) {
            videoClip.setVideoType(videoType);
            return videoClip;
        }
        return null;
    }

    /**
     * 添加视频片段
     * Append video clip
     *
     * @param videoPath the video path 视频或者图片路径
     * @param trimIn    the trim in point 裁入点
     * @param trimOut   the trim out point 裁出点
     * @return MeicamVideoClip the video clip视频片段
     **/
    public MeicamVideoClip appendVideoClip(String videoPath, long trimIn, long trimOut) {
        NvsVideoTrack nvsVideoTrack = getObject();
        if (nvsVideoTrack != null) {
            NvsVideoClip nvsVideoClip = nvsVideoTrack.appendClip(videoPath, trimIn, trimOut);
            if (nvsVideoClip != null) {
                return doCommonOperation(nvsVideoClip, createVideoClip(nvsVideoClip), true, false);
            }
        }
        return null;
    }

    /**
     * 添加视频片段
     * Append video clip
     *
     * @param videoPath the video path 视频或者图片路径
     * @param videoType the video type 片段类型
     * @return MeicamVideoClip the video clip视频片段
     **/
    public MeicamVideoClip appendVideoClip(String videoPath, String videoType) {
        MeicamVideoClip videoClip = appendVideoClip(videoPath);
        if (videoClip != null) {
            videoClip.setVideoType(videoType);
            return videoClip;
        }
        return null;
    }

    /**
     * 添加视频片段
     * Append video clip
     *
     * @param videoPath the video path 视频或者图片路径
     * @return MeicamVideoClip the video clip视频片段
     **/
    public MeicamVideoClip appendVideoClip(String videoPath) {
        NvsVideoTrack nvsVideoTrack = getObject();
        if (nvsVideoTrack != null) {
            NvsVideoClip nvsVideoClip = nvsVideoTrack.appendClip(videoPath);
            if (nvsVideoClip != null) {
                return doCommonOperation(nvsVideoClip, createVideoClip(nvsVideoClip), true, false);
            }
        }
        return null;
    }

    /**
     * 执行共有操作
     * Do common operation
     *
     * @param nvsVideoClip the nvs video clip sdk视频片段
     * @param videoClip    the meicam  video clip 上层视频片段
     * @param insert       true insert 插入,false not 最后追加
     */
    private MeicamVideoClip doCommonOperation(NvsVideoClip nvsVideoClip, MeicamVideoClip videoClip, boolean setBackground, boolean insert) {
        videoClip.setObject(nvsVideoClip);
        if (addVideoClip(videoClip, nvsVideoClip.getIndex())) {
            if (setBackground && videoClip.getTrackIndex() == 0) {
                videoClip.setDefaultBackground();
            }
            if (insert) {
                /*插入的话，需要把前一个片段的转场还原
                *To insert, you need to restore the transition of the previous clip.
                * */
                if (nvsVideoClip.getIndex() > 0) {
                    MeicamTransition transition = getTransition(nvsVideoClip.getIndex() - 1);
                    if (transition != null) {
                        /*有设置过转场
                        * There has been a transition.
                        * */
                        NvsVideoTransition nvsVideoTransition = buildNvsTransition(transition.getIndex(), transition.getType(), transition.getDesc());
                        if (nvsVideoTransition != null) {
                            transition.setObject(nvsVideoTransition);
                        } else {
                            removeTransition(nvsVideoClip.getIndex() - 1);
                        }
                    } else {
                        /*没有则清除默认转场
                        * If not, clear the default transition
                        * */
                        getObject().setBuiltinTransition(nvsVideoClip.getIndex() - 1, "");
                    }
                }
                getObject().setBuiltinTransition(nvsVideoClip.getIndex(), "");
                /*后边的转场进行移动
                *Move the next transition
                * */
                moveTransition(nvsVideoClip.getIndex(), 1);
            } else {
                checkTransition();
            }
            //logTransitionList();
            return videoClip;
        } else {
            /*添加失败则删除
            * Delete if adding fails.
            * */
            NvsVideoTrack videoTrack = getObject();
            if (videoTrack != null) {
                videoTrack.removeClip(nvsVideoClip.getIndex(), true);
            }
        }
        return null;
    }

    /**
     * 添加视频片段
     * Add video clip
     *
     * @param videoClip the meicam video clip 视频片段
     */
    private boolean addVideoClip(MeicamVideoClip videoClip, int index) {
        if (index > mVideoClipList.size() || index < 0) {
            LogUtils.e("add video clip failed !!!");
            return false;
        }
        videoClip.setIndex(index);
        videoClip.setTrackIndex(getIndex());
        mVideoClipList.add(index, videoClip);
        boolean needDelete;
        for (int i = index + 1; i < mVideoClipList.size(); i++) {
            MeicamVideoClip afterVideoClip = mVideoClipList.get(i);
            if (afterVideoClip.getObject() != null) {
                afterVideoClip.setIndex(afterVideoClip.getObject().getIndex());
                afterVideoClip.updateInAndOutPoint();
                /*后边的片段如果在添加的片段出入点内，说明需要删除
                *If the following clip is within the in and out point of the added clip,
                * it means that it needs to be deleted.
                * */
                needDelete = videoClip.getInPoint() >= afterVideoClip.getInPoint() &&
                        videoClip.getOutPoint() >= afterVideoClip.getOutPoint();
            } else {
                needDelete = true;
            }
            if (needDelete) {
                /*
                 * 底层可能出现覆盖的情况，如果有就移除上层多余的片段
                 * The native layer may be overwritten. If it is, remove the redundant clip.
                 */
                mVideoClipList.remove(i);
                i--;
            }
        }
        return true;
    }


    /**
     * 更新所有的出点和入点
     * Update all inPoint and outPoint
     */
    private void updateAllInAndOutPoint() {
        if (!CommonUtils.isEmpty(mVideoClipList)) {
            for (MeicamVideoClip clip : mVideoClipList) {
                clip.updateInAndOutPoint();
            }
        }
    }

    /**
     * 创建视频片段
     * Create meicam video clip
     *
     * @param nvsVideoClip the nvs video clip sdk 视频频段
     * @return MeicamVideoClip the video clip
     */
    private MeicamVideoClip createVideoClip(NvsVideoClip nvsVideoClip) {
        String videoType;
        if (CommonData.EMPTY_THUMBNAIL_IMAGE.equals(nvsVideoClip.getFilePath()) || CommonData.IMAGE_BLACK_HOLDER.equals(nvsVideoClip.getFilePath())) {
            videoType = CommonData.CLIP_HOLDER;
        } else {
            videoType = nvsVideoClip.getVideoType() == NvsVideoClip.VIDEO_CLIP_TYPE_IMAGE ? CommonData.CLIP_IMAGE : CommonData.CLIP_VIDEO;
        }
        MeicamVideoClip videoClip = new MeicamVideoClip(nvsVideoClip, videoType);
        videoClip.outPoint = nvsVideoClip.getOutPoint();
        if (CommonData.CLIP_IMAGE.equals(videoClip.getVideoType())) {
            videoClip.setOrgDuration(Long.MAX_VALUE);
        }
        return videoClip;
    }

    /**
     * 获取视频片段的数量
     * Get meicam video clip count
     *
     * @return the count of video clip 片段数量
     */
    @Override
    public int getClipCount() {
        return mVideoClipList.size();
    }

    /**
     * 获取视频片段
     * Get meicam video clip
     *
     * @param index the index 索引;
     * @return MeicamVideoClip 视频片段
     */
    public MeicamVideoClip getVideoClip(int index) {
        if (CommonUtils.isIndexAvailable(index, mVideoClipList)) {
            return mVideoClipList.get(index);
        }
        return null;
    }

    /**
     * 获取视频片段
     * Get meicam video clip
     *
     * @param inPoint the in point 入点;
     * @return MeicamVideoClip 视频片段
     */
    public MeicamVideoClip getVideoClip(long inPoint) {
        for (MeicamVideoClip item : mVideoClipList) {
            if (inPoint == item.getInPoint()) {
                return item;
            }
        }
        return null;
    }

    /**
     * 获取视频片段
     * Get meicam video clip
     *
     * @param timestamp 时间戳
     * @return MeicamVideoClip 视频片段
     */
    public MeicamVideoClip getClipByTimelinePosition(long timestamp) {
        for (MeicamVideoClip item : mVideoClipList) {
            if (timestamp >= item.getInPoint() && timestamp < item.getOutPoint()) {
                return item;
            }
        }
        return null;
    }

    /**
     * 移动片段
     * Move clip from index to index
     *
     * @param from index 开始的位置
     * @param to   index 最后的位置
     * @return true success 移动成功，false failed 移动失败
     */
    public boolean moveClip(int from, int to) {
        NvsVideoTrack videoTrack = getObject();
        if (videoTrack != null) {
            if (videoTrack.moveClip(from, to)) {
                MeicamVideoClip remove = mVideoClipList.remove(from);
                if (remove != null) {
                    mVideoClipList.add(to, remove);
                    int start = Math.min(from, to);
                    int end = Math.max(from, to);
                    int size = mVideoClipList.size();
                    /*调整片段的出入点
                    * Adjust the in and out point of clip.
                    * */
                    MeicamVideoClip meicamVideoClip;
                    SparseIntArray tempTransitionArray = new SparseIntArray();
                    SparseArray<NvsVideoTransition> tempNvsTransitionArray = new SparseArray<>();
                    for (int i = 0; i < size; i++) {
                        meicamVideoClip = mVideoClipList.get(i);
                        MeicamTransition transition = getTransition(meicamVideoClip.getIndex());
                        if (i >= start && i <= end) {
                            NvsVideoClip clip = meicamVideoClip.getObject();
                            if (clip != null) {
                                meicamVideoClip.updateInAndOutPoint();
                                meicamVideoClip.setIndex(clip.getIndex());
                            }
                        }
                        /*
                         *注意：调用moveClip方法后，底层会删除已经添加的转场，并且添加默认转场。
                         *视频片段是有序的，可以这么遍历,但是转场并不是有序的，需要先记录更改后的索引以及底层对象
                         *然后再遍历刷新索引以及底层对象
                         *Note: After calling the moveClip method, the native layer will delete the added transition and add the default transition.
                         * *Video clips are orderly and can be traversed in this way, but the transition is not orderly. You need to record the changed index and native objects first

                         *Then traverse the refresh index and the native object.
                         * */
                        if (transition != null) {
                            NvsVideoTransition nvsVideoTransition = null;
                            if (TYPE_BUILD_IN.equals(transition.getType())) {
                                nvsVideoTransition = videoTrack.setBuiltinTransition(meicamVideoClip.getIndex(), transition.getDesc());
                            } else if (TYPE_PACKAGE.equals(transition.getType())) {
                                videoTrack.setBuiltinTransition(meicamVideoClip.getIndex(), "");
                                nvsVideoTransition = videoTrack.setPackagedTransition(meicamVideoClip.getIndex(), transition.getDesc());
                            }
                            if (nvsVideoTransition != null) {
                                tempNvsTransitionArray.put(transition.getIndex(), nvsVideoTransition);
                            }
                            tempTransitionArray.put(transition.getIndex(), meicamVideoClip.getIndex());
                        } else {
                            videoTrack.setBuiltinTransition(meicamVideoClip.getIndex(), "");
                        }
                    }
                    /*刷新底层对象的索引到上层
                    * Refresh the index by index of native object.
                    * */
                    int newTransitionIndex;
                    for (MeicamTransition transition : mTransitionInfoList) {
                        NvsVideoTransition nvsVideoTransition = tempNvsTransitionArray.get(transition.getIndex(), null);
                        if (nvsVideoTransition != null) {
                            transition.setObject(nvsVideoTransition);
                        }
                        newTransitionIndex = tempTransitionArray.get(transition.getIndex(), -1);
                        if (newTransitionIndex != -1) {
                            transition.setIndex(newTransitionIndex);
                        }
                        transition.setDuration(transition.getDuration());
                    }
                    /*删除最后一个可用片段的转场，因为尾部不需要有转场
                    *Delete the transition of the last available clip, because there is no transition at the tail
                    **/
                    int lastIndex = videoTrack.getClipCount() - 1;
                    if (lastIndex >= 0) {
                        MeicamVideoClip lastVideoClip = getVideoClip(lastIndex);
                        if (lastVideoClip != null && CommonData.CLIP_HOLDER.equals(lastVideoClip.getType())) {
                            lastIndex = lastIndex - 1;
                        }
                    }
                    removeTransition(lastIndex);
                    //logTransitionList();
                    //logVideoClipList();
                }
                return true;
            }
        }
        return false;
    }

    /**
     * 分割视频片段
     * Split meicam video clip
     *
     * @param clipIndex  the clip index 片段索引
     * @param splitPoint the split point 片段分割点
     * @return MeicamVideoClip the last video clip 分割后，后边的的片段
     */
    @SuppressLint("UseSparseArrays")
    public MeicamVideoClip splitVideoClip(int clipIndex, long splitPoint) {
        NvsVideoTrack videoTrack = getObject();
        if (videoTrack != null) {
            MeicamVideoClip videoClip = getVideoClip(clipIndex);
            if (videoClip == null) {
                return null;
            }
            /*获取分割点处的所有特效的参数，如果特效没有关键帧，则没有值
             *Get the parameters of all effects at the split point.
             * If there is no key frame for the special effect, there is no value.
             */
            Map<String, List<MeicamFxParam<?>>> middleParamBetweenKeyFrames = getAllKeyParamParamAtSplitPoint(videoClip, splitPoint - videoClip.getInPoint());
            if (videoTrack.splitClip(clipIndex, splitPoint)) {
                MeicamVideoClip newVideoClip = (MeicamVideoClip) videoClip.clone();
                NvsVideoClip nvsVideoClip = videoTrack.getClipByIndex(clipIndex);
                videoClip.inPoint = nvsVideoClip.getInPoint();
                videoClip.outPoint = nvsVideoClip.getOutPoint();
                videoClip.trimIn = nvsVideoClip.getTrimIn();
                videoClip.trimOut = nvsVideoClip.getTrimOut();
                videoClip.curveSpeed = nvsVideoClip.getClipVariableSpeedCurvesString();
                videoClip.setObject(nvsVideoClip);
                for (CurveSpeed itemSpeed : videoClip.getCurveSpeedList()) {
                    if (itemSpeed.getSpeed().equals(videoClip.getCurveSpeed())) {
                        itemSpeed.setSpeed(nvsVideoClip.getClipVariableSpeedCurvesString());
                        break;
                    }
                }
                splitAllKeyFrame(videoClip, splitPoint, middleParamBetweenKeyFrames, 0, true);

                MeicamVideoFx meicamVideoFx = videoClip.getVideoFxById(NvsConstants.PROPERTY_FX);
                videoClip.bindToTimeline();
                boolean isComAnimation = false;
                if (meicamVideoFx != null) {
                    boolean isInAnimation = meicamVideoFx.getBooleanVal(NvsConstants.PACKAGE_TYPE_ANIMATION_IN);
                    if (!isInAnimation) {
                        String postPackageId = meicamVideoFx.getStringVal(NvsConstants.POST_PACKAGE_ID);
                        String packageId = meicamVideoFx.getStringVal(NvsConstants.PACKAGE_ID);
                        if (!TextUtils.isEmpty(postPackageId) || TextUtils.isEmpty(packageId)) {
                            isComAnimation = true;
                        }
                    }
                    if (isComAnimation) {
                        long duration = videoClip.getOutPoint() - videoClip.getInPoint();
                        double amplitude = duration * 1.0f / TIME_BASE;
                        meicamVideoFx.setFloatVal(NvsConstants.PACKAGE_EFFECT_OUT, duration);
                        meicamVideoFx.setExprVar(NvsConstants.AMPLITUDE, amplitude);
                    }
                }
                NvsVideoClip newNvsVideoClip = videoTrack.getClipByIndex(clipIndex + 1);
                for (CurveSpeed itemSpeed : newVideoClip.getCurveSpeedList()) {
                    if (itemSpeed.getSpeed().equals(newVideoClip.getCurveSpeed())) {
                        itemSpeed.setSpeed(newNvsVideoClip.getClipVariableSpeedCurvesString());
                        break;
                    }
                }
                if (WhiteList.isFLVAssetSplit(newNvsVideoClip.getFilePath())) {
                    newNvsVideoClip.setSoftWareDecoding(true);
                }
                newVideoClip.setObject(newNvsVideoClip);
                newVideoClip.trimIn = newNvsVideoClip.getTrimIn();
                newVideoClip.trimOut = newNvsVideoClip.getTrimOut();
                newVideoClip.inPoint = newNvsVideoClip.getInPoint();
                newVideoClip.outPoint = newNvsVideoClip.getOutPoint();
                newVideoClip.curveSpeed = newNvsVideoClip.getClipVariableSpeedCurvesString();
                meicamVideoFx = newVideoClip.getVideoFxById(NvsConstants.PROPERTY_FX);
                if (meicamVideoFx != null) {
                    /*分割的时候后片段没有动画
                     * There is no animation in the after clip after splitting.
                     * */
                    if (!isComAnimation) {
                        meicamVideoFx.mMeicamFxParam.put(NvsConstants.POST_PACKAGE_ID, new MeicamFxParam<>(TYPE_STRING, NvsConstants.POST_PACKAGE_ID, ""));
                        meicamVideoFx.mMeicamFxParam.put(NvsConstants.PACKAGE_ID, new MeicamFxParam<>(TYPE_STRING, NvsConstants.PACKAGE_ID, ""));
                        meicamVideoFx.mMeicamFxParam.put(NvsConstants.PACKAGE2_ID, new MeicamFxParam<>(TYPE_STRING, NvsConstants.PACKAGE2_ID, ""));
                        meicamVideoFx.mMeicamFxParam.put(NvsConstants.POST_PACKAGE2_ID, new MeicamFxParam<>(TYPE_STRING, NvsConstants.POST_PACKAGE2_ID, ""));
                        meicamVideoFx.mMeicamFxParam.put(NvsConstants.IS_POST_STORY_BOARD_3D, new MeicamFxParam<>(TYPE_BOOLEAN, NvsConstants.IS_POST_STORY_BOARD_3D, false));
                        meicamVideoFx.mMeicamFxParam.put(NvsConstants.PACKAGE_EFFECT_IN, new MeicamFxParam<>(TYPE_FLOAT, NvsConstants.PACKAGE_EFFECT_IN, 0));
                        meicamVideoFx.mMeicamFxParam.put(NvsConstants.PACKAGE_EFFECT_OUT, new MeicamFxParam<>(TYPE_FLOAT, NvsConstants.PACKAGE_EFFECT_OUT, 0));
                        meicamVideoFx.mMeicamFxParam.put(NvsConstants.PACKAGE2_EFFECT_IN, new MeicamFxParam<>(TYPE_FLOAT, NvsConstants.PACKAGE2_EFFECT_IN, 0));
                        meicamVideoFx.mMeicamFxParam.put(NvsConstants.PACKAGE2_EFFECT_OUT, new MeicamFxParam<>(TYPE_FLOAT, NvsConstants.PACKAGE2_EFFECT_OUT, 0));
                        meicamVideoFx.mMeicamFxParam.put(NvsConstants.AMPLITUDE, new MeicamFxParam<>(TYPE_STRING, NvsConstants.PACKAGE_EFFECT_OUT, String.valueOf(0)));
                    } else {
                        long duration = newVideoClip.getOutPoint() - newVideoClip.getInPoint();
                        double amplitude = duration * 1.0f / TIME_BASE;
                        meicamVideoFx.setFloatVal(NvsConstants.PACKAGE_EFFECT_OUT, duration);
                        meicamVideoFx.setExprVar(NvsConstants.AMPLITUDE, amplitude);
                    }
                }
                splitAllKeyFrame(newVideoClip, splitPoint, middleParamBetweenKeyFrames, videoClip.getOutPoint() - videoClip.getInPoint(), false);
                newVideoClip.bindToTimeline();
                mVideoClipList.add(clipIndex + 1, newVideoClip);
                for (int i = clipIndex; i < mVideoClipList.size(); i++) {
                    MeicamVideoClip video = mVideoClipList.get(i);
                    video.setIndex(video.getObject().getIndex());
                }
                /*转场后移
                * move transition
                * */
                moveTransition(clipIndex, 1);
                videoTrack.setBuiltinTransition(clipIndex, "");
                //logTransitionList();
                return newVideoClip;
            }
        }
        return null;
    }


    /**
     * Split all key frame.
     * 分割所有关键帧，进行删除或添加
     *
     * @param videoClip      the video clip video clip
     * @param splitPoint     the split point 分割点
     * @param keyFrameParams the key frame params 分割点处的关键帧信息
     * @param offset         the offset 偏移量，主要用于处理分割的右半部分
     * @param isLeft         the is left 是否是分割的左半边分
     */
    public void splitAllKeyFrame(MeicamVideoClip videoClip, long splitPoint, Map<String, List<MeicamFxParam<?>>> keyFrameParams, long offset, boolean isLeft) {
        if (CommonUtils.isEmpty(keyFrameParams)) {
            return;
        }
        int videoFxCount = videoClip.getVideoFxCount();
        for (int index = 0; index < videoFxCount; index++) {
            MeicamVideoFx videoFx = videoClip.getVideoFx(index);
            if (TYPE_PACKAGE.equals(videoFx.getType())) {
                continue;
            }
            String desc = videoFx.getDesc();
            List<MeicamFxParam<?>> meicamFxParams = keyFrameParams.get(desc);
            if (meicamFxParams == null) {
                continue;
            }
            splitKeyFrame(videoFx, null, meicamFxParams, splitPoint, offset, isLeft);
        }
    }


    /**
     * Gets all key param param at split point.
     * 获取所有特效的分割点处的参数，如果没有关键帧，则返回null
     *
     * @param videoClip the video clip the video clip
     * @param atTime    the at time
     * @return the all key param param at split point
     */
    public Map<String, List<MeicamFxParam<?>>> getAllKeyParamParamAtSplitPoint(MeicamVideoClip videoClip, long atTime) {
        int videoFxCount = videoClip.getVideoFxCount();
        Map<String, List<MeicamFxParam<?>>> result = new HashMap<>();
        for (int index = 0; index < videoFxCount; index++) {
            MeicamVideoFx videoFx = videoClip.getVideoFx(index);
            if (TYPE_PACKAGE.equals(videoFx.getType())) {
                continue;
            }
            List<MeicamFxParam<?>> middleParamBetweenKeyFrames = getKeyParamParamAtSplitPoint(videoFx, null, atTime);
            if (middleParamBetweenKeyFrames != null) {
                result.put(videoFx.getDesc(), middleParamBetweenKeyFrames);
            }
        }
        return result;
    }

    /**
     * Split key frame.
     * 分割关键帧，进行删除或添加
     *
     * @param videoFx        the video fx 特效
     * @param key            the key 关键帧关键字
     * @param keyFrameParams the key frame params 分割的关键帧参数
     * @param splitPoint     the split point 分割时间点
     * @param offset         the offset 偏移量，主要用于处理分割的右半部分
     * @param isLeft         the is left 是否是分割的左半边分
     */
    private void splitKeyFrame(MeicamVideoFx videoFx, String key, @NonNull List<MeicamFxParam<?>> keyFrameParams, long splitPoint, long offset, boolean isLeft) {
        KeyFrameProcessor<NvsVideoFx> keyFrameProcessor = videoFx.keyFrameProcessor();
        Map<Long, MeicamKeyFrame> keyFrameMap = keyFrameProcessor.getKeyFrameMap(key);
        boolean isRemoved = false;
        for (Map.Entry<Long, MeicamKeyFrame> keyFrameSet : keyFrameMap.entrySet()) {
            MeicamKeyFrame keyFrame = keyFrameSet.getValue();
            long time = keyFrame.getAtTime() + videoFx.getInPoint() - splitPoint;
            if (time > 0) {
                //编辑的是左边的clip，删除右半部分关键帧
                //Edit the left clip to delete the right half of the key frame
                if (isLeft) {
                    keyFrameProcessor.removeKeyFrame(new String[]{key}, keyFrame.getAtTime(), false);
                    isRemoved = true;
                }
            } else {
                //编辑的是右边的clip，删除左半部分关键帧
                //Edit the clip on the right and delete the left half of the key frame
                if (!isLeft) {
                    keyFrameProcessor.removeKeyFrame(new String[]{key}, keyFrame.getAtTime(), false);
                    isRemoved = true;
                }
            }
        }
        if (offset != 0) {
            Map<Long, MeicamKeyFrame> newKeyFrame = new HashMap<>();
            keyFrameMap = keyFrameProcessor.getKeyFrameMap(key);
            Set<Map.Entry<Long, MeicamKeyFrame>> entries = keyFrameMap.entrySet();
            for (Map.Entry<Long, MeicamKeyFrame> entry : entries) {
                MeicamKeyFrame value = entry.getValue();
                value.setAtTime(value.getAtTime() - offset);
                newKeyFrame.put(value.getAtTime(), value);
            }
            keyFrameProcessor.setKeyFrameMap(newKeyFrame);
        }
        if (keyFrameProcessor.haveKeyFrame(key) && isRemoved) {
            //删除完后，仍然存在，则在头部或尾部补充一个关键帧
            //If it still exists after deletion, add a key frame at the head or tail.
            long atTime = isLeft ? splitPoint - videoFx.getInPoint() : 0;
            MeicamKeyFrame insertKeyFrame = keyFrameProcessor.addKeyFrame(atTime);
            if (insertKeyFrame != null) {
                for (MeicamFxParam<?> item : keyFrameParams) {
                    String type = item.getType();
                    String itemKey = item.getKey();
                    Object itemValue = item.getValue();
                    if (MeicamFxParam.TYPE_OBJECT.equals(type)) {
                        if (itemValue instanceof MeicamMaskRegionInfo) {
                            insertKeyFrame.setArbDataVal(itemKey, (MeicamMaskRegionInfo) itemValue);
                        }
                    } else if (TYPE_BOOLEAN.equals(type)) {
                        if (itemValue instanceof Boolean) {
                            insertKeyFrame.setBooleanVal(itemKey, (Boolean) itemValue);
                        }
                    } else if (TYPE_FLOAT.equals(type)) {
                        if (itemValue instanceof Float) {
                            insertKeyFrame.setFloatVal(itemKey, (Float) itemValue);
                        }
                    } else if (TYPE_INT.equals(type)) {
                        if (itemValue instanceof Integer) {
                            insertKeyFrame.setIntVal(itemKey, (Integer) itemValue);
                        }
                    }
                }
            }
        } else {
            //删除后不存在关键帧，且之前是有关键帧的，对齐数据
            //There are no keyframes after deletion, and there are keyframes before, align the data.
            for (MeicamFxParam<?> item : keyFrameParams) {
                String type = item.getType();
                String itemKey = item.getKey();
                Object itemValue = item.getValue();
                if (MeicamFxParam.TYPE_OBJECT.equals(type)) {
                    if (itemValue instanceof MeicamMaskRegionInfo) {
                        videoFx.setObjectVal(itemKey, (MeicamMaskRegionInfo) itemValue);
                    }
                } else if (TYPE_BOOLEAN.equals(type)) {
                    if (itemValue instanceof Boolean) {
                        videoFx.setBooleanVal(itemKey, (Boolean) itemValue);
                    }
                } else if (TYPE_FLOAT.equals(type)) {
                    if (itemValue instanceof Float) {
                        videoFx.setFloatVal(itemKey, (Float) itemValue);
                    }
                } else if (TYPE_INT.equals(type)) {
                    if (itemValue instanceof Integer) {
                        videoFx.setIntVal(itemKey, (Integer) itemValue);
                    }
                }
            }
        }
    }

    /**
     * Gets key param param at split point.
     * 获取分割点处的参数，如果没有关键帧，则返回null
     *
     * @param videoFx the video fx 特效
     * @param key     the key 关键帧的关键字key
     * @param atTime  the at time 关键帧点
     * @return the key param param at split point 关键帧参数
     */
    private List<MeicamFxParam<?>> getKeyParamParamAtSplitPoint(MeicamVideoFx videoFx, String key, long atTime) {
        KeyFrameProcessor<NvsVideoFx> keyFrameHolder = videoFx.keyFrameProcessor();
        MeicamKeyFrame before = keyFrameHolder.findKeyFrame(key, atTime, true);
        MeicamKeyFrame after = keyFrameHolder.findKeyFrame(key, atTime, false);
        if (before != null || after != null) {
            return keyFrameHolder.getKeyFrameParams(key, atTime);
        }
        return null;
    }

    /**
     * 删除视频片段
     * Remove meicam video clip
     *
     * @param index     the index 索引
     * @param keepSpace true keep space ,false not 片段移除后，是否保留该片段在轨道上的空间。值为true则保留，false则不保留
     * @return the remove MeicamVideoClip被删除的片段
     */
    public MeicamVideoClip removeVideoClip(int index, boolean keepSpace) {
        NvsVideoTrack nvsVideoTrack = getObject();
        if (nvsVideoTrack != null && index >= 0 && index < mVideoClipList.size()) {
            NvsVideoClip nvsVideoClip = nvsVideoTrack.getClipByIndex(index);
            if (nvsVideoClip == null) {
                LogUtils.e("removeVideoClip failed!!!");
                return null;
            }
            MeicamVideoClip videoClip = mVideoClipList.get(index);
            if (videoClip.getInPoint() != nvsVideoClip.getInPoint()) {
                LogUtils.e("removeVideoClip failed!!!");
                return null;
            }
            /*移除底层数据
            * Remove native object
            * */
            if (nvsVideoTrack.removeClip(index, keepSpace)) {
                MeicamVideoClip remove = mVideoClipList.remove(index);
                /*更改上层索引,以及出入点
                * Update index、in and out point
                * */
                for (int i = index; i < mVideoClipList.size(); i++) {
                    videoClip = mVideoClipList.get(i);
                    videoClip.setIndex(videoClip.getObject().getIndex());
                    videoClip.updateInAndOutPoint();
                }
                removeTransition(index);
                if (index > 0) {
                    /*removeClip后需要检查上一个转场
                    *Need to check the previous transition after removeClip.
                    * */
                    MeicamTransition transition = getTransition(index - 1);
                    if (transition == null) {
                        /*清除默认转场
                        * Remove default transition
                        * */
                        nvsVideoTrack.setBuiltinTransition(index - 1, "");
                    } else {
                        /*恢复转场
                        * Restore the transition
                        * */
                        NvsVideoTransition nvsVideoTransition = buildNvsTransition(index - 1,
                                transition.getType(), transition.getDesc());
                        if (nvsVideoTransition != null) {
                            transition.setObject(nvsVideoTransition);
                            transition.setDuration(transition.getDuration());
                        }
                    }
                    // 把后边的转场进行前移，目前sdk会自动做前移。这里上层同步设置一下。
                    // Move the back transition forward. Currently the sdk will do this automatically. I'm going to set the upper level synchronization here.
                    for (MeicamTransition itemTransition : mTransitionInfoList) {
                        if (itemTransition.getIndex() > index) {
                            itemTransition.setIndex(itemTransition.getIndex() - 1);
                            NvsVideoTransition nvsVideoTransition = buildNvsTransition(itemTransition.getIndex(), itemTransition.getType(), itemTransition.getDesc());
                            itemTransition.setObject(nvsVideoTransition);
                            itemTransition.setDuration(itemTransition.getDuration());
                        }
                    }
                }
                // logVideoClipList();
                // logTransitionList();
                return remove;
            }
        }
        return null;
    }

    void logVideoClipList() {
        for (MeicamVideoClip videoClip : mVideoClipList) {
            LogUtils.d("VideoClip=" + videoClip + ",nvs=" + videoClip.getObject() + ",index=" + videoClip.getObject().getIndex() + ",inP=" + videoClip.getObject().getInPoint());
        }
        for (int i = 0; i < getObject().getClipCount(); i++) {
            NvsVideoClip nvsVideoClip = getObject().getClipByIndex(i);
            if (nvsVideoClip != null) {
                LogUtils.d("nvsVideoClip=" + nvsVideoClip.getIndex()
                        + ",inP=" + nvsVideoClip.getInPoint() + ",outP=" + nvsVideoClip.getOutPoint()
                        + ",i=" + i);
            }
        }
    }

    void logTransitionList() {
        for (MeicamTransition transition : mTransitionInfoList) {
            LogUtils.d("transition=" + transition);
        }
        for (int i = 0; i < getObject().getClipCount(); i++) {
            NvsVideoTransition nvsTransition = getObject().getTransitionBySourceClipIndex(i);
            if (nvsTransition != null) {
                LogUtils.d("nvsTransition=" + nvsTransition.getBuiltinVideoTransitionName()
                        + ",pName=" + nvsTransition.getVideoTransitionPackageId() + ",type=" + nvsTransition.getVideoTransitionType()
                        + ",i=" + i);
            }
        }
    }

    /**
     * 检查转场，片段数量增加的时候需要检查，因为底层会默认添加转场，
     * Check transition
     */
    private void checkTransition() {
        NvsVideoTrack videoTrack = getObject();
        if (videoTrack != null) {
            for (int i = 0; i < getClipCount(); i++) {
                MeicamTransition transition = getTransition(i);
                if (transition != null) {
                    NvsVideoTransition nvsVideoTransition = buildNvsTransition(i, transition.getType(), transition.getDesc());
                    if (nvsVideoTransition != null) {
                        transition.setObject(nvsVideoTransition);
                        transition.setDuration(transition.getDuration());
                    } else {
                        removeTransition(i);
                    }
                } else {
                    videoTrack.setBuiltinTransition(i, "");
                }

            }
        }
    }

    /**
     * 构建转场
     * Build transition
     *
     * @param index        the index 索引
     * @param type         the type 类型
     * @param transitionId the transition id 转场标识id
     * @return NvsVideoTransition 转场
     */
    private NvsVideoTransition buildNvsTransition(int index, String type, String transitionId) {
        NvsVideoTrack nvsVideoTrack = getObject();
        if (nvsVideoTrack != null) {
            NvsVideoTransition nvsVideoTransition = null;
            if (TYPE_BUILD_IN.equals(type)) {
                nvsVideoTrack.setPackagedTransition(index, "");
                nvsVideoTransition = nvsVideoTrack.setBuiltinTransition(index, transitionId);
            } else if (TYPE_PACKAGE.equals(type)) {
                nvsVideoTrack.setBuiltinTransition(index, "");
                nvsVideoTransition = nvsVideoTrack.setPackagedTransition(index, transitionId);
            }
            return nvsVideoTransition;
        }
        return null;
    }

    /**
     * 构建转场
     * Build transition
     *
     * @param index        the index 索引
     * @param type         the type 类型
     * @param transitionId the transition id 转场标识id
     * @return MeicamTransition 转场
     */
    public MeicamTransition buildTransition(int index, String type, String transitionId) {
        NvsVideoTransition nvsVideoTransition = buildNvsTransition(index, type, transitionId);
        return buildTransition(nvsVideoTransition, index, type, transitionId);
    }

    /**
     * 创建上层数据
     * Build meicam transition meicam transition.
     *
     * @param nvsVideoTransition the nvs video transition
     * @param index              the index
     * @param type               the type
     * @param transitionId       the transition id
     * @return the meicam transition
     */
    private MeicamTransition buildTransition(NvsVideoTransition nvsVideoTransition, int index, String type, String transitionId) {
        if (nvsVideoTransition != null) {
            MeicamTransition transition = new MeicamTransition(nvsVideoTransition, index, type, transitionId);
            transition.setObject(nvsVideoTransition);
            transition.setDuration(transition.getDuration());
            transition.setTrackIndex(getIndex());
            addTransition(transition, true);
            return transition;
        }
        return null;
    }

    /**
     * 构建转场
     * Build transition
     *
     * @param transition 旧的转场信息
     * @return MeicamTransition 转场
     */
    public MeicamTransition buildTransition(MeicamTransition transition, int index) {
        if (transition != null) {
            NvsVideoTransition nvsVideoTransition = buildNvsTransition(index, transition.getType(), transition.getDesc());
            if (nvsVideoTransition != null) {
                transition.setObject(nvsVideoTransition);
                transition.bindToTimeline();
                transition.setDuration(transition.getDuration());
                addTransition(transition, true);
                return transition;
            }
        }
        return null;
    }

    /**
     * 添加转场
     * Add transition
     *
     * @param transition        the meicam transition 转场
     * @param removeDuplication true remove duplication去重，false not不去重
     */
    private void addTransition(MeicamTransition transition, boolean removeDuplication) {
        if (removeDuplication) {
            for (MeicamTransition item : mTransitionInfoList) {
                if (item.getIndex() == transition.getIndex()) {
                    /*如果已经添加过了先删除
                    * If it has been added, delete it first.
                    * */
                    mTransitionInfoList.remove(item);
                    break;
                }
            }
        }
        mTransitionInfoList.add(transition);
    }

    /**
     * 移动转场
     * Move transition
     *
     * @param startIndex  move start index 移动的起始索引
     * @param offsetCount the offset count to move 偏移数量
     */
    private void moveTransition(int startIndex, int offsetCount) {
        NvsVideoTrack nvsVideoTrack = getObject();
        if (nvsVideoTrack != null) {
            MeicamVideoClip lastVideoClip = getVideoClip(getClipCount() - 1);
            int lastValidIndex = CommonData.CLIP_HOLDER.equals(lastVideoClip.getVideoType()) ?
                    getClipCount() - 2 : getClipCount() - 1;
            for (int i = 0; i < mTransitionInfoList.size(); i++) {
                MeicamTransition transition = mTransitionInfoList.get(i);
                if (transition.getIndex() >= startIndex) {
                    /*起始索引后边的所有转场先清除
                    * All transitions after the start index are cleared first.
                    * */
                    buildNvsTransition(transition.getIndex(), transition.getType(), "");
                    transition.setIndex(transition.getIndex() + offsetCount);
                    if (transition.getIndex() >= lastValidIndex) {
                        /*大于最后有效索引则移除该转场的上层数据
                        * If it is greater than the last valid index, the upper data of the transition will be removed.
                        * */
                        mTransitionInfoList.remove(i);
                        --i;
                    }
                }
            }
            /*重新添加对应对的转场
            *Add the transition to the response again.
            * */
            for (MeicamTransition transition : mTransitionInfoList) {
                if (transition.getIndex() >= startIndex) {
                    NvsVideoTransition nvsVideoTransition = buildNvsTransition(transition.getIndex(), transition.getType(), transition.getDesc());
                    transition.setObject(nvsVideoTransition);
                    transition.setDuration(transition.getDuration());
                }
            }
        }
    }



    /**
     * 向前移动转场
     * Move forward transition
     *
     * @param startIndex  move start index 移动的起始索引
     * @param offsetCount the offset count to move 偏移数量
     */
    private void moveForwardTransition(int startIndex, int offsetCount) {
        NvsVideoTrack nvsVideoTrack = getObject();
        if (nvsVideoTrack != null) {
            for (int i = 0; i < mTransitionInfoList.size(); i++) {
                MeicamTransition transition = mTransitionInfoList.get(i);
                if (transition.getIndex() >= startIndex) {
                    /*起始索引后边的所有转场先清除
                     * All transitions after the start index are cleared first.
                     * */
                    buildNvsTransition(transition.getIndex(), transition.getType(), "");
                    transition.setIndex(transition.getIndex() - offsetCount);
                    if (transition.getIndex() < 0) {
                        /*小于0的移除
                         * Remove less than 0.
                         * */
                        mTransitionInfoList.remove(i);
                        --i;
                    }
                }
            }
            /*重新添加对应对的转场
             *Add the transition to the response again.
             * */
            for (MeicamTransition transition : mTransitionInfoList) {
                if (transition.getIndex() >= startIndex) {
                    NvsVideoTransition nvsVideoTransition = buildNvsTransition(transition.getIndex(), transition.getType(), transition.getDesc());
                    transition.setObject(nvsVideoTransition);
                    transition.setDuration(transition.getDuration());
                }
            }
        }
    }

    /**
     * 把目标转场应用到视频轨道所有片段
     * Apply the target transitions to all  in the video track
     */
    public boolean applyTransitionToAll(MeicamTransition fromTransition) {
        NvsVideoTrack nvsVideoTrack = getObject();
        if (nvsVideoTrack != null && fromTransition != null) {
            for (MeicamTransition transition : mTransitionInfoList) {
                buildNvsTransition(transition.getIndex(), transition.getType(), "");
            }
            mTransitionInfoList.clear();
            int lastIndex = nvsVideoTrack.getClipCount() - 1;
            if (lastIndex >= 0) {
                MeicamVideoClip lastVideoClip = getVideoClip(lastIndex);
                if (CommonData.CLIP_HOLDER.equals(lastVideoClip.getVideoType())) {
                    lastIndex = lastIndex - 1;
                }
            }
            for (int i = 0; i <= lastIndex - 1; i++) {
                NvsVideoTransition nvsVideoTransition = buildNvsTransition(i, fromTransition.getType(), fromTransition.getDesc());
                if (nvsVideoTransition != null) {
                    MeicamTransition transition = (MeicamTransition) fromTransition.clone();
                    transition.setIndex(i);
                    transition.setObject(nvsVideoTransition);
                    transition.setDuration(transition.getDuration());
                    addTransition(transition, false);
                }
            }
            return true;
        }
        return false;
    }

    /**
     * 移除所有的转场
     * Remove all transition
     **/
    public void removeAllTransition() {
        NvsVideoTrack nvsVideoTrack = getObject();
        if (nvsVideoTrack != null) {
            for (MeicamTransition transition : mTransitionInfoList) {
                if (TYPE_BUILD_IN.equals(transition.getType())) {
                    nvsVideoTrack.setBuiltinTransition(transition.getIndex(), "");
                } else if (TYPE_PACKAGE.equals(transition.getType())) {
                    nvsVideoTrack.setPackagedTransition(transition.getIndex(), "");
                }
            }
            mTransitionInfoList.clear();
        }
    }

    /**
     * 移除转场
     * Remove transition
     *
     * @param index the index 索引
     * @return MeicamTransition 转场
     */
    public MeicamTransition removeTransition(int index) {
        NvsVideoTrack nvsVideoTrack = getObject();
        if (nvsVideoTrack != null) {
            for (MeicamTransition transition : mTransitionInfoList) {
                if (index == transition.getIndex()) {
                    nvsVideoTrack.setPackagedTransition(index, "");
                    nvsVideoTrack.setBuiltinTransition(index, "");
                    mTransitionInfoList.remove(transition);
                    return transition;
                }
            }
        }
        return null;
    }

    /**
     * 获取转场,注意转场的索引并不是在集合中的位置，而是其所在片段的索引
     * Get transition
     *
     * @param transitionIndex the index 转场的索引
     * @return MeicamTransition 转场
     */
    public MeicamTransition getTransition(int transitionIndex) {
        if (transitionIndex >= 0) {
            for (MeicamTransition transition : mTransitionInfoList) {
                if (transitionIndex == transition.getIndex()) {
                    return transition;
                }
            }
        }
        return null;
    }

    /**
     * 获取转场通过在集合中的位置,注意转场的索引并不是在集合中的位置
     * get Transition By CollectionI ndex
     *
     * @param index the index 转场在集合中的位置
     * @return MeicamTransition 转场
     */
    public MeicamTransition getTransitionByCollectionIndex(int index) {
        if (CommonUtils.isIndexAvailable(index, mTransitionInfoList)) {
            return mTransitionInfoList.get(index);
        }
        return null;
    }

    /**
     * 获取转场的数量
     * Get transition count
     */
    public int getTransitionCount() {
        return mTransitionInfoList.size();
    }

    /**
     * 获取轨道时长
     * Get video track duration
     */
    public long getDuration() {
        NvsVideoTrack videoTrack = getObject();
        if (videoTrack != null) {
            return videoTrack.getDuration();
        }
        return 0;
    }

    /**
     * 是否是静音
     * Mute or not
     *
     * @return true mute 静音,false not非静音
     */
    public boolean isMute() {
        return mIsMute;
    }

    /**
     * 设置静音
     * Set mute
     *
     * @param isMute true 静音，false not 非静音
     * @return true success 成功,false not失败
     */
    public boolean setIsMute(boolean isMute) {
        NvsVideoTrack object = getObject();
        if (object != null) {
            if (isMute) {
                object.setVolumeGain(0, 0);
            } else {
                object.setVolumeGain(1, 1);
            }
            this.mIsMute = isMute;
            return true;
        }
        return false;
    }

    /**
     * 设置静音
     * Set mute
     *
     * @param isMute           true 静音，false not 非静音
     * @param changeClipVolume true 影响clip音量，false not 不影响clip音量
     * @return true success 成功,false not失败
     */
    public boolean setIsMute(boolean isMute, boolean changeClipVolume) {
        boolean result = setIsMute(isMute);
        if (result && changeClipVolume) {
            setClipMute(isMute);
        }
        return result;
    }


    private void setClipMute(boolean isMute) {
        for (MeicamVideoClip videoClip : mVideoClipList) {
            videoClip.setVolume(isMute ? 0 : 1);
        }
    }

    /**
     * 获取字幕列表
     *
     * @return The list of caption clip 字幕列表
     */
    public List<MeicamCaptionClip> getCaptionList() {
        return captionList;
    }

    /**
     * 获取组合字幕列表
     *
     * @return The list of compound caption 组合字幕列表
     */
    public List<MeicamCompoundCaptionClip> getCompoundCaptionList() {
        return compoundCaptionList;
    }

    /**
     * 设置静音，通过设置videoClip
     * Set mute by set VideoClip
     *
     * @param isMute true 静音，false not 非静音
     */
    public void setIsMuteByVideoClip(boolean isMute) {
        for (int i = 0; i < mVideoClipList.size(); i++) {
            MeicamVideoClip meicamVideoClip = mVideoClipList.get(i);
            if (isMute) {
                meicamVideoClip.setVolume(0);
            } else {
                meicamVideoClip.setVolume(1);
            }
        }
        this.mIsMute = isMute;

    }


    @Override
    public LMeicamVideoTrack parseToLocalData() {
        LMeicamVideoTrack local = new LMeicamVideoTrack(getIndex());
        setCommondData(local);
        local.setIsMute(isMute());
        try {
            for (MeicamVideoClip clipInfo : mVideoClipList) {
                local.getVideoClipList().add(clipInfo.parseToLocalData());
            }
        } catch (Exception e) {
            LogUtils.e(e);
        }
       /* for (MeicamVideoClip clipInfo : mVideoClipList) {
            local.getVideoClipList().add(clipInfo.parseToLocalData());
        }*/
        for (MeicamTransition meicamTransition : mTransitionInfoList) {
            local.getTransitionInfoList().add(meicamTransition.parseToLocalData());
        }

        if (trackVideoFxList != null) {
            for (MeicamTrackVideoFx trackVideoFx : trackVideoFxList) {
                local.getTrackVideoFxList().add(trackVideoFx.parseToLocalData());
            }
        }
        return local;
    }

    /**
     * 恢复视频片段
     * Recover video clip
     *
     * @param videoPath the video path 片段路径
     * @param inPoint   the inPoint in timeline
     * @param trimIn    the trim in point  裁入点
     * @param trimOut   the trim out point  裁出点
     * @return MeicamVideoClip the video clip视频片段
     **/
    private MeicamVideoClip recoverVideoClip(String videoPath, long inPoint, long trimIn,
                                             long trimOut) {
        NvsVideoTrack nvsVideoTrack = getObject();
        if (nvsVideoTrack != null) {
            NvsVideoClip nvsVideoClip = nvsVideoTrack.addClip(videoPath, inPoint, trimIn, trimOut);
            if (nvsVideoClip != null) {
                return doCommonOperation(nvsVideoClip, createVideoClip(nvsVideoClip), false, false);
            }
        }
        return null;
    }

    @Override
    public void recoverFromLocalData(LMeicamVideoTrack lVideoTrack) {
        setIsMute(lVideoTrack.isMute(), true);
        setShow(lVideoTrack.isShow());
        setVolume(lVideoTrack.getVolume());
        if (lVideoTrack.getVideoClipList() != null) {
            for (LMeicamVideoClip lVideoClip : lVideoTrack.getVideoClipList()) {
                MeicamVideoClip videoClip = recoverVideoClip(lVideoClip.getVideoReverse() ? lVideoClip.getReverseFilePath() : lVideoClip.getFilePath(), lVideoClip.getInPoint(),
                        lVideoClip.getTrimIn(), lVideoClip.getTrimOut());
                if (videoClip != null) {
                    videoClip.recoverFromLocalData(lVideoClip);
                }
            }
        }
        if (lVideoTrack.getTransitionInfoList() != null) {
            for (LMeicamTransition lTransition : lVideoTrack.getTransitionInfoList()) {
                MeicamTransition transition = buildTransition(lTransition.getIndex(),
                        lTransition.getType(), lTransition.getDesc());
                if (transition != null) {
                    transition.recoverFromLocalData(lTransition);
                }
            }
        }
        List<LMeicamTrackVideoFx> trackVideoFxList = lVideoTrack.getTrackVideoFxList();
        if (!trackVideoFxList.isEmpty()) {
            for (LMeicamTrackVideoFx lMeicamTrackVideoFx : trackVideoFxList) {
                MeicamTrackVideoFx trackVideoFx = appendTrackVideoFx(lMeicamTrackVideoFx.getType(), lMeicamTrackVideoFx.getDesc(), lMeicamTrackVideoFx.getInPoint(), lMeicamTrackVideoFx.getOutPoint() - lMeicamTrackVideoFx.getInPoint(), lMeicamTrackVideoFx.getFlag(), null);
                if (trackVideoFx != null) {
                    trackVideoFx.recoverFromLocalData(lMeicamTrackVideoFx);
                }
            }
        }
    }

    @Override
    public void recoverFromTimelineData(NvsObject nvsObject) {
        if (nvsObject instanceof NvsVideoTrack) {
            NvsVideoTrack nvsVideoTrack = (NvsVideoTrack) nvsObject;
            setObject(nvsVideoTrack);
            loadData();
        }
    }

    @Override
    void loadData() {
        NvsVideoTrack nvsVideoTrack = getObject();
        if (nvsVideoTrack != null) {
            mIsMute = nvsVideoTrack.getVolumeGain().leftVolume == 0;
            setVolume(nvsVideoTrack.getVolumeGain().leftVolume);
            /*
             * 恢复轨道字幕
             * Restore track caption
             */
            NvsTrackCaption nvsTrackCaption = nvsVideoTrack.getFirstCaption();
            while (nvsTrackCaption != null) {
                MeicamCaptionClip meicamCaptionClip = new MeicamCaptionClip(nvsTrackCaption,
                        nvsTrackCaption.getText(), nvsTrackCaption.getInPoint(), nvsTrackCaption.getOutPoint());
                meicamCaptionClip.loadData();
                captionList.add(meicamCaptionClip);
                nvsTrackCaption = nvsVideoTrack.getNextCaption(nvsTrackCaption);
            }

            /*
             * 恢复轨道组合字幕
             * Restore track compound caption
             */
            NvsTrackCompoundCaption nvsTrackCompoundCaption = nvsVideoTrack.getFirstCompoundCaption();
            while (nvsTrackCompoundCaption != null) {
                MeicamCompoundCaptionClip meicamCompoundCaptionClip
                        = new MeicamCompoundCaptionClip(nvsTrackCompoundCaption);
                meicamCompoundCaptionClip.loadData();
                compoundCaptionList.add(meicamCompoundCaptionClip);
                nvsTrackCompoundCaption = nvsVideoTrack.getNextCaption(nvsTrackCompoundCaption);
            }

            int clipCount = nvsVideoTrack.getClipCount();
            for (int i = 0; i < clipCount; i++) {
                NvsVideoClip nvsVideoClip = nvsVideoTrack.getClipByIndex(i);
                MeicamVideoClip videoClip = createVideoClip(nvsVideoClip);
                videoClip.recoverFromTimelineData(nvsVideoClip);
                addVideoClip(videoClip, nvsVideoClip.getIndex());
                NvsVideoTransition nvsVideoTransition = nvsVideoTrack.getTransitionBySourceClipIndex(i);
                if (nvsVideoTransition != null) {
                    boolean isBuiltin = nvsVideoTransition.getVideoTransitionType() == NvsVideoTransition.VIDEO_TRANSITION_TYPE_BUILTIN;
                    MeicamTransition transition = buildTransition(nvsVideoTransition, i, isBuiltin ? TYPE_BUILD_IN : TYPE_PACKAGE,
                            isBuiltin ? nvsVideoTransition.getBuiltinVideoTransitionName() : nvsVideoTransition.getVideoTransitionPackageId());
                    if (transition != null) {
                        transition.recoverFromTimelineData(nvsVideoTransition);
                    }
                }
            }
        }
    }

    public float[] getTranslation() {
        NvsVideoTrack object = getObject();
        float[] trans = new float[2];
        if (object != null) {
            NvsTrackVideoFx firstTrackVideoFx = object.getFirstTrackVideoFx();
            while (firstTrackVideoFx != null) {
                String videoFxName = firstTrackVideoFx.getBuiltinTrackVideoFxName();
                if (NvsConstants.FX_TRANSFORM_2D.equals(videoFxName)) {
                    trans[0] = (float) firstTrackVideoFx.getFloatVal(NvsConstants.FX_TRANSFORM_2D_TRANS_X);
                    trans[1] = (float) firstTrackVideoFx.getFloatVal(NvsConstants.FX_TRANSFORM_2D_TRANS_Y);
                    break;
                }
                firstTrackVideoFx = object.getNextTrackVideoFx(firstTrackVideoFx);
            }
        }
        return trans;
    }

    public float getScale() {
        NvsVideoTrack object = getObject();
        if (object != null) {
            NvsTrackVideoFx firstTrackVideoFx = object.getFirstTrackVideoFx();
            while (firstTrackVideoFx != null) {
                String videoFxName = firstTrackVideoFx.getBuiltinTrackVideoFxName();
                if (NvsConstants.FX_TRANSFORM_2D.equals(videoFxName)) {
                    return (float) firstTrackVideoFx.getFloatVal(NvsConstants.FX_TRANSFORM_2D_SCALE_X);
                }
                firstTrackVideoFx = object.getNextTrackVideoFx(firstTrackVideoFx);
            }
        }
        return 1;
    }


    /**
     * Merge video clip.
     * 合并video clip
     *
     * @param firstIndex the first index
     */
    public void mergeVideoClip(int firstIndex) {
        MeicamVideoClip preClip = getVideoClip(firstIndex);
        MeicamVideoClip nextClip = getVideoClip(firstIndex + 1);
        if (preClip == null || nextClip == null) {
            return;
        }
        //removeVideoClip 会删除转场，这里需要备份
        //The removeVideoClip will delete the transition, and backup is required here;
        List<MeicamTransition> transitions = null;
        if (!mTransitionInfoList.isEmpty()) {
            transitions = new ArrayList<>();
            for (MeicamTransition meicamTransition : mTransitionInfoList) {
                transitions.add(meicamTransition);
            }
        }
        long duration = nextClip.getTrimOut() - nextClip.getTrimIn();
        removeVideoClip(firstIndex + 1, false);
        preClip.setTrimOut(preClip.getTrimOut() + duration, true);
        for (int index = preClip.getIndex(); index < mVideoClipList.size(); index++) {
            mVideoClipList.get(index).updateInAndOutPoint();
        }

        if (transitions != null) {
            mTransitionInfoList = transitions;
            //转场往前移动 move forward the transition.
            moveForwardTransition(firstIndex, 1);
        }
    }

    public MeicamVideoClip addTimelineClip(MeicamTimeline timeline) {
        NvsVideoTrack object = getObject();
        if (object != null) {
            NvsVideoClip videoClip = object.appendTimelineClip(timeline.getObject());
            if (videoClip != null) {
                return doCommonOperation(videoClip, createVideoClip(videoClip), false, false);
            }
        }
        return null;
    }

    /**
     * Append track video fx meicam track video fx.
     * 添加轨道特效
     *
     * @param fxType   the fx type 特效类型
     * @param desc     the desc 特效描述符
     * @param inPoint  the in point 特效入点
     * @param duration the duration 特效时长
     * @param flag     the flag 特效flg
     * @return the meicam track video fx 轨道特效
     */
    public MeicamTrackVideoFx appendTrackVideoFx(String fxType, String desc, long inPoint, long duration, int flag, String tag) {
        NvsVideoTrack object = getObject();
        if (object == null) {
            return null;
        }
        NvsTrackVideoFx nvsTrackVideoFx;
        if (TYPE_BUILD_IN.equals(fxType)) {
            nvsTrackVideoFx = object.addBuiltinTrackVideoFx(inPoint, duration, desc, flag);
        } else {
            nvsTrackVideoFx = object.addPackagedTrackVideoFx(inPoint, duration, desc, flag);
        }
        if (nvsTrackVideoFx != null) {
            MeicamTrackVideoFx trackVideoFx = new MeicamTrackVideoFx(nvsTrackVideoFx, fxType, desc, inPoint, duration, flag, tag);
            addTrackVideoFx(trackVideoFx);
            return trackVideoFx;
        }
        return null;
    }

    public MeicamTrackVideoFx appendTrackVideoFx(MeicamTrackVideoFx oldVideoFx) {
        if (oldVideoFx == null) {
            LogUtils.e("OldVideoFx is null!");
            return null;
        }
        NvsVideoTrack object = getObject();
        if (object == null) {
            return null;
        }
        NvsTrackVideoFx nvsTrackVideoFx;
        String fxType = oldVideoFx.getType();
        long inPoint = oldVideoFx.getInPoint();
        long duration = oldVideoFx.getOutPoint() - inPoint;
        String desc = oldVideoFx.getDesc();
        if (TYPE_BUILD_IN.equals(fxType)) {
            nvsTrackVideoFx = object.addBuiltinTrackVideoFx(inPoint, duration, desc, oldVideoFx.getFlag());
        } else {
            nvsTrackVideoFx = object.addPackagedTrackVideoFx(inPoint, duration, desc, oldVideoFx.getFlag());
        }
        if (nvsTrackVideoFx != null) {
            oldVideoFx.setObject(nvsTrackVideoFx);
            oldVideoFx.bindToTimeline();
            addTrackVideoFx(oldVideoFx);
            return oldVideoFx;
        }
        return null;
    }

    private void addTrackVideoFx(MeicamTrackVideoFx videoFx){
        if (trackVideoFxList == null) {
            trackVideoFxList = new ArrayList<>();
        }
        trackVideoFxList.add(videoFx);
    }

    /**
     * Remove track video fx meicam track video fx.
     * 删除轨道特效
     *
     * @param inPoint the in point inPoint
     * @return the meicam track video fx 特效
     */
    public MeicamTrackVideoFx removeTrackVideoFx(long inPoint) {
        if (!CommonUtils.isEmpty(trackVideoFxList)) {
            for (int index = trackVideoFxList.size() - 1; index >= 0; index--) {
                MeicamTrackVideoFx trackVideoFx = trackVideoFxList.get(index);
                if (inPoint == trackVideoFx.getInPoint()) {
                    NvsVideoTrack object = getObject();
                    NvsTrackVideoFx trackVideoFxObject = trackVideoFx.getObject();
                    if (object != null && trackVideoFxObject != null) {
                        object.removeTrackVideoFx(trackVideoFxObject);
                        return trackVideoFxList.remove(index);
                    }
                }
            }
        }
        return null;
    }

    public int getVideoFxCount(){
        return trackVideoFxList == null ? 0 : trackVideoFxList.size();
    }

    public MeicamTrackVideoFx getVideoFx(int index){
        if (!CommonUtils.isIndexAvailable(index, trackVideoFxList)) {
            return null;
        }
        return trackVideoFxList.get(index);
    }

    /**
     * Gets video fx.
     * 查询轨道video 特效
     *
     * @param inPoint the in point 特效入点
     * @return the video fx 特效
     */
    public MeicamTrackVideoFx getVideoFx(long inPoint) {
        if (!CommonUtils.isEmpty(trackVideoFxList)) {
            for (MeicamTrackVideoFx trackVideoFx : trackVideoFxList) {
                if (inPoint ==trackVideoFx.getInPoint()) {
                    return trackVideoFx;
                }
            }
        }
        return null;
    }

    /**
     * Gets video fx.
     * 查询轨道video 特效
     *
     * @param tag the tag 特效tag
     * @return the video fx 特效
     */
    public MeicamTrackVideoFx getVideoFx(String tag) {
        if (!CommonUtils.isEmpty(trackVideoFxList)) {
            for (MeicamTrackVideoFx trackVideoFx : trackVideoFxList) {
                if (TextUtils.equals(tag, trackVideoFx.getCreateTag())) {
                    return trackVideoFx;
                }
            }
        }
        return null;
    }
}
