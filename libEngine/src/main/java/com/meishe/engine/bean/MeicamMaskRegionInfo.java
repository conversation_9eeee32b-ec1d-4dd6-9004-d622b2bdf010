package com.meishe.engine.bean;

import androidx.annotation.NonNull;

import com.meicam.sdk.NvsMaskRegionInfo;
import com.meicam.sdk.NvsObject;
import com.meicam.sdk.NvsPosition2D;
import com.meicam.sdk.NvsVideoFx;
import com.meishe.engine.adapter.TimelineDataParserAdapter;
import com.meishe.engine.constant.NvsConstants;
import com.meishe.engine.local.LMeicamMaskRegionInfo;
import com.meishe.engine.local.LMeicamPosition2D;
import com.meishe.engine.util.DeepCopyUtil;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 * 版权所有:www.meishesdk.com
 *
 * <AUTHOR> LiHangZhou
 * @CreateDate :2021/05/24 10:07
 * @Description : 蒙版区域信息 The mask region info
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class MeicamMaskRegionInfo implements Serializable, Cloneable, TimelineDataParserAdapter<LMeicamMaskRegionInfo> {
    private List<RegionInfo> regionInfoArray = new ArrayList<>();

    public MeicamMaskRegionInfo() {
    }

    public List<RegionInfo> getLocalRegionInfoArray() {
        return regionInfoArray;
    }

    public void setRegionInfoArray(List<RegionInfo> regionInfoArray) {
        this.regionInfoArray = regionInfoArray;
    }

    public void addRegionInfo(MeicamMaskRegionInfo.RegionInfo regionInfo) {
        this.regionInfoArray.add(regionInfo);
    }

    public void removeRegionInfoByIndex(int index) {
        if (index >= 0 && index < this.regionInfoArray.size()) {
            this.regionInfoArray.remove(index);
        }
    }

    /**
     * 底层数据转上层数据
     * Sets region info.
     *
     * @param nvsMaskRegionInfo the nvs mask region info
     */
    public void setRegionInfo(NvsMaskRegionInfo nvsMaskRegionInfo) {
        if (nvsMaskRegionInfo != null) {
            List<NvsMaskRegionInfo.RegionInfo> regionInfoArray = nvsMaskRegionInfo.getRegionInfoArray();
            if (regionInfoArray != null) {
                for (NvsMaskRegionInfo.RegionInfo nvsRegionInfo : regionInfoArray) {
                    RegionInfo regionInfo = new RegionInfo(nvsRegionInfo.getType());
                    List<MeicamPosition2D> position2D = regionInfo.getPoints();
                    if (position2D.size() != 0) {
                        position2D.clear();
                    }
                    for (NvsPosition2D nvsPosition2D : nvsRegionInfo.getPoints()) {
                        position2D.add(new MeicamPosition2D(nvsPosition2D.x, nvsPosition2D.y));
                    }
                    NvsMaskRegionInfo.Ellipse2D nvsEllipse2D = nvsRegionInfo.getEllipse2D();
                    MeicamMaskRegionInfo.Ellipse2D meicamEllipse2D = regionInfo.getEllipse2D();
                    meicamEllipse2D.setA(nvsEllipse2D.getA());
                    meicamEllipse2D.setB(nvsEllipse2D.getB());
                    meicamEllipse2D.setTheta(nvsEllipse2D.getTheta());
                    meicamEllipse2D.setCenter(new MeicamPosition2D(nvsEllipse2D.getCenter().x, nvsEllipse2D.getCenter().y));

                    NvsMaskRegionInfo.Transform2D nvsTransform2D = nvsRegionInfo.getTransform2D();
                    MeicamMaskRegionInfo.Transform2D meicamTransform2D = regionInfo.getTransform2D();
                    meicamTransform2D.setAnchor(new MeicamPosition2D(nvsTransform2D.getAnchor().x, nvsTransform2D.getAnchor().y));
                    meicamTransform2D.setRotation(nvsTransform2D.getRotation());
                    meicamTransform2D.setScale(new MeicamPosition2D(nvsTransform2D.getScale().x, nvsTransform2D.getScale().y));
                    meicamTransform2D.setTranslation(new MeicamPosition2D(nvsTransform2D.getTranslation().x, nvsTransform2D.getTranslation().y));

                    NvsMaskRegionInfo.Mirror nvsMirror = new NvsMaskRegionInfo.Mirror();
                    MeicamMaskRegionInfo.Mirror meicamMirror = regionInfo.getMirror();
                    if (meicamMirror != null) {
                        meicamMirror.setTheta(nvsMirror.getTheta());
                        meicamMirror.setDistance(nvsMirror.getDistance());
                        if (nvsMirror.getCenter() != null) {
                            meicamMirror.setCenter(new MeicamPosition2D(nvsMirror.getCenter().x, nvsMirror.getCenter().y));
                        }
                    }
                    addRegionInfo(regionInfo);
                }
            }

        }
    }

    /**
     * 上层数据转底层数据
     * Gets mask region info.
     *
     * @return the mask region info
     */
    public NvsMaskRegionInfo getMaskRegionInfo() {
        NvsMaskRegionInfo nvsMaskRegionInfo = new NvsMaskRegionInfo();
        if (regionInfoArray != null && regionInfoArray.size() > 0) {
            setRegionInfoList(nvsMaskRegionInfo);
        }
        return nvsMaskRegionInfo;
    }

    private void setRegionInfoList(NvsMaskRegionInfo nvsMaskRegionInfo) {
        for (RegionInfo regionInfo : regionInfoArray) {
            NvsMaskRegionInfo.RegionInfo nvsRegionInfo = new NvsMaskRegionInfo.RegionInfo(regionInfo.getType());
            List<NvsPosition2D> nvsPosition2D = nvsRegionInfo.getPoints();
            if (nvsPosition2D.size() != 0) {
                nvsPosition2D.clear();
            }
            for (MeicamPosition2D meicamPosition2D : regionInfo.getPoints()) {
                nvsPosition2D.add(new NvsPosition2D(meicamPosition2D.x, meicamPosition2D.y));
            }

            NvsMaskRegionInfo.Ellipse2D nvsEllipse2D = nvsRegionInfo.getEllipse2D();
            MeicamMaskRegionInfo.Ellipse2D meicamEllipse2D = regionInfo.getEllipse2D();
            nvsEllipse2D.setA(meicamEllipse2D.getA());
            nvsEllipse2D.setB(meicamEllipse2D.getB());
            nvsEllipse2D.setTheta(meicamEllipse2D.getTheta());
            nvsEllipse2D.setCenter(new NvsPosition2D(meicamEllipse2D.getCenter().x, meicamEllipse2D.getCenter().y));

            NvsMaskRegionInfo.Transform2D nvsTransform2D = nvsRegionInfo.getTransform2D();
            MeicamMaskRegionInfo.Transform2D meicamTransform2D = regionInfo.getTransform2D();
            nvsTransform2D.setAnchor(new NvsPosition2D(meicamTransform2D.getAnchor().x, meicamTransform2D.getAnchor().y));
            nvsTransform2D.setRotation(meicamTransform2D.getRotation());
            nvsTransform2D.setScale(new NvsPosition2D(meicamTransform2D.getScale().x, meicamTransform2D.getScale().y));
            nvsTransform2D.setTranslation(new NvsPosition2D(meicamTransform2D.getTranslation().x, meicamTransform2D.getTranslation().y));

            NvsMaskRegionInfo.Mirror nvsMirror = nvsRegionInfo.getMirror();
            if (nvsMirror != null) {
                MeicamMaskRegionInfo.Mirror meicamMirror = regionInfo.getMirror();
                if (meicamMirror != null) {
                    nvsMirror.setTheta(meicamMirror.getTheta());
                    nvsMirror.setDistance(meicamMirror.getDistance());
                    MeicamPosition2D center = meicamMirror.getCenter();
                    if (center != null) {
                        nvsMirror.setCenter(new NvsPosition2D(center.x, center.y));
                    }
                }
            }
            nvsMaskRegionInfo.addRegionInfo(nvsRegionInfo);
        }
    }

    @NonNull
    @Override
    public MeicamMaskRegionInfo clone() {
        return (MeicamMaskRegionInfo) DeepCopyUtil.deepClone(this);
    }

    public static class RegionInfo implements Serializable, Cloneable, TimelineDataParserAdapter<LMeicamMaskRegionInfo.LRegionInfo> {
        private int type;
        private List<MeicamPosition2D> points = new ArrayList<>();
        private MeicamMaskRegionInfo.Mirror mirror = new MeicamMaskRegionInfo.Mirror();
        private MeicamMaskRegionInfo.Ellipse2D ellipse2d = new MeicamMaskRegionInfo.Ellipse2D();
        private MeicamMaskRegionInfo.Transform2D transform2d = new MeicamMaskRegionInfo.Transform2D();

        public RegionInfo(int var1) {
            this.type = var1;
        }

        public List<MeicamPosition2D> getPoints() {
            return this.points;
        }

        public MeicamMaskRegionInfo.Ellipse2D getEllipse2D() {
            return this.ellipse2d;
        }

        public MeicamMaskRegionInfo.Transform2D getTransform2D() {
            return this.transform2d;
        }

        public int getType() {
            return this.type;
        }

        public void setPoints(List<MeicamPosition2D> var1) {
            this.points = var1;
        }

        public void setEllipse2D(MeicamMaskRegionInfo.Ellipse2D var1) {
            this.ellipse2d = var1;
        }

        public void setTransform2D(MeicamMaskRegionInfo.Transform2D var1) {
            this.transform2d = var1;
        }

        public MeicamMaskRegionInfo.Mirror getMirror() {
            return mirror;
        }

        public void setMirror(MeicamMaskRegionInfo.Mirror mirror) {
            this.mirror = mirror;
        }

        @NonNull
        @Override
        public RegionInfo clone() {
            return (RegionInfo) DeepCopyUtil.deepClone(this);
        }

        @Override
        public LMeicamMaskRegionInfo.LRegionInfo parseToLocalData() {
            LMeicamMaskRegionInfo.LRegionInfo regionInfo = new LMeicamMaskRegionInfo.LRegionInfo(type);
            regionInfo.setEllipse2D(getEllipse2D().parseToLocalData());
            regionInfo.setTransform2D(getTransform2D().parseToLocalData());
            regionInfo.setMirror(getMirror().parseToLocalData());
            for (MeicamPosition2D nvsPosition2D : points) {
                regionInfo.getPoints().add(nvsPosition2D.parseToLocalData());
            }
            return regionInfo;
        }

        @Override
        public void recoverFromLocalData(LMeicamMaskRegionInfo.LRegionInfo lRegionInfo) {
            if (lRegionInfo.getTransform2D() != null) {
                transform2d.recoverFromLocalData(lRegionInfo.getTransform2D());
            }
            if (lRegionInfo.getEllipse2D() != null) {
                ellipse2d.recoverFromLocalData(lRegionInfo.getEllipse2D());
            }
            if (lRegionInfo.getMirror() != null) {
                mirror.recoverFromLocalData(lRegionInfo.getMirror());
            }
            List<LMeicamPosition2D> pointList = lRegionInfo.getPoints();
            if (pointList != null) {
                for (LMeicamPosition2D lPosition2D : pointList) {
                    points.add(new MeicamPosition2D(lPosition2D.x, lPosition2D.y));
                }
            }
        }

        @Override
        public void recoverFromTimelineData(NvsObject nvsObject) {

        }
    }

    public static class Mirror implements Serializable, Cloneable, TimelineDataParserAdapter<LMeicamMaskRegionInfo.LMirror> {
        private MeicamPosition2D center;
        private float distance;
        private float theta;

        public Mirror() {
            this.center = new MeicamPosition2D(0.0F, 0.0F);
        }

        public MeicamPosition2D getCenter() {
            return center;
        }

        public void setCenter(MeicamPosition2D center) {
            this.center = center;
        }

        public float getDistance() {
            return distance;
        }

        public void setDistance(float distance) {
            this.distance = distance;
        }

        public float getTheta() {
            return theta;
        }

        public void setTheta(float theta) {
            this.theta = theta;
        }

        @Override
        public LMeicamMaskRegionInfo.LMirror parseToLocalData() {
            LMeicamMaskRegionInfo.LMirror lMirror = new LMeicamMaskRegionInfo.LMirror();
            lMirror.setDistance(distance);
            lMirror.setTheta(theta);
            lMirror.setCenter(center.parseToLocalData());
            return lMirror;
        }

        @Override
        public void recoverFromLocalData(LMeicamMaskRegionInfo.LMirror lMirror) {
            setDistance(lMirror.getDistance());
            LMeicamPosition2D lCenter = lMirror.getCenter();
            if (lCenter != null) {
                if (center != null) {
                    center.recoverFromLocalData(lCenter);
                }
            }
            setTheta(lMirror.getTheta());
        }

        @Override
        public void recoverFromTimelineData(NvsObject nvsObject) {

        }

        @NonNull
        @Override
        public Mirror clone() {
            return (Mirror) DeepCopyUtil.deepClone(this);
        }
    }

    public static class Ellipse2D implements Serializable, Cloneable, TimelineDataParserAdapter<LMeicamMaskRegionInfo.LEllipse2D> {
        private MeicamPosition2D center;
        private float a;
        private float b;
        private float theta;

        public Ellipse2D() {
            this.center = new MeicamPosition2D(0.0F, 0.0F);
            this.a = this.b = this.theta = 0.0F;
        }

        public Ellipse2D(MeicamPosition2D var1, float var2, float var3, float var4) {
            this.center = var1;
            this.a = var2;
            this.b = var3;
            this.theta = var4;
        }

        public MeicamPosition2D getCenter() {
            return this.center;
        }

        public float getA() {
            return this.a;
        }

        public float getB() {
            return this.b;
        }

        public float getTheta() {
            return this.theta;
        }

        public void setCenter(MeicamPosition2D var1) {
            this.center = var1;
        }

        public void setA(float var1) {
            this.a = var1;
        }

        public void setB(float var1) {
            this.b = var1;
        }

        public void setTheta(float var1) {
            this.theta = var1;
        }

        @NonNull
        @Override
        public Ellipse2D clone() {
            return (Ellipse2D) DeepCopyUtil.deepClone(this);
        }

        @Override
        public LMeicamMaskRegionInfo.LEllipse2D parseToLocalData() {
            LMeicamMaskRegionInfo.LEllipse2D ellipse2D = new LMeicamMaskRegionInfo.LEllipse2D();
            ellipse2D.setA(getA());
            ellipse2D.setB(getB());
            ellipse2D.setCenter(getCenter().parseToLocalData());
            ellipse2D.setTheta(getTheta());
            return ellipse2D;
        }

        @Override
        public void recoverFromLocalData(LMeicamMaskRegionInfo.LEllipse2D lEllipse2D) {
            setA(lEllipse2D.getA());
            setB(lEllipse2D.getB());
            setTheta(lEllipse2D.getTheta());
            LMeicamPosition2D lCenter = lEllipse2D.getCenter();
            if (lCenter != null) {
                center.recoverFromLocalData(lCenter);
            }
        }

        @Override
        public void recoverFromTimelineData(NvsObject nvsObject) {

        }
    }

    public static class Transform2D implements Serializable, Cloneable, TimelineDataParserAdapter<LMeicamMaskRegionInfo.LTransform2D> {
        private MeicamPosition2D anchor;
        private MeicamPosition2D scale;
        private float rotation;
        private MeicamPosition2D translation;

        public Transform2D() {
            this.anchor = new MeicamPosition2D(0.0F, 0.0F);
            this.scale = new MeicamPosition2D(1.0F, 1.0F);
            this.rotation = 0.0F;
            this.translation = new MeicamPosition2D(0.0F, 0.0F);
        }

        public Transform2D(MeicamPosition2D var1, MeicamPosition2D var2, float var3, MeicamPosition2D var4) {
            this.anchor = var1;
            this.scale = var2;
            this.rotation = var3;
            this.translation = var4;
        }

        public MeicamPosition2D getAnchor() {
            return this.anchor;
        }

        public MeicamPosition2D getScale() {
            return this.scale;
        }

        public float getRotation() {
            return this.rotation;
        }

        public MeicamPosition2D getTranslation() {
            return this.translation;
        }

        public void setAnchor(MeicamPosition2D var1) {
            this.anchor = var1;
        }

        public void setRotation(float var1) {
            this.rotation = var1;
        }

        public void setScale(MeicamPosition2D var1) {
            this.scale = var1;
        }

        public void setTranslation(MeicamPosition2D var1) {
            this.translation = var1;
        }

        @NonNull
        @Override
        public Transform2D clone() {
            return (Transform2D) DeepCopyUtil.deepClone(this);
        }

        @Override
        public LMeicamMaskRegionInfo.LTransform2D parseToLocalData() {
            LMeicamMaskRegionInfo.LTransform2D transform2D = new LMeicamMaskRegionInfo.LTransform2D();
            transform2D.setAnchor(getAnchor().parseToLocalData());
            transform2D.setScale(getScale().parseToLocalData());
            transform2D.setTranslation(getTranslation().parseToLocalData());
            transform2D.setRotation(getRotation());
            return transform2D;
        }


        @Override
        public void recoverFromLocalData(LMeicamMaskRegionInfo.LTransform2D local) {
            LMeicamPosition2D anchor = local.getAnchor();
            if (anchor != null) {
                setAnchor(new MeicamPosition2D(anchor.x, anchor.y));
            }
            LMeicamPosition2D scale = local.getScale();
            if (scale != null) {
                setScale(new MeicamPosition2D(scale.x, scale.y));
            }
            LMeicamPosition2D translation = local.getTranslation();
            if (translation != null) {
                setTranslation(new MeicamPosition2D(translation.x, translation.y));
            }
            setRotation(local.getRotation());
        }

        @Override
        public void recoverFromTimelineData(NvsObject nvsObject) {

        }
    }

    @Override
    public LMeicamMaskRegionInfo parseToLocalData() {
        LMeicamMaskRegionInfo meicamNvsMaskRegionInfo = new LMeicamMaskRegionInfo();
        for (MeicamMaskRegionInfo.RegionInfo regionInfo : regionInfoArray) {
            meicamNvsMaskRegionInfo.getMeicamRegionInfoArray().add(regionInfo.parseToLocalData());
        }
        return meicamNvsMaskRegionInfo;
    }

    @Override
    public void recoverFromLocalData(LMeicamMaskRegionInfo lMaskRegionInfo) {
        List<LMeicamMaskRegionInfo.LRegionInfo> lRegionInfoList = lMaskRegionInfo.getMeicamRegionInfoArray();
        if (lRegionInfoList != null) {
            for (LMeicamMaskRegionInfo.LRegionInfo lRegionInfo : lRegionInfoList) {
                RegionInfo regionInfo = new RegionInfo(lRegionInfo.getType());
                regionInfo.recoverFromLocalData(lRegionInfo);
                regionInfoArray.add(regionInfo);
            }

        }
    }

    @Override
    public void recoverFromTimelineData(NvsObject nvsObject) {
        if (nvsObject instanceof NvsVideoFx) {
            NvsVideoFx nvsVideoFx = (NvsVideoFx) nvsObject;
            NvsMaskRegionInfo nvsMaskRegionInfo = (NvsMaskRegionInfo) nvsVideoFx.getArbDataVal(NvsConstants.KEY_MASK_REGION_INFO);
            setRegionInfo(nvsMaskRegionInfo);
//            List<NvsMaskRegionInfo.RegionInfo> regionInfos = nvsMaskRegionInfo.getRegionInfoArray();
//            if (regionInfos != null) {
//                for (NvsMaskRegionInfo.RegionInfo nvsRegionInfo : regionInfos) {
//                    RegionInfo regionInfo = new RegionInfo(nvsRegionInfo.getType());
//                    NvsMaskRegionInfo.Transform2D transform2D = nvsRegionInfo.getTransform2D();
//                    if (transform2D != null) {
//                        regionInfo.transform2d = getTransform2d(transform2D);
//                    }
//
//                    NvsMaskRegionInfo.Ellipse2D ellipse2D = nvsRegionInfo.getEllipse2D();
//                    if (ellipse2D != null) {
//                        regionInfo.ellipse2d = getEllipse2d(ellipse2D);
//                    }
//
//                    List<NvsPosition2D> pointList = nvsRegionInfo.getPoints();
//                    if (pointList != null) {
//                        for (NvsPosition2D nvsPosition2D : pointList) {
//                            regionInfo.points.add(new MeicamPosition2D(nvsPosition2D.x, nvsPosition2D.y));
//                        }
//                    }
//                    regionInfoArray.add(regionInfo);
//                }
//            }
        }
    }

//    private Ellipse2D getEllipse2d(NvsMaskRegionInfo.Ellipse2D ellipse2D) {
//        MeicamMaskRegionInfo.Ellipse2D meicamEllipse2D = new MeicamMaskRegionInfo.Ellipse2D();
//        meicamEllipse2D.setA(ellipse2D.getA());
//        meicamEllipse2D.setB(ellipse2D.getB());
//        meicamEllipse2D.setTheta(ellipse2D.getTheta());
//        NvsPosition2D center = ellipse2D.getCenter();
//        meicamEllipse2D.setCenter(new MeicamPosition2D(center.x, center.y));
//        return meicamEllipse2D;
//    }
//
//    private Transform2D getTransform2d(NvsMaskRegionInfo.Transform2D transform2D) {
//        MeicamMaskRegionInfo.Transform2D meicamTransform2D = new MeicamMaskRegionInfo.Transform2D();
//        NvsPosition2D anchor = transform2D.getAnchor();
//        if (anchor != null) {
//            meicamTransform2D.setAnchor(new MeicamPosition2D(anchor.x, anchor.y));
//        }
//        NvsPosition2D scale = transform2D.getScale();
//        if (scale != null) {
//            meicamTransform2D.setScale(new MeicamPosition2D(scale.x, scale.y));
//        }
//        NvsPosition2D translation = transform2D.getTranslation();
//        if (translation != null) {
//            meicamTransform2D.setTranslation(new MeicamPosition2D(translation.x, translation.y));
//        }
//        meicamTransform2D.setRotation(transform2D.getRotation());
//        return meicamTransform2D;
//    }
}
