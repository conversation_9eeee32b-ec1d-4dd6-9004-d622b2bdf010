package com.meishe.engine.bean;

import com.meicam.sdk.NvsVideoFx;
import com.meishe.engine.local.LMeicamVideoFx;
import com.meishe.engine.local.background.LMeicamBackgroundStory;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * author：yangtailin on 2020/7/10 20:38
 */
public class MeicamBackgroundStory extends MeicamStoryboardInfo {
    private int backgroundType;

    MeicamBackgroundStory(NvsVideoFx videoFx, String type, String desc) {
        super(videoFx, type, MeicamStoryboardInfo.SUB_TYPE_BACKGROUND, desc);
        classType = "BackgroundStory";
    }
    
    public int getBackgroundType() {
        return backgroundType;
    }

    public void setBackgroundType(int backgroundType) {
        this.backgroundType = backgroundType;
    }

    @Override
    public LMeicamBackgroundStory parseToLocalData() {
        LMeicamBackgroundStory local = new LMeicamBackgroundStory();
        setCommonData(local);
        local.setStoryDesc(getStoryDesc());
        local.setSource(getSource());
        local.setSourceDir(getSourceDir());
        Map<String, Float> localClipTrans = new HashMap<>();
        Map<String, Float> clipTrans = getClipTrans();
        Set<String> keySet = clipTrans.keySet();
        for (String key : keySet) {
            Float value = clipTrans.get(key);
            if (value == null || Float.isNaN(value)) {
                continue;
            }
            localClipTrans.put(key, value);
        }
        local.setClipTrans(localClipTrans);
        local.setBackgroundType(getBackgroundType());
        return local;
    }


    @Override
    public void recoverFromLocalData(LMeicamVideoFx lMeicamVideoFx) {
        super.recoverFromLocalData(lMeicamVideoFx);
        LMeicamBackgroundStory local = (LMeicamBackgroundStory) lMeicamVideoFx;
        setCommonRecoverData(local);
        setBackgroundType(local.getBackgroundType());
    }
}
