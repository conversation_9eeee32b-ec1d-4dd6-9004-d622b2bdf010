package com.meishe.engine.bean;

import android.graphics.PointF;
import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.meicam.sdk.NvsClipCompoundCaption;
import com.meicam.sdk.NvsColor;
import com.meicam.sdk.NvsCompoundCaption;
import com.meicam.sdk.NvsObject;
import com.meicam.sdk.NvsTimelineCompoundCaption;
import com.meicam.sdk.NvsTrackCompoundCaption;
import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.LogUtils;
import com.meishe.engine.adapter.TimelineDataParserAdapter;
import com.meishe.engine.interf.IKeyFrameProcessor;
import com.meishe.engine.local.LMeicamCompoundCaptionClip;
import com.meishe.engine.local.LMeicamCompoundCaptionItem;
import com.meishe.engine.util.ColorUtil;
import com.meishe.engine.util.DeepCopyUtil;
import com.meishe.engine.util.gson.GsonContext;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * All rights Reserved, Designed By www.meishesdk.com
 *
 * @Author: ChuChenGuang
 * @CreateDate: 2021/5/20 10:30
 * @Description: 组合字幕 The compound caption clip
 * @Copyright: www.meishesdk.com Inc. All rights reserved.
 */
public class MeicamCompoundCaptionClip extends ClipInfo<NvsCompoundCaption> implements Cloneable, Serializable, TimelineDataParserAdapter<LMeicamCompoundCaptionClip>, IKeyFrameProcessor<NvsCompoundCaption> {
    /**
     * Subtitle list
     * 子字幕列表
     */
    private List<MeicamCompoundCaptionItem> compoundCaptionItems = new ArrayList<>();
    /**
     * style
     * 样式
     */
    private String styleDesc;
    /**
     * scale X
     * 缩放 X
     */
    private float scaleX = 1;
    /**
     * scale Y
     * 缩放 Y
     */
    private float scaleY = 1;

    /**
     * Rotation Angle
     * 旋转角度
     */
    private float rotation = 0;
    /**
     * Horizontal translation
     * 水平方向平移
     */
    private float translationX = 0;
    /**
     * vertical translation
     * 竖直方向平移
     */
    private float translationY = 0;
    /**
     * Text used to update subtitles
     * 用于更新子字幕的文字
     */
    private int itemSelectedIndex = 0;

    /**
     * The anchor
     * 锚点
     */
    private PointF assetAnchor;

    MeicamCompoundCaptionClip(NvsCompoundCaption caption, long inPoint, long outPoint, String style) {
        super(caption, CommonData.CLIP_COMPOUND_CAPTION);
        this.setInPoint(inPoint);
        this.setOutPoint(outPoint);
        this.styleDesc = style;
    }

    MeicamCompoundCaptionClip(NvsCompoundCaption comCaption) {
        super(comCaption, CommonData.CLIP_COMPOUND_CAPTION);
        if (comCaption != null) {
            if (comCaption instanceof NvsTimelineCompoundCaption) {
                NvsTimelineCompoundCaption nvsTimelineCompoundCaption = (NvsTimelineCompoundCaption) comCaption;
                this.inPoint = nvsTimelineCompoundCaption.getInPoint();
                this.outPoint = nvsTimelineCompoundCaption.getOutPoint();
                this.styleDesc = nvsTimelineCompoundCaption.getCaptionStylePackageId();
            } else if (comCaption instanceof NvsTrackCompoundCaption) {
                NvsTrackCompoundCaption nvsTrackCompoundCaption = (NvsTrackCompoundCaption) comCaption;
                this.inPoint = nvsTrackCompoundCaption.getInPoint();
                this.outPoint = nvsTrackCompoundCaption.getOutPoint();
                this.styleDesc = nvsTrackCompoundCaption.getCaptionStylePackageId();
            } else if (comCaption instanceof NvsClipCompoundCaption) {
                NvsClipCompoundCaption nvsClipCaption = (NvsClipCompoundCaption) comCaption;
                this.inPoint = nvsClipCaption.getInPoint();
                this.outPoint = nvsClipCaption.getOutPoint();
                this.styleDesc = nvsClipCaption.getCaptionStylePackageId();
            }

        }
    }

    @Override
    public float getZValue() {
        NvsCompoundCaption object = getObject();
        if (object != null) {
            return object.getZValue();
        }
        return super.getZValue();
    }

    @Override
    public void setZValue(float zValue) {
        if (invalidFloat(zValue)) {
            return;
        }
        NvsCompoundCaption object = getObject();
        if (object != null) {
            object.setZValue(zValue);
            super.setZValue(zValue);
        }
    }

    @Override
    public void setInPoint(long inPoint) {
        NvsCompoundCaption object = getObject();
        if (object != null) {
            if (object instanceof NvsTimelineCompoundCaption) {
                NvsTimelineCompoundCaption nvsTimelineCompoundCaption = (NvsTimelineCompoundCaption) object;
                nvsTimelineCompoundCaption.changeInPoint(inPoint);
            } else if (object instanceof NvsTrackCompoundCaption) {
                NvsTrackCompoundCaption nvsTrackCompoundCaption = (NvsTrackCompoundCaption) object;
                nvsTrackCompoundCaption.changeInPoint(inPoint);
            } else if (object instanceof NvsClipCompoundCaption) {
                NvsClipCompoundCaption nvsClipCaption = (NvsClipCompoundCaption) object;
                nvsClipCaption.changeInPoint(inPoint);
            }
            this.inPoint = inPoint;
        }
    }

    @Override
    public void setOutPoint(long outPoint) {
        NvsCompoundCaption object = getObject();
        if (object != null) {
            if (object instanceof NvsTimelineCompoundCaption) {
                NvsTimelineCompoundCaption nvsTimelineCompoundCaption = (NvsTimelineCompoundCaption) object;
                nvsTimelineCompoundCaption.changeOutPoint(outPoint);
            } else if (object instanceof NvsTrackCompoundCaption) {
                NvsTrackCompoundCaption nvsTrackCompoundCaption = (NvsTrackCompoundCaption) object;
                nvsTrackCompoundCaption.changeOutPoint(outPoint);
            } else if (object instanceof NvsClipCompoundCaption) {
                NvsClipCompoundCaption nvsClipCaption = (NvsClipCompoundCaption) object;
                nvsClipCaption.changeOutPoint(outPoint);
            }
            this.outPoint = outPoint;
        }
    }

    List<MeicamCompoundCaptionItem> getCompoundCaptionItems() {
        return compoundCaptionItems;
    }


    public String getStyleDesc() {
        return styleDesc;
    }

    public void setStyleDesc(String styleDesc) {
        this.styleDesc = styleDesc;
    }

    public float getScaleX() {
        return scaleX;
    }

    public void setScaleX(float scaleX) {
        if (invalidFloat(scaleX)) {
            return;
        }
        NvsCompoundCaption object = getObject();
        if (object != null) {
            object.setScaleX(scaleX);
            this.scaleX = scaleX;
        }
    }

    public float getScaleY() {
        return scaleY;
    }

    public void setScaleY(float scaleY) {
        if (invalidFloat(scaleY)) {
            return;
        }
        NvsCompoundCaption object = getObject();
        if (object != null) {
            object.setScaleX(scaleY);
            this.scaleY = scaleY;
        }
    }

    public float getRotation() {
        return rotation;
    }

    public void setRotation(float rotation) {
        if (invalidFloat(rotation)) {
            return;
        }
        NvsCompoundCaption object = getObject();
        if (object != null) {
            object.setRotationZ(rotation);
            this.rotation = rotation;
        }
    }

    public float getTranslationX() {
        return translationX;
    }

    public void setTranslationX(float translationX) {
        if (invalidFloat(translationX)) {
            return;
        }
        NvsCompoundCaption object = getObject();
        if (object != null) {
            object.setCaptionTranslation(new PointF(translationX, getTranslationY()));
            this.translationX = translationX;
        }

    }

    public float getTranslationY() {
        return translationY;
    }

    public void setTranslationY(float translationY) {
        if (invalidFloat(translationY)) {
            return;
        }
        NvsCompoundCaption object = getObject();
        if (object != null) {
            object.setCaptionTranslation(new PointF(getTranslationX(), translationY));
            this.translationY = translationY;
        }
    }

    public int getItemSelectedIndex() {
        return itemSelectedIndex;
    }

    public void setItemSelectedIndex(int index) {
        this.itemSelectedIndex = index;
    }

    public void setFontFamily(int captionIndex, String fontName) {
        NvsCompoundCaption object = getObject();
        if (object != null) {
            object.setFontFamily(captionIndex, fontName);
            compoundCaptionItems.get(captionIndex).setFont(fontName);
        }
    }

    public String getFontFamily(int captionIndex) {
        NvsCompoundCaption object = getObject();
        if (object != null) {
            return object.getFontFamily(captionIndex);
        }
        return "";
    }


    public PointF getAssetAnchor() {
        return assetAnchor;
    }

    public void setAssetAnchor(PointF assetAnchor) {
        NvsCompoundCaption object = getObject();
        if (object != null) {
            object.setAnchorPoint(assetAnchor);
        }
        this.assetAnchor = assetAnchor;
    }

    /**
     * Gets the vertex position of the original border of the composite caption after transformation.
     * Return List&lt;PointF&gt;Object that contains four vertex positions corresponding,
     * in turn, to the top left, bottom left, bottom right, and top right vertices of the original border
     *
     * 获取复合字幕原始边框变换后的顶点位置
     *
     *
     * @param boundingTypeFrame the bounding type frame 包边类型
     * @return the compound bounding vertices 返回List<PointF>对象，包含四个顶点位置，依次分别对应原始边框的左上，左下，右下，右上顶点
     */
    public List<PointF> getCompoundBoundingVertices(int boundingTypeFrame) {
        NvsCompoundCaption object = getObject();
        if (object != null) {
            return object.getCompoundBoundingVertices(boundingTypeFrame);
        }
        return null;
    }

    /**
     * 获取组合字幕里的文字
     * Gets text.
     *
     * @param captionIndex the caption index
     * @return the text
     */
    public String getText(int captionIndex) {
        NvsCompoundCaption object = getObject();
        if (object != null) {
            return object.getText(captionIndex);
        }
        return "";
    }

    public String getTextColor(int captionIndex) {
        NvsCompoundCaption object = getObject();
        if (object != null) {
            NvsColor textColor = object.getTextColor(captionIndex);
            if (textColor != null) {
                return ColorUtil.nvsColorToHexString(textColor);
            }
        }
        return null;
    }

    /**
     * Get caption item count
     * <p>
     * 获取子字幕的个数
     *
     * @return the caption item count 子字幕的数量
     */
    public int getCaptionItemCount() {
        return compoundCaptionItems.size();
    }

    /**
     * 获取组合字幕，子字幕的边界
     * Gets caption bounding vertices.
     *
     * @param index            the index
     * @param boundingTypeText the bounding type text
     * @return the caption bounding vertices
     */
    public List<PointF> getCaptionBoundingVertices(int index, int boundingTypeText) {
        NvsCompoundCaption object = getObject();
        if (object != null) {
            return object.getCaptionBoundingVertices(index, boundingTypeText);
        }
        return null;
    }

    /**
     * Translate caption.
     * 组合字幕旋转
     *
     * @param timelinePoint the timeline point
     */
    public void translateCaption(PointF timelinePoint, PointF anchor) {
        if (timelinePoint == null || invalidFloat(timelinePoint.x) || invalidFloat(timelinePoint.y)) {
            LogUtils.e("param is error");
            return;
        }
        NvsCompoundCaption object = getObject();
        if (object != null) {
            object.translateCaption(timelinePoint);
            assetAnchor = anchor;
            translationX += timelinePoint.x;
            translationY += timelinePoint.y;
        }
    }

    /**
     * Sets text color.
     * 设置文字颜色
     *
     * @param captionIndex the caption index 字幕索引
     * @param nvsColor     the nvs color
     */
    public void setTextColor(int captionIndex, NvsColor nvsColor) {
        NvsCompoundCaption object = getObject();
        if (object != null) {
            object.setTextColor(captionIndex, nvsColor);
            MeicamCompoundCaptionItem meicamCompoundCaptionItem = compoundCaptionItems.get(captionIndex);
            if (meicamCompoundCaptionItem != null) {
                meicamCompoundCaptionItem.setTextColor(ColorUtil.getColorArray(nvsColor));
            }
        }

    }

    /**
     * Sets text.
     * 设置字幕颜色
     *
     * @param captionIndex the caption index 字幕索引
     * @param text         the text
     */
    public void setText(int captionIndex, String text) {
        NvsCompoundCaption object = getObject();
        if (object != null) {
            object.setText(captionIndex, text);
            MeicamCompoundCaptionItem meicamCompoundCaptionItem = compoundCaptionItems.get(captionIndex);
            if (meicamCompoundCaptionItem != null) {
                meicamCompoundCaptionItem.setText(text);
            }
        }

    }

    /**
     * 组合字幕缩放
     * Scale caption.
     *
     * @param scaleFactor the scale factor
     * @param anchor      the anchor
     */
    public void scaleCaption(float scaleFactor, PointF anchor) {
        NvsCompoundCaption object = getObject();
        if (object != null) {
            assetAnchor = anchor;
            object.scaleCaption(scaleFactor, anchor);
            scaleX = object.getScaleX();
            scaleY = object.getScaleY();

        }
    }

    /**
     * 组合字幕旋转
     * Rotate caption.
     *
     * @param angle  the angle
     * @param anchor the  anchor
     */
    public void rotateCaption(float angle, PointF anchor) {

        NvsCompoundCaption object = getObject();
        if (object != null) {
            assetAnchor = anchor;
            object.rotateCaption(angle, anchor);
            rotation = object.getRotationZ();
        }
    }

    /**
     * Gets caption item.
     * 获取子字幕上层数据
     *
     * @param captionIndex the caption index 字幕索引
     * @return the caption item
     */
    public MeicamCompoundCaptionItem getCaptionItem(int captionIndex) {
        if (CommonUtils.isIndexAvailable(captionIndex, compoundCaptionItems)) {
            return compoundCaptionItems.get(captionIndex);
        }
        return null;
    }

    @Override
    public void loadData() {
        NvsCompoundCaption comCaption = getObject();
        if (comCaption == null) {
            return;
        }
        setObject(comCaption);

        if (comCaption instanceof NvsTimelineCompoundCaption) {
            NvsTimelineCompoundCaption nvsTimelineCompoundCaption = (NvsTimelineCompoundCaption) comCaption;
            setInPoint(nvsTimelineCompoundCaption.getInPoint());
            setOutPoint(nvsTimelineCompoundCaption.getOutPoint());
        } else if (comCaption instanceof NvsTrackCompoundCaption) {
            NvsTrackCompoundCaption nvsTrackCompoundCaption = (NvsTrackCompoundCaption) comCaption;
            setInPoint(nvsTrackCompoundCaption.getInPoint());
            setOutPoint(nvsTrackCompoundCaption.getOutPoint());
        } else if (comCaption instanceof NvsClipCompoundCaption) {
            NvsClipCompoundCaption nvsClipCaption = (NvsClipCompoundCaption) comCaption;
            setInPoint(nvsClipCaption.getInPoint());
            setOutPoint(nvsClipCaption.getOutPoint());
        }


        int captionCount = comCaption.getCaptionCount();
        compoundCaptionItems.clear();
        for (int index = 0; index < captionCount; index++) {
            MeicamCompoundCaptionItem meicamCompoundCaptionItem = new MeicamCompoundCaptionItem(index, comCaption.getText(index));
            meicamCompoundCaptionItem.recoverFromTimelineData(comCaption);
            compoundCaptionItems.add(meicamCompoundCaptionItem);
        }
        PointF point = comCaption.getCaptionTranslation();
        if (point != null) {
            translationX = point.x;
            translationY = point.y;
        }
        scaleX = comCaption.getScaleX();
        scaleY = comCaption.getScaleY();
        rotation = comCaption.getRotationZ();
        zValue = comCaption.getZValue();
        assetAnchor = comCaption.getAnchorPoint();
    }


    boolean bindToTimeline() {
        NvsCompoundCaption newCaption = getObject();
        if (newCaption == null) {
            return false;
        }
        if (newCaption instanceof NvsTimelineCompoundCaption) {
            NvsTimelineCompoundCaption nvsTimelineCompoundCaption = (NvsTimelineCompoundCaption) newCaption;
            nvsTimelineCompoundCaption.setClipAffinityEnabled(false);
        } else if (newCaption instanceof NvsTrackCompoundCaption) {
            NvsTrackCompoundCaption nvsTrackCompoundCaption = (NvsTrackCompoundCaption) newCaption;
            nvsTrackCompoundCaption.setClipAffinityEnabled(false);
        } else if (newCaption instanceof NvsClipCompoundCaption) {

        }

        List<MeicamCompoundCaptionItem> captionAttrList = getCompoundCaptionItems();
        int captionCount = newCaption.getCaptionCount();
        for (int index = 0; index < captionCount; ++index) {
            if (index >= captionAttrList.size()) {
                break;
            }
            MeicamCompoundCaptionItem compoundCaptionItem = captionAttrList.get(index);
            if (compoundCaptionItem == null) {
                continue;
            }
            NvsColor textColor = ColorUtil.colorFloatToNvsColor(compoundCaptionItem.getTextColor());
            newCaption.setTextColor(index, textColor);

            String fontName = compoundCaptionItem.getFont();
            if (!TextUtils.isEmpty(fontName)) {
                newCaption.setFontFamily(index, fontName);
            }
            String captionText = compoundCaptionItem.getText();
            if (!TextUtils.isEmpty(captionText)) {
                newCaption.setText(index, captionText);
            }
        }
        newCaption.setZValue(getZValue());
        /*
         * 放缩字幕
         * Shrink captions
         * */
//        newCaption.setScaleX(getScaleX());
//        newCaption.setScaleY(getScaleY());

        /*
         * 旋转字幕
         * Spin subtitles
         * */
//        newCaption.setRotationZ(getRotation());

        PointF translation = new PointF(getTranslationX(), getTranslationY());
        newCaption.translateCaption(translation);
        newCaption.scaleCaption(getScaleX(), getAssetAnchor());
        newCaption.rotateCaption(getRotation(), getAssetAnchor());
        return true;
    }


    @NonNull
    @Override
    public Object clone() {
        return clone(true);
    }

    /**
     * 复制，
     * Clone
     *
     * @param changeCreateTag true change the create tag,false not
     * @return the MeicamCaptionClip
     */
    @Override
    public Object clone(boolean changeCreateTag) {
        Object clone = DeepCopyUtil.deepClone(this);
        if (clone == null) {
            String cloneString = GsonContext.getInstance().toJson(this);
            if (!TextUtils.isEmpty(cloneString)) {
                clone = GsonContext.getInstance().fromJson(cloneString, this.getClass());
            }
        }
        if (changeCreateTag && clone instanceof ClipInfo) {
            ((ClipInfo<?>) clone).generateCreateTag();
        }
        if (clone == null) {
            clone = new MeicamCompoundCaptionClip(null);
        }
        return clone;
    }

    @Override
    public LMeicamCompoundCaptionClip parseToLocalData() {
        LMeicamCompoundCaptionClip local = new LMeicamCompoundCaptionClip(styleDesc);
        setCommonData(local);
        local.setStyleDesc(getStyleDesc());
        local.setScaleX(getScaleX());
        local.setScaleY(getScaleY());
        local.setzValue(getZValue());
        local.setRotation(getRotation());
        local.setTranslationX(getTranslationX());
        local.setTranslationY(getTranslationY());
        local.setItemSelectedIndex(getItemSelectedIndex());
        local.setAssetAnchor(getAssetAnchor());
        for (MeicamCompoundCaptionItem compoundCaptionItem : compoundCaptionItems) {
            local.getCompoundCaptionItems().add(compoundCaptionItem.parseToLocalData());
        }
        return local;
    }

    @Override
    public void recoverFromLocalData(LMeicamCompoundCaptionClip lMeicamCompoundCaptionClip) {

        setZValue(lMeicamCompoundCaptionClip.getzValue());
        setItemSelectedIndex(lMeicamCompoundCaptionClip.getItemSelectedIndex());
//        setScaleX(lMeicamCompoundCaptionClip.getScaleX());
//        setScaleY(lMeicamCompoundCaptionClip.getScaleY());
//        setRotation(lMeicamCompoundCaptionClip.getRotation());
//        setTranslationX(lMeicamCompoundCaptionClip.getTranslationX());
//        setTranslationY(lMeicamCompoundCaptionClip.getTranslationY());
        setZValue(lMeicamCompoundCaptionClip.getzValue());
        PointF assetAnchor = lMeicamCompoundCaptionClip.getAssetAnchor();
        translateCaption(new PointF(lMeicamCompoundCaptionClip.getTranslationX(), lMeicamCompoundCaptionClip.getTranslationY()), assetAnchor);
        scaleCaption(lMeicamCompoundCaptionClip.getScaleX(), assetAnchor);
        rotateCaption(lMeicamCompoundCaptionClip.getRotation(), assetAnchor);


        NvsCompoundCaption nvsTimelineCompoundCaption = getObject();
        if (nvsTimelineCompoundCaption == null) {
            return;
        }
        List<LMeicamCompoundCaptionItem> captionAttrList = lMeicamCompoundCaptionClip.getCompoundCaptionItems();
        int captionCount = nvsTimelineCompoundCaption.getCaptionCount();
        for (int index = 0; index < captionCount; ++index) {
            if (index >= captionAttrList.size()) {
                break;
            }
            LMeicamCompoundCaptionItem compoundCaptionItem = captionAttrList.get(index);
            if (compoundCaptionItem == null) {
                continue;
            }
            NvsColor textColor = ColorUtil.colorFloatToNvsColor(compoundCaptionItem.getTextColor());
            setTextColor(index, textColor);

            String fontName = compoundCaptionItem.getFont();
            if (!TextUtils.isEmpty(fontName)) {
                setFontFamily(index, fontName);
            }
            String captionText = compoundCaptionItem.getText();
            if (!TextUtils.isEmpty(captionText)) {
                setText(index, captionText);
            }
        }

        if (!CommonUtils.isEmpty(compoundCaptionItems)) {
            for (int i = 0; i < captionAttrList.size(); i++) {
                if (CommonUtils.isIndexAvailable(i, compoundCaptionItems) && CommonUtils.isIndexAvailable(i, captionAttrList)) {
                    compoundCaptionItems.get(i).recoverFromLocalData(captionAttrList.get(i));
                }
            }
        }
        keyFrameProcessor().recoverFromLocalData(lMeicamCompoundCaptionClip.getKeyFrameProcessor());
    }

    @Override
    public void recoverFromTimelineData(NvsObject nvsObject) {
        if (!(nvsObject instanceof NvsCompoundCaption)) {
            return;
        }
        NvsCompoundCaption nvsTimelineCompoundCaption = (NvsCompoundCaption) nvsObject;
        setObject(nvsTimelineCompoundCaption);
        loadData();
    }


    /**
     * Gets template attachment.
     * <p>
     * 根据key设置相关属性
     *
     * @param type the type
     * @return the template attachment
     */
    public String getTemplateAttachment(String type) {
        NvsCompoundCaption object = getObject();
        if (object != null) {
            return object.getTemplateAttachment(type);
        }
        return null;
    }

    /**
     * Sets template attachment.
     * <p>
     * 设置模板附带属性
     *
     * @param key   the key
     * @param value the value
     */
    public void setTemplateAttachment(String key, String value) {
        NvsCompoundCaption object = getObject();
        if (object != null) {
            object.setTemplateAttachment(key, value);
        }
    }

    private KeyFrameProcessor<NvsCompoundCaption> mKeyFrameHolder;

    @Override
    public KeyFrameProcessor<NvsCompoundCaption> keyFrameProcessor() {
        if (mKeyFrameHolder == null) {
            mKeyFrameHolder = new KeyFrameProcessor<>(this);
        }
        return mKeyFrameHolder;
    }

    public void syncParam(MeicamCompoundCaptionClip captionClip) {
        this.compoundCaptionItems = captionClip.getCompoundCaptionItems();
        this.zValue = captionClip.getZValue();
        this.translationX = captionClip.getTranslationX();
        this.translationY = captionClip.getTranslationY();
        this.scaleX = captionClip.getScaleX();
        this.scaleY = captionClip.getScaleY();
        this.assetAnchor = captionClip.getAssetAnchor();
        this.rotation = captionClip.getRotation();
        bindToTimeline();
    }
}
