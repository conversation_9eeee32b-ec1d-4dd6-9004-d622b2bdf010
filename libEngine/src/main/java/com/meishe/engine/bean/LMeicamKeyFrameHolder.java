package com.meishe.engine.bean;

import android.annotation.SuppressLint;

import com.meishe.engine.local.LMeicamKeyFrame;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/7/1 15:06
 * @Description :关键帧处理器 The local key frame holder
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public  class LMeicamKeyFrameHolder implements Serializable {

    @SuppressLint("UseSparseArrays")
    protected Map<Long, LMeicamKeyFrame> keyFrameMap = new HashMap<>();

    public Map<Long, LMeicamKeyFrame> getKeyFrameMap() {
        return keyFrameMap;
    }

    public void setKeyFrameMap(Map<Long, LMeicamKeyFrame> keyFrameMap) {
        this.keyFrameMap = keyFrameMap;
    }
}
