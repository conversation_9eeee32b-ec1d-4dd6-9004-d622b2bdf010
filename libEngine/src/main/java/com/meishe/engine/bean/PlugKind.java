package com.meishe.engine.bean;

import com.meishe.base.utils.Utils;

import java.io.Serializable;
import java.util.List;

/**
 * All rights Reserved, Designed By www.meishesdk.com
 *
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2022/6/16 15:09
 * @Description: 插件类型 The plug kind
 * @Copyright: www.meishesdk.com Inc. All rights reserved.
 */
public class PlugKind implements Serializable {
    private String name;
    private String enName;
    public String effectListPath;
    public List<String> effectKeyList;

    public String getName() {
        return Utils.isZh() ? name : enName;
    }
}
