package com.meishe.engine.observer;


import com.meishe.engine.DownloadManager;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/3/11 18:40
 * @Description :上传监听对象， The observer for uploading.
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class DownLoadObserver<T> {

    public void onStateChanged(int count){

    }

    public void onProgress(String tag, int progress){

    }

    public void onSuccess(String tag, DownloadManager.DownloadParam<T> param){

    }

    public void onFailed(String tag){

    }
}
