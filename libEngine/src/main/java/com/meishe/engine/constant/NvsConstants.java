package com.meishe.engine.constant;


import java.util.ArrayList;

import android.text.TextUtils;

import com.meicam.sdk.NvsStreamingContext;
import com.meishe.base.utils.LogUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.meishe.engine.bean.MeicamFxParam.TYPE_BOOLEAN;
import static com.meishe.engine.bean.MeicamFxParam.TYPE_FLOAT;
import static com.meishe.engine.bean.MeicamFxParam.TYPE_OBJECT;
import static com.meishe.engine.bean.MeicamFxParam.TYPE_STRING;

/**
 * The type Nvs constants.
 * Nvs常量类
 *
 * <AUTHOR>
 * @version 1.0
 * @title
 * @description 该类主要功能描述
 * @company 美摄
 * @created 2020 /11/24 19:44
 * @changeRecord [修改记录] <br/>
 */
public class NvsConstants {

    private static String SDK_VERSION = "";
    public static String HTTP = "http";
    private static NvsStreamingContext.SdkVersion sdkVersion;
    /**
     * 人脸类型
     * Face type
     * SDK普通版
     * SDK Normal version
     */
    public static final int HUMAN_AI_TYPE_NONE = 0;
    /**
     * SDK meishe人脸模块
     * SDK meishe The face of a module
     * */
    public static final int HUMAN_AI_TYPE_MS = 1;

    public static final int HUMAN_AI_TYPE_FU = 2;//FU

    public static final int POINT16V9 = AspectRatio.AspectRatio_16v9;

    public static final int POINT1V1 = AspectRatio.AspectRatio_1v1;

    public static final int POINT9V16 = AspectRatio.AspectRatio_9v16;

    public static final int POINT3V4 = AspectRatio.AspectRatio_3v4;

    public static final int POINT4V3 = AspectRatio.AspectRatio_4v3;

    /**
     * Gets sdk version.
     * 获取SDK版本号
     *
     * @return the sdk version
     */
    public static String getSDKVersion() {
        if (TextUtils.isEmpty(SDK_VERSION)) {
            try {
                sdkVersion = NvsStreamingContext.getInstance().getSdkVersion();
                SDK_VERSION = sdkVersion.majorVersion + "." + sdkVersion.minorVersion +
                        "." + sdkVersion.revisionNumber;
            } catch (Exception e) {
                LogUtils.e(e);
            }
        }
        return SDK_VERSION;
    }

    public static NvsStreamingContext.SdkVersion getSdkVersion() {
        if (sdkVersion == null) {
            sdkVersion = NvsStreamingContext.getInstance().getSdkVersion();
        }
        return sdkVersion;
    }

    /**
     * The type Aspect ratio.
     * 纵横比类
     */
    public static class AspectRatio {
        /**
         * 不适配比例
         * Unfit ratio
         */
        public static final int AspectRatio_NoFitRatio = 0;
        public static final int AspectRatio_16v9 = 1;
        public static final int AspectRatio_1v1 = 2;
        public static final int AspectRatio_9v16 = 4;
        public static final int AspectRatio_4v3 = 8;
        public static final int AspectRatio_3v4 = 16;

        public static final int AspectRatio_18v9 = 32;
        public static final int AspectRatio_9v18 = 64;
        public static final int AspectRatio_21v9 = 512;
        public static final int AspectRatio_9v21 = 1024;
        public static final int AspectRatio_2D39v1 = 2048;
        public static final int AspectRatio_2D55v1 = 4096;
        public static final int AspectRatio_All = AspectRatio_16v9
                | AspectRatio_1v1
                | AspectRatio_9v16
                | AspectRatio_3v4
                | AspectRatio_4v3
                | AspectRatio_18v9
                | AspectRatio_9v18
                | AspectRatio_21v9
                | AspectRatio_2D39v1
                | AspectRatio_2D55v1
                | AspectRatio_9v21;
    }


    /**
     * 马赛克
     * mosaic
     */
    public static final String MOSAICNAME = "Mosaic";
    /**
     * 高斯模糊
     * Gaussian Blur
     */
    public static final String BLURNAME = "Gaussian Blur";

    /**
     * Transform 2D
     * 二维
     */
    public final static String FX_TRANSFORM_2D = "Transform 2D";

    public final static String FX_TRANSFORM_2D_ROTATION = "Rotation";

    public final static String FX_TRANSFORM_2D_SCALE_X = "Scale X";

    public final static String FX_TRANSFORM_2D_SCALE_Y = "Scale Y";

    public final static String FX_TRANSFORM_2D_TRANS_X = "Trans X";

    public final static String FX_TRANSFORM_2D_TRANS_Y = "Trans Y";

    /**
     * Color Property
     * 颜色属性
     */
    public final static String FX_COLOR_PROPERTY = "Color Property";

    public final static String FX_COLOR_PROPERTY_BRIGHTNESS = "Brightness";

    public final static String FX_COLOR_PROPERTY_CONTRAST = "Contrast";

    public final static String FX_COLOR_PROPERTY_SATURATION = "Saturation";

    /**
     * Vignette
     * 暗角
     */
    public final static String FX_VIGNETTE = "Vignette";

    public final static String FX_VIGNETTE_DEGREE = "Degree";

    /**
     * Sharpen
     * 锐度
     */
    public final static String FX_SHARPEN = "Sharpen";

    public final static String FX_SHARPEN_AMOUNT = "Amount";


    /**
     * 调节
     * adjust
     * 亮度，对比度，饱和度，高光，阴影，褐色
     * Brightness, contrast, saturation, highlight, shadow, brown
     */
    public final static String ADJUST_TYPE_BASIC_IMAGE_ADJUST = "BasicImageAdjust";
    /**
     * 亮度
     *  brightness
     * */
    public final static String ADJUST_BRIGHTNESS = "Brightness";
    /**
     * 对比度
     * contrast
     * */
    public final static String ADJUST_CONTRAST = "Contrast";
    /**
     * 饱和度
     * saturability
     * */
    public final static String ADJUST_SATURATION = "Saturation";
    /**
     * 高光
     * highlight
     * */
    public final static String ADJUST_HIGHTLIGHT = "Highlight";
    /**
     * 阴影
     * shadow
     * */
    public final static String ADJUST_SHADOW = "Shadow";
    /**
     * 褐色
     * brownness
     * */
    public final static String ADJUST_BLACKPOINT = "Blackpoint";
    /**
     * 色调
     * tinge
     * */
    public final static String ADJUST_TINT = "Tint";
    /**
     * 色温
     * color temperature
     * */
    public final static String ADJUST_TEMPERATURE = "Temperature";
    /**
     * 锐度
     * acutance
     * */
    public final static String ADJUST_AMOUNT = "Amount";
    /**
     * 暗角
     * Vignetting
     * */
    public final static String ADJUST_DEGREE = "Degree";

    /**
     * Video Mode Key
     * Video Mode
     * */
    public final static String KEY_VIDEO_MODE = "Video Mode";


    /**
     * 背景属性特效
     * Background attribute effects
     * 属性
     * property
     */
    public final static String PROPERTY_FX = "Positioner";
    /**
     * 背景模式
     * background mode
     * */
    public final static String KEY_BACKGROUND_MODE = "Background Mode";
    /**
     * 背景模式，颜色属性
     * Background mode, color properties
     * */
    public final static String VALUE_COLOR_BACKGROUND_MODE = "Color Solid";
    /**
     * 背景模式，模糊属性
     * Background mode, fuzzy attribute
     * */
    public final static String VALUE_BLUR_BACKGROUND_MODE = "Blur";

    /**
     * 背景模式，图片属性
     * Background mode, fuzzy attribute
     * */
    public final static String VALUE_IMAGE_BACKGROUND_MODE = "Image File";

    /**
     * 设置颜色 key
     * Set the color key
     * */
    public final static String KEY_BACKGROUND_COLOR = "Background Color";
    /**
     * 设置模糊程度 key
     * Set the blur level key
     * */
    public final static String KEY_BACKGROUND_BLUR_RADIUS = "Background Blur Radius";
    /**
     * 图片路径 key
     * Set the image path key
     */
    public final static String KEY_BACKGROUND_IMAGE_PATH = "Background Image";

    /**
     * 背景旋转
     * Enable Background Rotation
     */
    public final static String KEY_BACKGROUND_ROTATION = "Enable Background Rotation";
    /**
     *  背景，锯齿
     *  Enable MutliSample
     */
    public final static String KEY_BACKGROUND_MUTLISAMPLE = "Enable MutliSample";

    /**
     * 透明度
     * Opacity
     */
    public final static String PROPERTY_OPACITY = "Opacity";


    //适配 动画ID key  Adapt the animation ID
    public final static String PACKAGE_ID = "Package Id";
    public final static String PACKAGE2_ID = "Package2 Id";
    public final static String POST_PACKAGE_ID = "Post Package Id";
    public final static String POST_PACKAGE2_ID = "Post Package2 Id";
    public final static String IS_POST_STORY_BOARD_3D = "Is Post Storyboard 3D";
    public final static String PACKAGE_EFFECT_IN = "Package Effect In";
    public final static String PACKAGE_EFFECT_OUT = "Package Effect Out";
    public final static String PACKAGE2_EFFECT_IN = "Package2 Effect In";
    public final static String PACKAGE2_EFFECT_OUT = "Package2 Effect Out";
    public final static String PACKAGE_TYPE_ANIMATION_IN = "animation in";
    public final static String AMPLITUDE = "amplitude";
    public final static String SCAN_VALUE = "Scan Value";
    public final static String PAN_VALUE = "Pan Value";
    /**
     * 蒙版相关
     * About mask
     */
    public final static String KEY_MASK_GENERATOR = "Mask Generator";
    /**
     * 设置忽略背景
     * Setting Ignore background
     */
    public final static String KEY_MASK_KEEP_RGB = "Keep RGB";
    /**
     * 设置区域反转
     * Set zone inversion
     */
    public final static String KEY_MASK_INVERSE_REGION = "Inverse Region";
    /**
     * 设置蒙版区域
     * Set the mask are
     */
    public final static String KEY_MASK_REGION_INFO = "Region Info";
    /**
     * 设置羽化值
     * Set the feather value
     */
    public final static String KEY_MASK_FEATHER_WIDTH = "Feather Width";

    public final static String KEY_MASK_COORDINATE_SYSTEM = "Coordinate System";

    public final static String KEY_MASK_TYPE = "maskType";
    public final static String KEY_MASK_HORIZONTAL_SCALE = "horizontalScale";
    public final static String KEY_MASK_VERTICAL_SCALE = "verticalScale";
    public final static String KEY_MASK_CORNER_RADIUS_RATE = "cornerRadiusRate";
    public final static String KEY_MASK_TRANS_X = "Trans X";
    public final static String KEY_MASK_TRANS_Y = "Trans Y";
    public final static String KEY_MASK_SCALE_X = "Scale X";
    public final static String KEY_MASK_SCALE_Y = "Scale Y";
    public final static String KEY_MASK_ROTATION = "Rotation";
    public final static String KEY_MASK_OPACITY = "Opacity";
    public final static String KEY_MASK_ANCHOR_X = "Anchor X";
    public final static String KEY_MASK_ANCHOR_Y = "Anchor Y";

    public final static String KEY_CROPPER_TRANS_X = "Trans X";
    public final static String KEY_CROPPER_TRANS_Y = "Trans Y";
    public final static String KEY_CROPPER_SCALE_X = "Scale X";
    public final static String KEY_CROPPER_SCALE_Y = "Scale Y";
    public final static String KEY_CROPPER_ROTATION = "Rotation";
    public final static String KEY_CROPPER_ASSET_ASPECT_RATIO = "cropperAssetAspectRatio";
    public final static String KEY_CROPPER_RATIO = "cropperRatio";
    public final static String KEY_CROPPER_KEEP_RGB = "Keep RGB";
    public final static String KEY_CROPPER_REGION_INFO = "Region Info";
    public final static String KEY_CROPPER_IS_NORMALIZED_COORD = "Is Normalized Coord";
    public final static String KEY_CROPPER_FORCE_IDENTICAL_POSITION = "Force Identical Position";

    public final static String KEY_PROPERTY_MASK_REGION_INFO = "Mask Region Info";
    public final static String KEY_PROPERTY_MASK_INVERSE_REGION = "Mask Inverse Region";
    public final static String KEY_PROPERTY_MASK_FEATHER_WIDTH = "Mask Feather Width";
    public final static String KEY_PROPERTY_MASK_COORDINATE_SYSTEM = "Mask Coordinate System";

    public final static String FX_POSITIONER = "Positioner";


    public final static String KEY_UNIT_SIZE = "Unit Size";
    public final static String KEY_UNIT_RADIUS = "Radius";
    public final static String KEY_REGION = "region";

    public final static String VALUE_CROPPER_FREE = "free";
    /**
     * 播放完成以后需要回滚的位置
     * The position to be rolled back after the playback is complete
     */
    public final static String KEY_ATTACHMENT_RESET_PLAY_POINT = "reset_play_point";

    /**
     * 播放完成后重新开始播放的开始时间
     * The start time of restarting playback after playback is completed
     */
    public final static String KEY_ATTACHMENT_LOOP_PLAY_POINT_START = "loop_play_point_start";

    /**
     * 播放完成后重新开始播放的结束时间
     * The end time of restarting playback after playback is completed
     */
    public final static String KEY_ATTACHMENT_LOOP_PLAY_POINT_END = "loop_play_point_end";

    /**
     * 背景分割相关
     * Some param about the background segmentation
     */
    public final static String SEGMENTATION = "Segmentation";
    public final static String INVERSE_SEGMENT = "Inverse Segment";
    public final static String OUTPUT_MASK = "Output Mask";

    /**
     * alpha特效相关
     * Some param about alpha fx
     */
    public final static String SET_ALPHA = "Set Alpha";
    public final static String ALPHA_FILE = "Alpha File";
    public final static String ALPHA_CLIP_TRIM_USED = "Clip Trim Used";


    /**
     * 内置特效
     * builtin
     */
    public static final String TYPE_BUILD_IN = "builtin";

    /**
     * 包特效
     * package
     */
    public static final String TYPE_PACKAGE = "package";

    /**
     * raw包特效
     * raw package
     */
    public static final String TYPE_RAW_PACKAGE = "rawPackage";

    /**
     * 原始内置特效
     * rawBuiltin
     */
    public static final String TYPE_RAW_BUILTIN = "rawBuiltin";

    /**
     * 属性特技
     * Positioner
     */
    public static final String TYPE_PROPERTY = "Positioner";

    /**
     * AR Scene
     */
    public final static String VIDEO_FX_AR_SCENE = "AR Scene";

    public final static String VIDEO_FX_PRAM_SCENE_ID = "Scene Id";


    /**
     * The constant VIDEO_FX_BEAUTY_EFFECT.
     * 美颜
     */
    public final static String VIDEO_FX_BEAUTY_EFFECT = "Beauty Effect";

    /**
     * The constant VIDEO_FX_BEAUTY_FAST_MODE.
     * 是否开启快速美型
     */
    public final static String VIDEO_FX_BEAUTY_FAST_MODE = "Beauty Fast Mode Enabled";

    /**
     * The constant VIDEO_FX_BEAUTY_FAST_FACE_DETECTION.
     * 快速人脸检测
     */
    public final static String VIDEO_FX_BEAUTY_FAST_FACE_DETECTION = "Fast Face Detection";

    /**
     * The constant VIDEO_FX_BEAUTY_SHAPE.
     * 美型
     */
    public final static String VIDEO_FX_BEAUTY_SHAPE = "Beauty Shape";

    /**
     * The constant FACE_MESH_INTERNAL_ENABLED.
     * 高级美型开关
     */
    public final static String FACE_MESH_INTERNAL_ENABLED = "Face Mesh Internal Enabled";

    /**
     * 是新数据标记
     */
    public final static String VIDEO_FLAG_IS_NEW_BEAUTY_SHAPE_DATA = "Is New Beauty Shape Data";


    /**
     * The constant VIDEO_FX_SINGLE_BUFFER_MODE.
     */
    public final static String VIDEO_FX_SINGLE_BUFFER_MODE = "Single Buffer Mode";

    /**
     * The constant VIDEO_FX_BEAUTY_STRENGTH.
     * 磨皮
     */
    public final static String VIDEO_FX_BEAUTY_STRENGTH = "Beauty Strength";
    /**
     * The constant VIDEO_FX_BEAUTY_WHITENING.
     * 美白
     */
    public final static String VIDEO_FX_BEAUTY_WHITENING = "Beauty Whitening";
    /**
     * The constant VIDEO_FX_BEAUTY_REDDENING.
     * 红润
     */
    public final static String VIDEO_FX_BEAUTY_REDDENING = "Beauty Reddening";

    public final static String VIDEO_FX_FACE_SIZE_WARP_DEGREE = "Face Size Warp Degree";

    public final static String VIDEO_FX_EYE_SIZE_WARP_DEGREE = "Eye Size Warp Degree";

    public final static String VIDEO_FX_CHIN_LENGTH_WARP_DEGREE = "Chin Length Warp Degree";

    public final static String VIDEO_FX_FOREHEAD_HEIGHT_WARP_DEGREE = "Forehead Height Warp Degree";
    public final static String VIDEO_FX_NOSE_WIDTH_WARP_DEGREE = "Nose Width Warp Degree";
    public final static String VIDEO_FX_MOUTH_SIZE_WARP_DEGREE = "Mouth Size Warp Degree";

    public final static String AUDIO_FX_CHANGE_SPEED = "Change Speed";

    /**
     * 云剪辑抠像特技和参数
     * The mask keyer keys of param
     */
    public static class MasterKeyer {
        public static final String NAME = "Master Keyer";
        public static final String REGION_FEATHER_WIDTH = "Region Feather Width";
        public final static String REVERSE_REGION = "Reverse Region";
        public final static String FILTER_INTENSITY = "Filter Intensity";
        public final static String IGNORE_REGION_BACKGROUND = "Ignore Region Background";
        public final static String RGB_SOFTNESS = "RGB Softness";
        public final static String VAL_ROLL_OFF = "Val Roll Off";
        public final static String SAT_ROLL_OFF = "Sat Roll Off";
        public final static String SAT_WIDTH = "Sat Width";
        public final static String VAL_WIDTH = "Val Width";
        public final static String SPILL_REMOVAL_INTENSITY = "Spill Removal Intensity";
        public final static String APERTURE = "Aperture";
        public final static String KEYER_MODE = "Keyer Mode";
        public final static String REGION_COORDINATE_SYSTEM_TYPE = "Region Coordinate System Type";
        public final static String SHRINK_INTENSITY = "Shrink Intensity";
        public final static String DISABLE_PREMULTIPLY = "Disable Premultiply";
        public final static String ENABLE_REGION = "Enable Region";
        public final static String SOFTENESS_AMENDMENT = "Softeness Amendment";
        public final static String SPILL_REMOVAL = "Spill Removal";
        public final static String HUE_ROLL_OFF = "Hue Roll Off";
        public final static String KEY_COLOR = "Key Color";
        public final static String KEY_COLOR_POSITION = "Key Color Position";
        public final static String KEY_APERTURE = "Aperture";
        public final static float APERTURE_MAX = 350F;
    }

    public static class Mosic {
        public static final String NAME = "Mosaic";
        public static final String REGION_STORYBOARD_RESOURCE_DIR_PATH = "Region Storyboard Resource Dir Path";
        public static final String REGION_STORYBOARD_DESCRIPTION_STRING = "Region Storyboard Description String";
        public static final String REGION_FEATHER_WIDTH = "Region Feather Width";
        public static final String FILTER_INTENSITY = "Filter Intensity";
        public static final String REVERSE_REGION = "Reverse Region";
        public static final String ALIGN_CENTER = "Align Center";
        public static final String ENABLE_REGION = "Enable Region";
        public static final String IGNORE_REGION_BACKGROUND = "Ignore Region Background";
        public static final String UNIT_SIZE = "Unit Size";
        public static final String REGION = KEY_REGION;
    }

    public static class Blur {
        public static final String NAME = "Gaussian Blur";
        public static final String REGION_STORYBOARD_RESOURCE_DIR_PATH = "Region Storyboard Resource Dir Path";
        public static final String REGION_STORYBOARD_DESCRIPTION_STRING = "Region Storyboard Description String";
        public static final String REGION_FEATHER_WIDTH = "Region Feather Width";
        public static final String FILTER_INTENSITY = "Filter Intensity";
        public static final String ENABLE_REGION = "Enable Region";
        public static final String IGNORE_REGION_BACKGROUND = "Ignore Region Background";
        public static final String RADIUS = KEY_UNIT_RADIUS;
        public static final String REGION = KEY_REGION;
    }

    public static class TransForm2D {
        public static final String NAME = "Transform 2D";
        public static final String SCALE_X = "Scale X";
        public static final String SCALE_Y = "Scale Y";
        public static final String TRANS_X = "Trans X";
        public static final String TRANS_Y = "Trans Y";
        public static final String ANCHOR_Y = "Anchor Y";
        public static final String ANCHOR_X = "Anchor X";
        public static final String ROTATION = "Rotation";
        public static final String ROTATION_X = "Rotation X";
        public static final String ROTATION_Y = "Rotation Y";
    }

    public static class Beauty {
        public static final String NAME = VIDEO_FX_AR_SCENE;
        /**
         * The constant VIDEO_FX_BEAUTY_STRENGTH.
         * 磨皮
         */
        public final static String VIDEO_FX_BEAUTY_STRENGTH = "Beauty Strength";
        /**
         * The constant VIDEO_FX_BEAUTY_WHITENING.
         * 美白
         */
        public final static String VIDEO_FX_BEAUTY_WHITENING = "Beauty Whitening";
        /**
         * The constant VIDEO_FX_BEAUTY_REDDENING.
         * 红润
         */
        public final static String VIDEO_FX_BEAUTY_REDDENING = "Beauty Reddening";
    }

    public static class BeautyShapePackageID {
        /**
         * Face size
         * 瘦脸
         */
        public static final String Mesh_Face_Size = "63BD3F32-D01B-4755-92D5-0DE361E4045A";
        /**
         * Eye size
         * 大眼
         */
        public static final String Mesh_Eye_Size = "71C4CF51-09D7-4CB0-9C24-5DE9375220AE";
        /**
         * Nose width
         * 瘦鼻
         */
        public static final String Mesh_Nose_Width = "8D676A5F-73BD-472B-9312-B6E1EF313A4C";
        /**
         * Face length
         * 小脸
         */
        public static final String Mesh_Face_Length = "B85D1520-C60F-4B24-A7B7-6FEB0E737F15";
        /**
         * Forehead height
         * 额头
         */
        public static final String Warp_Forehead_Height = "A351D77A-740D-4A39-B0EA-393643159D99";
        /**
         * Chin length
         * 下巴
         */
        public static final String Mesh_Chin_Length = "FF2D36C5-6C91-4750-9648-BD119967FE66";
        /**
         * Mouth size
         * 嘴型
         */
        public static final String Mesh_Mouth_Size = "A80CC861-A773-4B8F-9CFA-EE63DB23EEC2";
    }

    public static class BeautyShapeID {
        public static final String Mesh_Face_Size = "Face Mesh Face Size Custom Package Id";
        public static final String Mesh_Eye_Size = "Face Mesh Eye Size Custom Package Id";
        public static final String Mesh_Nose_Width = "Face Mesh Nose Width Custom Package Id";
        public static final String Mesh_Face_Length = "Face Mesh Face Length Custom Package Id";
        public static final String Warp_Forehead_Height = "Warp Forehead Height Custom Package Id";
        public static final String Mesh_Chin_Length = "Face Mesh Chin Length Custom Package Id";
        public static final String Mesh_Mouth_Size = "Face Mesh Mouth Size Custom Package Id";
    }

    public static class BeautyShapeDegree {
        public static final String Mesh_Face_Size = "Face Mesh Face Size Degree";
        public static final String Mesh_Eye_Size = "Face Mesh Eye Size Degree";
        public static final String Mesh_Nose_Width = "Face Mesh Nose Width Degree";
        public static final String Mesh_Face_Length = "Face Mesh Face Length Degree";
        public static final String Warp_Forehead_Height = "Forehead Height Warp Degree";
        public static final String Mesh_Chin_Length = "Face Mesh Chin Length Degree";
        public static final String Mesh_Mouth_Size = "Face Mesh Mouth Size Degree";
    }

    public static class Crop {
        public static final String NAME = "Crop";
        public static final String BOUNDING_LEFT = "Bounding Left";
        public static final String BOUNDING_RIGHT = "Bounding Right";
        public static final String BOUNDING_TOP = "Bounding Top";
        public static final String BOUNDING_BOTTOM = "Bounding Bottom";
    }

    public final static String TEMPLATE_KEY_FOOTAGE_FX_GROUP = "MSTemplate-FxGroup";

    /**
     * 存储自定义贴纸图片路径的key值
     * Store the key value of the custom sticker image path
     */
    public final static String STICKER_KEY_FOR_CUSTOM_FILE_PATH = "Ext Image1";

    public static final String HDR_EXPORT_CONFIG_NONE = "none";
    public static final String HDR_EXPORT_CONFIG_2084 = "st2084";
    public static final String HDR_EXPORT_CONFIG_HLG = "hlg";
    public static final String HDR_EXPORT_CONFIG_HDR10PLUS = "hdr10plus";
    public static final String HDR_EXPORT_HEVC = "hevc";

    public static float sHdrColorGain = -1;
    public static int sHdrPreviewMode = -1;
    public static int sHdrBitDepth = -1;


    @Deprecated
    public static Map<String, String> getKeyToAdjustFxMap() {
        Map<String, String> data = new HashMap<>();
        data.put(ADJUST_AMOUNT, FX_SHARPEN);
        data.put(ADJUST_DEGREE, FX_VIGNETTE);
        data.put(ADJUST_BLACKPOINT, ADJUST_TYPE_BASIC_IMAGE_ADJUST);
        data.put(ADJUST_TINT, ADJUST_TINT);
        data.put(ADJUST_TEMPERATURE, ADJUST_TINT);
        data.put(ADJUST_SHADOW, ADJUST_TYPE_BASIC_IMAGE_ADJUST);
        data.put(ADJUST_HIGHTLIGHT, ADJUST_TYPE_BASIC_IMAGE_ADJUST);
        data.put(ADJUST_SATURATION, ADJUST_TYPE_BASIC_IMAGE_ADJUST);
        data.put(ADJUST_CONTRAST, ADJUST_TYPE_BASIC_IMAGE_ADJUST);
        data.put(ADJUST_BRIGHTNESS, ADJUST_TYPE_BASIC_IMAGE_ADJUST);
        return data;
    }

    public static Map<String, List<String>> getAdjustFxAndKeyMap() {
        Map<String, List<String>> data = new HashMap<>();
        List<String> item = new ArrayList<>();
        item.add(ADJUST_BLACKPOINT);
        item.add(ADJUST_SHADOW);
        item.add(ADJUST_HIGHTLIGHT);
        item.add(ADJUST_SATURATION);
        item.add(ADJUST_CONTRAST);
        item.add(ADJUST_BRIGHTNESS);
        data.put(ADJUST_TYPE_BASIC_IMAGE_ADJUST, item);
        item = new ArrayList<>();
        item.add(ADJUST_AMOUNT);
        data.put(FX_SHARPEN, item);
        item = new ArrayList<>();
        item.add(ADJUST_DEGREE);
        data.put(FX_VIGNETTE, item);
        item = new ArrayList<>();
        item.add(ADJUST_TINT);
        item.add(ADJUST_TEMPERATURE);
        data.put(ADJUST_TINT, item);
        return data;
    }


    public static String[] getPropertyKeys() {
        return new String[]{
                KEY_BACKGROUND_MODE,
                FX_TRANSFORM_2D_ROTATION,
                FX_TRANSFORM_2D_SCALE_X,
                FX_TRANSFORM_2D_SCALE_Y,
                FX_TRANSFORM_2D_TRANS_X,
                FX_TRANSFORM_2D_TRANS_Y,
                PROPERTY_OPACITY,
                KEY_BACKGROUND_COLOR,
                KEY_BACKGROUND_IMAGE_PATH,
                KEY_BACKGROUND_BLUR_RADIUS,
                KEY_BACKGROUND_ROTATION,
                PACKAGE_ID,
                PACKAGE2_ID,
                POST_PACKAGE_ID,
                POST_PACKAGE2_ID,
                IS_POST_STORY_BOARD_3D,
                PACKAGE_EFFECT_IN,
                PACKAGE_EFFECT_OUT,
                PACKAGE2_EFFECT_IN,
                PACKAGE2_EFFECT_OUT,
                PACKAGE_TYPE_ANIMATION_IN,
                AMPLITUDE
        };
    }

    public static String[] getARSenseKeys() {
        return new String[]{
                VIDEO_FX_PRAM_SCENE_ID,
                VIDEO_FX_BEAUTY_EFFECT,
                VIDEO_FX_BEAUTY_STRENGTH,
                VIDEO_FX_BEAUTY_WHITENING,
                VIDEO_FX_BEAUTY_REDDENING,
                VIDEO_FX_BEAUTY_SHAPE,
                VIDEO_FX_FACE_SIZE_WARP_DEGREE,
                VIDEO_FX_EYE_SIZE_WARP_DEGREE,
                VIDEO_FX_CHIN_LENGTH_WARP_DEGREE,
                VIDEO_FX_FOREHEAD_HEIGHT_WARP_DEGREE,
                VIDEO_FX_NOSE_WIDTH_WARP_DEGREE,
                VIDEO_FX_MOUTH_SIZE_WARP_DEGREE
        };
    }

    public static String[] getMaskKeys() {
        return new String[]{
                KEY_MASK_REGION_INFO,
                KEY_MASK_INVERSE_REGION,
                KEY_MASK_FEATHER_WIDTH,
                KEY_MASK_KEEP_RGB,
                KEY_MASK_COORDINATE_SYSTEM
        };
    }

    /**
     * Gets fx params.
     * 根据特效名，获取特效参数
     *
     * @param fxName the fx name 特效名
     * @return the fx params 特效参数
     */

    public static List<String[]> getFxParams(String fxName) {
        List<String[]> data = new ArrayList<>();
        if (FX_POSITIONER.equals(fxName)) {
            data.add(new String[]{TYPE_STRING, KEY_BACKGROUND_MODE});
            data.add(new String[]{TYPE_FLOAT, FX_TRANSFORM_2D_ROTATION});
            data.add(new String[]{TYPE_FLOAT, FX_TRANSFORM_2D_SCALE_X});
            data.add(new String[]{TYPE_FLOAT, FX_TRANSFORM_2D_SCALE_Y});
            data.add(new String[]{TYPE_FLOAT, FX_TRANSFORM_2D_TRANS_X});
            data.add(new String[]{TYPE_FLOAT, FX_TRANSFORM_2D_TRANS_Y});
            data.add(new String[]{TYPE_FLOAT, PROPERTY_OPACITY});
            data.add(new String[]{TYPE_STRING, KEY_BACKGROUND_COLOR});
            data.add(new String[]{TYPE_STRING, KEY_BACKGROUND_IMAGE_PATH});
            data.add(new String[]{TYPE_FLOAT, KEY_BACKGROUND_BLUR_RADIUS});
            data.add(new String[]{TYPE_BOOLEAN, KEY_BACKGROUND_ROTATION});
            data.add(new String[]{TYPE_STRING, PACKAGE_ID});
            data.add(new String[]{TYPE_STRING, PACKAGE2_ID});
            data.add(new String[]{TYPE_STRING, POST_PACKAGE_ID});
            data.add(new String[]{TYPE_STRING, POST_PACKAGE2_ID});
            data.add(new String[]{TYPE_FLOAT, PACKAGE_EFFECT_IN});
            data.add(new String[]{TYPE_FLOAT, PACKAGE_EFFECT_OUT});
            data.add(new String[]{TYPE_FLOAT, PACKAGE2_EFFECT_IN});
            data.add(new String[]{TYPE_FLOAT, PACKAGE2_EFFECT_OUT});
            data.add(new String[]{TYPE_BOOLEAN, PACKAGE_TYPE_ANIMATION_IN});
            data.add(new String[]{TYPE_STRING, AMPLITUDE});
            data.add(new String[]{TYPE_FLOAT, SCAN_VALUE});
            data.add(new String[]{TYPE_FLOAT, PAN_VALUE});
            data.add(new String[]{TYPE_OBJECT, KEY_PROPERTY_MASK_REGION_INFO});
            data.add(new String[]{TYPE_BOOLEAN, KEY_PROPERTY_MASK_INVERSE_REGION});
            data.add(new String[]{TYPE_FLOAT, KEY_PROPERTY_MASK_FEATHER_WIDTH});
        } else if (VIDEO_FX_AR_SCENE.equals(fxName)) {
            data.add(new String[]{TYPE_STRING, VIDEO_FX_PRAM_SCENE_ID});
            getBeautyParam(data);
            getShapeParam(data);
        } else if (KEY_MASK_GENERATOR.equals(fxName)) {
            data.add(new String[]{TYPE_OBJECT, KEY_MASK_REGION_INFO});
            data.add(new String[]{TYPE_BOOLEAN, KEY_MASK_INVERSE_REGION});
            data.add(new String[]{TYPE_FLOAT, KEY_MASK_FEATHER_WIDTH});
            data.add(new String[]{TYPE_BOOLEAN, KEY_MASK_KEEP_RGB});
            data.add(new String[]{TYPE_BOOLEAN, KEY_MASK_COORDINATE_SYSTEM});
        } else if (ADJUST_TYPE_BASIC_IMAGE_ADJUST.equals(fxName)) {
            data.add(new String[]{TYPE_FLOAT, ADJUST_BLACKPOINT});
            data.add(new String[]{TYPE_FLOAT, ADJUST_SHADOW});
            data.add(new String[]{TYPE_FLOAT, ADJUST_HIGHTLIGHT});
            data.add(new String[]{TYPE_FLOAT, ADJUST_SATURATION});
            data.add(new String[]{TYPE_FLOAT, ADJUST_CONTRAST});
            data.add(new String[]{TYPE_FLOAT, ADJUST_BRIGHTNESS});
        } else if (FX_SHARPEN.equals(fxName)) {
            data.add(new String[]{TYPE_FLOAT, ADJUST_AMOUNT});
        } else if (FX_VIGNETTE.equals(fxName)) {
            data.add(new String[]{TYPE_FLOAT, ADJUST_DEGREE});
        } else if (ADJUST_TINT.equals(fxName)) {
            data.add(new String[]{TYPE_FLOAT, ADJUST_TEMPERATURE});
            data.add(new String[]{TYPE_FLOAT, ADJUST_TINT});
        } else if (SET_ALPHA.equals(fxName)) {
            data.add(new String[]{TYPE_STRING, ALPHA_FILE});
            data.add(new String[]{TYPE_BOOLEAN, ALPHA_CLIP_TRIM_USED});
        } else if (MasterKeyer.NAME.equals(fxName)) {
            data.add(new String[]{TYPE_BOOLEAN, MasterKeyer.IGNORE_REGION_BACKGROUND});
            data.add(new String[]{TYPE_BOOLEAN, MasterKeyer.REVERSE_REGION});
            data.add(new String[]{TYPE_FLOAT, MasterKeyer.FILTER_INTENSITY});
            data.add(new String[]{TYPE_FLOAT, MasterKeyer.RGB_SOFTNESS});
            data.add(new String[]{TYPE_FLOAT, MasterKeyer.VAL_ROLL_OFF});
            data.add(new String[]{TYPE_FLOAT, MasterKeyer.SAT_ROLL_OFF});
            data.add(new String[]{TYPE_FLOAT, MasterKeyer.REGION_FEATHER_WIDTH});
            data.add(new String[]{TYPE_FLOAT, MasterKeyer.SAT_WIDTH});
            data.add(new String[]{TYPE_FLOAT, MasterKeyer.VAL_WIDTH});
            data.add(new String[]{TYPE_FLOAT, MasterKeyer.SPILL_REMOVAL_INTENSITY});
            data.add(new String[]{TYPE_FLOAT, MasterKeyer.APERTURE});
            data.add(new String[]{TYPE_OBJECT, MasterKeyer.KEYER_MODE});
            data.add(new String[]{TYPE_FLOAT, MasterKeyer.SHRINK_INTENSITY});
            data.add(new String[]{TYPE_BOOLEAN, MasterKeyer.DISABLE_PREMULTIPLY});
            data.add(new String[]{TYPE_BOOLEAN, MasterKeyer.ENABLE_REGION});
            data.add(new String[]{TYPE_FLOAT, MasterKeyer.SOFTENESS_AMENDMENT});
            data.add(new String[]{TYPE_BOOLEAN, MasterKeyer.SPILL_REMOVAL});
            data.add(new String[]{TYPE_FLOAT, MasterKeyer.HUE_ROLL_OFF});
            data.add(new String[]{TYPE_OBJECT, MasterKeyer.KEY_COLOR});
        } else if (Mosic.NAME.equals(fxName)) {
            data.add(new String[]{TYPE_STRING, Mosic.REGION_STORYBOARD_RESOURCE_DIR_PATH});
            data.add(new String[]{TYPE_STRING, Mosic.REGION_STORYBOARD_DESCRIPTION_STRING});
            data.add(new String[]{TYPE_FLOAT, Mosic.REGION_FEATHER_WIDTH});
            data.add(new String[]{TYPE_FLOAT, Mosic.FILTER_INTENSITY});
            data.add(new String[]{TYPE_BOOLEAN, Mosic.REVERSE_REGION});
            data.add(new String[]{TYPE_BOOLEAN, Mosic.ALIGN_CENTER});
            data.add(new String[]{TYPE_BOOLEAN, Mosic.ENABLE_REGION});
            data.add(new String[]{TYPE_BOOLEAN, Mosic.IGNORE_REGION_BACKGROUND});
            data.add(new String[]{TYPE_FLOAT, Mosic.UNIT_SIZE});
            data.add(new String[]{TYPE_OBJECT, Mosic.REGION});
        } else if (Blur.NAME.equals(fxName)) {
            data.add(new String[]{TYPE_STRING, Blur.REGION_STORYBOARD_RESOURCE_DIR_PATH});
            data.add(new String[]{TYPE_STRING, Blur.REGION_STORYBOARD_DESCRIPTION_STRING});
            data.add(new String[]{TYPE_FLOAT, Blur.REGION_FEATHER_WIDTH});
            data.add(new String[]{TYPE_FLOAT, Blur.FILTER_INTENSITY});
            data.add(new String[]{TYPE_BOOLEAN, Blur.ENABLE_REGION});
            data.add(new String[]{TYPE_BOOLEAN, Blur.IGNORE_REGION_BACKGROUND});
            data.add(new String[]{TYPE_FLOAT, Blur.RADIUS});
            data.add(new String[]{TYPE_OBJECT, Blur.REGION});
        } else if (TransForm2D.NAME.equals(fxName)) {
            data.add(new String[]{TYPE_FLOAT, TransForm2D.ANCHOR_X});
            data.add(new String[]{TYPE_FLOAT, TransForm2D.ANCHOR_Y});
            data.add(new String[]{TYPE_FLOAT, TransForm2D.SCALE_X});
            data.add(new String[]{TYPE_FLOAT, TransForm2D.SCALE_Y});
            data.add(new String[]{TYPE_FLOAT, TransForm2D.TRANS_X});
            data.add(new String[]{TYPE_FLOAT, TransForm2D.TRANS_Y});
            data.add(new String[]{TYPE_FLOAT, TransForm2D.ROTATION});
            data.add(new String[]{TYPE_FLOAT, TransForm2D.ROTATION_X});
            data.add(new String[]{TYPE_FLOAT, TransForm2D.ROTATION_Y});
        } else if (SEGMENTATION.equals(fxName)) {
            data.add(new String[]{TYPE_BOOLEAN, INVERSE_SEGMENT});
            data.add(new String[]{TYPE_BOOLEAN, OUTPUT_MASK});
        } else if (Crop.NAME.equals(fxName)) {
            data.add(new String[]{TYPE_FLOAT, Crop.BOUNDING_TOP});
            data.add(new String[]{TYPE_FLOAT, Crop.BOUNDING_LEFT});
            data.add(new String[]{TYPE_FLOAT, Crop.BOUNDING_RIGHT});
            data.add(new String[]{TYPE_FLOAT, Crop.BOUNDING_BOTTOM});
        }
        return data;
    }


    private static void getBeautyParam(List<String[]> data) {
        data.add(new String[]{TYPE_BOOLEAN, VIDEO_FX_BEAUTY_EFFECT});
        data.add(new String[]{TYPE_FLOAT, Beauty.VIDEO_FX_BEAUTY_STRENGTH});
        data.add(new String[]{TYPE_FLOAT, Beauty.VIDEO_FX_BEAUTY_WHITENING});
        data.add(new String[]{TYPE_FLOAT, Beauty.VIDEO_FX_BEAUTY_REDDENING});
    }

    /**
     * Gets beauty param.
     * 获取美颜参数
     *
     * @return the beauty param
     */
    public static List<String[]> getBeautyParam() {
        List<String[]> data = new ArrayList<>();
        getBeautyParam(data);
        return data;
    }


    private static void getShapeParam(List<String[]> data) {
        data.add(new String[]{TYPE_BOOLEAN, VIDEO_FX_BEAUTY_SHAPE});
        data.add(new String[]{TYPE_FLOAT, VIDEO_FX_FACE_SIZE_WARP_DEGREE});
        data.add(new String[]{TYPE_FLOAT, VIDEO_FX_EYE_SIZE_WARP_DEGREE});
        data.add(new String[]{TYPE_FLOAT, VIDEO_FX_CHIN_LENGTH_WARP_DEGREE});
        data.add(new String[]{TYPE_FLOAT, VIDEO_FX_FOREHEAD_HEIGHT_WARP_DEGREE});
        data.add(new String[]{TYPE_FLOAT, VIDEO_FX_NOSE_WIDTH_WARP_DEGREE});
        data.add(new String[]{TYPE_FLOAT, VIDEO_FX_MOUTH_SIZE_WARP_DEGREE});

        data.add(new String[]{TYPE_STRING, BeautyShapeID.Mesh_Chin_Length});
        data.add(new String[]{TYPE_STRING, BeautyShapeID.Mesh_Face_Size});
        data.add(new String[]{TYPE_STRING, BeautyShapeID.Mesh_Eye_Size});
        data.add(new String[]{TYPE_STRING, BeautyShapeID.Mesh_Nose_Width});
        data.add(new String[]{TYPE_STRING, BeautyShapeID.Mesh_Face_Length});
        data.add(new String[]{TYPE_STRING, BeautyShapeID.Warp_Forehead_Height});
        data.add(new String[]{TYPE_STRING, BeautyShapeID.Mesh_Mouth_Size});

        data.add(new String[]{TYPE_FLOAT, BeautyShapeDegree.Mesh_Chin_Length});
        data.add(new String[]{TYPE_FLOAT, BeautyShapeDegree.Mesh_Face_Size});
        data.add(new String[]{TYPE_FLOAT, BeautyShapeDegree.Mesh_Eye_Size});
        data.add(new String[]{TYPE_FLOAT, BeautyShapeDegree.Mesh_Nose_Width});
        data.add(new String[]{TYPE_FLOAT, BeautyShapeDegree.Mesh_Face_Length});
        data.add(new String[]{TYPE_FLOAT, BeautyShapeDegree.Warp_Forehead_Height});
        data.add(new String[]{TYPE_FLOAT, BeautyShapeDegree.Mesh_Mouth_Size});
    }

    /**
     * Gets shape param.
     * 获取美型参数
     *
     * @return the shape param
     */
    public static List<String[]> getShapeParam() {
        List<String[]> data = new ArrayList<>();
        getShapeParam(data);
        return data;
    }

}
