package com.meishe.engine.local;

import androidx.annotation.NonNull;

import com.google.gson.annotations.SerializedName;
import com.meishe.engine.bean.MeicamVideoFx;
import com.meishe.engine.util.DeepCopyUtil;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import static com.meishe.engine.bean.MeicamFxParam.TYPE_BOOLEAN;
import static com.meishe.engine.bean.MeicamFxParam.TYPE_FLOAT;
import static com.meishe.engine.bean.MeicamFxParam.TYPE_OBJECT;
import static com.meishe.engine.bean.MeicamFxParam.TYPE_STRING;

/**
 * All rights reserved,Designed by www.meishesdk.com
 * 版权所有:www.meishesdk.com
 *
 * <AUTHOR> LiHangZhou
 * @CreateDate :2021/05/20 10:07
 * @Description : 草稿 video fx特效数据 The draft data of video clip fx
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class LMeicamVideoFx extends LNvsObject implements Cloneable, Serializable {
    protected int index;
    // builtin package
    protected String type;
    protected String subType;
    protected String classType = "videoFx";

    protected String desc;
    //强度
    protected float intensity = 1;
    @SerializedName("fxParams")
    protected List<LMeicamFxParam<?>> mMeicamFxParam = new ArrayList<>();

    @SerializedName("maskRegionInfoData")
    private LMaskRegionInfoData maskRegionInfoData;

    @SerializedName("meicamMaskRegionInfo")
    private LMeicamMaskInfo meicamMaskRegionInfo;

    /**
     * The info for region effect of fx
     * 特效的区域特效
     */
    private LMeicamMaskRegionInfo regionInfo;

    private long inPoint;

    private long outPoint;

    /**
     * 时间线特效的标记集合
     * the set of timeline fx tag
     */
    private Set<String> timelineFxTagSet;

    public LMeicamVideoFx() {
        classType = "videoFx";
    }

    public LMeicamVideoFx(int index, String type, String desc) {
        this.index = index;
        this.type = type;
        this.desc = desc;
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public float getIntensity() {
        return intensity;
    }

    public void setSubType(String subType) {
        this.subType = subType;
    }

    public String getSubType() {
        return subType;
    }

    public void setIntensity(float intensity) {
        if (Float.isNaN(intensity)) {
            return;
        }
        this.intensity = intensity;
    }

    public List<LMeicamFxParam<?>> getMeicamFxParam() {
        return mMeicamFxParam;
    }

    public void setMeicamFxParam(List<LMeicamFxParam<?>> meicamFxParam) {
        mMeicamFxParam = meicamFxParam;
    }

    public void setStringVal(String key, String value) {
        LMeicamFxParam<String> param = new LMeicamFxParam<>(TYPE_STRING, key, value);
        mMeicamFxParam.add(param);
    }

    public String getStringVal(String key) {
        return getVal(String.class, TYPE_STRING, key);
    }

    private <T extends Object> T getVal(Class<T> clazz, String sysType, String key) {
        T t = null;
        for (LMeicamFxParam meicamFxParam : mMeicamFxParam) {
            if (sysType.equals(meicamFxParam.getType()) && key.equals(meicamFxParam.getKey())) {
                t = (T) meicamFxParam.getValue();
            }
        }
        return t;
    }

    public float getFloatVal(String key) {
        return getVal(Float.class, TYPE_FLOAT, key);
    }

    public void setBooleanVal(String key, boolean value) {
        LMeicamFxParam<Boolean> param = new LMeicamFxParam<>(TYPE_BOOLEAN, key, value);
        mMeicamFxParam.add(param);
    }

    public void setFloatVal(String key, float value) {
        LMeicamFxParam<Float> param = new LMeicamFxParam<>(TYPE_FLOAT, key, value);
        mMeicamFxParam.add(param);
    }

    public <T> void setObjectVal(String key, T value) {
        LMeicamFxParam<T> param = new LMeicamFxParam<>(TYPE_OBJECT, key, value);
        mMeicamFxParam.add(param);
    }

    public LMeicamMaskInfo getLMeicamMaskRegionInfo() {
        return meicamMaskRegionInfo;
    }

    public void setLMeicamMaskRegionInfo(LMeicamMaskInfo meicamMaskRegionInfo) {
        this.meicamMaskRegionInfo = meicamMaskRegionInfo;
    }

    public LMaskRegionInfoData getMaskRegionInfoData() {
        return maskRegionInfoData;
    }

    public void setMaskRegionInfoData(LMaskRegionInfoData maskRegionInfoData) {
        this.maskRegionInfoData = maskRegionInfoData;
    }

    @NonNull
    @Override
    public LMeicamVideoFx clone() {
        return (LMeicamVideoFx) DeepCopyUtil.deepClone(this);
    }

    public long getInPoint() {
        return inPoint;
    }

    public void setInPoint(long inPoint) {
        this.inPoint = inPoint;
    }

    public long getOutPoint() {
        return outPoint;
    }

    public void setOutPoint(long outPoint) {
        this.outPoint = outPoint;
    }

    private void setValue(MeicamVideoFx videoFx) {
        for (LMeicamFxParam fxParam : mMeicamFxParam) {
            if (TYPE_STRING.equals(fxParam.getType())) {
                videoFx.setStringVal(fxParam.getKey(), (String) fxParam.getValue());
            } else if (TYPE_BOOLEAN.equals(fxParam.getType())) {
                videoFx.setBooleanVal(fxParam.getKey(), (Boolean) fxParam.getValue());
            } else if (TYPE_FLOAT.equals(fxParam.getType())) {
                Object value = fxParam.getValue();
                if (value instanceof Float) {
                    float floatValue = (float) value;
                    videoFx.setFloatVal(fxParam.getKey(), floatValue);
                } else if (value instanceof Double) {
                    videoFx.setFloatVal(fxParam.getKey(), (float) value);
                }
            } else if (TYPE_OBJECT.equals(fxParam.getType())) {
                videoFx.setObjectVal(fxParam.getKey(), fxParam.getValue());
            }
        }
    }

    public LMeicamMaskRegionInfo getRegionInfo() {
        return regionInfo;
    }

    public void setRegionInfo(LMeicamMaskRegionInfo regionInfo) {
        this.regionInfo = regionInfo;
    }


    public Set<String> getTimelineFxTag() {
        return timelineFxTagSet;
    }

    public void setTimelineFxTag(Set<String> timelineFxTag) {
        this.timelineFxTagSet = timelineFxTag;
    }
}
