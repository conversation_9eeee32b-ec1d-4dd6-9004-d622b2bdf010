package com.meishe.engine.local;

import androidx.annotation.NonNull;

import com.meishe.engine.util.DeepCopyUtil;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * All rights Reserved, Designed By www.meishesdk.com
 *
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2021/5/13 16:59
 * @Description:
 * @Copyright: www.meishesdk.com Inc. All rights reserved.
 */
public class LMeicamMaskRegionInfo implements Serializable, Cloneable {
    private List<LRegionInfo> meicamRegionInfoArray = new ArrayList<>();


    public LMeicamMaskRegionInfo() {
    }

    public List<LRegionInfo> getMeicamRegionInfoArray() {
        return meicamRegionInfoArray;
    }

    public void setMeicamRegionInfoArray(List<LRegionInfo> meicamRegionInfoArray) {
        this.meicamRegionInfoArray = meicamRegionInfoArray;
    }

    @NonNull
    @Override
    public LMeicamMaskRegionInfo clone() {
        return (LMeicamMaskRegionInfo) DeepCopyUtil.deepClone(this);
    }

    public static class LRegionInfo implements Serializable, Cloneable {
        private int type;
        private List<LMeicamPosition2D> points = new ArrayList<>();
        private LMeicamMaskRegionInfo.LMirror mirror = new LMeicamMaskRegionInfo.LMirror();
        private LMeicamMaskRegionInfo.LEllipse2D ellipse2d = new LMeicamMaskRegionInfo.LEllipse2D();
        private LMeicamMaskRegionInfo.LTransform2D transform2d = new LMeicamMaskRegionInfo.LTransform2D();

        public LRegionInfo(int var1) {
            this.type = var1;
        }

        public List<LMeicamPosition2D> getPoints() {
            return this.points;
        }

        public LMeicamMaskRegionInfo.LEllipse2D getEllipse2D() {
            return this.ellipse2d;
        }

        public LMeicamMaskRegionInfo.LTransform2D getTransform2D() {
            return this.transform2d;
        }

        public int getType() {
            return this.type;
        }

        public void setPoints(List<LMeicamPosition2D> var1) {
            this.points = var1;
        }

        public void setEllipse2D(LMeicamMaskRegionInfo.LEllipse2D var1) {
            this.ellipse2d = var1;
        }

        public void setTransform2D(LMeicamMaskRegionInfo.LTransform2D var1) {
            this.transform2d = var1;
        }

        public LMirror getMirror() {
            return mirror;
        }

        public void setMirror(LMirror mirror) {
            this.mirror = mirror;
        }

        @NonNull
        @Override
        public LRegionInfo clone() {
            return (LRegionInfo) DeepCopyUtil.deepClone(this);
        }

    }
    public static class LMirror implements Serializable, Cloneable {
        private LMeicamPosition2D center;
        private float distance;
        private float theta;

        public LMeicamPosition2D getCenter() {
            return center;
        }

        public void setCenter(LMeicamPosition2D center) {
            this.center = center;
        }

        public float getDistance() {
            return distance;
        }

        public void setDistance(float distance) {
            this.distance = distance;
        }

        public float getTheta() {
            return theta;
        }

        public void setTheta(float theta) {
            this.theta = theta;
        }
    }
    public static class LEllipse2D implements Serializable, Cloneable {
        private LMeicamPosition2D center;
        private float a;
        private float b;
        private float theta;

        public LEllipse2D() {
            this.center = new LMeicamPosition2D(0.0F, 0.0F);
            this.a = this.b = this.theta = 0.0F;
        }

        public LEllipse2D(LMeicamPosition2D var1, float var2, float var3, float var4) {
            this.center = var1;
            this.a = var2;
            this.b = var3;
            this.theta = var4;
        }

        public LMeicamPosition2D getCenter() {
            return this.center;
        }

        public float getA() {
            return this.a;
        }

        public float getB() {
            return this.b;
        }

        public float getTheta() {
            return this.theta;
        }

        public void setCenter(LMeicamPosition2D var1) {
            this.center = var1;
        }

        public void setA(float var1) {
            this.a = var1;
        }

        public void setB(float var1) {
            this.b = var1;
        }

        public void setTheta(float var1) {
            this.theta = var1;
        }

        @NonNull
        @Override
        public LEllipse2D clone() {
            return (LEllipse2D) DeepCopyUtil.deepClone(this);
        }
    }

    public static class LTransform2D implements Serializable, Cloneable {
        private LMeicamPosition2D anchor;
        private LMeicamPosition2D scale;
        private float rotation;
        private LMeicamPosition2D translation;

        public LTransform2D() {
            this.anchor = new LMeicamPosition2D(0.0F, 0.0F);
            this.scale = new LMeicamPosition2D(1.0F, 1.0F);
            this.rotation = 0.0F;
            this.translation = new LMeicamPosition2D(0.0F, 0.0F);
        }

        public LTransform2D(LMeicamPosition2D var1, LMeicamPosition2D var2, float var3, LMeicamPosition2D var4) {
            this.anchor = var1;
            this.scale = var2;
            this.rotation = var3;
            this.translation = var4;
        }

        public LMeicamPosition2D getAnchor() {
            return this.anchor;
        }

        public LMeicamPosition2D getScale() {
            return this.scale;
        }

        public float getRotation() {
            return this.rotation;
        }

        public LMeicamPosition2D getTranslation() {
            return this.translation;
        }

        public void setAnchor(LMeicamPosition2D var1) {
            this.anchor = var1;
        }

        public void setRotation(float var1) {
            this.rotation = var1;
        }

        public void setScale(LMeicamPosition2D var1) {
            this.scale = var1;
        }

        public void setTranslation(LMeicamPosition2D var1) {
            this.translation = var1;
        }

        @NonNull
        @Override
        public LTransform2D clone() {
            return (LTransform2D) DeepCopyUtil.deepClone(this);
        }

    }

}
