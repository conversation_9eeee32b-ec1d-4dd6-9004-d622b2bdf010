package com.meishe.engine.local;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date :2020/9/21 16:07
 * @des : 构建NvRegionInfo 使用的数据源
 */
public class LMaskRegionInfoData  implements Cloneable, Serializable {
    //选择的锚点
    //private PointF mCenter;
    private float mCenterX;
    private float mCenterY;
    //蒙版宽度
    private int mMaskWidth;
    //萌版的高度
    private int mMashHeight;
    //旋转角度
    private float mRotation;
    //蒙版类型 0--6
    private int mType;
    //item 名字
    private String itemName;
    //图标
    private int drawableIcon;
    //区域反转
    private boolean reverse;
    //羽化值
    private float featherWidth;

    private float roundCornerRate;

    private float translationX;

    private float translationY;

    //水平压缩值
    private float horizontalScale = 1F;

    //竖直方向压缩值
    private float verticalScale = 1F;

    public float getHorizontalScale() {
        return horizontalScale;
    }

    public void setHorizontalScale(float horizontalScale) {
        if(Float.isNaN(horizontalScale)){
            return;
        }
        this.horizontalScale = horizontalScale;
    }

    public float getVerticalScale() {
        return verticalScale;
    }

    public void setVerticalScale(float verticalScale) {
        if(Float.isNaN(verticalScale)){
            return;
        }
        this.verticalScale = verticalScale;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getItemName() {
        return itemName;
    }

    public void setDrawableIcon(int drawableIcon) {
        this.drawableIcon = drawableIcon;
    }

    public int getDrawableIcon() {
        return drawableIcon;
    }

    public float getmCenterX() {
        return mCenterX;
    }

    public void setmCenterX(float mCenterX) {
        if(Float.isNaN(mCenterX)){
            return;
        }
        this.mCenterX = mCenterX;
    }

    public float getmCenterY() {
        return mCenterY;
    }

    public void setmCenterY(float mCenterY) {
        if(Float.isNaN(mCenterY)){
            return;
        }
        this.mCenterY = mCenterY;
    }

    public int getmType() {
        return mType;
    }

    public void setmType(int mType) {
        this.mType = mType;
    }

    public int getMaskWidth() {
        return mMaskWidth;
    }

    public void setMaskWidth(int mMaskWidth) {
        this.mMaskWidth = mMaskWidth;
    }

    public int getMaskHeight() {
        return mMashHeight;
    }

    public void setMaskHeight(int mMashHeight) {
        this.mMashHeight = mMashHeight;
    }

    public float getRotation() {
        return mRotation;
    }

    public void setRotation(float mRotation) {
        this.mRotation = mRotation;
    }

    public void setFeatherWidth(float featherWidth) {
        if(Float.isNaN(featherWidth)){
            return;
        }
        this.featherWidth = featherWidth;
    }

    public void setReverse(boolean reverse) {
        this.reverse = reverse;
    }

    public float getFeatherWidth() {
        return featherWidth;
    }

    public boolean isReverse() {
        return reverse;
    }
    public void setRoundCornerRate(float roundCornerRate) {
        if(Float.isNaN(roundCornerRate)){
            return;
        }
        this.roundCornerRate = roundCornerRate;
    }

    public float getTranslationX() {
        return translationX;
    }

    public void setTranslationX(float translationX) {
        if(Float.isNaN(translationX)){
            return;
        }
        this.translationX = translationX;
    }

    public float getTranslationY() {
        return translationY;
    }

    public void setTranslationY(float translationY) {
        if(Float.isNaN(translationY)){
            return;
        }
        this.translationY = translationY;
    }

    public float getRoundCornerRate() {
        return roundCornerRate;
    }

    public class MaskType{
        public static final int NONE = 0;
        public static final int LINE = 1;
        public static final int MIRROR = 2;
        public static final int CIRCLE = 3;
        public static final int RECT = 4;
        public static final int HEART = 5;
        public static final int STAR = 6;

    }
}
