package com.meishe.engine.local;

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2020/7/4 15:20
 */
public class LMeicamTheme implements Cloneable, Serializable {

    ///主题
    @SerializedName("themePackageId")
    private String mThemePackageId;
    ///片头时长
    @SerializedName("themeTitlesClipDuration")
    private long mThemeTitlesClipDuration;
    ///片尾时长
    @SerializedName("themeTailClipDuration")
    private long mThemeTailClipDuration;
    @SerializedName("themeTitleText")
    private String mThemeTitleText;
    @SerializedName("themeTrailerText")
    private String mThemeTrailerText;

    public LMeicamTheme(String themePackageId) {
        mThemePackageId = themePackageId;
    }

    public String getThemePackageId() {
        return mThemePackageId;
    }

    public void setThemePackageId(String themePackageId) {
        mThemePackageId = themePackageId;
    }

    public long getThemeTitlesClipDuration() {
        return mThemeTitlesClipDuration;
    }

    public void setThemeTitlesClipDuration(long themeTitlesClipDuration) {
        mThemeTitlesClipDuration = themeTitlesClipDuration;
    }

    public String getThemeTitleText() {
        return mThemeTitleText;
    }

    public void setThemeTitleText(String themeTitleText) {
        mThemeTitleText = themeTitleText;
    }

    public String getThemeTrailerText() {
        return mThemeTrailerText;
    }

    public void setThemeTrailerText(String themeTrailerText) {
        mThemeTrailerText = themeTrailerText;
    }

    public long getThemeTailClipDuration() {
        return mThemeTailClipDuration;
    }

    public void setThemeTailClipDuration(long themeTailClipDuration) {
        mThemeTailClipDuration = themeTailClipDuration;
    }

}
