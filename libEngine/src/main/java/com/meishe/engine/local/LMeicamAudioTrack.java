package com.meishe.engine.local;

import com.google.gson.annotations.SerializedName;
import com.meishe.engine.bean.CommonData;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 * 版权所有:www.meishesdk.com
 *
 * <AUTHOR> LiHangZhou
 * @CreateDate :2021/05/20 10:07
 * @Description : 音频轨道草稿数据 The draft data of audio track
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class LMeicamAudioTrack extends LTrackInfo implements Cloneable, Serializable {
    @SerializedName("transitions")
    private List<LMeicamTransition> mTransitionInfoList = new ArrayList<>();

    @SerializedName("clipInfos")
    private List<LMeicamAudioClip> audioClipList = new ArrayList<>();

    public LMeicamAudioTrack(int index) {
        super(CommonData.TRACK_AUDIO, index);
    }

    public List<LMeicamTransition> getTransitionInfoList() {
        return mTransitionInfoList;
    }

    public void setTransitionInfoList(List<LMeicamTransition> transitionInfoList) {
        mTransitionInfoList = transitionInfoList;
    }

    public List<LMeicamAudioClip> getAudioClipList() {
        return audioClipList;
    }
}
