package com.meishe.engine.local;

import android.graphics.PointF;

import com.meishe.engine.bean.Transform;

import java.io.Serializable;

/**
 * All rights Reserved, Designed By www.meishesdk.com
 *
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2021/7/21 13:18
 * @Description:
 * @Copyright: www.meishesdk.com Inc. All rights reserved.
 */
public class LNvMaskModel implements Serializable {
    public int maskType;
    public boolean inverseRegion = false;
    public float rectRate = 2;
    //圆角 和短边的比例
    public float cornerRadiusRate = 0;
    public float circleRate = 0.7f;
    public float horizontalScale = 1;
    public float verticalScale = 1;
    public float feather = 0;
    public PointF center;
    public float heightRateOfWidth = 9.0f / 10 / 5.0f;
    public int maxNumberWords = 10;
    public Transform transform = new Transform();
    public String text = "VIDEO";

    public float assetsWidth;
    public float assetsHeight;
}
