package com.meishe.engine.local;

import com.meishe.base.bean.FloatPoint;

import java.io.Serializable;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2021/8/9 18:24
 * @Description :关键帧控制点, 草稿数据 Keyframe control points for draft
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class LMeicamKeyframeControlPoints implements Serializable {
    /**
     * the id
     * 关键帧曲线的id
     */
    private int id;

    private int fontPointId;


    private int backPointId;

    /**
     * The forward control point
     * 前置控制点
     */
    private FloatPoint forwardControlPoint;
    /**
     * The backward control point
     * 后置控制点
     */
    private FloatPoint backwardControlPoint;


    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public FloatPoint getForwardControlPoint() {
        return forwardControlPoint;
    }

    public void setForwardControlPoint(FloatPoint forwardControlPoint) {
        this.forwardControlPoint = forwardControlPoint;
    }

    public FloatPoint getBackwardControlPoint() {
        return backwardControlPoint;
    }

    public void setBackwardControlPoint(FloatPoint backwardControlPoint) {
        this.backwardControlPoint = backwardControlPoint;
    }

    public int getFontPointId() {
        return fontPointId;
    }

    public void setFontPointId(int fontPointId) {
        this.fontPointId = fontPointId;
    }

    public int getBackPointId() {
        return backPointId;
    }

    public void setBackPointId(int backPointId) {
        this.backPointId = backPointId;
    }
}
