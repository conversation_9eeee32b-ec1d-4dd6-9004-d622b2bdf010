package com.meishe.engine.local;

import com.meishe.engine.bean.LMeicamKeyFrameHolder;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2020/7/3 17:10
 */
public class LNvsObject implements Cloneable, Serializable {
    private String extraTag;
    /**
     * 附带参数
     */
    private Map<String, Object> attachment = new HashMap<>();

    private LMeicamKeyFrameHolder keyFrameProcessor;

    public String getExtraTag() {
        return extraTag;
    }

    public void setExtraTag(String extraTag) {
        this.extraTag = extraTag;
    }

    public LMeicamKeyFrameHolder getKeyFrameProcessor() {
        return keyFrameProcessor;
    }

    public void setKeyFrameProcessor(LMeicamKeyFrameHolder keyFrameProcessor) {
        this.keyFrameProcessor = keyFrameProcessor;
    }

    public void setAttachment(Map<String, Object> attachment) {
        this.attachment = attachment;
    }


    public Map<String, Object> getAttachment() {
        return attachment;
    }

    public Object getAttachment(String key){
        if (attachment != null) {
            return attachment.get(key);
        }
        return null;
    }
}
