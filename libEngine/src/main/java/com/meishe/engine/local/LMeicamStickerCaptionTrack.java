package com.meishe.engine.local;

import com.google.gson.annotations.SerializedName;
import com.meishe.engine.bean.CommonData;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by Cao<PERSON>hiChao on 2020/7/3 20:39
 */
public class LMeicamStickerCaptionTrack extends LTrackInfo implements Cloneable{
    public LMeicamStickerCaptionTrack(int index) {
        super(CommonData.TRACK_STICKER_CAPTION, index);
    }

    @SerializedName("clipInfos")
    private List<LClipInfo> clipInfoList = new ArrayList<>();

    public List<LClipInfo> getClipInfoList() {
        return clipInfoList;
    }
}
