package com.meishe.engine.local;

import androidx.annotation.NonNull;

import com.google.gson.annotations.SerializedName;
import com.meishe.engine.bean.CommonData;
import com.meishe.engine.util.DeepCopyUtil;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by CaoZhiChao on 2020/7/4 11:24
 */
public class LMeicamTimelineVideoFxClip extends LClipInfo implements Cloneable, Serializable {
    // builtin package
    private String clipType;

    private String desc;
    // 网页端控制滤镜子类型的，APP端用不到。
    private int clipSubType;
    private String subType;
    private float intensity;
    // 下面4个也用不到
    private boolean isRegional = false;
    private boolean isIgnoreBackground = false;
    private boolean isInverseRegion = false;
    private int regionalFeatherWidth = 0;

    private String displayName;
    private String displayNamezhCN;
    /**
     * Region data for region effect
     * <p>
     * 区域特效数据
     */
    private float[] regionData;

    private float zValue;

    /**
     * 标识，用于标记特效
     */
    private String indicate;

    @SerializedName("fxParams")
    private List<LMeicamFxParam<?>> mMeicamFxParamList = new ArrayList<>();

    /**
     * The info for region effect of fx
     * 特效的局部特效数据
     */
    private LMeicamMaskRegionInfo regionInfo;

    public LMeicamTimelineVideoFxClip() {
        super(CommonData.CLIP_TIMELINE_FX);
    }

    public LMeicamTimelineVideoFxClip(String clipType, String desc) {
        super(CommonData.CLIP_TIMELINE_FX);
        this.clipType = clipType;
        this.desc = desc;
    }

    public String getClipType() {
        return clipType;
    }

    public void setClipType(String clipType) {
        this.clipType = clipType;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public int getClipSubType() {
        return clipSubType;
    }

    public void setClipSubType(int clipSubType) {
        this.clipSubType = clipSubType;
    }

    public float getIntensity() {
        return intensity;
    }

    public void setIntensity(float intensity) {
        if (Float.isNaN(intensity)) {
            return;
        }
        this.intensity = intensity;
    }

    public boolean isRegional() {
        return isRegional;
    }

    public void setRegional(boolean regional) {
        isRegional = regional;
    }

    public boolean isIgnoreBackground() {
        return isIgnoreBackground;
    }

    public void setIgnoreBackground(boolean ignoreBackground) {
        isIgnoreBackground = ignoreBackground;
    }

    public boolean isInverseRegion() {
        return isInverseRegion;
    }

    public void setInverseRegion(boolean inverseRegion) {
        isInverseRegion = inverseRegion;
    }

    public int getRegionalFeatherWidth() {
        return regionalFeatherWidth;
    }

    public void setRegionalFeatherWidth(int regionalFeatherWidth) {
        this.regionalFeatherWidth = regionalFeatherWidth;
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public String getDisplayNamezhCN() {
        return displayNamezhCN;
    }

    public void setDisplayNamezhCN(String displayNamezhCN) {
        this.displayNamezhCN = displayNamezhCN;
    }

    public List<LMeicamFxParam<?>> getMeicamFxParamList() {
        return mMeicamFxParamList;
    }

    public float[] getRegionData() {
        return regionData;
    }

    public void setRegionData(float[] regionData) {
        this.regionData = regionData;
    }

    @Override
    public float getzValue() {
        return zValue;
    }

    @Override
    public void setzValue(float zValue) {
        this.zValue = zValue;
    }

    public String getIndicate() {
        return indicate;
    }

    public void setIndicate(String indicate) {
        this.indicate = indicate;
    }

    public LMeicamMaskRegionInfo getRegionInfo() {
        return regionInfo;
    }

    public void setRegionInfo(LMeicamMaskRegionInfo regionInfo) {
        this.regionInfo = regionInfo;
    }

    public String getSubType() {
        return subType;
    }

    public void setSubType(String subType) {
        this.subType = subType;
    }

    @NonNull
    @Override
    public LMeicamTimelineVideoFxClip clone() {
        return (LMeicamTimelineVideoFxClip) DeepCopyUtil.deepClone(this);
    }

}
