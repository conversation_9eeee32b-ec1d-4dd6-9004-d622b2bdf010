package com.meishe.engine.view;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.PointF;
import android.util.AttributeSet;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;

import androidx.annotation.Nullable;

import com.meishe.base.utils.LogUtils;
import com.meishe.engine.R;

/**
 * All rights reserved,Designed by www.meishesdk.com
 * 版权所有:www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2021/08/09 10:20
 * @Description : 关键帧贝塞尔曲线编辑视图 Keyframe Bezier curve edit view
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class NvBezierFrameView extends View {

    private static final int TYPE_CONTROL_NONE_POINT = 0;
    private static final int TYPE_CONTROL_FRONT_POINT = 1;
    private static final int TYPE_CONTROL_BACK_POINT = 2;
    private static final int NUMBER_FOUR = 4;


    private Paint mGridPaint, mPaint;
    private int mWidth, mHeight;
    private int mStartX, mStartY;
    private int mEndX, mEndY;
    private int mRadius, mMaxRadius;

    private int mFrontX, mFrontY, mBackX, mBackY;
    private float mDownX, mDownY;

    private int mControlType = TYPE_CONTROL_NONE_POINT;
    private final Path mPath = new Path();

    private PointF mLeftBottomPointF = new PointF(0.333333F, 0.333333F);
    private PointF mRightTopPointF = new PointF(0.666667F, 0.666667F);

    private OnTouchPointCallback mTouchPointCallback;

    public NvBezierFrameView(Context context) {
        super(context);
        init();
    }

    public NvBezierFrameView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    private void init() {
        mGridPaint = new Paint();
        mGridPaint.setColor(getContext().getResources().getColor(R.color.bezier_keyframe_curve_rect));
        mGridPaint.setStyle(Paint.Style.STROKE);
        mGridPaint.setStrokeWidth(1);

        mPaint = new Paint();
        mPaint.setAntiAlias(true);
        mRadius = (int) getResources().getDimension(R.dimen.dp_px_30);
        mMaxRadius = (int) getResources().getDimension(R.dimen.dp_px_30);
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        mWidth = getMeasuredWidth();
        mHeight = getMeasuredHeight();

        mStartX = mRadius;
        mStartY = mHeight - mRadius;
        mEndX = mWidth - mRadius;
        mEndY = mRadius;

        mFrontX = (int) (mLeftBottomPointF.x * (mWidth - 2 * mRadius) + mRadius);
        mFrontY = (int) (mHeight - mRadius - mLeftBottomPointF.y * (mHeight - 2 * mRadius));
        mBackX = (int) (mRightTopPointF.x * (mWidth - 2 * mRadius) + mRadius);
        mBackY = (int) (mHeight - mRadius - mRightTopPointF.y * (mHeight - 2 * mRadius));
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);

        Log.d("TAG", "onDraw: ===============" + mFrontX + " " + mFrontY + "  " + mBackX + "  " + mBackY);
        mPaint.setColor(getContext().getResources().getColor(R.color.bezier_keyframe_curve_baseline));
        mPaint.setStyle(Paint.Style.FILL);
        canvas.drawRect(mRadius, mRadius, mEndX, mStartY, mPaint);

        mPath.reset();
        mPath.moveTo(mRadius, mRadius);
        mPath.lineTo(mEndX, mRadius);
        mPath.lineTo(mEndX, mStartY);
        mPath.lineTo(mRadius, mStartY);
        mPath.lineTo(mRadius, mRadius);
        canvas.drawPath(mPath, mGridPaint);


        float dimW = (mWidth - 2 * mRadius) * 1.0F / 4;
        float dimH = (mHeight - 2 * mRadius) * 1.0F / 4;
        for (int i = 1; i < NUMBER_FOUR; i++) {
            float src = dimW * i + mRadius;
            mPath.moveTo(src, mRadius);
            mPath.lineTo(src, mStartY);
            canvas.drawPath(mPath, mGridPaint);

            float des = dimH * i + mRadius;
            mPath.moveTo(mRadius, des);
            mPath.lineTo(mEndX, des);
            canvas.drawPath(mPath, mGridPaint);
        }


        mPaint.setColor(Color.WHITE);
        mPaint.setStrokeWidth(8);
        mPaint.setStyle(Paint.Style.STROKE);
        mPath.reset();
        mPath.moveTo(mStartX, mStartY);
        mPath.cubicTo(mFrontX, mFrontY, mBackX, mBackY, mEndX, mEndY);
        canvas.drawPath(mPath, mPaint);


        mPaint.setColor(getContext().getResources().getColor(R.color.bezier_keyframe_curve_circle_dot));
        mPaint.setStrokeWidth(6);
        mPaint.setStyle(Paint.Style.FILL);
        canvas.drawCircle(mFrontX, mFrontY, mRadius - 4, mPaint);
        canvas.drawCircle(mBackX, mBackY, mRadius - 4, mPaint);

        mPaint.setStyle(Paint.Style.STROKE);
        mPath.reset();
        mPath.moveTo(mStartX, mStartY);
        mPath.lineTo(mFrontX, mFrontY);
        canvas.drawPath(mPath, mPaint);

        mPath.reset();
        mPath.moveTo(mEndX, mEndY);
        mPath.lineTo(mBackX, mBackY);
        canvas.drawPath(mPath, mPaint);


        mPaint.setColor(Color.WHITE);
        mPaint.setStrokeWidth(16);
        mPaint.setStyle(Paint.Style.FILL);
        canvas.drawCircle(mStartX, mStartY, 16, mPaint);
        canvas.drawCircle(mEndX, mEndY, 16, mPaint);

        mPaint.setStrokeWidth(2);
        mPaint.setStyle(Paint.Style.STROKE);
        canvas.drawCircle(mFrontX, mFrontY, mRadius - 4, mPaint);
        canvas.drawCircle(mBackX, mBackY, mRadius - 4, mPaint);
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                mDownX = event.getX();
                mDownY = event.getY();
                mControlType = judgeControlType();
            case MotionEvent.ACTION_MOVE:
                float tempEventX = event.getX();
                float tempEventY = event.getY();
                if (tempEventX < mRadius) {
                    tempEventX = mRadius;
                }
                if (tempEventX > mEndX) {
                    tempEventX = mEndX;
                }
                if (tempEventY < mRadius) {
                    tempEventY = mRadius;
                }
                if (tempEventY > mStartY) {
                    tempEventY = mStartY;
                }
                if (mControlType == TYPE_CONTROL_FRONT_POINT) {
                    mFrontX = (int) tempEventX;
                    mFrontY = (int) tempEventY;
                    invalidate();
                } else if (mControlType == TYPE_CONTROL_BACK_POINT) {
                    mBackX = (int) tempEventX;
                    mBackY = (int) tempEventY;
                    invalidate();
                }

                break;
            case MotionEvent.ACTION_UP:
                if (mTouchPointCallback != null) {
                    if (mControlType == TYPE_CONTROL_FRONT_POINT) {
                        mLeftBottomPointF = getNormalizedCoordinates(mFrontX, mFrontY);
                        mTouchPointCallback.onPointsChanged(mLeftBottomPointF, mRightTopPointF);
                    } else if (mControlType == TYPE_CONTROL_BACK_POINT) {
                        mRightTopPointF = getNormalizedCoordinates(mBackX, mBackY);
                        mTouchPointCallback.onPointsChanged(mLeftBottomPointF, mRightTopPointF);
                    }
                }
                Log.d("TAG", "onTouchEvent=======:  event.getX()：" + event.getX() + "  event.getY():" + event.getY() + "  mRadius:" + mRadius + "  mControlType:" + mControlType);
                Log.d("TAG", "onTouchEvent=======:  leftPoint：" + getNormalizedCoordinates(mFrontX, mFrontY) + "  rightPoint:" + getNormalizedCoordinates(mBackX, mBackY));
                break;
            default:
                break;
        }
        return true;
    }


    private int judgeControlType() {
        if (Math.abs(mFrontX - mDownX) <= mMaxRadius && Math.abs(mFrontY - mDownY) <= mMaxRadius) {
            return TYPE_CONTROL_FRONT_POINT;
        } else if (Math.abs(mBackX - mDownX) <= mMaxRadius && Math.abs(mBackY - mDownY) <= mMaxRadius) {
            return TYPE_CONTROL_BACK_POINT;
        }
        return TYPE_CONTROL_NONE_POINT;
    }

    private PointF getNormalizedCoordinates(int eventPosX, int eventPosY) {
        PointF pointF = new PointF();
        pointF.x = (eventPosX - mRadius) * 1.0F / (mWidth - 2 * mRadius);
        pointF.y = (mHeight - mRadius - eventPosY) * 1.0F / (mHeight - 2 * mRadius);
        return pointF;
    }

    public void updateControlPoint(PointF leftPintF, PointF rightPointF) {
        if (leftPintF == null) {
            leftPintF = new PointF(0.333333F, 0.333333F);
        }
        if (rightPointF == null) {
            rightPointF = new PointF(0.666667F, 0.666667F);
        }
        mLeftBottomPointF = leftPintF;
        mRightTopPointF = rightPointF;
        LogUtils.d("updateControlPoint: =====================" + leftPintF + "  right:" + rightPointF);
        mFrontX = (int) (leftPintF.x * (mWidth - 2 * mRadius) + mRadius);
        mFrontY = (int) (mHeight - mRadius - leftPintF.y * (mHeight - 2 * mRadius));
        mBackX = (int) (rightPointF.x * (mWidth - 2 * mRadius) + mRadius);
        mBackY = (int) (mHeight - mRadius - rightPointF.y * (mHeight - 2 * mRadius));
        LogUtils.d("updateControlPoint: ===============" + mFrontX + " " + mFrontY + "  " + mBackX + "  " + mBackY);
        invalidate();
    }


    public void setTouchPointCallback(OnTouchPointCallback onTouchPointCallback) {
        this.mTouchPointCallback = onTouchPointCallback;
    }

    public interface OnTouchPointCallback {

        /**
         * On points changed.
         * 控制点发生变化的回调
         *
         * @param leftBottomPoint 左下控制点the left bottom point
         * @param rightTopPoint   右上控制点the right top point
         */
        void onPointsChanged(PointF leftBottomPoint, PointF rightTopPoint);
    }
}
