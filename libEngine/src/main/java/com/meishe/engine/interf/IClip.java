package com.meishe.engine.interf;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2020/12/18 14:48
 * @Description :Clip接口规范 Interface of clip
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public interface IClip {
    int INVALID = -1;

    /**
     * Gets subtype.
     *
     * @return the audio type
     */
    int getSubType();

    /**
     * Gets clip index in track.
     *
     * @return the clip index in track
     */
    int getClipIndexInTrack();

    /**
     * Gets track index.
     *
     * @return the track index
     */
    int getTrackIndex();

    /**
     * Sets track index.
     *
     * @param trackIndex the track index
     */
    void setTrackIndex(int trackIndex);

    /**
     * Gets in point.
     *
     * @return the in point
     */
    long getInPoint();

    /**
     * Sets in point.
     *
     * @param inPoint the in point
     */
    void setInPoint(long inPoint);

    /**
     * Sets out point.
     *
     * @param outPoint the out point
     */
    void setOutPoint(long outPoint);

    /**
     * Gets speed.
     *
     * @return the speed
     */
    double getSpeed();

    /**
     * Gets type.
     *
     * @return the type
     */
    String getType();

    /**
     * Gets icon file path.
     *
     * @return the icon file path
     */
    String getIconFilePath();

    /**
     * Gets file path.
     *
     * @return the file path
     */
    String getFilePath();

    /**
     * Gets trim in.
     *
     * @return the trim in
     */
    long getTrimIn();

    /**
     * Sets trim in.
     *
     * @param trimIn the trim in
     */
    void setTrimIn(long trimIn);

    /**
     * Gets trim out.
     *
     * @return the trim out
     */
    long getTrimOut();

    /**
     * Sets trim out.
     *
     * @param trimOut the trim out
     */
    void setTrimOut(long trimOut);

    /**
     * Get record array float [ ].
     *
     * @return the float [ ]
     */
    float[] getRecordArray();

    /**
     * Sets record duration.
     *
     * @param duration the record duration
     */
    void setDuration(long duration);

    /**
     * Gets record duration.
     *
     * @return the record length
     */
    long getDuration();

    /**
     * Gets audio fade in.
     *
     * @return the audio fade in
     */
    long getFadeIn();

    /**
     * Gets fade out.
     *
     * @return the audio fade out
     */
    long getFadeOut();

    /**
     * Gets display name.
     *
     * @return the display name
     */
    String getDisplayName();

    /**
     * Sets display name.
     *
     * @param name the display name
     */
    void setDisplayName(String name);

    /**
     * Set original duration
     * @param duration the duration
     */
    void setOriginalDuration(long duration);

    /**
     * Get original duration
     * @return the duration
     */
    long getOriginalDuration();

    /**
     * Get record array float [ ].
     *
     * @param data the float [ ]
     */
    void setRecordArray(float[] data);

    /**
     *Can I cross the length boundary?
     * <p>
     * 是否可以越过长度边界
     * @return true:yes;false no.
     */
    boolean canExceedLength();

    /**
     * Whether it can be drag or not.
     * <p>
     * 是否可以拖动
     * @return true:yes;false no.
     */
    boolean canDrag();

    /**
     * Get background color
     * 获取背景颜色
     * @return
     */
    int getBackGroundColor();


    /**
     * Is has prop boolean.
     * 是否有道具
     * @return the boolean
     */
    boolean isHasProp();

    class ThumbNailInfo{
        public String urlPrefix;
        public long interval;
        public String extension;
        public boolean isImage;

        public ThumbNailInfo(String urlPrefix, long interval, String extension, boolean isImage) {
            this.urlPrefix = urlPrefix;
            this.interval = interval;
            this.extension = extension;
            this.isImage = isImage;
        }
    }
}
