package com.meishe.engine.interf;

import com.meishe.engine.bean.KeyFrameProcessor;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/6/30 15:51
 * @Description :关键帧处理器接口 The interface of key frame processor
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public interface IKeyFrameProcessor<T> {
    /**
     * 获取关键帧持有者
     * Key frame processor key frame processor.
     *
     * @return the key frame processor 关键帧持有者
     */
    KeyFrameProcessor<T> keyFrameProcessor();
}
