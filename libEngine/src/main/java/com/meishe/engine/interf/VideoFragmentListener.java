package com.meishe.engine.interf;

import com.meicam.sdk.NvsTimeline;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2020/11/3 17:08
 * 时间线的播放回调PlaybackCallback和PlaybackCallback2。
 * 详见：https://www.meishesdk.com/android/doc_ch/html/content/classcom_1_1meicam_1_1sdk_1_1NvsStreamingContext.html
 */
public interface VideoFragmentListener {

    /**
     * 播放到文件最后回调
     * Play back eof.
     *
     * @param timeline the timeline 时间线
     */
    void playBackEOF(NvsTimeline timeline);

    /**
     * 播放结束回调
     * Play stopped.
     *
     * @param timeline the timeline 时间线
     */
    void playStopped(NvsTimeline timeline);

    /**
     * 播放到指定位置回调
     * Playback timeline position.
     *
     * @param timeline the timeline  时间线
     * @param stamp    the stamp 时间点
     */
    void playbackTimelinePosition(NvsTimeline timeline, long stamp);

    /**
     * 流媒体引擎状态改变回调
     * Streaming engine state changed.
     *
     * @param state the state 流媒体引擎状态
     */
    void streamingEngineStateChanged(int state);

    /**
     * Seek 回调
     * On seeking timeline position.
     *
     * @param timeline the timeline 时间线
     * @param position the position 时间点
     */
    void onSeekingTimelinePosition(NvsTimeline timeline, long position);
}
