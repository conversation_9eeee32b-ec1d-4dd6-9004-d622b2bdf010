package com.meishe.engine;


import android.graphics.Bitmap;
import android.text.TextUtils;

import com.meicam.sdk.NvsAssetPackageManager;
import com.meicam.sdk.NvsStreamingContext;
import com.meicam.sdk.NvsTimeline;
import com.meishe.base.utils.LogUtils;
import com.meishe.engine.observer.EngineCallbackObservable;
import com.meishe.engine.observer.EngineCallbackObserver;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> LiHangZhou
 * @CreateDate :2021/5/18 15:05
 * @Description :使用sdk异步操作设置各种回调的管理者,the calllback manager of edit engine about meishe sdk
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class EngineCallbackManager {
    private final EngineCallbackObservable mCallbackObservable;
    private final NvsStreamingContext mNvsStreamingContext;

    private EngineCallbackManager() {
        mCallbackObservable = new EngineCallbackObservable();
        mNvsStreamingContext = EditorEngine.getInstance().getStreamingContext();
        setStreamingContextCallback();
    }

    private final static class Holder {
        private static final EngineCallbackManager INSTANCE = new EngineCallbackManager();
    }

    public static EngineCallbackManager get() {
        return Holder.INSTANCE;
    }

    /**
     * 设置主Context的回调
     * Set StreamingContext Callback
     */
    private void setStreamingContextCallback() {
        if (mNvsStreamingContext != null) {
            mNvsStreamingContext.setCompileCallback(mCompileCallback);
            mNvsStreamingContext.setCompileCallback2(mCompileCallback2);
            mNvsStreamingContext.setImageGrabberCallback(mImageGrabberCallback);
            mNvsStreamingContext.setPlaybackCallback(mPlaybackCallback);
            mNvsStreamingContext.setPlaybackCallback2(mPlaybackCallback2);
            mNvsStreamingContext.setStreamingEngineCallback(mStreamingEngineCallback);
            mNvsStreamingContext.setSeekingCallback(mStreamingSeekingCallback);
            mNvsStreamingContext.getAssetPackageManager().setCallbackInterface(mAssetPackageManagerCallback);
        }
    }

    /**
     * 设置截图的回调
     * Set image grabber callback
     */
    public void setImageGrabberCallback() {
        if (mNvsStreamingContext != null) {
            mNvsStreamingContext.setImageGrabberCallback(mImageGrabberCallback);
        }
    }

    public void setAuxiliaryStreamingContextCallback(NvsStreamingContext auxiliaryStreamingContext) {

    }

    /**
     * 注册资源回调监听变动观察者
     * Register as a resource change observer
     *
     * @param observer the observer
     */
    public void registerCallbackObserver(EngineCallbackObserver observer) {
        try {
            mCallbackObservable.registerObserver(observer);
        } catch (Exception e) {
            LogUtils.e(e);
        }
    }

    /**
     * 设置当前的回调标记，扩展用
     * Set current callback tag
     *
     * @param fromTag from tag 来自页面的标记
     */
    public void setCurrentCallbackTag(String fromTag) {
        mCallbackObservable.setCallbackFromTag(fromTag);
    }

    /**
     * 取消注册回调监听变动观察者
     * Unregister as a resource change observer
     *
     * @param observer the observer
     * @param fromTag  the from tag 当前页面的标记
     */
    public void unregisterCallbackObserverRemoveTag(EngineCallbackObserver observer, String fromTag) {
        try {
            if (!TextUtils.isEmpty(fromTag) && fromTag.equals(mCallbackObservable.getCallbackFromTag())) {
                /*如果当前的标记和移除的标记相同的时候才能移除，因为执行本方法的时候其他地方可能已经再次设置过标记了
                * If the current tag is the same as the removed tag,
                * it can only be removed because the tag may have been set again elsewhere when executing this method.
                * */
                mCallbackObservable.setCallbackFromTag("");
            }
            mCallbackObservable.unregisterObserver(observer);
        } catch (Exception e) {
            LogUtils.e(e);
        }
    }

    /**
     * 取消注册回调监听变动观察者
     * Unregister as a resource change observer
     *
     * @param observer the observer
     */
    public void unregisterCallbackObserver(EngineCallbackObserver observer) {
        try {
            mCallbackObservable.unregisterObserver(observer);
        } catch (Exception e) {
            LogUtils.e(e);
        }
    }

    private NvsStreamingContext.CompileCallback mCompileCallback = new NvsStreamingContext.CompileCallback() {
        @Override
        public void onCompileProgress(NvsTimeline nvsTimeline, int i) {
            mCallbackObservable.onCompileProgress(nvsTimeline, i);
        }

        @Override
        public void onCompileFinished(NvsTimeline nvsTimeline) {
            mCallbackObservable.onCompileFinished(nvsTimeline);
        }

        @Override
        public void onCompileFailed(NvsTimeline nvsTimeline) {
            mCallbackObservable.onCompileFailed(nvsTimeline);
        }
    };

    private final NvsStreamingContext.CompileCallback2 mCompileCallback2 = new NvsStreamingContext.CompileCallback2() {

        @Override
        public void onCompileCompleted(NvsTimeline nvsTimeline, boolean b) {
            mCallbackObservable.onCompileCompleted(nvsTimeline, b);
        }
    };
    private final NvsStreamingContext.ImageGrabberCallback mImageGrabberCallback = new NvsStreamingContext.ImageGrabberCallback() {
        @Override
        public void onImageGrabbedArrived(Bitmap bitmap, long l) {
            mCallbackObservable.onImageGrabbedArrived(bitmap, l);
        }
    };
    private final NvsStreamingContext.PlaybackCallback mPlaybackCallback = new NvsStreamingContext.PlaybackCallback() {
        @Override
        public void onPlaybackPreloadingCompletion(NvsTimeline nvsTimeline) {
            mCallbackObservable.onPlaybackPreloadingCompletion(nvsTimeline);
        }

        @Override
        public void onPlaybackStopped(NvsTimeline nvsTimeline) {
            mCallbackObservable.onPlaybackStopped(nvsTimeline);
        }

        @Override
        public void onPlaybackEOF(NvsTimeline nvsTimeline) {
            mCallbackObservable.onPlaybackEOF(nvsTimeline);
        }
    };

    private final NvsStreamingContext.PlaybackCallback2 mPlaybackCallback2 = new NvsStreamingContext.PlaybackCallback2() {
        @Override
        public void onPlaybackTimelinePosition(NvsTimeline nvsTimeline, long timestamp) {
            mCallbackObservable.onPlaybackTimelinePosition(nvsTimeline, timestamp);
        }
    };
    private final NvsStreamingContext.StreamingEngineCallback mStreamingEngineCallback = new NvsStreamingContext.StreamingEngineCallback() {
        @Override
        public void onStreamingEngineStateChanged(int state) {
            mCallbackObservable.onStreamingEngineStateChanged(state);
        }

        @Override
        public void onFirstVideoFramePresented(NvsTimeline nvsTimeline) {
            mCallbackObservable.onFirstVideoFramePresented(nvsTimeline);
        }
    };

    private final NvsStreamingContext.SeekingCallback mStreamingSeekingCallback = new NvsStreamingContext.SeekingCallback() {
        @Override
        public void onSeekingTimelinePosition(NvsTimeline nvsTimeline, long l) {
            mCallbackObservable.onSeekingTimelinePosition(nvsTimeline, l);
        }
    };

    private final NvsAssetPackageManager.AssetPackageManagerCallback mAssetPackageManagerCallback
            = new NvsAssetPackageManager.AssetPackageManagerCallback() {
        @Override
        public void onFinishAssetPackageInstallation(String s, String s1, int i, int i1) {
            mCallbackObservable.onFinishAssetPackageInstallation(s, s1, i, i1);
        }

        @Override
        public void onFinishAssetPackageUpgrading(String s, String s1, int i, int i1) {
            mCallbackObservable.onFinishAssetPackageUpgrading(s, s1, i, i1);
        }
    };
}
