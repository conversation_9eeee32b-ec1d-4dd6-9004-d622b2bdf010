package androidx.sqlite.db.framework;

import android.content.Context;
import android.database.DatabaseErrorHandler;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteException;
import android.database.sqlite.SQLiteOpenHelper;
import android.os.Build;
import android.util.Log;

import androidx.sqlite.db.SupportSQLiteDatabase;
import androidx.sqlite.db.SupportSQLiteOpenHelper;

import com.meishe.base.utils.FileUtils;
import com.meishe.engine.util.PathUtils;

import java.io.File;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> LiHangZhou
 * @CreateDate :2021/2/23 18:40
 * @Description :自定义数据库打开辅助类 借鉴FrameworkSQLiteOpenHelper类。
 * Custom database helper classes.Refer FrameworkSQLiteOpenHelper
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class CustomSQLiteOpenHelper implements SupportSQLiteOpenHelper {
    private static final String TAG = "CustomSQLiteOpenHelper";
    private final CustomSQLiteOpenHelper.OpenHelper mDelegate;

    public CustomSQLiteOpenHelper(Context context, String name, Callback callback) {
        mDelegate = createDelegate(context, name, callback);
    }

    private CustomSQLiteOpenHelper.OpenHelper createDelegate(Context context, String name, Callback callback) {
        final FrameworkSQLiteDatabase[] dbRef = new FrameworkSQLiteDatabase[1];
        return new CustomSQLiteOpenHelper.OpenHelper(context, name, dbRef, callback);
    }

    @Override
    public String getDatabaseName() {
        return mDelegate.getDatabaseName();
    }

    @Override
    @androidx.annotation.RequiresApi(api = Build.VERSION_CODES.JELLY_BEAN)
    public void setWriteAheadLoggingEnabled(boolean enabled) {
        mDelegate.setWriteAheadLoggingEnabled(enabled);
    }

    @Override
    public SupportSQLiteDatabase getWritableDatabase() {
        return mDelegate.getWritableSupportDatabase();
    }

    @Override
    public SupportSQLiteDatabase getReadableDatabase() {
        return mDelegate.getReadableSupportDatabase();
    }

    @Override
    public void close() {
        mDelegate.close();
    }

    static class OpenHelper extends SQLiteOpenHelper {
        private SQLiteDatabase mDatabase;
        private String mName;
        private int mNewVersion;
        /**
         * This is used as an Object reference so that we can access the wrapped database inside
         * the constructor. SQLiteOpenHelper requires the error handler to be passed in the
         * constructor.
         */
        final FrameworkSQLiteDatabase[] mDbRef;
        final Callback mCallback;
        // see b/78359448
        private boolean mMigrated;

        OpenHelper(Context context, String name, final FrameworkSQLiteDatabase[] dbRef,
                   final Callback callback) {
            super(context, name, null, callback.version,
                    new DatabaseErrorHandler() {
                        @Override
                        public void onCorruption(SQLiteDatabase dbObj) {
                            callback.onCorruption(getWrappedDb(dbRef, dbObj));
                        }
                    });
            mNewVersion = callback.version;
            mName = name;
            mCallback = callback;
            mDbRef = dbRef;
        }

        @Override
        public SQLiteDatabase getWritableDatabase() {
            //重写了该方法 Rewritten the method
            synchronized (this) {
                return getDatabaseLocked(true);
            }
        }

        @Override
        public SQLiteDatabase getReadableDatabase() {
            //重写了该方法 Rewritten the method
            synchronized (this) {
                return getDatabaseLocked(false);
            }
        }

        /**
         * 获取数据库（更改数据库的打开方式以及存储位置）
         * Getting the database (changing how the database is opened and where it is stored
         *
         * @param writable true is writable,false not
         */
        private SQLiteDatabase getDatabaseLocked(boolean writable) {
            if (mDatabase != null) {
                if (!mDatabase.isOpen()) {
                    // Darn!  The user closed the database by calling mDatabase.close().
                    mDatabase = null;
                } else if (!writable || !mDatabase.isReadOnly()) {
                    // The database is already open for business.
                    return mDatabase;
                }
            }
            SQLiteDatabase db = mDatabase;
            try {
                String dbPath = PathUtils.getAssetDownloadPath(-1) + File.separator + mName;
                FileUtils.createOrExistsFile(dbPath);
                db = SQLiteDatabase.openOrCreateDatabase(dbPath, null);
                onConfigure(db);
                final int version = db.getVersion();
                if (version != mNewVersion) {
                    if (db.isReadOnly()) {
                        throw new SQLiteException("Can't upgrade read-only database from version " +
                                db.getVersion() + " to " + mNewVersion + ": " + mName);
                    }
                    db.beginTransaction();
                    try {
                        if (version == 0) {
                            onCreate(db);
                        } else {
                            if (version > mNewVersion) {
                                onDowngrade(db, version, mNewVersion);
                            } else {
                                onUpgrade(db, version, mNewVersion);
                            }
                        }
                        db.setVersion(mNewVersion);
                        db.setTransactionSuccessful();
                    } catch (Exception e) {
                        Log.e(TAG, "version=+" + mNewVersion + ",e=" + e);
                    } finally {
                        db.endTransaction();
                    }
                }
                onOpen(db);
                if (db.isReadOnly()) {
                    Log.w(TAG, "Opened " + mName + " in read-only mode");
                }

                mDatabase = db;
                return db;
            }/* catch (Exception e) {
                Log.e(TAG, "openOrCreateDatabase,e=" + e);
            }*/ finally {
                if (db != null && db != mDatabase) {
                    db.close();
                }
            }
        }


        synchronized SupportSQLiteDatabase getWritableSupportDatabase() {
            mMigrated = false;
            SQLiteDatabase db = getWritableDatabase();
            if (mMigrated) {
                // there might be a connection w/ stale structure, we should re-open.
                close();
                return getWritableSupportDatabase();
            }
            return getWrappedDb(db);
        }

        synchronized SupportSQLiteDatabase getReadableSupportDatabase() {
            mMigrated = false;
            SQLiteDatabase db = getReadableDatabase();
            if (mMigrated) {
                // there might be a connection w/ stale structure, we should re-open.
                close();
                return getReadableSupportDatabase();
            }
            return getWrappedDb(db);
        }

        FrameworkSQLiteDatabase getWrappedDb(SQLiteDatabase sqLiteDatabase) {
            return getWrappedDb(mDbRef, sqLiteDatabase);
        }

        @Override
        public void onCreate(SQLiteDatabase sqLiteDatabase) {
            mCallback.onCreate(getWrappedDb(sqLiteDatabase));
        }

        @Override
        public void onUpgrade(SQLiteDatabase sqLiteDatabase, int oldVersion, int newVersion) {
            mMigrated = true;
            mCallback.onUpgrade(getWrappedDb(sqLiteDatabase), oldVersion, newVersion);
        }

        @Override
        public void onConfigure(SQLiteDatabase db) {
            mCallback.onConfigure(getWrappedDb(db));
        }

        @Override
        public void onDowngrade(SQLiteDatabase db, int oldVersion, int newVersion) {
            mMigrated = true;
            mCallback.onDowngrade(getWrappedDb(db), oldVersion, newVersion);
        }

        @Override
        public void onOpen(SQLiteDatabase db) {
            if (!mMigrated) {
                // if we've migrated, we'll re-open the db so we should not call the callback.
                mCallback.onOpen(getWrappedDb(db));
            }
        }

        @Override
        public synchronized void close() {
            // super.close();
            if (mDatabase != null && mDatabase.isOpen()) {
                mDatabase.close();
                mDatabase = null;
            }
            mDbRef[0] = null;
        }

        static FrameworkSQLiteDatabase getWrappedDb(FrameworkSQLiteDatabase[] refHolder,
                                                    SQLiteDatabase sqLiteDatabase) {
            FrameworkSQLiteDatabase dbRef = refHolder[0];
            if (dbRef == null || !dbRef.isDelegate(sqLiteDatabase)) {
                refHolder[0] = new FrameworkSQLiteDatabase(sqLiteDatabase);
            }
            return refHolder[0];
        }
    }
}
