<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="@dimen/dp_px_405"
    android:layout_height="wrap_content"
    android:background="@drawable/convert_dialog_bg"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/iv_progress"
        android:layout_width="@dimen/dp_px_113"
        android:layout_height="@dimen/dp_px_113"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/dp_px_75"
        android:contentDescription="@null"/>

    <TextView
        android:id="@+id/tv_hint"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:text="@string/convert_progress"
        android:textColor="@color/color_ffcccccc"
        android:layout_marginTop="@dimen/dp_px_37"
        android:textSize="@dimen/sp_px_33"
        tools:ignore="RelativeOverlap" />

    <Button
        android:id="@+id/bt_cancel"
        android:layout_width="@dimen/dp_px_231"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/dp_px_60"
        android:layout_marginBottom="@dimen/dp_px_60"
        android:background="@color/color_ff363636"
        android:minHeight="@dimen/dp_px_60"
        android:text="@string/cancel"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_px_30" />
</LinearLayout>
