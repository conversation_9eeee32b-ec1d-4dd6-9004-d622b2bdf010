plugins {
    id 'com.android.library'
}

android {
    compileSdkVersion rootProject.android.extCompileSdkVersion
    buildToolsVersion rootProject.android.extBuildToolsVersion

    defaultConfig {
        minSdkVersion rootProject.android.extMinSdkVersion
        targetSdkVersion rootProject.android.extTargetSdkVersion
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

dependencies {

    implementation 'androidx.appcompat:appcompat:1.1.0'
    implementation 'com.google.android.material:material:1.1.0'
    implementation project(path: ':libNet')
    implementation project(path: ':libNet')
    testImplementation 'junit:junit:4.+'
    androidTestImplementation 'androidx.test.ext:junit:1.1.1'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.2.0'
    implementation project(path: ':libBase')
    implementation project(path: ':libLogic')
}