package com.meishe.libplugin;

import com.meishe.base.utils.LogUtils;
import com.meishe.libplugin.user.IUserPlugin;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2021/3/17 14:16
 * @Description :
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class PluginManager {
    private IUserPlugin mUserPlugin;
    private PluginManager(){}
    private static class SingletonHolder  {
        private final static PluginManager INSTANCE = new PluginManager();
    }

    public static PluginManager get(){
        return SingletonHolder.INSTANCE;
    }

    public IUserPlugin getUserPlugin() {
        if (mUserPlugin == null) {
            String plugin = "com.meishe.user.UserPlugin";
            try {
                Class<?> classType  = Class.forName(plugin);
                mUserPlugin = (IUserPlugin) classType.newInstance();
            } catch (ClassNotFoundException | IllegalAccessException | InstantiationException e) {
                LogUtils.e(e);
            }
        }
        return mUserPlugin;
    }


}
