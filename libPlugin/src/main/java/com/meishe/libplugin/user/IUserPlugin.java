package com.meishe.libplugin.user;

import android.app.Activity;
import android.content.Context;
import android.view.View;

import androidx.fragment.app.Fragment;

import com.meishe.net.custom.RequestCallback;

import java.security.InvalidParameterException;
import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2021/3/18 19:33
 * @Description :用户插件接口 The interface of user plugin.
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public interface IUserPlugin {

    /**
     * 登录
     * Login
     */
    int MESSAGE_LOGIN = 1;

    int MSG_NET_ERROR_NO_ASSETS = 0;
    int MSG_NO_PURCHASED = 1;
    int MSG_NO_CUSTOM = 2;
    int MSG_NET_ERROR_NEED_REFRESH = 3;
    int MSG_COMMIT_FAILED = 4;
    int MSG_COMMIT_SUCCESS = 5;
    /**
     * Show un available pop.
     *
     * @param context    the context
     * @param assetsData the assets data
     * @param listener   the listener
     */
    void showUnAvailablePop(Context context, List<UserAssetsInfo> assetsData, OnEventListener listener);

    /**
     * Show login pop.
     *
     * @param context  the context
     * @param callBack the call back
     */
    void showLoginPop(Context context, ILoginCallBack callBack);

    /**
     * Show login out.
     *
     * @param context  the context
     * @param view     the view
     * @param callBack the call back
     */
    void showLoginOut(Context context, View view, final IUserPlugin.ILoginCallBack callBack);

    /**
     * Show login in confirm pop.
     *
     * @param context  the context
     * @param view     the view
     * @param callBack the call back
     */
    void showLoginInConfirmPop(Context context, View view, final IUserPlugin.ILoginCallBack callBack);

    void showCloudDraftManagePop(Context context,boolean needUpload, IDraftManagerCallBack callBack);

    /**
     * Login.
     *
     * @param callBack the call back
     */
    void login(IUserPlugin.ILoginCallBack callBack);

    /**
     * Is login boolean.
     *
     * @return the boolean
     */
    boolean isLogin();

    /**
     * Gets token.
     *
     * @return the token
     */
    String getToken();

    /**
     * Gets user id
     * 获取用户id
     * @return the user id
     */
    String getUserId();

    /**
     * Gets msg.
     *
     * @param context the context
     * @param type    the type
     * @return the error msg
     */
    String getMsg(Context context, int type);

    void exitManagerState(Fragment fragment, int layoutId);

    void deleteDraft(Fragment fragment);

    void showCloudUploadConfirmPop(Activity activity, OnEventListener listener);

    void goToCompilingPage(Fragment fragment);

    void getProjectBatchInfo(List<String> projectList, RequestCallback<List<String>> stringRequestCallback);

    /**
     * Upload.
     * 上传草稿
     * @param item     the item  类型必须是 com.meishe.draft.data.DraftData，不然会报InvalidParameterException
     *                 The parameter mask be class of com.meishe.draft.data.DraftData、
     *                 Otherwise, it will cause InvalidParameterException
     * @param isUpdate the is update
     * @throws InvalidParameterException the invalid parameter exception
     */
    void upload(Object item, boolean isUpdate) throws InvalidParameterException;

    Fragment getEditingFragment(int containerLayoutId);

    /**
     * The interface On event listener.
     *
     * @param <T> the type parameter
     */
    interface OnEventListener<T> {
        /**
         * On confirm.
         *
         * @param t the t
         */
        void onConfirm(T t);

        /**
         * On cancel.
         */
        void onCancel();
    }

    /**
     * The interface Login call back.
     */
    interface ILoginCallBack {
        /**
         * On login success.
         *
         * @param token the token
         */
        void onLoginSuccess(String token);

        /**
         * On login failed.
         *
         * @param code the code
         */
        void onLoginFailed(int code);
    }

    /**
     * The interface draft manager call back.
     */
    interface IDraftManagerCallBack {

        /**
         * 删除
         * Delete
         */
        void onDelete();

        /**
         * 上传
         * On upload.
         */
        void onUpload();
    }
}
