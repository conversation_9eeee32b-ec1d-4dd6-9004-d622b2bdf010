package com.meishe.libplugin.user;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2021/6/22 16:42
 * @Description :用户相关的assets对象 The assets class about user
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class UserAssetsInfo {
    /**
     * Possessor.
     * If it is 1, it is the current owner.
     * <p>
     * 拥有者， 如果是1，是当前拥有者
     */
    private int possessor;
    /**
     * The tag
     */
    private String tag;
    /**
     * The name
     */
    private String name;
    /**
     * The content
     */
    private String content;

    public int getPossessor() {
        return possessor;
    }

    public void setPossessor(int possessor) {
        this.possessor = possessor;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
