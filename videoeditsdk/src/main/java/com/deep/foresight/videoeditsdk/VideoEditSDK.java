package com.deep.foresight.videoeditsdk;

import android.content.Context;
import android.content.Intent;

import androidx.annotation.NonNull;

import com.meishe.myvideo.activity.MaterialSelectActivity;
import com.meishe.myvideo.activity.TestActivity;
import java.util.List;

/**
 * Main entry point for the Video Edit SDK
 * This class provides access to all the functionality of the video editing SDK
 */
public class VideoEditSDK {
    private static VideoEditSDK instance;
    private Context applicationContext;
    
    private VideoEditSDK(Context context) {
        this.applicationContext = context.getApplicationContext();
    }
    
    /**
     * Initialize the SDK with application context
     * @param context Application context
     * @return VideoEditSDK instance
     */
    public static synchronized VideoEditSDK init(@NonNull Context context) {
        if (instance == null) {
            instance = new VideoEditSDK(context);
        }
        return instance;
    }
    
    /**
     * Get the SDK instance
     * @return VideoEditSDK instance
     * @throws IllegalStateException if SDK is not initialized
     */
    public static VideoEditSDK getInstance() {
        if (instance == null) {
            throw new IllegalStateException("VideoEditSDK is not initialized. Call init(Context) first.");
        }
        return instance;
    }
    
    /**
     * Launch the material selection activity
     * @param template Template type
     * @param type Media type
     * @param templateId Template ID
     * @param needEdit Whether editing is needed
     */
    public void launchMaterialSelection(int template, int type, String templateId, boolean needEdit) {
        Intent intent = new Intent(applicationContext, MaterialSelectActivity.class);
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        applicationContext.startActivity(intent);
    }
    
    /**
     * Launch the test activity (for demonstration purposes)
     */
    public void launchTestActivity() {
        Intent intent = new Intent(applicationContext, TestActivity.class);
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        applicationContext.startActivity(intent);
    }
    
    // Add more methods to expose other functionality from the SDK
}
