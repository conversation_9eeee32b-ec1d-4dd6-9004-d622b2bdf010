plugins {
    id 'com.android.library'
    id 'maven-publish'
    id 'com.kezong.fat-aar'
}

android {
    namespace 'com.deep.foresight.videoeditsdk'
    compileSdk 33

    defaultConfig {
        minSdk 24

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    // Enable publishing of the release variant
    publishing {
        singleVariant("release") {
            withSourcesJar()
            withJavadocJar()
        }
    }
}

// Define a configuration that can be resolved
configurations {
    releaseImplementation.extendsFrom implementation
}

dependencies {
    implementation 'androidx.appcompat:appcompat:1.7.0'
    implementation 'com.google.android.material:material:1.12.0'
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.2.1'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.6.1'

    // Embed all project modules as embedded dependencies
    // The fat-aar plugin will include these in the final AAR
    embed project(":editModule")
    embed project(":libEngine")
    embed project(":libLogic")
    embed project(":libPlayer")
    embed project(":libAssetsView")
    embed project(":libPlugin")
    embed project(":draft")
    embed project(":cutsameModel")
    embed project(":libSpeaker")
    embed project(":libBase")
    embed project(":libUser")
    embed project(":libNet")
    embed project(":libStatistic")
    embed project(":libAnnotateProcessor")
    embed project(":annotation")
    embed project(":captureModule")
}

// Task to copy the AAR to a more accessible location
task copyFatAar(type: Copy) {
    from "${buildDir}/outputs/aar"
    into "${rootDir}/sdk-output"
    include "videoeditsdk-release.aar"
    rename "videoeditsdk-release.aar", "videoeditsdk-fat-1.0.0.aar"
    doLast {
        println "Fat AAR copied to ${rootDir}/sdk-output/videoeditsdk-fat-1.0.0.aar"
    }
}


// Configure publishing details
afterEvaluate {
    publishing {
        publications {
            release(MavenPublication) {
                from components.release
                
                groupId = 'com.deep.foresight'
                artifactId = 'videoeditsdk'
                version = '1.0.0'
                
                pom {
                    name = 'Video Edit SDK'
                    description = 'A comprehensive video editing SDK'
                    licenses {
                        license {
                            name = 'The Apache License, Version 2.0'
                            url = 'http://www.apache.org/licenses/LICENSE-2.0.txt'
                        }
                    }
                }
            }
        }
        
        repositories {
            maven {
                name = 'localRepo'
                url = "${buildDir}/repo"
            }
        }
    }
}