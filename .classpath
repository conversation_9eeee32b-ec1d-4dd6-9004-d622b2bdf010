<?xml version="1.0" encoding="UTF-8"?>
<classpath>
    <classpathentry kind="src" path="app/src/main/java"/>
    <classpathentry kind="src" path="editModule/src/main/java"/>
    <classpathentry kind="src" path="libBase/src/main/java"/>
    <classpathentry kind="con" path="org.eclipse.jdt.launching.JRE_CONTAINER"/>
    <classpathentry kind="lib" path="${ANDROID_HOME}/platforms/android-33/android.jar"/>
    <classpathentry kind="lib" path="${ANDROID_HOME}/extras/android/m2repository/com/android/support/support-v4/28.0.0/support-v4-28.0.0.jar"/>
    <classpathentry kind="lib" path="${ANDROID_HOME}/extras/android/m2repository/com/android/support/appcompat-v7/28.0.0/appcompat-v7-28.0.0.jar"/>
    <classpathentry kind="output" path="bin"/>
</classpath> 